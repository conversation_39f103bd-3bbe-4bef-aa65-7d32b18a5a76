{include file="sql/advertise/ad_targets/create_role_detail.tpl"}
select role_id, role_name,create_role_time, last_login_time, plan_id
from (select a1.*,
a2.`last_login_time`
from (select cp_game_id, role_id, max(`time`) as last_login_time
from bigdata_dwd.dwd_sdk_role_login
group by cp_game_id, role_id) a2
right join (select b1.*, b2.package_id, b2.plan_id,b2.sv_key
from ddc_platform.dwd_sdk_adsource_game b2
right join role_detail b1 on b1.source_id = b2.source_id) a1
on a1.cp_game_id = a2.cp_game_id and a1.role_id = a2.role_id) t1
where
    1 = 1
{if !empty($params)}
    {foreach $params as $kk => $chill}
        {if $kk eq 'package_id'}
            {if is_array($chill)}
                and package_id in ({$chill|join:','})
            {else}
                and package_id = '{$chill}'
            {/if}
        {/if}

        {if $kk eq 'plan_id'}
            {if is_array($chill)}
                and (plan_id in ({$chill|join:','}) or sv_key in ({$chill|join:','}))
            {else}
                and (plan_id = '{$chill}' or sv_key = '{$chill}')
            {/if}
        {/if}

    {/foreach}
{/if}
order by t1.cp_game_id, t1.role_id
{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}


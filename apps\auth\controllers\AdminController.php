<?php

namespace app\apps\auth\controllers;

use app\service\Admin;
use app\service\Auth;
use Plus\MVC\Controller\JsonController;
use Plus\Net\Http;

/**
 * Class Admin
 * <AUTHOR>
 */
class AdminController extends JsonController
{
    public function __construct(){
        $this->authService = new Auth();
    }

    /**
     * auth list
     * @param array $data 请求参数
     * @return array
     */
    public function listAction($data)
    {
        $data = $this->authService->getAuthorityList($data);
        return $this->success($data["data"]);
    }

    /**
     * create admin
     * @param array $data 请求参数
     * @return array
     */
    public function createAction($data)
    {
        $this->authService->createUser($data);
        return $this->success([]);
    }

    /**
     * delete admin
     * @param array $data 请求参数
     * @return array
     */
    public function deleteAction($data)
    {
        $this->authService->deleteUser($data);
        return $this->success([]);
    }
}

<?php

namespace app\apps\operator\Helpers;

class EventDistributeExt
{
    /**
     * @param int    $display
     * @param string $prefix
     *
     * @return \Closure
     */
    public static function calcIndex(int $display = 0, int $groupType = 1, string $prefix = 'node_'): \Closure
    {
        $calcFn = static::getDisplayForCalc($display);

        return static function (&$target) use ($prefix, $groupType, $calcFn) {
            preg_match_all('/' . $prefix . '\d+/', implode(',', array_keys($target)), $keyMatches);
            $keyMatches = $keyMatches[0] ?? [];

            if (empty($keyMatches)) return;

            $list = array_intersect_key($target, array_flip($keyMatches));
            ksort($list, SORT_NATURAL);
            $eventNum = $target['event_sum'] ?? 0;

            if ($groupType === 3) {
                $list = static::accrueIndex($list, $eventNum, $prefix);
            }

            foreach ($list as $k => $num) {
                $target[$k] = $calcFn($num, $eventNum);
            }
        };
    }

    /**
     * @param int $groupType
     *
     * @return \Closure
     */
    public static function calcIndexXY(int $groupType): \Closure
    {
        return static function (&$target) use ($groupType) {

            preg_match_all('/x_node_\d+/', implode(',', array_keys($target)), $xMatchKey);
            preg_match_all('/y_node_\d+/', implode(',', array_keys($target)), $yMatchKey);
            $xMatchKey = $xMatchKey[0] ?? [];
            $yMatchKey = $yMatchKey[0] ?? [];

            $xList    = array_intersect_key($target, array_flip($xMatchKey));
            $yList    = array_intersect_key($target, array_flip($yMatchKey));
            $nodeList = [];
            foreach ($xMatchKey as $item) {
                $nodeList[] = (int)str_replace('x_node_', '', $item);
            }

            ksort($xList, SORT_NATURAL);
            ksort($yList, SORT_NATURAL);

            if ($groupType === 3) {
                $xList = static::accrueIndex($xList, $target['x_event_sum'] ?? 0, 'x_node_');
                $yList = static::accrueIndex($yList, $target['y_event_sum'] ?? 0, 'y_node_');
            }

            if (empty($target['x_event_sum']) || empty($target['y_event_sum'])) {
                $target['event_sum'] = 0.00;
            }
            else {
                $target['event_sum'] = number_format(
                    round(math_eval('x/y', ['x' => $target['x_event_sum'], 'y' => $target['y_event_sum']]), 2),
                    2);
            }

            $newNode = array_map(static function ($x, $y, $node) {
                if (empty($x) || empty($y)) {
                    $num = 0.00;
                }
                else {
                    $num = number_format(round(math_eval('x/y', ['x' => $x, 'y' => $y]), 2), 2);
                }

                return ['key' => 'node_' . $node, 'value' => $num];
            }, $xList, $yList, $nodeList);

            $newNode = array_column($newNode, 'value', 'key');
            $target  = array_diff_key($target, array_merge($xList, $yList));
            $target  = array_merge($target, $newNode);
        };

    }

    /**
     * @param array             $data
     * @param int|float|numeric $eventSum
     * @param string            $nodePrefix
     *
     * @return array
     */
    public static function accrueIndex(array $data, $eventSum, string $nodePrefix = ''): array
    {
        $lastN = 0;
        foreach ($data as $k => $chill) {
            $n        = $chill;
            $data[$k] = $eventSum - $lastN;
            $lastN    += $n;
        }

        return $data;
    }


    /**
     * @param int $dateDimension
     *
     * @return \Closure
     */
    public static function formatTDay(int $dateDimension): \Closure
    {
        if ($dateDimension === 3) {
            $formatFn = function (&$data) {
                if (!empty($data['tday'])) {
                    $tDay         = new \DateTime($data['tday']);
                    $start        = (clone $tDay)->format('Y-m-d');
                    $end          = (clone $tDay)->add(new \DateInterval('P6D'))->format('Y-m-d');
                    $data['tday'] = $start . '/' . $end;
                }
            };
        }
        elseif (4 === $dateDimension) {
            $formatFn = static function (&$data) {
                if (!empty($data['tday'])) {
                    $tDay         = new \DateTime($data['tday']);
                    $data['tday'] = $tDay->format('Y-m');
                }
            };
        }
        else {
            $formatFn = fn() => true;
        }

        return static function (&$target) use ($formatFn) {
            $formatFn($target);
        };
    }


    /**
     * 根据显示内容同选项格式化
     *
     * @param int $display
     *
     * @return \Closure
     */
    public static function getDisplayForCalc(int $display): \Closure
    {
        if (1 === $display) {
            return static function ($x, $y) {
                return $x ?? 0.00;
            };
        }
        elseif (2 === $display) {
            return static function ($x, $y) {
                $x = trim($x);
                $y = trim($y);

                if (empty($x) || empty($y)) {
                    return '0.00%';
                }
                else {
                    return number_format(round(math_eval('x/y*100', ['x' => $x, 'y' => $y]), 2), 2) . '%';
                }
            };
        }
        else {
            return static function ($x, $y) {
                $x = trim($x);
                $y = trim($y);

                if (empty($x) || empty($y)) {
                    return '0.00(0.00%)';
                }
                else {
                    $num = number_format(round(math_eval('x/y*100', ['x' => $x, 'y' => $y]), 2), 2) . '%';
                    return $x . "({$num})";
                }
            };
        }
    }
}
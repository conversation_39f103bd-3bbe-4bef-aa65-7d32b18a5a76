<?php

use app\extension\Context;
use Plus\Cache\RedisCache;
use Plus\Internal\ErrorHandler;
use Plus\MVC\Route\DefaultRoute;
use Plus\MVC\View\JSON;
use Plus\Session\Session;

require __DIR__ . '/Env.config.php';
if ('PRO' == APP_EVN || 'GRAY' == APP_EVN) {
    $dbConfig = require __DIR__ . '/db.php';
}
elseif ('TEST' == APP_EVN) {
    $dbConfig = require __DIR__ . '/dbTest.php';
}
else {
    $dbConfig = require __DIR__ . '/dbDev.php';
}

$mqConfig = require __DIR__ . '/mq.config.php';

$config = [
    'basePath'   => dirname(__FILE__) . '/../',
    'timeZone'   => 'Asia/Shanghai',
    //组件配置，覆盖框架自带的
    'components' => [
        //错误处理
        'error'   => [
            'class'         => 'PRO' == APP_EVN ? \app\extension\ErrorHandler::class : \Plus\Internal\ErrorHandler::class,
            'discardOutput' => true,
            'maxLine'       => 10,
        ],
        //路由组件
        'route'   => [
            'class'             => DefaultRoute::class,
            'defaultApp'        => 'index',
            'defaultController' => 'index',
            'defaultAction'     => 'index',
        ],
        //视图处理
        'view'    => ['class' => JSON::class],
        //缓存
        'redis'   => [
            'class' => RedisCache::class,
            [
                'host'   => 'PRO' == APP_EVN || 'GRAY' == APP_EVN ? 'ssdb.40.910app.com' : '**************',
                'port'   => 'PRO' == APP_EVN || 'GRAY' == APP_EVN ? 8003 : 8001,
                'db'     => 0,
                'extral' => true,
                'prefix' => 'spy',
            ],
        ],
        'redis82' => [
            'class' => RedisCache::class,
            [
                'host'   => 'PRO' == APP_EVN || 'GRAY' == APP_EVN ? 'ssdb.40.910app.com' : '**************',
                'port'   => 'PRO' == APP_EVN || 'GRAY' == APP_EVN ? 8002 : 8001,
                'db'     => 0,
                'extral' => true,
                'prefix' => '',
            ],
        ],
        'queue'   => [
            'class'    => \framework\rabbit\RabbitQueue::class,
            'host'     => '**********',
            'port'     => 5672,
            'vhost'    => 'spy',
            'username' => 'spy',
            'password' => 'Spy910app',
            'debug'    => false,
            'auto_ack' => false,
            //注册队列名称，统一管理，预防名称到处乱写
            [
                'ad_match_log'
            ],
        ],
        //会话
        'session' => [
            'class'  => Session::class,
            'driver' => 'redis',
            'expire' => '86400',
            'prefix' => 'spy_session_',
            ['name' => 'PHPSESSID'],
        ],
        //上下文
        'context' => [
            'class' => Context::class,
            'debug' => false,
        ],

        'sqlTemplates' => [
            'class'      => \app\extension\SqlTemplates::class,
            'locate_conf' => [
                'templates'   => 'public/templates',
                'templates_c' => 'public/templates_c',
            ]
        ]
    ],
];

if (!in_array(APP_EVN, ["DEV", "TEST"])) {
    //日志目录处理
    $config['components']["log"] = [
        'class'  => Plus\Util\Log\Logger::class,
        'logDir' => '/data/www/elk_log/spy.910admin.com/',
    ];
}
//数据库配置引入
$config['components'] = array_merge($config['components'], $dbConfig, $mqConfig);

return $config;

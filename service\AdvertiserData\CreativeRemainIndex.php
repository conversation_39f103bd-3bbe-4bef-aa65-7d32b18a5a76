<?php

namespace app\service\AdvertiserData;

use app\apps\internal\Helpers\ConstHub;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\DBHelper\FieldCollect;
use app\extension\Support\Helpers\TimeUtil;
use app\extension\TableAssistant\Helpers\TableBaseHelp;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Components\MatchParams\AdCreativeMatcher;
use app\service\AdvertiserData\Components\MatchParams\AdPlanMatcher;
use app\service\AdvertiserData\Scheme\AdCreativeRemainScheme;
use app\service\AdvertiserData\Scheme\BaseScheme;
use app\service\AdvertiserData\Traits\AdServiceable;
use app\service\AdvertiserData\Traits\BasicOperator;
use app\service\AdvertiserData\Traits\Converter;
use app\service\General\BizTagsServ;
use app\util\Common;
use Aura\SqlQuery\Common\SelectInterface;

class CreativeRemainIndex
{
    use Converter, AdServiceable, BasicOperator;

    /**
     * @param $params
     *
     * @return array|false
     */
    public function fetchRealtimeByCreative($params, $groups = [])
    {
        return $this->fetchAllByCreative($params, $groups, 30);
    }

    /**
     * @param       $params
     * @param array $groups
     *
     * @return array|false
     */
    public function fetchRealtimeByPlan($params, array $groups = [])
    {
        return $this->fetchAllByPlan($params, $groups, 30);
    }

    /**
     * @param          $params
     * @param array $groups
     * @param int|null $maxDayType
     *
     * @return array|false
     */
    public function fetchAllByCreative($params, array $groups = [], int $maxDayType = null)
    {
        if (empty($groups)) {
            $groups = ['tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id', 'creative_id', 'day_type'];
        }

        if (!in_array('day_type', $groups)) {
            $groups[] = 'day_type';
        }

        $powerSql  = \Plus::$service->admin->getAdminPowerSql();
        $scheme    = AdCreativeRemainScheme::NewOne()->select();
        $mainTable = $scheme::MAIN_TABLE['alias'];

        $scheme
            ->joinPowerSql($powerSql)
            ->joinCreativeBase()
            ->join($this->joinAccount('t_base'))
            ->join((new JoinClause('left', 'dataspy.admin_user', 't_admin'))
                ->on('t_base.USER_ID', '=', 't_admin.ID'))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->joinAdpOauth('left', 't_base')
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params)));

        $matcher = new AdCreativeMatcher($scheme->fieldReflect());
        $matcher->setParams($params);
        $matcher->execute($scheme);

        if (!is_null($maxDayType)) {
            $scheme->scope($this->matchRemainParams(['max_day_type' => $maxDayType]));
        }
        Common::dumpSql((clone $scheme)->toSql());
        return $this->fetchAll($scheme->toSql());
    }

    /**
     * @param            $params
     * @param array|null $groups
     * @param int|null $maxDayType
     *
     * @return array|false
     */
    public function fetchAllByPlan($params, array $groups = [], int $maxDayType = -1)
    {
        if (empty($groups)) {
            $groups = ['tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id', 'creative_id', 'day_type'];
        }

        if (!in_array('day_type', $groups)) {
            $groups[] = 'day_type';
        }

        $scheme = $this->getRemainStrandedSchemeByPlan();

        $scheme
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params, true)));

        $matcher = new AdPlanMatcher($scheme->fieldReflect());
        $matcher->setParams($params);
        $matcher->execute($scheme);

        if ($maxDayType >= 0) {
            $scheme->scope($this->matchRemainParams(['max_day_type' => $maxDayType]));
        }
        return $this->fetchAll($scheme->toSql());
    }

    /**
     * 创意留存(不需要补全)
     *
     * @param       $params
     * @param array $groups
     * @param int $maxDayType
     *
     * @return array|false
     */
    public function remainInfoByCreative($params, array $groups = [], int $maxDayType = -1)
    {
        if (!in_array('day_type', $groups)) $groups[] = 'day_type';

        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = AdCreativeRemainScheme::NewOne()->select();

        $scheme
            ->joinPowerSql($powerSql)
            ->joinCreativeBase()
            ->join($this->joinAccount('t_base'))
            ->join((new JoinClause('left', 'dataspy.admin_user', 't_admin'))
                ->on('t_base.USER_ID', '=', 't_admin.ID'))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->joinAdpOauth('left', 't_base')
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params)));

        $matcher = new AdCreativeMatcher($scheme->fieldReflect());
        $matcher->setParams($params);
        $matcher->execute($scheme);

        if ($maxDayType >= 0) {
            $scheme->scope($this->matchRemainParams(['max_day_type' => $maxDayType]));
        }
        Common::dumpSql((clone $scheme)->toSql());
        return $this->fetchAll($scheme->toSql());
    }

    /**
     * @param       $params
     * @param array $groups
     * @param int $maxDayType
     *
     * @return array|false
     * @throws \Exception
     */
    public function remainInfoByPlan($params, array $groups = [], int $maxDayType = -1)
    {
        if (!in_array('day_type', $groups)) {
            $groups[] = 'day_type';
        }

        $timeDimension = (int)Arr::get($params, 'range_date_dimension', ConstHub::DIMENSION_DAY);

        if ($timeDimension === ConstHub::DIMENSION_WEEK) {
            $scheme = $this->getWeekSchemeByPlan($params, $groups);
        }
        elseif ($timeDimension === ConstHub::DIMENSION_MONTH) {
            $scheme = $this->getMonthSchemeByPlan($params, $groups);
        }
        else {
            $scheme = $this->getDailySchemeByPlan($params, $groups);
        }
        Common::dumpSql((clone $scheme)->toSql());
        return $this->fetchAll($scheme->toSql());
    }

    /**
     * @param $params
     * @param $groups
     *
     * @return BaseScheme
     * @throws \Exception
     */
    protected function getWeekSchemeByPlan($params, $groups): BaseScheme
    {
        [
            'begin' => $dateStart,
            'end'   => $dateEnd,
            'cycle' => $dateCycle,
        ] = TimeUtil::divideWeekByRangeDate(
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end')
        );
        $params['range_date_start'] = $dateStart;
        $params['range_date_end']   = $dateEnd;

        $scheme = $this->getRemainStrandedSchemeByPlan();

        (new AdPlanMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        $subField       = $this->getFieldCollect($params);
        $subTDayForTDay = $this->getFieldForTDay(ConstHub::DIMENSION_WEEK, $scheme::MAIN_TABLE['alias'], $dateCycle);
        $fields         = array_merge([$subTDayForTDay], $subField->output(false, true, false, true));

        $scheme->scope(TableBaseHelp::setColumn($fields));
        $mainField = array_merge(['tday'], $subField->output(false, false, true, false));

        return (new BaseScheme())
            ->from($scheme, 'base_body')
            ->scope(TableBaseHelp::setGroup($groups))
            ->scope(TableBaseHelp::setColumn($mainField));
    }

    /**
     * @param $params
     * @param $groups
     *
     * @return AdCreativeRemainScheme
     */
    protected function getDailySchemeByPlan($params, $groups): AdCreativeRemainScheme
    {
        $scheme = $this->getRemainStrandedSchemeByPlan();

        $fields       = $this->getFieldCollect($params);
        $fieldForTDay = $this->getFieldForTDay(ConstHub::DIMENSION_DAY, $scheme::MAIN_TABLE['alias']);
        $fields       = array_merge([$fieldForTDay], $fields->output());

        $scheme
            ->scope(TableBaseHelp::setColumn($fields))
            ->scope(TableBaseHelp::setGroup(TableBaseHelp::changeGroups($groups, $this->groupsReflect())));

        (new AdPlanMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        if (!empty($params['login_date[<]'])) {
            $maxLoginDate = $params['login_date[<]'];
            $scheme->scope(function (&$query) use ($maxLoginDate) {
                if (!$query instanceof SelectInterface) return;

                $query->where("login_date < '{$maxLoginDate}'");
            });
        }

        return $scheme;
    }


    /**
     * @param $params
     * @param $groups
     *
     * @return BaseScheme
     */
    protected function getMonthSchemeByPlan($params, $groups): BaseScheme
    {
        $scheme = $this->getRemainStrandedSchemeByPlan();

        (new AdPlanMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        $subField     = $this->getFieldCollect($params, $scheme::MAIN_TABLE['alias']);
        $fieldForTDay = $this->getFieldForTDay(ConstHub::DIMENSION_MONTH, $scheme::MAIN_TABLE['alias']);
        $fields       = array_merge([$fieldForTDay], $subField->output(false, true, false, true));

        $scheme->scope(TableBaseHelp::setColumn($fields));
        $mainField = array_merge(['tday'], $subField->output(false, false, true, false));

        return (new BaseScheme())
            ->from($scheme, 'base_body')
            ->scope(TableBaseHelp::setGroup($groups))
            ->scope(TableBaseHelp::setColumn($mainField));
    }


    /**
     * @return AdCreativeRemainScheme
     */
    protected function getRemainStrandedSchemeByPlan(): AdCreativeRemainScheme
    {
        $powerSql = \Plus::$service->admin->getAdminPowerSql();

        $scheme    = AdCreativeRemainScheme::NewOne()->select();
        $mainTable = $scheme::MAIN_TABLE['alias'];

        $scheme
            ->joinPowerSql($powerSql)
            ->joinPlanBase('left')
            ->join($this->joinAccount('t_base'))
            ->join((new JoinClause('left', 'dataspy.admin_user', 't_admin'))
                ->on('t_base.USER_ID', '=', 't_admin.ID'))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->joinAdpOauth('left', 't_base')
            ->join((new JoinClause('left', 'adp_platform.tb_adp_campaign', 'base_campaign'))
                ->on('base_campaign.channel_id', '=', 't_base.main_channel_id')
                ->on('base_campaign.campaign_id', '=', 't_base.campaign_id'));

        return $scheme;
    }

    /**
     * @param array $columns
     *
     * @return \Closure
     */
    protected function buildColumn(array $columns = ['*']): \Closure
    {
        return function (&$query) use ($columns) {
            if (!$query instanceof SelectInterface) return;
            $query->cols($columns);
        };
    }

    /**
     * @param $params
     *
     * @return \Closure
     */
    protected function matchRemainParams($params): \Closure
    {
        return function (&$query) use ($params) {
            $mainAlias = AdCreativeRemainScheme::MAIN_TABLE['alias'];

            if (!$query instanceof SelectInterface) return;

            if (!empty($maxDayTYpe = $params['max_day_type'])) {
                $query->where("{$mainAlias}.day_type <= {$maxDayTYpe}");
            }
        };
    }

    /**
     * @param $groups
     *
     * @return \Closure
     */
    protected function buildGroup($groups): \Closure
    {
        $groups = $this->changeGroups($groups);

        return function (&$query) use ($groups) {
            if (!$query instanceof SelectInterface) return;

            $query->groupBy($groups);
        };
    }

    /**
     * @return string[]
     */
    private function groupsReflect(): array
    {
        $mainAlias = AdCreativeRemainScheme::MAIN_TABLE['alias'];

        return [
            'tday'          => $mainAlias . '.tday',
            'cp_game_id'    => $mainAlias . '.cp_game_id',
            'game_id'       => $mainAlias . '.game_id',
            'package_id'    => $mainAlias . '.package_id',
            'channel_id'    => $mainAlias . '.channel_id',
            //            'channel_main_id' => 'POWER.channel_main_id',
            'creative_id'   => $mainAlias . '.creative_id',
            'campaign_id'   => $mainAlias . '.campaign_id',
            'plan_id'       => $mainAlias . '.plan_id',
            'day_type'      => $mainAlias . '.day_type',
            'ad_account_id' => 'base_account.id',
        ];
    }

    /**
     * @param        $params
     * @param string $baseTable
     * @param array $columns
     *
     * @return FieldCollect
     */
    private function getFieldCollect(
        $params, string $baseTable = AdCreativeRemainScheme::MAIN_TABLE['alias'], array $columns = []
    ): FieldCollect
    {
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString      = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0, IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) AS promotion_channel_id",
            $planChannelsString
        );
        $channelMainString  = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );


        $fixed = [
            'cp_game_id'           => ['table' => $baseTable],
            'game_id'              => ['table' => $baseTable],
            'app_show_id'          => ['table' => 'POWER'],
            'platform_id'          => ['table' => 'POWER'],
            'package_id'           => ['table' => $baseTable],
            'campaign_id'          => ['table' => $baseTable],
            'plan_id'              => ['table' => $baseTable],
            'creative_id'          => ['table' => $baseTable],
            'promotion_id'         => ['table' => 'POWER', 'source_field' => 'popularize_v2_id'],
            //            'channel_main_id'      => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (6568, 6822, 5329), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id'],
            'channel_main_id'      => ['info', 'raw' => $channelMainString],
            //            'promotion_channel_id' => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id NOT IN (6568, 6822, 5329), POWER.channel_id,IF(t_base.channel_id != 0, t_base.channel_id,POWER.channel_id)), 0) AS promotion_channel_id'],
            'promotion_channel_id' => ['info', 'raw' => $channelString],
            'department_id'        => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),t_admin.department_id, POWER.ad_department_id),0) as department_id'],
            'user_id'              => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),t_base.USER_ID, POWER.AD_USER_ID), 0) AS user_id'],
            'ad_account'           => ['table' => 'base_account'],
            'account_id'           => ['table' => 'base_account'],
            'account_name'         => ['table' => 'adp_oauth', 'source_field' => 'ADVERTISER_NAME'],
            'day_type'             => ['table' => $baseTable],
            'last_update_time'     => ['table' => $baseTable, 'source_field' => 'update_time', 'aggregate' => 'max'],
            'dim_user_os'          => ['raw' => " case when USER_OS = JSON_ARRAY('ANDROID') then 'ANDROID' when USER_OS = JSON_ARRAY('IOS') then 'IOS' when JSON_CONTAINS(USER_OS, '[\"ANDROID\",\"IOS\"]', '$') = 1 then '混投' else '混投' end  AS dim_user_os"]
        ];

        $calc = [
            'login_num' => ['raw' => "COALESCE(IF (day_type = 1000, SUM( IF(login_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY), login_num, 0) ), SUM(login_num)), 0) as login_num"], // 登录人数
        ];

        return new FieldCollect(array_merge($fixed, $calc));
    }

    /**
     * @param array $params
     * @param bool $isPlan
     * @param bool $isTop
     * @param bool $isTotal
     *
     * @return array
     */
    protected function getColumns(array $params = [], bool $isPlan = false, bool $isTop = false, bool $isTotal = false): array
    {
        $mainAlias = AdCreativeRemainScheme::MAIN_TABLE['alias'];

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString      = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0,IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) AS promotion_channel_id",
            $planChannelsString
        );
        $channelMainString  = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );


        $fixInfoIndex = [
            'tday'                 => ['source' => $mainAlias],
            'cp_game_id'           => ['source' => $mainAlias],
            'game_id'              => ['source' => $mainAlias],
            'app_show_id'          => ['source' => 'POWER'],
            //            'channel_main_id'      => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (6568, 6822, 5329), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id'],
            'channel_main_id'      => ['info', 'raw' => $channelMainString],
            //            'promotion_channel_id' => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id NOT IN (6568, 6822, 5329), POWER.channel_id,IF(t_base.channel_id != 0, t_base.channel_id,POWER.channel_id)), 0) AS promotion_channel_id'],
            'promotion_channel_id' => ['info', 'raw' => $channelString],
            'platform_id'          => ['source' => 'POWER'],
            'package_id'           => ['source' => $mainAlias],
            'campaign_id'          => ['source' => $mainAlias],
            'plan_id'              => ['source' => $mainAlias],
            'creative_id'          => ['source' => $mainAlias],
            'promotion_id'         => ['source' => 'POWER', 'source_field' => 'popularize_v1_id'],
            'department_id'        => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),t_admin.department_id, POWER.ad_department_id),0) as department_id'],
            'user_id'              => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),t_base.USER_ID, POWER.AD_USER_ID), 0) AS user_id'],
            'ad_account'           => ['source' => 'base_account'],
            'account_id'           => ['source' => 'base_account'],
            'account_name'         => ['source' => 'adp_oauth', 'source_field' => 'ADVERTISER_NAME'],
            'day_type'             => ['source' => $mainAlias],
            'dim_user_os'          => ['info', 'raw' => "case when USER_OS = JSON_ARRAY('ANDROID') then 'ANDROID' when USER_OS = JSON_ARRAY('IOS') then 'IOS' when JSON_CONTAINS(USER_OS, '[\"ANDROID\",\"IOS\"]', '$') = 1 then '混投' else '混投' end as dim_user_os"]
        ];
        if (!$isPlan) {
            $fixInfoIndex['creative_id'] = ['source' => $mainAlias];
        }
        $calculatedIndex = [
            'login_num' => ['source' => $mainAlias, 'aggregate' => 'sum'], // 登录人数
        ];

        $result  = [];
        $collect = collect();

        if (!$isTotal) {
            $collect = $collect->merge($fixInfoIndex);
        }
        $collect = $collect->merge($calculatedIndex);

        $collect->each(function (&$item, $key) use (&$result, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];
                $format    = "{$aggregate}(%s)";
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }

            $field .= $item['source_field'] ?? $key;

            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }


    /**
     * @param string $sql
     *
     * @return array|false
     */
    private function fetchAll(string $sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function simpleList($params): array
    {
        $table    = AdCreativeRemainScheme::MAIN_TABLE['table'];
        $wheres   = [];
        $whereStr = '';

        [$dateStart, $dateEnd] = [
            $params['range_date_start'], $params['range_date_end'],
        ];

        $wheres[] = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = $params['cp_game_id'];
            if (str_contains($cpGameId, ',')) {
                $wheres[] = "cp_game_id IN ({$cpGameId})";
            }
            else {
                $wheres[] = "cp_game_id = {$cpGameId}";
            }
        }

        if (($params['max_day_type'] ?? 0) > 0) {
            $maxDayType = $params['max_day_type'];
            $wheres[]   = "(day_type <= {$maxDayType} or day_type = 1000)";
        }

        $wheres[] = "day_type < DATEDIFF(NOW(), tday)";

        if (!empty($wheres)) {
            $whereString = ' WHERE ' . implode(' and ', $wheres);
        }

        $sql = "
        SELECT tday, day_type, cp_game_id,SUM(login_num) as login_num
        FROM {$table} {$whereString} group by tday, day_type;
        ";

        $summarySql = "
        SELECT day_type, cp_game_id,SUM(login_num) as login_num
        FROM {$table} {$whereString} group by day_type;
        ";

        $list       = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $summaryRow = \Plus::$app->ddc_platform->query($summarySql)->fetchAll(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => $summaryRow,
        ];
    }

}
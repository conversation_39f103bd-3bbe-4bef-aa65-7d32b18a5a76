<?php

namespace app\logic\advertise;

use app\service\Advertiser\AdDashRemainProvider;
use Smarty\Exception;

/**
 * 留存处理逻辑层
 * @Date 2025/06/25
 */
class AdDashRemainLogic
{
    /**
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws Exception
     */
    public function dashData(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $result = (new AdDashRemainProvider())->getData(...func_get_args());
        $calcFn = $this->calcFn();

        if (!empty($result['list'])) {
            $list = &$result['list'];
            $calcFn($list);
        }

        if (!empty($result['summary'])) {
            $summary = [&$result['summary']];
            $calcFn($summary);
        }

        return $result;
    }

    /**
     * 数据处理
     * @return \Closure
     */
    protected function calcFn(): \Closure
    {
        $fnMap = [
            function (&$data) {
                foreach ($data as $kk => &$chill) {
                    if (str_contains($kk, 'remain_') && $kk != 'remain_days') {
                        if (empty($chill)) {
                            $chill = '-';
                        }
                        else {
                            $chill = round($chill, 2) . '%';
                        }
                    }
                }
            },
        ];

        return function (&$list) use ($fnMap) {
            foreach ($list as &$item) {
                foreach ($fnMap as $fn) {
                    if (!is_callable($fn)) continue;
                    $fn($item);
                }
            }
        };
    }
}
<?php
return [

    'dataspy' => [
        'class' => \Plus\SQL\Db::class,
        getDbConfig(["db" => "dataspy"]),
        getDbConfig(["db" => "dataspy", "readOnly" => true]),
    ],

    'base_conf_platform' => [
        'class' => \Plus\SQL\Db::class,
        getDbConfig(["db" => "base_conf_platform"]),
        getDbConfig(["db" => "base_conf_platform", "readOnly" => true]),
    ],

    'origin_platform' => [
        'class' => \Plus\SQL\Db::class,
        getDbConfig(["db" => "origin_platform"]),
        getDbConfig(["db" => "origin_platform", "readOnly" => true]),
    ],

    'ddc_platform' => [
        'class' => \Plus\SQL\Db::class,
        getDbConfig(["db" => "ddc_platform", 'charset' => 'utf8mb4']),
        getDbConfig(["db" => "ddc_platform", 'charset' => 'utf8mb4', "readOnly" => true]),
    ],

    'adp_platform' => [
        'class' => \Plus\SQL\Db::class,
        getDbConfig(["db" => "adp_platform"]),
        getDbConfig(["db" => "adp_platform", "username" => "project_spy", "password" => "1DQk0i42js%$7ZXCEyX6", "readOnly" => true]),
    ],

    //doris节点
    'ddc_doris1'   => [
        'class' => \Plus\SQL\Db::class,
        [
            'database_type' => 'mysql',
            'database_name' => 'ddc_platform',
            'server'        => '120.132.122.49',
            'port'          => 9030,
            'username'      => 'spy',
            'password'      => 'pW5t#8aRkL',
        ],
    ],
    'ddc_doris2'   => [
        'class' => \Plus\SQL\Db::class,
        [
            'database_type' => 'mysql',
            'database_name' => 'ddc_platform',
            'server'        => '117.48.216.208',
            'port'          => 9030,
            'username'      => 'spy',
            'password'      => 'pW5t#8aRkL',
        ],
    ],
    'ddc_doris3'   => [
        'class' => \Plus\SQL\Db::class,
        [
            'database_type' => 'mysql',
            'database_name' => 'ddc_platform',
            'server'        => '117.48.216.209',
            'port'          => 9030,
            'username'      => 'spy',
            'password'      => 'pW5t#8aRkL',
        ],
    ],
    'doris_entrance' => [
        'class' => \Plus\SQL\Db::class,
        [
            'database_type' => 'mysql',
            'database_name' => 'ddc_platform',
            'server'        => '120.132.122.40',
            'port'          => 9030,
            'username'      => 'spy',
            'password'      => 'pW5t#8aRkL',
        ],
    ],
    'doris_entrance2' => [
        'class' => \Plus\SQL\Db::class,
        [
            'database_type' => 'mysql',
            'database_name' => 'ddc_platform',
            'server'        => '**********',
            'port'          => 9030,
            'username'      => 'spy',
            'password'      => 'pW5t#8aRkL',
        ],
    ],
    'pp_platform'  => [
        'class' => \Plus\SQL\Db::class,
        [
            'database_type' => 'mysql',
            'database_name' => 'pp_platform',
            'server'        => 'mysql.38.3308.910app.com',
            'port'          => 3308,
            'username'      => 'project_spy',
            'password'      => '1DQk0i42js%$7ZXCEyX6',
        ],
    ],
    'py_platform'  => [
        'class' => \Plus\SQL\Db::class,
        [
            'database_type' => 'mysql',
            'database_name' => 'py_platform',
            'server'        => 'mysql.42.910app.com:3306',
            'port'          => 3308,
            'username'      => 'project_spy',
            'password'      => '1DQk0i42js%$7ZXCEyX6',
        ],
        [
            'database_type' => 'mysql',
            'database_name' => 'py_platform',
            'server'        => 'mysql.38.3308.910app.com',
            'port'          => 3308,
            'username'      => 'project_spy',
            'password'      => '1DQk0i42js%$7ZXCEyX6',
            'readOnly'      => true,
        ],
    ],
    'base_conf_platform_doris' => [
        'class' => \Plus\SQL\Db::class,
        [
            'database_type' => 'mysql',
            'database_name' => 'base_conf_platform',
            'server'        => '120.132.122.40',
            'port'          => 9030,
            'username'      => 'spy',
            'password'      => 'pW5t#8aRkL',
        ],
    ],
    'base_conf_platform_doris_new' => [
        'class' => \Plus\SQL\Db::class,
        [
            'database_type' => 'mysql',
            'database_name' => 'base_conf_platform',
            'server'        => '**********',
            'port'          => 9030,
            'username'      => 'spy',
            'password'      => 'pW5t#8aRkL',
        ]
    ]
];

/**
 * 获取数据库配置
 *
 * @param array $data data
 *
 * @return array
 */
function getDbConfig($data)
{
    $db       = $data["db"] ?? "dataspy";
    $readOnly = $data["readOnly"] ?? false;
    $hostMap  = [
        "master" => [
            "master.spy.910app.com" => ["dataspy", "ddc_platform", "base_conf_platform", "origin_platform", "adp_platform"],
        ],
        "slave"  => [
            "slave.spy.910app.com" => ["dataspy", "ddc_platform", "base_conf_platform", "origin_platform", "adp_platform"],
        ],
    ];
    if (!isset($data["host"])) {
        foreach ($hostMap[$readOnly ? "slave" : "master"] as $host => $dbArr) {
            if (in_array($db, $dbArr)) {
                $data["host"] = $host;
                break;
            }
        }
    }
    $config = [
        'database_type' => 'mysql',
        'database_name' => $db,
        'server'        => $data["host"] ?? "dataspy",
        'port'          => $data["port"] ?? 3306,
        'username'      => $data["username"] ?? 'project_spy',
        'password'      => $data["password"] ?? '1DQk0i42js%$7ZXCEyX6',
        'charset'       => $data["charset"] ?? 'utf8',
    ];
    if ($readOnly) {
        //true = 读库  fasle=主库
        $config["readOnly"] = $readOnly;
    }
    return $config;
}

<?php

namespace app\apps\media\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\media\PlatformLogic;

/**
 * 对应群控平台/媒体数据看板
 *
 * @route /media/platform/*
 * @date  2023/08/15
 *
 */
class PlatformController extends BaseTableController
{
    use ColumnsInteract;

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $paginate = [
            'page'      => $params->pull('page'),
            'page_size' => $params->pull('page_size'),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = [];
        }

        $groups  = Arr::wrap($params->pull('groups'));
        $options = $params->toArray();

        return (new PlatformLogic())->getInfo($options, $groups, $paginate, $sort);
    }

    /**
     * @param Collection $params
     *
     * @return array[]
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'task_overview', 'label' => '发布任务总览'],
                    ['value' => 'base_task', 'label' => '普通任务指标'],
                    ['value' => 'post_task', 'label' => '投稿任务指标'],
                ],
            ],
        ];

        $info = [
            'tday'                    => ['title' => '统计日期', 'dataIndex' => 'tday', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'media_platform'          => ['title' => '媒体平台', 'dataIndex' => 'media_platform', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'business_ownership_name' => ['title' => '业务归属', 'dataIndex' => 'business_ownership_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'use_kind_name'           => ['title' => '账号归属', 'dataIndex' => 'use_kind_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'operations_manager'      => ['title' => '运营负责人', 'dataIndex' => 'operations_manager', 'sorter' => true, 'classify' => ['attrs', 'base']],
        ];

        $fields = [
            ['title' => '任务安排账号数', 'dataIndex' => 'active_task_account', 'sorter' => true, 'classify' => ['attrs', 'task_overview']],
            ['title' => '视频总安排量', 'dataIndex' => 'all_video_task', 'sorter' => true, 'classify' => ['attrs', 'task_overview']],
            ['title' => '视频总成功量', 'dataIndex' => 'all_video_task_success', 'sorter' => true, 'classify' => ['attrs', 'task_overview']],

            ['title' => '普通任务安排账号数', 'dataIndex' => 'normal_task_account', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '普通任务成功账号数', 'dataIndex' => 'normal_task_account_success', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '普通任务安排发布量', 'dataIndex' => 'normal_task_post', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '普通任务成功发布量', 'dataIndex' => 'normal_task_post_success', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '普通任务发布成功率', 'dataIndex' => 'normal_task_post_success_rating', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '当日新增普通视频播放量', 'dataIndex' => 'normal_new_video_play_today', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '当日新增普通视频累计播放量', 'dataIndex' => 'normal_new_video_play', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '当日新增普通视频累计点赞数', 'dataIndex' => 'normal_new_video_like', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '当日新增普通视频累计评论数', 'dataIndex' => 'normal_new_video_comment', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '当日新增普通视频累计转发数', 'dataIndex' => 'normal_new_video_forward', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
            ['title' => '所有普通视频当日播放量', 'dataIndex' => 'normal_video_play_today', 'sorter' => true, 'classify' => ['attrs', 'base_task']],

            ['title' => '投稿任务安排账号数', 'dataIndex' => 'contri_task_account', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '投稿任务成功账号数', 'dataIndex' => 'contri_task_account_success', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '投稿任务安排发布量', 'dataIndex' => 'contri_task_post', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '投稿任务发布成功量', 'dataIndex' => 'contri_task_post_success', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '投稿任务发布成功率', 'dataIndex' => 'contri_task_post_success_rating', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '当日新增投稿视频播放量', 'dataIndex' => 'contri_new_video_play_today', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '当日新增投稿视频累计播放量', 'dataIndex' => 'contri_new_video_play', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '当日新增投稿视频累计点赞数', 'dataIndex' => 'contri_new_video_like', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '当日新增投稿视频累计评论数', 'dataIndex' => 'contri_new_video_comment', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '当日新增投稿视频累计转发数', 'dataIndex' => 'contri_new_video_forward', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
            ['title' => '所有投稿视频当日播放量', 'dataIndex' => 'contri_video_play_today', 'sorter' => true, 'classify' => ['attrs', 'post_task']],
        ];

        $logic  = new PlatformLogic();
        $groups = Arr::wrap($params->pull('groups'));

        if (!empty($groups)) {
            $resetGroupFn = $this->resetGroupsCols($logic->groupRelationMap($groups), $groups, array_keys($info));
        }
        else {
            $resetGroupFn = fn() => true;
        }

        $resetGroupFn($info);
        $fields = array_merge($info, $fields);
        foreach ($fields as $k => &$item) {
            if ($item == '-') {
                $item = null;
            }
        }

        return ['fields' => array_values(array_filter($fields)), 'classify' => $classify];
    }


    protected function registerParams(): Collection
    {
        $nowToday = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start', 'default' => $nowToday],
            ['field' => 'range_date_end', 'default' => $nowToday],
            ['field' => 'media_platform_id'],
            ['field' => 'business_ownership'],
            ['field' => 'use_kind'],
            ['field' => 'operations_manager'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups', 'default' => ['tday', 'business_ownership_name', 'use_kind_name', 'media_platform', 'operations_manager']],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

}
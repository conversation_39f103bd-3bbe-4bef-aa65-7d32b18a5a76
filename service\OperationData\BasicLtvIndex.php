<?php

namespace app\service\OperationData;

use app\apps\operator\Helpers\ConstFirstLogin;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Scheme\BaseScheme;
use app\service\AdvertiserData\Traits\BasicOperator;
use app\service\AdvertiserData\Traits\Converter;
use app\service\ConfigService\Traits\BaseHub;
use app\service\OperationData\Components\MatchParams\FirstLoginLtvMatcher;
use app\service\OperationData\Components\MatchParams\FirstLoginMatcher;
use app\service\OperationData\Scheme\FirstLoginLtvDailyScheme;
use app\util\Common;

/**
 * @FirstLoginLtvIndex 首登LTV查询服务层
 * @Date               02/06/2023
 */
class BasicLtvIndex
{
    use Converter, BasicOperator;

    protected string $ltvTable = '';

    protected string $ltvTableAs = '';

    public function __construct($ltvTable, $alias = '')
    {
        $this->ltvTable = $ltvTable;

        if (!empty($alias)) {
            $this->ltvTableAs = $alias;
        }
        else {
            if (str_contains($ltvTable, '.')) {
                $t        = explode('.', $ltvTable);
                $ltvTable = Arr::pull($t, $ltvTable);
            }

            $this->ltvTableAs = $ltvTable;
        }
    }

    /**
     * 查询ltv详情(不以最细维度补全为基础)
     *
     * @param array $params        条件范围
     * @param array $groups        汇总组合
     * @param bool  $hasSummaryRow 是否需要返回汇总行ltv信息
     *
     * @return array
     * @throws \Exception
     */
    public function ltvInfo(array $params = [], array $groups = [], bool $hasSummaryRow = true): array
    {
        $result             = collect();
        $powerSql           = \Plus::$service->admin->getAdminPowerSql();
        $rangeTimeDimension = (int)Arr::pull(
            $params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY
        );

        if (ConstFirstLogin::DIMENSION_WEEK === $rangeTimeDimension) {
            $scheme = $this->getWeekSubSqlBody($params);
        }
        elseif (ConstFirstLogin::DIMENSION_MONTH === $rangeTimeDimension) {
            $scheme = $this->getMonthSubSqlBody($params);
        }
        else {
            $scheme = $this->getDailySqlBody($params);
        }

        if (!in_array('day_type', $groups)) {
            $groups[] = 'day_type';
        }

        $dayCol = $this->ltvTableAs . '.tday';
        $cols   = array_merge([$dayCol], $this->getCols($params));
        $scheme = $scheme
            ->scope($this->buildColumn($cols))
            ->joinPowerSql($powerSql, $this->ltvTableAs);

        (new FirstLoginLtvMatcher($this->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        $scheme->scope($this->buildGroup($groups));

        $backupScheme = clone $scheme;
        //输出调试sql
        Common::dumpSql((clone $scheme)->toSql());
        $result = [
            'list' => \Plus::$app->ddc_platform->query($scheme->toSql())->fetchAll(\PDO::FETCH_ASSOC),
        ];

        // 返回ltv汇总行信息
        if ($hasSummaryRow) {
            $summaryScheme = (new BaseScheme())
                ->fromSub((clone $backupScheme)->toSql(), 'summary_body')
                ->scope($this->buildColumn($this->getCols($params, true, true)))
                ->scope($this->buildGroup(['day_type']));
            //输出调试sql
            Common::dumpSql((clone $summaryScheme)->toSql());
            $result['summary_row'] =
                \Plus::$app->ddc_platform->query($summaryScheme->toSql())->fetchAll(\PDO::FETCH_ASSOC);
        }

        return $result;
    }

    /**
     * @param array $params
     *
     * @return SchemeContract
     * @throws \Exception
     */
    protected function getWeekSubSqlBody(array &$params = []): SchemeContract
    {
        [
            'begin' => $rangeDateStart,
            'end'   => $rangeDateEnd,
            'cycle' => $rangeDateCycle,
        ] = TimeUtil::divideWeekByRangeDate(
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end')
        );

        $options = [
            'range_date_start' => $rangeDateStart,
            'range_date_end'   => $rangeDateEnd,
        ];

        $baseScheme = new BaseScheme();
        $baseScheme
            ->from($this->ltvTable)
            ->scope($this->buildColumn([
                $this->getTimeColForWeek($rangeDateCycle, $this->ltvTable),
                'cp_game_id', 'game_id', 'package_id',
                'day_type', 'money', 'money_all', 'pay_date',
            ]));

        (new FirstLoginMatcher())
            ->setParams($options)
            ->execute($baseScheme);

        return (new BaseScheme())->fromSub($baseScheme, $this->ltvTableAs);
    }

    /**
     *
     *
     * @param array $params
     *
     * @return BaseScheme
     */
    protected function getMonthSubSqlBody(array &$params = []): BaseScheme
    {
        $options = [
            'range_date_start' => Arr::pull($params, 'range_date_start'),
            'range_date_end'   => Arr::pull($params, 'range_date_end'),
        ];

        $baseScheme = new BaseScheme();
        $baseScheme
            ->from($this->ltvTable)
            ->scope($this->buildColumn([
                "DATE_FORMAT(tday, '%Y-%m') as tday",
                'cp_game_id', 'game_id', 'package_id',
                'day_type', 'money', 'money_all', 'pay_date',
            ]));

        (new FirstLoginMatcher())
            ->setParams($options)
            ->execute($baseScheme);

        return (new BaseScheme())->fromSub($baseScheme, $this->ltvTableAs);
    }

    /**
     * @param array $params
     *
     * @return BaseScheme
     */
    protected function getDailySqlBody(array $params = []): BaseScheme
    {
        return (new BaseScheme())->from($this->ltvTable, $this->ltvTableAs);
    }

    /**
     * @param array  $dateCycle
     * @param string $mainTable
     *
     * @return string
     */
    protected function getTimeColForWeek(array $dateCycle = [], string $mainTable = ''): string
    {
        $tDayCols = [];

        foreach ($dateCycle as $item) {
            ['begin_date' => $begin, 'end_date' => $end] = $item;

            $tDayCols[] = sprintf(
                "when {$mainTable}.tday between '%s' and '%s' then '%s'",
                $begin, $end, $begin . '/' . $end
            );
        }
        $caseString = implode(' ', $tDayCols);

        return sprintf(" CONCAT(case %s end) as tday ", $caseString);
    }

    /**
     * @param array $params
     * @param bool  $isSummaryRow
     * @param bool  $isTop
     *
     * @return array
     */
    protected function getCols(array $params = [], bool $isSummaryRow = false, bool $isTop = false): array
    {
        $mainTable = 't_ltv';

        $fixedCols = [
            'cp_game_id'      => ['source' => $mainTable],
            'game_id'         => ['source' => $mainTable],
            'package_id'      => ['source' => $mainTable],
            'pay_date'        => ['source' => $mainTable],
            'app_show_id'     => ['source' => 'POWER'],
            'channel_id'      => ['source' => 'POWER'],
            'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
            'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
            'platform_id'     => ['source' => 'POWER'],
            'channel_main_id' => ['source' => 'POWER'],
            'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v1_id'],
        ];

        $calcCols = [
            'day_type'  => ['source' => $mainTable],
            'money_all' => ['source' => $mainTable, 'aggregate' => 'sum'],
            'money'     => ['source' => $mainTable, 'aggregate' => 'sum'],
        ];

        return $this->generateColsArray($fixedCols, $calcCols, $isSummaryRow, $isTop);
    }

    /**
     * 拼接查询字段
     *
     * @param array $fixedCols
     * @param array $calcCols
     * @param bool  $isSummaryRow
     * @param bool  $isTop
     *
     * @return array
     */
    protected function generateColsArray(
        array $fixedCols = [], array $calcCols = [], bool $isSummaryRow = false, bool $isTop = false
    ): array
    {
        $result  = [];
        $collect = collect();

        if (!$isSummaryRow) {
            $collect = $collect->merge($fixedCols);
        }
        $collect = $collect->merge($calcCols);

        $collect->each(function (&$item, $key) use (&$result, $isSummaryRow, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];

                if ($this->isCalcValid($aggregate)) {
                    $format = "IFNULL({$aggregate}(%s), 0)";
                }
                else {
                    $format = "{$aggregate}(%s)";
                }
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }
            $field    .= $item['source_field'] ?? $key;
            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }

    /**
     * @param $aggregate
     *
     * @return bool
     */
    protected function isCalcValid($aggregate): bool
    {
        return in_array($aggregate, ['sum', 'avg']);
    }

    /**
     * @return array
     */
    private function fieldReflect(): array
    {
        return [
            'tday'            => $this->ltvTableAs,
            'cp_game_id'      => $this->ltvTableAs,
            'game_id'         => $this->ltvTableAs,
            'package_id'      => $this->ltvTableAs,
            'app_show_id'     => 'POWER',
            'channel_main_id' => 'POWER',
            'channel_id'      => 'POWER',
            'platform_id'     => 'POWER',
            'promotion_id'    => 'POWER.popularize_v2_id',
        ];
    }

    /**
     * 仅天维度
     *
     * @param array $params
     * @param array $groups
     * @param bool  $hasSummaryRow
     *
     * @return array
     */
    public function simpleListByDay(array $params = [], array $groups = [], bool $hasSummaryRow = true): array
    {
        $today       = date('Y-m-d');
        $wheres      = [];
        $whereString = '';

        [$dateStart, $dateEnd] = [
            $params['range_date_start'], $params['range_date_end'],
        ];

        $wheres[] = "tday between '{$dateStart}' and '{$dateEnd}'";
        $wheres[] = "pay_date < '{$today}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = $params['cp_game_id'];
            if (str_contains($cpGameId, ',')) {
                $wheres[] = "cp_game_id IN ({$cpGameId})";
            }
            else {
                $wheres[] = "cp_game_id = {$cpGameId}";
            }
        }

        if (($params['max_day_type'] ?? 0) > 0) {
            $maxDayType = $params['max_day_type'];
            $wheres[]   = "(day_type <= {$maxDayType} or day_type = 1000)";
        }

        if (!empty($wheres)) {
            $whereString = ' WHERE ' . implode(' and ', $wheres);
        }

        $sql = "
        SELECT 
         tday, day_type, cp_game_id, sum(money_all) as money_all, sum(money) as money 
        FROM {$this->ltvTable} {$whereString} group by tday, day_type
        ";

        $summarySql = "
        SELECT 
             day_type, cp_game_id, sum(money_all) as money_all, sum(money) as money 
        FROM {$this->ltvTable} {$whereString} group by day_type
        ";

        $list       = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $summaryRow = \Plus::$app->ddc_platform->query($summarySql)->fetchAll(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => $summaryRow,
        ];
    }


}
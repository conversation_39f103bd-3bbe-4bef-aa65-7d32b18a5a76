<?php

namespace app\apps\operator\controllers;

use app\apps\internal\controllers\BaseController;
use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Helpers\IndexCalculators;
use app\apps\internal\Helpers\IndicatorsHelpers;
use app\apps\internal\Helpers\LtvCalculator;
use app\apps\internal\Helpers\RoiCalculator;
use app\apps\internal\Traits\AdIndexCalculators;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\internal\Traits\OfflineDash;
use app\apps\internal\Traits\ServHostingAble;
use app\apps\operator\Helpers\ConstFirstLogin;
use app\apps\operator\Traits\NewLoginTrait;
use app\apps\operator\Traits\OperationCalculators;
use app\apps\operator\Traits\OperatorRequest;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\extension\Support\Helpers\Zakia;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use app\service\OperationData\BasicLtvIndex;
use app\service\OperationData\NewLoginIndex;

/**
 * @NewLoginLtvController 运营包号新增维度LTV查询
 * @route                 /{host}/operator/new-login-ltv/*
 */
class NewLoginLtvController extends BaseTableController
{
    use OperatorRequest, OperationCalculators, ColumnsInteract,
        AdIndexCalculators, NewLoginTrait, ServHostingAble, OfflineDash;


    /**
     * @inerhitDoc
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $dateDimension = (int)$params->get('range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        [
            'pagination' => $pagination,
            'sort'       => $sort,
        ] = $this->changeDefaultParams($params);

        if ($params->has('groups')) {
            $groups = Arr::wrap($params->pull('groups'));
        }
        else {
            $groups = ['tday', 'package_id'];
        }

        $options = $params->toArray();
        $options['max_data_day_ago'] = 1;

        // 格式化月份日期
        if ($dateDimension === ConstFirstLogin::DIMENSION_MONTH) {
            $options['range_date_start'] = date("Y-m-d", strtotime($options['range_date_start']));
            $options['range_date_end'] = date("Y-m-d", strtotime("{$options['range_date_end']} +1 month -1day"));
        }
        // 看情况是否限制查询的最新日期是哪一天
        $options['range_date_start'] = $options['range_date_start'] > date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) ? date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) : $options['range_date_start'];
        $options['range_date_end'] = $options['range_date_end'] > date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) ? date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) : $options['range_date_end'];

        $serv    = new NewLoginIndex();

        if (ConstFirstLogin::DIMENSION_WEEK === $dateDimension) {
            $result = $serv->listByWeek($options, $pagination, $groups, $sort);
        }
        elseif (ConstFirstLogin::DIMENSION_MONTH === $dateDimension) {
            $result = $serv->listByMonth($options, $pagination, $groups, $sort);
        }
        else {
            $result = $serv->listByDaily($options, $pagination, $groups, $sort);
        }

        $constConfCollect = (new BasicServ())->getMultiOptions([
            'platform_id', 'promotion_id', 'department_id',
            'user_id', 'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
        ])->put('channel_id', (new GeneralOptionServ())->listChannelOptions());;

        $ltvType     = (int)Arr::get($options, 'ltv_type', 0);
        $list        = &$result['list'];
        $ltvServ     = new BasicLtvIndex('ddc_platform.dws_package_ltv_daily', 't_ltv');
        $processLine = new ProcessLine();
        $calcFunc    = $this->newLoginGeneralCalculators();

        $this->cacheServForStorage($ltvServ, 'ltv');
        $this->cacheServForStorage($serv, 'base');

        if (
            in_array('tday', $groups)
            && $dateDimension === ConstFirstLogin::DIMENSION_DAY
        ) {
            $ltvGetUser = IndexCalculators::getSingleValue('new_user');
            $roiGetCost = IndexCalculators::getSingleValue('cost_discount');
        }
        else {
            $ltvGetUser = IndexCalculators::getValueInCollectByN('new_user_n');
            $roiGetCost = IndexCalculators::getValueInCollectByN('cost_discount_n');
        }

        $optionsServ = new GeneralOptionServ();

        $channelTagAppendFn = fn() => true;

        if (
            in_array('package_id', $groups)
            || in_array('channel_id', $groups)
        ) {
            $listChannel   = array_column($list, 'channel_id');
            $channelTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'channel_id', 'id' => $listChannel]) ?? []);

            if (!empty($channelTagMap)) {
                $channelTagAppendFn = function (&$target) use ($channelTagMap) {
                    if (
                        !empty($target['channel_id'])
                        && $target['channel_id'] != '-'
                    ) {
                        $channelId              = $target['channel_id'];
                        $target['channel_tags'] = array_values($channelTagMap[$channelId] ?? []);
                    }
                };
            }
        }

        $processLine
            // 添加每行LTV的信息
            ->addProcess($this->addInfoEachRow($options, $groups, ['new_user', 'cost_discount']))
            // 追加渠道标签
            ->addProcess($channelTagAppendFn)
            // 替换ID信息字段
            ->addProcess($this->replaceColumnDefine($constConfCollect))
            // 格式化汇总分组对应字段
            ->addProcess($this->resetUnusedColumn($groups))
            // 计算LTV_N
            ->addProcess(LtvCalculator::calcEachRow($ltvGetUser, ['groups' => $groups, 'ltv_type' => $ltvType]))
            // ROI_N计算
            ->addProcess(RoiCalculator::calcEachRow($roiGetCost, ['groups' => $groups, 'ltv_type' => $ltvType]))
            // 累计LTV计算
            ->addProcess(LtvCalculator::cumulativeLtvEachRow('new_user'))
            // 累计ROI
            ->addProcess(RoiCalculator::cumulativeRoiEachRow())
            // 其余指标计算
            ->addProcess($calcFunc);


        if (in_array('package_id', $groups)) {
            $listPackages  = array_column($list, 'package_id');
            $packageTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'package_id', 'id' => $listPackages]) ?? []);

            if (!empty($packageTagMap)) {
                $packageTagAppendFn = function (&$target) use ($packageTagMap) {
                    if (
                        !empty($target['package_id'])
                        && $target['package_id'] != '-'
                    ) {
                        $packageId              = $target['package_id'];
                        $target['package_tags'] = array_values($packageTagMap[$packageId] ?? []);
                    }
                };

                $processLine->addProcess($packageTagAppendFn);
            }
        }

        // 处理LTV输出值 (LTVn选项)
        if (2 === $ltvType) {
            // LTVn 增量展示
            $processLine->addProcess(LtvCalculator::ltvIncrementalEachRow());
        }
        elseif (3 === $ltvType) {
            // LTVn 赔率展示
            $processLine->addProcess(LtvCalculator::ltvMultipleEachRow());
        }

        $processLine->run($list);

        $summaryRow = &$result['summary'];

        $this->addInfoForSingleRow(
            $summaryRow, $options, [], $params['range_date_dimension'] ?? ConstFirstLogin::DIMENSION_DAY,
            ['new_user', 'cost_discount']
        );
        $summaryLtvGetUser = IndexCalculators::getValueInCollectByN('new_user_n');
        $summaryRoiGetCost = IndexCalculators::getValueInCollectByN('cost_discount_n');

        $calcFunc($summaryRow);

        LtvCalculator::calcLtv($summaryRow, $summaryRow['ltv_info'] ?? [], $summaryLtvGetUser, $ltvType);
        RoiCalculator::calcRoi($summaryRow, $summaryRow['ltv_info'] ?? [], $summaryRoiGetCost);
        LtvCalculator::cumulativeLtv($summaryRow, 'new_user');
        RoiCalculator::cumulativeRoi($summaryRow);

        if (2 === $ltvType) {
            IndexCalculators::ltvIncrementalCalc($summaryRow);
        }
        elseif (3 === $ltvType) {
            IndexCalculators::ltvMultipleCalc($summaryRow);
        }

        return $result;
    }

    /**
     * @inerhitDoc
     *
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $baseCollect = [
            'tday', 'thour', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id', 'promotion_id',
            'platform_id', 'department_id', 'user_id',
        ];

        $newUserCollect = [
            'new_user', 'cost_discount',
            'new_user_cost', 'new_user_total_pay',
            'total_ltv', 'total_roi',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],
                    ['value' => 'tags', 'label' => '标签'],

                    ['value' => 'ltv_group_1', 'label' => 'LTV1-30'],
                    ['value' => 'ltv_group_2', 'label' => 'LTV45-180'],
                    ['value' => 'ltv_group_3', 'label' => 'LTV210-360'],
                    ['value' => 'ltv_group_4', 'label' => 'LTV390-720'],

                    ['value' => 'roi_group_1', 'label' => 'ROI1-30'],
                    ['value' => 'roi_group_2', 'label' => 'ROI45-180'],
                    ['value' => 'roi_group_3', 'label' => 'ROI210-360'],
                    ['value' => 'roi_group_4', 'label' => 'ROI390-720'],
                ],
            ],
        ];

        $options = $params->toArray();

        if (empty($options['groups'])) {
            $options['groups'] = ['tday', 'package_id'];
        }

        $fields = $this->tableFields($options);

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'];

            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $newUserCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    private function changeDefaultParams(Collection &$params): array
    {
        // 分页获取
        [$page, $pageSize] = [
            $params->pull('page', 1), $params->pull('page_size', 20),
        ];

        // 排序获取
        if ($params->has('sort')) {
            $sort = $params->pull('sort');
            if ($params->has('order')) {
                $sort .= ' ' . ($params->pull('order') == 'ascend' ? 'asc' : 'desc');
            }
        }
        else {
            $sort = ['tday', 'new_user desc'];
        }

        return [
            'pagination' => ['page' => $page, 'page_size' => $pageSize],
            'sort'       => $sort,
        ];
    }

    protected function resetUnusedColumn($groups): \Closure
    {
        $resetCols = ColumnManager::groupFilterOperatorColumn($groups);

        return function (&$target) use ($resetCols) {
            $target = array_merge($target, $resetCols);
        };
    }
}
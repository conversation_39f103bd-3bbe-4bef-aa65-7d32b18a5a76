<?php

namespace app\ad_upload\tests\deduction;

use app\ad_upload\deduction\TimesDeductionStrategy;
use PHPUnit\Framework\TestCase;

/**
 * 按次数扣量 测试
 * <AUTHOR>
 *  phpcs:disable
 */
class TimesDeductionStrategyTest extends TestCase
{

    private $mockConfig;
    private $mockData;

    protected function setUp(): void
    {
        // 初始化模拟配置
        $this->mockConfig = [
            'kind'        => 1,
            'config_data' => [
                'ratio'       => 50, //上报比例
                'online_time' => [10, 100],
                'role_rank'   => [1, 50],
                'pay_times'   => [1, 10],
                'amount'      => [0.01, 100],
                'pay_amount'  => [10, 500],
            ],
            'id'          => 1,
            'update_time' => '2024-12-01 00:00:01',
        ];

        // 初始化模拟数据
        $this->mockData = [
            'CORE_ACCOUNT'    => 'test_account',
            'CP_GAME_ID'      => 123,
            'ROLE_RANK'       => 10,
            'MONEY'           => 50.00,
            'paid_report_log' => [],
        ];
    }

    /**
     * 测试通过所有检查的情况
     */
    public function testIsPass_AllChecksPassed()
    {
        $strategy = $this->getMockBuilder(TimesDeductionStrategy::class)
            ->onlyMethods(['getOnlineTime', 'getPaidTimesByCpAccount', 'getTotalPaidAmount'])
            ->getMock();

        $this->mockConfig['config_data']['ratio'] = 100; //上报比例
        //满足条件，100%上报
        for ($i = 0; $i < 100; $i++) {
            $strategy->init($this->mockConfig, $this->mockData);

            // 模拟依赖方法返回值
            $strategy->method('getOnlineTime')->willReturn(['ONLINE_TIME' => 50]);
            $strategy->method('getPaidTimesByCpAccount')->willReturn(['PAY_TIMES' => 5]);
            $strategy->method('getTotalPaidAmount')->willReturn(['TOTAL_AMOUNT' => 300]);

            $this->assertTrue($strategy->isPass(), '当所有条件通过时，应返回 true');
        }
    }

    /**
     * 概率测试
     */
    public function testIsPass_AllChecksPassed2()
    {

        $strategy = $this->getMockBuilder(TimesDeductionStrategy::class)
            ->onlyMethods(['getOnlineTime', 'getPaidTimesByCpAccount', 'getTotalPaidAmount'])
            ->getMock();

        $this->mockData['id']                     = 2;
        $this->mockConfig['config_data']['ratio'] = 70; //上报比例
        //满足条件，100%上报
        $yes = 0;
        $no  = 0;
        for ($i = 0; $i < 100; $i++) {
            $strategy->init($this->mockConfig, $this->mockData);

            // 模拟依赖方法返回值
            $strategy->method('getOnlineTime')->willReturn(['ONLINE_TIME' => 50]);
            $strategy->method('getPaidTimesByCpAccount')->willReturn(['PAY_TIMES' => 5]);
            $strategy->method('getTotalPaidAmount')->willReturn(['TOTAL_AMOUNT' => 300]);

            if ($strategy->isPass()) {
                $yes++;
            } else {
                $no++;
            }
        }

        $this->assertTrue($yes > $no, '当所有条件通过时，上报概率应大于不上报概率');

        $this->assertTrue($yes == 70);
        $this->assertTrue($no == 30);
    }


    /**
     * 测试未通过检查的情况
     */
    public function testIsPass_FailedChecks()
    {
        $strategy = $this->getMockBuilder(TimesDeductionStrategy::class)
            ->onlyMethods(['getOnlineTime', 'getPaidTimesByCpAccount', 'getTotalPaidAmount'])
            ->getMock();

        $strategy->init($this->mockConfig, $this->mockData);

        // 模拟某些检查未通过
        $strategy->method('getOnlineTime')->willReturn(['ONLINE_TIME' => 5]); // 不在范围内
        $strategy->method('getPaidTimesByCpAccount')->willReturn(['PAY_TIMES' => 5]);
        $strategy->method('getTotalPaidAmount')->willReturn(['TOTAL_AMOUNT' => 300]);

        // 调用方法并断言结果
        $result = $strategy->isPass();
        $this->assertFalse($result, '当部分条件未通过时，应返回 false');
    }

    /**
     * 测试未满足配置种类的情况
     */
    public function testIsPass_WrongKind()
    {
        $this->mockConfig['kind'] = 2; // 配置种类不匹配
        $strategy                 = new TimesDeductionStrategy();
        $strategy->init($this->mockConfig, $this->mockData);

        // 调用方法并断言结果
        $result = $strategy->isPass();
        $this->assertFalse($result, '当配置种类不匹配时，应返回 false');
    }
}

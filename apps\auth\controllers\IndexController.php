<?php

namespace app\apps\auth\controllers;

use app\extension\Exception\ParameterException;
use app\models\DataSpy\AdminUser;
use app\service\Admin;
use app\service\Auth;
use Plus\MVC\Controller\JsonController;
use Plus\Net\Http;

/**
 * Class Index
 * <AUTHOR>
 */
class IndexController extends JsonController
{
    /**
     * system key
     * @var string
     */
    private $verifyKey = 'sm09%#LM@!Nmi2';

    /**
     * domain，production：https://auth.910admin.com  test：https://test-auth.910admin.com
     * @var string
     */
    private $apiDomain = "https://test-auth.910admin.com";

    /**
     * 权限api对象
     * @var Auth
     */
    private $authApi = "";

    /**
     * login to jump address
     * @var string
     */
    private string $redictDomain = "https://test-spy.910admin.com";

    protected array $testWhiteList = ['luowenwei'];


    public function __construct()
    {
        $this->authApi = new Auth();
    }

    /**
     * oa login
     * @param array $data 请求参数
     * @return array
     */
    public function OaLoginAction($data)
    {

        $btime       = microtime(true);
        $id          = $this->getValue('id');
        $userName    = $this->getValue('name');
        $tkey        = $this->getValue('tkey');
        $sign        = $this->getValue('sign');
        $redirectUri = $this->getValue('redirectUri');
        $extern      = $this->getValue('extern');

        \Plus::$app->log->info([
            "message" => "收到参数", "url" => "", "params" => [
                'pid'  => $id,
                'name' => $userName,
                'tkey' => $tkey,
            ]
        ], [], 'oalogin');

        $signValue = md5(sha1($id . $userName . $this->verifyKey . $tkey));

        if ($id == '' || $userName == '' || $tkey == '' || $sign == '') {
            return $this->error('登录失败');
        }
        elseif ($sign != $signValue) {
            return $this->error('签名错误');
        }

        if (APP_EVN == "PRO") {
            $this->apiDomain    = "https://auth.910admin.com";
            $this->redictDomain = "https://spy.910admin.com";
        }
        else if (APP_EVN == "GRAY") {
            $this->apiDomain    = "https://authtest.910admin.com";
            $this->redictDomain = "https://gray-spy.910admin.com";
        }

        //request to verify
        $syUrl = $this->apiDomain . "/api/v1/auth/application/login/verify";
        $http  = new Http($syUrl);
        $res   = $http->get([
            'pid'  => $id,
            'name' => $userName,
            'tkey' => $tkey,
        ]);

        \Plus::$app->log->info(["message" => "请求login/verify接口", "result" => $_SERVER], [], 'oalogin_server_info');
        // 计算并输出执行时间
        \Plus::$app->log->info([
            "message" => "请求login/verify接口", "url" => $syUrl, "params" => [
                'pid'  => $id,
                'name' => $userName,
                'tkey' => $tkey,
            ]
        ], [], 'oalogin');

        if (!$res) {
            return $this->error('oa校验失败');
        }
        $result = json_decode($res, true);
        if ($result['code'] !== 0) {
            $etime = microtime(true);
            \Plus::$app->log->info(["message" => "失败", "result" => $_SERVER], [], 'oalogin_server_info');
            // 计算并输出执行时间
            \Plus::$app->log->info([
                "message"   => "登录校验失败：" . ($etime - $btime) . " 秒", "url" => $syUrl, "params" => [
                    'pid'  => $id,
                    'name' => $userName,
                    'tkey' => $tkey,
                ], "result" => $result
            ], [], 'oalogin');
            return $this->error('登录校验失败');
        }


        //check super admin
        $params = ["userList" => [$userName], "roleId" => 2];
        $ret    = $this->authApi->isRole($params);

        //To generate the token
        $accessToken = Admin::generateAccessToken(["username" => $userName, "is_admin" => $ret["data"][$userName] ? 1 : 0]);

        $userModel = new AdminUser();
        $user      = $userModel->find(["USER_NAME" => $userName], ["REAL_NAME"]);

        $url = $this->redictDomain . "/#/login?user_name=$userName&access_token=$accessToken&redirectUri=" . urlencode($redirectUri) . "&oa_access_token=" . $result["data"] . "&full_name=" . $user["REAL_NAME"];
        if ($extern) {
            $url .= "&extern=" . $extern;
        }
        header('content-type:text/html;charset=uft-8');
        header('location:' . $url);
    }

    //local login
    public function loginAction($data)
    {
        $userName = $data['username'] ?? '';
        if (APP_EVN == "PRO" && !in_array($userName, $this->testWhiteList)) {
            exit();
        }
        if (!isset($data["username"]) || !$data["username"] || !isset($data["password"])) {
            return $this->error("参数错误");
        }
        $userName = $data["username"];
        if ($data["password"] != "admin888") {
            return $this->error("密码错误");
        }
        $params = ["username" => $userName];
        $result = (new Auth())->getAllUsersList($params);
        if (!$result["data"]) {
            return $this->error('账号不存在');
        }

        //check super admin
        $params = ["userList" => [$userName], "roleId" => 2];
        $ret    = $this->authApi->isRole($params);

        //get oa user token
        $result = (new Auth())->getUserToken(["username" => $userName]);

        $user = (new AdminUser())->find(["USER_NAME" => $userName], ["REAL_NAME", "ID"]);

        //To generate the token
        $accessToken = \Plus::$service->admin->generateAccessToken(["username" => $userName, "is_admin" => $ret["data"][$userName] ? 1 : 0]);
        return $this->success([
            "access_token"    => $accessToken,
            "oa_access_token" => $result["data"],
            "username"        => $userName,
            "full_name"       => $user["REAL_NAME"],
            "user_id"         => $user['ID'],
        ]);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function oaRedirectAction(): array
    {
        $btime    = microtime(true);
        $id       = $this->getValue('id');
        $userName = $this->getValue('name');
        $tkey     = $this->getValue('tkey');
        $sign     = $this->getValue('sign');
//        $redirectUri = $this->getValue('redirectUri');
        $extern = $this->getValue('extern');

        \Plus::$app->log->info([
            'message' => '收到参数',
            'url'     => '',
            'params'  => [
                'pid'  => $id,
                'name' => $userName,
                'tkey' => $tkey,
            ],
            [],
            'oa-redirect'
        ]);

        $signValue = md5(sha1($id . $userName . $this->verifyKey . $tkey));

        if ($id == '' || $userName == '' || $tkey == '' || $sign == '') {
            return $this->error('登录失败');
        }
        elseif ($sign != $signValue) {
            return $this->error('签名错误');
        }

        if (APP_EVN == "PRO") {
            $this->apiDomain    = "https://auth.910admin.com";
            $this->redictDomain = "https://spy.910admin.com";
        }
        else if (APP_EVN == "GRAY") {
            $this->apiDomain    = "https://authtest.910admin.com";
            $this->redictDomain = "https://gray-spy.910admin.com";
        }

        $syUrl    = $this->apiDomain . "/api/v1/auth/application/login/verify";
        $http     = new Http($syUrl);
        $response = $http->get([
            'pid'  => $id,
            'name' => $userName,
            'tkey' => $tkey,
        ]);
        \Plus::$app->log->info(["message" => "请求login/verify接口", "result" => $_SERVER], [], 'oalogin_server_info');
        // 计算并输出执行时间
        \Plus::$app->log->info([
            "message" => "请求login/verify接口",
            "url"     => $syUrl,
            "params"  => [
                'pid'  => $id,
                'name' => $userName,
                'tkey' => $tkey,
            ]
        ], [], 'oa-redirect');

        if (!$response) {
            return $this->error('oa校验失败');
        }
        $result = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return $this->error('登录校验失败,返回数据格式解析错误');
        }

        $oaToken = $result['data'] ?: '';

        if ($result['code'] !== 0) {
            $etime = microtime(true);
            \Plus::$app->log->info(["message" => "失败", "result" => $_SERVER], [], 'oalogin_server_info');
            // 计算并输出执行时间
            \Plus::$app->log->info([
                "message"   => "登录校验失败：" . ($etime - $btime) . " 秒", "url" => $syUrl, "params" => [
                    'pid'  => $id,
                    'name' => $userName,
                    'tkey' => $tkey,
                ], "result" => $result
            ], [], 'oalogin');
            return $this->error('登录校验失败');
        }

        //check super admin
        $params = ["userList" => [$userName], "roleId" => 2];
        $ret    = $this->authApi->isRole($params);

        //To generate the token
        $accessToken = Admin::generateAccessToken(["username" => $userName, "is_admin" => $ret["data"][$userName] ? 1 : 0]);
        $userModel   = new AdminUser();
        $user        = $userModel->find(["USER_NAME" => $userName], ["REAL_NAME"]);

        return $this->success([
            'real_name' => $user['REAL_NAME'],
            'token'     => $accessToken,
            'user_name' => $userName,
            'oa_token'  => $oaToken,
            'extern'    => $extern ?: '',
            'user_id'   => $id,
        ]);
    }


    /**
     * logOut
     * @return array
     */
    public function logOutAction()
    {
        return $this->success([]);
    }

}

<?php

namespace app\service\Revenue;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\Convert;
use Spiral\Database\Database;
use Spiral\Database\Query\QueryBuilder;
use Spiral\Database\Query\SelectQuery;

/**
 * @ProfitsServ 分成表查询服务
 */
class ProfitsServ
{
    /**
     * 分成配置表
     */
    private const TABLE_INCOME_CONF = 'dataspy.tb_income_devide_conf';


    /**
     * 查询分成配置
     *
     * @param array $params
     * @param array $sort
     *
     * @return array
     */
    public function fetchRow(array $params = [], array $sort = []): array
    {
        $db = $this->getConn();
        $qb = $db->select()->from(static::TABLE_INCOME_CONF);

        if (!empty($params)) {
            $this->matchProfits($qb, $params);
        }

        if (empty($sort)) {
            $qb->orderBy('type');
        }
        else {
            foreach ($sort as $field => $sortType) {
                $qb->orderBy($field, $sortType);
            }
        }

        $qb->columns([
            'type as pay_type',
            'cp_devide',
            'channel_devide',
            'aisle_devide',
            'shoumeng_devide',
            'version',
            'ext',
            'admin_id',
        ]);

        return $qb->fetchAll();
    }

    /**
     * @param $data
     *
     * @return mixed
     * @throws \Throwable
     */
    public function updateProfits($data)
    {
        $db = $this->getConn();

        $this->profitsCalculate($data);

        return $db->transaction(static function (Database $db) use ($data) {
            $insertIds = [];
            // 更新旧的一批version=0的数据
            foreach ($data as $foo) {
                $operatorId = \Plus::$service->admin->getUserId();
                $payType    = $foo['pay_type'] ?? null;

                if (empty($payType)) throw new \InvalidArgumentException('missing pay_type');

                // 更新前一个版本的状态
                $maxVersion = $db
                    ->select()
                    ->from(static::TABLE_INCOME_CONF)
                    ->where('type', $payType)
                    ->max('version');

                $update = $db
                    ->table(static::TABLE_INCOME_CONF)
                    ->update([
                        'version'  => ++$maxVersion,
                        'admin_id' => $operatorId,
                    ]);

                $update
                    ->where('type', $payType)
                    ->where('version', 0)
                    ->run();

                $insertValue = [
                    'cp_devide'       => ($foo['cp_devide'] ?? 0.00),
                    'channel_devide'  => ($foo['channel_devide'] ?? 0.00),
                    'aisle_devide'    => ($foo['aisle_devide'] ?? 0.00),
                    'shoumeng_devide' => ($foo['shoumeng_devide'] ?? 0.00),
                    'version'         => 0,
                    'type'            => $payType,
                    'admin_id'        => $operatorId,
                    'ext'             => '',
                    'add_time'        => date('Y-m-d H:i:s'),
                ];

                if (!empty($foo['package_id'])) {
                    $packageIds = $foo['package_id'];

                    if (is_array($packageIds)) {
                        $packageIds = implode(',', $packageIds);
                    }

                    $insertValue['ext'] = $packageIds;
                }

                $insert = $db
                    ->insert(static::TABLE_INCOME_CONF)
                    ->values($insertValue);

                $re = $insert->run();

                if (is_numeric($re)) {
                    $insertIds[] = $re;
                }
            }

            return $insertIds;
        });
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('dataspy');
    }

    /**
     * @param SelectQuery|QueryBuilder $qb
     * @param array                    $params
     *
     * @return void
     */
    private function matchProfits(&$qb, array $params)
    {
        if (isset($params['version'])) {
            $qb->where('version', $params['version']);
        }
    }

    /**
     * 利润分成数据计算
     *  - cp_devide (cp分成)
     *  - channel_devide(渠道分成)
     *  - aisle_devide(通道分成)
     *  - shoumeng_devide(手盟分成)
     *
     * @param array[] $data
     *
     * @return void
     * @throws \Exception
     */
    private function profitsCalculate(array &$data)
    {
        foreach ($data as &$item) {
            if (!isset($item['shoumeng_devide'])) {
                $item['shoumeng_devide'] = 100 - $item['cp_devide'] - $item['channel_devide'] - $item['aisle_devide'];

                if ($item['shoumeng_devide'] < 0) {
                    throw new \Exception('分成比例不合理');
                }
            }
        }
    }
}
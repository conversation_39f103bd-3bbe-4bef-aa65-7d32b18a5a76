<?php

namespace app\service\OperationData\Components\MatchParams;

use app\extension\Support\Collections\Arr;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class PackageLtvMatch extends PackageMatch
{
    protected function matchFnList(): array
    {
        $new = [
            'pay_date' => $this->matchPayDay(),
        ];

        return array_merge(parent::matchFnList(), $new);
    }

    /**
     * @return \Closure
     */
    protected function matchPayDay(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value, $params, $operator = '=') {
            if (
                $operator != '='
                && !is_array($value)
            ) {
                $qb->where($key, $operator, $value);
            }
            else {
                $qb->where($key, new Parameter(Arr::wrap($value)));
            }
        };
    }
}
<?php

namespace app\ad_upload\tests\deduction;

use app\ad_upload\deduction\AmountDeductionStrategy;
use PHPUnit\Framework\TestCase;

/**
 * 金额扣量策略测试
 *
 * phpcs:disable
 */
class AmountDeductionStrategyTest extends TestCase
{
    private $strategy;

    protected function setUp(): void
    {
        $this->strategy = new AmountDeductionStrategy();
    }

    /**
     * 测试当 kind 不为 2 时返回 false
     * @return void
     * @throws \Exception
     */
    public function testIsPassWhenKindIsNot2(): void
    {
        $config = [
            'kind'        => 1,
            'config_data' => [],
        ];

        $data = [
            'MONEY' => 100,
        ];
        $this->strategy->init($config, $data);

        $this->assertFalse($this->strategy->isPass());
    }

    public function testPass(): void
    {
        $config = [
            'id'          => '343',
            'cp_game_id'  => '803',
            'game_id'     => '6837',
            'package_id'  => '0',
            'status'      => '2',
            'add_time'    => '0000-00-00 00:00:00',
            'update_time' => '2024-09-14 22:38:28',
            'operator'    => '曾金梅',
            'remark'      => '',
            'config_data' =>
                [
                    'rule'             =>
                        [
                            0 =>
                                [
                                    'amount'             =>
                                        [
                                            0 => 45,
                                            1 => 67,
                                        ],
                                    'report_amount_list' =>
                                        [
                                            0 => '30',
                                        ],
                                ],
                            1 =>
                                [
                                    'amount'             =>
                                        [
                                            0 => 68,
                                            1 => 98,
                                        ],
                                    'report_amount_list' =>
                                        [
                                            0 => '45',
                                        ],
                                ],
                            2 =>
                                [
                                    'amount'             =>
                                        [
                                            0 => 99,
                                            1 => 198,
                                        ],
                                    'report_amount_list' =>
                                        [
                                            0 => '68',
                                        ],
                                ],
                            3 =>
                                [
                                    'amount'             =>
                                        [
                                            0 => 199,
                                            1 => 328,
                                        ],
                                    'report_amount_list' =>
                                        [
                                            0 => '98',
                                        ],
                                ],
                            4 =>
                                [
                                    'amount'             =>
                                        [
                                            0 => 329,
                                            1 => 448,
                                        ],
                                    'report_amount_list' =>
                                        [
                                            0 => '128',
                                        ],
                                ],
                            5 =>
                                [
                                    'amount'             =>
                                        [
                                            0 => 449,
                                            1 => 648,
                                        ],
                                    'report_amount_list' =>
                                        [
                                            0 => '168',
                                        ],
                                ],
                        ],
                    'ratio'            => 0,
                    'amount'           =>
                        [
                        ],
                    'pay_times'        =>
                        [
                        ],
                    'role_rank'        =>
                        [
                        ],
                    'pay_amount'       =>
                        [
                        ],
                    'online_time'      =>
                        [
                        ],
                    'order_time_range' =>
                        [
                            0 => '2024-09-05 12:01:00',
                            1 => '2029-10-31 12:01:00',
                        ],
                ],
            'config_type' => '2',
            'name'        => '狂暴H5-冒险大幻想048-广点通-扣减规则',
            'kind'        => '2',
            'report_type' => '2',
            'media_id'    => '70',
        ];
        $data   = [
            'ID'               => '*********',
            'DEVICE_KEY'       => '92391158ce1e2b6e2c1fbc3ba976356f',
            'PACKAGE_ID'       => '********',
            'ORDER_ID'         => '*****************',
            'DEVICE_CODE'      => '0625aba4df6f1a8e0bcfa514f082e8b3',
            'OAID'             => '',
            'DEVICE_ID'        => '111-111-111-111',
            'MD5_DEVICE_ID'    => 'd41d8cd98f00b204e9800998ecf8427e',
            'CORE_ACCOUNT'     => 'minigame_6837_95b872773651f5c5993ebfb615b2ecc3',
            'PAYWAY'           => 'now_wx_wap',
            'MONEY'            => '45.000000',
            'GAME_ID'          => '6837',
            'OS'               => '',
            'IP'               => '*************',
            'ANDROID_ID'       => 'b81f9a3d0d1fa138',
            'CHANNEL_ID'       => '70',
            'SV_KEY'           => '********',
            'CLICK_ID'         => '*********',
            'TYPE'             => 'pay',
            'NEWLOGIN_TIME'    => '2024-12-04 18:08:19',
            'PAY_TIME'         => '2024-12-29 00:09:30',
            'CAMPAIGN_ID'      => '9774551',
            'PLAN_ID'          => '********',
            'ROLE_NAME'        => '魂淡',
            'ROLE_ID'          => '********20100000126',
            'ROLE_RANK'        => '117',
            'ROLE_VIP'         => '8',
            'GAME_SERVER_ID'   => '********',
            'CP_GAME_ID'       => '803',
            'PAY_GAME_ID'      => '6861',
            'SOURCE_GAME_ID'   => '6837',
            'PAY_PACKAGE_ID'   => '68600001',
            'SOURCE_ID'        => '364|6837_92391158ce1e2b6e2c1fbc3ba976356f_1',
            'PAY_RESULT'       => '1',
            'ACTUALLY_MONEY'   => '45.000000',
            'SOURCE_DIMENSION' => '2',
            'ACTIVATE_TIME'    => '2024-12-04 18:08:11',
            'UPLOAD_METHOD'    => '2',
            'EXT_ID'           => '4401',
            'CLICK_ID_AD'      => '',
            'CLICK_TIME'       => '2024-12-04 18:08:04',
            'CALLBACK_URL'     => 'https://cm.bilibili.com/conv/api/conversion/ad/cb/v1?track_id=pbaes.yICxVlc93AnKX2v0kGs8FLVqTq3nGCec_7eHMGvT4s1ikfQOfxc3-2ZBeEbxPZaAURB7xFTNTNT_541hkkqZFG_TtTOhIle-J2O7zg2Ad6PcFm8Vdp2Inf9UbpaRe95DMLp-DUdZl06VvOREvNPZg8rjXOz6Z7Md2n3Sy2KnP70',
            'CID'              => '',
        ];
        $this->strategy->init($config, $data);
        $this->assertTrue($this->strategy->isPass());

        $rs = $this->strategy->getData();

        $this->assertTrue($rs['MONEY_REPORT'] == 30);

        $data['MONEY'] = '30.000000';
        $this->strategy->init($config, $data);
        $this->assertFalse($this->strategy->isPass());

    }



}

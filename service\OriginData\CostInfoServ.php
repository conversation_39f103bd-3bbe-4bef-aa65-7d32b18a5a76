<?php

namespace app\service\OriginData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\models\OriginPlatform\TbAdCost;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use app\service\ConfigService\BasicServ;
use app\util\Common;

/**
 * 基于媒体数据查询
 * @return static
 */
class CostInfoServ
{

    private const TABLE_COST = 'origin_platform.tb_ad_cost';
    private const TABLE_COST_CREATIVE = 'origin_platform.tb_ad_creative_cost';

    private const DB_SLAVE = 'ddc_platform';

    /**
     * @param array $params
     * @param array $groups
     * @param array $page
     * @param array $columns
     *
     * @return array
     */
    public function fetchSimpleInfoInCost(array $params, array $groups = [], array $page = [], array $columns = []): array
    {
        $today = date('Y-m-d');
        $conn  = FakeDB::connection(static::DB_SLAVE);

        $qb = $conn->select()
            ->from(static::TABLE_COST . ' as t_cost')
            ->innerJoin('base_conf_platform.tb_package_detail_conf', 'power')
            ->on('t_cost.package_id', 'power.package_id');

        if (!empty($params['cp_game_id'])) {
            $qb->where([
                'power.cp_game_id' => ['in' => new Parameter(Arr::wrap($params['cp_game_id']))],
            ]);
        }

        [$dateStart, $dateEnd] = [
            $params['range_date_start'] ?? $today,
            $params['range_date_end'] ?? $today,
        ];

        $qb->where('TIME', 'between', $dateStart, $dateEnd);

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        if (empty($columns)) {
            $columns = [
                'TIME as tday',
                'power.channel_main_id',
                new Fragment('SUM(show_cnt) as `show`'),
                new Fragment('SUM(click_cnt) as `click`'),
                new Fragment('SUM(download_cnt) as `download`'),
                new Fragment('SUM(cost) as cost'),
                new Fragment('SUM(cost_discount) as `cost_discount`'),
            ];
        }

        $qb->columns($columns);

        return $qb->fetchAll(\PDO::FETCH_ASSOC);
    }


    /**
     * @param array $params
     * @param array $groups
     * @param array $page
     * @param array $columns
     *
     * @return array
     */


    public function listBase(
        array $params = [],
        array $page = [],
        array $groups = [],
              $sort = [],
        array $column = [],
        array $join)
    {
        $result = collect();

        $conn = FakeDB::connection(static::DB_SLAVE);

        $qb = $conn->select()
            ->from(static::TABLE_COST . ' as t_cost');


        [$joinColumns] = $this->join($qb, $join);

        $this->where($qb, $params);

        $baseColumns = [];
        $dataColumns = [];

        if (empty($columns)) {
            $baseColumns = [
                'TIME as tday',
                't_cost.id as id',
                't_cost.channel_id as channel_id',
                't_cost.cp_game_id as cp_game_id',
                't_cost.game_id as game_id',
                't_cost.package_id as package_id',
                't_cost.aid as aid',
                't_cost.user_id as update_user_id',
                't_cost.update_time as update_time',
                't_cost.ad_account as ad_account',
                't_cost.plan_id as plan_id',
                't_cost.ad_account_id as account_id',
                'power.channel_main_id',
            ];
            $dataColumns = [
                new Fragment('SUM(show_cnt) as `show`'),
                new Fragment('SUM(click_cnt) as `click`'),
                new Fragment('SUM(download_cnt) as `download`'),
                new Fragment('SUM(cost) as cost'),
                new Fragment('SUM(cost_discount) as `cost_discount`'),
            ];
        }

        $columns = array_filter(array_merge($baseColumns, $dataColumns, $joinColumns));

        $count = $qb->count();

        $qb->columns($dataColumns);
        $summaryRow = $qb->fetchAll(\PDO::FETCH_ASSOC)[0] ?? [];

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        if ($page) {
            $qb->limit($page['page_size'])->offset(($page['page'] - 1) * $page['page_size']);
        }
        $qb->columns($columns);
        $this->sort($qb, $sort);

        $list = $qb->fetchAll(\PDO::FETCH_ASSOC);

        $result
            ->put('summary', $summaryRow)
            ->put('list', $list)
            ->put('total', $count);


        return $result->toArray();
    }

    /**
     * where 条件
     *
     * @param object $qb
     * @param array $params
     * @return void
     */
    public function where(&$qb, $params = [])
    {
        $where = [
            // t_cost

            'cp_game_id'     => ['exp' => 'in', 'filed' => 't_cost.cp_game_id'],
            'game_id'        => ['exp' => 'in', 'filed' => 't_cost.game_id'],
            'channel_id'     => ['exp' => 'in', 'filed' => 't_cost.channel_id'],
            'package_id'     => ['exp' => 'in', 'filed' => 't_cost.package_id'],
            'plan_id'        => ['exp' => 'like', 'filed' => 't_cost.plan_id'],
            'aid'            => ['exp' => 'like', 'filed' => 't_cost.aid'],
            'campaign_id'    => ['exp' => 'like', 'filed' => 't_cost.plan_id'],
            'campaign_name'  => ['exp' => 'like', 'filed' => 't_cost.aid'],
            'ad_account'     => ['exp' => 'like', 'filed' => 't_cost.ad_account'],
            'account_id'     => ['exp' => 'like', 'filed' => 't_cost.ad_account_id'],
            'update_user_id' => ['exp' => 'in', 'filed' => 't_cost.user_id'],


            // bcc
            'platform_id'    => ['exp' => 'in', 'filed' => 'bcc.platform_id'],

            // aec
            'settlement_id'  => ['exp' => 'in', 'filed' => 'aec.settlement_id'],
            'agent_id'       => ['exp' => 'in', 'filed' => 'aec.agent_id'],
            'operate_id'     => ['exp' => 'in', 'filed' => 'aec.operate_id'],
            'explain_id'     => ['exp' => 'in', 'filed' => 'aec.explain_id'],
            'department_id'  => ['exp' => 'in', 'filed' => 'aec.department_id'],
            'user_id'        => ['exp' => 'in', 'filed' => 'aec.adv_user_id'],
        ];

        $today = date('Y-m-d');
        [$dateStart, $dateEnd] = [
            $params['range_date_start'] ?? $today,
            $params['range_date_end'] ?? $today,
        ];
        $qb->where('TIME', 'between', $dateStart, $dateEnd);


        foreach ($where as $key => $value) {
            if (($params[$key] ?? [])) {
                switch ($value['exp']) {
                    case 'in':
                        $qb->where([
                            $value['filed'] => ['in' => new Parameter(Arr::wrap($params[$key]))],
                        ]);
                        break;
                    case 'like':
                        $qb->where($value['filed'], 'like', "%{$params[$key]}%");
                        break;
                    default:
                        # code...
                        break;
                }
            }
        }
    }

    /**
     * join表，一般结合where，columns使用
     *
     * @param object $qb
     * @param array $join
     * @return void|array
     */
    public function join(&$qb, $join = [])
    {
        $joinColumns = [];

        $qb
            ->leftJoin('base_conf_platform.tb_package_detail_conf', 'power')
            ->on(['t_cost.package_id' => 'power.package_id']);


        if (in_array('tb_ad_account_ext_conf', $join)) {
            $qb
                ->leftJoin('base_conf_platform.tb_ad_account_ext_conf', 'aec')
                ->on([
                    't_cost.AD_ACCOUNT_ID' => 'aec.ADVERTISER_ID',
                ])
                ->andon('aec.START_DATE', '<=', 't_cost.TIME')
                ->andon('aec.END_DATE', '>=', 't_cost.TIME');

            $joinColumns = array_merge($joinColumns, [
                'aec.adv_user_id as user_id',
                'aec.department_id',
                'aec.settlement_id',
                'aec.agent_id',
                'aec.operate_id',
                'aec.explain_id',
            ]);
        }

        if (
            in_array('tb_ad_account_conf', $join)
            && in_array('tb_ad_account_ext_conf', $join)
        ) {
            $qb
                ->leftJoin('base_conf_platform.tb_ad_account_conf', 'ac')
                ->on([
                    'aec.CHANNEL_ID'    => 'ac.CHANNEL_ID',
                    'aec.AD_ACCOUNT_ID' => 'ac.ID',
                ])
                ->onWhere(['ac.STATUS' => '1']);

            /* 'TIME as tday',
                'power.channel_main_id',
                new Fragment('SUM(show_cnt) as `show`'),
                new Fragment('SUM(click_cnt) as `click`'),
                new Fragment('SUM(download_cnt) as `download`'),
                new Fragment('SUM(cost) as cost'),
                new Fragment('SUM(cost_discount) as `cost_discount`'), */

            $joinColumns = array_merge($joinColumns, [
                ''
            ]);
        }


        if (in_array('tb_base_package_conf', $join)) {
            $qb
                ->leftJoin('base_conf_platform.tb_base_package_conf', 'bpc')
                ->on([
                    't_cost.PACKAGE_ID' => 'bpc.PACKAGE_ID',
                ]);
        }

        if (in_array('tb_package_user_conf', $join)) {
            $qb
                ->leftJoin('base_conf_platform.tb_package_user_conf', 'bpu')
                ->on([
                    'bpc.GAME_ID'    => 'bpu.GAME_ID',
                    'bpc.PACKAGE_ID' => 'bpu.PACKAGE_ID',
                ]);
        }

        if (in_array('tb_base_channel_conf', $join)) {
            $qb
                ->leftJoin('base_conf_platform.tb_base_channel_conf', 'bcc')
                ->on([
                    'bpc.CHANNEL_ID' => 'bcc.CHANNEL_ID',
                ]);

            $joinColumns = array_merge($joinColumns, [
                'bcc.platform_id'
            ]);
        }

        return [$joinColumns];
    }

    /**
     * 排序
     *
     * @param [type] $qb
     * @param array $sort
     * @return void
     */
    public function sort(&$qb, $sort = [])
    {
        if ($sort) {
            $sortKeyMap = [
                'tday' => 't_cost.time',
            ];

            $sortKey = array_key_first($sort);

            $sort = [
                ($sortKeyMap[$sortKey] ?? $sortKey) => $sort[$sortKey]
            ];

            $qb->orderBy($sort);
        }
    }

    /**
     * @param array $data
     *
     * @return int|string|null
     * @throws \Exception
     */
    public function saveCostInfoForLive(array $data)
    {
        $map = [
            ['key' => 'tday', 'val' => '统计日期', 'default' => date('Y-m-d')],
            ['key' => 'channel_id', 'val' => '推广子渠道', 'default' => 0],
            ['key' => 'ad_account', 'val' => '投放账户', 'default' => 0],
            ['key' => 'ad_account_id', 'val' => '账号ID', 'default' => 0],
            ['key' => 'cp_game_id', 'val' => '游戏原名', 'default' => 0],
            ['key' => 'game_id', 'val' => '游戏统计名', 'default' => 0],
            ['key' => 'package_id', 'val' => '包号', 'default' => 0],
            ['key' => 'show_cnt', 'val' => '展示', 'default' => 0],
            ['key' => 'click_cnt', 'val' => '点击', 'default' => 0],
            ['key' => 'cost', 'val' => '返点前消耗金额', 'default' => 0],
            ['key' => 'cost_discount', 'val' => '返点后消耗金额', 'default' => 0],
            ['key' => 'cost_index_type', 'val' => '类型', 'default' => 0],
        ];

        $keyMap = array_column($map, 'key', 'val');

        // 中文需要转换的字段
        $fieldsMap = [
            'channel_id', 'cp_game_id', 'game_id', 'cost_index_type'
        ];

        // 必填字段
        $required = [
            'tday', 'ad_account_id', 'package_id', 'cost', 'cost_discount'
        ];

        $constConfCollect = (new BasicServ())->getMultiOptions($fieldsMap);
        $constConfCollect = $constConfCollect->toArray();
        foreach ($constConfCollect as $key => $value) {
            $constConfCollect[$key] = array_column($value, 'key', 'val');
        }

        $operator = \Plus::$service->admin->getUserId();

        $insertData = [];

        foreach ($data as $item) {
            $t = [];

            foreach ($item as $k => $foo) {
                $key = $keyMap[$k] ?? null;
                if (is_null($key)) continue;

                // 判断必填
                if (in_array($key, $required) && $foo === null) {
                    throw new \InvalidArgumentException("{$k}为必填");
                }

                if ($key === 'tday') {
                    try {
                        $data = (new \DateTime($foo))->format('Y-m-d');
                    }
                    catch (\Exception $e) {
                        throw new \InvalidArgumentException("日期错误");
                    }
                }
                else {

                    if (in_array($key, $fieldsMap)) {
                        $data = $constConfCollect[$key][$foo] ?? null;
                        if (!in_array($key, $required) && is_null($foo)) {
                            $data = '';
                        }
                        if (is_null($data)) {
                            throw new \InvalidArgumentException("{$foo} 不存在");
                        }
                    }
                    else {
                        $data = $foo;
                    }
                }
                $t[$key] = $data;
            }

            if (!empty($t)) {
                $t['time'] = $t['tday'] ?? $t['time'] ?? '';
                // 特殊处理
                if ($t['cost_index_type'] == '9999') {
                    // 预约字段为渠道+9999
                    $t['plan_id'] = $t['channel_id'] . '9999';
                }

                $t['aid'] = '';
                unset($t['cost_index_type'], $t['tday']);

                $t['user_id']     = $operator;
                $t['update_time'] = date('Y-m-d H:i:s');

                // 初始化
                $t['time']          = $t['time'] ?? '';
                $t['channel_id']    = $t['channel_id'] ?? '';
                $t['ad_account']    = $t['ad_account'] ?? '';
                $t['ad_account_id'] = $t['ad_account_id'] ?? '';
                $t['cp_game_id']    = $t['cp_game_id'] ?? '';
                $t['game_id']       = $t['game_id'] ?? '';
                $t['plan_id']       = $t['plan_id'] ?? '';
                $t['package_id']    = $t['package_id'] ?? '';
                $t['show_cnt']      = $t['show_cnt'] ?? 0;
                $t['click_cnt']     = $t['click_cnt'] ?? 0;
                $t['cost']          = $t['cost'] ?? 0;
                $t['cost_discount'] = $t['cost_discount'] ?? 0;


                $insertData[] = $t;
            }
        }

        $initMap                = array_column($map, 'default', 'key');
        $initMap['time']        = $initMap['tday'] ?? $initMap['time'] ?? '';
        $initMap['user_id']     = 0;
        $initMap['plan_id']     = '';
        $initMap['update_time'] = date('Y-m-d H:i:s');
        unset($initMap['cost_index_type'], $initMap['tday']);

        foreach ($insertData as $key => $value) {
            $where = [
                'TIME'          => $value['time'],
                'AD_ACCOUNT_ID' => $value['ad_account_id'],
                'PACKAGE_ID'    => $value['package_id'],
            ];
            $info  = (new TbAdCost())->asArray()->find($where);

            if (!empty($info)) {
                (new TbAdCost())->updateByWhere($value, ['id' => $info['ID']]);
            }
            else {
                (new TbAdCost($value))->insert();
            }
        }

        return True;
    }

}
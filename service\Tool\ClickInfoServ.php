<?php

namespace app\service\Tool;

use app\extension\FakeDB\FakeDB;
use app\models\DataSpy\TbAdSvlinkConf;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\util\Common;
use phpseclib3\Math\BigInteger\Engines\PHP;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use app\service\OriginData\ClickLogServ;

class ClickInfoServ
{
    /**
     * 简单的获取部分基础指标数据
     *
     *
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function simpleBaseInfo(
        array $params = [],
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): array
    {
        $data = [];
        return $data;
    }

    /**
     *
     *
     *
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function listInfo(
        array $params = []
    ): array
    {
        $data = ['list' => [], 'summary' => [], 'total' => 0];

        if (!($params['id'] ?? '')) {
            return $data;
        }

        $deviceData = $this->deviceData($params);

        $activeTime = $deviceData["ACTIVATE_TIME"];
        $packageId = $deviceData["PACKAGE_ID"];

        //没短链
        if (!$deviceData["SV_KEY"] || !$deviceData) {
            return  $data;
        }

        //查询点击数据
        $clickTime = date("Y-m-d H:i:s", strtotime("{$activeTime} -7 days"));
        $data = [];
        $_db = Common::pingDoris();

        $where = $this->where($deviceData);

        if ($where) {

                //查询激活时间 7天内点击
                $sql = "SELECT * FROM  bigdata_dwd.dwd_ad_click_log a  WHERE a.`TIME` between '$clickTime' AND '{$activeTime}'  AND   ($where) ";
                // echo $sql;die;
                $clickData = $_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                $data = array_merge($data, $clickData);
        }

        if (!$data) {
            //IP匹配
            $clickTimeOther = date("Y-m-d H:i:s", strtotime("{$activeTime} -3 days")); //三天内

            //查询激活时间 7天内点击
            $sql = "SELECT * FROM bigdata_dwd.dwd_ad_click_log a WHERE a.`TIME` between '{$clickTimeOther}' AND '{$activeTime}'  AND IP = md5('" . $deviceData['IP'] . "') ";
            // echo $sql;die;
            $clickData = $_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            $data = array_merge($data, $clickData);

        }


        $key = array_column(array_values($data), 'time');
        array_multisort($key, SORT_ASC, $data);


        $data = $this->format($data);

        return  ['list' => $data, 'summary' => [], 'total' => count($data)];
    }

    public function deviceData($params = [])
    {
        switch ($params['type'] ?? 'MAIN_ACCOUNT') {
            case "MAIN_ACCOUNT":
                $sql = "SELECT
                            a.PACKAGE_ID,
                            a.SV_KEY,
                            a.SV_KEY PLAN,
                            a.ACTIVATE_TIME,
                            IF(b.MD5_DEVICE_ID is null,c.MD5_DEVICE_ID,b.MD5_DEVICE_ID) MD5_DEVICE_ID,
                            IF(b.OAID is null,c.OAID,b.OAID) OAID,
                            IF(b.ANDROID_ID is null,c.ANDROID_ID,b.ANDROID_ID) ANDROID_ID,
                            a.IP IP
                        from ddc_platform.dwd_sdk_adsource_game a
                        left join origin_platform.tb_sdk_active_log b on a.ACTIVATE_TIME = b.TIME and a.DEVICE_KEY=b.DEVICE_KEY
                        left join ddc_platform.dwd_sdk_device_2018 c on a.DEVICE_KEY=c.DEVICE_KEY
                        where a.id = '{$params['id']}'";
                break;
            case "CORE_ACCOUNT":
                $sql = "SELECT
                            b.*,
                            b.TIME ACTIVATE_TIME
                        from origin_platform.tb_sdk_user_newlogin_package a
                        join origin_platform.tb_sdk_active_log b using(PACKAGE_ID,DEVICE_KEY)
                        where a.id = {$params['id']}";
                break;
            default:
                break;
        }
// echo $sql;die;
        $deviceData = \Plus::$app->ddc_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);

        return $deviceData;
    }

    /**
     * where条件
     *
     * @param array $params
     * @return void
     */
    public function where($deviceData = [])
    {

        $where = "";

        //先根据MD5_DEVICE_ID进行匹配
        if (!in_array($deviceData['MD5_DEVICE_ID'], ['', 'id_1', 'id_2', 'aes_tzqDZ5J0JYlLaCosoB_v1qr2O5qLmJbCDutkYlUdaqw', 'aes_6miKOskvddFGOyN69xrs1CEoaFCLBT4as69yM1jZM68'])) {
            $where .= " a.MD5_DEVICE_ID='" . $deviceData['MD5_DEVICE_ID'] . "'";
        }
        //根据OAID匹配
        if (!empty($deviceData['OAID']) && $deviceData['OAID'] != '00000000-0000-0000-0000-000000000000') {
            if (!empty($where)) {
                $where .= " OR ";
            }
            $where .= " a.OAID='" . $deviceData['OAID'] . "' OR a.OAID='" . md5($deviceData['OAID']) . "'";
        }
        //根据ANDROID_ID和md5加密的ANDROID_ID进行匹配
        if (!empty($deviceData['ANDROID_ID']) and $deviceData['ANDROID_ID'] != '00000000-0000-0000-0000-000000000000') {
            if (!empty($where)) {
                $where .= " OR ";
            }
            $where .= " a.ANDROID_ID='" . $deviceData['ANDROID_ID'] . "' OR a.ANDROID_ID='" . md5($deviceData['ANDROID_ID']) . "'";
        }

        return $where;
    }

    /**
     * 格式化
     *
     * @param array $list
     * @return void|array
     */
    public function format($list = [])
    {
        $model = new TbAdSvlinkConf();
        foreach ($list as $key => $value) {

            if($value["sv_key"]){
                $data = $model->asArray()->findById($value["sv_key"]);
            }else{
                $ext = json_decode($value["ext"],true);
                $planid = $ext["plan_id"];
                $value["sv_key"] = $planid;
                if($planid){
                    $data = $model->asArray()->find([
                        "PACKAGE_ID"=>$value["package_id"],
                        "PLAN_ID" => $planid
                    ]);

                }
            }
            $value["aid"] = $data["AID"]??"";
            $value = array_change_key_case($value, CASE_LOWER);
            $list[$key] = $value;
            $list[$key]['activate_time'] = $value['time'];
        }

        return $list;
    }
}
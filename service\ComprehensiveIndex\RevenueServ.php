<?php

namespace app\service\ComprehensiveIndex;

use app\models\DataSpy\AdminUser;
use app\models\DataSpy\TbIncomeStatPackageDaily;
use app\models\OriginPlatform\TbAdCost;
use Aura\SqlQuery\Common\SelectInterface;
use Aura\SqlQuery\QueryFactory;
use Plus\SQL\Medoo;

class RevenueServ
{
    /**
     * 获取业务收入详情(按天)
     *
     * @param array $wheres
     *
     * @return array
     * @throws \Exception
     */
    public function fetchIncomeDaily(array $wheres = []): array
    {
        $builder = new QueryFactory('mysql');
        $stm     = $builder->newSelect();

        $stm
            ->from('dataspy.tb_income_stat_package_daily as t1')
            ->join(
                'inner',
                'base_conf_platform.tb_package_detail_conf as power',
                't1.package_id = power.package_id');

        $today = date('Y-m-d');

        [
            $rangeStart, $rangeEnd,
        ] = [
            (new \DateTime($wheres['range_date_start'] ?? $today))->format('Ymd'),
            (new \DateTime($wheres['range_date_end'] ?? $today))->format('Ymd'),
        ];

        $stm
            ->where("t1.tday between '{$rangeStart}' and '{$rangeEnd}'")
            ->where('is_remove = 0');

        if (isset($wheres['type'])) {
            $stm->where('t1.type = ' . $wheres['type']);
        };

        if (!empty($wheres['cp_game_id'])) {
            $stm->where("cp_game_id = '{$wheres['cp_game_id']}'");
        }

        $cols = [
            'DATE(`tday`) as tday',
        ];

        if (isset($wheres['type'])) {
            if ($wheres['type'] == 2) {
                $cols[] = 'sum(sum_amount) as amount';
            }
            elseif ($wheres['type'] == 1) {
                $cols[] = 'sum(sum_amount) as recharge';
            }
        }

        $stm->cols($cols);
        $stm->groupBy(['tday']);

        $result = \Plus::$app->ddc_platform->query($stm->__toString())->fetchAll(\PDO::FETCH_ASSOC);

        return empty($result)
            ? []
            : array_column($result, null, 'tday');
    }

    /**
     * 获取消费详情(按天)
     *
     * @param array $wheres
     *
     * @return array
     */
    public function fetchCostInfoDaily(array $wheres = []): array
    {
        $stm        = TbAdCost::getInstance();
        $today      = date('Y-m-d');
        $bindWheres = [];

        [
            $rangeStart, $rangeEnd,
        ] = [
            $wheres['range_date_start'] ?? $today,
            $wheres['range_date_end'] ?? $today,
        ];

        $bindWheres['time[<>]']   = [$rangeStart, $rangeEnd];
        $bindWheres['status[<>]'] = [0, 1];

        if (!empty($wheres['cp_game_id'])) {
            $bindWheres['cp_game_id'] = $wheres['cp_game_id'];
        }

        $cols = [
            'tday'          => Medoo::raw('DATE(`TIME`)'),
            'cost'          => Medoo::raw('sum(`cost`)'),
            'cost_discount' => Medoo::raw('sum(`cost_discount`)'),
        ];

        $bindWheres['GROUP'] = ['tday'];

        return array_column($stm->asArray()->findAll($bindWheres, $cols), null, 'tday');
    }

    /**
     * 获取付费详情(按天)
     *
     * @param array $wheres
     *
     * @return array|false
     * @throws \Exception
     */
    public function fetchPaidDaily(array $wheres = [])
    {
        $isUnionHistory = false;
        $today          = date('Y-m-d');
        $table          = 'ddc_platform.dwd_sdk_user_payment';
        $tableHistory   = 'ddc_platform.dwd_sdk_user_payment_2018';

        [
            $rangeStart, $rangeEnd,
        ] = [
            $wheres['range_date_start'] ?? $today,
            $wheres['range_date_end'] ?? $today,
        ];

        if (
            (new \DateTime($rangeStart))->format('Y') < 2022 ||
            (new \DateTime($rangeEnd))->format('Y') < 2022
        ) {
            $isUnionHistory = true;
        }

        $builder = new QueryFactory('mysql');
        $stm     = $builder->newSelect();

        $buildUpFunc = function (SelectInterface &$query, $rangeDate = [], $cpGameId = null) {
            if (!empty($rangeDate)) {
                $query->where("pay_time between '{$rangeDate[0]} 00:00:00' and '{$rangeDate[1]} 23:59:59'");
            }
            if (!empty($cpGameId)) {
                $query->where("cp_game_id='{$cpGameId}'");
            }
            $query->where('pay_result=1');
            $query->cols(['DATE(pay_time) as tday', 'SUM(money) as pay_money']);
            $query->groupBy(['tday']);
        };

        $stm->from($table);
        $buildUpFunc($stm, [$rangeStart, $rangeEnd], ($wheres['cp_game_id'] ?? null));
        if ($isUnionHistory) {
            $stm->union()->from($tableHistory);
            $buildUpFunc($stm, [$rangeStart, $rangeEnd], ($wheres['cp_game_id'] ?? null));
        }

        $result = \Plus::$app->ddc_platform->query($stm->__toString())->fetchAll(\PDO::FETCH_ASSOC);

        return array_column($result, null, 'tday');
    }
}
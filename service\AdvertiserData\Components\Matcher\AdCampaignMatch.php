<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\models\AdpPlatform\TbAdpCampaign;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

/**
 * 广告组可匹配的搜索条件
 *
 */
class AdCampaignMatch extends AdBaseMatch
{
    /**
     * @return array
     * @throws \RedisException
     */
    protected function matchFnList(): array
    {
        $new = [
            'campaign_id'   => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'campaign_name' => $this->matchCampaignName(),
            'user_os'       => $this->matchUserOs(),
        ];

        return array_merge(parent::matchFnList(), $new);
    }

    /**
     * 广告组名匹配
     *
     * @return \Closure
     */
    protected function matchCampaignName(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value, $params = []) {
            if (isset($params['tday'])) {
                $rangeStart = $params['tday'][0];
                $rangeEnd   = $params['tday'][1];
            }
            else {
                $rangeStart = date('Y-m-d', strtotime('-30 day'));
                $rangeEnd   = date('Y-m-d');
            }

            $ids      = (new TbAdpCampaign())->getCampaignIdByName($value, $rangeStart, $rangeEnd);
            $campaign = $this->getReflectKey('campaign_id');

            if ($ids) {
                $qb->where($campaign, new Parameter($ids));
            }
            else {
                $qb->where($campaign, 'error');
            }

        };
    }

    protected function matchUserOs(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value, $params = []) {
            if (!empty($params['user_os'])) {
                $campaign = $this->getReflectKey('campaign_id');
                $userOs   = $params['user_os'];
                $qb->where(function (SelectQuery $nqb) use ($campaign, $userOs) {
                    foreach ($userOs as $oo) {

                        if ($oo == 1) {
                            $nqb->orWhere(new Fragment("{$campaign} IN (select CAMPAIGN_ID from adp_platform.tb_adp_campaign where user_os = JSON_ARRAY('IOS'))"));
                        }
                        elseif ($oo == 2) {
                            $nqb->orWhere(new Fragment("{$campaign} IN (select CAMPAIGN_ID from adp_platform.tb_adp_campaign where user_os = JSON_ARRAY('ANDROID'))"));
                        }
                        elseif ($oo == 3) {
                            $nqb->orWhere( new Fragment("{$campaign} IN (select CAMPAIGN_ID from adp_platform.tb_adp_campaign where (JSON_CONTAINS(user_os, '[\"ANDROID\",\"IOS\"]' , '$') =1 or user_os is null))"));
                        }
                    }
                });
            }
        };
    }


}
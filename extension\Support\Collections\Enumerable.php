<?php

namespace app\extension\Support\Collections;

use app\extension\Support\Contracts\Arrayable;
use app\extension\Support\Contracts\Jsonable;

/**
 * 枚举类
 */
interface Enumerable extends Arrayable, \Countable, \IteratorAggregate, Jsonable, \JsonSerializable
{
    /**
     * @param Arrayable|iterable|null $items
     * @return mixed
     */
    public static function make($items = []);

    /**
     * @param               $number
     * @param callable|null $callback
     * @return mixed
     */
    public static function times($number, callable $callback = null);

    /**
     *
     * @return array
     */
    public function all(): array;

    /**
     * @param $from
     * @param $to
     * @return static
     */
    public static function range($from, $to);

    /**
     * @param $value
     * @return mixed
     */
    public static function wrap($value);

    /**
     * @param $value
     * @return mixed
     */
    public static function unwrap($value);

    /**
     * @return static
     */
    public static function empty();

    /**
     * @return mixed
     */
    public function collapse();

    /**
     * @param $items
     * @return mixed
     */
    public function diff($items);

    /**
     * @param $items
     * @return mixed
     */
    public function diffAssoc($items);

    /**
     * @param          $items
     * @param callable $callback
     * @return mixed
     */
    public function diffAssocUsing($items, callable $callback);

    /**
     * @param $items
     * @return mixed
     */
    public function diffKeys($items);

    /**
     * @param          $items
     * @param callable $callback
     * @return mixed
     */
    public function diffKeysUsing($items, callable $callback);

    /**
     * @template TValue
     * @template TKey
     *
     * @param callable(TValue, TKey): mixed $callback
     * @return mixed
     */
    public function each(callable $callback);

    /**
     * @param callable $callback
     * @return static
     */
    public function eachSpread(callable $callback);

    /**
     * @param $key
     * @param $operator
     * @param $value
     * @return mixed
     */
    public function every($key, $operator = null, $value = null);

    /**
     * @param $keys
     * @return mixed
     */
    public function except($keys);

    /**
     * @param callable|null $callback
     * @return mixed
     */
    public function filter(callable $callback = null);

    /**
     * @param          $value
     * @param callable $callback
     * @param callable $default
     * @return mixed
     */
    public function unless($value, callable $callback, callable $default);

    /**
     * @param $key
     * @param $operator
     * @param $value
     * @return mixed
     */
    public function where($key, $operator = null, $value = null);

    /**
     * @param $key
     * @return mixed
     */
    public function whereNull($key = null);

    /**
     * @param $key
     * @return mixed
     */
    public function whereNotNull($key = null);

    /**
     * @param      $key
     * @param      $values
     * @param bool $strict
     * @return mixed
     */
    public function whereIn($key, $values, bool $strict = false);

    /**
     * @param $key
     * @param $value
     * @return mixed
     */
    public function whereStrict($key, $value);

    /**
     * @param $key
     * @param $values
     * @return mixed
     */
    public function whereInStrict($key, $values);

    /**
     * @param $key
     * @param $values
     * @return mixed
     */
    public function whereBetween($key, $values);

    /**
     * @param $key
     * @param $values
     * @return mixed
     */
    public function whereNotBetween($key, $values);

    /**
     * @param class-string|array<array-key, class-string> $type
     * @return mixed
     */
    public function whereInstanceOf($type);

    /**
     * @param float $depth
     * @return mixed
     */
    public function flatten(float $depth = INF);

    /**
     * @template TValue
     * @template TKey
     * @return static<TValue, TKey>
     */
    public function flip();

    /**
     * @param $key
     * @param $default
     * @return mixed
     */
    public function get($key, $default = null);

    /**
     * @param callable $callback
     * @return mixed
     */
    public function map(callable $callback);


    /**
     * @param $items
     * @return mixed
     */
    public function replace($items);

    /**
     * @param $size
     * @return mixed
     */
    public function chunk($size);

    /**
     * @return mixed
     */
    public function values();

    /**
     * @return int
     */
    public function count(): int;

    /**
     * @param int $options
     * @return mixed
     */
    public function toJson(int $options = 0);

//    /**
//     * @return string
//     */
//    public function __toString();

    /**
     * @param bool $escape
     * @return mixed
     */
    public function escapeWhenCastingToString(bool $escape = true);

    /**
     * @param $key
     * @return mixed
     */
    public function __get($key);

    /**
     * @param $key
     * @return bool
     */
    public function has($key): bool;

    /**
     * @template TKey
     * @template TValue
     *
     * @param Arrayable<TKey, TValue>|iterable<TKey, TValue> $items
     * @return static
     */
    public function merge($items);

    /**
     * @param $callback
     * @return static
     */
    public function sort($callback = null);

    /**
     * @param int $options
     * @return static
     */
    public function sortDesc(int $options = SORT_REGULAR);

    /**
     * @param      $callback
     * @param int  $options
     * @param bool $descending
     * @return static
     */
    public function sortBy($callback, int $options = SORT_REGULAR, bool $descending = false);

    /**
     * @param     $callback
     * @param int $options
     * @return static
     */
    public function sortByDesc($callback, int $options = SORT_REGULAR);

    /**
     * @param int  $options
     * @param bool $descending
     * @return static
     */
    public function sortKeys(int $options = SORT_REGULAR, bool $descending = false);

    /**
     * @param int $options
     * @return mixed
     */
    public function sortKeysDesc(int $options = SORT_REGULAR);

}
<?php

namespace app\service\Media;

use app\extension\FakeDB\FakeDB;
use app\service\Media\Components\Matcher\LiveAccountMatcher;
use app\service\Media\Helper\MediaTableConst;
use Spiral\Database\Query\SelectQuery;

class LiveAccountInfoServ
{

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $qbMode
     * @return SelectQuery
     */
    public function commonTableQuery(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $qbMode = -1
    ): SelectQuery
    {
        $qb = $this->baseQueryBuilder($qbMode);

        $matcher = new LiveAccountMatcher([
            'media_platform_id' => 'type'
        ]);

        $matcher($qb, $params);

        return $qb;
    }

    /**
     * @param int $mode
     * @return SelectQuery
     */
    protected function baseQueryBuilder(int $mode = -1): SelectQuery
    {
        $db = $this->getConn();
        return $db->select()->from(MediaTableConst::BASE_LIVE_ACCOUNT);
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

}
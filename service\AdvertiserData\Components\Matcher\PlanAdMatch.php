<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Matcher\Traits\AdAccountAble;
use app\service\AdvertiserData\Components\Matcher\Traits\AdCampaignAble;
use app\service\AdvertiserData\Components\Matcher\Traits\AdChannelAble;
use app\service\AdvertiserData\Components\Matcher\Traits\DepartmentMatchAble;
use app\service\General\BizTagsServ;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class PlanAdMatch extends BaseMatch
{
    use AdChannelAble, DepartmentMatchAble, AdAccountAble, AdCampaignAble;

    public function processLine(): array
    {
        $newLine = [
            [$this, 'matchTDay'],
            [$this, 'matchChannelMainId'],
            [$this, 'matchChannelId'],
            [$this, 'matchDepartment'],
            [$this, 'matchUserId'],
            [$this, 'matchCampaignId'],
            [$this, 'matchCampaignName'],
            [$this, 'matchPlanId'],
            [$this, 'matchPlanName'],
            [$this, 'matchIsHasNatural'],
            [$this, 'matchDataScope'],
            [$this, 'matchMarketingGoal'],
        ];

        return array_merge(parent::processLine(), $newLine);
    }


    /**
     * @throws \Exception
     */
    protected function matchTDay(&$qb, array $params)
    {
        if (
            empty($params['range_date_start']) || empty($params['range_date_end'])
        ) return;

        $field     = $this->getReflectField('tday');
        $rangeDate = array_filter([
            $params['range_date_start'],
            $params['range_date_end'],
        ]);
        sort($rangeDate);
        $rangeDate = array_unique($rangeDate);

        foreach ($rangeDate as &$foo) {
            $foo = (new \DateTime($foo))->format('Y-m-d');
        }

        if (count($rangeDate) === 1) {
            $qb->where($field, $rangeDate[0]);
        }
        else {
            $qb->where($field, 'between', $rangeDate[0], $rangeDate[1]);
        }
    }

    /**
     * @param SelectQuery $qb
     * @param array $params
     * @return void
     * @throws \RedisException
     */
    protected function matchChannelId(&$qb, array &$params)
    {
        if (empty($params['channel_id'])) return;

        $data = $params['channel_id'];

        if (is_string($data) && str_contains($data, ',')) {
            $data = \explode(',', $data);
        }
        $data               = implode(',', Arr::wrap($data));
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelMatch       = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0,IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) IN (%s)",
            $planChannelsString, $data
        );


        $qb->where(new Fragment($channelMatch));
    }

    /**
     * @param $qb
     * @param array $params
     * @return void
     * @throws \RedisException
     */
    protected function matchChannelMainId(&$qb, array &$params)
    {
        if (empty($params['channel_main_id'])) return;
        $data = $params['channel_main_id'];

        if (is_string($data) && str_contains($data, ',')) {
            $data = \explode(',', $data);
        }

        $data = implode(',', Arr::wrap($data));

        $planChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        if (!empty($planChannels)) {
            $channelMatch = sprintf(
                "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) IN (%s)",
                implode(',', $planChannels), $data
            );
        }
        else {
            $channelMatch = "COALESCE(POWER.channel_main_id,0) AS channel_main_id IN ({$data})";
        }

        $qb->where(new Fragment($channelMatch));
    }

    /**
     * @param $qb
     * @param array $params
     * @return void
     */
    protected function matchMarketingGoal(&$qb, array &$params)
    {
        if (!isset($params['marketing_goal'])) return;
        // 都选的话即为全部
        if (empty(array_diff([1, 2], $params['marketing_goal']))) return;

        $markingGoal = $params['marketing_goal'][0];
        // var_dump($markingGoal);exit();

        if ($markingGoal == 1) {
            $qb->where(new Fragment("campaign_base.ext_data->>'$.marketing_goal' != 'LIVE'"));
        }
        elseif ($markingGoal == 2) {
            $qb->where(new Fragment("campaign_base.ext_data->>'$.marketing_goal' = 'LIVE'"));
        }

    }
}
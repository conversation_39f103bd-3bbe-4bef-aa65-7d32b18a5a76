<?php

namespace app\models\DdcPlatform;

use Plus\MVC\Model\ActiveRecord;

/**
 * @property int    id
 * @property string tday
 * @property int    cp_game_id
 * @property  int   game_id
 * @property int    channel_id
 * @property int    package_id
 * @property int    sv_key
 * @property int    pay_user
 * @property float  pay_money
 * @property int    pay_count
 * @property int    pay_user_new
 * @property float  pay_money_new
 * @property int    pay_count_new
 * @property string update_time
 * @property int    is_remove
 */
class DwsSvkeyAdPaymentDaily extends ActiveRecord
{
    protected $_primaryKey = 'id';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->ddc_platform;
        parent::__construct($data);
    }

    /**
     * 获取付费金额
     * @param $wheres
     * @return mixed
     */
    public function getPayMoney($wheres)
    {
        $where = ["1=1"];
        if (!empty($wheres['PACKAGE_ID'])) {
            $packageIds = implode(',', $wheres['PACKAGE_ID']);
            $where[]    = "a.PACKAGE_ID IN ({$packageIds})";

        }

        if (!empty($wheres['DEPARTMENT_ID'])) {
            $departmentIds = implode(',', $wheres['DEPARTMENT_ID']);
            $where[]       = "c.DEPARTMENT_ID in ($departmentIds)";

        }

        if (!empty($wheres['TDAY'])) {
            $tDay = $wheres['TDAY'];
            sort($tDay);
            $where[] = "TDAY between '{$tDay[0]}' and '{$tDay[1]}'";
        }

        $sql  = "select sum(PAY_MONEY) pay_money from dws_svkey_ad_payment_daily a join dataspy.tb_ad_svlink_conf b on a.SV_KEY=b.ID join dataspy.admin_user c on b.USER_ID=c.ID where  SV_KEY!=0 AND  " . implode(" AND ", $where);
        $data = $this->_db->query($sql)->fetch(\PDO::FETCH_ASSOC);
        return $data["pay_money"];
    }
}
<?php

namespace app\service\ConfigService\Tables;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\Mather;
use app\service\ConfigService\Constants\FieldTag;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class AdCampaignLtv implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    public function getFields($options = null)
    {
        $columnScope = Arr::get($options, 'column_scope');
        $today       = new \DateTime();

        $NNodes = array_merge(
            range(1, 60), [], Mather::findNumInScope(60, 720, 30)
        );

        if (isset($options['cp_game_id'])) {
            if (count($options['cp_game_id']) && $options['cp_game_id'][0] == 789) {
                $NNodes = array_merge(
                    range(1, 60), Mather::findNumInScope(60, 720, 30)
                );
            }
        }


        $collect = $this->getBaseFields([
            'baseCollect',
            'AdAccountBaseCollect',
            'campaignBaseCollect',
        ]);

        $collect = $collect->merge([
            'new_user'           => ['title' => '广告新增用户', 'sorter' => 'true',],
            'cost'               => ['title' => '返点前消耗金额', 'sorter' => true, ],
            'cost_discount'      => ['title' => '返点后消耗金额', 'sorter' => true, ],
            'new_user_cost'      => ['title' => '新用户成本',],
            'new_user_total_pay' => ['title' => '新用户累计付费',],
            'total_ltv'          => ['title' => '累计LTV',],
            'total_roi'          => ['title' => '累计ROI',],
        ]);

        // $nDays = days_apart($today, $options['range_date_start'] ?? $today);
        $nDays = 720;

        if (in_array('ltv', $columnScope)) {
            $ltvList = $this->ltvNCollect(Mather::findIn($nDays, $NNodes));
            $ltvList = FieldTag::tagClassifyToNField($ltvList, 'ltv',
                [
                    ['range' => [1, 44], 'classify' => ['attrs', FieldTag::LTV_GROUP_1]],
                    ['range' => [45, 180], 'classify' => ['attrs', FieldTag::LTV_GROUP_2]],
                    ['range' => [210, 360], 'classify' => ['attrs', FieldTag::LTV_GROUP_3]],
                    ['range' => [390, 720], 'classify' => ['attrs', FieldTag::LTV_GROUP_4]],
                ]);

            $collect = $collect->merge($ltvList);
        }

        if (in_array('roi', $columnScope)) {
            $roiList = $this->roiNCollect(Mather::findIn($nDays, $NNodes));
            $roiList = FieldTag::tagClassifyToNField($roiList, 'roi',
                [
                    ['range' => [1, 44], 'classify' => ['attrs', FieldTag::ROI_GROUP_1]],
                    ['range' => [45, 180], 'classify' => ['attrs', FieldTag::ROI_GROUP_2]],
                    ['range' => [210, 360], 'classify' => ['attrs', FieldTag::ROI_GROUP_3]],
                    ['range' => [390, 720], 'classify' => ['attrs', FieldTag::ROI_GROUP_4]],
                ]);

            $collect = $collect->merge($roiList);
        }

        return $this->formatStandard($collect);
    }
}
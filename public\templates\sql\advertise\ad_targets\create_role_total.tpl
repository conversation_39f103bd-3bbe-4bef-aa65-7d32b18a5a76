{include file="sql/advertise/ad_targets/create_role_detail.tpl"}
select count(1) as total_row
from (select a1.*,
a2.`time`                                                                       as last_login_time,
row_number() over (partition by a1.cp_game_id,a1.role_id order by `time` desc ) as row_set
from bigdata_dwd.dwd_sdk_role_login a2
right join (select b1.*, b2.package_id, b2.plan_id, b2.sv_key
from ddc_platform.dwd_sdk_adsource_game b2
right join role_detail b1 on b1.source_id = b2.source_id) a1
on a1.cp_game_id = a2.cp_game_id and a1.role_id = a2.role_id) t1
where
t1.row_set = 1
{if !empty($params)}
    {foreach $params as $kk => $chill}
        {if $kk eq 'package_id'}
            {if is_array($chill)}
                and package_id in ({$chill|join:','})
            {else}
                and package_id = '{$chill}'
            {/if}
        {/if}

        {if $kk eq 'plan_id'}
            {if is_array($chill)}
                and (plan_id in ({$chill|join:','}) or sv_key in ({$chill|join:','}))
            {else}
                and (plan_id = '{$chill}' or sv_key = '{$chill}')
            {/if}
        {/if}

    {/foreach}
{/if}


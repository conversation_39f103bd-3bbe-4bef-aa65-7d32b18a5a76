<?php

namespace app\service\DataSpy;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

class OperationHourly
{
    /**
     * @param array $params
     * @param array $groups
     * @param       $sort
     * @param array $columns
     *
     * @return array
     */
    public function getInfoByHourly(array $params, array $groups = [], $sort = null, array $columns = []): array
    {
        $table = 'dataspy.tb_operation_report_hour';
        $today = date('Y-m-d');

        $db = $this->getConn();
        $qb = $db->select()->from($table . ' as t_base');

        if (empty($columns)) {
            $columns = [
                new Fragment("DATE(STR_TO_DATE(tday, '%Y%m%d')) as tday"),
                new Fragment("hour as thour"),
                new Fragment('SUM(active_user_num_unique) as active_user'),
            ];
        }


        $dateStart = $params['range_date_start'] ?? $today;
        $dateEnd   = $params['range_date_end'] ?? $today;

        $qb->where(
            'TDAY', 'between',
            date('Ymd', strtotime($dateStart)),
            date('Ymd', strtotime($dateEnd))
        );

        if (!empty($params['cp_game_id'])) {
            $qb->where('cp_game_id' , new Parameter(Arr::wrap($params['cp_game_id'])));
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        $qb->columns($columns);

        return ['list' => $qb->fetchAll()];
    }

    /**
     * @param array $params
     * @param array $groups
     * @param       $sort
     * @param array $columns
     *
     * @return array
     */
    public function getExtInfoByHourly(array $params, array $groups = [], $sort = null, array $columns = [])
    {
        $table = 'dataspy.tb_operation_report_hour_ext';
        $today = date('Y-m-d');

        $db = $this->getConn();
        $qb = $db->select()->from($table . ' as t_base');

        if (empty($columns)) {
            $columns = [
                new Fragment("DATE(STR_TO_DATE(tday, '%Y%m%d')) as tday"),
                new Fragment("hour as thour"),
                new Fragment('SUM(cost_sum) as cost'),
                new Fragment('SUM(cost_discount_num) as cost_discount'),
            ];
        }


        $dateStart = $params['range_date_start'] ?? $today;
        $dateEnd   = $params['range_date_end'] ?? $today;

        $qb->where(
            'TDAY', 'between',
            date('Ymd', strtotime($dateStart)),
            date('Ymd', strtotime($dateEnd))
        );

        if (!empty($params['cp_game_id'])) {
            $qb->where('cp_game_id' , new Parameter(Arr::wrap($params['cp_game_id'])));
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        $qb->columns($columns);
        $sql = (clone $qb)->__toString();

        return ['list' => $qb->fetchAll()];
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('dataspy');
    }
}
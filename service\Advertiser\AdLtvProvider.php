<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;
use app\service\General\BizTagsServ;
use app\util\Common;
use Smarty\Exception;

/**
 * ltv离线查询
 *
 */
class AdLtvProvider
{
    const RESULT_INFO    = 1;
    const RESULT_SUMMARY = 2;
    const RESULT_TOTAL   = 4;
    const RESULT_ALL     = 7;


    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param bool  $isApiRule
     * @param int   $resultMode
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function getData(
        array $params,
        array $groups = [],
        array $sort = [],
        array $paginate = [],
        bool  $isApiRule = false,
        int   $resultMode = self::RESULT_ALL
    ): array
    {
        $result     = [];
        $db         = $this->getConn();
        $adChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $isRealtime = $params['is_realtime'] ?? 0;
        $minDate    = min($params['range_date']);
        $ltvType    = $params['ltv_type'] ?? 0;
        $modeTest   = $params['mode_test'] ?? 0;

        if (!$isRealtime) {
            $maxLtvNode = days_apart(date('Y-m-d', strtotime('-1day')), $minDate) + 1;
        }
        else {
            $maxLtvNode = days_apart(date('Y-m-d'), $minDate) + 1;
        }

        if (!$isApiRule) {
            $powerSQL = \Plus::$service->admin->powerSubSQL();
        }

        if ($resultMode & self::RESULT_INFO) {
            $infoTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_ltv/creative_ltv.tpl');

            $infoTpl->assign('mode_test', $modeTest);
            $infoTpl
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params)
                ->assign('groups', $groups)
                ->assign('max_ltv_node', $maxLtvNode)
                ->assign('ltv_type', $ltvType);

            if (isset($powerSQL)) {
                $infoTpl->assign('power_join_sql', $powerSQL);
            }

            if (!empty($paginate)) {
                $infoTpl->assign('paginate', $paginate);
            }

            if (!empty($sort)) {
                $infoTpl->assign('sorts', $sort);
            }
            else {
                $infoTpl->assign('sorts', ['new_user' => 'desc']);
            }
            $infoSQL        = $infoTpl->fetch();
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }

        if ($resultMode & self::RESULT_TOTAL) {
            $totalTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_ltv/creative_ltv_total.tpl');
            $totalTpl->assign('mode_test', $modeTest);

            $totalTpl
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params)
                ->assign('groups', $groups)
                ->assign('max_ltv_node', $maxLtvNode)
                ->assign('ltv_type', $ltvType);

            if (isset($powerSQL)) {
                $totalTpl->assign('power_join_sql', $powerSQL);
            }

            $totalSQL        = $totalTpl->fetch();
            $result['total'] = $db->query($totalSQL)->fetch()['total_count'] ?? 0;
        }

        if ($resultMode & self::RESULT_SUMMARY) {
            $summaryTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_ltv/creative_ltv.tpl');
            $summaryTpl->assign('mode_test', $modeTest);
            $summaryTpl
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params)
                ->assign('max_ltv_node', $maxLtvNode)
                ->assign('ltv_type', $ltvType);

            if (isset($powerSQL)) {
                $summaryTpl->assign('power_join_sql', $powerSQL);
            }
            $summarySQL        = $summaryTpl->fetch();
            $result['summary'] = $db->query($summarySQL)->fetch();
        }

        return $result;
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('doris_entrance');
    }
}
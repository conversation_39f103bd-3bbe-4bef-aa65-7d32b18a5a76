<?php

namespace app\ad_upload\strategies;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\contract\AdUploadStrategyInterface;

/**
 * 创角数据上报 策略
 * <AUTHOR>
 */
class CreateRoleUploadStrategy extends AdUploadStrategyInterface
{
    /**
     * 上报类型
     * @var string
     */
    public $action = AdBaseInterface::ACTION_CREATE_ROLE;

    /**
     * 最大id的数据
     * @var array
     */
    private $maxlastData = [];

    /**
     * 初始化上报点
     * @return void
     */
    public function initUploadLast()
    {
        parent::initUploadLast();
        $sql               = 'select max(id) as ID from tb_sdk_user_role_newlogin';
        $this->maxlastData = \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
        //排除已过期的未归因匹配id
        // 数量大于100 才进行排除，减少查询次数
        if (count($this->unmatchedIds) < 100) {
            return;
        }
        $before             = date('Y-m-d H:i:s', strtotime($this->unmatchedTime));
        $data               = \Plus::$app->origin_platform->select('tb_sdk_user_role_newlogin', 'id', [
            'id' => $this->unmatchedIds,
            'TIME[>]' => $before
        ]);
        $this->unmatchedIds = $data;
        if (empty($data)) {
            $this->unmatchedIds = [0];
        }
    }

    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    public function getData($uploadConfig, $packages): array
    {
        //获取补漏上报ID
        $unmatchedIdString = implode(',', $this->unmatchedIds);
        if (empty($packages) || $this->lastId <= 0) {
            return [];
        }
        //查询登录数据
        $condition  = "  P.CHANNEL_ID={$this->channelId} AND R.PACKAGE_ID IN ({$packages}) AND P.PACKAGE_ID IN ({$packages}) AND L.PACKAGE_ID IN ({$packages}) ";
        $condition .= !empty($this->timeBegin) && !empty($this->timeEnd) ?
            " AND R.TIME BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'" : " AND ((R.ID > {$this->lastId} ))";

        if (!empty($beginTime) && !empty($endTime)) {
            $condition .= " AND R.TIME BETWEEN '{$beginTime}' AND '{$endTime}'";
        } else {
            $condition .= " AND ((R.ID > {$this->lastId}  ) OR (R.ID IN ($unmatchedIdString)))";
        }

        $condition .= " AND P.OAID != '' ";

        $sql = "SELECT
                        R.ID,
                        P.DEVICE_KEY,
                        P.DEVICE_CODE,
                        P.OAID,
                        P.PACKAGE_ID,
                        P.DEVICE_ID,
                        P.MD5_DEVICE_ID,
                        P.GAME_ID,
                        P.OS,
                        P.CORE_ACCOUNT,
                        P.IP,
                        P.ANDROID_ID,
                        P.CHANNEL_ID,
                        P.SV_KEY,
                        P.CLICK_ID,
                        '{$this->action}' AS TYPE,
                        L.CALLBACK_URL 
                    FROM
                        tb_sdk_user_role_newlogin as R
                    JOIN tb_sdk_user_newlogin_package AS P USING(PACKAGE_ID, GAME_ID, CORE_ACCOUNT)
                    JOIN tb_ad_click_match_log as L ON L.ID = P.CLICK_ID
                    WHERE
                       {$condition}
                    GROUP BY
                        R.PACKAGE_ID,
                        R.GAME_ID,
                        R.ROLE_ID
                    ORDER BY
                        R.ID ASC";
        return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 设置如果上一步getData 获取数据为空，设置最大上报id
     * 避免长期没有数据上报的渠道，查询过多数据
     * @return void
     */
    public function setMaxLastId():void
    {
        $this->setLastId($this->maxlastData);
    }
}

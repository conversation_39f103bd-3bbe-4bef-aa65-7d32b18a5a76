<?php

namespace app\models\baseConfPlatform;

use Plus\MVC\Model\ActiveRecord;

/**
 * @table tb_ad_channel_conf
 * @property int id
 * @property string ad_code
 * @property string ad_name
 * @property string back_param_android
 * @property string back_param_ios
 * @property string upload_method
 * @property string add_time
 * @property string parent_id
 */
class TbAdChannelConf extends ActiveRecord
{
    public $_primaryKey = 'id';

    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->base_conf_platform;
    }
}
<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\advertise\AdIncomeDashLogic;
use Smarty\Exception;

/**
 * 广告营收看板 接口
 * @date 2024/07/29
 *
 */
class AdIncomeDashController extends BaseTableController
{
    /**
     * @param Collection $params
     * @return array
     * @throws Exception
     */
    protected function data(Collection $params): array
    {
        $config = [
            ['prop' => 'tday', 'label' => '日期'],
            ['prop' => 'pull_cnt', 'label' => '拉取量', 'series' => ['type' => 'line']],
            ['prop' => 'exposure_cnt', 'label' => '曝光量', 'series' => ['type' => 'line']],
            ['prop' => 'exposure_percent', 'label' => '曝光率', 'series' => ['type' => 'line']],
            ['prop' => 'click_cnt', 'label' => '点击量', 'series' => ['type' => 'line']],
            ['prop' => 'click_percent', 'label' => '点击率', 'series' => ['type' => 'line']],
            ['prop' => 'ecpm', 'label' => 'eCPM', 'series' => ['type' => 'line']],
            ['prop' => 'income_rmb', 'label' => '收入', 'series' => ['type' => 'line']],
        ];

        $result = (new AdIncomeDashLogic())->getList($params->toArray());

        return array_merge(['config' => $config], $result);
    }

    /**
     * @param Collection $params
     * @return array[]
     */
    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '日期', 'dataIndex' => 'tday'],
            ['title' => '拉取量', 'dataIndex' => 'pull_cnt'],
            ['title' => '曝光量', 'dataIndex' => 'exposure_cnt'],
            ['title' => '曝光率', 'dataIndex' => 'exposure_percent'],
            ['title' => '点击量', 'dataIndex' => 'click_cnt'],
            ['title' => '点击率', 'dataIndex' => 'click_percent'],
            ['title' => 'eCPM', 'dataIndex' => 'ecpm'],
            ['title' => '收入', 'dataIndex' => 'income_rmb'],
        ];

        return ['fields' => $fields];
    }

    protected function registerParams(): Collection
    {
        $last7DaysAgo = date('Y-m-d', strtotime('-7days'));
        $today        = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start', 'default' => $last7DaysAgo], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
            ['field' => 'cp_game_id'],
            ['field' => 'game_id'],
            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups', 'default' => ['tday']],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }
}
<?php

namespace app\ad_upload\deduction;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\contract\DeductionStrategyInterface;
use app\ad_upload\tool\AlternatingLoop;
use Plus\Util\StringUtil;

/**
 * 自定义扣量v2
 * <AUTHOR>
 */
class CustomDeductionStrategy extends DeductionStrategyInterface
{

    use DeductionStrategyTrait;

    /**
     * 全部上报
     */
    const RATIO_ALL_UPLOAD = 1;
    /**
     * 全部过滤
     */
    const RATIO_ALL_FILTER = 2;

    /**
     * 循环上报
     */
    const RATIO_LOOP = 3;

    /**
     * 正常上报
     */
    const RATIO_NORMAL = 4;

    /**
     * 时间检查
     * @return bool
     * @deprecated paymentTime 中实现
     */
    public function timeCheck(): bool
    {
        return true;
    }

    /**
     * 返回是否通过扣量检查
     * @return bool
     */
    public function isPass(): bool
    {
        if ($this->config['kind'] != 3) {
            return false;
        }
        $configs  = $this->config['config_data']['rule'];
        $data     = $this->data;
        $checkLog = [];

        foreach ($configs as $c => $conf) {
            if (!$conf['enabled']) {
                continue;
            }
            $passNum = 0;
            foreach ($conf['rules'] as $rule) {
                if (!$this->conditionCheck($rule)) {
                    \Plus::$app->log->alert('不合法：' . $rule['operator'] . '|' . $data['REPORT_RULE_ID'], [], AdBaseInterface::LOG_DIR);
                    continue;
                }
                if ($rule['operator'] == '=') {
                    $rule['operator'] = '==';
                }
                $fnName = lcfirst(StringUtil::convertToCamelHump($rule['field'], '_'));
                if (!method_exists($this, $fnName)) {
                    \Plus::$app->log->alert('不存在：' . $rule['field'] . '|' . $data['REPORT_RULE_ID'], [], AdBaseInterface::LOG_DIR);
                    continue;
                }
                $rs                           = call_user_func_array([$this, $fnName], [$rule, $data]);
                $checkLog[$c][$rule['field']] = $rs;
                if ($rs['result']) {
                    $passNum++;
                }
            }
            //所有检查函数都通过
            if ($passNum == count($conf['rules'])) {
                \Plus::$app->log->info($data['ORDER_ID'] . '_通过：' . json_encode($checkLog, JSON_UNESCAPED_UNICODE), [], AdBaseInterface::LOG_DIR);
                //上报日志
                $this->log($conf, true, '', $checkLog);
                return true;
            }
        }// end foreach()
        \Plus::$app->log->info($data['ORDER_ID'] . '_未通过：' . json_encode($checkLog, JSON_UNESCAPED_UNICODE), [], AdBaseInterface::LOG_DIR);
        //兜底上报
        $this->log(['ratio' => ['type' => self::RATIO_NORMAL]], true, '', $checkLog);
        return true;
    }

    /**
     * 上报日志
     *
     *  $reported_behavior = 1 会拦截上报，记录日志
     * 上报行为类别(0-正常上报, 1-次数扣减, 2-金额扣减,3-虚拟订单上报)",
     * @param array  $conf     配置
     * @param bool   $pass     是否通过
     * @param string $error    不上报原因
     * @param array  $checkLog 检查结果
     * @return void
     *
     * @var \app\ad_upload\contract\AdBaseInterface::uploadBefore
     */
    public function log($conf, $pass = true, $error = '不上报原因：匹配上报规则失败', $checkLog = [])
    {
        $data              = $this->data;
        $reported_behavior = 0; // 默认：正常上报
        $reportMoney       = $data['MONEY'];
        $amountStr         = $conf['ratio']['amount'] ?? '';

        switch ($conf['ratio']['type']) {
            case self::RATIO_ALL_UPLOAD:
                [$reportMoney, $reported_behavior] = $this->getRandomAmount($amountStr);
                break;

            case self::RATIO_LOOP:
                $cacheKey = sprintf(
                    'cltj:rule_%s:%s:%s:%s',
                    $this->config['id'],
                    strtotime($this->config['update_time']),
                    $data['CORE_ACCOUNT'],
                    md5(json_encode($conf)),
                );
                $p        = new AlternatingLoop(\Plus::$app->redis82, $cacheKey, $conf['ratio']['start'], $conf['ratio']['stop']);
                $next     = $p->next();
                \Plus::$app->log->notice(['cache_key' => $cacheKey, 'next' => $next, 'order_id' => $data['ORDER_ID']]);
                if ($next) {
                    [$reportMoney, $reported_behavior] = $this->getRandomAmount($amountStr);
                } else {
                    $reported_behavior = 1;
                    $reportMoney = 0;
                }
                break;

            case self::RATIO_ALL_FILTER:
                // 不上报
                $reported_behavior = 1;
                $reportMoney       = 0;
                break;
        }// end switch()

        // 记录上报金额
        $data['MONEY_REPORT']  = $reportMoney;
        $data['MONEY_ACTUALY'] = $data['MONEY'];
        $data['MONEY']         = $data['MONEY_REPORT'];

        // 记录日志
        $reportedLog = [
            'reported_money'         => $reportMoney,
            'reported_behavior'      => $reported_behavior,
            'reported_rule_id'       => $this->config['id'] ?? 0,
            'reported_behavior_rule' => str_replace("'", '“', json_encode(['rule' => $conf, 'check' => $checkLog], JSON_UNESCAPED_UNICODE)),
            'no_reported_origin'     => $reported_behavior == 1 ? '不上报原因：次数扣减' : '',
        ];

        $data['paid_report_log'] = array_merge($reportedLog, $data['paid_report_log']);
        if (!$pass) {
            $data['paid_report_log']['reported_rule_id']       = 0;
            $data['paid_report_log']['reported_behavior_rule'] = '';
            $data['paid_report_log']['no_reported_origin']     = $error;
        }

        $this->data = $data;
    }

    /**
     * 1. 角色等级检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function roleLevel($rule, $data)
    {
        $condition = "return {$data['ROLE_RANK']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition];
    }

    /**
     * 2. 支付次数检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function paymentCount($rule, $data)
    {
        $coreAccount = $data['CORE_ACCOUNT'] ?? '';
        $cpGameId    = $data['CP_GAME_ID'] ?? 0;
        $times       = $this->getPaidTimesByCpAccount($cpGameId, $coreAccount, $data['PAY_TIME']);
        $times       = $times['PAY_TIMES'] ?? 0;
        $condition   = "return {$times} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition];
    }

    /**
     * 3. 支付总金额检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function totalPayment($rule, $data)
    {
        $coreAccount = $data['CORE_ACCOUNT'] ?? '';
        $cpGameId    = $data['CP_GAME_ID'] ?? 0;
        $money       = $this->getTotalPaidAmount($cpGameId, $coreAccount, $data['PAY_TIME']);
        $money       = $money['TOTAL_AMOUNT'] ?? 0;
        $condition   = "return {$money} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition];
    }

    /**
     * 4. 核心账号检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function coreAccount($rule, $data)
    {
        return $this->textCompare($rule, $data['CORE_ACCOUNT']);
    }

    /**
     * 5. 广告新增天数 检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function adNewDays($rule, $data)
    {
        $newLoginTime = $data['NEWLOGIN_TIME'] ?? '';
        if (empty($newLoginTime)) {
            $userTime     = $this->getUserTime($data['PACKAGE_ID'], $data['CORE_ACCOUNT']);
            $newLoginTime = $userTime['newLoginTime'];
        }
        if (empty($newLoginTime)) {
            return ['result' => false, 'condition' => 'empty newLoginTime'];
        }
        $now             = new \DateTime(date('Y-m-d', strtotime($data['PAY_TIME'])));
        $newLoginTimeObj = new \DateTime(date('Y-m-d', strtotime($newLoginTime)));
        $days            = $now->diff($newLoginTimeObj)->days + 1;

        $condition = "return {$days} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition];
    }

    /**
     * 6. 广告激活天数 检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function adActiveDays($rule, $data)
    {
        $activeTime = $data['ACTIVATE_TIME'] ?? '';
        if (empty($activeTime)) {
            $userTime   = $this->getUserTime($data['PACKAGE_ID'], $data['CORE_ACCOUNT']);
            $activeTime = $userTime['activeTime'];
        }
        if (empty($activeTime)) {
            return ['result' => false, 'condition' => 'empty activeTime'];
        }
        $now           = new \DateTime(date('Y-m-d', strtotime($data['PAY_TIME'])));
        $activeTimeObj = new \DateTime(date('Y-m-d', strtotime($activeTime)));
        $days          = $now->diff($activeTimeObj)->days + 1;

        $condition = "return {$days} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition];
    }

    /**
     * 7. 归因渠道 检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function sourceChannel($rule, $data)
    {
        $channelId = $data['CHANNEL_ID'] ?? 0;
        $condition = "return {$channelId} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition];
    }


    /**
     * 8. 归因账号Id 检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function sourceAccountId($rule, $data)
    {
        $account = $this->getAdAccountId($data['CHANNEL_ID'], $data['PLAN_ID']);
        return $this->textCompare($rule, $account);
    }

    /**
     * 9. 归因项目ID 检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function sourceProjectId($rule, $data)
    {
        $projectId = $data['CAMPAIGN_ID'];
        return $this->textCompare($rule, $projectId);
    }

    /**
     * 10. 归因计划ID 检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function sourcePlanId($rule, $data)
    {
        $projectId = $data['PLAN_ID'];
        return $this->textCompare($rule, $projectId);
    }

    /**
     * 11. 归因包号 检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function sourcePackage($rule, $data)
    {
        $condition = "return '{$data['PACKAGE_ID']}' {$rule['operator']} '{$rule['value']}';";
        return ['result' => eval($condition), 'condition' => $condition];
    }

    /**
     * 12. 支付金额检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function paymentAmount($rule, $data)
    {
        $condition = "return {$data['MONEY']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition];
    }

    /**
     * 13. 支付方式 检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function paymentMethod($rule, $data)
    {
        $condition = "return '{$data['PAYWAY']}' {$rule['operator']} '{$rule['value']}';";
        return ['result' => eval($condition), 'condition' => $condition];
    }

    /**
     * 14. 付费时间 检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function paymentTime($rule, $data)
    {
        $t1        = strtotime($data['PAY_TIME']);
        $t2        = strtotime($rule['value']);
        $condition = "return {$t1} {$rule['operator']} {$t2};";
        return ['result' => eval($condition), 'condition' => $condition];
    }

    /**
     * 15. 归因计划ROI1   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planRoi1($rule, $data)
    {
        $rs = $this->getPlanRoiCost(1, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['roi']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 16. 归因计划ROI2   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planRoi2($rule, $data)
    {
        $rs = $this->getPlanRoiCost(2, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['roi']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 17. 归因计划ROI3   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planRoi3($rule, $data)
    {
        $rs = $this->getPlanRoiCost(3, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['roi']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 18. 归因计划ROI4   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planRoi4($rule, $data)
    {
        $rs = $this->getPlanRoiCost(4, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['roi']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 19. 归因计划ROI5   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planRoi5($rule, $data)
    {
        $rs = $this->getPlanRoiCost(5, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['roi']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 20. 归因计划ROI6   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planRoi6($rule, $data)
    {
        $rs = $this->getPlanRoiCost(6, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['roi']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 21. 归因计划ROI7   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planRoi7($rule, $data)
    {
        $rs = $this->getPlanRoiCost(7, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['roi']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 22. 归因计划1日付费次成本   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planPaymentCountCost1($rule, $data)
    {
        $rs = $this->getPlanRoiCost(1, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['cost']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 23. 归因计划2日付费次成本   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planPaymentCountCost2($rule, $data)
    {
        $rs = $this->getPlanRoiCost(2, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['cost']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 24. 归因计划3日付费次成本   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planPaymentCountCost3($rule, $data)
    {
        $rs = $this->getPlanRoiCost(3, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['cost']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 25. 归因计划4日付费次成本   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planPaymentCountCost4($rule, $data)
    {
        $rs = $this->getPlanRoiCost(4, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['cost']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 26. 归因计划5日付费次成本   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planPaymentCountCost5($rule, $data)
    {
        $rs = $this->getPlanRoiCost(5, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['cost']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 27. 归因计划6日付费次成本   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planPaymentCountCost6($rule, $data)
    {
        $rs = $this->getPlanRoiCost(6, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['cost']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }

    /**
     * 28. 归因计划7日付费次成本   检查
     * @param array $rule 规则
     * @param array $data 订单数据
     * @return array
     */
    private function planPaymentCountCost7($rule, $data)
    {
        $rs = $this->getPlanRoiCost(7, $data);
        if (is_string($rs)) {
            return ['result' => false, 'condition' => $rs];
        }
        $condition = "return {$rs['cost']} {$rule['operator']} {$rule['value']};";
        return ['result' => eval($condition), 'condition' => $condition, 'cal' => $rs['cal']];
    }
}

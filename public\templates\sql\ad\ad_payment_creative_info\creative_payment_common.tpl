with dashboard_info as (
select
tday,
a1.cp_game_id,
{if isset($ad_channels) }
    COALESCE( IF(power.channel_id not in ({$ad_channels|join:','}), power.channel_id, IF(a1.channel_id != 0, IF(a1.channel_id =1013, 4, a1.channel_id), power.channel_id) ) ,0) as promotion_channel_id,
    COALESCE( IF(power.channel_id not in ({$ad_channels|join:','}), power.ad_user_id, IF(a1.user_id != 0, a1.user_id, power.ad_user_id)),0 ) as dim_user_id,
{else}
    a1.channel_id as promotion_channel_id,
    a1.user_id as dim_user_id,
{/if}
a1.campaign_id,
a1.plan_id,
a1.creative_id,
a1.main_channel_id,
a1.account_id,
a2.ad_account,
power.platform_id                     as platform_id,
a4.advertiser_name                    as account_name,
a1.is_ad_data                         as is_ad_data,
a1.is_appointment                     as is_appointment,
a1.marketing_goal                     as marketing_goal,
power.popularize_v2_id                as promotion_id,
power.app_show_id                     as app_show_id,
{if $paid_mode == 'pay_money'}
    a1.pay_user_list
{else}
    a1.pay_user_new_list
{/if}
from (
select *
from bigdata_dws.dws_ad_creative_daily_full
{if !empty($params)}
    {assign var="mark_first" value=1}
    {foreach $params as $kk => $foo}
        {if $kk eq 'range_date'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            tday between '{$foo[0]}' and '{$foo[1]}'
            {continue}
        {/if}
        {if $kk eq 'cp_game_id'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            {if is_array($foo)}
                cp_game_id in ({$foo|join:','})
            {else}
                cp_game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            {if is_array($foo)}
                game_id in ({$foo|join:','})
            {else}
                game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'package_id'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            {if is_array($foo)}
                package_id in ({$foo|join:','})
            {else}
                package_id = {$foo}
            {/if}
            {continue}
        {/if}
        {if $kk eq 'account_id'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            account_id like  '{'%'|cat:$foo|cat:'%'}'
            {continue}
        {/if}
        {if $kk eq 'campaign_name'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            campaign_id in (select distinct campaign_id from adp_platform.tb_adp_campaign where campaign_name like '{'%'|cat:$foo|cat:'%'}')
            {continue}
        {/if}
        {if $kk eq 'campaign_id'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            {if is_array($foo)}
                campaign_id in ({$foo|join:','})
            {else}
                campaign_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'plan_name'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            (
            plan_id in (select distinct plan_id from adp_platform.tb_adp_plan_base where plan_name like '{'%'|cat:$foo|cat:'%'}')
            or plan_id in (select distinct id  from dataspy.tb_ad_svlink_conf where aid like '{'%'|cat:$foo|cat:'%'}')
            )
            {continue}
        {/if}
        {if $kk eq 'plan_id'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            {if is_array($foo)}
                plan_id in ({$foo|join:','})
            {else}
                plan_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'data_scope'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            {if $foo eq 1}
                is_ad_data = 1
            {elseif $foo eq 2}
                is_ad_data = 0
            {/if}
            {continue}
        {/if}
        {if $kk eq 'marketing_goal'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            {if $foo neq [1,2] || $foo neq [2,1]}
                {if in_array(1, $foo)}
                    marketing_goal != 2
                {/if}
                {if in_array(2, $foo)}
                    marketing_goal = 2
                {/if}
            {/if}
            {continue}
        {/if}
        {if $kk eq 'is_has_appointment'}
            {if empty($foo)} {if !$mark_first} and {else} where {$mark_first=0} {/if} is_appointment != 1 {/if}
            {continue}
        {/if}
        {if $kk eq 'is_has_natural'}
            {if empty($foo)} {if !$mark_first} and {else} where {$mark_first=0} {/if} channel_id > 0 {/if}
            {continue}
        {/if}
        {if $kk eq 'dim_user_os'}
            {if !$mark_first} and {else} where {$mark_first=0} {/if}
            {if is_array($foo)}
                (
                {foreach $foo as $ii => $chill}
                    {if !$chill@first} or {/if}
                    {if $chill eq 'IOS'} campaign_id in (select campaign_id from adp_platform.tb_adp_campaign where user_os = '["IOS"]'){/if}
                    {if $chill eq 'ANDROID'} campaign_id in (select campaign_id from adp_platform.tb_adp_campaign where user_os = '["ANDROID"]') {/if}
                    {if $chill eq '混投'} campaign_id not in (select campaign_id from adp_platform.tb_adp_campaign where user_os IN ('["IOS"]', '["ANDROID"]') ) {/if}
                {/foreach}
                )
            {else}
                {if $foo eq 'IOS'} campaign_id in (select campaign_id from adp_platform.tb_adp_campaign where user_os = '["IOS"]'){/if}
                {if $foo eq 'ANDROID'} campaign_id in (select campaign_id from adp_platform.tb_adp_campaign where user_os = '["ANDROID"]') {/if}
                {if $foo eq '混投'} campaign_id not in (select campaign_id from adp_platform.tb_adp_campaign where user_os IN ('["IOS"]', '["ANDROID"]') ){/if}
            {/if}
        {/if}
    {/foreach}
{/if}
) a1
{if !empty($power_join_sql) && $power_join_sql == 'base_conf_platform.tb_package_detail_conf'}
    join {$power_join_sql} power on a1.package_id = power.package_id
{else}
    join base_conf_platform.tb_package_detail_conf power on a1.package_id = power.package_id
{/if}
left join base_conf_platform.tb_ad_account_conf a2 on a1.account_id = a2.account_id
left join adp_platform.tb_adp_oauth a4 on a1.account_id = a4.advertiser_id and a1.main_channel_id = a4.channel_id
{if !empty($params)}
    {assign var="tag_first_where" value=1}
    {foreach $params as $vv => $og}
        {if $vv eq 'app_show_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($og)}
                power.app_show_id in ({$og|join:','})
            {else}
                power.app_show_id = '{$og}'
            {/if}
            {continue}
        {/if}
        {if $vv eq 'platform_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($og)}
                power.platform_id in ({$og|join:','})
            {else}
                power.platform_id = '{$og}'
            {/if}
            {continue}
        {/if}
        {if $vv eq 'promotion_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($og)}
                power.popularize_v2_id in ({$og|join:','})
            {else}
                power.popularize_v2_id = '{$og}'
            {/if}
            {continue}
        {/if}
        {if $vv eq 'ad_account'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            a2.ad_account like '{'%'|cat:$og|cat:'%'}'
            {continue}
        {/if}
    {/foreach}
{/if}
),
creative_dash as (
select
{if $paid_mode == 'pay_money'}
    pay_user_list
{else}
    pay_user_new_list
{/if}
from dashboard_info t1
left join base_conf_platform.tb_base_channel_conf t2 on t1.promotion_channel_id = t2.channel_id
left join dataspy.admin_user t3 on t1.dim_user_id = t3.id
{if !empty($params)}
    {assign var="tag_first" value=1}
    {foreach $params as $key => $item}
        {if $key eq 'channel_id'}
            {if !$tag_first} and {else} where {$tag_first=0} {/if}
            {if is_array($item)}
                t1.promotion_channel_id in ({$item|join:','})
            {else}
                t1.promotion_channel_id = {$item}
            {/if}
        {/if}
        {if $key eq 'channel_main_id'}
            {if !$tag_first} and {else} where {$tag_first=0} {/if}
            {if is_array($item)}
                channel_main_id in ({$item|join:','})
            {else}
                channel_main_id = {$item}
            {/if}
        {/if}
        {if $key eq 'user_id'}
            {if !$tag_first} and {else} where {$tag_first=0} {/if}
            {if is_array($item)}
                t1.dim_user_id in ({$item|join:','})
            {else}
                t1.dim_user_id = {$item}
            {/if}
        {/if}
        {if $key eq 'department_id'}
            {if !$tag_first} and {else} where {$tag_first=0} {/if}
            {if is_array($item)}
                t3.department_id in ({$item|join:','})
            {else}
                {if $item == 0}
                    (t3.department_id is null or t3.department_id =0)
                {else}
                    t3.department_id = {$item}
                {/if}
            {/if}
        {/if}
    {/foreach}
{/if}
),
source_id_list as (
    select * from (select core_source from creative_dash lateral view explode( {if $paid_mode == 'pay_money'} pay_user_list {else} pay_user_new_list {/if}) tmp1 as core_source) t1
)
<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\advertise\AdCreativePassLogic;


class PilotSchemeController extends BaseTableController
{

    /**
     * @param Collection $params
     * @return array
     * @throws \Smarty\Exception
     */
    protected function data(Collection $params): array
    {
        if ($params->has('groups')) {
            $groups = $params->pull('groups');
        }
        else {
            $groups = ['tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id'];
        }

        $options = $params->toArray();
        $logic   = new AdCreativePassLogic();
        return $logic->getRowInfo($options, $groups);
    }

    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '时间点', 'dataIndex' => 'tday'],
            ['title' => '广告新增用户', 'dataIndex' => 'denominator'],
            ['title' => '通过人数', 'dataIndex' => 'numerator'],
            ['title' => '通过率', 'dataIndex' => 'pass_rate'],
        ];

        return ['fields' => $fields];
    }
}
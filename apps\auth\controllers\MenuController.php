<?php

namespace app\apps\auth\controllers;

use app\service\Auth;
use app\service\Logs\SystemAccessLogServ;
use Plus\MVC\Controller\JsonController;


/**
 * Class Menu
 *
 * <AUTHOR>
 */
class MenuController extends JsonController
{
    public function __construct()
    {
        $this->authService = new Auth();
    }

    /**
     * get menu
     *
     * @param array $data 请求参数
     *
     * @return array
     */
    public function listAction()
    {
        // 记录开始时间
        $start_time = microtime(true);

        $username = \Plus::$service->admin->getUsername();
        if (!$username) {
            return $this->success(["menu" => [], "button" => []]);
        }
        //获取用户所有权限
        $params       = ["userList" => [$username], "type" => ["PAGE", "API"]];
        $userAuthList = (new Auth())->getUserAuthorizeList($params);
        $userAuthList = $userAuthList["data"][$username];
        $menuList     = [];
        $buttonList   = [];
        $apiList      = [];

        // 记录结束时间
        $end_time = microtime(true);
        // 计算并输出执行时间
        \Plus::$app->log->info(["message" => "菜单查询执行时间： " . ($end_time - $start_time) . " 秒"], [], 'index');


        foreach ($userAuthList as $item) {
            switch ($item["type"]) {
                case "PAGE":
                    $menuList[] = $item;
                    break;
                case "API":
                    $apiList[] = array_merge($item, ['permissions' => ['create', 'update', 'delete']]);
                    break;
            }
        }
        \Plus::$app->redis->set($username . ":menu_fields", ""); //菜单字段
        return $this->success(["menu" => $menuList, "button" => $buttonList, "api" => $apiList]);
    }

    //前端页面访问日志收集
    public function collectAction($data)
    {
        if (!$data || !isset($data["uri"])) {
            return $this->error("uri参数缺失");
        }
        return $this->success([]);
    }

    /**
     * 获取最近访问菜单
     *
     * @return array
     */
    public function recentVisitsAction(): array
    {
        $user = \Plus::$service->admin->getUsername();

        return $this->success((new SystemAccessLogServ())->getRecentVisits($user));
    }
}

<?php

namespace app\ad_upload_tmp\deduction;

use app\ad_upload_tmp\contract\DeductionStrategyInterface;
use app\ad_upload_tmp\tool\AlternatingProbability;

/**
 * 按次数扣量
 * <AUTHOR>
 */
class TimesDeductionStrategy extends DeductionStrategyInterface
{
    use DeductionStrategyTrait;
    /**
     * 返回是否通过扣量检查
     * @return bool
     */
    public function isPass(): bool
    {
        if ($this->config['kind'] != 1) {
            return false;
        }
        $conf = $this->config['config_data'];
        $data = $this->data;

        $checkFunction = [
            [$this, 'checkPayTimes'],
            [$this, 'checkPaySingle'],
            [$this, 'checkPayTotal'],
            [$this, 'checkRoleRank'],
            [$this, 'checkOnlineTime'],
        ];

        $passNum = 0;
        foreach ($checkFunction as $v) {
            if (call_user_func_array($v, [$conf, $data])) {
                $passNum++;
            }
        }
        //所有检查函数都通过
        if ($passNum == count($checkFunction)) {
            //概率
            $cacheKey = sprintf('cltj:rule_%s:%s', $this->config['id'], strtotime($this->config['update_time']));
            $p        = new AlternatingProbability(\Plus::$app->redis82, $cacheKey, $conf['ratio'], 10);
            if ($p->next()) {
                //上报日志
                $data['paid_report_log']['reported_behavior']      = 1;
                $data['paid_report_log']['reported_rule_id']       = $this->config['id'] ?? 0;
                $data['paid_report_log']['reported_behavior_rule'] = json_encode($conf, JSON_UNESCAPED_UNICODE);
                $data['paid_report_log']['no_reported_origin']     = '命中扣减次数规则';
                $data['paid_report_log']['reported_money']         = 0;
                /**
                 * 会拦截上报，记录日志
                 * @var \app\ad_upload\contract\AdBaseInterface::uploadBefore
                 */
                $this->data = $data;
                return true;
            } else {
                return false;
            }
        }// end if()
        return false;
    }

    /**
     * 检查在线时长
     * @param array $conf 配置
     * @param array $data 数据
     * @return bool
     */
    protected function checkOnlineTime($conf, $data)
    {
        $conf['online_time'] = array_filter($conf['online_time'] ?? [], function ($item) {
            return $item !== null;
        });
        if (empty($conf['online_time'])) {
            return true;
        }
        $rangeOnlineTime = $conf['online_time'];
        $coreAccount     = $data['CORE_ACCOUNT'] ?? '';
        $cpGameId        = $data['CP_GAME_ID'] ?? 0;

        $onlineInfo = $this->getOnlineTime($cpGameId, $coreAccount, $data['PAY_TIME']);
        $onlineTime = $onlineInfo['ONLINE_TIME'] ?? 0;

        if ($onlineTime >= $rangeOnlineTime[0] && $onlineTime <= $rangeOnlineTime[1]) {
            return true;
        }
        return false;
    }

    /**
     * 角色等级
     * @param array $conf 配置
     * @param array $data 数据
     * @return bool
     */
    protected function checkRoleRank($conf, $data)
    {
        $conf['role_rank'] = array_filter($conf['role_rank'] ?? [], function ($item) {
            return $item !== null;
        });
        if (empty($conf['role_rank'])) {
            return true;
        }
        $roleRank      = $data['ROLE_RANK'];
        $rangeRoleRank = $conf['role_rank'];

        if ($roleRank >= $rangeRoleRank[0] && $roleRank <= $rangeRoleRank[1]) {
            return true;
        }
        return false;
    }

    /**
     * 检查付费次数
     * @param array $conf 配置
     * @param array $data 数据
     * @return bool
     */
    protected function checkPayTimes($conf, $data)
    {
        $coreAccount = $data['CORE_ACCOUNT'] ?? '';
        $cpGameId    = $data['CP_GAME_ID'] ?? 0;
        // 判断付费次数是否在该范围内
        $conf['pay_times'] = array_filter($conf['pay_times'] ?? [], function ($item) {
            return $item !== null;
        });
        $payTimes          = $conf['pay_times'];
        if (empty($payTimes)) {
            return true;
        }
        sort($payTimes);
        $paidTimes = $this->getPaidTimesByCpAccount($cpGameId, $coreAccount, $data['PAY_TIME']);
        $paidTimes = $paidTimes['PAY_TIMES'] ?? 0;
        if ($paidTimes >= $payTimes[0] && $paidTimes <= $payTimes[1]) {
            return true;
        }
        return false;
    }

    /**
     * 单次付费金额
     * @param array $conf 配置
     * @param array $data 数据
     * @return bool
     */
    protected function checkPaySingle($conf, $data)
    {
        $conf['amount'] = array_filter($conf['amount'] ?: [], function ($item) {
            return $item !== null;
        });
        if (empty($conf['amount'])) {
            return true;
        }
        $payMoney = $data['MONEY'] ?: 0.00;

        $rangeAmount = $conf['amount'];

        if ($payMoney >= $rangeAmount[0] && $payMoney <= $rangeAmount[1]) {
            return true;
        }
        return false;
    }

    /**
     * 总付费金额
     * @param array $conf 配置
     * @param array $data 数据
     * @return bool
     */
    protected function checkPayTotal($conf, $data)
    {
        $conf['pay_amount'] = array_filter($conf['pay_amount'] ?: [], function ($item) {
            return $item !== null;
        });
        if (empty($conf['pay_amount'])) {
            return true;
        }
        $coreAccount      = $data['CORE_ACCOUNT'] ?? '';
        $cpGameId         = $data['CP_GAME_ID'] ?? 0;
        $totalPaidAmount  = $this->getTotalPaidAmount($cpGameId, $coreAccount, $data['PAY_TIME']);
        $totalPaidAmount  = $totalPaidAmount['TOTAL_AMOUNT'] ?? 0;
        $rangeTotalAmount = $conf['pay_amount'];
        $nowPaidTotal     = $totalPaidAmount;
        if ($nowPaidTotal >= $rangeTotalAmount[0] && $nowPaidTotal <= $rangeTotalAmount[1]) {
            return true;
        }
        return false;
    }
}

<?php

namespace app\apps\operator\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Helpers\ConstHub;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\operator\FirstLoginPayRemainLogic;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;

/**
 *
 */
class FirstLoginPayRemainController extends BaseTableController
{

    /**
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        ini_set('memory_limit', '4096M');
        $sort     = [];
        $paginate = [
            'page'      => $params->pull('page'),
            'page_size' => $params->pull('page_size'),
        ];
        $groups   = [];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }

        if ($params->has('groups')) {
            $groups = Arr::wrap($params->pull('groups'));
           
            if (empty($sort)) {
                $sort = [$groups[0] => 'ASC'];
            }
        }

        if (empty($sort)) {
            $sort = ['tday' => 'DESC'];
        }

        $options = $params->toArray();
        $logic   = new FirstLoginPayRemainLogic();

        return $logic->payRetainStretchable($options, $groups, $paginate, $sort);
    }

    protected function fields(Collection $params): array
    {
        $baseCollect = [
            'tday', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id', 'promotion_id',
            'platform_id', 'department_id', 'user_id', 'pay_interval',
        ];

        $newUserCollect = [
            'summary_pay_user',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'pay_remain_index', 'label' => '付费留存基础指标'],
                    // ['value' => 'tags', 'label' => '标签'],

                    ['value' => 'remain_group_1', 'label' => '付费次留-30留'],
                    ['value' => 'remain_group_2', 'label' => '付费45留-180留'],
                    ['value' => 'remain_group_3', 'label' => '付费210留-360留'],
                    ['value' => 'remain_group_4', 'label' => '付费360留-720留'],
                ],
            ],
        ];

        $options = $params->toArray();
        if (empty($options['groups'])) {
            $options['groups'] = ['tday', 'package_id'];
        }

        $fields = $this->tableFields($options);
        $fields = array_column($fields, null, 'dataIndex');

        if ($params->has('groups')) {
            $groups     = $params->get('groups');
            $relateMaps = ColumnManager::groupOperatorRelation($groups);
            $infoCols   = ConstHub::OPERATOR_FIXED_INFO_COLS;
            $fields     = array_filter($fields, fn($item, $k) => !in_array($k, $infoCols) || in_array($k, $relateMaps), ARRAY_FILTER_USE_BOTH);
        }

        $fields = array_values($fields);

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'];

            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $newUserCollect)) {
                $field['classify'] = ['attrs', 'pay_remain_index'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $today = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start', 'default' => $today], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
            ['field' => 'range_date_dimension', 'default' => ConstHub::DIMENSION_DAY], // 时间维度
            ['field' => 'cp_game_id'], // 游戏原名
            ['field' => 'game_id'], // 游戏统计名
            ['field' => 'app_show_id'], // 游戏前端名
            ['field' => 'channel_main_id'], // 主渠道
            ['field' => 'channel_id'], //渠道
            ['field' => 'platform_id'], // 客户端ID
            ['field' => 'package_id'], // 包号
            ['field' => 'promotion_id'], // 推广分类
            ['field' => 'department_id'], // 部门
            ['field' => 'user_id'], // 投放人
            ['field' => 'channel_id_tags'], // 渠道标签
            ['field' => 'channel_main_id_tags'], // 主渠道标签
            ['field' => 'package_id_tags'], // 包号标签
            ['field' => 'remain_type'],
            ['field' => 'remain_days'],
            ['field' => 'paid_amount_days'],
            ['field' => 'paid_amount_range'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);

    }
}
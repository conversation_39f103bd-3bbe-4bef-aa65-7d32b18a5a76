<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Helpers\IndicatorsHelpers;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\internal\Traits\InternalRoutes;
use app\apps\operator\controllers\FirstLoginLtvController;
use app\apps\operator\controllers\FirstLoginRemainController;
use app\apps\operator\controllers\NewLoginLtvController;
use app\apps\operator\controllers\NewLoginRemainController;
use app\extension\Exception\ParameterException;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\service\AdvertiserData\RealtimePlanIndex;
use app\service\ComprehensiveIndex\AdIndexServ;
use app\service\ComprehensiveIndex\OperatorServ;
use app\service\ComprehensiveIndex\RevenueServ;
use app\service\ConfigService\BasicServ;
use app\service\DataSpy\OperationHourly;
use app\service\OperationData\FirstLoginIndex;
use app\service\OperationData\NewLoginIndex;
use app\service\OperationData\NewUserDistribution;
use app\service\OriginData\CostInfoServ;
use app\service\Revenue\IncomeServ;
use app\service\SourceData\PaymentCountServ;
use app\service\SourceData\UserPayStatistics;
use InvalidArgumentException;
use Plus\MVC\Controller\JsonController;
use Plus\MVC\Request\Request;

/**
 * @DashboardController 大盘看板接口
 */
class DashboardController extends JsonController
{
    use InternalRoutes, ColumnsInteract;

    /**
     * 营收报表接口
     *
     * @template TParams
     *                  cp_game_id,range_time,
     *
     * @return array
     * @throws \Exception
     */
    public function revenueAction(): array
    {
        $today  = date('Y-m-d');
        $params = $this->wrapParams(\Plus::$app->request);

        $serv         = new RevenueServ();
        $params       = $params->toArray();
        $incomeInfo   = $serv->fetchIncomeDaily(array_merge($params, ['type' => 2]));
        $rechargeInfo = $serv->fetchIncomeDaily(array_merge($params, ['type' => 1]));
        $costInfo     = $serv->fetchCostInfoDaily($params);

        $rangeDate = [
            $params['range_time_start'] ?? ($params['range_date_start'] ?? $today),
            $params['range_time_end'] ?? ($params['range_date_end'] ?? $today),
        ];

        sort($rangeDate);

        [$rangeStart, $rangeEnd,] = $rangeDate;

        $this->alignDays($incomeInfo, $rangeStart, $rangeEnd);
        $this->alignDays($costInfo, $rangeStart, $rangeEnd);
        $this->alignDays($rechargeInfo, $rangeStart, $rangeEnd);

        $costInfo = array_merge(
            array_fill_keys(array_keys($incomeInfo), ['cost_discount' => 0.00]),
            $costInfo
        );

        ksort($incomeInfo);
        ksort($costInfo);

        // 结余计算
        $balanceInfo = array_map(function ($item1, $item2) {
            $tDay = $item1['tday'] ?? $item2['tday'];

            return [
                'tday'    => $tDay,
                'balance' => ($item1['amount'] ?? 0) - ($item2['cost_discount'] ?? 0),
            ];
        }, $incomeInfo, $costInfo);

        $balanceInfo = array_column($balanceInfo, null, 'tday');
        $list        = $this->crossResult($incomeInfo, $costInfo, $rechargeInfo, $balanceInfo);
        $list        = empty($list) ? [] : array_column($list, null, 'tday');
        $this->alignDays($list, $rangeStart, $rangeEnd);

        // 补零
        foreach ($list as &$item) {
            foreach (
                ['recharge', 'amount', 'cost_discount', 'balance'] as $field
            ) {
                if (!isset($item[$field])) {
                    $item[$field] = 0;
                }
                $item[$field] = round($item[$field]);
            }
        }
        $this->exceptYearForTDay($list);

        // 各模块汇总值
        $totalAmount       = round(array_sum(array_column($incomeInfo, 'amount')));
        $totalCostDiscount = round(array_sum(array_column($costInfo, 'cost_discount')));
        $totalPay          = round(array_sum(array_column($rechargeInfo, 'recharge')));
        $totalBalance      = round(array_sum(array_column($balanceInfo, 'balance')));


        $config = [
            ['prop' => 'tday', 'label' => '时间'],
            [
                'prop'   => 'amount',
                'label'  => '业务收入',
                'series' => [
                    'type' => 'line',
                ],
            ],
            [
                'prop'   => 'cost_discount',
                'label'  => '返点后消耗金额',
                'series' => [
                    'type' => 'line',
                ],
            ],
            [
                'prop'   => 'balance',
                'label'  => '结余',
                'series' => [
                    'type' => 'line',
                ],
            ],
        ];

        return $this->success([
            'config' => $config,
            'list'   => array_values($list),
        ]);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function operatorAction(): array
    {
        $params = $this->wrapParams(\Plus::$app->request);
        $params = $params->toArray();

        $serv = new OperatorServ();

        $dashboardIndex = $serv->fetchDashboardIndex($params);

        $fields = [
            'activate_device', 'firstlogin_user', 'create_role_new',
            'create_role_new', 'firstlogin_active_user', 'firstlogin_pay_user',
            'firstlogin_pay_money', 'firstlogin_pay_money_new', 'old_pay_money',
        ];

        array_walk($dashboardIndex, function (&$item, $key) use ($fields) {

            foreach ($fields as $field) {
                if (!isset($item[$field])) {
                    $item[$field] = 0;
                }

                $item[$field] = round($item[$field]);
            }

            $item['old_pay_money'] = round(($item['firstlogin_pay_money'] ?? 0.00) - ($item['firstlogin_pay_money_new'] ?? 0.00));
        });

        // 汇总行
        $totalActivateDevice = round(array_sum(array_column($dashboardIndex, 'activate_device')));
        $totalFirstUser      = round(array_sum(array_column($dashboardIndex, 'firstlogin_user')));
        $totalCreateRole     = round(array_sum(array_column($dashboardIndex, 'create_role_new')));
        $totalActiveUser     = round(array_sum(array_column($dashboardIndex, 'firstlogin_active_user')));
        $totalPayUser        = round(array_sum(array_column($dashboardIndex, 'firstlogin_pay_user')));
        $totalPayMoney       = round(array_sum(array_column($dashboardIndex, 'firstlogin_pay_money')));
        $totalPayNewMoney    = round(array_sum(array_column($dashboardIndex, 'firstlogin_pay_money_new')));
        $totalOldPayMoney    = round(array_sum(array_column($dashboardIndex, 'old_pay_money')));

        $config = [
            ['label' => '日期', 'value' => 'tday'],
            ['label' => '激活设备', 'value' => 'activate_device', 'num' => $totalActivateDevice],
            ['label' => '首登用户', 'value' => 'firstlogin_user', 'num' => $totalFirstUser],
            ['label' => '创角新用户', 'value' => 'create_role_new', 'num' => $totalCreateRole],
            ['label' => '活跃用户', 'value' => 'firstlogin_active_user', 'num' => $totalActiveUser],
            ['label' => '付费用户', 'value' => 'firstlogin_pay_user', 'num' => $totalPayUser],
            ['label' => '付费金额', 'value' => 'firstlogin_pay_money', 'num' => $totalPayMoney],
            ['label' => '新用户付费金额', 'value' => 'firstlogin_pay_money_new', 'num' => $totalPayNewMoney],
            ['label' => '老用户付费金额', 'value' => 'old_pay_money', 'num' => $totalOldPayMoney],
        ];

        $result = [
            'config' => $config,
            'list'   => array_values($dashboardIndex),
        ];

        return $this->success($result);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function adAction(): array
    {
        $today  = date('Y-m-d');
        $params = $this->wrapParams($this->request);
        $params = $params->toArray();

        $serv = new AdIndexServ();

        $baseIndex    = $serv->listBaseIndex($params);
        $paymentIndex = $serv->listPaymentIndex($params);
        $ltv1Index    = $serv->fetchLtv1($params);

        $rangeDate = [
            $params['range_time_start'] ?? ($params['range_date_start'] ?? $today),
            $params['range_time_end'] ?? ($params['range_date_end'] ?? $today),
        ];

        sort($rangeDate);

        [$rangeStart, $rangeEnd,] = $rangeDate;

        $this->alignDays($baseIndex, $rangeStart, $rangeEnd);
        $this->alignDays($paymentIndex, $rangeStart, $rangeEnd);
        $this->alignDays($ltv1Index, $rangeStart, $rangeEnd);


        // 新用户成本
        array_walk($baseIndex, function (&$foo) {

            $foo['new_user_cost'] = IndicatorsHelpers::division(
                $foo['cost_discount'] ?? 0, $foo['new_user'] ?? 0
            );

            $foo['create_role_percent'] = IndicatorsHelpers::division(
                $foo['create_role_new'] ?? 0, $foo['new_user'] ?? 0, 2, true
            );

            $foo['new_user']      = round($foo['new_user'] ?? 0);
            $foo['cost_discount'] = round($foo['cost_discount'] ?? 0);

        });

        foreach ($paymentIndex as &$foo) {
            $foo['pay_user_new']  = round($foo['pay_user_new'] ?? 0);
            $foo['pay_money_new'] = round($foo['pay_money_new'] ?? 0);
        }

        // ROI1
        $roiInfo = array_map(function ($ltv, $base) {
            if ($base['tday'] != $ltv['tday']) return false;

            $roi = IndicatorsHelpers::division(
                $ltv['money_all'] ?? 0, $base['cost_discount'] ?? 0, 2, true
            );

            return [
                'tday'  => $base['tday'],
                'roi_1' => $roi,
            ];
        }, $ltv1Index, $baseIndex);

        $roiInfo = array_column($roiInfo, null, 'tday');

        // 新用户付费率
        $payUserPercentInfo = array_map(function ($base, $payment) {
            if ($base['tday'] != $payment['tday']) return false;

            $d = IndicatorsHelpers::division(
                $payment['pay_user_new'] ?? 0, $base['new_user'] ?? 0, 2, true
            );

            return [
                'tday'             => $base['tday'],
                'pay_user_percent' => $d,
            ];
        }, $baseIndex, $paymentIndex);

        $payUserPercentInfo = array_column($payUserPercentInfo, null, 'tday');
        // 汇总
        $summaryNewUser      = round(array_sum(array_column($baseIndex, 'new_user')));
        $summaryCostDiscount = round(array_sum(array_column($baseIndex, 'cost_discount')));
        $summaryLtv          = array_sum(array_column($ltv1Index, 'money_all'));
        $summaryPayUser      = round(array_sum(array_column($paymentIndex, 'pay_user_new')));
        $summaryPayMoney     = round(array_sum(array_column($paymentIndex, 'pay_money_new')));
        $summaryCreateRole   = array_sum(array_column($baseIndex, 'create_role_new'));

        $summaryRoi = empty($summaryLtv) || empty($summaryCostDiscount)
            ? '0.00%'
            : number_format($summaryLtv / $summaryCostDiscount * 100, 2) . '%';

        $summaryCreateRolePercent = empty($summaryCreateRole) || empty($summaryNewUser)
            ? '0.00%'
            : number_format($summaryCreateRole / $summaryNewUser * 100, 2) . '%';

        $summaryPayUserPercent = empty($summaryPayUser) || empty($summaryNewUser)
            ? '0.00%'
            : number_format($summaryPayUser / $summaryNewUser * 100, 2) . '%';

        $summaryNewUserCost = empty($summaryCostDiscount) || empty($summaryNewUser)
            ? '0.00'
            : number_format($summaryCostDiscount / $summaryNewUser, 2);

        $config = [
            ['label' => '日期', 'value' => 'tday'],
            ['label' => '广告新增用户', 'value' => 'new_user', 'num' => $summaryNewUser],
            ['label' => '返点后消耗金额', 'value' => 'cost_discount', 'num' => $summaryCostDiscount],
            ['label' => '新用户成本', 'value' => 'new_user_cost', 'num' => $summaryNewUserCost],
            ['label' => 'ROI1', 'value' => 'roi_1', 'num' => $summaryRoi],
            ['label' => '付费新用户', 'value' => 'pay_user_new', 'num' => $summaryPayUser],
            ['label' => '新用户付费率', 'value' => 'pay_user_percent', 'num' => $summaryPayUserPercent],
            ['label' => '新用户付费金额', 'value' => 'pay_money_new', 'num' => $summaryPayMoney],
            ['label' => '新用户创角率', 'value' => 'create_role_percent', 'num' => $summaryCreateRolePercent],
        ];

        array_walk($baseIndex, function (&$item) {
            unset($item['create_role_new']);
        });

        $list = $this->crossResult($baseIndex, $paymentIndex, $payUserPercentInfo, $roiInfo);

        $result = [
            'config' => $config,
            'list'   => $list,
        ];

        return $this->success($result);
    }

    /**
     * 大盘LTV看板
     * 查询条件:
     *  range_date_dimension -- 时间维度
     *  range_date_start -- 搜索开始时间
     *  range_date_end -- 搜索结束时间
     *  cp_game_id -- 游戏原名
     *  report_dimension -- 报表维度 (1-广告新增, 2-新增, 3-运营首登, 4-运营新增)
     *
     *
     * @route /internal/operator/dashboard/ltv-list
     *
     * @return array
     * @throws \Exception
     */
    public function ltvListAction(): array
    {
        $request         = \Plus::$app->request;
        $params          = $this->wrapParams($request);
        $reportDimension = (int)$params->pull('report_dimension', 1);

        if (2 === $reportDimension) {
            // 运营首登
            $control = new FirstLoginLtvController();
        }
        else {
            // 广告新增(使用广告计划维度)
            $control = new AdPlanLtvController();
        }

        (function () {
            $this->params['column_scope'] = ['ltv'];
            $this->params['groups']       = ['tday'];
            $this->params['search_mode']  = ['simple'];
            $this->params['max_day_type'] = 7;
        })->call($request);

        $control->setRequest($request);

        $re = $control->simpleSearchAction();

        if (isset($re['data']['list'])) {
            $this->exceptYearForTDay($re['data']['list']);
        }

        $fields = &$re['data']['fields'];
        foreach ($fields as &$foo) {
            if ($foo['title'] === '广告新增用户' || $foo['title'] === '新增用户') {
                $foo['title'] = '新用户';
            }
        }

        return $re;
    }

    /**
     * 大盘ROI看板
     * 查询条件:
     *  range_date_dimension -- 时间维度
     *  range_date_start -- 搜索开始时间
     *  range_date_end -- 搜索结束时间
     *  cp_game_id -- 游戏原名
     *  report_dimension -- 报表维度 (1-广告新增, 2-新增, 3-运营首登, 4-运营新增)
     *
     * @route /internal/operator/dashboard/roi-list
     * @return array
     * @throws \Exception
     */
    public function roiListAction(): array
    {
        $request         = \Plus::$app->request;
        $params          = $this->wrapParams($request);
        $reportDimension = (int)$params->pull('report_dimension', 1);

        if (2 === $reportDimension) {
            // 运营首登
            $control = new FirstLoginLtvController();
        }
        else {
            // 广告新增(使用广告计划维度)
            $control = new AdPlanLtvController();
        }

        (function () {
            $this->params['column_scope'] = ['roi'];
            $this->params['groups']       = ['tday'];
            $this->params['search_mode']  = ['simple'];
            $this->params['max_day_type'] = 7;
        })->call($request);

        $control->setRequest($request);
        $re = $control->simpleSearchAction();

        if (isset($re['data']['list'])) {
            $this->exceptYearForTDay($re['data']['list']);
        }

        $fields = &$re['data']['fields'];
        foreach ($fields as &$foo) {
            if ($foo['title'] === '广告新增用户' || $foo['title'] === '新增用户') {
                $foo['title'] = '新用户';
            }
        }

        return $re;
    }

    /**
     *
     *
     * @route {{spy_host}}/internal/dashboard/remain-list
     * @return array
     * @throws \Exception
     */
    public function remainListAction(): array
    {
        $request         = \Plus::$app->request;
        $params          = $this->wrapParams($request);
        $reportDimension = (int)$params->pull('report_dimension', 1);

        if (2 === $reportDimension) {
            // 运营首登
            $control = new FirstLoginRemainController();
        }
        else {
            // 广告新增(使用广告计划维度)
            $control = new AdPlanRemainController();
        }

        (function () {
            $this->params['groups'] = ['tday'];
        })->call($request);

        $control->setRequest($request);

        $result = $control->simpleSearchAction();

        $dataBody = &$result['data'];

        if (isset($result['data']['list'])) {
            $this->exceptYearForTDay($result['data']['list']);
        }

        if (!empty($dataBody['fields'])) {
            $fields = &$dataBody['fields'];

            foreach ($fields as &$field) {
                $dataIndex = $field['dataIndex'];

                if (
                    strstr($dataIndex, 'remain') === false
                    && !in_array($dataIndex, ['tday', 'new_user', 'firstlogin_user'])
                ) {
                    $field = null;
                }

                if ($field['title'] === '广告新增用户' || $field['title'] === '新增用户') {
                    $field['title'] = '新用户';
                }
            }

            $fields = array_values(array_filter($fields));
        }

        return $result;
    }


    /**
     * 新用户来源分布模块
     *
     * @route /internal/operator/dashboard/user-distribution
     * @return array
     * @throws \Exception
     */
    public function userDistributionAction(): array
    {
        $today           = date('Y-m-d');
        $request         = \Plus::$app->request;
        $params          = $this->wrapParams($request);
        $reportDimension = (int)$params->pull('report_dimension', 1);
        $userKey         = 'new_user';

        $rangeDate = [$params->get('range_date_start', $today), $params->get('range_date_end', $today)];
        sort($rangeDate);

        $options = [
            'range_date_start'     => $rangeDate[0],
            'range_date_end'       => $rangeDate[1],
            'range_date_dimension' => $params->get('range_date_dimension', ConstHub::DIMENSION_DAY),
            'is_has_natural'       => 1,
        ];

        if ($params->has('cp_game_id')) {
            $options['cp_game_id'] = $params->get('cp_game_id');
        }

        $groups = ['tday', 'channel_main_id'];

        if (2 === $reportDimension) {
            // 运营首登
            $result = (new FirstLoginIndex())->simpleListByDay($options, [], $groups);
        }
        else {
            // 广告新增(使用广告计划维度)
            $result = (new RealtimePlanIndex())->simpleListByDay($options, [], $groups);
        }

        $constConfCollect = (new BasicServ())->getMultiOptions(['channel_main_id']);

        $channelMap = (function ($data) {
            $result = [];
            foreach ($data as $item) {
                $result[$item['key']] = $item['val'];
            }
            return $result;
        })($constConfCollect->get('channel_main_id'));


        $list = &$result['list'];

        $summaryInfoByChannel = (function ($data) use ($userKey) {
            $result = [];
            foreach ($data as $item) {
                $channelMain = $item['channel_main_id'] ?? 0;
                isset($result[$channelMain])
                    ? $result[$channelMain] += $item[$userKey]
                    : $result[$channelMain] = $item[$userKey];
            }
            return $result;
        })($list);

        // 根据汇总参数获取Top5渠道
        arsort($summaryInfoByChannel);
        $channelOnSort = array_keys($summaryInfoByChannel);
        $top5Channel   = array_splice($channelOnSort, 0, 5);

        $data = [];

        foreach ($list as $item) {
            $tDay = $item['tday'];

            isset($data[$tDay]) ?: $data[$tDay] = ['tday' => $tDay];

            $chill   = &$data[$tDay];
            $channel = $item['channel_main_id'] ?? 0;

            in_array($channel, $top5Channel)
                ? $dataKey = 'channel_' . $channel
                : $dataKey = 'channel_other';

            isset($chill[$dataKey])
                ? $chill[$dataKey] += $item[$userKey]
                : $chill[$dataKey] = $item[$userKey];
        }

        $config = [
            ['label' => '时间', 'prop' => 'tday'],
        ];

        foreach (array_merge($top5Channel, ['other']) as $channel) {
            $dataKey = 'channel_' . $channel;

            $config[] = [
                'label'  => $channelMap[$channel] ?? '其他',
                'prop'   => $dataKey,
                'series' => [
                    'stack'          => 'x1',
                    'showBackground' => true,
                ],
            ];
        }

        $this->exceptYearForTDay($data);

        $result = [
            'list'   => array_values($data),
            'config' => $config,
        ];

        return $this->success($result);
    }

    /**
     * 新用户成本
     *
     * @route /internal/operator/dashboard/new-user-cost
     * @return array
     * @throws \Exception
     */
    public function newUserCostAction(): array
    {
        $today           = date('Y-m-d');
        $params          = $this->wrapParams(\Plus::$app->request);
        $reportDimension = (int)$params->pull('report_dimension', 1);
        $userKey         = 'new_user';
        $costKey         = 'cost_discount';

        $rangeDate = [$params->get('range_date_start', $today), $params->get('range_date_end', $today)];
        sort($rangeDate);

        $options = [
            'range_date_start'     => $rangeDate[0],
            'range_date_end'       => $rangeDate[1],
            'range_date_dimension' => $params->get('range_date_dimension', ConstHub::DIMENSION_DAY),
        ];

        if ($params->has('cp_game_id')) {
            $options['cp_game_id'] = $params->get('cp_game_id');
        }

        $groups = ['tday', 'channel_main_id'];

        if (2 === $reportDimension) {
            // 运营首登
            $result = (new FirstLoginIndex())->simpleListByDay($options, [], $groups);
        }
        else {
            // 广告新增(使用广告计划维度)
            $result = (new RealtimePlanIndex())->simpleListByDay($options, [], $groups);
        }

        $constConfCollect = (new BasicServ())->getMultiOptions(['channel_main_id']);

        $channelMap = (function ($data) {
            $result = [];
            foreach ($data as $item) {
                $result[$item['key']] = $item['val'];
            }
            return $result;
        })($constConfCollect->get('channel_main_id'));

        $list = &$result['list'];

        $summaryInfoByChannel = (function ($data) use ($costKey) {
            $result = [];
            foreach ($data as $item) {
                $channelMain = $item['channel_main_id'] ?? 0;
                isset($result[$channelMain])
                    ? $result[$channelMain] += $item[$costKey]
                    : $result[$channelMain] = $item[$costKey];
            }
            return $result;
        })($list);

        // 根据汇总参数获取Top5渠道
        arsort($summaryInfoByChannel);
        $channelOnSort = array_keys($summaryInfoByChannel);
        $top5Channel   = array_splice($channelOnSort, 0, 5);
        $result        = [];
        $userRe        = [];

        foreach ($list as $item) {
            $tDay    = $item['tday'];
            $channel = $item['channel_main_id'] ?? 0;

            if (empty($channel)) continue;

            isset($result[$tDay]) ?: $result[$tDay] = ['tday' => $tDay];
            isset($userRe[$tDay]) ?: $userRe[$tDay] = ['tday' => $tDay];

            $chill  = &$result[$tDay];
            $uChill = &$userRe[$tDay];

            in_array($channel, $top5Channel)
                ? $key = 'cost_' . $channel
                : $key = 'cost_other';

            isset($chill[$key])
                ? $chill[$key] += $item[$costKey]
                : $chill[$key] = $item[$costKey];

            isset($uChill[$key])
                ? $uChill[$key] += $item[$userKey]
                : $uChill[$key] = $item[$userKey];
        }

        foreach ($result as &$item) {
            $tDay = $item['tday'];
            preg_match_all('/cost_(\d+)*[other]*/', implode(',', array_keys($item)), $costKeys);
            $costKeys = $costKeys[0];

            foreach ($costKeys as $key) {
                $k  = str_replace('cost_', '', $key);
                $nk = 'channel_' . $k;

                if (empty($item[$key]) || empty($userRe[$tDay][$key])) {
                    $item[$nk] = 0.00;
                    continue;
                }

                $item[$nk] = (float)number_format(math_eval('x/y', ['x' => $item[$key], 'y' => $userRe[$tDay][$key]]), 2);
                unset($item[$key]);
            }
        }

        $config = [
            ['label' => '时间', 'prop' => 'tday'],
        ];

        foreach (array_merge($top5Channel, ['other']) as $channel) {
            $dataKey = 'channel_' . $channel;

            $config[] = [
                'label'  => $channelMap[$channel] ?? '其他',
                'prop'   => $dataKey,
                'series' => [
                    'type' => 'line',
                ],
            ];
        }

        $this->exceptYearForTDay($result);

        return $this->success([
            'list'   => array_values($result),
            'config' => $config,
        ]);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function newUserConvertAction(): array
    {
        $today           = date('Y-m-d');
        $params          = $this->wrapParams(\Plus::$app->request);
        $reportDimension = (int)$params->pull('report_dimension', 1);
        $dashboardIndex  = [
            'click'           => '步骤一: 点击',
            'activate_device' => '步骤二: 激活',
            'new_user'        => '步骤三: 新增',
            'create_role'     => '步骤四: 创角',
            'pay_user'        => '步骤五: 付费',
        ];
        $activeKey       = 'activate_device';
        $rangeDate       = [$params->get('range_date_start', $today), $params->get('range_date_end', $today)];
        sort($rangeDate);

        $options = [
            'range_date_start'     => $rangeDate[0],
            'range_date_end'       => $rangeDate[1],
            'range_date_dimension' => $params->get('range_date_dimension', ConstHub::DIMENSION_DAY),
        ];

        if ($params->has('cp_game_id')) {
            $options['cp_game_id'] = $params->get('cp_game_id');
        }

        $groups = ['channel_main_id'];

        if (2 === $reportDimension) {
            // 运营首登
            $result        = (new FirstLoginIndex())->simpleListByDay($options, [], $groups);
            $paymentResult = (new FirstLoginIndex())->simplePayListByDay($options, [], $groups);
            $mediaRe       = array_column(
                (new CostInfoServ())->fetchSimpleInfoInCost($options, $groups), null, 'channel_main_id'
            );

            foreach ($result['list'] as &$foo) {
                $channelMain  = $foo['channel_main_id'] ?? '';
                $foo['click'] = $mediaRe[$channelMain]['click'] ?? 0;
            }
        }
        else {
            // 广告新增(使用广告计划维度)
            $result        = (new RealtimePlanIndex())->simpleListByDay($options, [], $groups);
            $paymentResult = (new RealtimePlanIndex())->simplePayListByDay($options, [], $groups);
        }

        $constConfCollect = (new BasicServ())->getMultiOptions(['channel_main_id']);

        $channelMap = (function ($data) {
            $result = [];
            foreach ($data as $item) {
                $result[$item['key']] = $item['val'];
            }
            return $result;
        })($constConfCollect->get('channel_main_id'));

        $list = &$result['list'];

        // 合并付费数据
        $payInfo = array_column($paymentResult['list'], null, 'channel_main_id');

        foreach ($list as &$item) {
            $channelMain      = $item['channel_main_id'];
            $item['pay_user'] = $payInfo[$channelMain]['pay_user'] ?? 0.00;
        }

        $summaryInfoByChannel = (function ($data) use ($activeKey) {
            $r = [];

            foreach ($data as $item) {
                $channelMain = $item['channel_main_id'] ?? 0;
                isset($r[$channelMain])
                    ? $r[$channelMain] += $item[$activeKey]
                    : $r[$channelMain] = $item[$activeKey];
            }
            return $r;
        })($list);

        // 根据汇总参数获取Top5渠道
        arsort($summaryInfoByChannel);
        $channelOnSort = array_keys($summaryInfoByChannel);
        $top5Channel   = array_splice($channelOnSort, 0, 5);

        $data = [];

        foreach ($list as $item) {
            $channel = $item['channel_main_id'] ?? 'other';

            in_array($channel, $top5Channel)
                ? $dataKey = 'channel_' . $channel
                : $dataKey = 'channel_other';

            foreach ($dashboardIndex as $event => $prop) {
                isset($data[$event]) ?: $data[$event] = ['summary' => 0, 'event' => $prop];

                $chill = &$data[$event];

                isset($chill[$dataKey])
                    ? $chill[$dataKey] += ($item[$event] ?? 0.00)
                    : $chill[$dataKey] = ($item[$event] ?? 0.00);

                $chill['summary'] += ($item[$event] ?? 0.00);
            }
        }

        $config = [
            ['label' => '事件', 'prop' => 'event'],
        ];

        foreach (array_merge($top5Channel, ['other']) as $channel) {
            $dataKey = 'channel_' . $channel;

            $config[] = [
                'prop'   => $dataKey,
                'label'  => $channelMap[$channel] ?? '其他',
                'series' => [],
            ];
        }

        return $this->success([
            'list'   => array_values($data),
            'config' => $config,
        ]);
    }

    /**
     * 活跃用户模块
     *
     * @route /internal/dashboard/active-user
     * @return array
     * @throws \Exception
     */
    public function activeUserAction(): array
    {
        $today           = date('Y-m-d');
        $params          = $this->wrapParams(\Plus::$app->request);
        $reportDimension = (int)$params->pull('report_dimension', 1);
        $activeUserKey   = 'active_user';
        $newUserKey      = 'new_user';
        $rangeDate       = [$params->get('range_date_start', $today), $params->get('range_date_end', $today)];
        sort($rangeDate);

        $options = [
            'range_date_start'     => $rangeDate[0],
            'range_date_end'       => $rangeDate[1],
            'range_date_dimension' => $params->get('range_date_dimension', ConstHub::DIMENSION_DAY),
        ];

        if ($params->has('cp_game_id')) {
            $options['cp_game_id'] = $params->get('cp_game_id');
        }

        $groups = ['tday'];

        if (2 === $reportDimension) {
            // 运营首登
            $result = (new FirstLoginIndex())->simpleListByDay($options, [], $groups);
        }
        else {
            // 广告新增(使用广告计划维度)
            $result = (new RealtimePlanIndex())->simpleActiveByDay($options, [], $groups);
        }

        $list = &$result['list'];

        $data = [];

        foreach ($list as $foo) {
            $tDay = $foo['tday'];

            isset($data[$tDay]) ?: $data[$tDay] = ['tday' => $tDay];
            $chill = &$data[$tDay];

            isset($chill['new_user'])
                ? $chill['new_user'] += $foo[$newUserKey]
                : $chill['new_user'] = $foo[$newUserKey];

            isset($chill['old_user'])
                ? $chill['old_user'] += ($foo[$activeUserKey] - $foo[$newUserKey])
                : $chill['old_user'] = ($foo[$activeUserKey] - $foo[$newUserKey]);
        }

        $config = [
            ['prop' => 'tday', 'label' => '时间'],
            [
                'prop'   => 'old_user',
                'label'  => '老用户',
                'series' => [
                    'type'  => 'bar',
                    'stack' => 'x1',
                ],
            ],
            [
                'prop'   => 'new_user',
                'label'  => '新用户',
                'series' => [
                    'type'  => 'bar',
                    'stack' => 'x1',
                ],
            ],
        ];

        $this->exceptYearForTDay($data);

        return $this->success([
            'list'   => array_values($data),
            'config' => $config,
        ]);
    }

    /**
     * 活跃来源时间分布 复刻spy1.0的逻辑
     *
     * @route {{spy_host}}/internal/dashboard/active-user-distribute
     * @return array
     * @throws \Exception
     */
    public function activeUserDistributeAction(): array
    {
        $today         = date('Y-m-d');
        $params        = $this->wrapParams(\Plus::$app->request);
        $timeDimension = (int)$params->get('range_date_dimension', ConstHub::DIMENSION_DAY);

        $calcFunc = function (&$data, $maxDayType = 5) {
            foreach ($data as &$foo) {
                preg_match_all('/day_type\d+/', implode(',', array_keys($foo)), $dayTypeKeys);
                $dayTypeKeys = $dayTypeKeys[0];
                $eventNum    = (int)($foo['event_sum'] ?? 0.00);

                foreach ($dayTypeKeys as $dayTypeKey) {
                    $i = (int)str_replace('day_type', '', $dayTypeKey);

                    if ($i >= $maxDayType) {
                        if ($i != 1000) {
                            isset($foo['day_type1000'])
                                ? $foo['day_type1000'] += ($foo[$dayTypeKey] ?? 0)
                                : $foo['day_type1000'] = ($foo[$dayTypeKey] ?? 0);

                            continue;
                        }
                        else {
                            break;
                        }
                    };

                    if (empty($foo[$dayTypeKey]) || empty($eventNum)) {
                        $foo[$dayTypeKey] = '0.00%';
                    }
                    else {
                        $foo[$dayTypeKey] = number_format(
                                math_eval('x/y * 100', ['x' => (int)$foo[$dayTypeKey], 'y' => $eventNum]), 2
                            ) . '%';
                    }
                }

                if (empty($foo['day_type1000']) || empty($eventNum)) {
                    $foo['day_type1000'] = '0.00%';
                }
                else {
                    $foo['day_type1000'] = number_format(
                            math_eval('x/y * 100', ['x' => (int)$foo['day_type1000'], 'y' => $eventNum]), 2
                        ) . '%';
                }

            }
        };

        $dateFunc = function (&$data, $timeDimension) {
            if (ConstHub::DIMENSION_WEEK === $timeDimension) {
                $f = function ($foo) {
                    $dayT  = $foo['tday'];
                    $start = strtotime(substr($dayT, 0, -2) . 'W' . substr($dayT, -2));
                    $end   = strtotime('+1 week -1 day', $start);

                    return date('Y/m/d', ($start - 86400 * 3)) . '-' . date("Y/m/d", ($end - 86400 * 3));
                };
            }
            elseif (ConstHub::DIMENSION_MONTH === $timeDimension) {
                $f = function ($foo) {
                    return date("Y/m", strtotime($foo['tday'] . "01"));
                };
            }
            else {
                $f = function ($foo) {
                    return date("Y/m/d", strtotime($foo['tday']));
                };
            }

            foreach ($data as &$item) {
                $item['tday'] = $f($item);
            }
        };


        $dataFn = function () use ($params, $calcFunc, $dateFunc) {
            $today   = date('Y-m-d');
            $serv    = new NewUserDistribution();
            $options = $params->toArray();

            $timeDimension         = (int)$params->get('range_date_dimension', ConstHub::DIMENSION_DAY);
            $options['event_type'] = 1;

            if (ConstHub::DIMENSION_MONTH === $timeDimension) {
                $options['max_day_type'] = 24;
                $options['report_type']  = 3;
            }
            elseif (ConstHub::DIMENSION_WEEK === $timeDimension) {
                $options['max_day_type'] = 60;
                $options['report_type']  = 2;
            }
            else {
                $options['max_day_type'] = 60;
            }

            [
                $options['range_date_start'], $options['range_date_end'],
            ] = (function ($left, $right) {
                if ($left > $right) {
                    return [$right, $left];
                }
                else {
                    return [$left, $right];
                }
            })($options['range_date_start'] ?? $today, $options['range_date_end'] ?? $today);

            $data       = $serv->fetchSpecInfo($options, ['tday']);
            $summaryRow = $serv->fetchSpecInfo($options);

            $calcFunc($data);
            $dateFunc($data, $timeDimension);
            $calcFunc($summaryRow);


            return [
                'summary' => $summaryRow[0] ?? [],
                'list'    => $data,
            ];
        };

        $fieldFn = function () use ($timeDimension) {
            $fields = [
                ['title' => '时间', 'dataIndex' => 'tday'],
                ['title' => '活跃用户', 'dataIndex' => 'event_sum'],
            ];

            if ($timeDimension === ConstHub::DIMENSION_WEEK) {
                $weekField = [
                    ['title' => '当周', 'dataIndex' => 'day_type0'],
                    ['title' => '1周前', 'dataIndex' => 'day_type1'],
                    ['title' => '2周前', 'dataIndex' => 'day_type2'],
                    ['title' => '3周前', 'dataIndex' => 'day_type3'],
                    ['title' => '4周前', 'dataIndex' => 'day_type4'],
                    ['title' => '>5周', 'dataIndex' => 'day_type1000'],
                ];
                $fields    = array_merge($fields, $weekField);
            }
            elseif ($timeDimension === ConstHub::DIMENSION_MONTH) {
                $monthField = [
                    ['title' => '当月', 'dataIndex' => 'day_type0'],
                    ['title' => '1月前', 'dataIndex' => 'day_type1'],
                    ['title' => '2月前', 'dataIndex' => 'day_type2'],
                    ['title' => '3月前', 'dataIndex' => 'day_type3'],
                    ['title' => '4月前', 'dataIndex' => 'day_type4'],
                    ['title' => '>5月', 'dataIndex' => 'day_type1000'],
                ];
                $fields     = array_merge($fields, $monthField);
            }
            else {
                $monthField = [
                    ['title' => '当月', 'dataIndex' => 'day_type0'],
                    ['title' => '1月前', 'dataIndex' => 'day_type1'],
                    ['title' => '2月前', 'dataIndex' => 'day_type2'],
                    ['title' => '3月前', 'dataIndex' => 'day_type3'],
                    ['title' => '4月前', 'dataIndex' => 'day_type4'],
                    ['title' => '>5月', 'dataIndex' => 'day_type1000'],
                ];
                $fields     = array_merge($fields, $monthField);
            }

            return ['fields' => $fields];
        };

        $methodAction = $params->get('method', ['data', 'fields']);
        $resultRe     = [];

        foreach ($methodAction as $ac) {
            if ($ac == 'data') {
                $resultRe = array_merge($resultRe, $dataFn());
            }
            elseif ($ac = 'fields') {
                $resultRe = array_merge($resultRe, $fieldFn());
            }
        }
        if (isset($resultRe['list'])) {
            $this->exceptYearForTDay($resultRe['list']);
        }
        return $this->success($resultRe);
    }

    /**
     * 创角数模块
     * @route {{spy_host}}/internal/dashboard/create-roles
     *
     * @return array
     * @throws \Exception
     */
    public function createRolesAction(): array
    {
        $params = $this->wrapParams(\Plus::$app->request);

        if (!$params->has('range_date_start')) {
            $params->put('range_date_start', date('Y-m-d'));
        }

        if (!$params->has('range_date_end')) {
            $params->put('range_date_end', date('Y-m-d'));
        }

        $serv   = new FirstLoginIndex();
        $result = $serv->listBase($params->toArray(), [], ['tday'], 'tday asc');
        $list   = &$result['list'];

        foreach ($list as &$item) {
            $item['server_roll']         = math_eval(
                'x-y', ['x' => $item['create_role'], 'y' => $item['firstlogin_role2']]
            );
            $item['server_roll_percent'] = (float)number_format(
                math_eval('x/y*100', ['x' => $item['server_roll'], 'y' => $item['create_role']]), 2
            );

            $item = array_intersect_key(
                $item, array_flip(['tday', 'create_role_new', 'create_role', 'server_roll', 'server_roll_percent', 'firstlogin_role2'])
            );
        }

        $config = [
            ['prop' => 'tday', 'label' => '时间'],
            [
                'prop'   => 'firstlogin_role2',
                'label'  => '新创角',
                'series' => [
                    'type'  => 'bar',
                    'stack' => 'x1',
                ],
            ],
            [
                'prop'   => 'server_roll',
                'label'  => '滚服数',
                'series' => [
                    'type'  => 'bar',
                    'stack' => 'x1',
                ],
            ],
            [
                'prop'   => 'server_roll_percent',
                'label'  => '滚服率',
                'series' => [
                    'type' => 'line',
                ],
            ],
        ];
        $this->exceptYearForTDay($list);
        return $this->success([
            'config' => $config,
            'list'   => $list,
        ]);
    }

    /**
     * 今日关注
     * @route {{spy_host}}/internal/dashboard/focus-today
     *
     * @return array
     */
    public function focusTodayAction(): array
    {
        $today   = date('Y-m-d');
        $nowHour = date('H');
        $params  = $this->wrapParams(\Plus::$app->request);

        $tags = Arr::wrap($params->get('tags', ['pay_money', 'cost_discount', 'active_user']));
        if (!$params->has('range_date_start')) {
            $params->put('range_date_start', (new \DateTime('-1day'))->format('Y-m-d'));
        }

        if (!$params->has('range_date_end')) {
            $params->put('range_date_end', date('Y-m-d'));
        }

        $options = $params->toArray();

        if (in_array('pay_money', $tags)) {
            $options = array_merge($options, ['pay_result' => 1]);
            $result  = (new UserPayStatistics())->getPayInfoByHourly($options, ['tday', 'thour']);
        }
        elseif (in_array('cost_discount', $tags)) {
            $result = (new OperationHourly())->getExtInfoByHourly($options, ['tday', 'thour']);
        }
        else {
            $result = (new OperationHourly())->getInfoByHourly($options, ['tday', 'thour']);
        }

        $data        = [];
        $dataByDate  = [];
        $list        = &$result['list'];
        $summaryInfo = [];

        foreach ($list as $item) {
            $tDay  = $item['tday'];
            $tHour = $item['thour'];

            isset($dataByDate[$tDay]) ?: $dataByDate[$tDay] = ['day' => $tDay];
            $chill = &$dataByDate[$tDay];

            if ($tDay === $today && $tHour > $nowHour) continue;

            isset($chill[$tHour]) ?: $chill[$tHour] = ['hour' => $tHour];
            $foo = &$chill[$tHour];

            foreach ($tags as $tag) {
                isset($foo[$tag])
                    ? $foo[$tag] += $item[$tag]
                    : $foo[$tag] = $item[$tag];

                if ($tag == 'cost_discount') {
                    $foo[$tag] = round($foo[$tag]);
                }
                elseif ($tag == 'pay_money') {
                    $foo[$tag] = round($foo[$tag], 2);
                }

            }
        }

        $todayNowHour = 0;
        $today        = date('Y-m-d');
        $yesterday    = date('Y-m-d', strtotime('-1day'));

        foreach ($dataByDate as $day => &$foo) {

            foreach ($foo as $hour => &$chill) {
                if ($day == $today) $todayNowHour = $hour;

                if (is_string($hour)) continue;
                if ($day == $today && $hour > $nowHour) continue;

                foreach ($chill as $k => &$item) {
                    if ($k == 'hour') continue;
                    if ($k == 'cost_discount' || $k == 'cost') continue;
                    $item += ($foo[$hour - 1][$k] ?? 0);

                    if ($k == 'pay_money') {
                        $item = round($item, 2);
                    }
                }
            }

        }

        unset($foo, $day, $hour, $item, $chill);

        foreach ($dataByDate as $day => $item) {
            foreach ($item as $hour => $chill) {
                if (!is_numeric($hour)) continue;
                isset($data[$hour]) ?: $data[$hour] = ['thour' => $hour . ':00'];

                $foo = &$data[$hour];

                foreach ($chill as $ki => $fi) {
                    if ($ki == 'hour') continue;

                    $key = $ki . '_' . $day;

                    isset($foo[$key])
                        ? $foo[$key] += $fi
                        : $foo[$key] = $fi;
                }
            }
        }

        $samePeriod       = $data[$todayNowHour] ?? [];
        $summaryPayMoney  = 0.00;
        $contrastPayMoney = '0.00%';
        $summaryCost      = 0.00;
        $contrastCost     = '0.00%';
        $summaryActive    = 0.00;
        $contrastActive   = '0.00%';

        foreach ($tags as $tag) {
            $todayD     = $samePeriod[$tag . '_' . $today] ?? 0;
            $yesterdayD = $samePeriod[$tag . '_' . $yesterday] ?? 0;
            $rechargeD  = $todayD - $yesterdayD;

            if ($tag == 'pay_money') {
                $summaryPayMoney = $todayD;

                if (empty($rechargeD) || empty($yesterdayD)) {
                    $contrastPayMoney = '0.00%';
                }
                else {
//                    $contrastPayMoney = number_format(math_eval(
//                            'x/y * 100', ['x' => ($todayD - $yesterdayD), 'y' => $yesterdayD]
//                        ), 2) . '%';

                    $contrastPayMoney = round(($todayD - $yesterdayD) / $yesterdayD * 100, 2) . '%';
                }
            }

            if ($tag == 'cost_discount') {
                $summaryCost = $todayD;

                if (empty($rechargeD) || empty($yesterdayD)) {
                    $contrastCost = '0.00%';
                }
                else {
//                    $contrastCost = number_format(math_eval(
//                            'x/y * 100', ['x' => ($todayD - $yesterdayD), 'y' => $yesterdayD]
//                        ), 2) . '%';

                    $contrastCost = round(($todayD - $yesterdayD) / $yesterdayD * 100, 2) . '%';
                }
            }

            if ($tag == 'active_user') {
                $summaryActive = $todayD;

                if (empty($rechargeD) || empty($yesterdayD)) {
                    $contrastActive = '0.00%';
                }
                else {
//                    $contrastActive = number_format(math_eval(
//                            'x/y * 100', ['x' => ($todayD - $yesterdayD), 'y' => $yesterdayD]
//                        ), 2) . '%';
                    $contrastActive = round(($todayD - $yesterdayD) / $yesterdayD * 100, 2) . '%';
                }
            }
        }

        $config = [
            ['prop' => 'thour', 'label' => '小时'],
        ];

        if (in_array('pay_money', $tags)) {
            $config[] = ['label' => '今日付费金额', 'num' => $summaryPayMoney, 'total_prop' => 'pay_money'];
            $config[] = ['label' => '较昨日', 'num' => $contrastPayMoney, 'comparison_prop' => 'comparison_yesterday'];
        }

        if (in_array('cost_discount', $tags)) {
            $config[] = ['label' => '今日消耗金额', 'num' => $summaryCost, 'total_prop' => 'cost_discount'];
            $config[] = ['label' => '较昨日', 'num' => $contrastCost, 'comparison_prop' => 'comparison_yesterday'];
        }

        if (in_array('active_user', $tags)) {
            $config[] = ['label' => '今日活跃用户', 'num' => $summaryActive, 'total_prop' => 'active_user'];
            $config[] = ['label' => '较昨日', 'num' => $contrastActive, 'comparison_prop' => 'comparison_yesterday'];
        }

        foreach ($tags as $tag) {
            $config[] = [
                'prop'   => $tag . '_' . $today,
                'label'  => '今天',
                'series' => [
                    'type' => 'line',
                ],
            ];

            $config[] = [
                'prop'   => $tag . '_' . $yesterday,
                'label'  => '昨天',
                'series' => [
                    'type' => 'line',
                ],
            ];
        }

        return $this->success([
            'config' => $config,
            'data'   => $data,
        ]);

    }


    /**
     * 合并各组结果数据
     *
     * @param ...$arrays
     *
     * @return array
     */
    private function crossResult(...$arrays): array
    {
        $crossKeys = [];
        foreach ($arrays as $array) {
            $crossKeys = array_merge($crossKeys, array_keys($array));
        }

        $crossKeys = array_unique($crossKeys);
        sort($crossKeys);
        $crossIndex = array_fill_keys($crossKeys, null);

        foreach ($arrays as &$array) {
            $array = array_merge($crossIndex, $array);
        }

        return array_map(function (...$items) {
            return array_merge(...$items);
        }, ...$arrays);
    }

    /**
     * @param array            $data
     * @param string|\DateTime $rangeDateStart
     * @param string|\DateTime $rangeDateEnd
     *
     * @return void
     * @throws \Exception
     */
    private function alignDays(array &$data, $rangeDateStart, $rangeDateEnd)
    {
        foreach ([&$rangeDateStart, &$rangeDateEnd] as &$date) {
            if (!$date instanceof \DateTime) {
                $date = new \DateTime($date);
            }
        }
        unset($date);

        $step = new \DateInterval('P1D');
        $n    = $rangeDateEnd->diff($rangeDateStart)->format('%a');

        $oneDate = clone $rangeDateStart;
        for ($i = 0; $i <= $n; $i++) {
            $d = $oneDate->format('Y-m-d');

            if (!isset($data[$d])) {
                $data[$d] = ['tday' => $d];
            }

            $oneDate = $oneDate->add($step);
        }

        ksort($data);
    }

    /**
     * @param array $list
     *
     * @return void
     */
    private function exceptYearForTDay(array &$list)
    {
        foreach ($list as &$item) {
            if (!isset($item['tday'])) {
                continue;
            }

            $tDay         = $item['tday'];
            $item['tday'] = date('m-d', strtotime($tDay));
        }
    }

    /**
     * @throws \Exception
     */
    public function coinInfoAction(): array
    {
        $params  = $this->wrapParams(\Plus::$app->request);
        $methods = Arr::wrap($params->get('method', ['fields', 'data']));

        $data = [];
        try {
            foreach ($methods as $method) {
                if ($method == 'fields') {
                    $r = [
                        'fields' => [
                            ['title' => '游戏原名', 'dataIndex' => 'cp_game_id'],
                            ['title' => '虚拟成本总预算', 'dataIndex' => 'virtually_cost_budget'],
                            ['title' => '已使用代金券成本', 'dataIndex' => 'cost_coupon_used'],
                            ['title' => '已使用九玩币成本', 'dataIndex' => 'cost_coin_used'],
                            ['title' => '可用成本', 'dataIndex' => 'cost_usable'],
                        ],

                    ];
                }
                elseif ($method == 'data') {
                    $r = $this->coinInfoData($params);
                }
                else {
                    $r = [];
                }
                $data = array_merge($data, $r);
            }
        }
        catch (\Exception $e) {
            return $this->error($e->getMessage());
        }

        return $this->success($data);
    }


    /**
     * @throws \Exception
     */
    public function couponInfoAction(): array
    {
        $params  = $this->wrapParams(\Plus::$app->request);
        $methods = Arr::wrap($params->get('method', ['fields', 'data']));

        $data = [];
        try {
            foreach ($methods as $method) {
                if ($method == 'fields') {
                    $r = [
                        'fields' => [
                            ['title' => '游戏原名', 'dataIndex' => 'cp_game_id'],
                            ['title' => '部门', 'dataIndex' => 'department_id'],
                            ['title' => '可用成本', 'dataIndex' => 'cost_usable'],
                            ['title' => '可用代金券', 'dataIndex' => 'coupon_usable'],
                            ['title' => '已使用代金券', 'dataIndex' => 'coupon_used'],
                            /* ['title' => '成本预算', 'dataIndex' => 'cost_budget'],
                            ['title' => '已使用成本', 'dataIndex' => 'cost_used'],
                            ['title' => '可用成本', 'dataIndex' => 'cost_usable'],
                            ['title' => '代金券预算', 'dataIndex' => 'coupon_budget'],
                            ['title' => '已使用代金券', 'dataIndex' => 'coupon_used'], */
                        ],
                    ];
                }
                elseif ($method == 'data') {
                    $r = $this->couponInfoData($params);
                }
                else {
                    $r = [];
                }
                $data = array_merge($data, $r);
            }
        }
        catch (\Exception $e) {
            return $this->error($e->getMessage());
        }

        return $this->success($data);
    }


    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function couponInfoData(Collection $params): array
    {


        // 暂时写死
        $departmentRatios = [
            '389' => 0.75,   // 国内运营中心
            '387' => 0.25, // 发行直播营销部
        ];

        $year = $params->get('year', date('Y'));

        $options = [
            'range_date_start' => $year . '-01-01',
            'range_date_end'   => $year . '-12-31',
        ];

        if ($params->has('cp_game_id')) {
            $options['cp_game_id'] = $params->get('cp_game_id');
        }

        if ($params->has('department_id')) {
            $departmentId = $params->get('department_id');

            if (!isset($departmentRatios[$departmentId])) {
                return [
                    'list'    => [],
                    'summary' => [
                        'cost_budget'   => 0,
                        'cost_usable'   => 0,
                        'cost_used'     => 0,
                        'coupon_budget' => 0,
                        'coupon_usable' => 0,
                        'coupon_used'   => 0,
                    ],
                ];
            }

            $departmentRatios = array_intersect_key($departmentRatios, array_flip(Arr::wrap($departmentId)));
        }

        $incomeOptions                = $options;
        $incomeOptions['type']        = 1;
        $incomeOptions['only_coupon'] = 1;

        $incomeServ       = new IncomeServ();
        $incomeRe         = $incomeServ->rechargeInfo($incomeOptions, ['cp_game_id'], [], ['sum' => 'DESC'], $incomeServ::MODE_LIST, ['cp_game_id', 'sum_amount', 'coin_deduct', 'coupon_deduct']);
        $paymentCountServ = new PaymentCountServ();
        $couponRe         = $paymentCountServ->fetchPayExtInfo($options, ['cp_game_id', 'coupon_department_id'], [], [], $paymentCountServ::MODE_LIST);
        $incomeList       = $incomeRe['list'] ?? [];
        $couponList       = $couponRe['list'] ?? [];
        $couponSummary    = ['coupon_used' => 0];
        $incomeSummary    = [];

        // 计算对应部门的代金券预算
        $resultList            = [];
        $cpGamesWithUsedCoupon = array_unique(array_column($couponList, 'cp_game_id'));

        foreach ($incomeList as $item) {
            $cpGame = $item['cp_game_id'] ?? ($item['CP_GAME_ID'] ?? '');

            if (!in_array($cpGame, $cpGamesWithUsedCoupon)) continue;

            $amount       = floatval($item['sum'] ?? 0);           // 充值总金额
            $couponDeduct = floatval($item['coupon_deduct'] ?? 0); // 代金券金额
            $coinDeduct   = floatval($item['coin_deduct'] ?? 0);   // 九玩币抵扣金额
            foreach ($departmentRatios as $depId => $ratio) {
                $uk = implode('|', [$cpGame, $depId]);
                // $budget = round($amount * 0.1 * $ratio);
                if (!isset($resultList[$uk])) {
                    $resultList[$uk] = [
                        'department_id' => $depId,
                        'cp_game_id'    => $cpGame,
                    ];
                }
                $resultList[$uk]['amount']      = ($resultList[$uk]['amount'] ?? 0) + $amount;
                $resultList[$uk]['coin_deduct'] = ($resultList[$uk]['coin_deduct'] ?? 0) + $coinDeduct;

                /* else {
                    isset($resultList[$uk]['coupon_budget'])
                        ? $resultList[$uk]['coupon_budget'] += $budget
                        : $resultList[$uk]['coupon_budget'] = $budget;
                } */
            }
        }

        // 已使用代金券
        foreach ($couponList as $item) {
            $depId = $item['coupon_department_id'] ?? ($item['COUPON_DEPARTMENT_ID'] ?? 389);
            if ($depId == 0) $depId = 389;

            $cpGame     = $item['cp_game_id'] ?? ($item['CP_GAME_ID'] ?? '');
            $uk         = implode('|', [$cpGame, $depId]);
            $couponUsed = $item['coupon_money'] ?? 0.00; // 已使用代金券金额

            if (!isset($resultList[$uk])) {
                $resultList[$uk] = [
                    'department_id' => $depId,
                    'cp_game_id'    => $cpGame,
                ];
            };
            $resultList[$uk]['coupon_used'] = ($resultList[$uk]['coupon_used'] ?? 0) + $couponUsed;
            /* isset($resultList[$uk]['coupon_used'])
                ? $resultList[$uk]['coupon_used'] += $couponUsed
                : $resultList[$uk]['coupon_used'] = $couponUsed; */

            // $couponSummary['coupon_used'] += $couponUsed;
        }

        foreach ($resultList as &$foo) {
            /* if (!isset($foo['coupon_used'])) {
                $foo['cost_used'] = 0;
            }

            // 可用代金券
            $foo['coupon_usable'] = round($foo['coupon_budget'] - ($foo['coupon_used'] ?? 0));

            // 成本预算
            $foo['coupon_budget'] = floatval($foo['coupon_budget']);

            $foo['cost_budget'] =
                !empty($foo['coupon_budget'])
                    ? round($foo['coupon_budget'] / 5)
                    : 0.00;
            $foo['cost_used']   =
                !empty($foo['coupon_used'])
                    ? round($foo['coupon_used'] / 5)
                    : 0.00;
            $foo['cost_usable'] =
                !empty($foo['coupon_usable'])
                    ? round($foo['coupon_usable'] / 5)
                    : 0.00; */

            /*
                虚拟成本总预算 = 游戏项目总充值金额 * 2%
                已使用代金券成本 = 已使用代金券抵扣金额 * 20%
                已使用九玩币成本 = 已使用九玩币抵扣金额 * 20%

                国内运营中心可用成本 =（项目虚拟成本总预算 - 已使用九玩币成本）* 75% - 运营中心已使用的代金券抵扣金额 *20%
                发行直播营销部可用成本 =（项目虚拟成本总预算 - 已使用九玩币成本）* 25% - 发行直播营销部已使用的代金券抵扣金额 *20%
                可用代金券=可用成本 * 5
                已使用代金券=各部门已使用的代金券金额
            */
            // 初始化数值
            $foo['amount']      = round($foo['amount'] ?? 0);
            $foo['coupon_used'] = round($foo['coupon_used'] ?? 0);
            $foo['coin_deduct'] = round($foo['coin_deduct'] ?? 0);

            // 部门占比
            $ratio = $departmentRatios[$foo['department_id']] ?? 0;

            // 已使用九玩币成本
            $coinCostUsed = round($foo['coin_deduct'] * 0.2, 2);

            // 可用成本
            $foo['cost_usable'] = round(((($foo['amount'] * 0.02) - $coinCostUsed) * $ratio) - ($foo['coupon_used'] * 0.2));
            // 可用代金券=可用成本 * 5
            $foo['coupon_usable'] = round($foo['cost_usable'] * 5);
        }

        unset($foo);
        foreach ($resultList as $i => $foo) {
            $depId = $foo['department_id'] ?: null;

            if (!isset($departmentRatios[$depId])) {
                unset($resultList[$i]);
            }
        }

        // $incomeSummary['coupon_budget'] = round(array_sum(array_column($resultList, 'coupon_budget')));
        $incomeSummary['coupon_used']   = round(array_sum(array_column($resultList, 'coupon_used')));
        $incomeSummary['coupon_usable'] = round(array_sum(array_column($resultList, 'coupon_usable')));
        // $incomeSummary['cost_budget']   = round(array_sum(array_column($resultList, 'cost_budget')));
        // $incomeSummary['cost_used']     = round(array_sum(array_column($resultList, 'cost_used')));
        $incomeSummary['cost_usable'] = round(array_sum(array_column($resultList, 'cost_usable')));

//        $resultList = array_order($resultList, 'coupon_used', SORT_DESC, 'cp_game_id', SORT_DESC);

        $constConfCollect = (new BasicServ())->getMultiOptions(['department_id', 'cp_game_id:all']);
        $replaceFn        = $this->replaceColumnDefine($constConfCollect);
        $process          = new ProcessLine();
        $process->addProcess($replaceFn);
        $process->run($resultList);

        return ['list' => array_values($resultList), 'summary' => $incomeSummary];
    }


    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function coinInfoData(Collection $params): array
    {
        $year = $params->get('year', date('Y'));

        $options = [
            'range_date_start' => $year . '-01-01',
            'range_date_end'   => $year . '-12-31',
        ];

        if ($params->has('cp_game_id')) {
            $options['cp_game_id'] = $params->get('cp_game_id');
        }

        $incomeOptions                = $options;
        $incomeOptions['type']        = 1;
        $incomeOptions['only_coupon'] = 1;

        $incomeServ = new IncomeServ();
        $incomeRe   = $incomeServ->rechargeInfo($incomeOptions, ['cp_game_id'], [], ['sum' => 'DESC'], $incomeServ::MODE_LIST, ['cp_game_id', 'sum_amount', 'coin_deduct', 'coupon_deduct']);

        $incomeList = $incomeRe['list'] ?? [];

        $incomeSummary = [];

        $resultList = [];
        /*
            按游戏项目统计、年度累计数据
            虚拟成本总预算 = 游戏项目总充值金额 * 2%   virtually_cost_budget
            已使用代金券成本 = 已使用代金券抵扣金额 * 20%  cost_coupon_used
            已使用九玩币成本 = 已使用九玩币抵扣金额 * 20%  cost_coin_used
            可用成本 = 虚拟成本总预算 - 已使用代金券成本 - 已使用九玩币成本
        */

        foreach ($incomeList as $key => $item) {
            $tmpData                = $item + [
                    'cp_game_id'            => $item['cp_game_id'] ?? ($item['CP_GAME_ID'] ?? ''),
                    'virtually_cost_budget' => round($item['sum'] * 0.02),
                    'cost_coupon_used'      => round($item['coupon_deduct'] * 0.2),
                    'cost_coin_used'        => round($item['coin_deduct'] * 0.2),
                ];
            $tmpData['cost_usable'] = round($tmpData['virtually_cost_budget'] - $tmpData['cost_coupon_used'] - $tmpData['cost_coin_used']);
            $resultList[]           = $tmpData;
        }

        $incomeSummary['virtually_cost_budget'] = round(array_sum(array_column($resultList, 'virtually_cost_budget')));
        $incomeSummary['cost_coupon_used']      = round(array_sum(array_column($resultList, 'cost_coupon_used')));
        $incomeSummary['cost_coin_used']        = round(array_sum(array_column($resultList, 'cost_coin_used')));
        $incomeSummary['cost_usable']           = round(array_sum(array_column($resultList, 'cost_usable')));

        $constConfCollect = (new BasicServ())->getMultiOptions(['cp_game_id:all']);
        $replaceFn        = $this->replaceColumnDefine($constConfCollect);
        $process          = new ProcessLine();
        $process->addProcess($replaceFn);
        $process->run($resultList);

        return ['list' => array_values($resultList), 'summary' => $incomeSummary];
    }

}
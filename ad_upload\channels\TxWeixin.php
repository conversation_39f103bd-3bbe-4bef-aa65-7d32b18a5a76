<?php
/**
 * 腾讯微信渠道数据上报
 * Created by PhpStorm.
 * User: Tim
 * Date: 2019/3/14
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class Tx<PERSON>eixin extends AdBaseInterface
{
    private $accessToken = '';

    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'ACTIVATE_APP');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, 'REGISTER');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'PURCHASE');
    }

    /**
     * @param $info
     * @param $uploadType
     * @return void
     */
    private function uploadData($info, $uploadType = 'ACTIVATE_APP')
    {
        $url = $info['CALLBACK_URL'];
        if (!empty($url)) {
            //拼接上报参数
            $money = isset($info['MONEY']) ? $info['MONEY'] * 100 : 0;
            $imei  = $idfa = $androidId = '';
            if (substr($info['PACKAGE_ID'], -2) == 99) { //IOS包
                $idfa = $info['MD5_DEVICE_ID'];
            } else { //安卓包
                $imei      = $info['MD5_DEVICE_ID'];
                $androidId = md5($info['ANDROID_ID']);
            }
            $data = [
                'actions' => [
                    [
                        'outer_action_id' => $info['ID'] . $uploadType, //可根据此去重，防止重复上报
                        'action_time'     => time(),
                        'user_id'         => [
                            'hash_imei'       => $imei,
                            'hash_idfa'       => $idfa,
                            'hash_android_id' => $androidId,
                        ],
                        'action_type'     => $uploadType,
                        'action_param'    => [
                            'value' => $money
                        ]

                    ]
                ]
            ]; //可以多条上报

            //上报
            $http = new Http($url);
            $res  = $http->post($data);

            $resArr  = json_decode($res, true);
            $message = $resArr['errcode'] == 0 ? '上报成功' : '上报失败';

            //记录上报结果
            $logInfo                 = $info['log_info'] ?? [];
            $logInfo['channel_code'] = 'tx_weixin';
            $logInfo['request']      = json_encode(['url' => $url, 'data'=>$data]);
            $logInfo['response']     = $res;
            $resContent              = json_decode($res, true);

            if ($resContent['errcode'] == 0) {
                $logInfo['reported_status'] = 1;
            } else {
                $logInfo['reported_status'] = -1;
            }
            //写日志
            $this->log($info, $logInfo, $res, $url);
        }// end if()
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

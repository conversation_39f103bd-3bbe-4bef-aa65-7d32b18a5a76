<?php

namespace app\extension\Support\Helpers\DBHelper;

use Spiral\Database\Query\SelectQuery;

abstract class MatcherAbstract
{
    protected array $reflectMap = [];

    public function __construct()
    {
        if (func_num_args() === 1) {
            [$this->reflectMap] = func_get_args();
        }
    }

    public function __invoke(SelectQuery &$qb, array $params)
    {
        $carryList = $this->matchFnList();
        $keys      = array_unique(array_map([$this, 'pickColumn'], array_keys($params)));

        if ($carryList == 0) return;

        foreach ($params as $k => $value) {
            $column   = $this->pickColumn($k);
            $operator = $this->pickOperator($k) ?? '=';

            if (
                !isset($carryList[$column])
                || !is_callable($carryList[$column])
            ) continue;

            $callback = $carryList[$column];
            $field    = $this->getReflectKey($column);

            $callback($qb, $field, $value, $params, $operator);
        }
    }

    abstract protected function matchFnList(): array;

    protected function getReflectKey($key)
    {
        return $this->reflectMap[$key] ?? $key;
    }

    /**
     * @param string $column
     * @return string
     */
    protected function pickColumn(string $column): string
    {
        preg_match(
            '/(?<column>[a-zA-Z0-9_\.]+)(?:\s*\((?<alias>[a-zA-Z0-9_]+)\))?(?:\s*\[(?<type>(?:String|Bool|Int|Number|Object|JSON))\])?/i',
            $column, $match
        );

        return $match['column'] ?? '';
    }

    /**
     * @param string $column
     * @return string
     */
    protected function pickOperator(string $column): string
    {
        preg_match('/([a-zA-Z0-9_\.]+)(\[(?<operator>\>\=?|\<\=?|\!|\<\>|\>\<|\!?~|REGEXP)\])?/i',
                   $column, $match);

        return $match['operator'] ?? '';
    }


}
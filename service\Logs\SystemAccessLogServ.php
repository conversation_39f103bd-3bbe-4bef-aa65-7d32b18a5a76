<?php
// phpcs:disable
namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;

class SystemAccessLogServ
{
    /**
     * @param string $userName
     * @return array
     */
    public function getRecentVisits(string $userName): array
    {
        $sql = "
        select uri, type, count(1) as visits
        from (select *
              from dataspy.system_access_log
              where USER_NAME = '{$userName}'
                and TYPE = 'PAGE'
                and URI != '/home/<USER>'
              order by id desc
              limit 10) main_body
        group by URI, TYPE
        order by visits desc
        ";

        return FakeDB::connection('dataspy')->query($sql)->fetchAll();
    }
}

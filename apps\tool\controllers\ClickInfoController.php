<?php

namespace app\apps\tool\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\service\Tool\ClickInfoServ;
/**
 * 日志 -> 点击明细
 *
 * @route /tool/click-info/*
 *
 */
class ClickInfoController extends BaseTableController
{
    /**
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $options = $params->toArray();

        try {
            return (new ClickInfoServ())->listInfo($options);
        }
        catch (\UnexpectedValueException $ue) {
            return ['list' => [], 'summary' => [], 'total' => 0];
        }
    }

    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '点击时间', 'dataIndex'          => 'activate_time', 'classify' => ['attrs', 'base']],
            ['title' => '包号', 'dataIndex'            => 'package_id', 'classify'    => ['attrs', 'base']],
            ['title' => '计划短链名称', 'dataIndex'        => 'aid', 'classify'           => ['attrs', 'base']],
            ['title' => 'OAID', 'dataIndex'          => 'oaid', 'classify'          => ['attrs', 'base']],
            ['title' => '安卓ID', 'dataIndex'          => 'android_id', 'classify'    => ['attrs', 'base']],
            ['title' => 'MD5_DEVICE_ID', 'dataIndex' => 'md5_device_id', 'classify' => ['attrs', 'base']],
            ['title' => 'ip', 'dataIndex'            => 'ip', 'classify'            => ['attrs', 'base']],
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                ],
            ],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $start = date('Y-m-d');
        $end   = date('Y-m-d');
        /*

        */
        return collect([
            ['field' => 'method', 'default' => ['data', 'fields']],
            // 类型
            ['field' => 'type', 'default' => 'CORE_ACCOUNT'],
            // 主键ID
            ['field' => 'id'],
            // 激活来源ID
            // ['field' => 'source_active_id'],
            // 激活ID
            // ['field' => 'active_id'],
            // ['field' => 'page_size', 'default' => 100],
            // ['field' => 'page', 'default' => 1],
        ]);
    }
}
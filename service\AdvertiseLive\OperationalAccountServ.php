<?php

namespace app\service\AdvertiseLive;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiseLive\Helpers\TableConst;
use Spiral\Database\Database;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class OperationalAccountServ
{

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     *
     * @return array
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $db = $this->getConn();
        $qb = $db->select()->from(TableConst::CONF_LIVE_OP_ACCOUNT);

        $qb->columns([
            'id as id',
            'account_name as account_name',
            'sorted as sorted',
            'state as state',
            'operator_id as operator_id',
        ]);

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        if (!empty($params['state'])) {
            $qb->where('state', $params['state']);
        }

        return $qb->fetchAll();
    }

    /**
     * @param $list
     *
     * @return mixed
     * @throws \Throwable
     */
    public function updateMultiById($list)
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $nDb) use ($list) {
            foreach ($list as $item) {
                $id = Arr::pull($item, 'id');

                if (is_null($id)) continue;

                $nDb
                    ->table(TableConst::CONF_LIVE_OP_ACCOUNT)
                    ->update($item)
                    ->where('id', $id)->run();
            }

            return true;
        });
    }

    /**
     * @param array $singleData
     *
     * @return mixed
     * @throws \Throwable
     */
    public function insert(array $singleData = [])
    {
        $db = $this->getConn();
        return $db->transaction(function (Database $nDb) use ($singleData) {
            $baseQb = $nDb->table(TableConst::CONF_LIVE_OP_ACCOUNT);
            if (empty($singleData['account_name'])) {
                throw new \InvalidArgumentException('缺少必要参数');
            }
            $count = (clone $baseQb)->select()->where('account_name', $singleData['account_name'])->count();

            if ($count > 0) {
                throw new \InvalidArgumentException('该运营账号已存在');
            }

            return (clone $baseQb)->insertOne($singleData);
        });
    }

    /**
     * 硬删除
     *
     * @param array $ids
     *
     * @return mixed
     * @throws \Throwable
     */
    public function removeByIds(array $ids)
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $nDb) use ($ids) {
            $baseQb = $nDb->table(TableConst::CONF_LIVE_OP_ACCOUNT);

            $delQb = (clone $baseQb)->delete()->where('id', new Parameter($ids));

            return $delQb->run();
        });
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('base_conf_platform');
    }
}
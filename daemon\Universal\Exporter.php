<?php declare(ticks=1);

namespace app\daemon\Universal;

use app\service\General\ExportManagerServ;
use PhpAmqpLib\Connection\Heartbeat\PCNTLHeartbeatSender;
use PhpAmqpLib\Message\AMQPMessage;

class Exporter extends \Plus\CLI\DaemonProcess
{
    private const QueueTopic = 'report_export';

    public function run()
    {
        $retry = 3;
        @ini_set('memory_limit', -1);
        print 'starting...' . PHP_EOL;

        try {
            consumer_event:
            $this->jobConsumer();
        }
        catch (\Exception $e) {
            print $e->getTraceAsString() . PHP_EOL;
            print $e->getMessage() . PHP_EOL;

            $retry--;

            if ($retry != 0) {
                goto consumer_event;
            }
        }
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function jobConsumer()
    {
        \Plus::$app->export_mq->consume(function (AMQPMessage $message) {
            $message->ack();
            try {
                $messageBody = $message->getBody();
                print $messageBody . PHP_EOL;
                $serv = new ExportManagerServ();
                $body = json_decode($messageBody, true);

                if (json_last_error() != JSON_ERROR_NONE) {
                    $message->ack();
                    throw new \Exception('JSON 解析错误' . json_last_error_msg());
                }

                $serv->exportTask($body);
            }
            catch (\Exception $e) {
                print $e->getTraceAsString() . PHP_EOL;
                print $e->getMessage() . PHP_EOL;
                sleep(5);
                \Plus::$app->universal_mq->put(static::QueueTopic, $messageBody);
            }
        }, static::QueueTopic);
    }


}
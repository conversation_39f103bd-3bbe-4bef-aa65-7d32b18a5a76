<?php

namespace app\extension\Support\CommonCenter\ConfCenter;

use app\extension\Exception\ParameterException;
use app\extension\Support\Contracts\AuthAble;
use app\extension\Support\Contracts\RequireAccessToken;
use Plus\Net\Http;

/**
 * 配置中心请求客户端
 *
 */
class Request extends Http implements AuthAble, RequireAccessToken
{

    protected const INDEX_KEY_CODE = 'cltj';
    protected const INDEX_KEY_KK   = 'xdjfdnne';
    private $pageNo = 1;
    private $pageSize = 10000;

    /**
     * @param $params
     * @param $headers
     * @return bool|string
     * @throws ParameterException
     */
    public function get($params = null, $headers = [])
    {
        if (empty($headers['Access-Token'])) {
            ['token' => $accessToken] = $this->auth();
            $headers[] = 'Access-Token:' . $accessToken;
        }
        if (!isset($params["pageNo"])) {
            $params["pageNo"] = $this->pageNo;
        }
        if (!isset($params["pageSize"])) {
            $params["pageSize"] = $this->pageSize;
        }
        return parent::get($params, $headers);
    }

    /**
     * @param $params
     * @param $fileUpload
     * @param $headers
     * @return bool|string
     * @throws ParameterException
     */
    public function post($params = null, $fileUpload = false, $headers = [])
    {
        if (empty($headers['Access-Token'])) {
            ['token' => $accessToken] = $this->auth();
            $headers[] = 'Access-Token:' . $accessToken;
        }

        return parent::post($params, $fileUpload, $headers);
    }

    /**
     * @param array $options
     * @return array
     * @throws ParameterException
     */
    public function auth(array $options = []): array
    {
        $tokenInfo = $this->getAccessToken([
            'code' => ($options['code'] ?? static::INDEX_KEY_CODE),
            'key'  => ($options['key'] ?? static::INDEX_KEY_KK),
        ]);

        return ['token' => $tokenInfo['access_token']];
    }

    /**
     * @param array $options
     * @return mixed|void
     * @throws ParameterException
     */
    public function getAccessToken(array $options = [])
    {
        if (empty($options['code']) || empty($options['key'])) {
            return [];
        }

        // todo: 缓存拿取
        return $this->requestToken($options);
    }

    /**
     * @param string $token
     * @param array  $options
     * @return bool
     */
    public function checkToken(string $token, array $options): bool
    {
        // 暂时没什么必要
        return true;
    }

    /**
     * 请求获取业务中台令牌权限
     *
     * @param array $options
     * @return mixed|array
     * @throws ParameterException
     * @throws \Exception
     */
    private function requestToken(array $options)
    {
        $uri = Uri::getHost() . Uri::URI_TOKEN;

        [
            'code' => $code,
            'key'  => $key,
        ] = $options;

        if (empty($code) || empty($key)) {
            throw new ParameterException('[fail to get token]missing required argument...');
        }

        $now  = \time();
        $sign = \md5(\sha1($code . $now . $key));

        $httpClient = new Http($uri);
        $result     = $httpClient->get([
            'code' => $code,
            'time' => $now,
            'sign' => $sign,
        ]);

        $result = \json_decode($result, true);

        if (empty($result['data']['access_token'])) {
            throw new \RuntimeException('[fail to get token]response data is null...');
        }

        return $result['data'];
    }
}
<?php

namespace app\apps\media\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Exception\ParameterException;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\media\MediaVideoLogic;

/**
 * 短视频-视频数据
 */
class VideoController extends BaseTableController
{
    use ColumnsInteract;

    /**
     * @param Collection $params
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $paginate = [
            'page'      => $params->pull('page'),
            'page_size' => $params->pull('page_size'),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = ['tday' => 'DESC'];
        }

        $groups  = Arr::wrap($params->pull('groups'));
        $options = $params->toArray();

        return (new MediaVideoLogic())->getInfo($options, $groups, $paginate, $sort);
    }

    /**
     * 表头字段
     *
     * @param Collection $params
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'play_index', 'label' => '播放量指标'],
                    ['value' => 'duration_index', 'label' => '播放时长'],
                    ['value' => 'interact', 'label' => '互动指标'],
                ],
            ],
        ];

        $info = [
            'tday'                    => ['title' => '发布时间', 'dataIndex' => 'tday', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'media_platform'          => ['title' => '媒体平台', 'dataIndex' => 'media_platform', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'business_ownership_name' => ['title' => '业务归属', 'dataIndex' => 'business_ownership_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'account_name'            => ['title' => '账号名称', 'dataIndex' => 'account_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'account_id'              => ['title' => '账号ID', 'dataIndex' => 'account_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'use_kind_name'           => ['title' => '账号归属', 'dataIndex' => 'use_kind_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'operations_manager'      => ['title' => '运营负责人', 'dataIndex' => 'operations_manager', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'video_title'             => ['title' => '视频标题', 'dataIndex' => 'video_title', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'demo_video_title'        => ['title' => '样片标题', 'dataIndex' => 'demo_video_title', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'demo_video_id'           => ['title' => '样片ID', 'dataIndex' => 'demo_video_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'mc_id'                   => ['title' => '批创ID', 'dataIndex' => 'mc_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'mc_video_id'             => ['title' => '视频ID', 'dataIndex' => 'mc_video_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'video_topic'             => ['title' => '视频话题', 'dataIndex' => 'video_topic', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'job_kind'                => ['title' => '任务类型', 'dataIndex' => 'job_kind', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'task_script_name'        => ['title' => '发布脚本', 'dataIndex' => 'task_script_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'task_id'                 => ['title' => '发布任务ID', 'dataIndex' => 'task_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'video_fitid'             => ['title' => '媒体视频标识', 'dataIndex' => 'video_fitid', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'media_video_title'       => ['title' => '媒体视频标题', 'dataIndex' => 'media_video_title', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'media_video_topic'       => ['title' => '媒体视频话题', 'dataIndex' => 'media_video_topic', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'data_label'              => ['title' => '数据标签', 'dataIndex' => 'data_label', 'sorter' => true, 'classify' => ['attrs', 'base']],
        ];

        $fields = [
            'play_one'   => ['title' => '1日播放量', 'dataIndex' => 'play_one', 'sorter' => true, 'classify' => ['attrs', 'play_index']],
            'play_two'   => ['title' => '2日播放量', 'dataIndex' => 'play_two', 'sorter' => true, 'classify' => ['attrs', 'play_index']],
            'play_three' => ['title' => '3日播放量', 'dataIndex' => 'play_three', 'sorter' => true, 'classify' => ['attrs', 'play_index']],
            'play_count' => ['title' => '累计播放量', 'dataIndex' => 'play_count', 'sorter' => true, 'classify' => ['attrs', 'play_index']],

            'completion_5s_rate' => ['title' => '5秒完播率', 'dataIndex' => 'completion_5s_rate', 'classify' => ['attrs', 'duration_index']],
            'completion_rate'    => ['title' => '整体完播率', 'dataIndex' => 'completion_rate', 'classify' => ['attrs', 'duration_index']],
            'video_duration'     => ['title' => '视频时长(片长)', 'dataIndex' => 'video_duration', 'classify' => ['attrs', 'duration_index']],
            'avg_play_duration'  => ['title' => '平均播放时长', 'dataIndex' => 'avg_play_duration', 'classify' => ['attrs', 'duration_index']],

            'like_count'            => ['title' => '点赞数', 'dataIndex' => 'like_count', 'sorter' => true, 'classify' => ['attrs', 'interact']],
            'comment_count'         => ['title' => '评论数', 'dataIndex' => 'comment_count', 'sorter' => true, 'classify' => ['attrs', 'interact']],
            'share_count'           => ['title' => '转发数', 'dataIndex' => 'share_count', 'sorter' => true, 'classify' => ['attrs', 'interact']],
            'fans_increase_count'   => ['title' => '新增粉丝量', 'dataIndex' => 'fans_increase_count', 'sorter' => true, 'classify' => ['attrs', 'interact']],
            'homepage_access_count' => ['title' => '主页访问次数', 'dataIndex' => 'homepage_access_count', 'sorter' => true, 'classify' => ['attrs', 'interact']],
        ];

        $logic  = new MediaVideoLogic();
        $groups = Arr::wrap($params->pull('groups'));

        if (!empty($groups)) {
            $resetGroupFn = $this->resetGroupsCols($logic->groupRelationMap($groups), $groups, array_keys($info));
        }
        else {
            $resetGroupFn = fn() => true;
        }

        $resetGroupFn($info);
        $fields = array_merge($info, $fields);
        foreach ($fields as $k => &$item) {
            if ($item == '-') {
                $item = null;
            }
        }

        return ['fields' => array_values(array_filter($fields)), 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $nowToday = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start'],
            ['field' => 'range_date_end'],
            ['field' => 'media_platform_id'],
            ['field' => 'account_id'],
            ['field' => 'business_ownership'],
            ['field' => 'video_title'],
            ['field' => 'video_topic'],

            ['field' => 'use_kind'],
            ['field' => 'operations_manager'],
            ['field' => 'demo_video_id'],
            ['field' => 'mc_id'],
            ['field' => 'mc_video_id'],
            ['field' => 'video_duration'],
            ['field' => 'task_script_id'],
            ['field' => 'row_options'],
            ['field' => 'job_kind'],
            ['field' => 'media_video_topic'],
            ['field' => 'media_video_title'],
            ['field' => 'video_fitid'],
            ['field' => 'task_id'],
            ['field' => 'data_label'],
            ['field' => 'data_type'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

    /**
     * 视频受众信息查询
     *
     * @return array
     * @throws ParameterException
     */
    public function audienceAnalyzeAction(): array
    {
        $params  = $this->wrapParams(\Plus::$app->request);
        $groups  = Arr::wrap($params->pull('groups'));
        $options = $params->toArray();
        $logic   = new MediaVideoLogic();
        $info    = $logic->audienceAnalyze($options, $groups);

        return $this->success($info);
    }

}
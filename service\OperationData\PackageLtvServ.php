<?php

namespace app\service\OperationData;

use app\apps\internal\Helpers\ConstHub;
use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\service\General\Helpers\DdcPlatformTable;
use app\service\OperationData\Components\MatchParams\PackageLtvMatch;
use app\service\OperationData\Components\MatchParams\PackageMatch;
use app\util\Common;
use phpseclib3\File\ASN1\Maps\Time;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Query\SelectQuery;

/**
 * @date 2024/01/19
 */
class PackageLtvServ
{
    public array $ltvTableMaps = [
        'first_log.ltv' => DdcPlatformTable::DwsFirstloginLtvDaily,
        'new_log.ltv'   => DdcPlatformTable::DwsPackageLtvDaily,
    ];

    protected string $ltvTable;

    public function __construct(string $table)
    {
        if (isset($this->ltvTableMaps[$table])) {
            $this->ltvTable = $this->ltvTableMaps[$table];
        }
        else {
            $this->ltvTable = $table;
        }
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * @return SelectQuery
     */
    protected function baseQueryBuilder(): SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from($this->ltvTable . ' as t_ltv');

        $qb
            ->innerJoin(new Fragment(str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql())), 'power')
            ->on(['t_ltv.package_id' => 'power.package_id']);

        return $qb;
    }

    /**
     * @param SelectQuery $infoQb
     * @param array $params
     * @param array $groups
     * @param $quote
     * @return SelectQuery
     * @throws \Exception
     */
    protected function queryBuilderJoinInfoQb(
        SelectQuery $infoQb, array &$params = [], array $groups = []
    ): SelectQuery
    {
        $db           = $this->getConn();
        $mainSubQuery = $this->baseQueryBuilder();
        $matcher      = new PackageLtvMatch([
                                                'tday'       => 't_ltv.tday',
                                                'package_id' => 't_ltv.package_id',
                                                'cp_game_id' => 't_ltv.cp_game_id',
                                                'game_id'    => 't_ltv.game_id',
                                            ]);

        $ltvCols  = $this->ltvCols($params);
        $ltv1000  = $this->getLtvTotalCol($params);
        $ltvCols  = array_merge($ltvCols, $ltv1000);
        $infoCols = $this->ltvInfoCols($params);
        $dayCols  = $this->generateDayCol($params);
        $cols     = array_merge($infoCols, $ltvCols);

        if (in_array('tday', $groups)) {
            $cols = array_merge($cols, $dayCols);
        }

        $mainSubQuery->columns($cols);
        $matcher($mainSubQuery, $params);

        $groupMap = [
            'package_id'      => 't_ltv.package_id',
            'channel_id'      => 'power.channel_id',
            'channel_main_id' => 'power.channel_main_id',
            'user_id'         => 'power.ad_user_id',
            'department_id'   => 'power.ad_department_id',
            'tday'            => 'group_day',
        ];

        $joinOn = [];
        foreach ($groups as $g) {
            if (isset($groupMap[$g])) {
                $mainSubQuery->groupBy($groupMap[$g]);
            }
            else {
                $mainSubQuery->groupBy($g);
            }

            $m          = 't_ltv.' . $g;
            $s          = 'slave_info.' . $g;
            $joinOn[$m] = $s;
        }

        $newDb = $this->getConn();
        $newQb = $newDb->select()->from(new Fragment('(' . $mainSubQuery->__toString() . ') as t_ltv'));
        $newQb
            ->leftJoin(new Fragment('(' . $infoQb->__toString() . ')'), 'slave_info')
            ->on($joinOn);

        return $newQb;
    }

    /**
     * 生成LTV查询列
     *
     * @param array $params
     * @param int $mode
     * @param string $moneyField
     * @param string $dayField
     * @return array
     * @throws \Exception
     */
    protected function ltvCols(
        array  $params = [],
        int    $mode = 1,
        string $moneyField = 'money_all',
        string $dayField = 'tday'
    ): array
    {
        $cols      = [];
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $alignDate = $params['pay_date[<=]'] ?? $yesterday;
        $minDay    = $yesterday;

        if (isset($params[$dayField])) {
            $days   = Arr::wrap($params[$dayField]);
            $minDay = min($days);
        }

        $maxDayNode = days_apart($minDay, $alignDate) + 1;

        if ($mode == 1) {
            $colFn = fn($i) => new Fragment("SUM(IF(day_type ={$i}, {$moneyField}, 0)) as ltv_{$i}");
        }
        else {
            $colFn = fn($i) => "ltv_{$i}";
        }
        $maxDayNode = min($maxDayNode, 720);
        for ($i = 1; $i <= $maxDayNode; $i++) {
            $cols[] = $colFn($i);
        }

        return $cols;
    }

    /**
     * @param array $params
     * @param array $eqCols
     * @return array
     */
    protected function ltvInfoCols(
        array $params, array $eqCols = []
    ): array
    {
        return [
            'cp_game_id'      => 't_ltv.cp_game_id as cp_game_id',
            'game_id'         => 't_ltv.game_id as game_id',
            'package_id'      => 't_ltv.package_id as package_id',
            'platform_id'     => 'power.platform_id as platform_id',
            'channel_id'      => 'power.channel_id as channel_id',
            'channel_main_id' => 'power.channel_main_id as channel_main_id',
            'department_id'   => 'power.ad_department_id as department_id',
            'user_id'         => 'power.ad_user_id as user_id',
            'promotion_id'    => 'power.popularize_v2_id as promotion_id',
            'app_show_id'     => 'power.app_show_id as app_show_id',
        ];
    }

    /**
     * @param array $params
     * @param string $table
     * @param string $alias
     * @return array
     * @throws \Exception
     */
    protected function generateDayCol(array &$params = [], string $table = 't_ltv', string $alias = 'group_day'): array
    {
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        if ($timeDimension == ConstHub::DIMENSION_WEEK) {
            $days = $params['tday'];
            sort($days);

            [
                'begin' => $rangeStart,
                'end'   => $rangeEnd,
                'cycle' => $rangeDateCycle
            ] = TimeUtil::divideWeekByRangeDate(...$days);

            $params['tday'] = [$rangeStart, $rangeEnd];
            $dayCols        = [];

            foreach ($rangeDateCycle as $item) {
                ['begin_date' => $begin, 'end_date' => $end] = $item;
                $dayCols[] = sprintf(
                    "when %s.tday between '%s' and '%s' then '%s'",
                    $table, $begin, $end, $begin . '/' . $end
                );
            }

            $caseString    = implode(' ', $dayCols);
            $dayString     = " CONCAT(case {$caseString} end) as tday ";
            $dayGroupAlias = " CONCAT(case {$caseString} end) as $alias ";

            return [new Fragment($dayString), new Fragment($dayGroupAlias)];
        }
        elseif ($timeDimension == ConstHub::DIMENSION_MONTH) {
            return [
                new Fragment(" DATE_FORMAT({$table}.tday, '%Y-%m') as tday"),
                new Fragment(" DATE_FORMAT({$table}.tday, '%Y-%m') as group_day"),
            ];
        }
        else {
            return [
                "{$table}.tday as tday",
                "{$table}.tday as {$alias}",
            ];
        }
    }

    /**
     * 获取当前LTV的值
     *
     * @param array $params
     * @return Fragment[]
     */
    protected function getLtvTotalCol(array $params = []): array
    {
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        if (!empty($params['pay_date[<=]']) && $timeDimension == ConstHub::DIMENSION_DAY) {
            $maxPayDate = $params['pay_date[<=]'];
            return [new Fragment("SUM(IF(DAY_TYPE = datediff('{$maxPayDate}', t_ltv.tday)+1, t_ltv.money_all, 0)) as ltv_1000")];
        }
        else {
            return [new Fragment("SUM(IF(DAY_TYPE = 1000, money_all, 0)) as ltv_1000")];
        }
    }

    /**
     * @param SelectQuery $infoQb
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $cols
     * @param int $mode
     * @return void
     * @throws \Exception
     */
    public function getListWithInfoQb(
        SelectQuery $infoQb,
        array       $params = [],
        array       $groups = [],
        array       $paginate = [],
        array       $sort = [],
        array       $cols = [],
        int         $mode = -1
    ): array
    {
        $result = [];
        $qb     = $this->queryBuilderJoinInfoQb($infoQb, $params, $groups);
        $qb->where('link_mark', 1);

        $matcher      = new PackageLtvMatch([
                                                'tday'            => 't_ltv.tday',
                                                'package_id'      => 'slave_info.package_id',
                                                'cp_game_id'      => 'slave_info.cp_game_id',
                                                'game_id'         => 'slave_info.game_id',
                                                'platform_id'     => 'slave_info.platform_id',
                                                'app_show_id'     => 'slave_info.app_show_id',
                                                'channel_main_id' => 'slave_info.channel_main_id',
                                                'channel_id'      => 'slave_info.channel_id',
                                                'promotion_id'    => 'slave_info.promotion_id',
                                                'department_id'   => 'slave_info.department_id',
                                                'user_id'         => 'slave_info.user_id',
                                            ]);
        $filterParams = array_intersect_key(array_diff_key($params, ['tday' => '']), array_flip($groups));
        $matcher($qb, $filterParams);
        $topRemainCols   = $this->ltvCols($params, 2);
        $topRemainCols[] = 'ltv_1000';
        $topCols         = array_merge([new Fragment('slave_info.*')], $topRemainCols);
        $qb->columns($topCols);

        @Common::dumpSql($qb->__toString());
        $result['list'] = $qb->fetchAll();

        $summaryQb = $this->baseQueryBuilder($params, $groups);
        $matcher   = new PackageLtvMatch([
                                             'tday'            => 't_ltv.tday',
                                             'package_id'      => 'power.package_id',
                                             'cp_game_id'      => 'power.cp_game_id',
                                             'game_id'         => 'power.game_id',
                                             'platform_id'     => 'power.platform_id',
                                             'app_show_id'     => 'power.app_show_id',
                                             'channel_main_id' => 'power.channel_main_id',
                                             'channel_id'      => 'power.channel_id',
                                             'promotion_id'    => 'power.popularize_v2_id',
                                             'department_id'   => 'power.ad_department_id',
                                             'user_id'         => 'power.ad_user_id',
                                         ]);

        $matcher($summaryQb, $params);
        $summaryCols   = $this->ltvCols($params);
        $summaryCols   = array_merge($summaryCols, $this->getLtvTotalCol($params));
        $summaryCols[] = new Fragment("MAX(t_ltv.update_time) as last_update_time");
        $summaryQb->columns($summaryCols);

        @Common::dumpSql($summaryQb->__toString());
        $result['summary'] = $summaryQb->fetchAll();

        return $result;
    }


}
<?php

namespace app\ad_upload\tool;

use Plus\Cache\RedisCache;

/**
 * 交替概率
 * 用于生成按指定概率分布的随机序列，并使用Redis进行存储和管理
 * <AUTHOR>
 */
class AlternatingProbability
{
    /**
     * Redis 实例
     * @var RedisCache
     */
    private $redis;
    /**
     * Redis 键名
     * @var string
     */
    private $key;

    /**
     * 概率
     * @var int
     */
    private $ratio;
    /**
     * 总次数
     * @var int
     */
    private $total;

    /**
     * 构造函数
     * @param RedisCache $redis Redis 实例
     * @param string     $key   Redis
     *                          键名
     * @param float      $ratio 概率
     *                          (0-100)
     * @param int        $total 总次数
     */
    public function __construct($redis, string $key, float $ratio, int $total)
    {
        $this->redis = $redis;
        $this->key   = $key;
        $this->ratio = $ratio;
        $this->total = $total;
    }

    /**
     * 初始化交替序列并存储到 Redis
     * @param float $ratio 概率
     * @param int   $total 总次数
     * @return void
     */
    private function initializeSequence(float $ratio, int $total): void
    {
        $reportNum   = round($total * $ratio / 100);
        $unReportNum = $total - $reportNum;

        // 创建交替的数组
        $rA       = array_fill(0, $reportNum, 1);
        $rB       = array_fill(0, $unReportNum, 0);
        $sequence = $this->crossMergeArray($rA, $rB);

        // 将序列存储到 Redis 列表
        foreach ($sequence as $v) {
            $this->redis->rPush($this->key, $v);
        }
    }

    /**
     * 交替合并两个数组
     * @param array $arr1 数组1
     * @param array $arr2 数组2
     * @return array 交替后的数组
     */
    private function crossMergeArray(array $arr1, array $arr2): array
    {
        $arr1   = array_values($arr1);
        $arr2   = array_values($arr2);
        $count  = max(count($arr1), count($arr2));
        $result = [];
        for ($i = 0; $i < $count; $i++) {
            if ($i < count($arr1)) {
                $result[] = $arr1[$i];
            }
            if ($i < count($arr2)) {
                $result[] = $arr2[$i];
            }
        }
        return $result;
    }

    /**
     * 获取下一个值
     * @return bool
     */
    public function next(): bool
    {
        $nextValue = $this->redis->lPop($this->key);
        // 如果队列为空，重新生成序列
        if ($nextValue === false) {
            $this->initializeSequence($this->ratio, $this->total);
            $nextValue = $this->redis->lPop($this->key);
        }
        return $nextValue == 1;
    }
}

<?php

namespace app\service\OperationData;

use app\apps\internal\Helpers\ConstHub;
use app\apps\operator\Helpers\ConstFirstLogin;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\extension\TableAssistant\Helpers\ColumChanger;
use app\extension\TableAssistant\Helpers\TableBaseHelp;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Components\Helpers\WrapQuery;
use app\service\AdvertiserData\Scheme\BaseScheme;
use app\service\AdvertiserData\Scheme\RealtimePlanScheme;
use app\service\AdvertiserData\Traits\BasicOperator;
use app\service\AdvertiserData\Traits\Converter;
use app\service\OperationData\Components\MatchParams\FirstLoginMatcher;
use app\service\OperationData\Scheme\PackageBaseDailyScheme;
use app\service\OperationData\Scheme\PackageBaseHourlyScheme;
use app\service\OperationData\Scheme\PackageBaseMonthScheme;
use app\util\Common;
use Aura\SqlQuery\Common\SelectInterface;
use Aura\SqlQuery\QueryFactory;

/**
 * @FirstLoginIndex 首登基础指标查询
 *
 */
class FirstLoginIndex
{
    use BasicOperator;


    /**
     * @throws \Exception
     */
    public function listBase(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $rangeDateDimension = (int)Arr::pull($params, 'range_date_dimension', ConstHub::DIMENSION_DAY);

        if (ConstHub::DIMENSION_WEEK === $rangeDateDimension) {
            return $this->listByWeek(...func_get_args());
        }
        elseif (ConstHub::DIMENSION_MONTH === $rangeDateDimension) {
            return $this->listByMonth(...func_get_args());
        }
        elseif (ConstHub::DIMENSION_HOUR === $rangeDateDimension) {
            return $this->listByHourly(...func_get_args());
        }
        else {
            return $this->listByDaily(...func_get_args());
        }
    }


    /**
     * 按天或按周
     *
     * @param array $params
     * @param array $page
     * @param array $groups
     * @param null $sort
     * @param array $columns
     * @param bool $returnSummary
     *
     * @return array
     *
     */
    public function listByDaily(
        array $params = [], array $page = [], array $groups = [],
              $sort = null, array $columns = [], bool $returnSummary = true
    ): array
    {
        $result   = collect();
        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = PackageBaseDailyScheme::NewOne()->select();

        $scheme
            ->joinPowerSql($powerSql)
            ->join((new JoinClause('left', $this->sqlPayPermeationForDaily($params), 't_permeation'))
                       ->on('t_permeation.tday', '=', $scheme::MAIN_TABLE['alias'] . '.tday')
                       ->on('t_permeation.package_id', '=', $scheme::MAIN_TABLE['alias'] . '.package_id'))
            ->scope(TableBaseHelp::setGroup($groups))
            ->scope(TableBaseHelp::setColumn($this->getColsByDaily($params)));

        (new FirstLoginMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        // 备用
        $backupScheme = clone $scheme;

        if (!empty($sort)) {
            $scheme->scope(TableBaseHelp::setOrder(Arr::wrap($sort)));
        }

        if (!empty($page)) {
            $scheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }
        Common::dumpSql((clone $scheme)->toSql());
        $result->put('list', $this->fetchAll($scheme->toSql()));

        if ($returnSummary) {
            // 汇总行处理
            $summaryBaseSql = (clone $backupScheme)->toSql();
            Common::dumpSql($summaryBaseSql);
            $summaryCols     = $this->getColsByDaily($params, true, true);
            $summarySql      = WrapQuery::totalRowQuery($summaryBaseSql, $summaryCols);
            $countSql        = WrapQuery::countRowQuery($summaryBaseSql);
            $totalRow        = $this->fetch($countSql);
            $summaryRow      = $this->fetch($summarySql);
            $lastUpdateTimes = $summaryRow['update_time'] ?? '';

            $result
                ->put('summary', $summaryRow)
                ->put('time', $lastUpdateTimes)
                ->put('total', $totalRow['total'] ?? 0);
        }

        return $result->toArray();
    }

    /**
     * @param array $params
     * @param array $page
     * @param array $groups
     * @param null $sort
     * @param array $columns
     * @param bool $returnSummary
     *
     * @return array
     * @throws \Exception
     */
    public function listByWeek(
        array $params = [], array $page = [], array $groups = [],
              $sort = null, array $columns = [], bool $returnSummary = true
    ): array
    {
        $result = collect();

        // 若选择周期不满足为7天的周期则往后面继续延长到7天为一周
        [
            'begin' => $rangeDateStart,
            'end'   => $rangeDateEnd,
            'cycle' => $rangeDateCycle,
        ] = TimeUtil::divideWeekByRangeDate(
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end')
        );

        $params['range_date_start'] = $rangeDateStart;
        $params['range_date_end']   = $rangeDateEnd;

        $powerSql  = \Plus::$service->admin->getAdminPowerSql();
        $subScheme = PackageBaseDailyScheme::NewOne()->select();
        $subScheme
            ->joinPowerSql($powerSql)
            ->scope(TableBaseHelp::setColumn($this->getSubColsForWeek($rangeDateCycle)));

        (new FirstLoginMatcher($subScheme->fieldReflect()))
            ->setParams($params)
            ->execute($subScheme);

        $mainScheme = new BaseScheme();
        $mainScheme
            ->from($subScheme, 'main_body')
            ->scope(TableBaseHelp::setColumn($this->getColsForWeek()))
            ->scope(TableBaseHelp::setGroup($groups));

        $backupScheme = clone $mainScheme;

        if (!empty($page)) {
            $mainScheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }

        if (!empty($sort)) {
            $mainScheme->scope(TableBaseHelp::setOrder($sort));
        }
        Common::dumpSql((clone $mainScheme)->toSql());
        $result->put('list', $this->fetchAll($mainScheme->toSql()));

        if ($returnSummary) {
            $summaryBaseSql = (clone $backupScheme)->toSql();
            Common::dumpSql($summaryBaseSql);
            $summarySql = WrapQuery::totalRowQuery($summaryBaseSql, $this->getSummaryColsForWeek());
            // 汇总
            $countSql = WrapQuery::countRowQuery($summaryBaseSql);
            $totalRow = $this->fetch($countSql);

            $summaryRow      = $this->fetch($summarySql);
            $lastUpdateTimes = Arr::pull($summaryRow, 'update_time');

            $result
                ->put('summary', $summaryRow)
                ->put('time', $lastUpdateTimes)
                ->put('total', $totalRow['total'] ?? 0);
        }

        return $result->toArray();
    }

    /**
     * 按小时
     *
     * @param array $params
     * @param array $page
     * @param array $groups
     * @param null $sort
     * @param array $columns
     * @param bool $returnSummary
     *
     * @return array
     */
    public function listByHourly(
        array $params = [], array $page = [], array $groups = [],
              $sort = null, array $columns = [], bool $returnSummary = true
    ): array
    {

        $result   = collect();
        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = PackageBaseHourlyScheme::NewOne()->select();

        $scheme
            ->joinPowerSql($powerSql)
            ->scope(TableBaseHelp::setGroup($groups))
            ->scope(TableBaseHelp::setColumn($this->getColsByHourly($params)));

        (new FirstLoginMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        $backupScheme = clone $scheme;

        if (!empty($sort)) {
            $scheme->scope(TableBaseHelp::setOrder($sort));
        }

        if (!empty($page)) {
            $scheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }
        Common::dumpSql((clone $scheme)->toSql());
        $result->put('list', $this->fetchAll($scheme->toSql()));

        if ($returnSummary) {
            // 汇总行处理
            $summaryBaseSql = (clone $backupScheme)->toSql();
            Common::dumpSql($summaryBaseSql);
            $summaryCols     = $this->getColsByHourly($params, true, true);
            $summarySql      = WrapQuery::totalRowQuery($summaryBaseSql, $summaryCols);
            $countSql        = WrapQuery::countRowQuery($summaryBaseSql);
            $totalRow        = $this->fetch($countSql);
            $summaryRow      = $this->fetch($summarySql);
            $lastUpdateTimes = Arr::pull($summaryRow, 'update_time');

            $result
                ->put('summary', $summaryRow)
                ->put('time', $lastUpdateTimes)
                ->put('total', $totalRow['total'] ?? 0);
        }

        return $result->toArray();
    }

    /**
     * 按月
     *
     * @param array $params
     * @param array $page
     * @param array $groups
     * @param       $sort
     * @param array $columns
     * @param       $options
     *
     * @return array
     */
    public function listByMonth(
        array $params = [], array $page = [], array $groups = [],
              $sort = null, array $columns = ['*'], $options = null
    ): array
    {
        $result   = collect();
        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = PackageBaseMonthScheme::NewOne()->select();

        // 日期格式化处理
        [$rangeDateStart, $rangeDateEnd] = [
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end'),
        ];

        foreach ([&$rangeDateStart, &$rangeDateEnd] as &$item) {
            $item = date('Ym', strtotime($item));
        }

        Arr::set($params, 'range_date_start', $rangeDateStart);
        Arr::set($params, 'range_date_end', $rangeDateEnd);

        $infoCol = $this->getColsForMonth($params);

        $scheme
            ->joinPowerSql($powerSql)
            ->joinMonthDataFilterDailyData($infoCol) // 判断月报表最新数据连表
            ->scope(TableBaseHelp::setGroup($groups))
            ->scope(TableBaseHelp::setColumn($infoCol));

        (new FirstLoginMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        $backupScheme = clone $scheme;

        if (!empty($sort)) {
            $scheme->scope(TableBaseHelp::setOrder($sort));
        }

        if (!empty($page)) {
            $scheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }
        Common::dumpSql((clone $scheme)->toSql());

        $result->put('list', $this->fetchAll($scheme->toSql()));

        // 汇总行处理
        $summaryBaseSql = (clone $backupScheme)->toSql();
        Common::dumpSql($summaryBaseSql);
        $summaryCols     = $this->getColsForMonth($params, true, true);
        $summarySql      = WrapQuery::totalRowQuery($summaryBaseSql, $summaryCols);
        $countSql        = WrapQuery::countRowQuery($summaryBaseSql);
        $totalRow        = $this->fetch($countSql);
        $summaryRow      = $this->fetch($summarySql);
        $lastUpdateTimes = Arr::pull($summaryRow, 'update_time');

        $result
            ->put('summary', $summaryRow)
            ->put('time', $lastUpdateTimes)
            ->put('total', $totalRow['total'] ?? 0);

        return $result->toArray();
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return array|false
     */
    public function getInfoByFixedDate(
        array $params = [], array $groups = [], array $columns = []
    )
    {
        $dateDimension = (int)Arr::get($params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        if ($dateDimension === 3) {
            [
                'begin' => $rangeDateStart,
                'end'   => $rangeDateEnd,
            ] = TimeUtil::divideWeekByRangeDate(
                Arr::pull($params, 'range_date_start'),
                Arr::pull($params, 'range_date_end')
            );

            $params['range_date_start'] = $rangeDateStart;
            $params['range_date_end']   = $rangeDateEnd;
        }

        return $this->fetchAll(($this->sqlInfoByFixedDate(...func_get_args()))->toSql());
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return array|false
     */
    public function getSummaryInfoByFixedDate(
        array $params = [], array $groups = [], array $columns = []
    )
    {
        $dateDimension = (int)Arr::get($params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        if ($dateDimension === 3) {
            [
                'begin' => $rangeDateStart,
                'end'   => $rangeDateEnd,
            ] = TimeUtil::divideWeekByRangeDate(
                Arr::pull($params, 'range_date_start'),
                Arr::pull($params, 'range_date_end')
            );

            $params['range_date_start'] = $rangeDateStart;
            $params['range_date_end']   = $rangeDateEnd;
        }

        $baseInfoScheme = $this->sqlInfoByFixedDate(...func_get_args());

        $summaryScheme = (new BaseScheme())
            ->from($baseInfoScheme, 'summary_body')
            ->scope(
                TableBaseHelp::setColumn(ColumChanger::changeArrayForCols([], $columns, true, true))
            )
            ->scope(function (&$query) {
                if (!$query instanceof SelectInterface) return;
                $query->groupBy(['tday']);
            });

        return $this->fetchAll($summaryScheme->toSql());
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return PackageBaseDailyScheme
     */
    protected function sqlInfoByFixedDate(
        array $params = [], array $groups = [], array $columns = []
    ): PackageBaseDailyScheme
    {
        $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias'];

        $groupsMap = [
            'tday'            => $mainTable . '.tday',
            'cp_game_id'      => $mainTable . '.cp_game_id',
            'game_id'         => $mainTable . '.game_id',
            'package_id'      => $mainTable . '.package_id',
            'channel_id'      => 'POWER.channel_id',
            'channel_main_id' => 'POWER.channel_main_id',
            'day_type'        => $mainTable . '.day_type',
            'app_show_id'     => 'POWER.app_show_id',
        ];

        if (empty($columns)) {
            $columns = [
                'tday'            => ['source' => 't_base'],
                'cost_discount'   => ['source' => 't_base', 'aggregate' => 'sum'],
                'cost'            => ['source' => 't_base', 'aggregate' => 'sum'],
                'firstlogin_user' => ['source' => 't_base', 'aggregate' => 'sum'],
                'new_user'        => ['source' => 't_base', 'aggregate' => 'sum'],
                'app_show_id'     => ['source' => 'POWER'],
                'channel_id'      => ['source' => 'POWER'],
                'channel_main_id' => ['source' => 'POWER'],
                'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
                'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
                'platform_id'     => ['source' => 'POWER'],
                'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            ];
        }

        if (!in_array('tday', $groups)) $groups[] = 'tday';

        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = PackageBaseDailyScheme::NewOne()->select();
        $scheme
            ->joinPowerSql($powerSql)
            ->scope(TableBaseHelp::setGroup(TableBaseHelp::changeGroups($groups, $groupsMap)))
            ->scope(TableBaseHelp::setColumn(ColumChanger::changeArrayForCols([], $columns)));

        (new FirstLoginMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        return $scheme;
    }

    /**
     * @param array $params
     *
     * @return string
     */
    protected function sqlPayPermeationForDaily(array $params = []): string
    {
        $rangeDate = array_filter([
                                      $params['range_date_start'],
                                      $params['range_date_end'],
                                  ]);

        sort($rangeDate);

        $rangeDate = array_unique($rangeDate);

        if (count($rangeDate) > 1) {
            $timeRangeString = "tday between '{$rangeDate[0]}' and '{$rangeDate[1]}'";
        }
        else {
            $timeRangeString = "tday = '{$rangeDate[0]}'";
        }

        $grammar    = new QueryFactory('mysql');
        $joinSelect = $grammar
            ->newSelect()
            ->from('ddc_platform.dws_fristlogin_pay_permeation_daily')
            ->where($timeRangeString)
            ->cols(['tday', 'package_id', 'MAX(day_type) as day_type'])
            ->groupBy(['tday', 'package_id']);

        $mainSelect = $grammar
            ->newSelect()
            ->from('ddc_platform.dws_fristlogin_pay_permeation_daily t1')
            ->joinSubSelect(
                'inner',
                $joinSelect->__toString(),
                't2',
                't1.tday = t2.tday and t1.package_id = t2.package_id and t1.day_type = t2.day_type')
            ->cols(['t1.tday', 't1.package_id', 'SUM(pay_user_num) as pay_user_per'])
            ->groupBy(['t1.tday', 't1.package_id']);

        return $mainSelect->__toString();
    }

    /**
     * @param array $params
     * @param bool $isBase
     * @param bool $isTotal
     *
     * @return void
     * @todo Unified field output(to be completed)
     */
    protected function getColumns(array $params = [], bool $isBase = true, bool $isTotal = false)
    {
        $dateDimension = $params['range_date_dimension'] ?? 2;

        if (1 === $dateDimension) {
            $mainTable = PackageBaseHourlyScheme::MAIN_TABLE;
        }
        elseif (4 === $dateDimension) {
            $mainTable = PackageBaseMonthScheme::MAIN_TABLE;
        }
        else {
            $mainTable = PackageBaseDailyScheme::MAIN_TABLE;
        }

        $fixedInfoIndex = [
            'tday'            => ['source' => $mainTable],
            'cp_game_id'      => ['source' => $mainTable],
            'game_id'         => ['source' => $mainTable],
            'app_show_id'     => ['source' => 'POWER'],
            'channel_main_id' => ['source' => 'POWER'],
            'channel_id'      => ['source' => 'POWER'],
            'platform_id'     => ['source' => 'POWER'],
            'package_id'      => ['source' => $mainTable],
            'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
            'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
            'is_multiple'     => ['source' => $mainTable],
        ];

        $calculatedIndex = [
            'activate_device'           => ['source' => $mainTable, 'aggregate' => 'sum'], // 激活设备
            'cost'                      => ['source' => $mainTable, 'aggregate' => 'sum'], // 返点前消耗金额
            'cost_discount'             => ['source' => $mainTable, 'aggregate' => 'sum'], // 返点后消耗金额
            'firstlogin_user'           => ['source' => $mainTable, 'aggregate' => 'sum'], // 首登用户
            'firstlogin_device'         => ['source' => $mainTable, 'aggregate' => 'sum'],//首登设备
            'firstlogin_active_user'    => ['source' => $mainTable, 'aggregate' => 'sum'], //首登活跃用户
            'create_role_new'           => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_role'], // 首登创角
            'firstlogin_role2'          => ['source' => $mainTable, 'aggregate' => 'sum'],
            'firstlogin_real_user'      => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_week'          => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_7days_ago'     => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user_7_days_ago'],
            'active_pay_user'           => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user_pay'],
            'active_user_7_days_ago'    => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_user'                  => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_device'                => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_real_user'             => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_real_user_der'         => ['source' => $mainTable, 'aggregate' => 'sum'],
            'reg_user'                  => ['source' => $mainTable, 'aggregate' => 'sum'],
            'reg_device'                => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user'               => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user'],
            'new_user_imulator'         => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_imulator'      => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_imulator_bind' => ['source' => $mainTable, 'aggregate' => 'sum'],
            'create_role'               => ['source' => $mainTable, 'aggregate' => 'sum'],
            'pay_new_user'              => ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user_new'],
            'pay_money_new'             => ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money_new'],
            'pay_user_newlogin'         => ['source' => 't_payment', 'aggregate' => 'sum'],
            'firstlogin_pay_money'      => ['source' => 't_payment', 'aggregate' => 'sum'],
            'order_count'               => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_money'                 => ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money'],
            'pay_user'                  => ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user'],
            'order_success'             => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_money_newlogin'        => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_money_reg'             => ['source' => 't_payment', 'aggregate' => 'sum'],
            'firstlogin_pay_user'       => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_user_per'              => ['source' => 't_permeation', 'aggregate' => 'sum'],
            'update_time'               => ['source' => $mainTable, 'aggregate' => 'max'],
        ];


    }


    /**
     * @param array $params
     * @param bool $isTop
     * @param bool $isTotal
     *
     * @return array
     */
    protected function getColsByDaily(array $params = [], bool $isTop = false, bool $isTotal = false): array
    {
        $isHasVisualPay = (int)($params['has_visual_pay'] ?? 0);
        $mainTable      = PackageBaseDailyScheme::MAIN_TABLE['alias'];

        $fixInfoIndex = [
            'tday'            => ['source' => $mainTable],
            'cp_game_id'      => ['source' => $mainTable],
            'game_id'         => ['source' => $mainTable],
            'app_show_id'     => ['source' => 'POWER'],
            'channel_main_id' => ['source' => 'POWER'],
            'channel_id'      => ['source' => 'POWER'],
            'platform_id'     => ['source' => 'POWER'],
            'package_id'      => ['source' => $mainTable],
            'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
            'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
            'is_multiple'     => ['source' => $mainTable],
        ];

        $calculatedIndex = [
            'activate_device'           => ['source' => $mainTable, 'aggregate' => 'sum'], // 激活设备
            'cost'                      => ['source' => $mainTable, 'aggregate' => 'sum'], // 返点前消耗金额
            'cost_discount'             => ['source' => $mainTable, 'aggregate' => 'sum'], // 返点后消耗金额
            'firstlogin_user'           => ['source' => $mainTable, 'aggregate' => 'sum'], // 首登用户
            'firstlogin_device'         => ['source' => $mainTable, 'aggregate' => 'sum'],//首登设备
            'firstlogin_active_user'    => ['source' => $mainTable, 'aggregate' => 'sum'], //首登活跃用户
            'firstlogin_pay_user_new'   => ['source' => 't_payment', 'aggregate' => 'sum'], //首登付费新用户
            'firstlogin_pay_user'       => ['source' => 't_payment', 'aggregate' => 'sum'], //首登付费用户
            'create_role_new'           => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_role'], // 首登创角
            'firstlogin_role2'          => ['source' => $mainTable, 'aggregate' => 'sum'],
            'firstlogin_real_user'      => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_week'          => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_7days_ago'     => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user_7_days_ago'],
            'active_pay_user'           => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user_pay'],
            'active_user_7_days_ago'    => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_user'                  => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_device'                => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_real_user'             => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_real_user_der'         => ['source' => $mainTable, 'aggregate' => 'sum'],
            'reg_user'                  => ['source' => $mainTable, 'aggregate' => 'sum'],
            'reg_device'                => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user'               => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user'],
            'new_user_imulator'         => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_imulator'      => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_imulator_bind' => ['source' => $mainTable, 'aggregate' => 'sum'],
            'create_role'               => ['source' => $mainTable, 'aggregate' => 'sum'],
            'pay_user_newlogin'         => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_money_newlogin'        => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_money_reg'             => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_user_per'              => ['source' => 't_permeation', 'aggregate' => 'sum'],
            'update_time'               => ['source' => $mainTable, 'aggregate' => 'max'],
        ];

        if ($isHasVisualPay) {
            $calculatedIndex['pay_money']     = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'pay_money_all'];
            $calculatedIndex['pay_user']      = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'pay_user_all'];
            $calculatedIndex['pay_new_user']  = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user_new_all'];
            $calculatedIndex['pay_money_new'] = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money_new_all'];

            $calculatedIndex['order_count']   = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'order_count_all'];
            $calculatedIndex['order_success'] = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'order_success_all'];
        }
        else {
            $calculatedIndex['pay_money']     = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'pay_money'];
            $calculatedIndex['pay_user']      = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'pay_user'];
            $calculatedIndex['pay_new_user']  = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user_new'];
            $calculatedIndex['pay_money_new'] = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money_new'];

            $calculatedIndex['order_count']   = ['source' => 't_payment', 'aggregate' => 'sum'];
            $calculatedIndex['order_success'] = ['source' => 't_payment', 'aggregate' => 'sum'];
        }

        $result  = [];
        $collect = collect();

        if (!$isTotal) {
            $collect = $collect->merge($fixInfoIndex);
        }
        $collect = $collect->merge($calculatedIndex);

        $collect->each(function (&$item, $key) use (&$result, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];

                if ($aggregate == 'sum') {
                    $format = "IFNULL({$aggregate}(%s), 0)";
                }
                else {
                    $format = "{$aggregate}(%s)";
                }
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }

            $field .= $item['source_field'] ?? $key;

            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }

    /**
     * @param array $params
     * @param bool $isTop
     * @param bool $isTotal
     *
     * @return array
     */
    protected function getColsByHourly(array $params = [], bool $isTop = false, bool $isTotal = false): array
    {
        $isHasVisualPay = (int)($params['has_visual_pay'] ?? 0);
        $mainTable      = PackageBaseHourlyScheme::MAIN_TABLE['alias'];

        $fixInfoIndex = [
            'tday'            => ['source' => $mainTable],
            'thour'           => ['source' => $mainTable],
            'cp_game_id'      => ['source' => $mainTable],
            'game_id'         => ['source' => $mainTable],
            'app_show_id'     => ['source' => 'POWER'],
            'channel_main_id' => ['source' => 'POWER'],
            'channel_id'      => ['source' => 'POWER'],
            'platform_id'     => ['source' => 'POWER'],
            'package_id'      => ['source' => $mainTable],
            'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
            'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
            'is_multiple'     => ['source' => $mainTable],
        ];

        $calculatedIndex = [
            'activate_device'           => ['source' => $mainTable, 'aggregate' => 'sum'], // 激活设备
            'cost'                      => ['source' => $mainTable, 'aggregate' => 'sum'], // 返点前消耗金额
            'cost_discount'             => ['source' => $mainTable, 'aggregate' => 'sum'], // 返点后消耗金额
            'firstlogin_user'           => ['source' => $mainTable, 'aggregate' => 'sum'], // 首登用户
            'firstlogin_device'         => ['source' => $mainTable, 'aggregate' => 'sum'],//首登设备
            'firstlogin_active_user'    => ['source' => $mainTable, 'aggregate' => 'sum'], //首登活跃用户
            'create_role_new'           => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_role'], // 首登创角
            'firstlogin_role2'          => ['source' => $mainTable, 'aggregate' => 'sum'],
            'firstlogin_real_user'      => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_week'          => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_7days_ago'     => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user_7_days_ago'],
            'active_pay_user'           => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user_pay'], // active_pay_user
            'active_user_7_days_ago'    => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_user'                  => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_device'                => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_real_user'             => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_real_user_der'         => ['source' => $mainTable, 'aggregate' => 'sum'],
            'reg_user'                  => ['source' => $mainTable, 'aggregate' => 'sum'],
            'reg_device'                => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user'               => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user'],
            'new_user_imulator'         => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_imulator'      => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user_imulator_bind' => ['source' => $mainTable, 'aggregate' => 'sum'],
            'create_role'               => ['source' => $mainTable, 'aggregate' => 'sum'],
            'pay_user_newlogin'         => ['source' => 't_payment', 'aggregate' => 'sum'],
            'firstlogin_pay_money'      => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_money_newlogin'        => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_money_reg'             => ['source' => 't_payment', 'aggregate' => 'sum'],
            'firstlogin_pay_user_new'   => ['source' => 't_payment', 'aggregate' => 'sum'], //首登付费新用户
            'firstlogin_pay_user'       => ['source' => 't_payment', 'aggregate' => 'sum'], //首登付费用户
            'pay_money_all_with_hour'   => ['source' => 't_payment', 'aggregate' => 'sum'],
            'update_time'               => ['source' => $mainTable, 'aggregate' => 'max'],
        ];

        if ($isHasVisualPay) {
            $calculatedIndex['pay_money']     = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money_all'];
            $calculatedIndex['pay_user']      = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user_all'];
            $calculatedIndex['pay_new_user']  = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user_new_all'];
            $calculatedIndex['pay_money_new'] = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money_new_all'];
            $calculatedIndex['order_count']   = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'order_count_all'];
            $calculatedIndex['order_success'] = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'order_success_all'];
        }
        else {
            $calculatedIndex['pay_money']     = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money'];
            $calculatedIndex['pay_user']      = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user'];
            $calculatedIndex['pay_new_user']  = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user_new'];
            $calculatedIndex['pay_money_new'] = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money_new'];
            $calculatedIndex['order_count']   = ['source' => 't_payment', 'aggregate' => 'sum'];
            $calculatedIndex['order_success'] = ['source' => 't_payment', 'aggregate' => 'sum'];
        }

        $result  = [];
        $collect = collect();

        if (!$isTotal) {
            $collect = $collect->merge($fixInfoIndex);
        }
        $collect = $collect->merge($calculatedIndex);

        $collect->each(function (&$item, $key) use (&$result, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];

                if ($aggregate == 'sum') {
                    $format = "IFNULL({$aggregate}(%s), 0)";
                }
                else {
                    $format = "{$aggregate}(%s)";
                }
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }

            $field .= $item['source_field'] ?? $key;

            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }

    /**
     * @param array $dateCycle
     *
     * @return array
     */
    protected function getSubColsForWeek(array $dateCycle = []): array
    {
        $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias'];
        $tDayCols  = [];

        foreach ($dateCycle as $item) {
            ['begin_date' => $begin, 'end_date' => $end] = $item;
            $tDayCols[] = sprintf(
                "when {$mainTable}.tday between '%s' and '%s' then '%s'",
                $begin, $end, $begin . '/' . $end
            );
        }
        $caseString = implode(' ', $tDayCols);
        $tDayString = sprintf(" CONCAT(case %s end) as tday ", $caseString);

        return [
            $tDayString,
            $mainTable . '.cp_game_id',
            $mainTable . '.game_id',
            $mainTable . '.package_id',
            "CONCAT_WS('|', {$mainTable}.tday, active_user_week)             as active_user_week",
            "CONCAT_WS('|', {$mainTable}.tday, firstlogin_active_user_week)  as firstlogin_active_user_week",
            "CONCAT_WS('|', {$mainTable}.tday, pay_user_all_week)            as pay_user_all_week",
            "CONCAT_WS('|', {$mainTable}.tday, pay_user_week)                as pay_user_week",
            "CONCAT_WS('|', {$mainTable}.tday, firstlogin_pay_user_all_week) as firstlogin_pay_user_all_week",
            "CONCAT_WS('|', {$mainTable}.tday, firstlogin_pay_user_week)     as firstlogin_pay_user_week",
            'firstlogin_pay_money as firstlogin_pay_money',
            'firstlogin_pay_user_new',
            'firstlogin_pay_user_new_all',
            'firstlogin_pay_user',
            'firstlogin_pay_user_new as pay_new_user',
            'firstlogin_pay_money_new as pay_money_new',
            'firstlogin_pay_money_all as pay_money_all',
            'firstlogin_pay_user_new_all as pay_new_user_all',
            'firstlogin_pay_money_new_all as pay_money_new_all',
            'firstlogin_pay_user as pay_user',
            'firstlogin_pay_user_all as pay_user_all',
            'order_count_all as order_count_all',
            'pay_money_all as pay_money_all',
            'order_success_all as order_success_all',
            'pay_money_newlogin_all as pay_money_newlogin_all',
            'pay_user_newlogin_all as pay_user_newlogin_all',
            'pay_money_reg_all as pay_money_reg_all',
            'order_count as order_count',
            'order_success as order_success',
            'pay_money_reg as pay_money_reg',
            "{$mainTable}.is_multiple",
            'activate_device as activate_device',
            'cost',
            'cost_discount',
            'firstlogin_user',
            'firstlogin_device',
            'firstlogin_active_user',
            'firstlogin_role',
            'firstlogin_role2',
            'firstlogin_real_user',
            'firstlogin_active_user_7_days_ago',
            'firstlogin_active_user_pay',
            'active_user_7_days_ago',
            'create_role',
            'new_user',
            'new_device',
            'new_real_user',
            'new_real_user_der',
            'reg_user',
            'reg_device',
            'active_user_imulator',
            'active_user_imulator_bind',
            'POWER.channel_id',
            'POWER.channel_main_id',
            'POWER.ad_department_id as department_id',
            'POWER.ad_user_id as user_id',
            'POWER.popularize_v2_id as promotion_id',
            'POWER.app_show_id',
            'POWER.platform_id',
            "{$mainTable}.update_time",
        ];
    }

    /**
     * @param array $params
     *
     * @return string[]
     */
    protected function getColsForWeek(array $params = []): array
    {
        $isHasVisualPay = (int)($params['has_visual_pay'] ?? 0);

        $fields = [
            'tday', 'cp_game_id',
            'game_id', 'package_id',
            'channel_id as channel_id',
            'channel_main_id as channel_main_id',
            'department_id', 'user_id',
            'promotion_id', 'app_show_id',
            'platform_id',
            'SUM(activate_device) as activate_device',
            'SUM(cost) as cost',
            'SUM(cost_discount) as cost_discount',
            'SUM(firstlogin_device) as firstlogin_device',
            'SUM(firstlogin_role) as create_role_new',
            'SUM(firstlogin_role2) as firstlogin_role2',
            'SUM(firstlogin_real_user) as firstlogin_real_user',
            'SUM(firstlogin_active_user_7_days_ago) as active_user_7days_ago',
            'SUM(firstlogin_active_user_pay) as active_pay_user',
            'SUM(active_user_7_days_ago)    as active_user_7_days_ago',
            'SUM(create_role) as create_role',
            'SUM(new_user)                  as new_user',
            'SUM(new_device)                as new_device',
            'SUM(new_real_user)             as new_real_user',
            'SUM(new_real_user_der)         as new_real_user_der',
            'SUM(reg_user)                  as reg_user',
            'SUM(reg_device)                as reg_device',
            'SUM(active_user_imulator)      as active_user_imulator',
            'SUM(active_user_imulator_bind) as active_user_imulator_bind',
            'SUM(is_multiple)               as is_multiple',
            'SUM(firstlogin_user)           as firstlogin_user',
            "SUM(IF(SUBSTRING_INDEX(tday, '/', 1) = SUBSTRING_INDEX(active_user_week, '|', 1), SUBSTRING_INDEX(SUBSTRING_INDEX(active_user_week, '|', -1), '|', 1), 0)) as active_user",
            "SUM(IF(SUBSTRING_INDEX(tday, '/', 1) = SUBSTRING_INDEX(firstlogin_active_user_week, '|', 1),SUBSTRING_INDEX(SUBSTRING_INDEX(firstlogin_active_user_week, '|', -1), '|', 1), 0)) as firstlogin_active_user",
            "SUM(IF(SUBSTRING_INDEX(tday, '/', 1) = SUBSTRING_INDEX(firstlogin_pay_user_week, '|', 1),SUBSTRING_INDEX(SUBSTRING_INDEX(firstlogin_pay_user_week, '|', -1), '|', 1),0)) as pay_user",
            // "SUM(IF(SUBSTRING_INDEX(tday, '|', 1) = SUBSTRING_INDEX(pay_user_week, '|', 1), SUBSTRING_INDEX(SUBSTRING_INDEX(pay_user_week, '|', -1), '|', 1), 0)) as pay_user",
            "max(update_time) as update_time",
        ];

        if ($isHasVisualPay) {
            $fields = array_merge($fields, [
                'SUM(pay_new_user_all) as pay_new_user',
                'SUM(firstlogin_pay_money_all) as pay_money',
                'SUM(pay_money_new_all) as pay_money_new',
                'SUM(pay_money_new_all) as pay_money_new',
                'SUM(order_count_all) as order_count',
                'SUM(order_success_all) as order_success',
                'SUM(pay_money_reg_all) as pay_money_reg',
            ]);
        }
        else {
            $fields = array_merge($fields, [
                'SUM(pay_new_user) as pay_new_user',
                'SUM(firstlogin_pay_money) as pay_money',
                'SUM(pay_money_new) as pay_money_new',
                'SUM(pay_money_new) as pay_money_new',
                'SUM(order_count) as order_count',
                'SUM(order_success) as order_success',
                'SUM(pay_money_reg) as pay_money_reg',
            ]);
        }

        return $fields;
    }

    /**
     * @return string[]
     */
    protected function getSummaryColsForWeek(): array
    {
        return [
            'SUM(activate_device) as activate_device',
            'SUM(cost) as cost',
            'SUM(cost_discount) as cost_discount',
            'SUM(firstlogin_device) as firstlogin_device',
            'SUM(create_role_new) as create_role_new',
            'SUM(firstlogin_role2) as firstlogin_role2',
            'SUM(firstlogin_real_user) as firstlogin_real_user',
            'SUM(active_user_7days_ago) as active_user_7days_ago',
            'SUM(active_pay_user) as active_pay_user',
            'SUM(active_user_7_days_ago)    as active_user_7_days_ago',
            'SUM(firstlogin_user)           as new_user',
            'SUM(new_device)                as new_device',
            'SUM(new_real_user)             as new_real_user',
            'SUM(new_real_user_der)         as new_real_user_der',
            'SUM(reg_user)                  as reg_user',
            'SUM(active_user_imulator)      as active_user_imulator',
            'SUM(active_user_imulator_bind) as active_user_imulator_bind',
            'SUM(is_multiple)               as is_multiple',
            'SUM(firstlogin_user)           as firstlogin_user',
            "SUM(active_user) as active_user",
            "SUM(firstlogin_active_user) as firstlogin_active_user",
            "SUM(pay_user) as pay_user",
            "SUM(pay_new_user) as pay_new_user",
            "SUM(pay_money_new) as pay_money_new",
            "SUM(order_count) as order_count",
            "SUM(pay_money) as pay_money",
            "SUM(order_success) as order_success",
            "SUM(pay_money_reg) as pay_money_reg",
            'SUM(create_role) as create_role',
            "max(update_time) as update_time",
        ];
    }

    /**
     * @param array $params
     * @param bool $isTop
     * @param bool $isTotal
     *
     * @return array
     */
    public function getColsForMonth(array $params = [], bool $isTop = false, bool $isTotal = false): array
    {
        $mainTable      = PackageBaseMonthScheme::MAIN_TABLE['alias'];
        $isHasVisualPay = (int)($params['has_visual_pay'] ?? 0);

        $fixInfoIndex = [
            'tday'            => ['raw' => "DATE_FORMAT(STR_TO_DATE({$mainTable}.tday, '%Y%m'), '%Y-%m') as tday"],
            'cp_game_id'      => ['source' => $mainTable],
            'game_id'         => ['source' => $mainTable],
            'app_show_id'     => ['source' => 'POWER'],
            'channel_main_id' => ['source' => 'POWER'],
            'channel_id'      => ['source' => 'POWER'],
            'platform_id'     => ['source' => 'POWER'],
            'package_id'      => ['source' => $mainTable],
            'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
            'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
            'is_multiple'     => ['source' => $mainTable],
        ];

        $calculatedIndex = [
            'activate_device'        => ['source' => $mainTable, 'aggregate' => 'sum'],
            'cost'                   => ['source' => $mainTable, 'aggregate' => 'sum'],
            'cost_discount'          => ['source' => $mainTable, 'aggregate' => 'sum'],
            'create_role_new'        => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_role'], // 首登创角
            'firstlogin_role2'       => ['source' => $mainTable, 'aggregate' => 'sum'],
            'firstlogin_user'        => ['source' => $mainTable, 'aggregate' => 'sum'],
            'firstlogin_device'      => ['source' => $mainTable, 'aggregate' => 'sum'],
            'firstlogin_real_user'   => ['source' => $mainTable, 'aggregate' => 'sum'],
            'active_user'            => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'active_user_month'],
            'new_user'               => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_device'             => ['source' => $mainTable, 'aggregate' => 'sum'],
            'reg_user'               => ['source' => $mainTable, 'aggregate' => 'sum'],
            'reg_device'             => ['source' => $mainTable, 'aggregate' => 'sum'],
            'update_time'            => ['source' => $mainTable, 'aggregate' => 'max'],
            'firstlogin_active_user' => ['source' => $mainTable, 'aggregate' => 'sum', 'source_field' => 'firstlogin_active_user_month'],
            'create_role'            => ['source' => $mainTable, 'aggregate' => 'sum'],
            'pay_user_newlogin'      => ['source' => 't_payment', 'aggregate' => 'sum'],

            'pay_money_newlogin'      => ['source' => 't_payment', 'aggregate' => 'sum'],
            'pay_money_reg'           => ['source' => 't_payment', 'aggregate' => 'sum'],
            'firstlogin_pay_user_new' => ['source' => 't_payment', 'aggregate' => 'sum'], //首登付费新用户
            'firstlogin_pay_user'     => ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user_month'], //首登付费用户
        ];

        if ($isHasVisualPay) {
            $calculatedIndex['pay_money']     = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'pay_money_all'];
            $calculatedIndex['pay_user']      = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'pay_user_all_month'];
            $calculatedIndex['pay_new_user']  = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user_new_all'];
            $calculatedIndex['pay_money_new'] = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money_new_all'];

            $calculatedIndex['order_count']   = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'order_count_all'];
            $calculatedIndex['order_success'] = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'order_success_all'];
        }
        else {
            $calculatedIndex['pay_money']     = ['source' => 't_payment', 'aggregate' => 'sum'];
            $calculatedIndex['pay_user']      = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'pay_user_month'];
            $calculatedIndex['pay_new_user']  = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_user_new'];
            $calculatedIndex['pay_money_new'] = ['source' => 't_payment', 'aggregate' => 'sum', 'source_field' => 'firstlogin_pay_money_new'];

            $calculatedIndex['order_count']   = ['source' => 't_payment', 'aggregate' => 'sum'];
            $calculatedIndex['order_success'] = ['source' => 't_payment', 'aggregate' => 'sum'];
        }

        // 非汇总行时，查询月度报表时减去最新max_data_day_ago天实时报表
        if (!$isTop && $params['range_date_dimension'] == 4) {
            $calculatedIndex = ColumChanger::ltvRoiLoginMonth($calculatedIndex, $params['max_data_day_ago'], $params['range_date_end']);
        }

        $result  = [];
        $collect = collect();

        if (!$isTotal) {
            $collect = $collect->merge($fixInfoIndex);
        }
        $collect = $collect->merge($calculatedIndex);

        $collect->each(function (&$item, $key) use (&$result, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];

                if ($aggregate == 'sum') {
                    $format = "IFNULL({$aggregate}(%s), 0)";
                }
                else {
                    $format = "{$aggregate}(%s)";
                }
            }
            elseif (isset($item['custom'])) {
                $format = "{$item['custom']}";
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }

            $field .= $item['source_field'] ?? $key;

            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }

    /**
     * @param string $mainTable
     *
     * @return string[]
     */
    protected function groupsReflect(string $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias']): array
    {
        return [
            'tday'            => $mainTable . '.tday',
            'cp_game_id'      => $mainTable . '.cp_game_id',
            'game_id'         => $mainTable . '.game_id',
            'package_id'      => $mainTable . '.package_id',
            'day_type'        => $mainTable . '.day_type',
            'channel_id'      => 'POWER.channel_id',
            'channel_main_id' => 'POWER.channel_main_id',
            'app_show_id'     => 'POWER.app_show_id',
        ];
    }


    private function fetchAll($sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    private function fetch($sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * @param array $params
     * @param array $page
     * @param array $group
     * @param       $sort
     * @param array $column
     * @param bool $returnSummary
     *
     * @return array
     */
    public function simpleListByDay(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $groupString = '';
        $whereString = '';
        $table       = 'ddc_platform.dws_package_base_daily';
        $wheres      = [];
        $columns     = [
            'tday',
            'channel_main_id',
            'sum(cost) as cost',
            'sum(cost_discount) as cost_discount',
            'sum(firstlogin_user) as new_user',
            '0 as click',
            'sum(`activate_device`) as activate_device',
            'sum(`firstlogin_role`) as create_role',
            'sum(`firstlogin_active_user`) as active_user',
        ];

        $columnString = implode(',', $columns);

        [
            $dateStart, $dateEnd,
        ] = [$params['range_date_start'], $params['range_date_end']];

        $wheres[] = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = Arr::wrap(explode(',', $params['cp_game_id']));
            $cpGameId = implode(',', $cpGameId);
            $wheres[] = "t_base.cp_game_id in ({$cpGameId})";
        }

        if (!empty($wheres)) {
            $whereString = ' WHERE ' . implode(' and ', $wheres);
        }

        if (!empty($group)) {
            $groupString = ' group by ' . implode(', ', $group);
        }

        $sql        = "SELECT {$columnString} FROM {$table} t_base join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString}   {$groupString} order by tday";
        $summarySql = "SELECT {$columnString} FROM {$table} t_base join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString}";
        $list       = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $summaryRow = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => $summaryRow,
        ];
    }

    public function simplePayListByDay(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $groupString = '';
        $whereString = '';
        $table       = 'ddc_platform.dws_package_payment_daily';
        $wheres      = [];
        $columns     = [
            'tday',
            'channel_main_id',
            'sum(`firstlogin_pay_user_new_all`) as pay_user',
        ];

        $columnString = implode(',', $columns);

        [
            $dateStart, $dateEnd,
        ] = [$params['range_date_start'], $params['range_date_end']];

        $wheres[] = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = Arr::wrap(explode(',', $params['cp_game_id']));
            $cpGameId = implode(',', $cpGameId);
            $wheres[] = "t_base.cp_game_id in ({$cpGameId})";
        }

        if (!empty($wheres)) {
            $whereString = ' WHERE ' . implode(' and ', $wheres);
        }

        if (!empty($group)) {
            $groupString = ' group by ' . implode(', ', $group);
        }

        $sql        = "SELECT {$columnString} FROM {$table} t_base join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString}   {$groupString} order by tday";
        $summarySql = "SELECT {$columnString} FROM {$table} t_base join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString}";
        $list       = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $summaryRow = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => $summaryRow,
        ];
    }

    public function simpleListByWeek(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $table = 'ddc_platform.dws_package_base_daily';

        [
            'begin' => $dateStart,
            'end'   => $dateEnd,
            'cycle' => $dateCycle,
        ] = TimeUtil::divideWeekByRangeDate(
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end')
        );
        $params['range_date_start'] = $dateStart;
        $params['range_date_end']   = $dateEnd;
        $fieldForTDay               = $this->getFieldForTDay(
            ConstHub::DIMENSION_WEEK, 't_base', $dateCycle
        );
        $columns                    = [
            $fieldForTDay,
            'cp_game_id',
            'cost_discount',
            'cost',
            'firstlogin_user',
        ];

        $columnString = \implode(',', $columns);
        $wheres       = [];
        $wheres[]     = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = $params['cp_game_id'];
            if (str_contains($cpGameId, ',')) {
                $wheres[] = "cp_game_id in ({$cpGameId})";
            }
            else {
                $wheres[] = "cp_game_id = '{$cpGameId}'";
            }
        }

        $whereString = ' WHERE ' . implode(' and ', $wheres);

        if (!empty($group)) {
            $groupString = ' GROUP BY ' . implode(',', $group);
        }

        $subSql = "SELECT 
            {$columnString}
            FROM {$table} t_base
            $whereString";

        $sql = "
        SELECT 
            tday , sum(cost) as cost, sum(cost_discount) as cost_discount,sum(firstlogin_user) as new_user
        FROM ({$subSql}) as main_body
        {$groupString} order by tday
        ";

        $result         = [];
        $result['list'] = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $summarySql = "
        SELECT 
            tday , sum(cost) as cost, sum(cost_discount) as cost_discount,sum(firstlogin_user) as new_user
        FROM ({$subSql}) as main_body";

        $summaryRow         = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);
        $summaryRow['tday'] = '汇总';
        $result['summary']  = $summaryRow;

        return $result;
    }


    public function simpleListByMonth(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $table        = 'ddc_platform.dws_package_base_daily';
        $fieldForTDay = $this->getFieldForTDay(
            ConstHub::DIMENSION_MONTH, 't_base'
        );

        [
            $dateStart, $dateEnd,
        ] = [$params['range_date_start'], $params['range_date_end']];

        $columns = [
            $fieldForTDay,
            'cp_game_id',
            'cost_discount',
            'cost',
            'firstlogin_user',
        ];

        $columnString = \implode(',', $columns);
        $wheres       = [];
        $wheres[]     = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = $params['cp_game_id'];
            if (str_contains($cpGameId, ',')) {
                $wheres[] = "cp_game_id in ({$cpGameId})";
            }
            else {
                $wheres[] = "cp_game_id = '{$cpGameId}'";
            }
        }

        $whereString = ' WHERE ' . implode(' and ', $wheres);

        if (!empty($group)) {
            $groupString = ' GROUP BY ' . implode(',', $group);
        }

        $subSql = "SELECT 
            {$columnString}
            FROM {$table} t_base
            $whereString";

        $sql            = "
        SELECT 
            tday , sum(cost) as cost, sum(cost_discount) as cost_discount,sum(firstlogin_user) as new_user
        FROM ({$subSql}) as main_body
        {$groupString} order by tday
        ";
        $result         = [];
        $result['list'] = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $summarySql = "
        SELECT 
            tday , sum(cost) as cost, sum(cost_discount) as cost_discount,sum(firstlogin_user) as new_user
        FROM ({$subSql}) as main_body";

        $summaryRow         = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);
        $summaryRow['tday'] = '汇总';
        $result['summary']  = $summaryRow;

        return $result;
    }

}
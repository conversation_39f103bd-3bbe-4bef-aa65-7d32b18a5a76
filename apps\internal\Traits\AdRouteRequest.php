<?php

namespace app\apps\internal\Traits;

use app\extension\Support\Collections\Collection;

trait AdRouteRequest
{

    protected function baseParams(): Collection
    {
        $today = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start', 'default' => $today], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
            ['field' => 'cp_game_id'], // 游戏原名
            ['field' => 'game_id'], // 游戏统计名
            ['field' => 'app_show_id'], // 游戏前端名
            ['field' => 'channel_main_id'], // 主渠道
            ['field' => 'channel_id'], //渠道
            ['field' => 'promotion_channel_id'], // 推广渠道
            ['field' => 'platform_id'], // 客户端ID
            ['field' => 'package_id'], // 包号
            ['field' => 'promotion_id'], // 推广分类
            ['field' => 'department_id'], // 部门
            ['field' => 'user_id'], // 投放人
            ['field' => 'ad_account'], // 投放账号
            ['field' => 'advertiser_id'], // 广告账号ID
            ['field' => 'campaign_id'], // 广告组ID
            ['field' => 'campaign_name'], // 广告组名称
            ['field' => 'plan_name'], // 计划名
            ['field' => 'plan_id'], // 计划ID
            ['field' => 'creative_name'], // 创意名
            ['field' => 'creative_id'], // 创意ID
            ['field' => 'data_range'], // 数据范围
            ['field' => 'is_has_natural', 'default' => 1], // 是否含自然量
            ['field' => 'is_has_appointment', 'default' => 1], // 是否包含预约数据
            ['field' => 'account_id'], // 账号ID搜索
            ['field' => 'data_scope'], // 数据范围搜索
            ['field' => 'tab'], // 数据范围搜索
            ['field' => 'channel_id_tags'], // 渠道标签
            ['field' => 'channel_main_id_tags'], // 主渠道标签
            ['field' => 'package_id_tags'], // 包号标签
            ['field' => 'game_id_tags'], // 包号标签
            ['field' => 'user_os'], // 包号标签
            ['field' => 'mode_test'], // 包号标签

            ['field' => 'page_size', 'default' => 20],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order',],
            ['field' => 'sort',],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

}
<?php

namespace app\logic\adconf;

use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\Zakia;
use app\service\AdConf\SvlinkServ;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;

class SvlinkManagerLogic
{
    use ColumnsInteract;

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     *
     * @return array
     * @throws \Exception
     */
    public function getInfo(
        array $params = [], array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $re          = (new SvlinkServ())->getInfo(...func_get_args());
        $list        = &$re['list'];
        $generalServ = new GeneralOptionServ();

        $constMap = (new BasicServ())
            ->getMultiOptions(['game_id', 'cp_game_id:all'])
            ->put('channel_id', $generalServ->listChannelOptions())
            ->put(
                'user_id',
                collect(Arr::crossMap($generalServ->getAllUserList(), ['id' => 'key', 'real_name' => 'val']))
            );

        $replaceFn = $this->replaceColumnDefine($constMap);

        foreach ($list as &$foo) {
            $replaceFn($foo);
        }

        return $re;
    }


    /**
     * @param $list
     *
     * @return mixed
     * @throws \Throwable
     */
    public function multiInsert($list)
    {
        return (new SvlinkServ())->multiInsert($list);
    }

    /**
     * @param $id
     *
     * @return mixed
     * @throws \Throwable
     */
    public function deleteById($id)
    {
        return (new SvlinkServ())->deleteById($id);
    }

}
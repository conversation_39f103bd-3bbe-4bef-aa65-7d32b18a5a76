<?php

namespace app\apps\general\controllers;

use app\apps\internal\Traits\InternalRoutes;
use app\extension\FakeDB\FakeDB;
use app\service\General\ExportManagerServ;
use PHP_CodeSniffer\Reports\Csv;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Exception;
use Plus\MVC\Controller\JsonController;

/**
 *  导出管理
 *
 * @route /general/export-manager/*
 */
class ExportManagerController extends JsonController
{
    use InternalRoutes;

    private const QueueTopic = 'report_export';

    /**
     * 导出Excel报表
     *
     * @route /general/export-manager/export-excel
     * @return array
     */
    public function exportExcelAction(): array
    {
        $params        = $this->wrapParams($this->request);
        $requestURI    = $params['request_uri'] ?? null;
        $requestParams = $params['request_params'] ?? null;
        $requestToken  = $_SERVER['HTTP_ACCESS_TOKEN'] ?? '';
        $operatorId    = \Plus::$service->admin->getUserId();
        $fields        = $params['fields'] ?? null;
        $httpHost      = $_SERVER['HTTP_HOST'] ?? '';
        $requestSchema = !empty($_SERVER['REQUEST_SCHEME']) ? ($_SERVER['REQUEST_SCHEME'] . '://') : '';

        if (empty($requestURI) || empty($requestParams)) {
            throw new \RuntimeException("非法导出操作, 缺少重要参数");
        }

        if (is_string($requestParams)) {
            $requestParams = \json_decode($requestParams, true);
        }

        $jobOptions = [
            'user_id'      => $operatorId,
            'request_body' => [
                'request_uri'  => $requestSchema . $httpHost . $requestURI,
                'params'       => $requestParams,
                'access_token' => $requestToken,
            ],
        ];
        
        if (!empty($fields)) {
            $jobOptions['request_body']['fields'] = $fields;
        }

        $re = (new ExportManagerServ())->createExportJob($jobOptions);

        if ($re['code'] !== 200) {
            return $this->error($re['message']);
        }
        else {
            return $this->success([]);
        }
    }

    /**
     * 获取导出状态
     *
     * @return array
     * @throws \RedisException
     */
    public function getExportJobsAction(): array
    {

        // 记录开始时间
        $start_time = microtime(true);
        $userId = \Plus::$service->admin->getUserId();
        $data   = (new ExportManagerServ())->getExportJob(['user_id' => $userId]);

        // 记录结束时间
        $end_time = microtime(true);
        // 计算并输出执行时间
        \Plus::$app->log->info(["message"=>"导出列表查询执行时间： " . ($end_time - $start_time) . " 秒"], [], 'index');


        $fields = [
            ['title' => '序号', 'dataIndex' => 'id'],
            ['title' => '状态', 'dataIndex' => 'job_status'],
            ['title' => '导出时间', 'dataIndex' => 'create_at'],
            ['title' => '进度', 'dataIndex' => 'progress'],
        ];

        $list = &$data['list'];
        foreach ($list as &$foo) {
            $jobStatus = $foo['job_status'];
            if ($jobStatus == 0) {
                $foo['job_status'] = '创建成功';
            }
            elseif ($jobStatus == 1) {
                $foo['job_status'] = '排队中';
            }
            elseif ($jobStatus == 2) {
                $foo['job_status'] = '处理中';
            }
            elseif ($jobStatus == 3) {
                $foo['job_status'] = '导出完成';
            }
            else {
                $foo['job_status'] = '未知异常';
            }
            unset($foo['file_path'], $foo['request_body']);
        }

        return $this->success(array_merge(['fields' => $fields], $data));
    }

    /**
     * 下载excel
     *
     * @return array|void
     * @throws Exception
     * @throws \RedisException
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function downloadAction()
    {
        ini_set('memory_limit', '2048M');
        $params = $this->wrapParams($this->request);
        $userId = \Plus::$service->admin->getUserId();
        $id     = $params->get('id');
        $data   = (new ExportManagerServ())->getExportJob(['user_id' => $userId, 'id' => $id]);
        $job    = $data['list'][0] ?? [];

        if (empty($job) || empty($job['file_path'])) {
            return $this->error('No corresponding file found');
        }

        $filePath = $job['file_path'];

        $type     = 'Csv';
        $fileName = basename($filePath);

        header("Content-Type:application/csv");
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition:attachment;filename={$fileName}");

        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
        $reader->setInputEncoding('GBK');

        $sp     = $reader->load($filePath);
        $writer = IOFactory::createWriter($sp, IOFactory::WRITER_CSV);

        $writer->save("php://output");//数据流
        exit();
    }

}
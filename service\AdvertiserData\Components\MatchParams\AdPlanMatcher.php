<?php

namespace app\service\AdvertiserData\Components\MatchParams;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Macroable\Traits\Macroable;
use app\models\AdpPlatform\TbAdpPlanBase;
use app\service\AdvertiserData\Components\Helpers\Convert;
use app\service\AdvertiserData\Components\MatchParams\Contracts\MatcherContract;
use app\service\AdvertiserData\Components\MatchParams\Traits\CampaignMatch;
use app\service\AdvertiserData\Components\MatchParams\Traits\GeneralMatch;
use app\service\AdvertiserData\Components\MatchParams\Traits\GroupConvertable;
use app\service\AdvertiserData\Components\MatchParams\Traits\Matchable;
use app\service\AdvertiserData\Components\MatchParams\Traits\PlanMatch;
use app\service\AdvertiserData\Contracts\GroupConvertContract;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Traits\Converter;
use app\service\General\BizTagsServ;
use app\service\General\Matcher\Traits\TagsMatcher;
use Aura\SqlQuery\Common\InsertInterface;
use Aura\SqlQuery\Common\SelectInterface;

class AdPlanMatcher extends Matcher implements MatcherContract, GroupConvertContract
{
    use Matchable, GeneralMatch, CampaignMatch, PlanMatch, GroupConvertable, TagsMatcher;

    public function __construct($reflectMap = [])
    {
        if (!empty($reflectMap)) {
            $this->setReflectMap($reflectMap);
        }
    }

    public function setParams($params): AdPlanMatcher
    {
        $this->processLine($params);

        return $this;
    }

    /**
     * @param SchemeContract $scheme
     *
     * @return void
     */
    public function execute(SchemeContract &$scheme)
    {
        $wheres = array_unique(array_filter($this->wheres));

        $scheme->scope(function (&$query) use ($wheres) {
            if (!$query instanceof SelectInterface) return;

            foreach ($wheres as $where) {
                $query->where($where);
            }
        });
    }


    /**
     *
     * @param $params
     *
     * @return void
     */
    protected function processLine($params)
    {
        $line = [
            [$this, 'matchTDay'],
            [$this, 'matchCpGames'],
            [$this, 'matchGames'],
            [$this, 'matchAppShowIds'],
            [$this, 'matchGameIdTags'],
            [$this, 'matchPackageTags'],
            [$this, 'matchChannelTags'],
            [$this, 'matchChannelMainTags'],
            [$this, 'matchChannelMainId'],
            [$this, 'matchChannelId'],
            [$this, 'matchPlatformId'],
            [$this, 'matchPackageId'],
            [$this, 'matchPromotionId'],
            [$this, 'matchAdAccountId'],
            [$this, 'matchAdAccount'],
            [$this, 'matchAccountId'],
            [$this, 'matchCampaignId'],
            [$this, 'matchCampaignName'],
            [$this, 'matchPlanId'],
            [$this, 'matchPlanNameSvLinkName'],
            [$this, 'matchUserId'],
            [$this, 'matchDepartmentId'],
            [$this, 'matchIsHasNatural'],
            [$this, 'matchIsHasAppointment'],
            [$this, 'matchDataScope'],
            [$this, 'removeLtvRealtime'],
            [$this, 'matchMarketingGoal'],
            [$this, 'matchUserOS'],
        ];

        foreach ($line as $callback) {
            if (is_callable($callback)) {
                $this->wheres[] = call_user_func_array($callback, [$params]);
            }
        }
    }

    /**
     * @param $params
     *
     * @return string
     */
    protected function matchIsHasNatural($params): string
    {
        if (empty($params['is_has_natural'])) {
            $field = $this->getField('plan_id');
            return "{$field}  > 0";
        }
        else {
            return '';
        }
    }

    //是否包含预约数据
    protected function matchIsHasAppointment($params): string
    {
        if (!isset($params['is_has_appointment']) || !empty($params['is_has_appointment'])) return '';
        $field = $this->getField('is_appointment');
        return "({$field} != 1 or {$field} is null)";
    }

    /**
     * 计划名搜索
     *
     * @param $params
     *
     * @return string
     */
    protected function matchDepartmentId($params): string
    {
        if (!array_key_exists('department_id', $params)) return '';

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        if (is_null($params['department_id'])) {
            return "(COALESCE(IF(`power`.`channel_id` IN ({$planChannelsString}), `t_admin`.`department_id`, `power`.`ad_department_id`), 0) = 0)";
        }

        $data = Convert::convertInString(Arr::get($params, 'department_id'));
        if (empty($data)) return '';

        // 匹配顺序
        // t_base user_id对应的department_id
        // -> power.ad_department_id

        return "COALESCE(IF(`power`.`channel_id` IN ({$planChannelsString}), `t_admin`.`department_id`, `power`.`ad_department_id`), 0) IN ({$data})";
    }


    /**
     * 计划名搜索
     *
     * @param $params
     *
     * @return string
     */
    protected function matchUserId($params): string
    {
        if (!array_key_exists('user_id', $params)) return '';

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        $field = $this->getField('user_id');

        if (is_null($params['user_id'])) {
            return "COALESCE(IF(`POWER`.`channel_id` IN ({$planChannelsString}), `t_base`.`USER_ID`, `POWER`.`AD_USER_ID`), 0) = 0";
        }

        $data = Convert::convertInString(Arr::get($params, 'user_id'));

        return "COALESCE(IF(`POWER`.`channel_id` IN ({$planChannelsString}), `t_base`.`USER_ID`, `POWER`.`AD_USER_ID`), 0) IN ({$data})";
    }

    /**
     * @param $params
     *
     * @return string
     */
    protected function matchChannelMainId($params): string
    {
        $data = Convert::convertInString(Arr::get($params, 'channel_main_id'));

        if (empty($data)) return '';

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

//        return "((POWER.channel_id IN ({$planChannelsString}) and base_channel.channel_main_id IN ({$data}) ) or  (POWER.channel_id NOT IN ({$planChannelsString}) and POWER.channel_main_id IN ({$data})))";
        return "COALESCE(IF(`POWER`.`channel_id` IN ({$planChannelsString}), IF(`base_channel`.`channel_main_id` != 0,`base_channel`.`channel_main_id`,`power`.`channel_main_id`),`power`.`channel_main_id`),0) IN ({$data})";
    }

    /**
     * @param $params
     *
     * @return string
     */
    protected function removeLtvRealtime($params): string
    {
        if (empty($params['remove_realtime'])) return '';

        return "pay_date < DATE(NOW())";
    }

    /**
     * 营销场景
     *
     * @param $params
     *
     * @return string
     */
    protected function matchMarketingGoal($params): string
    {
        if (!array_key_exists('marketing_goal', $params)) return '';

        $field = $this->getField('marketing_goal');

        if (
            is_null($params['marketing_goal'])
            || $params['marketing_goal'] === ['']
            || count($params['marketing_goal']) == 2
        ) {
            return '';
        }

        $wheres = [];
        if (in_array(1, $params['marketing_goal'])) {
            $wheres[] = "{$field} != 2";
        }

        if (in_array(2, $params['marketing_goal'])) {
            $wheres[] = "{$field} = 2";
        }

        if (!empty($wheres)) {
            return '(' . implode(' and ', $wheres) . ')';
        }
        else {
            return '';
        }
    }
}
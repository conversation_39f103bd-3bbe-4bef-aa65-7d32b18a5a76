<?php
/**
 * 快手数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\CommonFunc;
use app\ad_upload\tool\Http;
use Plus\Util\StringUtil;

class <PERSON><PERSON>hou extends AdBaseInterface
{
    const CONVERT_ACTIVE   = 1;
    const CONVERT_REGISTER = 2;
    const CONVERT_PURCHASE = 3;

    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY');
    }

    /**
     * 公共上报
     * @param $info
     * @param $type
     * @return bool
     */
    protected function upload($info, $type, $remark = 'KUAISHOU_PARAM_')
    {
        $className   = get_called_class();
        $parts       = explode('\\', $className);
        $channelCode = end($parts);

        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = StringUtil::unCamelize($channelCode);
        $logInfo['log_type']     = 'reported_platform_log';
        $callbackUrl             = urldecode($info['CALLBACK_URL']);
        $microTime               = CommonFunc::getMillisecond();
        switch ($type) {
            case 'ACTIVE':
                $callbackUrl = $callbackUrl . '&event_type=' . self::CONVERT_ACTIVE . '&event_time=' . $microTime;
                $typeName    = '激活';
                break;
            case 'REG':
                $callbackUrl = $callbackUrl . '&event_type=' . self::CONVERT_REGISTER . '&event_time=' . $microTime;
                $typeName    = '注册';
                break;
            case 'PAY':
                $callbackUrl = $callbackUrl . '&event_type=' . self::CONVERT_PURCHASE . '&event_time=' . $microTime . '&purchase_amount=' . $info['MONEY'];
                $typeName    = '付费';
                break;
        }
        $logInfo['request']  = json_encode(['url' => $callbackUrl]);
        $http                = new Http($callbackUrl);
        $res                 = $http->get();
        $logInfo['response'] = addslashes($res);
        //记录上报结果
        $resArr  = json_decode($res, true);
        $resCode = $resArr['result'] ?? -1;
        if ($resCode == 1) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        $this->log($info, $logInfo, $res, $callbackUrl);
        return $logInfo['reported_status'] == 1;
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

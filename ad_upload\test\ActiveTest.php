<?php

namespace app\ad_upload\test;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\CustomProcess;

/**
 * 激活测试
 * <AUTHOR>
 */
class ActiveTest extends CustomProcess
{

    use CommonTrait;
    /**
     * 渠道id
     * @var int
     */
    public int $channel_id = 0;
    /**
     * 天数
     * @var int
     */
    public int $days = 0;

    /**
     * run
     * @return void
     */
    public function run()
    {
        $action = AdBaseInterface::ACTION_ACTIVE;
        echo '0. 删除缓存', PHP_EOL;
        $this->delCache('ad_up_repeat_'.$action);
        echo '1. 同步配置', PHP_EOL;
        $this->syncConfig();
        echo '2. 设置上报点', PHP_EOL;
        $timeBegin = date('Y-m-d', strtotime("-{$this->days} day")) . ' 00:00:00';
        $timeEnd   = date('Y-m-d H:i:s');
        $sql       = "select id from tb_sdk_active_log where time >='$timeBegin' and time <='$timeEnd' limit 1";
        $id        = \Plus::$app->origin_platform->query($sql)->fetchColumn();
        $this->runJob($id, $action, $timeBegin, $timeEnd);
    }
}

<?php

namespace app\service\ConfigService\Traits;

use app\extension\Support\Collections\Collection;
use app\service\ConfigService\Constants\FieldTag;

/**
 * @BaseHub 基础表头存放
 */
trait BaseHub
{
    /**
     * @param array $filterFields
     *
     * @return Collection
     */
    public function getBaseFields(array $target = []): Collection
    {
        $collect = collect();

        foreach ($target as $k => $item) {
            if (is_numeric($k) && !is_array($item)) {
                if (!is_callable([$this, $item])) continue;
                $r = $this->{$item}();
            }
            else {
                if (!is_callable([$this, $k])) continue;
                $r = $this->{$k}(...array_values($item));
            }

            $collect = $collect->merge($r);
        }

        return $collect;
    }

    /**
     *
     * @param int $dimension
     *
     * @return Collection
     */
    protected function timeDimension(int $dimension): Collection
    {
        $collect = collect();

        $collect->put('tday', ['title' => '统计日期', 'sorter' => true,]);

        if (1 === $dimension) {
            $collect->put('thour', ['title' => '小时', 'sorter' => true,]);
        }

        return $collect;
    }

    /**
     * 基础表头
     *
     * @return Collection
     */
    protected function baseCollect(): Collection
    {
        return collect([
            'tday'                 => ['title' => '统计日期', 'sorter' => true,],
            'cp_game_id'           => ['title' => '游戏原名', 'sorter' => true,],
            'game_id'              => ['title' => '游戏统计名', 'sorter' => true,],
            'app_show_id'          => ['title' => '游戏前端名', 'sorter' => true,],
            'package_id'           => ['title' => '包号', 'sorter' => true,],
            'channel_main_id'      => ['title' => '主渠道', 'sorter' => true,],
            'promotion_channel_id' => ['title' => '推广子渠道', 'sorter' => true,],
            //            'promotion_channel_id' => ['title' => '推广渠道', 'sorter' => true,],
            'promotion_id'         => ['title' => '推广分类', 'sorter' => true,],
            'platform_id'          => ['title' => '客户端', 'sorter' => true,],
            'department_id'        => ['title' => '投放部门', 'sorter' => true,],
            'user_id'              => ['title' => '投放人', 'sorter' => true,],
        ]);
    }

    /**
     * 临时
     * @return Collection
     */
    protected function baseOperationCollect(): Collection
    {
        return collect([
            'tday'            => ['title' => '统计日期', 'sorter' => true,],
            'cp_game_id'      => ['title' => '游戏原名', 'sorter' => true,],
            'game_id'         => ['title' => '游戏统计名', 'sorter' => true,],
            'app_show_id'     => ['title' => '游戏前端名', 'sorter' => true,],
            'package_id'      => ['title' => '包号', 'sorter' => true,],
            'channel_main_id' => ['title' => '主渠道', 'sorter' => true,],
            'channel_id'      => ['title' => '推广子渠道', 'sorter' => true,],
            'promotion_id'    => ['title' => '推广分类', 'sorter' => true,],
            'platform_id'     => ['title' => '客户端', 'sorter' => true,],
            'department_id'   => ['title' => '投放部门', 'sorter' => true,],
            'user_id'         => ['title' => '投放人', 'sorter' => true,],
        ]);

    }


    /**
     * 含有推广账户的表头部分
     *
     * @return Collection
     */
    protected function AdAccountBaseCollect(): Collection
    {
        return collect([
            'account_id'   => ['title' => '账户ID', 'sorter' => true,],
            'ad_account'   => ['title' => '投放账户', 'sorter' => true,],
            'account_name' => ['title' => '账户名称', 'sorter' => true,],
        ]);
    }


    /**
     * 含有短链的表头部分
     *
     * @return Collection
     */
    protected function svKeyBaseCollect(): Collection
    {
        return collect([
            'sv_key' => ['title' => '短链ID'],
            'aid'    => ['title' => '广告短链'],
        ]);
    }

    /**
     * 计划表头部分
     *
     * @return Collection
     */
    protected function planBaseCollect(): Collection
    {
        return collect([
            'plan_name' => ['title' => '计划名', 'sorter' => false,],
            'plan_id'   => ['title' => '计划ID', 'sorter' => true],
        ]);
    }

    /**
     * 广告组表头部分
     *
     * @return Collection
     */
    protected function campaignBaseCollect(): Collection
    {
        return collect([
            'campaign_name' => ['title' => '广告组名称', 'sorter' => false,],
            'campaign_id'   => ['title' => '广告组ID', 'sorter' => true,],
            'dim_user_os'   => ['title' => '系统定向',],
        ]);
    }

    /**
     * 创意基础表头
     *
     * @return Collection
     */
    protected function creativeBaseCollect(): Collection
    {
        return $this->campaignBaseCollect()
            ->merge($this->planBaseCollect())
            ->merge([
                'creative_name' => ['title' => '创意名', 'sorter' => false,],
                'creative_id'   => ['title' => '创意ID', 'sorter' => true,],
            ]);
    }

    /**
     * 消耗组合表头
     *
     * @return Collection
     */
    protected function costBaseCollect(): Collection
    {
        return collect([
            'cost'          => ['title' => '返点前消耗金额', 'sorter' => true,],
            'cost_discount' => ['title' => '返点后消耗金额', 'sorter' => true,],
        ]);
    }

    /**
     * 用户通关率
     *
     * @param int $node
     *
     * @return Collection
     */
    protected function passNCollect(int $node = 4): Collection
    {
        $collect = collect();
        $field   = 'pass_level';

        for ($i = 1; $i <= $node; $i++) {
            $nodeField = $field . '_' . $i;
            $comment   = '用户通过率' . $i;
            $collect->put($nodeField, ['title' => $comment]);
        }

        return $collect;
    }

    /**
     * 广告维度表头
     *
     * @return Collection
     */
    protected function adIndexBaseCollect(): Collection
    {
        return collect()
            ->merge($this->costBaseCollect())
            ->merge([
                'new_user'                                 => ['title' => '广告新增用户', 'sorter' => true],
                'create_role_new'                          => ['title' => '创角新用户', 'sorter' => true],
                'create_role_percent'                      => ['title' => '新用户创角率'],
                'create_role_cost'                         => ['title' => '新用户创角成本'],
                'new_user_cost'                            => ['title' => '新用户成本'],
                'new_user_payment_cost'                    => ['title' => '新用户付费成本(人数)', 'classify' => ['attrs', 'our_side_ad']],
                'pay_user_new'                             => ['title' => '付费新用户', 'sorter' => true],
                'pay_money_new'                            => ['title' => '新用户付费金额', 'sorter' => true],
                'pay_user_new_percent'                     => ['title' => '新用户付费率'],
                'arpu_new_user'                            => ['title' => '新用户ARPU'],
                'first_online_time_avg'                    => ['title' => '首次在线时长'],
                'online_time_avg'                          => ['title' => '首日在线时长'],
                'show_cnt'                                 => ['title' => '展示数', 'sorter' => true],
                'click_cnt'                                => ['title' => '点击数', 'sorter' => true],
                'click_show_percent'                       => ['title' => '点击率(CRT)'],
                'cpc'                                      => ['title' => '每次点击成本(CPC)'],
                'qian_cost'                                => ['title' => '千展成本'],
                'lp_view'                                  => ['title' => '落地页展示数', 'sorter' => true],
                'lp_download'                              => ['title' => '落地页点击数', 'sorter' => true],
                'lp_click_percent'                         => ['title' => '落地页点击率'],
                'convert_cnt'                              => ['title' => '转化数', 'sorter' => true],
                'convert_cost'                             => ['title' => '转化成本'],
                'play_duration_3s_percent'                 => ['title' => '3秒播放率'],
                'play_time_avg'                            => ['title' => '平均单次播放时长'],
                'convert_percent'                          => ['title' => '转化率(CVR)'],
                'show_convert_percent'                     => ['title' => '曝光转化率'],
                'download_start'                           => ['title' => '下载开始数', 'sorter' => true],
                'download_start_cost_percent'              => ['title' => '下载开始成本'],
                'download_start_percent'                   => ['title' => '下载开始率'],
                'download_finish_percent'                  => ['title' => '下载完成率'],
                'install_finish_percent'                   => ['title' => '安装完成率'],
                'activate_cnt'                             => ['title' => '媒体激活数', 'sorter' => true],
                'activate_cost'                            => ['title' => '媒体激活成本'],
                'activate_percent'                         => ['title' => '媒体激活率'],
                'activate_install_percent'                 => ['title' => '媒体安装激活完成率'],
                'register'                                 => ['title' => '媒体注册数', 'sorter' => true],
                'register_cost'                            => ['title' => '媒体注册成本'],
                'register_percent'                         => ['title' => '媒体注册率'],
                'activate_device'                          => ['title' => '广告激活', 'sorter' => true],
                'new_user_real_percent'                    => ['title' => '新用户实名率'],
                'remain_1_cost'                            => ['title' => '次留成本'],
                'pay_count_new'                            => ['title' => '新用户付费次数', 'sorter' => true],
                'arppu_new_user'                           => ['title' => '新用户ARPPU'],
                'pay_user'                                 => ['title' => '付费用户', 'sorter' => true],
                'pay_count'                                => ['title' => '付费次数', 'sorter' => true],
                'pay_money'                                => ['title' => '付费金额', 'sorter' => true],
                'pay_permeation_percent'                   => ['title' => '付费渗透率'],
                'pay_new_user_7days'                       => ['title' => '付费新用户(7天内)', 'sorter' => true],
                'pay_frequency_7days'                      => ['title' => '新用户付费次数(7天内)', 'sorter' => true],
                'pay_frequency_avg_7days'                  => ['title' => '新用户平均付费次数(7天内)'],
                'pay_frequency_7days_cost'                 => ['title' => '新用户付费次数成本'],
                'active_pay_intra_day_count'               => ['title' => '激活当日首次付费数'],
                'attribution_billing_game_in_app_ltv_1day' => ['title' => '计费当日付费金额'],
            ])->merge($this->passNCollect());
    }

    /**
     * 运营指标(基础版)
     *
     * @return Collection
     */
    protected function operatorBaseCollect(): Collection
    {
        return collect([
            'activate_device'        => ['title' => '激活设备', 'sorter' => true,],
            'firstlogin_device'      => ['title' => '首登设备', 'sorter' => true],
            'firstlogin_user'        => ['title' => '首登用户', 'sorter' => 'true'],
            'create_role_new'        => ['title' => '创角新用户', 'sorter' => 'true'],
            'device_conversion_rate' => ['title' => '设备转化率'],
            'create_role_percent'    => ['title' => '新用户创角率', 'width' => 150],
            'create_role'            => ['title' => '游戏创角'],
            'server_roll_percent'    => ['title' => '滚服率'],
            'fistlogin_real_name'    => ['title' => '首登实名率'],
            'pay_new_user'           => ['title' => '付费新用户', 'sorter' => 'true'],
            'pay_money_new'          => ['title' => '新用户付费金额', 'sorter' => true],
            'pay_user_new_percent'   => ['title' => '新用户付费率'],
            'pay_permeation_percent' => ['title' => '付费渗透率'],
            'arpu_new_user'          => ['title' => '新用户ARPU'],
            'arppu_new_user'         => ['title' => '新用户ARPPU'],
        ])
            ->merge($this->costBaseCollect())
            ->merge([
                'new_user_cost'           => ['title' => '新用户成本'],
                'active_user'             => ['title' => '活跃用户', 'sorter' => 'true',],
                'pay_user'                => ['title' => '付费用户', 'sorter' => 'true'],
                'pay_money'               => ['title' => '付费金额', 'sorter' => 'true'],
                'pay_money_all_with_hour' => ['title' => '付费金额(按付费时间)', 'sorter' => true],
                'active_pay_percent'      => ['title' => '活跃付费率'],
                'arpu_active'             => ['title' => '活跃ARPU'],
                'arppu_active'            => ['title' => '活跃ARPPU'],
                'active_user_7days_ago'   => ['title' => '活跃用户(7天前)', 'sorter' => true],
                'active_pay_user'         => ['title' => '付费活跃用户数',],
                'active_pay_user_percent' => ['title' => '付费活跃用户占比',],
                'old_user'                => ['title' => '老用户'],
                'old_user_pay'            => ['title' => '付费老用户',],
                'old_user_pay_money'      => ['title' => '老用户付费金额'],
                'old_user_pay_percent'    => ['title' => '老用户付费率',],
                'old_user_arpu'           => ['title' => '老用户ARPU',],
                'old_user_arppu'          => ['title' => '老用户ARPPU',],
                'old_user_percent'        => ['title' => '老用户占比',],
            ]);
    }

    /**
     * 留存N留表头集合
     *
     * @param int|iterable $nodes
     *
     * @return Collection
     */
    public function remainNCollect($nodes, $prefix = ''): Collection
    {
        $callback = fn($i) => $i == 1 ? ($prefix . '次留') : ($prefix . ($i + 1) . '留');

        return is_iterable($nodes)
            ? $this->stepNCollectByNodes($callback, 'remain', $nodes)
            : $this->stepNCollect($callback, 'remain', 1, $nodes);
    }

    /**
     * 付费留存N留表头集合
     *
     * @param int|iterable $nodes
     *
     * @return Collection
     */
    public function payRemainNCollect($nodes): Collection
    {
        $callback = fn($i) => $i == 1 ? '付费次留' : ('付费' . ($i + 1) . '留');

        return is_iterable($nodes)
            ? $this->stepNCollectByNodes($callback, 'pay_remain', $nodes)
            : $this->stepNCollect($callback, 'pay_remain', 1, $nodes);
    }

    /**
     * ltvN表头集合
     *
     * @param int|iterable $nodes
     *
     * @return Collection
     */
    public function ltvNCollect($nodes): Collection
    {
        return is_iterable($nodes)
            ? $this->stepNCollectByNodes('LTV', 'ltv', $nodes)
            : $this->stepNCollect('LTV', 'ltv', 1, $nodes);
    }

    /**
     * roiN表头集合
     *
     * @param int|iterable $nodes
     *
     * @return Collection
     */
    public function roiNCollect($nodes): Collection
    {
        return is_iterable($nodes)
            ? $this->stepNCollectByNodes('ROI', 'roi', $nodes)
            : $this->stepNCollect('ROI', 'roi', 1, $nodes);
    }

    /**
     * @param string|\Closure $title
     * @param string          $field
     * @param int             $initialStep
     * @param int             $runN
     * @param bool            $isEq
     *
     * @return Collection
     */
    protected function stepNCollect(
        $title, string $field, int $initialStep = 0, int $runN = 0, bool $isEq = true, array $options = []
    ): Collection
    {
        $collect = collect();

        if ($isEq)
            $eqFunc = fn($data, $target) => $data <= $target;
        else
            $eqFunc = fn($data, $target) => $data < $target;

        for ($i = $initialStep; $eqFunc($i, $runN); $i++) {
            $nodeField = $field . '_' . $i;

            if ($title instanceof \Closure) {
                $nodeTitle = $title($i, $field);
            }
            else {
                $nodeTitle = $title . $i;
            }

            $collect->put($nodeField, array_merge(['title' => $nodeTitle], $options));
        }

        return $collect;
    }

    /**
     * @param          $title
     * @param string   $field
     * @param iterable $nodes
     * @param bool     $isEq
     *
     * @return Collection
     */
    protected function stepNCollectByNodes(
        $title, string $field, iterable $nodes, bool $isEq = true, array $options = []
    ): Collection
    {
        $collect = collect();

        if ($isEq)
            $eqFunc = fn($data, $target) => $data <= $target;
        else
            $eqFunc = fn($data, $target) => $data < $target;

        foreach ($nodes as $foo) {
            $nodeField = $field . '_' . $foo;

            if ($title instanceof \Closure)
                $nodeTitle = $title($foo, $field);
            else
                $nodeTitle = $title . $foo;

            $collect->put($nodeField, array_merge(['title' => $nodeTitle], $options));
        }

        return $collect;
    }

    /**
     * @param        $nodes
     * @param string $fieldPrefix
     * @param string $showCol
     * @param null   $showFn
     * @param string $firstFormat
     * @return Collection
     */
    public function hasNodesCols($nodes, string $fieldPrefix = '', string $showCol = '', $showFn = null, string $firstFormat = '', $isEq = true, array $options = []): Collection
    {
        $callbackFn = fn($i) => ($i == 1 && !empty($firstFormat)) ? $firstFormat : (is_callable($showFn) ? str_replace('<n>', $showFn($i), $showCol) : str_replace('<n>', $i, $showCol));

        return is_iterable($nodes)
            ? $this->stepNCollectByNodes($callbackFn, $fieldPrefix, $nodes, $isEq, $options)
            : $this->stepNCollect($callbackFn, $fieldPrefix, 1, $nodes, $isEq, $options);
    }

}
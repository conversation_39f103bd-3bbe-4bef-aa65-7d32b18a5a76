<?php

namespace app\ad_upload\test;

use app\ad_upload\tool\CommonFunc;
use app\ad_upload\tool\CustomProcess;
use app\apps\api\controllers\AdUploadController;

/**
 * sdk客户端上报测试 模拟
 * <AUTHOR>
 */
class SdkPayTest extends CustomProcess
{
    /**
     * 开始时间
     * @var string
     */
    public $timeBegin = '';
    /**
     * 结束时间
     * @var string
     */
    public $timeEnd = '';

    /**
     * 订单id
     * @var string
     */
    public $ids = '';

    /**
     * run
     * @return void
     */
    public function run()
    {
        if (empty($this->timeBegin)) {
            $this->timeBegin = date('Y-m-d');
        }
        if (empty($this->timeEnd)) {
            $this->timeEnd = date('Y-m-d');
        }

        $sql = "select * from
                    `bigdata_dwd`.`dwd_reported_paid_platform_log`  where 
                 dt between '$this->timeBegin' and '$this->timeEnd'
                 and source_dimension =1";
        if ($this->ids) {
            $ids = explode(',', $this->ids);
            $ids = CommonFunc::arr2Str($ids);
            $sql = "select * from
                    `bigdata_dwd`.`dwd_reported_paid_platform_log`  where 
                     order_id in($ids)  and source_dimension =1";
        }
        $orders = \Plus::$app->doris_entrance->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        foreach ($orders as $order) {
            $api = new AdUploadController();
            $rs  = $api->ruleAction(['order_id'=>$order['order_id']]);
            if ($rs["code"] == 200) {
                $reported_behavior = $rs['data']["reported_behavior"];
                //上报行为类别(0-正常上报, 1-次数扣减, 2-金额扣减,3-虚拟订单上报)
                if (in_array($reported_behavior, [0, 2])) {
                    $order['reported_status'] = 1;
                } else {
                    $order['reported_status'] = 0;
                }
                $data = array_merge($order, $rs["data"]);
                $dt   = date('Y-m-d', strtotime($data['pay_time']));
                $sql  = sprintf(
                    "INSERT INTO bigdata_dwd.dwd_reported_paid_platform_log 
                (order_id, pay_result, reported_status, actually_money, 
                 reported_money, game_id, package_id, channel_id, oaid, device_key, pay_time, 
                  `core_account` , `source_id`, `source_dimension`, reported_behavior, 
                 reported_rule_id, reported_behavior_rule, no_reported_origin, callback_url, content,
                 response_content,cp_game_id, channel_code, `time`, dt, create_time, update_time) 
                VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', 
                 '%s', '%s', '%s' , '%s', '%s', '%s', '%s', '%s', '%s','%s','%s','%s', NOW(), '%s', NOW(), NOW())",
                    ($data['order_id'] ?? '0'),
                    ($data['pay_result'] ?? 1),
                    ($data['reported_status'] ?? 0),
                    ($data['actually_money'] ?? 0),
                    ($data['reported_money'] ?? 0),
                    ($data['game_id'] ?? 0),
                    ($data['package_id'] ?? 0),
                    ($data['channel_id'] ?? 0),
                    ($data['oaid'] ?? ''),
                    ($data['device_key'] ?? ''),
                    ($data['pay_time'] ?? ''),
                    ($data['core_account'] ?? ''),
                    ($data['source_id'] ?? ''),
                    ($data['source_dimension'] ?? ''),
                    ($data['reported_behavior'] ?? 0),
                    ($data['reported_rule_id'] ?? '-1'),
                    ($data['reported_behavior_rule'] ?? null),
                    ($data['no_reported_origin'] ?? ''),
                    ($data['callback_url'] ?? ''),
                    ($data['content'] ?? ''),
                    ($data['response_content'] ?? ''),
                    ($data['cp_game_id'] ?? ''),
                    ($data['channel_code'] ?? ''),
                    $dt,
                );
                \Plus::$app->doris_entrance->exec($sql);
            }// end if()
        }// end foreach()
    }
}

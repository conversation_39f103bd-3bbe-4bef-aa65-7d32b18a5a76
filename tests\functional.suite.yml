# Codeception Test Suite Configuration
#
# Suite for functional tests
# Emulate web requests and make application process them
# Include one of framework modules (Symfony2, Yii2, Laravel5, Phalcon4) to use it
# Remove this suite if you don't use frameworks

actor: FunctionalTester
modules:
    enabled:
        # add a framework module here
        - \Helper\Functional
        - Plus:
            configFile: config/main.php
            serviceFile: config/service.php
            applicationClass: \app\extension\Application
            applicationServiceClass: \app\extension\ApplicationService
    step_decorators: ~        
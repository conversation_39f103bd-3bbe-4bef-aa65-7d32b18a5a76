<?php

namespace app\ad_upload\channels;

/**
 * 推广子渠道 -- 抖音直播投流
 */
class DouyinAd extends Toutiao2
{
    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->toutiaoUpload($info, 'ACTIVE', 'DOUYINAD_PARAM_');
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->toutiaoUpload($info, 'REG', 'DOUYINAD_PARAM_');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->toutiaoUpload($info, 'PAY', 'DOUYINAD_PARAM_');
    }
}

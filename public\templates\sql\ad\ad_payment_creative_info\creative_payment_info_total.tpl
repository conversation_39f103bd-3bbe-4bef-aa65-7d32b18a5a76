{include file="sql/ad/ad_payment_creative_info/creative_payment_common.tpl"}
select COUNT(1) as total_row from (
select
q2.main_account
from ddc_platform.dwd_sdk_adsource_game q2
join (
select lt.*
from ddc_platform.dwd_sdk_user_payment lt
join source_id_list rt on lt.SOURCE_ID = rt.core_source
where pay_result = 1
) q1 on q1.source_id = q2.source_id
{if !empty($params)}
    {assign var="mf" value=1}
    {foreach $params as $ff => $fi}
        {if $ff eq 'range_date'}
            {if !$mf} and {else} where {$mf=0} {/if}
            DATE(pay_time) between '{$fi[0]}' and '{$fi[1]}'
            {continue}
        {/if}
    {/foreach}
{/if}
group by q2.main_account
) main_body

<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

class NewUserDistributeServ
{
    private const TB_NEW_USER_DISTRIBUTE = 'ddc_platform.dws_ad_event_distribution';


    public function simpleInfo(
        array $params,
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): array
    {
        $db = $this->getConn();
        $qb = $db->select()->from(static::TB_NEW_USER_DISTRIBUTE);

        $dateDimension = $params['range_date_dimension'];

        if ($dateDimension == 3) {
            if (in_array('tday', $columns)) {
                unset($columns[array_search('tday', $columns)]);
                $columns = array_merge($columns, [
                    new Fragment('FROM_DAYS(TO_DAYS(`tday`) - MOD(TO_DAYS(`tday`) - 7, 7)) as my_week'),
                    new Fragment('MIN(tday) as start_day'),
                    new Fragment('MAX(tday) as end_day'),
                ]);
            }
        }
        elseif ($dateDimension == 4) {
            unset($columns[array_search('tday', $columns)]);
            $columns = array_merge($columns, [new Fragment("DATE_FORMAT(tday, '%Y-%m') as my_month")]);
        }

        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            $rangeStart = $params['range_date_start'];
            $rangeEnd   = $params['range_date_end'];

            $qb->where('tday', 'between', $rangeStart, $rangeEnd);
        }

        if (!empty($params['cp_game_id'])) {
            $qb->where('cp_game_id', $params['cp_game_id']);
        }

        if (!empty($params['cp_game_id[!]'])) {
            $qb->where('cp_game_id', 'not in', new Parameter($params['cp_game_id[!]']));
        }

        if (!empty($params['event_type'])) {
            $qb->where('event_type', $params['event_type']);
        }

        if (!empty($params['group_type'])) {
            $qb->where('group_type', $params['group_type']);
        }

        if (!empty($params['report_type'])) {
            $qb->where('report_type', $params['report_type']);
        }

        if (isset($params['day_type']) && (!empty($params['day_type']) || is_numeric($params['day_type']))) {
            $qb->where('day_type', $params['day_type']);
        }

        $qb->columns($columns);

        if (!empty($sort)) {
            $qb->orderBy(Arr::wrap($sort));
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }
        return $qb->fetchAll();
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
<?php

namespace app\apps\operator\Helpers;

use app\apps\internal\Helpers\IndicatorsHelpers;

class IndicatorCalcHelpers
{
    public static function deviceConversionRate(&$data)
    {
        $data['device_conversion_rate'] = IndicatorsHelpers::division(
            ($data['firstlogin_user'] ?? 0), ($data['firstlogin_device'] ?? 0), 2, true
        );
    }

    /**
     * @param $data
     *
     * @return void
     */
    public static function deviceConversionRateNewLogin(&$data)
    {
        $data['device_conversion_rate'] = IndicatorsHelpers::division(
            ($data['new_user'] ?? 0), ($data['new_device'] ?? 0), 2, true
        );
    }

    /**
     * 首登创角
     *
     * @param $data
     *
     * @return void
     */
    public static function fistLoginCreateRole(&$data)
    {
        $data['create_role_percent'] = IndicatorsHelpers::division(
            ($data['create_role_new'] ?? 0), ($data['firstlogin_user'] ?? 0), 2, true
        );
    }

    /**
     * 新增创角
     *
     * @param $data
     *
     * @return void
     */
    public static function newLoginCreateRole(&$data)
    {
        $data['create_role_percent'] = IndicatorsHelpers::division(
            ($data['create_role_new'] ?? 0), ($data['new_user'] ?? 0), 2, true
        );
    }

    public static function fistLoginRealName(&$data)
    {
        $data['fistlogin_real_name'] = IndicatorsHelpers::division(
            ($data['firstlogin_real_user'] ?? 0), ($data['firstlogin_user'] ?? 0), 2, true
        );
    }

    /**
     * 新增实名用户
     *
     * @param $data
     *
     * @return void
     */
    public static function newLoginRealName(&$data)
    {
        $data['newlogin_real_name'] = IndicatorsHelpers::division(
            ($data['newlogin_real_user'] ?? 0), ($data['new_user'] ?? 0), 2, true
        );
    }

    /**
     * @param $data
     *
     * @return void
     */
    public static function firstLoginUserPayPercent(&$data)
    {
        $data['pay_user_new_percent'] = IndicatorsHelpers::division(
            ($data['pay_new_user'] ?? 0), ($data['firstlogin_user'] ?? 0), 2, true
        );
    }

    /**
     *
     * @param $data
     *
     * @return void
     */
    public static function newLoginUserPayPercent(&$data)
    {
        $data['pay_user_new_percent'] = IndicatorsHelpers::division(
            ($data['pay_new_user'] ?? 0), ($data['new_user'] ?? 0), 2, true
        );
    }


    /**
     * @param $data
     *
     * @return void
     */
    public static function firstLoginUserARPU(&$data)
    {
        $data['arpu_new_user'] = IndicatorsHelpers::division(
            $data['pay_money_new'] ?? 0, $data['firstlogin_user'] ?? 0
        );
    }

    /**
     * ARPU(新增)
     *
     * @param $data
     *
     * @return void
     */
    public static function newLoginUserARPU(&$data)
    {

        $data['arpu_new_user'] = IndicatorsHelpers::division(
            ($data['pay_money_new'] ?? 0), ($data['new_user'] ?? 0)
        );
    }


    /**
     * @param $data
     *
     * @return void
     */
    public static function firstLoginUserARPPU(&$data)
    {
        $data['arppu_new_user'] = IndicatorsHelpers::division(
            ($data['pay_money_new'] ?? 0), ($data['pay_new_user'] ?? 0)
        );
    }

    /**
     * @param $data
     *
     * @return void
     */
    public static function newLoginUserARPPU(&$data)
    {
        $data['arppu_new_user'] = IndicatorsHelpers::division(
            ($data['pay_money_new'] ?? 0), ($data['pay_new_user'] ?? 0)
        );
    }

    /**
     * 首登成本
     *
     * @return void
     */
    public static function firstLoginCost(&$data)
    {
        $data['new_user_cost'] = IndicatorsHelpers::division(
            ($data['cost_discount'] ?? 0), ($data['firstlogin_user'] ?? 0)
        );
    }

    public static function newLoginCost(&$data)
    {
        $data['new_user_cost'] = IndicatorsHelpers::division(
            ($data['cost_discount'] ?? 0), ($data['new_user'] ?? 0)
        );
    }

    /**
     * @param $data
     *
     * @return void
     */
    public static function payActivePercent(&$data)
    {
        $data['active_pay_percent'] = IndicatorsHelpers::division(
            ($data['pay_user'] ?? 0), ($data['firstlogin_active_user'] ?? 0),
            2, true
        );
    }

    /**
     * @param $data
     *
     * @return void
     */
    public static function payActivePercentByNewLogin(&$data)
    {
        $data['active_pay_percent'] = IndicatorsHelpers::division(
            ($data['pay_user'] ?? 0), ($data['active_user'] ?? 0), 2, true
        );
    }

    /**
     * 活跃ARPU(首登)
     *
     * @param $data
     *
     * @return void
     */
    public static function activeARPU(&$data)
    {
        $data['arpu_active'] = IndicatorsHelpers::division(
            ($data['pay_money'] ?? 0),
            max(($data['active_user'] ?? 0), 0)
        );
    }

    /**
     * 活跃ARPU(新增)
     *
     * @param $data
     *
     * @return void
     */
    public static function activeARPUByNewLogin(&$data)
    {
        $data['arpu_active'] = IndicatorsHelpers::division(
            ($data['pay_money'] ?? 0),
            ($data['active_user'] ?? 0)
        );
    }

    /**
     * @param $data
     *
     * @return void
     */
    public static function activeARPPU(&$data)
    {
        $data['arppu_active'] = IndicatorsHelpers::division(
            ($data['pay_money'] ?? 0),
            ($data['pay_user'] ?? 0)
        );
    }

    public static function activeUserOld(&$data)
    {
        $data['old_user'] = max(($data['firstlogin_active_user'] ?? 0) - ($data['firstlogin_user'] ?? 0), 0);
    }

    public static function activeUserOldByNewLogin(&$data)
    {
        $data['old_user'] = max(($data['active_user'] ?? 0) - ($data['new_user'] ?? 0), 0);
    }

    /**
     * @param $data
     *
     * @return void
     */
    public static function oldUserPay(&$data)
    {
        $data['old_user_pay'] = ($data['pay_user'] ?? 0) - ($data['pay_new_user'] ?? 0);
    }

    /**
     * @param $data
     *
     * @return void
     */
    public static function oldUserPayMoney(&$data)
    {
        $data['old_user_pay_money'] = ($data['pay_money'] ?? 0) - ($data['pay_money_new'] ?? 0);
    }

    public static function oldUserPayPercent(&$data)
    {
        $data['old_user_pay_percent'] = IndicatorsHelpers::division(
            ($data['old_user_pay'] ?? 0), ($data['old_user'] ?? 0),
            2, true
        );
    }

    public static function oldUserARPU(&$data)
    {
        $data['old_user_arpu'] = IndicatorsHelpers::division(
            ($data['old_user_pay_money'] ?? 0), ($data['old_user'] ?? 0)
        );
    }

    public static function oldUserARPPU(&$data)
    {
        $data['old_user_arppu'] = IndicatorsHelpers::division(
            ($data['old_user_pay_money'] ?? 0), ($data['old_user_pay'] ?? 0)
        );
    }


    public static function oldUserPercent(&$data)
    {
        $data['old_user_percent'] = IndicatorsHelpers::division(
            ($data['old_user'] ?? 0), max(($data['firstlogin_active_user'] ?? 0), 0),
            2, true
        );
    }

    /**
     * 付费活跃用户占比(首登)
     *
     * @param $data
     *
     * @return void
     */
    public static function firstLoginActiveUserPayPercent(&$data)
    {
        $data['active_pay_user_percent'] = IndicatorsHelpers::division(
            ($data['active_pay_user'] ?? 0), max(($data['firstlogin_active_user'] ?? 0), 0), 2, true
        );
    }

    /**
     * 滚服率
     *
     * @param $data
     *
     * @return void
     */
    public static function serverRollPercent(&$data)
    {
        $data['server_roll_percent'] = IndicatorsHelpers::division(
            (($data['create_role'] ?? 0) - ($data['firstlogin_role2'] ?? 0)),
            $data['create_role'] ?? 0,
            2, true
        );
    }

    /**
     * 付费渗透率
     *
     * @param $data
     *
     * @return void
     */
    public static function payPermeationPercent(&$data)
    {
        $data['pay_permeation_percent'] = IndicatorsHelpers::division(
            ($data['pay_user_per'] ?? 0), ($data['firstlogin_user'] ?? 0),
            2, true
        );
    }


}
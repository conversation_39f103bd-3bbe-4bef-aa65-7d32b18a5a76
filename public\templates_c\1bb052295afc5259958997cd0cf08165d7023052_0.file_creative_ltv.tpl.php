<?php
/* Smarty version 5.0.0-rc3, created on 2025-06-23 12:11:15
  from 'file:sql/advertise/ad_ltv/creative_ltv.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.0.0-rc3',
  'unifunc' => 'content_6858d3e3406b74_43502366',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '1bb052295afc5259958997cd0cf08165d7023052' => 
    array (
      0 => 'sql/advertise/ad_ltv/creative_ltv.tpl',
      1 => 1750651839,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:sql/advertise/ad_dashboard/creative_dash_info.tpl' => 1,
  ),
))) {
function content_6858d3e3406b74_43502366 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = '/mnt/e/project/spy2.0-api/public/templates/sql/advertise/ad_ltv';
$_smarty_tpl->renderSubTemplate("file:sql/advertise/ad_dashboard/creative_dash_info.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), (int) 0, $_smarty_current_dir);
?>
,
creative_deep_info as (
    select
        t1.tday,
        t1.package_id,
        t1.promotion_channel_id,
        t1.campaign_id,
        any(t1.campaign_name) as campaign_name,
        t1.plan_id,
        t1.creative_id,
        any(game_id) as game_id,
        any(cp_game_id) as cp_game_id,
        any(dim_user_id) as dim_user_id,
        any(t1.platform_id) as platform_id,
        any(t1.app_show_id) as app_show_id,
        any(channel_main_id) as channel_main_id,
        any(department_id) as department_id,
        any(t1.promotion_id) as promotion_id,
        any(t1.account_id) as account_id,
        any(t1.account_name) as account_name,
        any(t1.ad_account) as ad_account,
        any(t1.dim_user_os) as dim_user_os,
        SUM(t1.new_user) as new_user,
        SUM(t1.cost_discount) as cost_discount,
        SUM(t1.cost) as cost
    from dashboard_info t1
    left join base_conf_platform.tb_base_channel_conf t2 on t1.promotion_channel_id = t2.channel_id
    left join dataspy.admin_user t3 on t1.dim_user_id = t3.id
    <?php if (!empty($_smarty_tpl->getValue('params'))) {?>
        <?php $_smarty_tpl->assign('first_mark', 1, false, NULL);?>
        <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'item', true, 'key');
$_smarty_tpl->getVariable('item')->iteration = 0;
$foreach0DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('key')->value => $_smarty_tpl->getVariable('item')->value) {
$foreach0DoElse = false;
$_smarty_tpl->getVariable('item')->iteration++;
$_smarty_tpl->getVariable('item')->last = $_smarty_tpl->getVariable('item')->iteration === $_smarty_tpl->getVariable('item')->total;
$foreach0Backup = clone $_smarty_tpl->getVariable('item');
?>
            <?php if ($_smarty_tpl->getValue('key') == 'channel_id') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                    t1.promotion_channel_id  in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
)
                <?php } else { ?>
                    t1.promotion_channel_id  = '<?php echo $_smarty_tpl->getValue('item');?>
'
                <?php }?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('key') == 'channel_id_tags') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
                t1.promotion_channel_id in (
                    select distinct data_id from base_conf_platform.biz_tags where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
) and table_name = 'app_channel'
                )
            <?php }?>
            <?php if ($_smarty_tpl->getValue('key') == 'channel_main_id') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                    channel_main_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
)
                <?php } else { ?>
                    channel_main_id = <?php echo $_smarty_tpl->getValue('item');?>

                <?php }?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('key') == 'channel_main_id_tags') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
                channel_main_id in (
                    select distinct data_id from base_conf_platform.biz_tags where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
) and table_name = 'package_channel_main'
                )
            <?php }?>
            <?php if ($_smarty_tpl->getValue('key') == 'user_id') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                    t1.dim_user_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
)
                <?php } else { ?>
                    t1.dim_user_id = <?php echo $_smarty_tpl->getValue('item');?>

                <?php }?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('key') == 'department_id') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                    t3.department_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
)
                <?php } else { ?>
                    t3.department_id = <?php echo $_smarty_tpl->getValue('item');?>

                <?php }?>
            <?php }?>
        <?php
$_smarty_tpl->setVariable('item', $foreach0Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
    <?php }?>
    group by t1.tday, t1.package_id, t1.promotion_channel_id, t1.campaign_id, t1.plan_id, t1.creative_id
)
select
<?php if (!empty($_smarty_tpl->getValue('max_ltv_node'))) {?>
    <?php
$_smarty_tpl->assign('foo', null);$_smarty_tpl->tpl_vars['foo']->step = 1;$_smarty_tpl->tpl_vars['foo']->total = (int) ceil(($_smarty_tpl->tpl_vars['foo']->step > 0 ? $_smarty_tpl->getValue('max_ltv_node')+1 - (1) : 1-($_smarty_tpl->getValue('max_ltv_node'))+1)/abs($_smarty_tpl->tpl_vars['foo']->step));
if ($_smarty_tpl->tpl_vars['foo']->total > 0) {
for ($_smarty_tpl->tpl_vars['foo']->value = 1, $_smarty_tpl->tpl_vars['foo']->iteration = 1;$_smarty_tpl->tpl_vars['foo']->iteration <= $_smarty_tpl->tpl_vars['foo']->total;$_smarty_tpl->tpl_vars['foo']->value += $_smarty_tpl->tpl_vars['foo']->step, $_smarty_tpl->tpl_vars['foo']->iteration++) {
$_smarty_tpl->tpl_vars['foo']->first = $_smarty_tpl->tpl_vars['foo']->iteration === 1;$_smarty_tpl->tpl_vars['foo']->last = $_smarty_tpl->tpl_vars['foo']->iteration === $_smarty_tpl->tpl_vars['foo']->total;?>
        <?php if (!empty($_smarty_tpl->getValue('column_scope'))) {?>
            <?php if ($_smarty_tpl->getSmarty()->getModifierCallback('in_array')('ltv',$_smarty_tpl->getValue('column_scope'))) {?>
                <?php if (!empty($_smarty_tpl->getValue('ltv_type'))) {?>
                    <?php if ($_smarty_tpl->getValue('ltv_type') == 1) {?>
                        SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0)) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
                    <?php } elseif ($_smarty_tpl->getValue('ltv_type') == 2) {?>                         ROUND((SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0))-COALESCE(SUM(IF(day_type = (<?php echo $_smarty_tpl->getValue('foo');?>
-1), money_all, 0)), 0))/SUM(all_new_user), 2) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
                    <?php } elseif ($_smarty_tpl->getValue('ltv_type') == 3) {?>
                        ROUND( SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0))/SUM(IF(day_type=1, money_all, 0)), 2 ) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
                    <?php } else { ?>
                        ROUND(SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0)) / SUM(all_new_user), 2) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
                    <?php }?>
                <?php } else { ?>
                    ROUND(SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0)) / SUM(all_new_user), 2) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
                <?php }?>
            <?php }?>

            <?php if ($_smarty_tpl->getSmarty()->getModifierCallback('in_array')('roi',$_smarty_tpl->getValue('column_scope'))) {?>
                CONCAT(ROUND(SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0)) / SUM(all_cost_discount) * 100, 2), '%') as roi_<?php echo $_smarty_tpl->getValue('foo');?>
,
            <?php }?>
        <?php } else { ?>
            <?php if (!empty($_smarty_tpl->getValue('ltv_type'))) {?>
                <?php if ($_smarty_tpl->getValue('ltv_type') == 1) {?>
                    SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0)) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
                <?php } elseif ($_smarty_tpl->getValue('ltv_type') == 2) {?>                     ROUND((SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0))-COALESCE(SUM(IF(day_type = (<?php echo $_smarty_tpl->getValue('foo');?>
-1), money_all, 0)), 0))/SUM(all_new_user), 2) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
                <?php } elseif ($_smarty_tpl->getValue('ltv_type') == 3) {?>
                    ROUND( SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0))/SUM(IF(day_type=1, money_all, 0)), 2 ) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
                <?php } else { ?>
                    ROUND(SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0)) / SUM(all_new_user), 2) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
                <?php }?>
            <?php } else { ?>
                ROUND(SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0)) / SUM(all_new_user), 2) as ltv_<?php echo $_smarty_tpl->getValue('foo');?>
,
            <?php }?>
            CONCAT(ROUND(SUM(IF(day_type=<?php echo $_smarty_tpl->getValue('foo');?>
, money_all, 0)) / SUM(all_cost_discount) * 100, 2), '%') as roi_<?php echo $_smarty_tpl->getValue('foo');?>
,
        <?php }?>
    <?php }
}
}?>
SUM(all_new_user) as new_user,
SUM(all_cost_discount) as cost_discount,
SUM(IF(day_type=1000, money_all, 0)) as new_user_total_pay,
ROUND(SUM(all_cost_discount) / SUM(all_new_user), 2) as new_user_cost,
COALESCE(ROUND(SUM(IF(day_type=1000, money_all, 0)) / SUM(all_new_user), 2)) as total_ltv,
CONCAT(COALESCE(ROUND(SUM(IF(day_type=1000, money_all, 0)) / SUM(all_cost_discount) * 100, 2) , 0), '%') as total_roi,
SUM(all_cost) as cost
<?php if (!empty($_smarty_tpl->getValue('groups'))) {?>
    ,
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('groups'), 'item', true);
$_smarty_tpl->getVariable('item')->iteration = 0;
$foreach1DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('item')->value) {
$foreach1DoElse = false;
$_smarty_tpl->getVariable('item')->iteration++;
$_smarty_tpl->getVariable('item')->last = $_smarty_tpl->getVariable('item')->iteration === $_smarty_tpl->getVariable('item')->total;
$foreach1Backup = clone $_smarty_tpl->getVariable('item');
?>
        <?php if ($_smarty_tpl->getValue('item') == 'tday') {?> tday<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'cp_game_id') {?> cp_game_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'game_id') {?> game_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'app_show_id') {?> app_show_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'channel_main_id') {?> channel_main_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_channel_id') {?> promotion_channel_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_id') {?> promotion_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'user_id') {?> dim_user_id as user_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'department_id') {?> department_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'package_id') {?> package_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'platform_id') {?> platform_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'account_id') {?> account_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'account_name') {?> account_name<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'ad_account') {?> ad_account<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'campaign_id') {?> campaign_id,any(campaign_name) as campaign_name<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'plan_id') {?> plan_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'creative_id') {?> creative_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'dim_user_os') {?> dim_user_os<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php }?>
    <?php
$_smarty_tpl->setVariable('item', $foreach1Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}?>
from (
select
r1.*,
IF(day_type =
max(day_type) over (partition by tday, package_id, promotion_channel_id, campaign_id, plan_id, creative_id),
cost_discount, 0) as all_cost_discount,
IF(day_type =
max(day_type) over (partition by tday, package_id, promotion_channel_id, campaign_id, plan_id, creative_id),
cost, 0) as all_cost,
IF(day_type =
max(day_type) over (partition by tday, package_id, promotion_channel_id, campaign_id, plan_id, creative_id),
new_user, 0) as all_new_user
from (
select a2.*, COALESCE(a1.day_type, 0) as day_type, a1.money_all, a1.money
from (
select
tday,
z1.package_id,
COALESCE(IF(z2.channel_id not in (6568, 5327, 5329, 6447, 6822),z2.channel_id, IF(z1.channel_id != 0,IF(z1.channel_id = 1013, 4, IF(z1.CAMPAIGN_ID!=0,z1.channel_id, z2.channel_id)),z2.channel_id)), 0) as channel_id,
IF(z1.campaign_id = 0, '', campaign_id) as campaign_id,
IF(z1.plan_id = 0, '', plan_id)         as plan_id,
IF(z1.creative_id = 0, '', creative_id) as creative_id,
day_type,
z1.cp_game_id,
z1.game_id,
main_channel_id,
money,
money_all,
pay_date,
z1.id
from ddc_platform.dws_creative_ad_ltv_daily_v2 z1
join base_conf_platform.tb_package_detail_conf z2 on z1.package_id = z2.package_id
<?php if (!empty($_smarty_tpl->getValue('params'))) {?>
    <?php $_smarty_tpl->assign('mark_first_2', 1, false, NULL);?>
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'foo', false, 'kk');
$foreach2DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('kk')->value => $_smarty_tpl->getVariable('foo')->value) {
$foreach2DoElse = false;
?>
        <?php if ($_smarty_tpl->getValue('kk') == "range_date") {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            z1.tday between '<?php echo $_smarty_tpl->getValue('foo')[0];?>
' and '<?php echo $_smarty_tpl->getValue('foo')[1];?>
' <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'cp_game_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                z1.cp_game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                z1.cp_game_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'game_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                z1.game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                z1.game_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'game_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            z1.game_id in (
                select distinct data_id from base_conf_platform.biz_tags
                    where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'games'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'package_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                z1.package_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                z1.package_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'package_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            z1.package_id in (
                select distinct data_id from base_conf_platform.biz_tags
                    where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'packages'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'last_pay_date') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            z1.pay_date < '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php continue 1;?>
        <?php }?>
    <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}?>
) a1
right join creative_deep_info a2
on a1.tday = a2.tday
and a1.package_id = a2.package_id
and a1.channel_id = a2.promotion_channel_id
and a1.campaign_id = a2.campaign_id
and a1.plan_id = a2.plan_id
and a1.creative_id = a2.creative_id
) r1
) rr1
<?php if (!empty($_smarty_tpl->getValue('groups'))) {?>
    group by
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('groups'), 'item', true);
$_smarty_tpl->getVariable('item')->iteration = 0;
$foreach3DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('item')->value) {
$foreach3DoElse = false;
$_smarty_tpl->getVariable('item')->iteration++;
$_smarty_tpl->getVariable('item')->last = $_smarty_tpl->getVariable('item')->iteration === $_smarty_tpl->getVariable('item')->total;
$foreach3Backup = clone $_smarty_tpl->getVariable('item');
?>
        <?php if ($_smarty_tpl->getValue('item') == 'tday') {?> tday<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'cp_game_id') {?> cp_game_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'game_id') {?> game_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'app_show_id') {?> app_show_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'channel_main_id') {?> channel_main_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_channel_id') {?> promotion_channel_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_id') {?> promotion_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'user_id') {?> dim_user_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'department_id') {?> department_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'package_id') {?> package_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'platform_id') {?> platform_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'account_id') {?> account_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'account_name') {?> account_name<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'ad_account') {?> ad_account<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'campaign_id') {?> campaign_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'plan_id') {?> plan_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'creative_id') {?> creative_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'dim_user_os') {?> dim_user_os<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php }?>
    <?php
$_smarty_tpl->setVariable('item', $foreach3Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}
if (!empty($_smarty_tpl->getValue('sorts'))) {?>
    order by
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('sorts'), 'oo', true, 'ss');
$_smarty_tpl->getVariable('oo')->iteration = 0;
$foreach4DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('ss')->value => $_smarty_tpl->getVariable('oo')->value) {
$foreach4DoElse = false;
$_smarty_tpl->getVariable('oo')->iteration++;
$_smarty_tpl->getVariable('oo')->last = $_smarty_tpl->getVariable('oo')->iteration === $_smarty_tpl->getVariable('oo')->total;
$foreach4Backup = clone $_smarty_tpl->getVariable('oo');
?>
        <?php echo $_smarty_tpl->getValue('ss');?>
 <?php echo $_smarty_tpl->getValue('oo');?>

        <?php if (!$_smarty_tpl->getVariable('oo')->last) {?>, <?php }?>
    <?php
$_smarty_tpl->setVariable('oo', $foreach4Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}
if (!empty($_smarty_tpl->getValue('paginate'))) {?>
    limit <?php echo $_smarty_tpl->getValue('paginate')['page_size'];?>
 offset <?php echo $_smarty_tpl->getValue('paginate')['offset'];?>

<?php }
}
}

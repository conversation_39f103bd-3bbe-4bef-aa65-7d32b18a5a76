select
    <PERSON><PERSON>(android_firstlogin_user) as android_firstlogin_user,
    <PERSON><PERSON>(ios_firstlogin_user) as ios_firstlogin_user,
    <PERSON><PERSON>(android_ad_new_user) as android_ad_new_user,
    <PERSON>UM(ios_ad_new_user) as ios_ad_new_user,
    <PERSON>UM(android_source_transaction_records) as android_source_transaction_records,
    SUM(android_covert_transaction_records) as android_covert_transaction_records,
    ROUND(SUM(android_covert_transaction_records) / (SUM(android_source_transaction_records) + SUM(android_covert_transaction_records)) * 100, 2) as android_convert_percentage,
    SUM(android_new_user_transaction_records_month) as android_new_user_transaction_records_month,
    ROUND(SUM(android_source_transaction_records) / (SUM(android_source_transaction_records) + SUM(ios_source_transaction_records)) * 100, 2) as android_source_records_percentage,
    SUM(ios_source_transaction_records) as ios_source_transaction_records,
    SUM(ios_convert_transaction_records) as ios_convert_transaction_records,
    ROUND(SUM(ios_convert_transaction_records) / (SUM(ios_source_transaction_records) + SUM(ios_convert_transaction_records)) * 100, 2) as ios_convert_percentage,
    SUM(ios_new_user_transaction_records_month) as ios_new_user_transaction_records_month,
    ROUND(SUM(ios_source_transaction_records) / (SUM(android_source_transaction_records) + SUM(ios_source_transaction_records)) * 100, 2) as ios_source_records_percentage,
    concat(ROUND((SUM(ios_convert_transaction_records) + SUM(ios_source_transaction_records))/(SUM(ios_convert_transaction_records)+ SUM(ios_source_transaction_records) + SUM(android_covert_transaction_records) + SUM(android_source_transaction_records)) * 100 , 2) , '%') as ios_records_percentage,
    SUM(android_convert_new_user) as android_convert_new_user,
    SUM(android_convert_paid_user) as android_convert_paid_user,
    SUM(ios_convert_new_user) as ios_convert_new_user,
    SUM(ios_convert_paid_user) as ios_convert_paid_user,
    ios_convert_active_user as ios_convert_active_user
from dws_convert_platform_dashboard_daily

{include file="sql/operator/convert_data/convert_data_match.tpl"}
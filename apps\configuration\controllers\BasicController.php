<?php

namespace app\apps\configuration\controllers;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\models\baseConfPlatform\TbPackageDetailConf;
use app\service\ConfigService\BasicServ;
use app\service\General\BizTagsServ;
use app\service\General\GeneralOptionServ;
use Plus\MVC\Controller\JsonController;

/**
 * @BasicController
 *                 基础配置请求
 */
class BasicController extends JsonController
{
    /**
     * @return array
     * @throws \Exception
     */
    public function confListAction(): array
    {
        $serv   = new BasicServ();
        $keygen = $this->getValue('keygen');

        if (empty($keygen)) return $this->success([]);

        $result = $serv->getMultiOptions($keygen);
        $result = $result->map(function ($item, $key) {
            if ($key === 'cp_game_id' || $key === 'game_id') {
                if ($item instanceof Collection) {
                    foreach ($item as $i => $foo) {
                        if ($foo['key'] == 0) {
                            $item = $item->except($i);
                        }
                    }
                    return $item->values();
                }
            }

            return $item;
        });

        // todo: 优化
        $authOption = \Plus::$service->admin->getAdminPowerOption();

        foreach ($authOption as $kk => $item) {
            if (empty($item)) continue;

            if ($kk === 'cpGameIds') {
                $cpGames = $result->get('cp_game_id');
                if (empty($cpGames)) continue;
                $result->put('cp_game_id', array_values($cpGames->whereIn('key', Arr::wrap($item))->toArray()));
            }

            if ($kk === 'gameIds') {
                $games = $result->get('game_id');
                if (empty($games)) continue;
                $result->put('game_id', array_values($games->whereIn('key', Arr::wrap($item))->toArray()));
            }

            if ($kk === 'channelIds') {
                $channels = $result->get('channel_id');
                if (empty($channels)) continue;
                $result->put('channel_id', array_values($channels->whereIn('key', Arr::wrap($item))->toArray()));
            }

            if ($kk === 'channelMainIds') {
                $channelMain = $result->get('channel_main_id');
                if (empty($channelMain)) continue;
                $result->put('channel_main_id', array_values($channelMain->whereIn('key', Arr::wrap($item))->toArray()));
            }
        }

        return $this->success(
            $result->toArray()
        );
    }

    /**
     * @return array
     * @throws \RedisException
     */
    public function packageInfoAction(): array
    {
        $packageId = \Plus::$app->request->getValue('package_id', null);

        if (empty($packageId)) {
            return $this->error('缺少包号ID');
        }

        $packageInfo  = TbPackageDetailConf::getInstance()->getInfoByPackageId($packageId);
        $packageInfo  = array_column($packageInfo, null, 'package_id');
        $planChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');

        foreach ($packageInfo as &$foo) {
            $channel = $foo['channel_id'] ?: 0;

            if (in_array($channel, $planChannels)) {
                $foo['enable_upload_conf'] = true;
            }
            else {
                $foo['enable_upload_conf'] = false;
            }
        }

        return $this->success($packageInfo);
    }

    /**
     * @return array
     */
    public function channelListAction(): array
    {
        $tags    = Arr::wrap(\Plus::$app->request->getValue('tag_id'));
        $options = [];
        if (!empty($tags)) {
            $options['tags'] = $tags;
        }

        $info = (new GeneralOptionServ())->getChannelInfo($options);

        return $this->success(Arr::crossMap($info, ['channel_id' => 'key', 'channel_name' => 'val'], false, true));
    }
}
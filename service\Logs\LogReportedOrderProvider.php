<?php

namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;
use app\util\Common;
use Smarty\Exception;

/**
 * 订单查询
 */
class LogReportedOrderProvider
{
    const RESULT_INFO    = 1;
    const RESULT_SUMMARY = 2;
    const RESULT_TOTAL   = 4;
    const RESULT_ALL     = 7;

    /**
     * 对应看板数据查询
     *
     * @param array $params     搜索条件
     * @param array $paginate   页码
     * @param array $sort       排序
     * @param int   $resultMode 返回结果模式
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function getDashboardData(
        array $params,
        array $paginate = [],
        array $sort = [],
        int   $resultMode = self::RESULT_ALL
    ): array
    {
        $result = [];
        $db     = $this->getConn();

        if ($resultMode & self::RESULT_INFO) {
            $infoTpl = \Plus::$app->sqlTemplates->createTemplate('sql/logs/reported_paid_log/dash.tpl');

            $infoTpl->assign('params', $params);

            if (!empty($paginate)) {
                $infoTpl->assign('paginate', $paginate);
            }

            if (!empty($sort)) {
                $infoTpl->assign('sorts', $sort);
            }

            $infoSQL = $infoTpl->fetch();
            @Common::dumpSql($infoSQL);
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }

        if ($resultMode & self::RESULT_TOTAL) {
            $totalTpl = \Plus::$app->sqlTemplates->createTemplate('sql/logs/reported_paid_log/dash_total.tpl');
            $totalTpl->assign('params', $params);
            if (!empty($sort)) {
                $totalTpl->assign('sorts', $sort);
            }

            $totalSQL = $totalTpl->fetch();
            @Common::dumpSql($totalSQL);
            $result['total'] = $db->query($totalSQL)->fetch()['total_count'] ?? 0;
        }

        return $result;
    }

    /**
     * 获取数据连接
     *
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('doris_entrance');
    }
}
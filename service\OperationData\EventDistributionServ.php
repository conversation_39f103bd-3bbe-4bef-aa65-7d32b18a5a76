<?php

namespace app\service\OperationData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\TableCollect as AdTableCollect;
use app\service\AdvertiserData\Components\Matcher\EventDistributionMatch;
use app\service\OperationData\Components\Helpers\TableCollect;
use app\util\Common;
use Cycle\ORM\Select\QueryBuilder;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Query\SelectQuery;


/**
 * 用户分布类型数据查询
 *
 */
class EventDistributionServ
{
    const QB_MODE_ALL          = 99;
    const QB_MODE_CHANNEL_BASE = 2;
    const QB_MODE_POWER        = 1;
    const DIMENSION_FIRST_LOG  = 1;
    const DIMENSION_ADVERTISE  = 2;
    const MODE_ALL             = 3;
    const MODE_SUMMARY         = 2;
    const MODE_INFO            = 1;
    const ROW_MODE_SUMMARY     = 2;
    const ROW_MODE_INFO        = 1;


    /**
     * 列表数据查询
     *
     * @param int   $dimension (指标维度)DIMENSION_FIRST_LOG || DIMENSION_ADVERTISE
     * @param array $params    搜索条件
     * @param array $groups    汇总选项
     * @param array $paginate  分页
     * @param array $sort      排序
     * @param int   $mode      数据模式
     *
     * @return array
     */
    public function getList(
        int $dimension, array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $allMode       = self::MODE_ALL | self::MODE_SUMMARY | self::MODE_INFO;
        $parameterMode = $mode & $allMode;
        $result        = [];

        if ($dimension === self::DIMENSION_FIRST_LOG) {
            $qb = $this->getQueryBuilder($dimension, self::QB_MODE_ALL ^ self::QB_MODE_CHANNEL_BASE);
        }
        elseif ($dimension === self::DIMENSION_ADVERTISE) {
            $qb = $this->getQueryBuilder($dimension);
        }
        else {
            throw new \RuntimeException('暂时不支持该标识的维度数据');
        }

        $eventType     = $params['event_type'] ?? 1;
        $groupType     = $params['group_type'] ?? 1;
        $columnOptions = ['group_type' => $params['group_type'] ?? 1];

        if ($groupType == 3) {
            $columnOptions['group_type'] = $params['group_type'] = 1;
        }

        if ($eventType == 991) {
            $columnOptions['event_type'] = $params['event_type'] = [2, 1];
        }
        elseif ($eventType == 992) {
            $columnOptions['event_type'] = $params['event_type'] = [3, 1];
        }

        // 拼接 where 搜索条件
        $matcher = new EventDistributionMatch($this->reflectFieldMap($dimension));
        $matcher->exec($qb, $params);

        if ($parameterMode & self::MODE_INFO) {
            $infoQb = clone $qb;

            if (!empty($groups)) {
                $reflectGroups = $this->changeGroupReflect($groups);
                foreach ($reflectGroups as $g) {
                    $infoQb->groupBy($g);
                }
                $columnOptions['intersect'] = $groups;
            }

            $infoCols = $this->getColumns($dimension, static::ROW_MODE_INFO, $columnOptions);
            $infoQb->columns(array_values($infoCols));

            if (!empty($sort)) $infoQb->orderBy($sort);

            $notPageInfoQb = clone $infoQb;

            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;
                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }
            //打印sql调试
            Common::dumpSql($infoQb->__toString());
            $result['list']  = $infoQb->fetchAll();
            $chillDb         = $this->getConn();
            $result['total'] = $chillDb
                ->select()
                ->from(new Fragment('(' . $notPageInfoQb->__toString() . ') as totalBody'))
                ->count();
        }

        if ($parameterMode & self::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            $summaryQb->columns($this->getColumns($dimension, static::ROW_MODE_SUMMARY, $columnOptions));
            //打印sql调试
            Common::dumpSql($summaryQb->__toString());
            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * 获取对应的基础queryBuilder
     *
     * @param int $type static::DIMENSION_FIRST_LOG || DIMENSION_ADVERTISE
     * @param int $mode
     *                  - MODE_ALL or MODE_POWER or MODE_CHANNEL_BASE
     *                  - MODE_ALL ^ MODE_CHANNEL_BASE
     *                  - MODE_ALL ^ MODE_POWER...
     *
     * @return \Spiral\Database\Query\SelectQuery
     */
    private function getQueryBuilder(int $type, int $mode = -1): \Spiral\Database\Query\SelectQuery
    {
        $allMode       = self::QB_MODE_ALL | self::QB_MODE_POWER | self::QB_MODE_CHANNEL_BASE;
        $db            = $this->getConn();
        $qb            = $db->select();
        $parameterMode = $mode & $allMode;

        if ($type === self::DIMENSION_ADVERTISE) {
            $qb->from(AdTableCollect::DDC_AD_EVENT_DISTRIBUTION . ' as t_base');
        }
        elseif ($type === self::DIMENSION_FIRST_LOG) {
            $qb->from(TableCollect::DDC_FIRST_EVENT_DISTRIBUTION . ' as t_base');
        }
        else {
            throw new \RuntimeException('暂时不支持该标识的维度数据');
        }

        /**
         * 关联 package_detail
         */
        if ($parameterMode & self::QB_MODE_POWER) {
            $powerSql = str_replace(' POWER', '', \Plus::$service->admin->getAdminPowerSql());
            $qb
                ->innerJoin(new Fragment($powerSql), 'power')
                ->on('t_base.package_id', 'power.package_id');
        }

        /**
         * 关联 tb_base_channel_conf
         */
        if ($parameterMode & self::QB_MODE_CHANNEL_BASE) {
            $qb
                ->leftJoin(AdTableCollect::BASE_CHANNEL_CONF, 'base_channel')
                ->on('t_base.channel_id', 'base_channel.channel_id');
        }

        return $qb;
    }

    /**
     * 获取数据库连接
     *
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
        // todo: test doris connect
//        return FakeDB::connection('ddc_doris');
    }

    /**
     * @param int $dimension
     *
     * @return array
     */
    private function reflectFieldMap(int $dimension = self::DIMENSION_FIRST_LOG): array
    {
        $fieldMap = [];
        if ($dimension === self::DIMENSION_FIRST_LOG) {
            $fieldMap = [
                'tday'            => 't_base.tday',
                'package_id'      => 't_base.package_id',
                'cp_game_id'      => 't_base.cp_game_id',
                'game_id'         => 't_base.game_id',
                'channel_id'      => 'power.channel_id',
                'channel_main_id' => 'power.channel_main_id ',
                'platform_id'     => 'power.platform_id',
                'promotion_id'    => 'power.popularize_v2_id',
                'report_type'     => 't_base.report_type',
                'event_type'      => 't_base.event_type',
                'group_type'      => 't_base.group_type',
            ];
        }
        elseif ($dimension === self::DIMENSION_ADVERTISE) {
            $fieldMap = [
                'tday'            => 't_base.tday',
                'package_id'      => 't_base.package_id',
                'cp_game_id'      => 't_base.cp_game_id',
                'game_id'         => 't_base.game_id',
                'channel_id'      => [
                    't_base.channel_id',
                    'power.channel_id',
                ],
                'channel_main_id' => [
                    'base_channel.channel_main_id',
                    'power.channel_main_id',
                ],
                'platform_id'     => 'power.platform_id',
                'promotion_id'    => 'power.popularize_v2_id',
                'report_type'     => 't_base.report_type',
                'event_type'      => 't_base.event_type',
                'group_type'      => 't_base.group_type',
            ];
        }

        return $fieldMap;
    }

    /**
     *
     *
     * @param       $dimension
     * @param int   $mode
     * @param array $options
     *
     * @return array|string[]
     */
    private function getColumns($dimension, int $mode = -1, array $options = []): array
    {
        $effectiveRange = $options['effective_range'] ?? []; // 有效的列展示范围
        $calcCols       = [];

        $fixedCols = [
            'tday'         => 't_base.tday as tday',
            'cp_game_id'   => 'power.cp_game_id as cp_game_id',
            'game_id'      => 'power.game_id as game_id',
            'package_id'   => 't_base.package_id as package_id',
            'platform_id'  => 'power.platform_id as platform_id',
            'promotion_id' => 'power.popularize_v2_id as promotion_id',
        ];

        if (self::DIMENSION_FIRST_LOG === $dimension) {
            $fixedCols['channel_id']      = 'power.channel_id as t_channel_id';
            $fixedCols['channel_main_id'] = 'power.channel_main_id as t_channel_main_id';
        }
        elseif (self::DIMENSION_ADVERTISE === $dimension) {
            $fixedCols['channel_id']      = new Fragment(
                'IF(COALESCE(t_base.channel_id, 0) != 0, t_base.channel_id, power.channel_id) as t_channel_id'
            );
            $fixedCols['channel_main_id'] = new Fragment(
                'IF(COALESCE(base_channel.channel_main_id, 0) != 0, base_channel.channel_main_id, power.channel_main_id) as t_channel_main_id'
            );
        }

        if (!empty($options['intersect'])) {
            $fixedCols = array_intersect_key($fixedCols, array_flip($options['intersect']));
        }

        if (empty($effectiveRange)) {
            $groupType = $options['group_type'] ?? 1; // 默认按天

            if ($groupType == 2) {
                // 自然月
                $effectiveRange = [0, 24];
            }
            else {
                $effectiveRange = [0, 60];
            }
        }

        $eventType      = $options['event_type'] ?? 1;
        $paramGroupType = $options['group_type'] ?? 1;
        $eventWhere     = [];
        $sumEventWhere  = [];

        if (is_array($paramGroupType)) {
            [$sumGroup, $nodeGroup] = $paramGroupType;
            $sumEventWhere['x'] = ['t_base.group_type=' . $sumGroup];
            $eventWhere['x']    = ['t_base.group_type=' . $nodeGroup];
        }

        if (is_array($eventType)) {
            [$xAs, $yAs] = $eventType;
            $sumEventWhere['y']   = $sumEventWhere['x'] ?? [];
            $eventWhere['y']      = $eventWhere['x'] ?? [];
            $eventWhere['x'][]    = 't_base.event_type=' . $xAs;
            $eventWhere['y'][]    = 't_base.event_type=' . $yAs;
            $sumEventWhere['x'][] = 't_base.event_type=' . $xAs;
            $sumEventWhere['y'][] = 't_base.event_type=' . $yAs;
        }

        if (count($eventWhere) == 1 || count($sumEventWhere) == 1) {
            $summaryColWhere = array_shift($sumEventWhere);
            $colWhere        = array_shift($eventWhere);
            $eventSum        = implode(' and ', $summaryColWhere);

            $calcCols['event_sum'] = new Fragment("COALESCE(SUM(IF({$eventSum}, num, 0)), 0) as event_sum");
            for ($i = $effectiveRange[0]; $i <= $effectiveRange[1]; $i++) {
                $simpleWhere        = $colWhere;
                $nodeKey            = 'node_' . $i;
                $simpleWhere[]      = 't_base.day_type=' . $i;
                $nodeWhere          = implode(' and ', $simpleWhere);
                $calcCols[$nodeKey] = new Fragment("COALESCE(SUM(IF({$nodeWhere}, num, 0)),0) as {$nodeKey}");
            }
            $lastWhere     = $colWhere;
            $lastWhere[]   = "t_base.day_type not between {$effectiveRange[0]} and {$effectiveRange[1]}";
            $lastNodeWhere = implode(' and ', $lastWhere);

            $calcCols['node_1000'] = new Fragment("COALESCE(SUM(IF({$lastNodeWhere}, num, 0)), 0) as node_1000");
        }
        elseif (count($eventWhere) > 1 || count($sumEventWhere) > 1) {
            ['x' => $xNodeWhere, 'y' => $yNodeWhere] = $eventWhere;
            ['x' => $xSumWhere, 'y' => $ySumWhere] = $sumEventWhere;

            $xSumWhere               = implode(' and ', $xSumWhere);
            $ySumWhere               = implode(' and ', $ySumWhere);
            $calcCols['x_event_sum'] = new Fragment("COALESCE(SUM(IF({$xSumWhere}, num, 0))) as x_event_sum");
            $calcCols['y_event_sum'] = new Fragment("COALESCE(SUM(IF({$ySumWhere}, num , 0))) as y_event_sum");

            for ($i = $effectiveRange[0]; $i <= $effectiveRange[1]; $i++) {
                $nodeKey  = 'node_' . $i;
                $chillX   = $xNodeWhere;
                $chillY   = $yNodeWhere;
                $chillX[] = 't_base.day_type=' . $i;
                $chillY[] = 't_base.day_type=' . $i;
                $chillX   = implode(' and ', $chillX);
                $chillY   = implode(' and ', $chillY);

                $calcCols['x_' . $nodeKey] = new Fragment("COALESCE(SUM(IF({$chillX}, NUM, 0)),0) as x_{$nodeKey}");
                $calcCols['y_' . $nodeKey] = new Fragment("COALESCE(SUM(IF({$chillY}, NUM, 0)),0) as y_{$nodeKey}");
            }

            $lastX = $xNodeWhere;
            $lastY = $yNodeWhere;

            $lastX[] = $lastY[] = "t_base.day_type not between {$effectiveRange[0]} and {$effectiveRange[1]}";

            $lastX = implode(' and ', $lastX);
            $lastY = implode(' and ', $lastY);

            $calcCols['x_node_1000'] = new Fragment("COALESCE(SUM(IF({$lastX}, NUM, 0)), 0) as x_node_1000");
            $calcCols['y_node_1000'] = new Fragment("COALESCE(SUM(IF({$lastY}, NUM, 0)), 0) as y_node_1000");
        }
        else {
            $calcCols['event_sum'] = new Fragment('COALESCE(SUM(num), 0) as event_sum');

            for ($i = $effectiveRange[0]; $i <= $effectiveRange[1]; $i++) {
                $nodeKey            = 'node_' . $i;
                $calcCols[$nodeKey] = new Fragment("COALESCE(SUM(IF(t_base.DAY_TYPE = {$i}, NUM, 0)),0) as {$nodeKey}");
            }

            $calcCols['node_1000'] = new Fragment("COALESCE(SUM(IF(t_base.DAY_TYPE not between {$effectiveRange[0]} and {$effectiveRange[1]}, NUM, 0)), 0) as node_1000");
        }

        $fields = [];

        if ($mode === static::ROW_MODE_INFO) {
            $fields = array_merge($fixedCols, $calcCols);
        }
        elseif ($mode === static::ROW_MODE_SUMMARY) {
            $fields = $calcCols;
        }

        return $fields;
    }

    /**
     * @param array $groups
     *
     * @return array
     */
    private function changeGroupReflect(array $groups): array
    {
        $reflectMap = [
            'channel_id'      => 't_channel_id',
            'channel_main_id' => 't_channel_main_id',
            'tday'            => 't_base.tday',
            'cp_game_id'      => 'power.cp_game_id',
            'game_id'         => 'power.game_id',
            'package_id'      => 't_base.package_id',
        ];

        foreach ($groups as &$g) {
            if (isset($reflectMap[$g])) {
                $g = $reflectMap[$g];
            }
        }

        return $groups;
    }
}
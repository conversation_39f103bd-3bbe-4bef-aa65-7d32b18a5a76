<?php
/**
 * 哔哩哔哩数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class <PERSON><PERSON>bili extends AdBaseInterface
{
    const CONVERT_ACTIVE   = 'APP_FIRST_ACTIVE';
    const CONVERT_REGISTER = 'USER_REGISTER';
    const CONVERT_PURCHASE = 'USER_COST';

    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY');
    }


    /**
     * 公共上报
     * @param $info
     * @param $type
     */
    private function upload($info, $type)
    {
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'bilibili';
        $logInfo['log_type']     = 'reported_platform_log';
        $callbackUrl             = "https://cm.bilibili.com/conv/api/conversion/ad/cb/v1?";

        if (strstr($info['CALLBACK_URL'], $callbackUrl) !== false) {
            $callbackUrl = '';
        }

        switch ($type) {
            case 'ACTIVE':
                $callbackUrl .= $info['CALLBACK_URL'] . "&conv_type=" . self::CONVERT_ACTIVE . "&conv_time=" . (time() * 1000);
                break;
            case 'REG':
                $conv_type    = 'FORM_SUBMIT';
                $callbackUrl .= $info['CALLBACK_URL'] . "&conv_type=" . $conv_type . "&conv_time=" . (time() * 1000);
                break;
            case 'PAY':
                $callbackUrl .= $info['CALLBACK_URL'] . "&conv_type=" . self::CONVERT_PURCHASE . "&conv_time=" . (time() * 1000) . "&conv_value=" . ($info["MONEY"] * 100);
                break;
        }
        $logInfo['request'] = json_encode(['url' => $callbackUrl]);

        $http = new Http($callbackUrl);
        $res  = $http->get();
        //记录上报结果
        $logInfo['response'] = $res;
        $resArr              = json_decode($res, true);
        if (isset($resArr['code']) && $resArr['code'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        $this->log($info, $logInfo, $res, $callbackUrl);
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

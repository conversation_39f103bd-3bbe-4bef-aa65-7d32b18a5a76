<?php

namespace app\apps\revenue\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\service\ConfigService\BasicServ;
use app\service\Revenue\CostServ;
use app\service\Revenue\IncomeServ;
use app\service\Revenue\ProfitsServ;
use Plus\MVC\Controller\JsonController;

/**
 *
 */
class ManagerController extends BaseTableController
{
    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function data(Collection $params): array
    {
        // TODO: Implement data() method.
        return [];
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        // TODO: Implement fields() method.
        return [];
    }

    /**
     * 营收看板概览
     *
     * @return array
     * @throws \Exception
     */
    public function overviewAction(): array
    {
        $request      = $this->wrapParams(\Plus::$app->request);
        $options      = $request->toArray();
        $incomeParams = array_merge($options, ['type' => [1, 2], 'is_remove' => 0]);
        $costParams   = array_merge($options, ['status' => [0, 1]]);

        $incomeInfo = (new IncomeServ())->fetchIncomeDaily($incomeParams);
        $costInfo   = (new CostServ())->getOverallCostInfo($costParams);
        $incomeInfo = array_column($incomeInfo, null, 'tday');
        $costInfo   = array_column($costInfo, null, 'tday');
        $infoRe     = array_merge_recursive($incomeInfo, $costInfo);

        foreach ($infoRe as $k => &$item) {
            $item['tday'] = $k;

            $item['cost_discount'] = round($item['cost_discount'] ?? 0);
            $item['amount']        = round($item['amount'] ?? 0);
            $item['recharge']      = round($item['recharge'] ?? 0);
            $item['balance']       = round($item['amount'] - $item['cost_discount']);
        }

        $totalAmount       = round(array_sum(array_column($infoRe, 'amount')));        // 业务收入
        $totalCostDiscount = round(array_sum(array_column($infoRe, 'cost_discount'))); // 返点后消耗数据
        $totalBalance      = round(array_sum(array_column($infoRe, 'balance')));       // 结余

        $config = [
            ['prop' => 'tday', 'label' => '时间'],
            [
                'prop'   => 'amount',
                'label'  => '业务收入',
                'num'    => $totalAmount,
                'series' => [
                    'type' => 'line',
                ],
            ],
            [
                'prop'   => 'cost_discount',
                'label'  => '返点后消耗金额',
                'num'    => $totalCostDiscount,
                'series' => [
                    'type' => 'line',
                ],
            ],
            [
                'prop'   => 'balance',
                'label'  => '结余',
                'num'    => $totalBalance,
                'series' => [
                    'type' => 'line',
                ],
            ],
        ];

        return $this->success([
            'config' => $config,
            'list'   => array_values($infoRe),
        ]);
    }


    /**
     * @return array
     * @throws \Exception
     */
    public function rechargeInfoAction(): array
    {
        $request = $this->wrapParams(\Plus::$app->request);
        $options = $request->toArray();
        $methods = $request->get('method', ['fields', 'data']);
        $result  = [];

        if (empty($options['type'])) {
            $options['type'] = 1;
        }

        if (in_array('data', $methods)) {
            $rechargeRe      = (new IncomeServ())->fetchRechargeInfo($options);
            $rechargeSummary = &$rechargeRe['summary'];

            foreach ($rechargeRe['list'] as &$foo) {
                $foo['other_channel']      = round(($foo['other_channel_normal'] ?? 0) + ($foo["other_channel_special"] ?? 0), 2);
                $foo['other']              += (($foo['yyb_special_first'] ?? 0) + ($foo['yyb_normal'] ?? 0));
                $foo['other']              = round($foo['other'], 2);
                $foo['order_total_amount'] = round(($foo['sum'] ?? 0) + ($foo['coin_deduct'] ?? 0) + ($foo['coupon_deduct'] ?? 0), 2);
            }

            $rechargeSummary['other_channel']      = round(($rechargeSummary['other_channel_normal'] ?? 0) + ($rechargeSummary["other_channel_special"] ?? 0), 2);
            $rechargeSummary['other']              += (($rechargeSummary['yyb_special_first'] ?? 0) + ($rechargeSummary["yyb_normal"] ?? 0));
            $rechargeSummary['other']              = round($rechargeSummary['other'], 2);
            $rechargeSummary['order_total_amount'] = round(($rechargeSummary['sum'] ?? 0) + ($rechargeSummary['coin_deduct'] ?? 0) + ($rechargeSummary['coupon_deduct'] ?? 0), 2);

            unset(
                $rechargeSummary['tday'],
                $rechargeSummary['yyb_special_first'],
                $rechargeSummary['yyb_normal']
            );

            $result = array_merge($result, $rechargeRe);
        }

        if (in_array('fields', $methods)) {
            $fields = [
                ['title' => '统计日期', 'dataIndex' => 'tday'],
                //                ['title' => '总充值', 'dataIndex' => 'sum'],
                ['title' => '总充值', 'dataIndex' => 'sum', 'tip' => '总充值=游戏内现金充值总金额',],
                ['title' => '手盟充值', 'dataIndex' => 'shoumeng'],
                ['title' => '应用宝手盟', 'dataIndex' => 'yyb_shoumeng'],
                ['title' => 'ios手盟', 'dataIndex' => 'ios_shoumeng'],
                //                ['title' => '应用宝(8:92)', 'dataIndex' => 'yyb_special_first'],
                //                ['title' => '应用宝(4:6)', 'dataIndex' => 'yyb_normal'],
                ['title' => 'ios充值', 'dataIndex' => 'ios'],
                ['title' => '第三方渠道', 'dataIndex' => 'other_channel'],
                ['title' => '外放联运', 'dataIndex' => 'wfly'],
                ['title' => '微信广点通(手盟包)', 'dataIndex' => 'gdt_weixin'],
                ['title' => '组合支付现金充值', 'dataIndex' => 'coin', 'tip' => '组合支付中的现金充值金额'],
                ['title' => '其他充值', 'dataIndex' => 'other', 'tip' => '其他类型的充值汇总(如应用宝4:6, 应用宝8:92)'],
                ['title' => '代金券抵扣', 'dataIndex' => 'coupon_deduct', 'tip' => '组合支付中代金券抵扣的金额'],
                ['title' => '九玩币抵扣', 'dataIndex' => 'coin_deduct', 'tip' => '组合支付中的九玩币金额'],
                ['title' => '订单总金额', 'dataIndex' => 'order_total_amount', 'tip' => '订单总金额=总充值+代金券抵扣+九玩币抵扣'],
            ];

            $result['fields'] = $fields;
        }

        return $this->success($result);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function costInfoAction(): array
    {
        $request         = $this->wrapParams(\Plus::$app->request);
        $options         = $request->toArray();
        $methods         = $request->get('method', ['fields', 'data']);
        $options['mode'] = 1;

        $costRe = (new CostServ())->getOverallCostInfo($options, ['tday', 'base_channel.channel_main_id']);

        $ranking  = [];
        $result   = [];
        $costType = $options['cost_type'] ?? 'cost';


        // 按天排列
        foreach ($costRe as $item) {
            $channel = $item['channel_main_id'] ?? 0;
            $tDay    = $item['tday'] ?? '';
            $index   = 'channel_' . $channel;

            if (!isset($result[$tDay])) {
                $result[$tDay] = [];
            }
            $chill   = &$result[$tDay];
            $costNum = round($item[$costType] ?? 0.00);

            isset($chill[$index])
                ? $chill[$index] += $costNum
                : $chill[$index] = $costNum;

            isset($ranking[$channel])
                ? $ranking[$channel] += $costNum
                : $ranking[$channel] = $costNum;
        }
        unset($chill);

        foreach ($result as $k => &$foo) {
            $foo['summary_cost'] = array_sum($foo);
            $foo['tday']         = $k;
        }
        unset($foo);

        $list = array_values($result);

        $channelMap = (new BasicServ())->getMultiOptions(['channel_main_id',]);
        $channelMap = array_column($channelMap->get('channel_main_id')->toArray(), 'val', 'key');

        arsort($ranking);
        $summaryRow = ['summary_cost' => 0];

        $fields = [
            ['title' => '时间', 'dataIndex' => 'tday'],
            ['title' => '总消耗', 'dataIndex' => 'summary_cost'],
        ];

        foreach ($ranking as $i => $chill) {
            $channelIndex = 'channel_' . $i;
            $fields[]     = ['title' => $channelMap[$i] ?? '', 'dataIndex' => 'channel_' . $i];

            $num = round($chill);

            $summaryRow[$channelIndex]  = $num;
            $summaryRow['summary_cost'] += $num;
        }

        return $this->success([
            'fields'  => $fields,
            'list'    => $list,
            'summary' => $summaryRow,
        ]);
    }


}
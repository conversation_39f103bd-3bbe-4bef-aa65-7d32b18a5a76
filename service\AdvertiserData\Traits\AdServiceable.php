<?php

namespace app\service\AdvertiserData\Traits;

use app\service\AdvertiserData\Components\Helpers\JoinClause;

trait AdServiceable
{
    /**
     * @param       $relateTable
     * @param array $rangeDate
     *
     * @return JoinClause
     */
    protected function getAccountJoins($relateTable, array $rangeDate = []): JoinClause
    {
        if (empty($rangeDate)) {
            $today     = date('Y-m-d');
            $rangeDate = [$today, $today];
        }

        $sql = "
        select a.ID, a.account_id,a.ad_account, b.adv_user_id, b.department_id
        from base_conf_platform.tb_ad_account_conf a
                 join base_conf_platform.tb_ad_account_ext_conf b on a.ID = b.AD_ACCOUNT_ID
        where b.START_DATE <= '{$rangeDate[0]}'
          and b.END_DATE >= '{$rangeDate[1]}'
        group by ACCOUNT_ID";

        return
            (new JoinClause('left', $sql, 'base_account'))
                ->on('base_account.account_id', '=', $relateTable . '.account_id');
    }

    /**
     * @param $relateTable
     *
     * @return JoinClause
     */
    protected function joinAccount($relateTable): JoinClause
    {
        return (new JoinClause('left', 'base_conf_platform.tb_ad_account_conf', 'base_account'))
            ->on('base_account.account_id', '=', $relateTable . '.account_id')
            ->on('base_account.status', '=', '1');
    }
}
<?php

namespace app\service\ConfigService\Tables;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\Mather;
use app\service\ConfigService\Constants\FieldTag;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;


class FirstLoginLtv implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    /**
     * @throws \Exception
     */
    public function getFields($options = null): Collection
    {
        $rangeDateDimension = (int)($options['range_date_dimension'] ?? 2);

        $today = new \DateTime();

        $rangeNode1 = range(1, 60);
        $rangeNode2 = [];

        if (!empty($options['cp_game_id'])) {
            if (count($options['cp_game_id']) == 1) {
                $cpGameId = $options['cp_game_id'][0];
                if ($cpGameId == 789) {
                    $rangeNode2 = range(31, 60);
                }
            }
        }

        $NNodes = array_merge(
            $rangeNode1, $rangeNode2, Mather::findNumInScope(60, 720, 30)
        );

        $collect = $this->getBaseFields(['baseOperationCollect']);

        $collect = $collect->merge([
            'firstlogin_user'    => ['title' => '首登用户', 'sorter' => 'true'],
            'cost_discount'      => ['title' => '返点后消耗金额', 'sorter' => true, 'width' => 150],
            'new_user_cost'      => ['title' => '新用户成本'],
            'new_user_total_pay' => ['title' => '新用户累计付费'],
        ]);

        $groups = $options['groups'];

        if (in_array('package_id', $groups)) {
            $collect = $collect->merge([
                'package_tags' => ['title' => '包号标签', 'classify' => ['attrs', 'tags']]
            ]);
        }

        if (
            in_array('package_id', $groups)
            || in_array('channel_id', $groups)
        ) {
            $collect = $collect->merge([
                'channel_tags' => ['title' => '推广子渠道标签', 'classify' => ['attrs', 'tags']]
            ]);
        }

        $collect = $collect->merge([
            'total_ltv' => ['title' => '累计LTV'],
            'total_roi' => ['title' => '累计ROI'],
        ]);

        // $nDays = days_apart($today, $options['range_date_start'] ?? $today);
        $nDays = 720;

        if (!empty($options['pay_days_num'])) {
            $nDays = $options['pay_days_num'];
        }

        $ltvFields = $this->ltvNCollect(Mather::findIn($nDays, $NNodes));
        $ltvFields = FieldTag::tagClassifyToNField($ltvFields, 'ltv',
            [
                ['range' => [1, 44], 'classify' => ['attrs', FieldTag::LTV_GROUP_1]],
                ['range' => [45, 180], 'classify' => ['attrs', FieldTag::LTV_GROUP_2]],
                ['range' => [210, 360], 'classify' => ['attrs', FieldTag::LTV_GROUP_3]],
                ['range' => [390, 720], 'classify' => ['attrs', FieldTag::LTV_GROUP_4]],
            ]);

        $roiFields = $this->roiNCollect(Mather::findIn($nDays, $NNodes));
        $roiFields = FieldTag::tagClassifyToNField($roiFields, 'roi', [
            ['range' => [1, 44], 'classify' => ['attrs', FieldTag::ROI_GROUP_1]],
            ['range' => [45, 180], 'classify' => ['attrs', FieldTag::ROI_GROUP_2]],
            ['range' => [210, 360], 'classify' => ['attrs', FieldTag::ROI_GROUP_3]],
            ['range' => [390, 720], 'classify' => ['attrs', FieldTag::ROI_GROUP_4]],
        ]);

        $collect = $collect->merge($ltvFields)->merge($roiFields);

        return $this->formatStandard($collect);
    }

    /**
     * @param $options
     *
     * @return Collection
     */
    public function getSimpleFields($options = null): Collection
    {
        $columnScope = Arr::get($options, 'column_scope');
        $today       = new \DateTime();
        $collect     = collect();
        $collect     = $collect->merge([
            'tday'     => ['title' => '统计日期'],
            'new_user' => ['title' => '新增用户'],
        ]);

        if (in_array('ltv', $columnScope)) {
            $collect = $collect->merge([
                'total_ltv' => ['title' => '累计LTV'],
            ]);

            $collect = $collect->merge($this->ltvNCollect(7));
        }

        if (in_array('roi', $columnScope)) {
            $collect = $collect->merge([
                'total_roi' => ['title' => '累计ROI'],
            ]);

            $collect = $collect->merge($this->roiNCollect(7));
        }

        return $this->formatStandard($collect);
    }
}
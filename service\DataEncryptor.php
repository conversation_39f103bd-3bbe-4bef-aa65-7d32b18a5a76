<?php

namespace app\service;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use Plus\Net\Http;

/**
 * 数据加密、解密服务
 * phpcs:disable
 */
class DataEncryptor extends \Plus\Service\Children
{
    private const URI                         = 'http://crypt.910app.com/api/';
    private const NAME                        = 'dataspy';
    private const KEY                         = 'k8OxUbojXz79uxJh';
    private const DB_INDEX                    = 3;
    private const CACHE_ENCRYPT_PREFIX        = 'DATA_ENCRYPT_';
    private const CACHE_DECRYPT_PREFIX        = 'DATA_DECRYPT_';
    private const CACHE_DEVICE_MAP_PREFIX     = 'DEVICE_MAP_';
    private const CACHE_DEVICE_MAP_MD5_PREFIX = 'DEVICE_MAP_MD5_';
    private const CACHE_DEVICE_MAP_ID_PREFIX  = 'DEVICE_MAP_ID_';


    public function __construct()
    {

    }

    /**
     * @param     $data
     * @param int $isCache
     *
     * @return array
     * @throws \RedisException
     * @throws \Exception
     */
    public function encrypt($data, int $isCache = 0): array
    {
        $data    = Arr::wrap($data);
        $dataTmp =
        $dataMap = [];

        foreach ($data as $foo) {
            if (
                substr($foo, 0, 3) != 'id_'
                && substr($foo, 0, 4) != 'aes_'
                && !empty($foo)
            ) {
                $dataTmp[] = $foo;
            }
            else {
                $dataMap[$foo] = $foo;
            }
        }

        $data = $dataTmp;

        if ($isCache == 1) {
            foreach ($data as $k => $ff) {
                $cache = \Plus::$app->redis82->get(static::CACHE_DECRYPT_PREFIX . $ff);
                if ($cache) {
                    $dataMap[$ff] = $cache;
                    unset($data[$k]);
                }
            }
        }

        if (!empty($data)) {
            // 调用加密接口：最多三次
            for ($i = 1; $i <= 3; $i++) {
                $now      = \time();
                $req      = new Http(static::URI . 'encryption');
                $response = $req->postJson([
                    'data' => $data,
                    'name' => static::NAME,
                    'sign' => \md5(self::NAME . $now . static::KEY),
                    'time' => $now,
                ]);

                $result = \json_decode($response, true);
                if ($result['code'] == 1) {
                    break;
                }
            }

            if ($result['code'] == 1) {
                $encryptDataMap = array_combine($data, $result['data']);

                if ($isCache == 1) {
                    foreach ($encryptDataMap as $k => $f) {
                        \Plus::$app->redis82->setex(self::CACHE_ENCRYPT_PREFIX . $k, 2592000, $f);
                    }
                }
                $dataMap = $dataMap + $encryptDataMap;
            }
            else {
                $dataMap = $dataMap + array_combine($data, $data);
            }
        }

        return $dataMap;
    }

    /**
     * @param     $encryptData
     * @param int $type
     * @param int $isCache
     *
     * @return array
     * @throws \RedisException
     */
    public function decrypt($encryptData, int $type = 2, int $isCache = 0): array
    {
        $encryptData     = Arr::wrap($encryptData);
        $encryptDataTemp =
        $dataMap = [];

        foreach ($encryptData as $value) {
            if (substr($value, 0, 4) == 'aes_') {
                $encryptDataTemp[] = $value;
            }
            elseif (substr($value, 0, 3) == 'id_') {
                $dataMap[$value] = $this->getDeviceMap($value, $type, $isCache);
            }
            else {
                $dataMap[$value] = $value;
            }
        }
        $encryptData = $encryptDataTemp;

        if ($isCache == 1) {
            foreach ($encryptData as $key => $value) {
                $cache = \Plus::$app->redis82->get(self::CACHE_DECRYPT_PREFIX . $value);

                if ($cache) {
                    $dataMap[$value] = $cache;
                    unset($encryptData);
                }
            }
        }

        if (!empty($encryptData)) {
            for ($i = 1; $i <= 3; $i++) {
                $now      = \time();
                $response = (new Http(self::URI . 'decryption'))
                    ->postJson([
                        'data' => $encryptData,
                        'name' => self::NAME,
                        'sign' => \md5(self::NAME . $now . self::KEY),
                        'time' => $now,
                    ]);
                $result   = \json_decode($response, true);
                if (isset($result['code']) && $result['code'] == 1) {
                    break;
                }
            }

            if (isset($result['code']) && $result['code'] == 1) {
                $decryptDataMap = array_combine($encryptData, $result['data']);

                if ($isCache == 1) {
                    foreach ($decryptDataMap as $key => $value) {
                        \Plus::$app->redis82->setex(self::CACHE_DECRYPT_PREFIX . $key, 2592000, $value);
                    }
                }
                $dataMap = $dataMap + $decryptDataMap;
            }
            else {
                $dataMap = $dataMap + array_combine($encryptDataTemp, $encryptDataTemp);
            }
        }

        return $dataMap;
    }

    /**
     * @param     $id
     * @param int $type
     * @param int $isCache
     *
     * @return mixed|string
     * @throws \RedisException
     */
    public function getDeviceMap($id, int $type = 2, int $isCache = 1)
    {
        if (!empty($id) && \substr($id, 0, 3) == 'id_') {
            $id = \substr($id, 3);

            if ($id > 0) {
                $idKey = self::CACHE_DEVICE_MAP_ID_PREFIX . $id;
                $cache = \Plus::$app->redis82->get($idKey);
                $map   = \json_decode($cache, true);
                if (
                    !empty($cache)
                    && substr($map['DEVICE_ID'], 0, 4) != 'aes_'
                ) {
                    $return = ($type = 1) ? $map['DEVICE_ID'] : $map['MD5_DEVICE_ID'];
                }
                else {
                    $deviceMapInfo = FakeDB::connection('origin_platform')
                        ->table('tb_device_map')
                        ->select()
                        ->where('id', $id)
                        ->fetchAll();

                    $deviceMapInfo = $deviceMapInfo[0] ?? [];
                    $deviceId      = $deviceMapInfo['DEVICEID'];
                    $md5DeviceId   = $deviceMapInfo['MD5_DEVICEID'];
                    $decryptMap    = $this->decrypt([$deviceId, $md5DeviceId]);

                    if (
                        substr($decryptMap[$deviceId], 0, 4) != 'aes_'
                        && substr($decryptMap[$md5DeviceId], 0, 4) != 'aes'
                    ) {
                        $map = [
                            'DEVICE_ID'     => $decryptMap[$deviceId],
                            'MD5_DEVICE_ID' => $decryptMap[$md5DeviceId],
                        ];

                        if ($isCache == 1) {
                            \Plus::$app->redis82->setex($idKey, 2592000, \json_encode($map));
                        }
                    }

                    $return = ($type == 1) ? $decryptMap[$deviceId] : $decryptMap[$md5DeviceId];
                }

            }
            else {
                $return = '';
            }

        }
        else {
            $return = $id;
        }

        return $return;
    }

    /**
     * 异位或加密字符串
     *
     * @param $value      [需要加密或解密的字符串]
     * @param int $type   [0:加密 1:解密]
     * @param string $key [返回加密或解密的字符串]
     *
     * @return array|int|string|string[]
     */
    public function encryptionString($value, int $type = 0, string $key = "app910.@$@!@")
    {
        if (!$type) {
            return str_replace('=', '', base64_encode($value ^ $key));
        }
        //解密
        $value = base64_decode($value);

        return $value ^ $key;
    }
}
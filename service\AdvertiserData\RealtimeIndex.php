<?php

namespace app\service\AdvertiserData;

use app\models\AdpPlatform\TbAdpCampaign;
use app\models\AdpPlatform\TbAdpCreativeBase;
use app\models\AdpPlatform\TbAdpPlanBase;

class RealtimeIndex
{
    /**
     * 非正式
     *
     * @param array $options
     * @return void
     */
    public function demo(array $options = []): array
    {
        $thisDate    = date('Y-m-d');
        $powerSql    = $this->powerSql();
        $whereString = '';
        $wheres      = [];

        if (!empty($rangeDate = $options['range_date'] ?? null)) {
            sort($rangeDate);

            $wheres[] = "t1.tday between '{$rangeDate[0]}' and '{$rangeDate[1]}'";
        }
        else {
            $wheres[] = "t1.tday = '$thisDate'";
        }

        if (!empty($cpGameId = $options['cp_game_id'] ?? null)) {
            if (is_array($cpGameId)) {
                $cpGameId = implode(',', $cpGameId);
            }

            $wheres[] = "t1.CP_GAME_ID IN ({$cpGameId})";
        }

        if (!empty($gameId = $options['game_id'] ?? null)) {
            if (is_array($gameId)) {
                $gameId = implode(',', $gameId);
            }

            $wheres[] = "t1.GAME_ID IN ({$gameId})";
        }


        if (!empty($wheres)) {
            $whereString = ' where ' . implode(' and ', $wheres);
        }


        $sqlBody = "
        select
            t1.tday,
            t4.cp_game_name,
            t5.game_name,
            t6.app_show_name,
            t1.package_id,
            t1.sv_key,
            t3.AD_NAME as channel,
            platform_id,
            IFNULL(t7.USER_ID, power.AD_USER_ID) as user_id,
            IFNULL(t8.DEPARTMENT_ID, power.AD_DEPARTMENT_ID) as department_id,
            sum(new_user)                                    as new_user,
            sum(t2.pay_user_new)                             as pay_user_new,
            sum(t2.pay_money_new) as pay_money_new,
            sum(t2.pay_user) as pay_user,
            sum(t2.pay_money) as pay_money,
            max(t1.update_time) as update_time
        from
            ddc_platform.dws_svkey_ad_base_daily t1 
        join {$powerSql} on t1.PACKAGE_ID = power.PACKAGE_ID
        left join ddc_platform.dws_svkey_ad_payment_daily t2
                on t1.TDAY = t2.TDAY and t1.PACKAGE_ID = t2.PACKAGE_ID and t1.SV_KEY = t2.SV_KEY
         left join base_conf_platform.tb_ad_channel_conf t3 on t1.CHANNEL_ID = t3.ID
         left join base_conf_platform.tb_base_cp_game_conf t4 on t1.CP_GAME_ID = t4.CP_GAME_ID
         left join base_conf_platform.tb_base_game_conf t5 on t1.GAME_ID = t5.GAME_ID
         left join base_conf_platform.tb_base_game_app_show_conf t6 on power.APP_SHOW_ID = t6.APP_SHOW_ID
         left join dataspy.tb_ad_svlink_conf t7 on t1.SV_KEY = t7.ID
        left join dataspy.admin_user t8 on t7.USER_ID = t8.ID
        {$whereString}
        group by tday, cp_game_name, game_name, app_show_name, package_id, sv_key, channel
       ";

        if (!empty($options['sort'])) {
            $sortColumn = $options['sort'];
            $sort       = $options['order'] == 'ascend' ? '' : 'desc';
            $sqlBody    .= " order by {$sortColumn} {$sort} ";
        }

        $page     = $options['page'] ?? 1;
        $pageSize = $options['page_size'] ?? 50;

        $sql  = $sqlBody . $this->forPage($page, $pageSize);
        $list = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $summarySql = "
        select
            '汇总'  as tday,
            '-' as cp_game_name,
            '-' as game_name,
            '-' as app_show_name,
            '-' as package_id,
            '-' as sv_key,
            '-' as channel,
            '-' as platform_id,
            '-' as user_id,
            '-' as department_id,
            sum(new_user)  as new_user,
            sum(pay_user_new)  as pay_user_new,
            sum(pay_money_new) as pay_money_new,
            sum(pay_user) as pay_user,
            sum(pay_money) as pay_money,
            max(update_time) as update_time,
            count(*) as count_row
        from ($sqlBody) main";

        $summary = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => array_diff_key($summary, ['update_time' => '', 'count_row' => '']),
            'time'    => $summary['update_time'] ?? '',
            'total'   => $summary['count_row'] ?? 0,
        ];
    }

    /**
     * @param int $page
     * @param int $pageSize
     * @return string
     */
    protected function forPage(int $page = 1, int $pageSize = 50): string
    {
        $offset = ($page - 1) * $pageSize;

        return "limit {$pageSize} offset {$offset}";
    }

    /**
     * @return string
     */
    protected function powerSql(): string
    {
        return \Plus::$service->admin->getAdminPowerSql();
    }

    //根据汇总分组获取广告补全信息
    public function getAdInfoByGroups(array $list,array $groups){

        $dimension = "";
        $adInfo = [];
        if(in_array("creative_id",$groups)){
            $adModel = new TbAdpCreativeBase();
            $dimension = "creative_id";
        }elseif(in_array("plan_id",$groups)){
            $adModel = new TbAdpPlanBase();
            $dimension = "plan_id";
        }elseif(in_array("campaign_id",$groups)){
            $adModel = new TbAdpCampaign();
            $dimension = "campaign_id";
        }
        if($dimension){
            //补全广告信息
            $ids = array_filter(array_column($list,$dimension));
            if($dimension == "creative_id"){
                $planIds = array_filter(array_column($list,"plan_id"));
                $adInfo = $adModel->getDataById($planIds,$ids);
            }else{
                $adInfo = $adModel->getDataById($ids);
            }
        }
        return [$adInfo,$dimension];
    }
}
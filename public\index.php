<?php
//composer 自动加载

use app\extension\Application;


$loader  = require __DIR__ . '/../vendor/autoload.php';
$config  = require __DIR__ . '/../config/main.php';
$service = require __DIR__ . '/../config/service.php';

require __DIR__ . '/../vendor/framework/plus/src/Plus.php';

new \app\extension\ApplicationService($service);

const APP_ROOT = __DIR__ . '/../';

$app = new Application($config);
$app->run();

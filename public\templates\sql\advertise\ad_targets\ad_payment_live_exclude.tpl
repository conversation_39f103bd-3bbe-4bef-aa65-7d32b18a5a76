{include file="sql/advertise/ad_targets/ad_payment_detail.tpl"}
,
exclude_orders as (
    select f1.*, f2.`time` as firstlogin_time from ddc_platform.dwd_sdk_user_firstlogin f2 right join (
        select j1.*, j2.reason, j2.first_login_package_channel, j2.first_login_time, j2.first_paid_package_channel, j2.first_paid_time, j2.after_login_package_channel, j2.after_login_time from payment_detail j1 join (
        select
        order_id,
        json_unquote(json_extract(exclude_reason, '$.reason'))                      as reason,
        json_unquote(json_extract(exclude_reason, '$.first_login_package_channel')) as first_login_package_channel,
        json_unquote(json_extract(exclude_reason, '$.first_login_time')) as first_login_time,
        json_unquote(json_extract(exclude_reason, '$.first_paid_package_channel')) as first_paid_package_channel,
        json_unquote(json_extract(exclude_reason, '$.first_paid_time')) as first_paid_time,
        json_unquote(json_extract(exclude_reason, '$.after_login_package_channel')) as after_login_package_channel,
        json_unquote(json_extract(exclude_reason, '$.after_login_time')) as after_login_time
        from bigdata_dwd.dwd_sdk_user_payment_live_exclude
        where pay_result=1
        {if !empty($params)}
            {foreach $params as $kk => $chill}
                {if $kk eq 'range_date'}
                    and to_date(pay_time) between '{$chill[0]}' and '{$chill[1]}'
                {/if}
            {/foreach}
        {/if}
        ) j2 on j1.order_id=j2.order_id
    ) f1 on f1.cp_game_id=f2.cp_game_id and f1.core_account=f2.core_account
)
select
    p1.role_id,
    p1.role_name,
    p1.pay_time,
    p1.order_id,
    order_money,
    IF(p1.real_money =0, p1.money, p1.real_money) as cash,
    p1.coupon_money,
    p1.decuct_coin as platform_coins,
    p1.payway as pay_way,
    p1.reason,
    p1.first_login_package_channel,
    p1.first_login_time,
    p1.first_paid_package_channel,
    p1.first_paid_time,
    p1.after_login_package_channel,
    p1.after_login_time,
    p1.device_key as paid_device_key,
    p1.core_account,
    p1.firstlogin_time
from ddc_platform.dwd_sdk_adsource_game p2 join exclude_orders p1 on p1.source_id=p2.source_id
{if !empty($params)}
    {assign var="tag_first_where" value=1}
    {foreach $params as $key => $foo}
        {if $key eq 'plan_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                p2.plan_id in ('{$foo|join:"', '"}')
            {else}
                p2.plan_id = '{$foo}'
            {/if}
        {/if}
        {if $key eq 'package_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                p2.package_id in ('{$foo|join:"', '"}')
            {else}
                p2.package_id = '{$foo}'
            {/if}
        {/if}
    {/foreach}
{/if}
order by p1.pay_time desc
{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}
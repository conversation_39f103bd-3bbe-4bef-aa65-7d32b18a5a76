<?php

namespace app\service\AdvertiserData;

use app\apps\internal\Helpers\ConstHub;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\DBHelper\FieldCollect;
use app\extension\Support\Helpers\TimeUtil;
use app\extension\TableAssistant\Helpers\TableBaseHelp;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Components\Helpers\WrapQuery;
use app\service\AdvertiserData\Components\MatchParams\AdPlanMatcher;
use app\service\AdvertiserData\Scheme\BaseScheme;
use app\service\AdvertiserData\Scheme\RealtimeCreativeScheme;
use app\service\AdvertiserData\Scheme\RealtimePlanScheme;
use app\service\AdvertiserData\Traits\AdServiceable;
use app\service\AdvertiserData\Traits\BasicOperator;
use app\service\AdvertiserData\Traits\Converter;
use app\service\ConfigService\BasicServ;
use app\service\General\BizTagsServ;
use app\util\Common;
use Aura\SqlQuery\Common\SelectInterface;

class RealtimePlanIndex
{
    use Converter, AdServiceable, BasicOperator;

    /**
     * @param array $params
     * @param array $page
     * @param array $group
     * @param       $sort
     * @param array $column
     * @param bool $returnSummary
     *
     * @return array
     * @throws \Exception
     */
    public function listBase(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $rangeDateDimension = (int)Arr::pull($params, 'range_date_dimension', ConstHub::DIMENSION_DAY);

        if (ConstHub::DIMENSION_WEEK === $rangeDateDimension) {
            return $this->listByWeek(...func_get_args());
        }
        elseif (ConstHub::DIMENSION_MONTH === $rangeDateDimension) {
            return $this->listByMonth(...func_get_args());
        }
        else {
            return $this->listByDaily(...func_get_args());
        }
    }

    /**
     * 按天维度
     *
     *
     * @param array $params
     * @param array $page
     * @param array $group
     * @param null $sort
     * @param array $column
     * @param bool $returnSummary
     *
     * @return array
     */
    public function listByDaily(
        array $params = [], array $page = [],
        array $group = [], $sort = null,
        array $column = [], bool $returnSummary = true
    ): array
    {
        $today = new \DateTime();

        $today    = date('Y-m-d');
        $result   = collect();
        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = RealtimePlanScheme::NewOne()->select();

        $fieldCollect = $this->getFieldCollect($params, $scheme::MAIN_TABLE['alias'], $column);
        $fieldForTDay = $this->getFieldForTDay(ConstHub::DIMENSION_DAY, $scheme::MAIN_TABLE['alias']);
        $fields       = array_merge([$fieldForTDay], $fieldCollect->output());

        $scheme
            ->joinPowerSql($powerSql)
            ->join($this->joinAccount('t_base'))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->join((new JoinClause('left', 'adp_platform.tb_adp_campaign', 'base_campaign'))
                ->on('base_campaign.channel_id', '=', 't_base.main_channel_id')
                ->on('base_campaign.campaign_id', '=', 't_base.campaign_id'));

        $matcher = new AdPlanMatcher($scheme->fieldReflect());
        $matcher->setParams($params);
        $matcher->execute($scheme);

        //总行数sql实例
        $rowCountScheme       = clone $scheme;
        $rowCountFieldCollect = $this->getFieldCollect($params, $scheme::MAIN_TABLE['alias'], ['update_time' => ['table' => $scheme::MAIN_TABLE['alias'], 'aggregate' => 'max']], "fixed");
        $rowCountFieldForTDay = $this->getFieldForTDay(ConstHub::DIMENSION_DAY, $scheme::MAIN_TABLE['alias']);
        $rowCountFields       = array_merge([$rowCountFieldForTDay], $rowCountFieldCollect->output(false, false, false, true, ["update_time"]));
        $rowCountScheme->scope(TableBaseHelp::setColumn($rowCountFields));

        //汇总行sql实例
        $totalScheme   = clone $scheme;
        $summaryColumn = $fieldCollect->output(true, false, false, true);
        $summaryColumn = array_merge([$rowCountFieldForTDay], $summaryColumn);
        $totalScheme->scope(TableBaseHelp::setColumn($summaryColumn));

        //查询字段
        $scheme->scope(TableBaseHelp::setColumn($fields));
        if ($returnSummary) {
            // 汇总行
            $totalSql = $totalScheme->toSql();
            Common::dumpSql($totalSql);
            // 获取总行数
            $rowCountScheme->scope(TableBaseHelp::setGroup(TableBaseHelp::changeGroups($group, $this->groupsReflect())));
            $countSql             = WrapQuery::countRowQuery($rowCountScheme->toSql());
            $summaryRow           = $this->fetchOne($totalSql);
            $countRow             = $this->fetchOne($countSql);
            $lastUpdateTime       = Arr::pull($summaryRow, 'update_time');
            $summaryRowWithDaySQL = $totalSql . ' group by tday';
            $summaryRowWithDay    = $this->fetchAll($summaryRowWithDaySQL);

            $result
                ->put('summary', $summaryRow)
                ->put('total', $countRow['total'] ?? 0)
                ->put('time', $lastUpdateTime)
                ->put('summary_with_day', $summaryRowWithDay);
        }

        //分组汇总
        $scheme->scope(TableBaseHelp::setGroup(TableBaseHelp::changeGroups($group, $this->groupsReflect())));

        // 排序处理
        if (!empty($sort)) {
            $scheme->scope($this->buildOrder($sort));
        }
        // 分页
        if (!empty($page)) {
            $scheme->scope($this->buildPage($page));
        }
        Common::dumpSql((clone $scheme)->toSql());
        $result->put('list', $this->fetchAll($scheme->toSql()));

        return $result->toArray();
    }

    /**
     * 按周维度查询
     *
     * @param array $params
     * @param array $page
     * @param array $group
     * @param null $sort
     * @param array $column
     * @param bool $returnSummary
     *
     * @return array
     * @throws \Exception
     */
    public function listByWeek(
        array $params = [], array $page = [],
        array $group = [], $sort = null,
        array $column = [], bool $returnSummary = true
    ): array
    {
        $result = collect();

        [
            'begin' => $dateStart,
            'end'   => $dateEnd,
            'cycle' => $dateCycle,
        ] = TimeUtil::divideWeekByRangeDate(
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end')
        );
        $params['range_date_start'] = $dateStart;
        $params['range_date_end']   = $dateEnd;

        $subScheme = $this->getWeekSubScheme($params, $dateCycle, $column);

        $wrapScheme   = (new BaseScheme())->from(clone $subScheme, 'base_body');
        $fieldCollect = $this->getFieldCollect($params, 'base_body', $column);

        $fieldCollect->set('tday', ['info']);
        $wrapScheme
            ->scope(TableBaseHelp::setColumn($fieldCollect->output(false, false, true, false)))
            ->scope(TableBaseHelp::setGroup($group));

        // 排序处理
        if (!empty($sort)) {
            $wrapScheme->scope(TableBaseHelp::setOrder($sort));
        }
        // 分页
        if (!empty($page)) {
            $wrapScheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }

        $backupScheme = clone $wrapScheme;

        $result->put('list', $this->fetchAll((clone $wrapScheme)->toSql()));

        if ($returnSummary) {
            $summaryColumn = $fieldCollect->output(true, false, true, false);
            // 汇总行
            $summarySql = WrapQuery::totalRowQuery((clone $subScheme)->toSql(), $summaryColumn);
            // 获取总行数
            $countSql       = WrapQuery::countRowQuery((clone $backupScheme)->toSql());
            $summaryRow     = $this->fetchOne($summarySql);
            $countRow       = $this->fetchOne($countSql);
            $lastUpdateTime = Arr::pull($summaryRow, 'update_time');

            $result
                ->put('summary', $summaryRow)
                ->put('total', $countRow['total'] ?? 0)
                ->put('time', $lastUpdateTime);
        }

        return $result->toArray();
    }

    /**
     * 按月维度
     *
     * @param array $params
     * @param array $page
     * @param array $group
     * @param null $sort
     * @param array $column
     * @param bool $returnSummary
     *
     * @return array
     * @throws \Exception
     */
    public function listByMonth(
        array $params = [], array $page = [],
        array $group = [], $sort = null,
        array $column = [], bool $returnSummary = true
    ): array
    {
        $today  = date('Y-m-d');
        $result = collect();

        $dateStart = Arr::pull($params, 'range_date_start', $today);
        $dateEnd   = Arr::pull($params, 'range_date_end', $today);

        $dateStart = (new \DateTime($dateStart))->format('Y-m-01');
        $dateEnd   = (new \DateTime($dateEnd))->format('Y-m-t');

        $params['range_date_start'] = $dateStart;
        $params['range_date_end']   = $dateEnd;

        $subScheme = $this->getMonthSubScheme($params, $column);

        $wrapScheme   = (new BaseScheme())->from(clone $subScheme, 'base_body');
        $fieldCollect = $this->getFieldCollect($params, 'base_body', $column);

        $fieldCollect->set('tday', ['info']);
        $wrapScheme
            ->scope(TableBaseHelp::setColumn($fieldCollect->output(false, false, true, false)))
            ->scope(TableBaseHelp::setGroup($group));

        $result->put('list', $this->fetchAll((clone $wrapScheme)->toSql()));

        if ($returnSummary) {
            $summaryColumn = $fieldCollect->output(true, false, true, false);
            // 汇总行
            $summarySql = WrapQuery::totalRowQuery((clone $subScheme)->toSql(), $summaryColumn);
            // 获取总行数
            $countSql       = WrapQuery::countRowQuery((clone $wrapScheme)->toSql());
            $summaryRow     = $this->fetchOne($summarySql);
            $countRow       = $this->fetchOne($countSql);
            $lastUpdateTime = Arr::pull($summaryRow, 'update_time');

            $result
                ->put('summary', $summaryRow)
                ->put('total', $countRow['total'] ?? 0)
                ->put('time', $lastUpdateTime);
        }

        return $result->toArray();
    }

    /**
     * 获取按周的子查询预处理sql
     *
     * @param       $params
     * @param array $dateCycle
     * @param array $columns
     *
     * @return RealtimeCreativeScheme|RealtimePlanScheme
     */
    protected function getWeekSubScheme($params, array $dateCycle, array $columns = [])
    {
        $powerSql   = \Plus::$service->admin->getAdminPowerSql();
        $mainScheme = RealtimePlanScheme::NewOne()->select();

        $fields       = $this->getFieldCollect($params, $mainScheme::MAIN_TABLE['alias'], $columns);
        $fieldForTDay = $this->getFieldForTDay(
            ConstHub::DIMENSION_WEEK, $mainScheme::MAIN_TABLE['alias'], $dateCycle
        );

        $fields = array_merge([$fieldForTDay], $fields->output(false, true, false, true));

        $mainScheme
            ->joinPowerSql($powerSql)
            ->join($this->getAccountJoins($mainScheme::MAIN_TABLE['alias']))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->scope(TableBaseHelp::setColumn($fields));

        $matcher = new AdPlanMatcher($mainScheme->fieldReflect());
        $matcher
            ->setParams($params)
            ->execute($mainScheme);

        return $mainScheme;
    }

    /**
     * @param       $params
     * @param array $columns
     *
     * @return RealtimeCreativeScheme|RealtimePlanScheme
     */
    protected function getMonthSubScheme($params, array $columns = [])
    {
        $powerSql   = \Plus::$service->admin->getAdminPowerSql();
        $mainScheme = RealtimePlanScheme::NewOne()->select();
        $fields     = $this->getFieldCollect($params, $mainScheme::MAIN_TABLE['alias'], $columns);

        $fieldForTDay = $this->getFieldForTDay(ConstHub::DIMENSION_MONTH, $mainScheme::MAIN_TABLE['alias']);
        $fields       = array_merge([$fieldForTDay], $fields->output(false, true, false, true));

        $mainScheme
            ->joinPowerSql($powerSql)
            ->join($this->getAccountJoins($mainScheme::MAIN_TABLE['alias']))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->scope(TableBaseHelp::setColumn($fields));

        $matcher = new AdPlanMatcher($mainScheme->fieldReflect());
        $matcher
            ->setParams($params)
            ->execute($mainScheme);

        return $mainScheme;
    }

    /**
     * @param        $params
     * @param string $baseTable
     * @param array $columns
     * @param string $colType 列类型
     *
     * @return FieldCollect
     */
    private function getFieldCollect(
        $params, string $baseTable = RealtimePlanScheme::MAIN_TABLE['alias'], array $columns = [], $colType = "all"
    ): FieldCollect
    {
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString      = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0, IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) AS promotion_channel_id",
            $planChannelsString
        );
        $channelMainString  = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );


        $fixed = [
            // 详情类型字段
            'cp_game_id'           => ['info', 'table' => $baseTable],
            'game_id'              => ['info', 'table' => $baseTable],
            'app_show_id'          => ['info', 'table' => 'POWER'],
            'channel_main_id'      => ['info', 'raw' => $channelMainString],
            //            'promotion_channel_id' => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id NOT IN (6568, 6822, 5329), POWER.channel_id,IF(t_base.channel_id != 0, t_base.channel_id,POWER.channel_id)), 0) AS promotion_channel_id'],
            'promotion_channel_id' => ['info', 'raw' => $channelString],
            'platform_id'          => ['info', 'table' => 'POWER'],
            'package_id'           => ['info', 'table' => $baseTable],
            'promotion_id'         => ['info', 'table' => 'POWER', 'source_field' => 'popularize_v2_id'],
            'main_channel_id'      => ['info', 'table' => $baseTable],
            'department_id'        => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),t_admin.department_id, POWER.ad_department_id),0) as new_department_id'],
            'user_id'              => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),' . $baseTable . '.USER_ID, POWER.AD_USER_ID), 0) AS user_id'],
            'ad_account'           => ['info', 'table' => 'base_account'],
            'account_name'         => ['info', 'table' => 'adp_oauth', 'source_field' => 'ADVERTISER_NAME'],
            'account_id'           => ['info', 'table' => $baseTable],
            'campaign_id'          => ['info', 'table' => $baseTable],
            'plan_id'              => ['info', 'table' => $baseTable],
            'dim_user_os'          => ['info', 'raw' => "case when USER_OS = JSON_ARRAY('ANDROID') then 'ANDROID' when USER_OS = JSON_ARRAY('IOS') then 'IOS' when JSON_CONTAINS(USER_OS, '[\"ANDROID\",\"IOS\"]', '$') = 1 then '混投' else '混投' end as dim_user_os"]
        ];

        $calc = [
            'cost'                                     => ['table' => $baseTable, 'aggregate' => 'sum'], // 返点前消耗金额
            'cost_discount'                            => ['table' => $baseTable, 'aggregate' => 'sum'], // 返点后消耗金额
            'new_user'                                 => ['table' => $baseTable, 'aggregate' => 'sum'], // 广告新增用户
            'create_role_new'                          => ['table' => $baseTable, 'aggregate' => 'sum'], // 创角新增
            'pay_user_new'                             => ['table' => 't_payment', 'aggregate' => 'sum'], // 新增用户付费金额
            'pay_user'                                 => ['table' => 't_payment', 'aggregate' => 'sum'], // 付费用户数
            'pay_money'                                => ['table' => 't_payment', 'aggregate' => 'sum'], // 付费金额
            'pay_count'                                => ['table' => 't_payment', 'aggregate' => 'sum'], // 付费次数
            'pay_money_new'                            => ['table' => 't_payment', 'aggregate' => 'sum'], // 新用户付费金额
            'pay_count_new'                            => ['table' => 't_payment', 'aggregate' => 'sum'], //新用户付费次数
            'show'                                     => ['table' => $baseTable, 'aggregate' => 'sum'], // 展示数
            'click'                                    => ['table' => $baseTable, 'aggregate' => 'sum'], // 点击数
            'download'                                 => ['table' => $baseTable, 'aggregate' => 'sum'], //开始下载数
            'activate'                                 => ['table' => $baseTable, 'aggregate' => 'sum'], // 激活数
            'convert'                                  => ['table' => $baseTable, 'aggregate' => 'sum'], // 转化数
            'install'                                  => ['table' => $baseTable, 'aggregate' => 'sum'], // 安装数
            'lp_view'                                  => ['table' => $baseTable, 'aggregate' => 'sum'], // 落地页展示数
            'lp_download'                              => ['table' => $baseTable, 'aggregate' => 'sum'], // 落地页点击数
            'download_start'                           => ['table' => $baseTable, 'aggregate' => 'sum'], // 开始下载数
            'register'                                 => ['table' => $baseTable, 'aggregate' => 'sum'], // 注册数
            'new_real_user'                            => ['table' => $baseTable, 'aggregate' => 'sum'], // 广告新增实名用户
            'new_user_emulator'                        => ['table' => $baseTable, 'aggregate' => 'sum'], // 广告新增用户(模拟器)
            'activate_device'                          => ['table' => $baseTable, 'aggregate' => 'sum'], // 激活设备
            'pay_new_user_7days'                       => ['table' => $baseTable, 'aggregate' => 'sum'], // 付费新用户(7天内)
            'pay_frequency_7days'                      => ['table' => $baseTable, 'aggregate' => 'sum'], // 新用户付费次数(7天内)
            'online_time'                              => ['table' => $baseTable, 'aggregate' => 'sum'], // 广告新增用户今日总在线时长(秒)
            'first_online_time'                        => ['table' => $baseTable, 'aggregate' => 'sum'], // 广告新增用户首次在线时长(秒)
            'active_user'                              => ['table' => $baseTable, 'aggregate' => 'sum'], // 广告新增活跃用户(秒)
            'total_play'                               => ['table' => $baseTable, 'aggregate' => 'sum'], // 播放量
            'play_time_per_play'                       => ['table' => $baseTable, 'aggregate' => 'sum'], // 播放总时长(秒)
            'play_duration_3s'                         => ['table' => $baseTable, 'aggregate' => 'sum'], // 3秒播放数
            'active_pay_intra_day_count'               => ['table' => $baseTable, 'aggregate' => 'sum'], // 3秒播放数
            'attribution_billing_game_in_app_ltv_1day' => ['table' => $baseTable, 'aggregate' => 'sum'], // 3秒播放数
            'update_time'                              => ['table' => $baseTable, 'aggregate' => 'max'], // 最大更新时间
        ];

        if (!empty($columns) && $colType != "fixed") {
            $calc = array_intersect_key($calc, array_flip($columns));
        }

        if ($colType == "fixed") {
            return new FieldCollect(array_merge($fixed, $columns));
        }
        return new FieldCollect(array_merge($fixed, $calc));
    }

    /**
     * 分组对照SQL的关系
     *
     * @return string[]
     */
    private function groupsReflect(): array
    {
        $mainAlias = RealtimePlanScheme::MAIN_TABLE['alias'];

        return [
            'tday'          => $mainAlias . '.tday',
            'cp_game_id'    => $mainAlias . '.cp_game_id',
            'game_id'       => $mainAlias . '.game_id',
            'package_id'    => $mainAlias . '.package_id',
            'channel_id'    => $mainAlias . '.channel_id',
            'creative_id'   => $mainAlias . '.creative_id',
            'ad_account_id' => 'base_account.id',
            'department_id' => 'new_department_id',
            //            'channel_main_id' => 'POWER.channel_main_id',
        ];
    }

    /**
     * 查询一条数据
     *
     * @param $sql
     *
     * @return mixed
     */
    private function fetchOne($sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * 执行查询数据
     *
     * @param $sql
     *
     * @return array|false
     */
    private function fetchAll($sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * @param array $params
     * @param array $page
     * @param array $group
     * @param       $sort
     * @param array $column
     * @param bool $returnSummary
     *
     * @return array
     */
    public function simpleListByDay(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $today              = date('Y-m-d');
        $rangeDateDimension = (int)Arr::pull($params, 'range_date_dimension', ConstHub::DIMENSION_DAY);
        $groupString        = '';
        $table              = 'ddc_platform.dws_plan_ad_base_daily';
        $wheres             = [];
        $columns            = [
            'tday',
            'channel_main_id',
            'sum(cost) as cost',
            'sum(cost_discount) as cost_discount',
            'sum(new_user) as new_user',
            'sum(`click`) as click',
            'sum(`activate_device`) as activate_device',
            'sum(`create_role_new`) as create_role',
            'sum(`active_user`) as active_user',
        ];

        $columnString = implode(',', $columns);

        [
            $dateStart, $dateEnd,
        ] = [$params['range_date_start'], $params['range_date_end']];


        $wheres[] = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = Arr::wrap(explode(',', $params['cp_game_id']));
            $cpGameId = implode(',', $cpGameId);
            $wheres[] = "t_base.cp_game_id in ({$cpGameId})";
        }

        if (!empty($wheres)) {
            $whereString = ' WHERE ' . implode(' and ', $wheres);
        }

        if (!empty($group)) {
            $groupString = ' group by ' . implode(', ', $group);
        }

        $sql        = "SELECT {$columnString} FROM {$table} t_base join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString} {$groupString} order by tday";
        $summarySql = "SELECT {$columnString} FROM {$table} t_base join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString}";

        $list       = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $summaryRow = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => $summaryRow,
        ];
    }


    /**
     * @param array $params
     * @param array $page
     * @param array $group
     * @param       $sort
     * @param array $column
     * @param bool $returnSummary
     *
     * @return array
     */
    public function simpleActiveByDay(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $today              = date('Y-m-d');
        $rangeDateDimension = (int)Arr::pull($params, 'range_date_dimension', ConstHub::DIMENSION_DAY);
        $groupString        = '';
        $table              = 'ddc_platform.dws_plan_ad_active_daily';
        $wheres             = [];
        $columns            = [
            'tday',
            'channel_main_id',
            'sum(t_base.`active_user`) as active_user',
            'sum(new_user) as new_user',
        ];

        $columnString = implode(',', $columns);

        [
            $dateStart, $dateEnd,
        ] = [$params['range_date_start'], $params['range_date_end']];


        $wheres[] = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = Arr::wrap(explode(',', $params['cp_game_id']));
            $cpGameId = implode(',', $cpGameId);
            $wheres[] = "t_base.cp_game_id in ({$cpGameId})";
        }

        if (!empty($wheres)) {
            $whereString = ' WHERE ' . implode(' and ', $wheres);
        }

        if (!empty($group)) {
            $groupString = ' group by ' . implode(', ', $group);
        }

        $sql        = "SELECT {$columnString} FROM {$table} t_base left join ddc_platform.dws_plan_ad_base_daily t_active using(TDAY,PACKAGE_ID,CHANNEL_ID,PLAN_ID)  join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString} {$groupString} order by tday";
        $summarySql = "SELECT {$columnString} FROM {$table} t_base left join ddc_platform.dws_plan_ad_base_daily t_active using(TDAY,PACKAGE_ID,CHANNEL_ID,PLAN_ID) join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString}";

        $list       = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $summaryRow = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => $summaryRow,
        ];
    }


    public function simplePayListByDay(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $rangeDateDimension = (int)Arr::pull($params, 'range_date_dimension', ConstHub::DIMENSION_DAY);
        $groupString        = '';
        $table              = 'ddc_platform.dws_plan_ad_payment_daily';
        $wheres             = [];
        $columns            = [
            'tday',
            'channel_main_id',
            'sum(`pay_user_new`) as pay_user',
        ];

        $columnString = implode(',', $columns);

        [
            $dateStart, $dateEnd,
        ] = [$params['range_date_start'], $params['range_date_end']];


        $wheres[] = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = Arr::wrap(explode(',', $params['cp_game_id']));
            $cpGameId = implode(',', $cpGameId);
            $wheres[] = "t_base.cp_game_id in ({$cpGameId})";
        }

        if (!empty($wheres)) {
            $whereString = ' WHERE ' . implode(' and ', $wheres);
        }

        if (!empty($group)) {
            $groupString = ' group by ' . implode(', ', $group);
        }

        $sql        = "SELECT {$columnString} FROM {$table} t_base join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString} {$groupString} order by tday";
        $summarySql = "SELECT {$columnString} FROM {$table} t_base join base_conf_platform.tb_package_detail_conf power on t_base.package_id = power.package_id {$whereString}";

        $list       = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $summaryRow = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => $summaryRow,
        ];
    }


    /**
     * 暂时只供大盘使用
     *
     * @param array $params
     * @param array $page
     * @param array $group
     * @param       $sort
     * @param array $column
     * @param bool $returnSummary
     *
     * @return array
     * @throws \Exception
     * @todo 整合到data
     */
    public function simpleListByWeek(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $table = 'ddc_platform.dws_plan_ad_base_daily';

        [
            'begin' => $dateStart,
            'end'   => $dateEnd,
            'cycle' => $dateCycle,
        ] = TimeUtil::divideWeekByRangeDate(
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end')
        );
        $params['range_date_start'] = $dateStart;
        $params['range_date_end']   = $dateEnd;
        $fieldForTDay               = $this->getFieldForTDay(
            ConstHub::DIMENSION_WEEK, 't_base', $dateCycle
        );

        $columns = [
            $fieldForTDay,
            'cp_game_id',
            'cost_discount',
            'cost',
            'new_user',
        ];

        $columnString = \implode(',', $columns);
        $wheres       = [];
        $wheres[]     = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = $params['cp_game_id'];
            if (str_contains($cpGameId, ',')) {
                $wheres[] = "cp_game_id in ({$cpGameId})";
            }
            else {
                $wheres[] = "cp_game_id = '{$cpGameId}'";
            }
        }

        $whereString = ' WHERE ' . implode(' and ', $wheres);

        if (!empty($group)) {
            $groupString = ' GROUP BY ' . implode(',', $group);
        }

        $subSql = "SELECT 
            {$columnString}
            FROM {$table} t_base
            $whereString";

        $sql = "
        SELECT 
            tday , sum(cost) as cost, sum(cost_discount) as cost_discount,sum(new_user) as new_user
        FROM ({$subSql}) as main_body
        {$groupString} order by tday
        ";

        $result         = [];
        $result['list'] = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $summarySql = "
        SELECT 
            tday , sum(cost) as cost, sum(cost_discount) as cost_discount,sum(new_user) as new_user
        FROM ({$subSql}) as main_body";

        $summaryRow         = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);
        $summaryRow['tday'] = '汇总';
        $result['summary']  = $summaryRow;

        return $result;
    }

    /**
     * @param array $params
     * @param array $page
     * @param array $group
     * @param null $sort
     * @param array $column
     * @param bool $returnSummary
     *
     * @return array
     */
    public function simpleListByMonth(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $table = 'ddc_platform.dws_plan_ad_base_daily';

        $fieldForTDay = $this->getFieldForTDay(
            ConstHub::DIMENSION_MONTH, 't_base'
        );

        [
            $dateStart, $dateEnd,
        ] = [$params['range_date_start'], $params['range_date_end']];

        $columns = [
            $fieldForTDay,
            'cp_game_id',
            'cost_discount',
            'cost',
            'new_user',
        ];

        $columnString = \implode(',', $columns);
        $wheres       = [];
        $wheres[]     = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = $params['cp_game_id'];
            if (str_contains($cpGameId, ',')) {
                $wheres[] = "cp_game_id in ({$cpGameId})";
            }
            else {
                $wheres[] = "cp_game_id = '{$cpGameId}'";
            }
        }

        $whereString = ' WHERE ' . implode(' and ', $wheres);

        if (!empty($group)) {
            $groupString = ' GROUP BY ' . implode(',', $group);
        }

        $subSql = "SELECT 
            {$columnString}
            FROM {$table} t_base
            $whereString";

        $sql = "
        SELECT 
            tday , sum(cost) as cost, sum(cost_discount) as cost_discount,sum(new_user) as new_user
        FROM ({$subSql}) as main_body
        {$groupString} order by tday
        ";

        $result         = [];
        $result['list'] = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $summarySql = "
        SELECT 
            tday , sum(cost) as cost, sum(cost_discount) as cost_discount,sum(new_user) as new_user
        FROM ({$subSql}) as main_body";

        $summaryRow         = \Plus::$app->ddc_platform->query($summarySql)->fetch(\PDO::FETCH_ASSOC);
        $summaryRow['tday'] = '汇总';
        $result['summary']  = $summaryRow;

        return $result;
    }

}
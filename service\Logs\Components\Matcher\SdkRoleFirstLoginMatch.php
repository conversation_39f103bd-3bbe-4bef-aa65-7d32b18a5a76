<?php

namespace app\service\Logs\Components\Matcher;


use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\General\Helpers\BigDataDwdTable;
use app\service\General\Helpers\DdcPlatformTable;
use app\util\Common;
use Spiral\Database\Query\SelectQuery;

class SdkRoleFirstLoginMatch extends BaseLogMatch
{
    /**
     * @return array
     * @throws \Exception
     */
    protected function matchFnList(): array
    {
        $new = [
            'device_code' => $this->matchDevice(),
            'role_name'   => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'game_server' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'role_id'     => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'time'        => $this->matchTime(),
        ];

        return array_merge(parent::matchFnList(), $new);
    }

    /**
     * @return \Closure
     * @throws \Exception
     */
    protected function matchDevice(): \Closure
    {
        $db = $this->getConn();
        return function (SelectQuery &$qb, $key, $value) use ($db) {
            $subQb = $db
                ->select()
                ->from(DdcPlatformTable::DwdSdkDevice)
                ->where('oaid', $value)
                ->orWhere('device_id', $value)
                ->columns(['device_key'])->distinct();

            $qb->where('device_key', 'IN', $subQb);
        };
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     * @throws \Exception
     */
    protected function getConn()
    {
        if ($dorisIndex = Common::pingDorisIndex()) {
            return FakeDB::connection($dorisIndex);
        }
        else {
            throw new \RuntimeException('与数据库连接断开');
        }
    }

}
<?php

namespace app\apps\internal\Traits;

use app\extension\Support\Collections\Collection;
use Plus\MVC\Request\Request;

trait InternalRoutes
{
    /**
     * 如果没有registerParams方法会获取全部的参数
     *
     * @param Request $request
     *
     * @return Collection
     */
    protected function wrapParams(Request $request): Collection
    {
        $registerParam = is_callable([$this, 'registerParams'])
            ? $this->registerParams()
            : null;

        $allParam = $this->getAllParam($request);

        if (empty($registerParam)) {
            return $allParam;
        }

        $collect = collect();

        $registerParam->each(function ($item) use (&$collect, $allParam) {
            $field   = $item['field'];
            $default = $item['default'] ?? null;

            if (isset($allParam[$field])) {
                $collect->put($field, $allParam[$field]);
            }
            else {
                if (!is_null($default)) {
                    $collect->put($field, $default);
                }
            }
        });

        return $collect;
    }

    /**
     * 获取全部参数
     *
     * @param Request $request
     *
     * @return Collection
     */
    protected function getAllParam(Request $request): Collection
    {
        return (function () {
            return collect($this->params ?? []);
        })->call($request);
    }

}
<?php

namespace app\service\AdvertiserData\Scheme;

use app\extension\Support\Macroable\Traits\Macroable;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Traits\JoinBase;
use app\service\AdvertiserData\Traits\JoinFixAble;
use app\service\AdvertiserData\Traits\OperableQuery;
use app\service\AdvertiserData\Traits\Schemer;
use app\service\AdvertiserData\Traits\TotalAble;
use Aura\SqlQuery\Common\InsertInterface;
use Aura\SqlQuery\Common\SelectInterface;
use Aura\SqlQuery\QueryFactory;

class AdPlanPassScheme implements SchemeContract
{
    use Macroable, Schemer, OperableQuery, JoinFixAble, JoinBase;

    /**
     * @var QueryFactory
     */
    protected QueryFactory $queryFactory;
    /**
     * @var SelectInterface|InsertInterface
     */
    protected $query;

    public const MAIN_TABLE = [
        'alias' => 't_pass',
        'table' => 'ddc_platform.dws_plan_ad_pass_daily',
    ];

    /**
     * @var array|array[]
     */
    protected array $joinTables = [];

    public function __construct()
    {
        $this->queryFactory = new QueryFactory('mysql');
    }

    /**
     * 实例化对象
     *
     * @return AdPlanPassScheme
     */
    public static function NewOne(): AdPlanPassScheme
    {
        return new static();
    }

    public function __clone()
    {
        $this->queryFactory = clone $this->queryFactory;
        $this->query        = clone $this->query;
    }

    public function fieldReflect(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];

        return [
            'tday'                 => $mainTable,
            'cp_game_id'           => $mainTable,
            'game_id'              => $mainTable,
            'package_id'           => $mainTable,
            'app_show_id'          => 'POWER',
            'channel_main_id'      => 'POWER',
            'channel_id'           => $mainTable,
            'platform_id'          => 'POWER',
            'plan_id'              => $mainTable,
            'campaign_id'          => $mainTable,
            'promotion_id'         => 'POWER.popularize_v1_id',
            'promotion_channel_id' => $mainTable . '.channel_id',
            'plan_name'            => $mainTable,
            'ad_account'           => 'base_account.ad_account',
            'ad_account_id'        => 'base_account.id',
            'account_id'           => 'base_account.account_id',
            'user_id'              => 'base_account.adv_user_id',
            'department_id'        => 'base_account.department_id',
            'is_ad_data'           => 't_base'
        ];
    }

    protected function fixedTables(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];
        $fixTables = [];
        $fixTables[] =
            (new JoinClause('left', 'dataspy.admin_user', 't_admin'))
                ->on('t_base.USER_ID', '=', 't_admin.ID');

        return $fixTables;
    }

}
<?php

namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\BusinessPlatform\OptionServ;
use app\service\General\Helpers\TableConst;
use app\service\Media\Helper\MediaTableConst;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;


class PaymentVoided
{

    const MODE_SUMMARY = 2;
    const MODE_LIST = 1;


    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $qb = $this->getQueryBuilder();
        $this->whereMatch(
            $qb,
            $params,
            [
                'order_id'       => 't_payment.order_id',  // 手盟订单
                'package_id'     => 't_payment.package_id', // 包号
                'core_account'   => 't_payment.core_account', // 核心账号
                'game_server_id' => 't_payment.game_server_id', // 区服
                'role_id'        => 't_payment.role_id', // 角色id
                'cp_game_id'     => 'base_game.cp_game_id', // 游戏原名
                'game_id'        => 'base_game.game_id', // 游戏统计名
                'payway'         => 't_payment.payway', // 支付渠道
            ]
        );
        $result = [];

        if ($mode & static::MODE_LIST) {
            $qb->columns([
                new Fragment('DATE(t_voided.voided_time) as tday'),
                'base_game.cp_game_name as cp_game_id',
                'base_game.sm_game_name as game_id',
                't_payment.package_id as package_id',
                't_payment.game_server_id as game_server_id',
                't_payment.login_account as login_account',
                't_payment.core_account as core_account',
                't_payment.role_id as role_id',
                't_payment.role_name as role_name',
                't_payment.role_rank as role_rank',
                't_payment.role_vip as role_vip',
                't_payment.order_id as order_id',
                't_payment.cp_order_id as cp_order_id',
                't_payment.channel_order_id as channel_order_id',
                't_payment.company as company',
                't_payment.money_type as money_type',
                't_payment.money as money',
                't_payment.game_coin as game_coin',
                't_payment.goods_name as goods_name',
                't_payment.order_time as order_time',
                't_payment.pay_time as pay_time',
                't_payment.pay_result as order_status',
                'base_channel.channel_name as channel_id',
                'base_channel.channel_main_name as channel_main_id',
                't_payment.game_result as game_back_status',
                'base_pay_way.name as payway',
                't_voided.voided_type as voided_type',
                't_payment.order_type.order_type',
            ]);

            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    $infoQb->groupBy($g);
                }
            }
            $noPageQb = clone $infoQb;

            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;
                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }

            if (!empty($sort)) {
                $infoQb->orderBy($sort);
            }

            $result['list']  = $infoQb->fetchAll();
            $result['total'] = $this
                ->getConn()
                ->select()
                ->from(new Fragment('(' . (clone $noPageQb)->__toString() . ') as total_body'))
                ->count();
        }

        if ($mode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            $summaryQb->columns([
                new Fragment('SUM(t_payment.MONEY) as money'),
            ]);

            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * @param SelectQuery $qb
     * @param array $params
     * @param array $reflectMap
     *
     * @return void
     */
    protected function whereMatch(SelectQuery &$qb, array $params = [], array $reflectMap = [])
    {
        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            $rangeDate = [
                $params['range_date_start'],
                $params['range_date_end'],
            ];

            sort($rangeDate);

            if (count($rangeDate) === 1) {
                $qb->where('voided_time', $rangeDate[0]);
            }
            else {
                $qb->where('voided_time', 'between', $rangeDate[0], $rangeDate[1]." 23:59:59");
            }
        }

        if (isset($params['cp_game_id'])) {
            $d     = $params['cp_game_id'];
            $field = $reflectMap['cp_game_id'] ?? 'cp_game_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['package_id'])) {
            $d = $params['package_id'];
            $field = $reflectMap['package_id'] ?? 'package_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['game_id'])) {
            $d = $params['game_id'];
            $field = $reflectMap['game_id'] ?? 'game_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['core_account'])) {
            $d = $params['core_account'];
            $field = $reflectMap['core_account'] ?? 'core_account';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['role_id'])) {
            $d = $params['role_id'];
            $field = $reflectMap['role_id'] ?? 'role_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['order_id'])) {
            $d = $params['order_id'];
            $field = $reflectMap['order_id'] ?? 'order_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['game_server_id'])) {
            $d = $params['game_server_id'];
            $field = $reflectMap['game_server_id'] ?? 'game_server_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['voided_type'])) {
            $d = $params['voided_type'];
            $field = $reflectMap['voided_type'] ?? 'voided_type';

            $qb->where($field, new Parameter($d));
        }

        if (!empty($params['login_account'])) {
            $loginAccount = $this->convertArray($params['login_account']);
            $coreList     = (new OptionServ())->getAccountInfo(['login_account' => new Parameter($loginAccount)], ['login_account', 'core_user']);

            if (empty($coreList)) {
                throw new \UnexpectedValueException('没有该登录账号信息');
            }

            $coreAccount = array_column($coreList, 'core_user');

            $qb->where('t_payment.core_account', new Parameter($coreAccount));
        }
    }

    protected function convertArray($data): array
    {
        if (is_string($data) && str_contains($data, ',')) {
            $data = \explode(',', $data);
        }
        return Arr::wrap($data);
    }

    /**
     * @param int $qbMode
     *
     * @return \Spiral\Database\Query\SelectQuery
     */
    private function getQueryBuilder(int $qbMode = -1): \Spiral\Database\Query\SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from(TableConst::DWD_SDK_USER_PAYMENT_VOIDED . ' as t_voided');
        $qb
            ->leftJoin(TableConst::DWD_SDK_USER_PAYMENT, 't_payment')
            ->on([
                't_voided.order_id' => 't_payment.order_id',
            ])->leftJoin(TableConst::CONF_PACKAGE_DETAIL, 'base_package')
            ->on([
                't_payment.PACKAGE_ID' => 'base_package.PACKAGE_ID',
            ])
            ->leftJoin(TableConst::CONF_BASE_CHANNEL, 'base_channel')
            ->on(['base_package.channel_id' => 'base_channel.channel_id'])
            ->leftJoin(TableConst::CONF_GAME_BASE, 'base_game')
            ->on(['base_package.game_id' => 'base_game.game_id'])
            ->leftJoin(TableConst::CONF_PAY_WAY, 'base_pay_way')
            ->on(['t_payment.payway' => 'base_pay_way.code']);

        return $qb;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $mode
     *
     * @return array
     */
    public function getDimensionWithAccount(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $result = [];
        $db     = $this->getConn();
        $qb     = $db
            ->select()
            ->from('ddc_platform.dwd_media_account_snapshot_audience_analyze as account_audience');

        if ($mode & static::MODE_LIST) {
            $infoQb = clone $qb;

            $infoQb->columns([
                'tday',
                'data_type as data_type',
                'dimension as dimension',
                'num as num',
            ]);

            $result['list'] = $infoQb->fetchAll();
        }

        return $result;
    }

    /**
     * @param array $groupTA
     *
     * @return array|array[]
     */
    public function getDimensionGroupTA(array $groupTA): array
    {
        if (empty($groupTA)) return [];

        $result = ['list' => []];
        $db     = $this->getConn();
        $qb     = null;

        foreach ($groupTA as $foo) {
            [
                'tday'       => $tDay,
                'account_id' => $accountId,
            ] = $foo;

            if (empty($tDay) || empty($accountId)) {
                continue;
            }

            $newQb = $db
                ->select()
                ->from('ddc_platform.dwd_media_account_snapshot_audience_analyze')
                ->where('tday', $tDay)
                ->where('account_id', $accountId)
                ->columns([
                    'tday as tday',
                    'account_id as account_id',
                    'data_type as data_type',
                    'dimension as dimension',
                    'num as num',
                ]);

            if (is_null($qb)) {
                $qb = $newQb;
            }
            else {
                $qb->unionAll($newQb);
            }
        }

        if (!is_null($qb)) {
            $result['list'] = $qb->fetchAll();
        }

        return $result;
    }


}
<?php
/* Smarty version 5.0.0-rc3, created on 2025-06-23 12:11:16
  from 'file:sql/advertise/ad_dashboard/creative_dash_info.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.0.0-rc3',
  'unifunc' => 'content_6858d3e47674e0_86932520',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'ee869ccf65f5337673d7dfc73f94e3825c73fc5e' => 
    array (
      0 => 'sql/advertise/ad_dashboard/creative_dash_info.tpl',
      1 => 1750651077,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
))) {
function content_6858d3e47674e0_86932520 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = '/mnt/e/project/spy2.0-api/public/templates/sql/advertise/ad_dashboard';
?>with creative_ocean_table as (
select
    `tday`,
    `channel_id`,
    `package_id`,
    IF(null_or_empty(campaign_id) = 1 or campaign_id = '0', '', campaign_id) as campaign_id,
    IF(null_or_empty(plan_id) = 1 or plan_id = '0', '', plan_id) as plan_id,
    IF(null_or_empty(creative_id) = 1 or creative_id = '0', '', creative_id) as creative_id,
    `cp_game_id`,
    `game_id`,
    `main_channel_id`,
    `account_id`,
    `show`,
    `click`,
    `download`,
    `activate`,
    `convert`,
    `install`,
    `lp_view`,
    `lp_download`,
    `download_start`,
    `register`,
    `cost`,
    `cost_discount`,
    new_real_user,
    new_user,
    new_user_emulator,
    activate_device,
    create_role_new,
    pay_new_user_7days,
    pay_frequency_7days,
    online_time,
    first_online_time,
    active_user,
    active_user_week,
    total_play,
    play_time_per_play,
    play_duration_3s,
    `user_id`,
    is_ad_data,
    is_appointment,
    marketing_goal,
    pay_user,
    pay_user_list,
    pay_money,
    pay_count,
    pay_user_new,
    pay_user_new_list,
    pay_money_new,
    pay_count_new,
    pay_money_no_visual,
    pay_money_new_no_visual,
    `update_time`
from
    bigdata_dws.dws_ad_creative_daily
<?php if (!empty($_smarty_tpl->getValue('params'))) {?>
    <?php $_smarty_tpl->assign('first_mark', 1, false, NULL);?>
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'foo', false, 'kk');
$foreach5DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('kk')->value => $_smarty_tpl->getVariable('foo')->value) {
$foreach5DoElse = false;
?>
        <?php if ($_smarty_tpl->getValue('kk') == "range_date") {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            tday between '<?php echo $_smarty_tpl->getValue('foo')[0];?>
' and '<?php echo $_smarty_tpl->getValue('foo')[1];?>
' <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'cp_game_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                cp_game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                cp_game_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'game_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                game_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'game_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            game_id in (
                select distinct data_id from base_conf_platform.biz_tags
                    where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'games'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'package_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                package_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                package_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'package_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            package_id in (
                select distinct data_id from base_conf_platform.biz_tags
                    where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'packages'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'account_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            account_id like  '<?php echo (('%').($_smarty_tpl->getValue('foo'))).('%');?>
'
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'campaign_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                campaign_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                campaign_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'campaign_name') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            campaign_id in (select distinct campaign_id from adp_platform.tb_adp_campaign where campaign_name like '<?php echo (('%').($_smarty_tpl->getValue('foo'))).('%');?>
')
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'plan_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                plan_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                plan_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'plan_name') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            (
            plan_id in (select distinct plan_id from adp_platform.tb_adp_plan_base where plan_name like '<?php echo (('%').($_smarty_tpl->getValue('foo'))).('%');?>
')
            or plan_id in (select distinct id  from dataspy.tb_ad_svlink_conf where aid like '<?php echo (('%').($_smarty_tpl->getValue('foo'))).('%');?>
')
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'creative_id') {?>
            <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                creative_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                creative_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'data_scope') {?>
            <?php if ($_smarty_tpl->getValue('foo') == 1) {?>
                <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?> is_ad_data = 1
            <?php } elseif ($_smarty_tpl->getValue('foo') == 2) {?>
                <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?> is_ad_data = 0
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'marketing_goal') {?>
            <?php if ($_smarty_tpl->getValue('foo') != array(1,2) || $_smarty_tpl->getValue('foo') != array(2,1)) {?>
                <?php if ($_smarty_tpl->getSmarty()->getModifierCallback('in_array')(1,$_smarty_tpl->getValue('foo'))) {?>
                    <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?> marketing_goal != 2
                <?php }?>
                <?php if ($_smarty_tpl->getSmarty()->getModifierCallback('in_array')(2,$_smarty_tpl->getValue('foo'))) {?>
                    <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?> marketing_goal = 2
                <?php }?>
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'is_has_appointment') {?>
            <?php if (empty($_smarty_tpl->getValue('foo'))) {?> <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?> is_appointment != 1 <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'is_has_natural') {?>
            <?php if (empty($_smarty_tpl->getValue('foo'))) {?> <?php if (!$_smarty_tpl->getValue('first_mark')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark', 0, false, NULL);?> <?php }?> channel_id > 0 <?php }?>
            <?php continue 1;?>
        <?php }?>
    <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}?>
),
dashboard_info as (

    select
        b1.tday,
        DATE_FORMAT(b1.tday,'%Y-%m') as tmonth,
        b1.cp_game_id,
        b1.game_id,
        b1.package_id,
        b1.campaign_id,
        any(coalesce(b2.campaign_name, '')) as campaign_name,
        b1.plan_id,
        b1.creative_id,
        b1.main_channel_id,
        b1.account_id,
        b1.ad_account,
        any(b1.platform_id) as platform_id,
        any(b1.account_name) as account_name,
        any(b1.is_ad_data) as is_ad_data,
        any(b1.is_appointment) as is_appointment,
        any(b1.marketing_goal) as marketing_goal,
        any(case when user_os = '["ANDROID"]' then 'ANDROID' when user_os = '["IOS"]' then 'IOS' else '混投' end)  as dim_user_os,
        any(b1.popularize_v2_id) as promotion_id,
        any(b1.app_show_id) as app_show_id,
        b1.promotion_channel_id,
        b1.dim_user_id
        <?php if (empty($_smarty_tpl->getValue('base_only_dimension'))) {?>
            ,sum(`show`)  as show_cnt,
            sum(click)   as click_cnt,
            sum(download) as download_cnt,
            sum(activate) as activate_cnt,
            sum(`convert`) as convert_cnt,
            sum(`install`) as install_cnt,
            sum(lp_view) as lp_view,
            sum(lp_download) as lp_download,
            sum(download_start) as download_start,
            sum(register) as register,
            sum(cost) as cost,
            sum(cost_discount) as cost_discount,
            sum(new_real_user) as new_real_user,
            sum(new_user) as new_user,
            sum(new_user_emulator) as new_user_emulator,
            sum(activate_device) as activate_device,
            sum(create_role_new) as create_role_new,
            sum(pay_new_user_7days) as pay_new_user_7days,
            sum(pay_frequency_7days) as pay_frequency_7days,
            sum(online_time) as online_time,
            sum(first_online_time) as first_online_time,
            sum(active_user) as active_user,
            sum(active_user_week) as active_user_week,
            sum(total_play) as total_play,
            sum(play_time_per_play) as play_time_per_play,
            sum(play_duration_3s) as play_duration_3s,
            sum(pay_user) as pay_user,
            sum(pay_money) as pay_money,
            sum(pay_count)  as pay_count,
            sum(pay_user_new) as pay_user_new,
            sum(pay_money_new) as pay_money_new,
            sum(pay_count_new) as pay_count_new,
            sum(pay_money_no_visual) as pay_money_no_visual,
            sum(pay_money_new_no_visual) as pay_money_new_no_visual,
            max(b1.update_time) as update_time
        <?php }?>
    from
        adp_platform.tb_adp_campaign b2
    right join (
    select
        <?php if ((null !== ($_smarty_tpl->getValue('ad_channels') ?? null))) {?>
            COALESCE( IF(power.channel_id not in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('ad_channels'),',');?>
), power.channel_id, IF(a1.channel_id != 0, IF(a1.channel_id =1013, 4, a1.channel_id), power.channel_id) ) ,0) as promotion_channel_id,
            COALESCE( IF(power.channel_id not in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('ad_channels'),',');?>
), power.ad_user_id, IF(a1.user_id != 0, a1.user_id, power.ad_user_id)),0 ) as dim_user_id,
        <?php } else { ?>
            a1.channel_id as promotion_channel_id,
            a1.user_id as dim_user_id,
        <?php }?>
        a1.*, a2.ad_account,
        a3.advertiser_name as account_name,
        power.platform_id,
        power.popularize_v2_id,
        power.app_show_id
        from creative_ocean_table a1
        <?php if (!empty($_smarty_tpl->getValue('power_join_sql')) && $_smarty_tpl->getValue('power_join_sql') == 'base_conf_platform.tb_package_detail_conf') {?>
            join (<?php echo $_smarty_tpl->getValue('power_join_sql');?>
) power on a1.package_id = power.package_id
        <?php } else { ?>
            join base_conf_platform.tb_package_detail_conf power on a1.package_id = power.package_id
        <?php }?>
        left join base_conf_platform.tb_ad_account_conf a2 on a1.account_id = a2.account_id
        left join adp_platform.tb_adp_oauth a3 on a1.account_id = a3.advertiser_id and a1.main_channel_id = a3.channel_id
    <?php if (!empty($_smarty_tpl->getValue('params'))) {?>
        <?php $_smarty_tpl->assign('first_mark_1', 1, false, NULL);?>
        <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'foo', false, 'kk');
$foreach6DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('kk')->value => $_smarty_tpl->getVariable('foo')->value) {
$foreach6DoElse = false;
?>
            <?php if ($_smarty_tpl->getValue('kk') == 'ad_account') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark_1', 0, false, NULL);?> <?php }?>
                a2.ad_account like '<?php echo (('%').($_smarty_tpl->getValue('foo'))).('%');?>
'
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'app_show_id') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark_1', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                    power.app_show_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
                <?php } else { ?>
                    power.app_show_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'platform_id') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark_1', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                    power.platform_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
                <?php } else { ?>
                    power.platform_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'promotion_id') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark_1', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                    power.popularize_v2_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
                <?php } else { ?>
                    power.popularize_v2_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
                <?php }?>
                <?php continue 1;?>
            <?php }?>
        <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
    <?php }?>
    ) b1 on b1.campaign_id = b2.campaign_id and b1.main_channel_id = b2.channel_id
    <?php if (!empty($_smarty_tpl->getValue('params'))) {?>
        <?php $_smarty_tpl->assign('first_mark_2', 1, false, NULL);?>
        <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'foo', false, 'key');
$foreach7DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('key')->value => $_smarty_tpl->getVariable('foo')->value) {
$foreach7DoElse = false;
?>
            <?php if ($_smarty_tpl->getValue('kk') == 'user_os') {?>
                <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                    <?php if (!$_smarty_tpl->getValue('first_mark_2')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark_2', 0, false, NULL);?> <?php }?>
                    (
                    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('foo'), 'chill', false, 'ii');
$_smarty_tpl->getVariable('chill')->index = -1;
$foreach8DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('ii')->value => $_smarty_tpl->getVariable('chill')->value) {
$foreach8DoElse = false;
$_smarty_tpl->getVariable('chill')->index++;
$_smarty_tpl->getVariable('chill')->first = !$_smarty_tpl->getVariable('chill')->index;
$foreach8Backup = clone $_smarty_tpl->getVariable('chill');
?>
                        <?php if (!$_smarty_tpl->getVariable('chill')->first) {?> or <?php }?>
                        <?php if ($_smarty_tpl->getValue('chill') == 1) {?> b2.user_os =  '["IOS"]'<?php }?>
                        <?php if ($_smarty_tpl->getValue('chill') == 2) {?> b2.user_os =  '["ANDROID"]'<?php }?>
                        <?php if ($_smarty_tpl->getValue('chill') == 3) {?> (b2.user_os != '["IOS"]' and b2.user_os != '["ANDROID"]')<?php }?>
                    <?php
$_smarty_tpl->setVariable('chill', $foreach8Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
                    )
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'campaign_name') {?>
                <?php if (!$_smarty_tpl->getValue('first_mark_2')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('first_mark_2', 0, false, NULL);?> <?php }?>
                b2.campaign_name like '<?php echo (('%').($_smarty_tpl->getValue('foo'))).('%');?>
'
            <?php }?>
        <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
    <?php }?>
    group by tday, b1.cp_game_id, b1.game_id, b1.package_id, promotion_channel_id, b1.campaign_id, b1.plan_id,
    b1.creative_id, b1.main_channel_id, b1.account_id, dim_user_id, b1.ad_account
)
<?php }
}

<?php

namespace app\extension\Support\Helpers;

/**
 * @ProcessLine 数据处理流程
 *              旨在在一个循环中根据任务处理顺序望下执行处理每行的数据
 */
class ProcessLine
{
    protected \SplQueue $line;
    protected array     $prependLine;

    public function __construct()
    {
        $this->line        = new \SplQueue();
        $this->prependLine = [];
    }

    /**
     * 添加数据处理流程
     *
     * @param $callback
     *
     * @return ProcessLine
     */
    public function addProcess($callback): ProcessLine
    {
        if ($this->isCallable($callback)) {
            $this->line->enqueue($callback);
        }

        return $this;
    }

    /**
     * 添加处理数据前提取想要每行处理返回的参数
     *
     * @param \Closure|callable ...$prependLines function ($target) {}
     *
     * @return ProcessLine
     */
    public function prependEachRow(...$prependLines): ProcessLine
    {
        foreach ($prependLines as $prependLine) {
            if (is_array($prependLine)) {
                $this->prependEachRow(...$prependLine);
            }

            $this->prependLine[] = $prependLine;
        }

        return $this;
    }

    /**
     * @param iterable $data
     *
     * @return void
     */
    public function run(iterable &$data)
    {
        $workLine = clone $this->line;
        $context  = new ProcessLineContext();

        foreach ($data as $k => &$target) {
            $this->prependRunEachRow($target, $context);

            $workLine->rewind();

            while ($workLine->valid()) {
                $callback = $workLine->current();

                if ($callback instanceof \Closure) {
                    $callback($target, $context, $k);
                }
                elseif (is_callable($callback)) {
                    call_user_func_array($callback, [&$target, &$context, $k]);
                }

                $workLine->next();
            }

            $context->clear();
        }
    }

    /**
     * @param                    $target
     * @param ProcessLineContext $context
     *
     * @return void
     */
    protected function prependRunEachRow(&$target, ProcessLineContext &$context)
    {
        if (0 === count($this->prependLine)) return;

        foreach ($this->prependLine as $callback) {
            if ($callback instanceof \Closure) {
                $callback($target, $context);
            }
            elseif (is_callable($callback)) {
                call_user_func_array($callback, [$target, &$context]);
            }
        }
    }


    /**
     * @param $callback
     *
     * @return bool
     */
    private function isCallable($callback): bool
    {
        return ($callback instanceof \Closure)
            || is_callable($callback);
    }


}
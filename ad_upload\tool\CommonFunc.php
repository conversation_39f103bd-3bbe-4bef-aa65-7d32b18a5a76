<?php

namespace app\ad_upload\tool;

use app\ad_upload\contract\AdBaseInterface;

/**
 * 通用函数
 * <AUTHOR>
 */
class CommonFunc
{
    /**
     * 获取毫秒时间戳
     * @return bool|string
     */
    public static function getMillisecond()
    {
        [$msec, $sec] = explode(' ', microtime());
        $msectime     = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

        return substr($msectime, 0, 13);
    }

    /**
     * 数组转字符串
     * @param array   $arr       数组
     * @param string  $linkChar  连接符
     * @param boolean $readValue 是否读取值
     * @param boolean $noSymbol  无引号
     * @return string
     */
    public static function arr2Str($arr, $linkChar = ',', $readValue = true, $noSymbol = false)
    {
        $str    = '';
        $symbol = $noSymbol ? '' : "'";
        foreach ($arr as $key => $val) {
            $linkData = $readValue ? $val : $key;
            $str     .= "{$linkChar}{$symbol}{$linkData}{$symbol}";
        }
        $linkLen = strlen($linkChar);
        $str     = strlen($str) > $linkLen ? substr($str, $linkLen) : '';
        return $str;
    }

    /**
     * 获取小程序子渠道
     * @return array
     */
    public static function getXcxChannelId()
    {
        $sql  = "SELECT DATA_ID FROM base_conf_platform.biz_tags WHERE TAG_ID = 961 and TABLE_NAME ='app_channel'";
        $list = \Plus::$app->base_conf_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        return array_column($list, "DATA_ID");
    }

    /**
     * 小程序包ID
     * @var array
     */
    private static $xcxPackageId = [];

    /**
     * 获取小程序包ID, 注意，只能在定时任务中调用，不能在常驻进程中调用
     * @return array
     */
    public static function getXcxPackageId()
    {
        if (!empty(self::$xcxPackageId)) {
            return self::$xcxPackageId;
        }

        // 获取渠道ID列表
        $channelIds = self::getXcxChannelId();
        if (empty($channelIds)) {
            return [];
        }
        // 构建SQL查询语句
        $sql = "SELECT PACKAGE_ID FROM base_conf_platform.tb_package_detail_conf WHERE CHANNEL_ID IN(" . implode(",", $channelIds) . ")";
        try {
            // 执行数据库查询
            $data = \Plus::$app->base_conf_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            if (empty($data)) {
                return [];
            }
            // 提取PACKAGE_ID并缓存
            self::$xcxPackageId = array_column($data, "PACKAGE_ID");
        } catch (\Throwable $e) {
            // 处理异常情况
            \Plus::$app->log->error("查询微信小程序包ID失败: " . $e->getMessage(), [], AdBaseInterface::LOG_DIR_ERR);
            return [];
        }
        return self::$xcxPackageId;
    }

    /**
     * 检查是否已上报
     * 查上报日志
     * @param string $orderId 订单号
     * @return bool
     */
    public static function checkPayUploaded($orderId)
    {
        //先查缓存
        $key = 'ad_up_repeat_pay';
        if (\Plus::$app->redis82->hget($key, $orderId)) {
            \Plus::$app->log->warning($key.'_'.$orderId, [], AdBaseInterface::LOG_DIR);
            return true;
        }
        /**
         * 写日志表的时候才能设置缓存，不然未归因订单、处理失败订单 会一直被过滤掉
         * set 缓存在：
         * @var \app\ad_upload\contract\LogDbTrait::logPaidToDoris
         *
         */
        // 查询数据库
        $db = \Plus::$app->doris_entrance;
        if (APP_EVN == 'DEV') {
            $db = \Plus::$app->doris_entrance2; //测试环境读本地的
        }
        $sql    = "SELECT COUNT(1) as roz from bigdata_dwd.dwd_reported_paid_platform_log where order_id = '{$orderId}' and reported_status =1";
        $result = $db->query($sql)->fetch(\PDO::FETCH_ASSOC);
        $count  = $result['roz'] ?? 0;
        return $count > 0;
    }

    /**
     * 获取时间差
     *
     * 该函数用于计算两个日期时间字符串之间的差异，并返回格式化后的时间差。
     *
     * @param string $base   基准时间字符串
     * @param string $target 目标时间字符串
     * @param string $format 返回的时间差格式，默认为 '%a' 表示星期数
     * @return string         格式化后的时间差字符串
     */
    public static function dateDiff($base, $target, $format = '%a')
    {
        // 遍历基准时间和目标时间，如果不是 \DateTime 对象，则将其转换为 \DateTime 对象
        foreach ([&$base, &$target] as &$foo) {
            if (!($foo instanceof \DateTime)) {
                $foo = new \DateTime($foo);
            }
        }
        // 使用 date_diff 函数计算时间差，并返回格式化后的结果
        return date_diff($base, $target)->format($format);
    }


    /**
     * 获取客户端类型
     * @var array
     */
    private static $packageClient = [];

    /**
     * 获取客户端类型
     * @param string $packageId 包号
     * @return string
     */
    public static function getClientType($packageId)
    {
        if (isset(self::$packageClient[$packageId])) {
            return self::$packageClient[$packageId];
        }
        $platformId = \Plus::$app->base_conf_platform->get(
            'tb_base_package_conf',
            'platform_id',
            ['package_id' => $packageId]
        );
        $rs         = '未知';
        if ($platformId == 101) {
            $rs = 'android';
        }
        if ($platformId == 102) {
            $rs = 'ios';
        }
        self::$packageClient[$packageId] = $rs;
        return $rs;
    }
}

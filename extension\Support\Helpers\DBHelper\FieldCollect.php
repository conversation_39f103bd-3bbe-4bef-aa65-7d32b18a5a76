<?php

namespace app\extension\Support\Helpers\DBHelper;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Contracts\Arrayable;

/**
 * @FieldCollect 简单的一个集合, 单纯为了方便输出字段
 *
 */
class FieldCollect implements Arrayable, \ArrayAccess, \Countable
{
    protected array $collect = [];

    public function __construct(array $collect = [])
    {
        if (!empty($collect)) {
            foreach ($collect as $k => $option) {
                $this->collect[$k] = $option;
            }
        }
    }

    /**
     * @param       $field
     * @param array $option
     *
     * @return $this
     */
    public function set($field, array $option = []): FieldCollect
    {
        $this->collect[$field] = $option;

        return $this;
    }

    /**
     * @param bool $isSummaryRow
     * @param bool $isNotAggregate
     * @param bool $onlyKeyReflect
     * @param bool $isChangeField
     * @param array $filterCol
     *
     * @return array
     */
    public function output(
        bool $isSummaryRow = false,
        bool $isNotAggregate = false,
        bool $onlyKeyReflect = false,
        bool $isChangeField = true,
        array $filterCol = []
    ): array
    {
        $result  = [];
        $collect = $this->collect;

        if ($isSummaryRow) {

            // 剔除不需要计算的字段
            foreach ($collect as $k => $item) {
                if (in_array('info', $item)) {
                    unset($collect[$k]);
                }
            }

        }

        foreach ($collect as $key => $item) {
            //过滤字段
            if($filterCol && in_array($key,$filterCol)){
                continue;
            }
            if (isset($item['aggregate']) && !$isNotAggregate) {
                $aggregate = $item['aggregate'];

                $this->isPassIfNullAggregate($aggregate)
                    ? $format = "IFNULL({$aggregate}(%s), 0) as %s"
                    : $format = "{$aggregate}(%s) as %s";
            }
            else {
                $format = '%s as %s';
            }

            if ($isChangeField) {
                $field = '';
                if (isset($item['table'])) {
                    $field .= $item['table'] . '.';
                }

                $field .= $item['source_field'] ?? "`{$key}`";
            }
            else {
                $field = "`{$key}`";
            }

            if (isset($item['raw']) && !$onlyKeyReflect) {
                $result[] = $item['raw'];
                continue;
            }

            if ($onlyKeyReflect) {
                $result[] = sprintf($format, "`{$key}`", $key);
            }
            else {
                $result[] = sprintf($format, $field, $key);
            }
        }
        return $result;
    }

    /**
     * @param $aggregate
     *
     * @return bool
     */
    private function isPassIfNullAggregate($aggregate): bool
    {
        return in_array($aggregate, ['sum', 'avg']);
    }


    public function toArray(): array
    {
        return array_values($this->collect);
    }

    public function offsetExists($offset): bool
    {
        return isset($this->collect[$offset]);
    }

    public function offsetGet($offset)
    {
        return $this->collect[$offset] ?? null;
    }

    public function offsetSet($offset, $value)
    {
        $this->collect[$offset] = $value;
    }

    public function offsetUnset($offset)
    {
        unset($this->collect[$offset]);
    }

    public function count(): int
    {
        return count($this->collect);
    }

}
<?php

namespace app\util;

use Plus;

/**
 * 基于 redis 单机锁
 * @package app\util
 * <AUTHOR>
 */
class Lock
{
    /**
     * $lockName
     * @var string $lockName
     */
    private $lockName;

    /**
     * 构造函数
     * @param string $lockName 锁的名称
     */
    public function __construct($lockName)
    {
        $this->lockName = sprintf("PLUS_DISTRIBUTE_LOCK_%s", $lockName);
    }

    /**
     * 获取分布式锁
     * @param float $timeout 锁的超时时间，如果设定，超过指定时间锁会释放
     * @return bool 是否获取到锁
     */
    public function aquire($timeout = 0)
    {
        $redis = Plus::$app->redis;
        if (!$redis->get($this->lockName)) {
            return $redis->set($this->lockName, 1, $timeout);
        } else {
            return false;
        }
    }

    /**
     * 释放锁
     * @return bool result
     */
    public function release()
    {
        return Plus::$app->redis->delete($this->lockName);
    }
}

<?php

namespace app\service\ConfigService\Tables;

use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class AdUploadCost implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    public function getFields($options = null)
    {
        $fields = collect([
            'tday'            => ['title' => '日期', 'sorter'    => true, 'classify' => ['attrs', 'base']],
            'cp_game_id'      => ['title' => '游戏原名', 'sorter'  => true, 'classify' => ['attrs', 'base']],
            'game_id'         => ['title' => '游戏统计名', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'platform_id'     => ['title' => '客户端', 'sorter'   => true, 'classify' => ['attrs', 'base']],
            'channel_id'      => ['title' => '推广子渠道', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'package_id'      => ['title' => '包号', 'sorter'    => true, 'classify' => ['attrs', 'base']],
            'plan_id'         => ['title' => '广告组ID', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'aid'             => ['title' => '广告组名称', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'ad_account'      => ['title' => '投放账户', 'sorter'  => true, 'classify' => ['attrs', 'base']],
            'account_id'      => ['title' => '账号ID', 'sorter'  => true, 'classify' => ['attrs', 'base']],
            'settlement_id'   => ['title' => '结算方式', 'sorter'  => true, 'classify' => ['attrs', 'base']],
            'agent_id'        => ['title' => '代理', 'sorter'    => true, 'classify' => ['attrs', 'base']],
            'operate_id'      => ['title' => '运营方式', 'sorter'  => true, 'classify' => ['attrs', 'base']],
            'explain_id'      => ['title' => '区分说明', 'sorter'  => true, 'classify' => ['attrs', 'base']],
            'department_id'   => ['title' => '所属部门', 'sorter'  => true, 'classify' => ['attrs', 'base']],
            'user_id'         => ['title' => '投放人', 'sorter'   => true, 'classify' => ['attrs', 'base']],
            'update_user_id'  => ['title' => '修改人', 'sorter'   => true, 'classify' => ['attrs', 'base']],
            'update_time'     => ['title' => '修改时间', 'sorter'  => true, 'classify' => ['attrs', 'base']],


            'show'          => ['title' => '展示', 'sorter'      => true, 'classify' => ['attrs', 'media_index']],
            'click'         => ['title' => '点击', 'sorter'      => true, 'classify' => ['attrs', 'media_index']],
            'cost'          => ['title' => '返点前消耗金额', 'sorter' => true, 'classify' => ['attrs', 'media_index']],
            'cost_discount' => ['title' => '返点后消耗金额', 'sorter' => true, 'classify' => ['attrs', 'media_index']],

        ]);

        return $this->formatStandard($fields);
    }
}
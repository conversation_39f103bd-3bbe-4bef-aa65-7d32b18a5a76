<?php declare(ticks=1);

namespace app\daemon\Universal;

use app\service\General\ExportManagerServ;
use framework\rabbit\RabbitQueue;
use League\CLImate\CLImate;
use League\CLImate\Logger;
use PHP_CodeSniffer\Tokenizers\PHP;
use PhpAmqpLib\Connection\Heartbeat\PCNTLHeartbeatSender;
use PhpAmqpLib\Exception\AMQPConnectionClosedException;
use PhpAmqpLib\Message\AMQPMessage;
use Plus\CLI\DaemonProcess;

/**
 * @\app\daemon\Universal\ExportExcel
 * 通用下载守护进程,监听队列下载
 *
 * @package app/daemon
 */
class ExportExcel extends DaemonProcess
{
    private const QueueTopic = 'report_export';

    /**
     * 最大重试次数
     */
    private const MAX_TRY = 5;

    public function __construct()
    {
    }

    /**
     * @throws \Exception
     */
    public function run()
    {
        $isExit = false;
        $retry  = 3;
        @ini_set('memory_limit', -1);

        echo 'is running...' . PHP_EOL;

        while ($retry > 0 && $this->running) {
            try {
                $rabbit = \Plus::$app->universal_mq->getConnection();
                $sender = new PCNTLHeartbeatSender($rabbit);
                $sender->register();

                $channel = $rabbit->channel();
                $channel->queue_declare(static::QueueTopic, false, true, false, false);
                $channel->basic_qos((int)null, 1, (bool)null);
                $channel->basic_consume(
                    static::QueueTopic, '', false, false, false, false, [$this, 'jobConsumer']
                );

                /**
                 * 注册信号
                 */
                pcntl_signal(SIGUSR1, function ($signal) use (&$isExit) {
                    $isExit = true;
                });

                pcntl_signal(SIGTERM, function ($signal) use (&$isExit) {
                    $isExit = true;
                });

                while ($channel->is_open()) {
                    pcntl_signal_dispatch();
                    echo 'waiting message' . PHP_EOL;

                    try {
                        $channel->wait();
                    }
                    catch (AMQPConnectionClosedException $e) {
                        echo $e->getMessage() . PHP_EOL;
                    }
                    catch (\Throwable $throwable) {
                        echo $throwable->getMessage() . PHP_EOL;
                    }

                    if ($isExit) {
                        exit('收到中断信号, 程序退出');
                    }

                }

                $sender->unregister();
            }
            catch (\Throwable $throwable) {
                echo $throwable->getMessage() . PHP_EOL;
                echo $throwable->getTraceAsString() . PHP_EOL;

                if (!empty($sender)) {
                    $sender->unregister();
                }
                if (!empty($channel)) {
                    if ($channel->is_open()) {
                        $channel->close();
                    }
                }

                $retry--;
            }
        }

        echo '程序退出...' . PHP_EOL;
    }

    /**
     * 队列消费行为
     *
     * @return void
     * @example [
     *          "request_uri": {导出数据的接口},
     *          "operator" : {操作用户的ID},
     *          "fields": [{导出的列}]
     *          ]
     */
    public function jobConsumer(AMQPMessage $message)
    {
        try {
            $messageBody = $message->body;

            echo 'have message:' . PHP_EOL;
            echo $messageBody . PHP_EOL;

            $serv   = new ExportManagerServ();
            $body   = \json_decode($messageBody, true);
            $action = $body['action'] ?? null;
            $tryN   = $message->getDeliveryTag();

            if (empty($action)) {
                $message->nack();
                return;
            }

            try {
                [
                    'is_ack'  => $isAck,
                    'message' => $jobMessage,
                ] = $serv->exportOffLine($body);
            }
            catch (\Throwable $e) {
                $isAck      = false;
                $jobMessage = $e->getMessage() . "\n" . $e->getTraceAsString();
            }

            if ($isAck) {
                $message->ack();
            }
            else {
                if ($tryN >= static::MAX_TRY) {
                    $message->nack();
                    $serv->updateJob($body['id'], ['job_status' => -1, 'remark' => $jobMessage]);
                }
                else {
                    $message->nack(true, true);
                    $serv->updateJob($body['id'], ['job_status' => 1,]);
                }
            }
        }
        catch (\JsonException|\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            echo $e->getTraceAsString() . PHP_EOL;
            $message->ack();
        }
    }


}
<?php
/* Smarty version 5.0.0-rc3, created on 2025-06-30 11:01:26
  from 'file:sql/operator/firstlogin_dash/firstlogin_dash_daily.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.0.0-rc3',
  'unifunc' => 'content_6861fe061a2442_15649532',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '4c8689ea29ed4a53876832793551c7e5a58f7284' => 
    array (
      0 => 'sql/operator/firstlogin_dash/firstlogin_dash_daily.tpl',
      1 => 1749631986,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
))) {
function content_6861fe061a2442_15649532 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = '/mnt/e/project/spy2.0-api/public/templates/sql/operator/firstlogin_dash';
?>with dash_detail as (
        select
        <?php if ($_smarty_tpl->getValue('time_range_type') == 3) {?>
            case
            <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('range_days'), 'dd');
$foreach0DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('dd')->value) {
$foreach0DoElse = false;
?>
                when a1.tday between '<?php echo $_smarty_tpl->getValue('dd')['begin_date'];?>
' and '<?php echo $_smarty_tpl->getValue('dd')['end_date'];?>
' then '<?php echo (($_smarty_tpl->getValue('dd')['begin_date']).('/')).($_smarty_tpl->getValue('dd')['end_date']);?>
'
            <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
            end as tday,
        <?php } else { ?>
            tday,
        <?php }?>
        power.cp_game_id,
        power.game_id,
        power.app_show_id,
        power.channel_main_id,
        power.channel_id,
        power.platform_id,
        a1.package_id,
        power.popularize_v2_id as promotion_id,
        power.ad_department_id as department_id,
        power.ad_user_id as user_id,
        a1.is_multiple,
        a1.activate_device,
        a1.cost,
        a1.cost_discount,
        a1.firstlogin_user,
        a1.firstlogin_device,
        <?php if ($_smarty_tpl->getValue('time_range_type') == 3) {?>
            case
                <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('range_days'), 'dd');
$foreach1DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('dd')->value) {
$foreach1DoElse = false;
?>
                    when a1.tday = '<?php echo $_smarty_tpl->getValue('dd')['begin_date'];?>
' then a1.firstlogin_active_user_week
                <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
            else 0
            end as firstlogin_active_user,
            case
            <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('range_days'), 'dd');
$foreach2DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('dd')->value) {
$foreach2DoElse = false;
?>
                when a1.tday = '<?php echo $_smarty_tpl->getValue('dd')['begin_date'];?>
' then a1.firstlogin_pay_user_week
            <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
            else 0
            end as firstlogin_pay_user,
            case
            <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('range_days'), 'dd');
$foreach3DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('dd')->value) {
$foreach3DoElse = false;
?>
                when a1.tday = '<?php echo $_smarty_tpl->getValue('dd')['begin_date'];?>
' then a1.pay_user_week
            <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
            else 0
            end as pay_user,
        <?php } else { ?>
            a1.firstlogin_active_user,
            a1.firstlogin_pay_user,
            a1.pay_user,
        <?php }?>
        a1.firstlogin_pay_user_new,
        a1.firstlogin_role,
        a1.firstlogin_role2,
        a1.firstlogin_real_user,
        a1.active_user_week,
        a1.firstlogin_active_user_7_days_ago,
        a1.firstlogin_active_user_pay,
        a1.active_user_7_days_ago,
        a1.new_user,
        a1.new_device,
        a1.new_real_user,
        a1.new_real_user_der,
        a1.reg_user,
        a1.reg_device,
        a1.new_user_imulator,
        a1.active_user_imulator,
        a1.active_user_imulator_bind,
        a1.create_role,
        a1.pay_user_newlogin,
        a1.pay_money_newlogin,
        a1.pay_money_reg,
        a1.update_time,
        a1.pay_money,
        a1.firstlogin_pay_money_new,
        a1.order_count,
        a1.order_success
from bigdata_dws.dws_package_all_daily a1
    <?php if (!empty($_smarty_tpl->getValue('power_join_sql')) && $_smarty_tpl->getValue('power_join_sql') == 'base_conf_platform.tb_package_detail_conf') {?>
        join (<?php echo $_smarty_tpl->getValue('power_join_sql');?>
) power on a1.package_id = power.package_id
    <?php } else { ?>
        join base_conf_platform.tb_package_detail_conf power on a1.package_id = power.package_id
    <?php }
if (!empty($_smarty_tpl->getValue('params'))) {?>
    <?php $_smarty_tpl->assign('tag_first_where', 1, false, NULL);?>
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'foo', false, 'kk');
$foreach4DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('kk')->value => $_smarty_tpl->getVariable('foo')->value) {
$foreach4DoElse = false;
?>
        <?php if ($_smarty_tpl->getValue('kk') == 'range_date') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            a1.tday between '<?php echo $_smarty_tpl->getValue('foo')[0];?>
' and '<?php echo $_smarty_tpl->getValue('foo')[1];?>
'
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'cp_game_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                power.cp_game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                power.cp_game_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'game_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                power.game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                power.game_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'app_show_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                power.app_show_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                power.app_show_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'package_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                a1.package_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                a1.package_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'channel_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                power.channel_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                power.channel_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'channel_main_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                power.channel_main_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                power.channel_main_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'channel_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            power.channel_id in (
                select distinct data_id from base_conf_platform.biz_tags
                where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'app_channel'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'package_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            a1.package_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'packages'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'channel_main_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            power.channel_main_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'package_channel_main'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'game_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            power.game_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'games'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'platform_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                power.platform_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                power.platform_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'promotion_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                power.popularize_v2_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                power.popularize_v2_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'department_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                power.ad_department_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                power.ad_department_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'user_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                power.ad_user_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                power.ad_user_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'has_no_cost') {?>
            <?php if ($_smarty_tpl->getValue('foo') == 0) {?>
                <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
                a1.cost > 0
            <?php }?>
        <?php }?>
    <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}?>
),paid_per_info as (
select tday, package_id, SUM(pay_user_num) AS `pay_user_per`
from (
SELECT tday,
b1.package_id,
row_number() over (partition by tday, b1.package_id order by day_type desc ) as rowz,
pay_user_num
FROM `ddc_platform`.`dws_fristlogin_pay_permeation_daily` b1
left join base_conf_platform.tb_package_detail_conf b2 on b1.package_id = b2.package_id
<?php if (!empty($_smarty_tpl->getValue('params'))) {?>
    <?php $_smarty_tpl->assign('tag_first_per_where', 1, false, NULL);?>

    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'foo', false, 'kk');
$foreach5DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('kk')->value => $_smarty_tpl->getVariable('foo')->value) {
$foreach5DoElse = false;
?>
        <?php if ($_smarty_tpl->getValue('kk') == 'range_date') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            b1.tday between '<?php echo $_smarty_tpl->getValue('foo')[0];?>
' and '<?php echo $_smarty_tpl->getValue('foo')[1];?>
'
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'cp_game_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b2.cp_game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b2.cp_game_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'game_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b2.game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b2.game_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'app_show_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b2.app_show_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b2.app_show_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'package_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b1.package_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b1.package_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'channel_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b2.channel_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b2.channel_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'channel_main_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b2.channel_main_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b2.channel_main_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'channel_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            b2.channel_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'app_channel'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'package_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            b2.package_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'packages'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'channel_main_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            b2.channel_main_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'package_channel_main'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'game_id_tags') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            b2.game_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
) and table_name = 'games'
            )
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'platform_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b2.platform_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b2.platform_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'promotion_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b2.popularize_v2_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b2.popularize_v2_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'department_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b2.ad_department_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b2.ad_department_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
        <?php if ($_smarty_tpl->getValue('kk') == 'user_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_per_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_per_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('foo'))) {?>
                b2.ad_user_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('foo'),',');?>
)
            <?php } else { ?>
                b2.ad_user_id = '<?php echo $_smarty_tpl->getValue('foo');?>
'
            <?php }?>
            <?php continue 1;?>
        <?php }?>
    <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>

<?php }?>
) c1
where rowz = 1
group by tday, package_id
)
select
    <?php if (!empty($_smarty_tpl->getValue('groups'))) {?>
        <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('groups'), 'item', true);
$_smarty_tpl->getVariable('item')->iteration = 0;
$foreach6DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('item')->value) {
$foreach6DoElse = false;
$_smarty_tpl->getVariable('item')->iteration++;
$_smarty_tpl->getVariable('item')->last = $_smarty_tpl->getVariable('item')->iteration === $_smarty_tpl->getVariable('item')->total;
$foreach6Backup = clone $_smarty_tpl->getVariable('item');
?>
            <?php if ($_smarty_tpl->getValue('item') == 'tday') {?> dash_detail.tday,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'cp_game_id') {?> cp_game_id,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'game_id') {?> game_id,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'app_show_id') {?> app_show_id,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'channel_main_id') {?> channel_main_id,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'channel_id') {?> channel_id,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_id') {?> promotion_id,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'user_id') {?> user_id,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'department_id') {?> department_id,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'package_id') {?> dash_detail.package_id,
            <?php } elseif ($_smarty_tpl->getValue('item') == 'platform_id') {?> platform_id,
            <?php }?>
        <?php
$_smarty_tpl->setVariable('item', $foreach6Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
    <?php }?>
    IFNULL(sum(activate_device), 0)                   AS activate_device,
    IFNULL(sum(cost), 0)                              AS cost,
    IFNULL(sum(cost_discount), 0)                     AS cost_discount,
    IFNULL(sum(firstlogin_user), 0)                   AS firstlogin_user,
    IFNULL(sum(firstlogin_device), 0)                 AS firstlogin_device,
    IFNULL(sum(firstlogin_active_user), 0)            AS firstlogin_active_user,
    IFNULL(sum(firstlogin_pay_user_new), 0)           AS firstlogin_pay_user_new,
    IFNULL(sum(firstlogin_pay_user), 0)               AS firstlogin_pay_user,
    IFNULL(sum(firstlogin_role), 0)                   AS create_role_new,
    IFNULL(sum(firstlogin_role2), 0)                  AS firstlogin_role2,
    IFNULL(sum(firstlogin_real_user), 0)              AS firstlogin_real_user,
    IFNULL(sum(active_user_week), 0)                  AS active_user_week,
    IFNULL(sum(firstlogin_active_user_7_days_ago), 0) AS active_user_7days_ago,
    IFNULL(sum(firstlogin_active_user_pay), 0)        AS active_pay_user,
    IFNULL(sum(active_user_7_days_ago), 0)            AS active_user_7_days_ago,
    IFNULL(sum(new_user), 0)                          AS new_user,
    IFNULL(sum(new_device), 0)                        AS new_device,
    IFNULL(sum(new_real_user), 0)                     AS new_real_user,
    IFNULL(sum(new_real_user_der), 0)                 AS new_real_user_der,
    IFNULL(sum(reg_user), 0)                          AS reg_user,
    IFNULL(sum(reg_device), 0)                        AS reg_device,
    IFNULL(sum(firstlogin_active_user), 0)            AS active_user,
    IFNULL(sum(new_user_imulator), 0)                 AS new_user_imulator,
    IFNULL(sum(active_user_imulator), 0)              AS active_user_imulator,
    IFNULL(sum(active_user_imulator_bind), 0)         AS active_user_imulator_bind,
    IFNULL(sum(create_role), 0)                       AS create_role,
    IFNULL(sum(pay_user_newlogin), 0)                 AS pay_user_newlogin,
    IFNULL(sum(pay_money_newlogin), 0)                AS pay_money_newlogin,
    IFNULL(sum(pay_money_reg), 0)                     AS pay_money_reg,
    max(update_time)                                  AS update_time,
    IFNULL(sum(pay_money), 0)                         AS pay_money,
    IFNULL(sum(pay_user), 0)                          AS pay_user,
    IFNULL(sum(firstlogin_pay_user_new), 0)           AS pay_new_user,
    IFNULL(sum(firstlogin_pay_money_new), 0)          AS pay_money_new,
    IFNULL(sum(order_count), 0)                       AS order_count,
    IFNULL(sum(order_success), 0)                     AS order_success,
    coalesce(round(sum(firstlogin_user)/sum(firstlogin_device) *100, 2),0) as device_conversion_rate,
    coalesce(round(sum(firstlogin_role)/sum(firstlogin_user) *100, 2),0) as create_role_percent,
    coalesce(round(sum(firstlogin_real_user)/sum(firstlogin_user) *100, 2),0) as fistlogin_real_name,
    coalesce(round(sum(firstlogin_pay_user_new)/sum(firstlogin_user) *100, 2),0) as pay_user_new_percent,
    coalesce(round(sum(firstlogin_pay_money_new)/sum(firstlogin_user), 2),0) as arpu_new_user,
    coalesce(round(sum(firstlogin_pay_money_new)/sum(firstlogin_pay_user_new), 2),0) as arppu_new_user,
    coalesce(round(sum(cost_discount)/sum(firstlogin_user), 2),0) as new_user_cost,
    coalesce(round(sum(pay_user)/sum(firstlogin_active_user) * 100, 2),0) as active_pay_percent,
    coalesce(round(sum(pay_money)/sum(firstlogin_active_user), 2),0) as arpu_active,
    coalesce(round(sum(pay_money)/sum(pay_user), 2),0) as arppu_active,
    coalesce(sum(firstlogin_active_user) - sum(firstlogin_user),0) as old_user,
    coalesce(sum(pay_user) - sum(firstlogin_pay_user_new),0) as old_user_pay,
    ROUND(coalesce(sum(pay_money) - sum(firstlogin_pay_money_new),0), 2) as old_user_pay_money,
    coalesce( round((sum(pay_user) - sum(firstlogin_pay_user_new)) / (sum(firstlogin_active_user) - sum(firstlogin_user)) * 100, 2), 0 ) as old_user_pay_percent,
    coalesce( round((sum(pay_money) - sum(firstlogin_pay_money_new)) / (sum(firstlogin_active_user) - sum(firstlogin_user)),2), 0 ) as old_user_arpu,
    coalesce( round( (sum(pay_money) - sum(firstlogin_pay_money_new)) / (sum(pay_user) - sum(firstlogin_pay_user_new)), 2 ), 0 ) as old_user_arppu,
    coalesce( round( (sum(firstlogin_active_user) - sum(firstlogin_user)) / sum(firstlogin_active_user) * 100, 2 ), 0 ) as old_user_percent,
    coalesce( round(sum(firstlogin_active_user_pay) / sum(firstlogin_active_user) * 100,2), 0) as active_pay_user_percent,
    coalesce( round((sum(create_role) - sum(firstlogin_role2)) / sum(create_role) * 100, 2 ), 0) as server_roll_percent,
    IFNULL(sum(pay_user_per), 0) AS pay_user_per,
    coalesce(round(sum(pay_user_per)/ sum(firstlogin_user) * 100, 2), 0) as pay_permeation_percent
from dash_detail left join paid_per_info on dash_detail.tday = paid_per_info.tday and dash_detail.package_id = paid_per_info.package_id
<?php if (!empty($_smarty_tpl->getValue('groups'))) {?>
    group by
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('groups'), 'item', true);
$_smarty_tpl->getVariable('item')->iteration = 0;
$foreach7DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('item')->value) {
$foreach7DoElse = false;
$_smarty_tpl->getVariable('item')->iteration++;
$_smarty_tpl->getVariable('item')->last = $_smarty_tpl->getVariable('item')->iteration === $_smarty_tpl->getVariable('item')->total;
$foreach7Backup = clone $_smarty_tpl->getVariable('item');
?>
        <?php if ($_smarty_tpl->getValue('item') == 'tday') {?> dash_detail.tday<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'cp_game_id') {?> cp_game_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'game_id') {?> game_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'app_show_id') {?> app_show_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'channel_main_id') {?> channel_main_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'channel_id') {?> channel_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_id') {?> promotion_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'user_id') {?> user_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'department_id') {?> department_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'package_id') {?> dash_detail.package_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'platform_id') {?> platform_id<?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
        <?php }?>
    <?php
$_smarty_tpl->setVariable('item', $foreach7Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}
if (!empty($_smarty_tpl->getValue('sorts'))) {?>
    order by
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('sorts'), 'oo', true, 'ss');
$_smarty_tpl->getVariable('oo')->iteration = 0;
$foreach8DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('ss')->value => $_smarty_tpl->getVariable('oo')->value) {
$foreach8DoElse = false;
$_smarty_tpl->getVariable('oo')->iteration++;
$_smarty_tpl->getVariable('oo')->last = $_smarty_tpl->getVariable('oo')->iteration === $_smarty_tpl->getVariable('oo')->total;
$foreach8Backup = clone $_smarty_tpl->getVariable('oo');
?>
        <?php echo $_smarty_tpl->getValue('oo');?>

        <?php if (!$_smarty_tpl->getVariable('oo')->last) {?>, <?php }?>
    <?php
$_smarty_tpl->setVariable('oo', $foreach8Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}
if (!empty($_smarty_tpl->getValue('paginate'))) {?>
    <?php $_smarty_tpl->assign('page_size', $_smarty_tpl->getValue('paginate')['page_size'], false, NULL);?>
    <?php $_smarty_tpl->assign('page', $_smarty_tpl->getValue('paginate')['page'], false, NULL);?>
    limit <?php echo $_smarty_tpl->getValue('page_size');?>
 offset <?php echo ($_smarty_tpl->getValue('page')-1)*$_smarty_tpl->getValue('page_size');?>

<?php }?>




<?php }
}

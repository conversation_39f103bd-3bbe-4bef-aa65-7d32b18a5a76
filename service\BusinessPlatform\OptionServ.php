<?php

namespace app\service\BusinessPlatform;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use Spiral\Database\Injection\Parameter;

class OptionServ
{
    /**
     * 获取万精油特殊的账号
     *
     * @param array $accounts
     *
     * @return array
     */
    public function getSpecialAccount(array $accounts): array
    {
        $db   = FakeDB::connection('pp_platform');
        $data = $db
            ->select()
            ->from('pp_platform.login_user_map')
            ->where([
                'status'        => 1,
                'login_account' => new Parameter($accounts),
            ])
            ->groupBy('core_user')
            ->columns(['core_user as core_user'])
            ->fetchAll();

        return array_column($data, 'core_user');
    }

    /**
     * @param array $params
     * @param array $columns
     *
     * @return array
     */
    public function getAccountInfo(array $params, array $columns = []): array
    {
        return FakeDB::connection('pp_platform')
            ->select()
            ->from('pp_platform.login_user_map')
            ->where($params)
            ->columns($columns)
            ->fetchAll();
    }


    /**
     * @return array
     */
    public function getPayWay(): array
    {
        $db = FakeDB::connection('py_platform');
        return $db->select()->from('payway')->columns(['code as code', 'name as name'])->fetchAll();
    }

    /**
     * @return array
     */
    public function getTaggingOperator(): array
    {
        $db = FakeDB::connection('dataspy');

        $subQb = (clone $db)
            ->select()
            ->from('ddc_platform.dwd_sdk_payment_tagging')
            ->columns(['operator_id'])
            ->distinct();

        return $db
            ->select()
            ->from('dataspy.admin_user')
            ->where('id', 'IN', $subQb)
            ->columns(['id', 'real_name'])
            ->fetchAll();
    }


    public function getTaggingDepartment(): array
    {
        $db = FakeDB::connection('base_conf_platform');

        return $db->select()->from(TableCollect::OA_DEPARTMENT_CONF)->fetchAll();
    }

    public function getOaDepartment(): array
    {
        $db = FakeDB::connection('base_conf_platform');

        return $db
            ->select()
            ->from(TableCollect::OA_DEPARTMENT_CONF)
            ->where('level', '<=', 2)
            ->where('useif', 0)
            ->columns(['id', 'name', 'parent_id', 'level'])
            ->fetchAll();
    }

    /**
     * @return array
     */
    public function getAllDepartment(): array
    {
        $db = FakeDB::connection('base_conf_platform');
        return $db
            ->select()
            ->from('base_conf_platform.oa_base_department_conf')
            ->columns(['id', 'name'])
            ->fetchAll();
    }

    /**
     * @return array
     */
    public function getAllUser(): array
    {
        $db = FakeDB::connection('dataspy');
        return $db
            ->select()
            ->from('dataspy.admin_user')
            ->columns(['id', 'real_name'])
            ->fetchAll();
    }

    /**
     * @param array $wheres
     *
     * @return array
     */
    public function getMediaPlatform(array $wheres = []): array
    {
        $db = FakeDB::connection('base_conf_platform');
        $qb = $db
            ->select()
            ->from('base_conf_platform.tb_ad_live_platform')
            ->columns(['id', 'name']);

        if (!empty($wheres)) {
            $qb->where($wheres);
        }

        return $qb->fetchAll();
    }

    /**
     * @param array $wheres
     *
     * @return array
     */
    public function getDemoVideoId(array $wheres = [], string $groupBy = ''): array
    {
        $db = FakeDB::connection('ddc_platform');
        $qb = $db
            ->select()
            ->from('ddc_platform.media_demo_video')
            ->columns(['demo_video_id', 'demo_video_title']);

        if (!empty($wheres)) {
            $qb->where($wheres);
        }

        if ($groupBy) {
            $qb->groupBy($groupBy);
        }

        return $qb->fetchAll();
    }

    /**
     * @param array $wheres
     *
     * @return array
     */
    public function getCustomFieldConf(array $wheres = []): array
    {
        $db = FakeDB::connection('base_conf_platform');
        $qb = $db
            ->select()
            ->from('base_conf_platform.tb_base_custom_field_conf')
            ->columns(['field_id', 'field_desc']);

        if (!empty($wheres)) {
            $qb->where($wheres);
        }

        return $qb->fetchAll();
    }
}
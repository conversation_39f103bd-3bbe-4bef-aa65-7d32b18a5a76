<?php

namespace app\service\General;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\models\baseConfPlatform\TbBaseGameConf;
use app\models\DataSpy\AdminUser;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\General\Helpers\BaseConfPlatformTable;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

class GeneralOptionServ
{
    /**
     *
     */
    private const TB_BIZ_COMMON_KV_CONFIG = 'base_conf_platform.biz_common_kv_config';

    private const TB_BIZ_TAGS = 'base_conf_platform.biz_tags';

    /**
     * 渠道表
     */
    private const TB_CHANNEL_BASE = 'base_conf_platform.tb_base_channel_conf';


    private const TB_LIVE_ACCOUNT = 'base_conf_platform.tb_ad_live_account_conf';

    private const TB_PACKAGE_DETAIL = 'base_conf_platform.tb_package_detail_conf';

    private const CACHE_PREFIX = 'spy:base_conf_platform:';


    /**
     * @param array $params
     *
     * @return array
     */
    public function listTags(array $params): array
    {
        if (empty($params['option_type'])) return [];

        $db = $this->getConn();
        $qb = $db->select()->from(static::TB_BIZ_COMMON_KV_CONFIG);

        $subQb      = $db->select()->from(static::TB_BIZ_TAGS)->columns([new Fragment('distinct TAG_ID')]);
        $optionType = $params['option_type'];

        if ($optionType == 'channel_id') {
            $subQb->where('table_name', 'app_channel');
        }
        elseif ($optionType == 'package_id') {
            $subQb->where('table_name', 'packages');
        }
        elseif ($optionType == 'channel_main_id') {
            $subQb->where('table_name', 'package_channel_main');
        }
        elseif ($optionType == 'game_id') {
            $subQb->where('table_name', 'games');
        }

        $qb
            ->where('ID', 'IN', $subQb)
            ->where('STATE', 1)
            ->where('IS_REMOVE', 0);

        $qb->columns(['id', 'code', 'name', 'type', 'state', 'ord']);

        return $qb->fetchAll();
    }

    /**
     * @param array $params
     *
     * @return array
     */
    public function listTagsName(array $params): array
    {
        if (empty($params['option_type'])) return [];

        $db = $this->getConn();
        $qb = $db
            ->select()
            ->from(static::TB_BIZ_TAGS . ' as t1')
            ->leftJoin(static::TB_BIZ_COMMON_KV_CONFIG, 't2')
            ->on(['t1.tag_id' => 't2.id'])
            ->leftJoin(static::TB_BIZ_COMMON_KV_CONFIG, 't3')
            ->on(['t2.type' => 't3.code']);

        $optionType = $params['option_type'];

        if ($optionType == 'channel_id') {
            $qb->where('table_name', 'app_channel');
        }
        elseif ($optionType == 'package_id') {
            $qb->where('table_name', 'packages');
        }
        elseif ($optionType == 'channel_main_id') {
            $qb->where('table_name', 'package_channel_main');
        }
        elseif ($optionType == 'game_id') {
            $qb->where('table_name', 'games');
        }

        $ids = $params['id'];

        if (empty($ids)) return [];

        $qb
            ->where('t1.data_id', new Parameter($ids))
            ->where('t2.state', 1)
            ->where('t2.is_remove', 0)
            ->where('t3.use_platform', 1)
            ->columns([
                't1.data_id as data_id',
                't2.name as name',
                't3.name as parent_name',
                't3.id as parent_id',
            ]);

        return $qb->fetchAll();
    }


    /**
     * @return Collection
     */
    public function listChannelOptions(): Collection
    {
        $db = $this->getConn();
        $re = $db
            ->select()
            ->from(static::TB_CHANNEL_BASE)
            ->columns(['channel_id', 'channel_name'])
            ->fetchAll();

        $collect = [];

        foreach ($re as $item) {
            $collect[] = ['val' => $item['channel_name'], 'key' => $item['channel_id']];
        }

        return collect($collect);
    }

    /**
     * 直播账号配置
     *
     * @return array
     */
    public function listLiveAccountOptions(): array
    {
        $db = $this->getConn();

        $result = $db
            ->select()
            ->from(static::TB_LIVE_ACCOUNT)
            ->columns(['account', 'account_id'])
            ->fetchAll();

        $collect = [];

        foreach ($result as $item) {
            $collect[] = ['val' => $item['account'], 'key' => $item['account_id']];
        }

        return $collect;
    }

    /**
     * @return array
     */
    public function listLiveCostType(): array
    {
        $db     = $this->getConn();
        $result = $db
            ->select()
            ->from(TableCollect::BASE_COST_TYPE)
            ->columns(['id', 'type_name']);

        $collect = [];

        foreach ($result as $item) {
            $collect[] = ['val' => $item['type_name'], 'key' => $item['id']];
        }

        return $collect;
    }

    /**
     * @param $package
     *
     * @return int
     */
    public function checkPackages($package): int
    {
        $db = $this->getConn();

        return $db->select()->from(static::TB_PACKAGE_DETAIL)->where('package_id', $package)->count();
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('base_conf_platform');
    }

    /**
     * 根据ID获取用户列表
     *
     * @param array $ids
     *
     * @return array
     */
    public function getUserListByIds(array $ids): array
    {
        return AdminUser::getInstance()->asArray()->findAll(['id' => $ids], ['id', 'real_name']);
    }

    /**
     * @return array
     */
    public function getAllUserList(): array
    {
        return AdminUser::getInstance()->asArray()->findAll([], ['id', 'real_name']);
    }

    /**
     * @param array $options
     *
     * @return array
     */
    public function getChannelInfo(array $options = []): array
    {
        $db = $this->getConn();

        $qb = $db->select()->from(TableCollect::BASE_CHANNEL_CONF);

        if (!empty($options['tags'])) {
            $subQb = $db->select()->from(self::TB_BIZ_TAGS);
            $tags  = Arr::wrap($options['tags']);
            $subQb
                ->where('tag_id', new Parameter($tags))
                ->where('table_name', 'app_channel')
                ->columns(['data_id'])
                ->distinct();

            $qb->where('channel_id', 'IN', $subQb);
        }

        $qb->columns([
            'channel_id as channel_id',
            'channel_code as channel_code',
            'channel_name as channel_name',
            'channel_main_id as channel_main_id',
            'channel_main_name as channel_main_name',
            'channel_label as channel_label',
        ]);

        return $qb->fetchAll();
    }

    /**
     * @param $packageId
     *
     * @return array
     */
    public function getPackageInfo($packageId): array
    {
        $db = $this->getConn();

        $qb = $db->select()->from(BaseConfPlatformTable::TbPackageDetailConf);

        $qb->where('package_id', $packageId);

        return $qb->fetchAll()[0] ?? [];
    }


    public function listChannelInfo(array $wheres = [])
    {
        $db = $this->getConn();

        $qb = $db->table(BaseConfPlatformTable::TbBaseChannelConf)->select();
        $qb->columns([
            'channel_id as channel_id',
            'channel_code as channel_code',
            'channel_name as channel_name',
            'channel_main_id as channel_main_id',
            'channel_main_name as channel_main_name',
            'channel_label as channel_label',
            'chan_id as chan_id',
            'bill_id as bill_id',
            'platform_id as platform_id',
            'popularize_id as popularize_id',
            'promotion_way_id as promotion_way_id',
            'back_param_android as back_param_android',
            'back_param_ios as back_param_ios',
            'upload_method as upload_method',
            'parent_id as parent_id',
            'is_use_sv_key as is_use_sv_key',
            'is_show as is_show',
        ]);

        if (!empty($wheres)) {
            $qb->where($wheres);
        }

        return $qb->fetchAll();
    }

    /**
     * @param array $packages
     * @return array
     */
    public function listPackageInfo(array $packages): array
    {
        $db = $this->getConn();
        $qb = $db->select()->from(BaseConfPlatformTable::TbPackageDetailConf);
        $qb->where('package_id', 'IN', new Parameter($packages));
        $list = array_map(fn($item) => array_change_key_case($item), $qb->fetchAll());

        return array_column($list, 'package_id');
    }

}
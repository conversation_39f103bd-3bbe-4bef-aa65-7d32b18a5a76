<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\Traits\InternalRoutes;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\CollectHelper;
use app\service\AdvertiseLive\OperationalAccountServ;
use Plus\MVC\Controller\JsonController;

/**
 * 直播运营账号管理
 *
 * @route /advertise/live-operational-account/*
 * @date  2023/07/16
 */
class LiveOperationalAccountController extends JsonController
{
    use InternalRoutes;

    /**
     * @return array
     */
    public function listAction(): array
    {
        $params = $this->wrapParams(\Plus::$app->request);
        $serv   = new OperationalAccountServ();
        $list   = $serv->getList(['state' => 1], [], [], ['sorted' => 'DESC']);
        $list   = Arr::crossMap($list, ['id' => 'key', 'account_name' => 'val']);

        return $this->success([
            'list' => $list,
        ]);
    }

    /**
     * @return array
     * @throws \Throwable
     */
    public function updateSortedAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);
        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $orderList  = $params['list'];
        $serv       = new OperationalAccountServ();
        $updateList = [];
        $count      = \count($orderList);

        foreach ($orderList as $id) {
            $t            = [
                'id'     => $id,
                'sorted' => $count--,
            ];
            $updateList[] = $t;
        }

        try {
            $serv->updateMultiById($updateList);
        }
        catch (\Throwable $e) {
            return $this->error('更新失败');
        }

        return $this->success([]);
    }

    /**
     * 添加运营账号
     *
     * @return array
     */
    public function saveAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        if (empty(trim($params['account_name']))) {
            return $this->error('缺失必要参数');
        }

        $serv = new OperationalAccountServ();
        try {
            $params['account_name'] = trim($params['account_name']);
            $params['operator_id']  = \Plus::$service->admin->getUserId();
            $serv->insert($params);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

    /**
     * 更新接口
     *
     * @return array
     */
    public function updateAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);
        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $serv = new OperationalAccountServ();

        try {
            $data                = $params;
            $data['operator_id'] = \Plus::$service->admin->getUserId();
            $serv->updateMultiById([$data]);
        }
        catch (\Throwable $e) {
            return $this->error('更新失败', ['trace' => $e->getMessage()]);
        }

        return $this->success([]);
    }

    /**
     * @return array
     */
    public function delAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $id = $params['id'] ?? null;

        if (empty($id)) {
            return $this->error('参数缺失');
        }

        $serv = new OperationalAccountServ();

        try {
            $serv->removeByIds(Arr::wrap($id));
        }
        catch (\Throwable $e) {
            return $this->error('删除失败');
        }

        return $this->success([]);
    }
}
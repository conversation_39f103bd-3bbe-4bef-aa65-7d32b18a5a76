<?php

namespace app\service\AdvertiserData\Components\MatchParams\Traits;


use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\Convert;
use app\service\General\BizTagsServ;

trait GeneralMatch
{

    /**
     * 统计日期
     *
     * @param $params
     *
     * @return string
     */
    protected function matchTDay($params): string
    {

        if (
            empty($params['range_date_start'])
            && empty($params['range_date_end'])
        ) {
            return '';
        }

        $rangeDate = array_filter([
            $params['range_date_start'] ?? null,
            $params['range_date_end'] ?? null,
        ]);

        $field = $this->getField('tday');
        sort($rangeDate);

        $rangeDate = array_unique($rangeDate);

        if (count($rangeDate) == 1) {
            return "{$field} = '{$rangeDate[0]}'";
        }
        else {
            return "{$field} between '{$rangeDate[0]}' and '{$rangeDate[1]}'";
        }
    }

    /**
     * 游戏原名
     *
     * @param $params
     *
     * @return string
     */
    protected function matchCpGames($params): string
    {
        if (!array_key_exists('cp_game_id', $params)) return '';

        $field = $this->getField('cp_game_id');

        if (is_null($params['cp_game_id'])) {
            return "{$field} = 0";
        }

        $data = Convert::convertInString(Arr::get($params, 'cp_game_id'));

        return "{$field} IN ({$data})";
    }

    /**
     * 游戏统计名
     *
     * @param $params
     *
     * @return string
     */
    protected function matchGames($params): string
    {
        $data = Convert::convertInString(Arr::get($params, 'game_id'));
        if (empty($data)) return '';

        $field = $this->getField('game_id');

        return "{$field} IN ({$data})";
    }

    /**
     * 小时
     *
     * @param $params
     *
     * @return string
     */
    protected function matchThour($params): string
    {
        $data = Convert::convertInString(Arr::get($params, 'thour'));
        if (empty($data)) return '';
        $field = $this->getField('thour');

        return "{$field} IN ({$data})";
    }


    /**
     * 游戏前端名
     *
     * @param $params
     *
     * @return string
     */
    protected function matchAppShowIds($params): string
    {
        $data = Convert::convertInString(Arr::get($params, 'app_show_id'));

        if (empty($data)) return '';

        $field = $this->getField('app_show_id');

        return "{$field} IN ({$data})";
    }

    /**
     * 主渠道
     *
     * @param $params
     *
     * @return string
     */
    protected function matchChannelMainId($params): string
    {
        $data = Convert::convertInString(Arr::get($params, 'channel_main_id'));

        if (empty($data)) return '';

        $field = $this->getField('channel_main_id');

        return "{$field} IN ({$data})";
    }

    /**
     * 渠道
     *
     * @param $params
     *
     * @return string
     * @throws \RedisException
     */
    protected function matchChannelId($params): string
    {
        $channels = array_unique(array_merge(
            Convert::splitArray(Arr::get($params, 'channel_id')),
            Convert::splitArray(Arr::get($params, 'promotion_channel_id'))
        ));

        if (empty($channels)) return '';
        $channels = Convert::convertInString($channels);
//        $fields   = [];
//
//        if (isset($params['channel_id'])) {
//            $fields[] = $this->getField('channel_id');
//        }
//
//        if (isset($params['promotion_channel_id'])) {
//            $fields[] = $this->getField('promotion_channel_id');
//        }
//
//        $fields = array_unique($fields);
//        $wheres = [];
//
//        foreach ($fields as $field) {
//            if (strtolower($field) == 'power.channel_id') continue;
//
//            $wheres[] = "{$field} IN ({$channels})";
//        }
//
//        $wheres[]    = "POWER.channel_id IN ({$channels})";
//        $whereString = implode(' or ', $wheres);

//        $whereString = "COALESCE(IF(`power`.`channel_id` NOT IN (6568, 6822, 5329), `power`.`channel_id`, IF(`t_base`.`channel_id` != 0,`t_base`.`channel_id`,`power`.`channel_id`)), 0)  IN ({$channels})";

        $planChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannels = implode(',' , $planChannels);

        $whereString = "IF(`POWER`.`channel_id` NOT IN ({$planChannels}), `POWER`.`channel_id`, IF(`t_base`.`channel_id` != 0,IF(t_base.channel_id=1013,4,t_base.channel_id),`POWER`.`channel_id`)) IN ({$channels})";

        return "({$whereString})";
    }

    /**
     * 客户端
     *
     * @param $params
     *
     * @return string
     */
    protected function matchPlatformId($params): string
    {
        if (!array_key_exists('platform_id', $params)) return '';

        $field = $this->getField('platform_id');

        if (is_null($params['platform_id'])) {
            return "{$field} = 0";
        }

        $data = Convert::convertInString(Arr::get($params, 'platform_id'));

        return "{$field} IN ({$data})";
    }

    /**
     * 包号
     *
     * @param $params
     *
     * @return string
     */
    protected function matchPackageId($params): string
    {
        $data = Convert::convertInString(Arr::get($params, 'package_id'));

        if (empty($data)) return '';

        $field = $this->getField('package_id');

        return "{$field} IN ({$data})";
    }

    /**
     * 推广分类
     *
     * @return void
     */
    protected function matchPromotionId($params): string
    {
        $data = Convert::convertInString(Arr::get($params, 'promotion_id'));
        if (empty($data)) return '';

        $field = $this->getField('promotion_id');

        return "{$field} IN ({$data})";
    }

    /**
     * 广告账号ID
     *
     * @param $params
     *
     * @return string
     */
    protected function matchAdAccountId($params): string
    {
        if (empty($params['ad_account_id'])) return '';

        $field = $this->getField('ad_account_id');
        $data  = $params['ad_account_id'];

        return "{$field} = '{$data}'";
    }

    /**
     * 推广渠道搜索
     *
     * @param $params
     *
     * @return string
     */
    protected function matchPromotionChannel($params): string
    {
        $data = Convert::convertInString(Arr::get($params, 'promotion_channel_id'));

        if (empty($data)) return '';

        $field = $this->getField('promotion_channel_id');

        return "{$field} IN ({$data})";
    }

    /**
     * @param $params
     *
     * @return string
     */
    protected function matchAdAccount($params): string
    {
        if (empty($params['ad_account'])) return '';

        $field = $this->getField('ad_account');
        $data  = $params['ad_account'];

        return "{$field} like '%{$data}%'";
    }

    /**
     * 搜索账号ID
     *
     * @param $params
     *
     * @return string
     */
    protected function matchAccountId($params): string
    {
        if (empty($params['account_id'])) return '';

        $field = $this->getField('account_id');

        if (is_null($params['account_id'])) {
            return "{$field} = 0";
        }
        $data = Convert::convertInString(Arr::get($params, 'account_id'));
        return "{$field} in ({$data})";
    }

    /**
     * 计划名搜索
     *
     * @param $params
     *
     * @return string
     */
    protected function matchDepartmentId($params): string
    {
        if (!array_key_exists('department_id', $params)) return '';

        if (is_null($params['department_id'])) {
            return "(t_admin.DEPARTMENT_ID IS NULL or  `POWER`.`AD_DEPARTMENT_ID` = 0)";
        }

        $data = Convert::convertInString(Arr::get($params, 'department_id'));
        if (empty($data)) return '';

        $field = $this->getField('department_id');

        return "
       (
            t_admin.department_id IN ({$data})
            or 
            `POWER`.AD_DEPARTMENT_ID IN ({$data})
        )
        ";
    }


    /**
     * 投放人搜索
     *
     * @param $params
     *
     * @return string
     */
    protected function matchUserId($params): string
    {
        if (!array_key_exists('user_id', $params)) return '';

        $field = $this->getField('user_id');
        if (is_null($params['user_id'])) {
            return "(`t_base`.`user_id` IS NULL or `POWER`.`AD_USER_ID` = 0)";
        }

        $data = Convert::convertInString(Arr::get($params, 'user_id'));

        return "(t_base.user_id IN ({$data}) or power.ad_user_id IN ({$data}))";
    }

}
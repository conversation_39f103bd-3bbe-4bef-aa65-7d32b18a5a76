<?php

namespace app\service;

use Plus;
use Generator;

/**
 * 下拉选项配置
 * @package app\service
 * <AUTHOR>
 * @property ?Generator status                                  登录状态
 * @property ?Generator level                                   管理员级别
 * @property ?Generator sex                                     管理员性别
 * @property ?Generator dep                                     部门
 * @property ?Generator adminUser                               管理员
 * @property ?Generator group                                   权限分组
 * @property ?Generator exportState                             文件导出状态
 */
class Options
{
    /**
     * 访问字段名, 返回 Generator, 用于格式化查询结果
     * @param string $name 选项
     * @return Generator|null
     */
    public function __get($name)
    {
        $func = 'get' . ucfirst($name) . 'Arr';
        if (method_exists($this, $func)) {
            return call_user_func(function () use ($func) {
                yield $this->{$func}();
            });
        } else {
            return null;
        }
    }

    /**
     * row转map
     * @param array  $arr 行数据
     * @param string $k   key
     * @param string $v   val
     * @return array
     */
    public static function row2map($arr, $k, $v): array
    {
        $res = [];
        foreach ($arr as [$k => $key, $v => $val]) {
            $res[$key] = $val;
        }
        return $res;
    }

    /**
     * 默认权限
     * @return string[]
     */
    public function getAuthActionsArr(): array
    {
        return [
            'list'   => '查询',
            'create' => '新增',
            'update' => '编辑',
            'delete' => '删除',
            'import' => '导入',
            'export' => '导出',
        ];
    }

    /**
     * 文件导出状态
     * @return array
     */
    public function getExportStateArr(): array
    {
        return [
            1 => '待处理',
            2 => '处理中',
            3 => '处理完成',
        ];
    }

    /**
     * 登录状态
     * @return array
     */
    public function getStatusArr(): array
    {
        return [
            '0' => '允许登录',
            '1' => '禁止登录',
        ];
    }

    /**
     * 管理员级别
     * @return array
     */
    public function getLevelArr(): array
    {
        return [
            3 => '普通',
            6 => '主管',
            7 => '经理',
            8 => '总经理',
            9 => '超级',
        ];
    }

    /**
     * 管理员姓名
     * @return string[]
     */
    public function getSexArr(): array
    {
        return [0 => '女', 1 => '男'];
    }

    /**
     * 部门
     * @param string $str 搜索字符串
     * @return array
     */
    public function getDepArr($str = ''): array
    {
        $db    = Plus::$app->db;
        $where = ['is_remove' => '0'];
        if ($str) {
            $where['name[~]'] = $str;
        }
        $deps = $db->select('department', ['id', 'name'], $where);
        return self::row2map($deps, 'id', 'name');
    }

    /**
     * 管理员
     * @param string $str 搜索字符串
     * @return array
     */
    public function getAdminUserArr($str = ''): array
    {
        $db    = Plus::$app->db;
        $where = [];
        if ($str) {
            $where['real_name[~]'] = $str;
        }
        $deps = $db->select('admin_user', ['id', 'user_name', 'real_name'], $where);
        return self::row2map($deps, 'user_name', 'real_name');
    }

    /**
     * 权限分组
     * @param string $str 搜索字符串
     * @return array
     */
    public function getGroupArr($str = ''): array
    {
        $db    = Plus::$app->db;
        $where = [];
        if ($str) {
            $where['name[~]'] = $str;
        }
        $groups = $db->select('admin_groups', ['id', 'name'], $where);
        return self::row2map($groups, 'id', 'name');
    }
}

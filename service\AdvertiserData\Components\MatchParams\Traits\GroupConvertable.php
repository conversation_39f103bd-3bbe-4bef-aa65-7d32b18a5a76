<?php

namespace app\service\AdvertiserData\Components\MatchParams\Traits;

use app\extension\Support\Collections\Arr;

/**
 * @GroupConvertable 可转换分组字段类型
 */
trait GroupConvertable
{

    /**
     * @inerhitDoc
     * @param $groups
     * @return array
     */
    public function exchangeGroups($groups): array
    {
        if (is_string($groups) && str_contains(',', $groups)) {
            $groups = explode(',', $groups);
        }

        $groups = Arr::wrap($groups);

        foreach ($groups as &$foo) {
            $foo = $this->getField($foo);
        }

        return $groups;
    }
}
<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Traits\InternalRoutes;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\service\ConfigService\TableFieldsHubServ;
use app\service\Tool\BusinessDataDictServ;
use Plus\MVC\Controller\JsonController;

/**
 * @BaseTableController 表单类控制器基础类
 *
 */
abstract class BaseTableController extends JsonController
{
    use InternalRoutes;

    /**
     * 表数据获取
     *
     * @param Collection $params
     *
     * @return array
     */
    abstract protected function data(Collection $params): array;

    /**
     * 表头数据获取
     *
     * @param Collection $params
     *
     * @return array
     */
    abstract protected function fields(Collection $params): array;

    /**
     * 通用数据统一入口
     * @route {host}/xxx/xxx/data
     *
     * @return array
     * @throws \RedisException
     */
    public function dataAction(): array
    {
        $result        = [];
        $params        = $this->wrapParams(\Plus::$app->request);
        $getMethodFunc = Arr::wrap($params->pull('method'));

        foreach ($getMethodFunc as $func) {
            if (!is_callable([$this, $func])) continue;

            $r = $this->{$func}(clone $params);

            if ($func == 'fields') {
                if (!empty($r['fields'])) {
                    $this->appendFieldTips($r['fields']);
                }
            }

            $result = array_merge($result, $r);
        }

        return $this->success($result);
    }

    /**
     * 通用表头获取方法
     *
     * @param mixed|null $options   传递的选项
     * @param mixed|null $className 默认为当前类
     * @param mixed|null $getFunc   获取操作的方法
     *
     * @return array
     */
    protected function tableFields($options = null, $className = null, $getFunc = null): array
    {
        !empty($className)
            ?: $className = $this->getStaticClass();

        !empty($getFunc)
            ?: $getFunc = 'getFields';

        $serv = new TableFieldsHubServ($className);

        return $this->filterField($serv->{$getFunc}($options));
    }

    /**
     * 过滤或格式化表头操作
     *
     * @param $data
     *
     * @return array
     */
    protected function filterField($data): array
    {
        return $data instanceof Collection ? $data->values()->toArray() : (array)$data;
    }

    /**
     * 获取当前类
     *
     * @return string
     */
    protected function getStaticClass(): string
    {
        $classmap = \explode('\\', static::class);

        return (string)str_replace('Controller', '', array_pop($classmap));
    }

    /**
     * @param array $fields
     * @return void
     * @throws \RedisException
     */
    protected function appendFieldTips(array &$fields)
    {
        $tips = (new BusinessDataDictServ())->getAll();

        foreach ($fields as &$foo) {
            $title = $foo['title'];
            if (isset($tips[$title])) {
                $foo['tip'] = $tips[$title];
            }
        }
    }
}
<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiseLive\Components\Matcher\AdLiveMatch;
use app\service\AdvertiserData\Components\Helpers\Convert;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\AdvertiseLive\Helpers\TableConst as LiveTableConst;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class LiveRoomServ
{
    const QB_MODE_ALL     = 99;
    const QB_MODE_DUTY    = 2;
    const QB_MODE_ACCOUNT = 3;

    const MODE_ALL     = 3;
    const MODE_SUMMARY = 2;
    const MODE_LIST    = 1;

    protected array $groupMap = [
        'range_time' => ['live_log.start_time', 'live_log.end_time'],
        'tday'       => 'live_log.tday',
        'account_id' => 'live_log.anchor_account_id',
    ];


    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int   $mode
     *
     * @return array
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $qb        = $this->getIndexBaseQb($params);
        $paramMode = $mode & (static::MODE_ALL | static::MODE_SUMMARY | static::MODE_LIST);
        $result    = [];

        $liveMatcher = new AdLiveMatch([
            'start_time'      => 'live_log.start_time',
            'end_time'        => 'live_log.end_time',
            'live_platform'   => 'duty_info.live_platform',
            'live_team'       => 'duty_info.live_team',
            'docking_partner' => 'duty_info.docking_partner',
            'package_id'      => 'duty_info.package_id',
            'operator_id'     => 'duty_info.operator_id',
            'live_account_id' => 'live_log.anchor_account_id',
        ]);
        $liveMatcher->exec($qb, $params);

        if ($paramMode & static::MODE_LIST) {
            $infoQb = clone $qb;

            $infoQb->columns([
                'live_log.tday as tday',
                'live_log.start_time as start_time',
                'live_log.end_time as end_time',
                'live_log.anchor_account_id as account_id',
                'account_info.account as account_name',
                new Fragment("CONCAT(TIME_FORMAT(live_log.start_time, '%H:%i'), '~', TIME_FORMAT(live_log.end_time, '%H:%i')) as range_time"),
                new Fragment('SUM(live_watch_count) as watch'),
                new Fragment('SUM(live_watch_ucount) as watch_ucount'),
                //                new Fragment('SUM(max_user) as max_user'),
                //                new Fragment('SUM(live_avg_watch_duration) as avg_online'),
                new Fragment('ROUND(SUM(live_watch_count) / SUM(TIMESTAMPDIFF(MINUTE ,live_log.start_time, live_log.end_time)), 2) as speed_push'),
                new Fragment('ROUND(SUM(live_avg_watch_duration * live_watch_count) / SUM(live_watch_count)) as avg_user_duration'),
                new Fragment('SUM(live_follow_count) as fans'),
                new Fragment('SUM(live_fans_count) as fans_club'),
                //                new Fragment("CONCAT(COALESCE(ROUND(SUM(interaction) / SUM(watch) * 100, 2) , 0),'%') as interaction_percent"), // 互动率
                new Fragment("CONCAT(ROUND(SUM(live_follow_count) / SUM(live_watch_count) * 100, 2), '%') as convert_fans"), // 转粉率
                'duty_info.cp_game_id as cp_game_id',
                'duty_info.game_id as game_id',
                'duty_info.package_id as package_id',
                'duty_info.anchor_id as anchor_id',
                'duty_info.anchor_name as anchor_name',
                'duty_info.docking_partner as docking_partner',
                'duty_info.live_platform as live_platform',
                'duty_info.collaborative_code as collaborative_code',
                'duty_info.live_team as live_team',
                'duty_info.assistant_id as assistant_id',
                'duty_info.operation_account_id',
                'duty_info.channel_id',
                'duty_info.channel_main_id',
                'duty_info.user_id',
                'duty_info.department_id',
                'duty_info.live_account_id',
            ]);

            // 固定最细维度以便其他指标配合查询
            $result['mix_dimension_select_qb'] =
                (clone $infoQb)
                    ->groupBy('live_log.start_time')
                    ->groupBy('live_log.end_time')
                    ->groupBy('live_log.anchor_account_id')
                    ->groupBy('duty_info.package_id')
                    ->columns([
                        'live_log.tday as tday',
                        'live_log.start_time as start_time',
                        'live_log.end_time as end_time',
                        new Fragment("CONCAT(TIME_FORMAT(live_log.start_time, '%H:%i'), '~', TIME_FORMAT(live_log.end_time, '%H:%i')) as range_time"),
                        'live_log.anchor_account_id as account_id',
                        'account_info.account as account_name',
                        'duty_info.cp_game_id as cp_game_id',
                        'duty_info.game_id as game_id',
                        'duty_info.package_id as package_id',
                        'duty_info.anchor_id as anchor_id',
                        'duty_info.anchor_name as anchor_name',
                        'duty_info.docking_partner as docking_partner',
                        'duty_info.live_platform as live_platform',
                        'duty_info.collaborative_code as collaborative_code',
                        'duty_info.live_team as live_team',
                        'duty_info.assistant_id as assistant_id',
                        'duty_info.operation_account_id',
                        'duty_info.channel_id',
                        'duty_info.channel_main_id',
                        'duty_info.user_id',
                        'duty_info.department_id',
                        'duty_info.live_account_id',
                    ]);

            if (!empty($groups)) {
                foreach ($this->exchangeGroupMapGen($groups, $this->groupMap) as $g) {
                    $infoQb->groupBy($g);
                }
            }

            $noPageQb = clone $infoQb;
            $noPageQb->columns([new Fragment('1 as row_num')]);

            if (!empty($sort)) {
                $infoQb->orderBy($sort);
            }

            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;
                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }

            $result['list']             = $infoQb->fetchAll();
            $result['not_have_page_qb'] = clone $noPageQb;
            $chillDb                    = $this->getConn();
            $result['total']            = $chillDb
                ->select()
                ->from(new Fragment('(' . $noPageQb->__toString() . ') as totalBody'))
                ->count();
        }

        if ($paramMode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            $summaryQb->columns([
                new Fragment('SUM(live_watch_count) as watch'),
                new Fragment('SUM(live_watch_ucount) as watch_ucount'),
                //                new Fragment('SUM(max_user) as max_user'),
                //                new Fragment('SUM(avg_online) as avg_online'),
                new Fragment('ROUND(SUM(live_watch_count) / SUM(TIMESTAMPDIFF(MINUTE ,live_log.start_time, live_log.end_time)), 2) as speed_push'),
                new Fragment('ROUND(SUM(live_avg_watch_duration * live_watch_count) / SUM(live_watch_count)) as avg_user_duration'),
                new Fragment('SUM(live_follow_count) as fans'),
                new Fragment('SUM(live_fans_count) as fans_club'),
                //                new Fragment("CONCAT(COALESCE(ROUND(SUM(interaction) / SUM(watch) * 100, 2) , 0),'%') as interaction_percent"),
                new Fragment("CONCAT(ROUND(SUM(live_follow_count) / SUM(live_watch_count) * 100, 2), '%') as convert_fans"),
            ]);

            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }


    /**
     * @param     $params
     * @param int $mode
     *
     * @return SelectQuery
     */
    private function getIndexBaseQb($params, int $mode = -1): SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from(TableCollect::DWD_LIVE_LOG . ' as live_log');

        if ($mode & static::QB_MODE_ACCOUNT) {
            $qb
                ->leftJoin(LiveTableConst::CONF_LIVE_ACCOUNT, 'account_info')
                ->on('live_log.anchor_account_id', 'account_info.account_id');
        }

        if ($mode & static::QB_MODE_DUTY) {
            $dutySubQb = $db
                ->select()
                ->from(LiveTableConst::CONF_LIVE_ON_DUTY . ' as base_duty')
                ->innerJoin('base_conf_platform.tb_package_detail_conf', 'power')
                ->on(['base_duty.package_id' => 'power.package_id'])
                ->leftJoin(LiveTableConst::CONF_LIVE_ACCOUNT, 'base_account')
                ->on(['base_duty.live_account_id' => 'base_account.account_id'])
                ->leftJoin(LiveTableConst::CONF_LIVE_ANCHOR, 'base_anchor')
                ->on(['base_duty.anchor_id' => 'base_anchor.id'])
                ->columns([
                    'base_duty.start_time as start_time',
                    'base_duty.end_time as end_time',
                    'base_duty.package_id as package_id',
                    'base_duty.anchor_id as anchor_id',
                    'base_anchor.anchor_name as anchor_name',
                    'base_anchor.docking_partner as docking_partner',
                    'base_account.account_id as account_id',
                    'base_account.type as live_platform',
                    'base_account.collaborative_code as collaborative_code',
                    'base_anchor.team_properties as live_team',
                    'power.cp_game_id as cp_game_id',
                    'power.game_id as game_id',
                    'base_duty.assistant_id as assistant_id',
                    'base_duty.operation_account_id as operation_account_id',
                    'power.channel_id as channel_id',
                    'power.channel_main_id as channel_main_id',
                    'power.ad_user_id as user_id',
                    'power.ad_department_id as department_id',
                    'base_duty.live_account_id as live_account_id',
                ]);

            if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
                $rangeDate = [
                    $params['range_date_start'],
                    $params['range_date_end'],
                ];

                sort($rangeDate);
                [$timeStart, $timeEnd] = $rangeDate;
                $timeStart = $timeStart . ' 00:00:00';
                $timeEnd   = $timeEnd . ' 23:59:59';

                $dutySubQb
                    ->where('base_duty.start_time', 'between', $timeStart, $timeEnd);
            }

            $qb
                ->leftJoin(new Fragment('(' . $dutySubQb->__toString() . ')'), 'duty_info')
                ->onWhere(
                    new Fragment('duty_info.start_time between DATE_SUB(live_log.start_time, INTERVAL 30 MINUTE) AND DATE_ADD(live_log.end_time, INTERVAL 30 MINUTE)')
                )->onWhere(
                    new Fragment('duty_info.end_time between DATE_SUB(live_log.start_time, INTERVAL 30 MINUTE) AND DATE_ADD(live_log.end_time, INTERVAL 30 MINUTE)')
                )->onWhere(
                    new Fragment('duty_info.account_id = live_log.anchor_account_id')
                );
        }

        return $qb;
    }

    /**
     * @param array $groups
     * @param array $groupsMap
     *
     * @return \Generator
     */
    protected function exchangeGroupMapGen(array $groups, array $groupsMap = []): \Generator
    {
        foreach ($groups as $g) {
            if (!isset($groupsMap[$g])) {
                yield $g;
            }
            else {
                $d = $groupsMap[$g];

                if (is_array($d)) {
                    foreach ($d as $chill) {
                        yield $chill;
                    }
                }
                else {
                    yield $d;
                }
            }
        }
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
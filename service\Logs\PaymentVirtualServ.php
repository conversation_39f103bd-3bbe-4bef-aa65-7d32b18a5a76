<?php

namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;
use app\service\General\Helpers\DdcPlatformTable;
use app\service\Logs\Components\Matcher\PaymentVirtualMatch;
use app\util\Common;
use Smarty\Exception;
use Spiral\Database\Database;

class PaymentVirtualServ
{
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     *
     *
     *
     * @param array $list
     * @return mixed
     * @throws \Throwable
     */
    public function batchInsert(array $list)
    {
        $conn = $this->getConn();

        return $conn->transaction(function (Database $db) use ($list) {
            $result      = [];
            $table       = DdcPlatformTable::DwdUserPaymentUploadVirtual;
            $columns     = $db
                ->query("SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'ddc_platform' AND TABLE_NAME = 'dwd_user_payment_upload_virtual'")
                ->fetchAll();
            $columns     = array_diff(array_column($columns, 'COLUMN_NAME'), ['ID', 'EXT', 'CREATE_TIME', 'LOG_REPORT_TIME', 'UPLOAD_STATUS']);
            $columns     = array_map(fn($item) => strtolower($item), $columns);
            $columnIndex = array_flip($columns);

            foreach ($list as $foo) {
                $foo      = array_intersect_key($foo, $columnIndex);
                $result[] = $db->table($table)->insertOne($foo);
            }

            return $result;
        });
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function list(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {

        $db       = $this->getConn();
        $baseTpl  = \Plus::$app->sqlTemplates->createTemplate('sql/logs/payment_virtual/base_list.tpl');
        $countTpl = \Plus::$app->sqlTemplates->createTemplate('sql/logs/payment_virtual/base_count.tpl');
        $baseTpl
            ->assign('params', $params)
            ->assign('sort', $sort)
            ->assign('paginate', $paginate);

        $countTpl->assign('params', $params);

        $infoSQL  = $baseTpl->fetch();
        $countSQL = $countTpl->fetch();

        @Common::dumpSql($infoSQL);
        $list     = $db->query($infoSQL)->fetchAll();
        $countRow = $db->query($countSQL)->fetch()['total_rows'] ?? 0;

        return [
            'list'  => $list,
            'total' => $countRow,
        ];
    }


}
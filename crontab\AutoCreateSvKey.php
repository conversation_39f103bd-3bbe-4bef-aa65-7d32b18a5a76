<?php declare(ticks=1);

namespace app\crontab;

use app\models\baseConfPlatform\BizTags;
use app\models\baseConfPlatform\TbAdAccountExtConf;
use app\models\baseConfPlatform\TbPackageDetailConf;
use Plus\CLI\DaemonProcess;


/**
 * 根据消费创建短链
 * @package app/daemon
 */
class AutoCreateSvKey extends DaemonProcess
{

    /**
     * 创短链渠道
     * @var array
     */
    private $planChannel     = "1,6,3,49,4,2,32,6695";
    private $campaignChannel = "";


    public function __construct()
    {
        $this->xcxChannelId = (new BizTags())->getXcxChannelId();
    }

    /**
     * @throws \Exception
     */
    public function run()
    {
        $args      = func_get_args();
        $params    = $args[0]["p"] ?? "";
        $beginDate = $params[0] ?? date("Y-m-d", strtotime("-1 days"));
        $endDate   = $params[1] ?? date("Y-m-d");
        $startTime = strtotime($beginDate);
        $endTime   = strtotime($endDate);

        while ($startTime <= $endTime) {
            $beginDate = date("Y-m-d", $startTime);
            var_dump($beginDate);
            $endDate = date("Y-m-d", $startTime);
            //创建短链
            $this->createSvLink($beginDate, $endDate);
            //更新短链名称和投放人
            $this->updateSvLink($beginDate, $endDate);
            $startTime += 86400;
        }

    }

    /**
     * 创建短链
     * @param $beginDate
     * @param $endDate
     * @param $type
     * @return void
     */
    public function createSvLink($beginDate, $endDate, $type = "PLAN")
    {
        switch ($type) {
            case "PLAN":
                $sql  = "SELECT a.CHANNEL_ID,PLAN_ID,PLAN_NAME,PACKAGE_ID,AD_ACCOUNT_ID,b.AD_ACCOUNT,CP_GAME_ID,GAME_ID FROM tb_ad_creative_cost  a left join base_conf_platform.tb_ad_account_conf b  on a.AD_ACCOUNT_ID=b.ACCOUNT_ID WHERE TIME BETWEEN '{$beginDate}' and '{$endDate}'  and a.CHANNEL_ID IN($this->planChannel)  and SV_KEY=0 and COST>0  group by PLAN_ID";
                $data = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                break;
            case "CAMPAIGN":
                $sql  = "SELECT a.CHANNEL_ID,CAMPAIGN_ID PLAN_ID,CAMPAIGN_NAME PLAN_NAME,PACKAGE_ID,AD_ACCOUNT_ID,b.AD_ACCOUNT,CP_GAME_ID,GAME_ID FROM tb_ad_creative_cost  a left join base_conf_platform.tb_ad_account_conf b  on a.AD_ACCOUNT_ID=b.ACCOUNT_ID WHERE TIME BETWEEN '{$beginDate}' and '{$endDate}' and a.CHANNEL_ID IN($this->campaignChannel) and SV_KEY=0 and COST>0   group by CAMPAIGN_ID";
                $data = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                break;
            default:
                exit();
        }
        $insertDataArr = [];
        if (!$data) return;
        $planId    = array_filter(array_column($data, "PLAN_ID"));
        $planIdStr = "'" . implode("','", $planId) . "'";

        //头条抖音直播投流
        $sql               = "
                SELECT
                    PLAN_ID
                from
                    adp_platform.tb_adp_plan_base tapb
                left join
                    adp_platform.tb_adp_campaign tac on
                    tapb.CHANNEL_ID = tac.CHANNEL_ID
                    and tapb.CAMPAIGN_ID = tac.CAMPAIGN_ID
                where
                    tapb.CHANNEL_ID = 2
                    and  tapb.PLAN_ID in($planIdStr)
                    and tac.EXT_DATA -> '$.marketing_goal' = 'LIVE'
        ";
        $douyinZhibo       = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $douyinZhiboPlanId = array_column($douyinZhibo, "PLAN_ID");

        //快手直播投流
        $sql                 = "
                SELECT
                    PLAN_ID
                from
                    adp_platform.tb_adp_plan tapb
                left join
                    adp_platform.tb_adp_campaign tac on
                    tapb.CHANNEL_ID = tac.CHANNEL_ID
                    and tapb.CAMPAIGN_ID = tac.CAMPAIGN_ID
                where
                    tapb.CHANNEL_ID = 6
                    and  tapb.PLAN_ID in($planIdStr)
                    and tac.EXT_DATA ->  '$.campaign_type' = 16
        ";
        $kuaishouZhibo       = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $kuaishouZhiboPlanId = array_column($kuaishouZhibo, "PLAN_ID");

        $packageArr          = array_unique(array_column($data, "PACKAGE_ID"));
        $packageArr          = (new TbPackageDetailConf())->asArray()->findAll(["PACKAGE_ID" => $packageArr]);
        $packageMapChannelId = array_column($packageArr, "CHANNEL_ID", "PACKAGE_ID");
        foreach ($data as $item) {
            if ($item["CHANNEL_ID"] == 4) {
                $item["CHANNEL_ID"] = 1013;
            }
            $channelId = $item["CHANNEL_ID"];
            if ($this->isXcx($packageMapChannelId[$item["PACKAGE_ID"]])) {
                switch ($item["CHANNEL_ID"]) {
                    case 2:
                        //头条微信小程序
                        $channelId = 1107;
                        //抖音小程序
                        if ($packageMapChannelId[$item["PACKAGE_ID"]] == 6447) {
                            $channelId = 6447;
                        }
                        break;
                    case 1013:
                        $channelId = 1105;
                        break;
                    case 4:
                        $channelId = 1105;
                        break;
                    case 3:
                        $channelId = 1113;
                        break;
                    default:
                        $channelId = $item["CHANNEL_ID"];
                }
            }

            //抖音直播投流
            if (in_array($item["PLAN_ID"], $douyinZhiboPlanId)) {
                $channelId = 8329;
            }

            //快手直播投流
            if (in_array($item["PLAN_ID"], $kuaishouZhiboPlanId)) {
                $channelId = 8249;
            }

            //直播渠道
            if (in_array($packageMapChannelId[$item["PACKAGE_ID"]], [8329, 8249])) {
                $channelId = $packageMapChannelId[$item["PACKAGE_ID"]];
            }

            //查询
            $sql    = "SELECT ID from tb_ad_svlink_conf where PACKAGE_ID={$item["PACKAGE_ID"]} and PLAN_ID='{$item["PLAN_ID"]}'";
            $result = \Plus::$app->dataspy->query($sql)->fetch(\PDO::FETCH_ASSOC);

            if ($result) {
                continue;
            }

            //查询投放人
            $info   = (new TbAdAccountExtConf())->getUserIdByAdvertiserId($item["AD_ACCOUNT_ID"]);
            $userId = $info["ADV_USER_ID"] ? $info["ADV_USER_ID"] : 0;

            $insertData      = [
                "CHANNEL_ID" => $channelId,
                "CP_GAME_ID" => $item["CP_GAME_ID"],
                "GAME_ID"    => $item["GAME_ID"],
                "PACKAGE_ID" => $item["PACKAGE_ID"],
                "PLAN_ID"    => $item["PLAN_ID"],
                "AID"        => $item["PLAN_NAME"],
                "AD_ACCOUNT" => $item["AD_ACCOUNT"] ? $item["AD_ACCOUNT"] : $item["AD_ACCOUNT_ID"],
                "EXT"        => "php自动创建",
                "USER_ID"    => $userId,
                "ADD_TIME"   => date("Y-m-d H:i:s"),
            ];
            $insertDataArr[] = $insertData;
        }

        if ($insertDataArr) {
            try {
                \Plus::$app->dataspy->insert("tb_ad_svlink_conf", $insertDataArr);
            }
            catch (\Exception $e) {
                echo $e->getTraceAsString() . PHP_EOL;
            }
        }

    }

    /**
     * 更改短链名称、匹配投放人
     * @param $beginDateAll
     * @param $endDateAll
     * @throws \Plus\SQL\FreeSQLException
     */
    public function updateSvLink($beginDate, $endDate, $type = "PLAN")
    {
        //直播投流渠道
        $planChannel = $this->planChannel . ",8329,8249,1113,1107,1105";
        switch ($type) {
            case "PLAN":
                $sql  = "select PLAN_NAME,a.CHANNEL_ID,PLAN_ID,AD_ACCOUNT_ID,b.AD_ACCOUNT,AD_ACCOUNT_NAME,SV_KEY from tb_ad_creative_cost  a left join base_conf_platform.tb_ad_account_conf b  on a.AD_ACCOUNT_ID=b.ACCOUNT_ID where PLAN_ID in(select PLAN_ID from dataspy.tb_ad_svlink_conf a where a.CHANNEL_ID IN($planChannel) and ((AID REGEXP '[^0-9.]') != 1 OR  USER_ID=0) )  AND TIME BETWEEN '{$beginDate}' and '{$endDate}' GROUP BY PLAN_ID";
                $data = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                break;
            case "CAMPAIGN":
                $sql  = "select CAMPAIGN_NAME PLAN_NAME,a.CHANNEL_ID,CAMPAIGN_ID PLAN_ID,AD_ACCOUNT_ID,b.AD_ACCOUNT,AD_ACCOUNT_NAME,SV_KEY from tb_ad_creative_cost  a left join base_conf_platform.tb_ad_account_conf b  on a.AD_ACCOUNT_ID=b.ACCOUNT_ID where CAMPAIGN_ID in(select PLAN_ID from dataspy.tb_ad_svlink_conf a where a.CHANNEL_ID IN($planChannel) and ((AID REGEXP '[^0-9.]') != 1 OR  USER_ID=0) ) AND  TIME BETWEEN '{$beginDate}' and '{$endDate}' GROUP BY CAMPAIGN_ID";
                $data = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                break;
            default:
                exit();
        }

        foreach ($data as $item) {

            if (!$item['PLAN_ID']) {
                continue;
            }
            //查询投放人
            $info   = (new TbAdAccountExtConf())->getUserIdByAdvertiserId($item["AD_ACCOUNT_ID"]);
            $userId = isset($info["ADV_USER_ID"]) ? $info["ADV_USER_ID"] : 0;

            $sql       = "select PACKAGE_ID,ID,AID from dataspy.tb_ad_svlink_conf  where PLAN_ID ='{$item['PLAN_ID']}'";
            $svlinkArr = \Plus::$app->dataspy->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

            foreach ($svlinkArr as $value) {
                $planName = $item["PLAN_NAME"];
                //更新短链
                $sql = "UPDATE tb_ad_svlink_conf SET AID='" . $planName . "',AD_ACCOUNT='" . ($item["AD_ACCOUNT"] ? $item["AD_ACCOUNT"] : $item["AD_ACCOUNT_ID"]) . "',USER_ID={$userId} WHERE ID =" . $value["ID"];
                try {
                    \Plus::$app->dataspy->exec($sql);
                }
                catch (\Exception $e) {
                    echo $e->getTraceAsString() . PHP_EOL;
                }
            }


        }
    }

    //是否小程序
    public function isXcx($channelId)
    {
        return in_array($channelId, $this->xcxChannelId) ? TRUE : FALSE;
    }


}



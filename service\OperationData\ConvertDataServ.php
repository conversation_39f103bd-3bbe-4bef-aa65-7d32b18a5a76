<?php

namespace app\service\OperationData;

use app\extension\FakeDB\FakeDB;
use app\util\Common;
use Smarty\Exception;

class ConvertDataServ
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function getList(array $params, array $groups = [], array $paginate = [], array $sort = []): array
    {
        $db         = $this->getConn();
        $baseTpl    = \Plus::$app->sqlTemplates->createTemplate('sql/operator/convert_data/convert_data.tpl');
        $countTpl   = \Plus::$app->sqlTemplates->createTemplate('sql/operator/convert_data/convert_data_total.tpl');
        $summaryTpl = \Plus::$app->sqlTemplates->createTemplate('sql/operator/convert_data/convert_data_summary.tpl');

        $baseTpl
            ->assign('params', $params)
            ->assign('sort', $sort)
            ->assign('paginate', $paginate);

        $countTpl->assign('params', $params);
        $summaryTpl->assign('params', $params);
        $infoSQL    = $baseTpl->fetch();
        $countSQL   = $countTpl->fetch();
        $summarySQL = $summaryTpl->fetch();
        @Common::dumpSql($infoSQL);

        $list       = $db->query($infoSQL)->fetchAll();
        $countRow   = $db->query($countSQL)->fetch()['total_rows'] ?? 0;
        $summaryRow = $db->query($summarySQL)->fetch();

        return [
            'list'    => $list,
            'total'   => $countRow,
            'summary' => $summaryRow,
        ];
    }

    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
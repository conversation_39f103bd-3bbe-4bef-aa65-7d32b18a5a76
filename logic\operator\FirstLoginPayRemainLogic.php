<?php

namespace app\logic\operator;

use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Helpers\ProcessLine;
use app\extension\Support\Helpers\RemainCalculators;
use app\extension\Support\Helpers\Zakia;
use app\logic\advertise\Traits\RemainUtilTrait;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use app\service\OperationData\FirstLoginPayRemainServ;
use app\service\OperationData\FirstLoginServ;

class FirstLoginPayRemainLogic
{
    use ColumnsInteract, RemainUtilTrait;

    protected const GROUP_RELATE = [
        'package_id'    => [
            'game_id', 'platform_id', 'user_id',
            'app_show_id', 'channel_id'
        ],
        'cp_game_id'    => [],
        'game_id'       => ['cp_game_id'],
        'channel_id'    => ['channel_main_id'],
        'user_id'       => ['department_id'],
        'department_id' => [],
        'platform_id'   => [],
        'app_show_id'   => [],
        'tday'          => [],
    ];

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \Exception
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        if (empty($groups)) {
            if ($timeDimension == ConstHub::DIMENSION_HOUR) {
                $groups = [
                    'tday', 'thour', 'package_id',
                    'cp_game_id', 'game_id', 'app_show_id',
                    'channel_main_id', 'channel_id',
                    'promotion_id', 'platform_id', 'department_id', 'user_id'
                ];
            }
            else {
                $groups = [
                    'tday', 'package_id', 'package_id',
                    'cp_game_id', 'game_id', 'app_show_id',
                    'channel_main_id', 'channel_id',
                    'promotion_id', 'platform_id', 'department_id', 'user_id'
                ];
            }
        }

        if (
            !empty($params['range_date_start'])
            && !empty($params['range_date_end'])
        ) {
            if ($timeDimension == ConstHub::DIMENSION_MONTH) {
                $params['range_date_start'] = date('Y-m-01', strtotime($params['range_date_start']));
                $params['range_date_end']   = date('Y-m-t', strtotime($params['range_date_end']));
            }

            $params['tday'] = [
                $params['range_date_start'], $params['range_date_end']
            ];
            sort($params['tday']);
            unset($params['range_date_start'], $params['range_date_end']);
        }

        $readCols = [
            'tday', 'cp_game_id', 'game_id',
            'app_show_id', 'platform_id', 'channel_id',
            'channel_main_id', 'package_id',
            'promotion_id', 'department_id', 'user_id', 'firstlogin_pay_user_new_all', 'link_mark'
        ];

        $baseServ = new FirstLoginServ();
        $mode     = $baseServ::MODE_SUMMARY | $baseServ::MODE_TOTAL | $baseServ::MODE_DETAIL_QB | $baseServ::MODE_DETAIL;
        $baseRe   = $baseServ->getListWithOnlyPaymentForBigData($params, $groups, $paginate, $sort, $readCols, $mode);
        $baseQb   = $baseRe['detail_qb'];

        // 留存查询
        $remainServ = new FirstLoginPayRemainServ();
        $remainRe   = $remainServ->getListWithInfoQb($baseQb, $params, $groups, []);
        unset($baseRe['detail_qb']);

        // 数据处理流程
        $infoList   = &$baseRe['list'];
        $remainList = &$remainRe['list'];
        $infoList   = Zakia::appendKeysWithDimension($infoList, $groups);
        $remainList = Zakia::appendKeysWithDimension($remainList, $groups);

        $configBasic      = new BasicServ();
        $constConfCollect = (new BasicServ())->getMultiOptions([
            'platform_id', 'promotion_id', 'department_id', 'user_id',
            'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
        ])->put('channel_id', (new GeneralOptionServ())->listChannelOptions());

        $resetFn      = $this->resetGroupsCols(
            ColumnManager::groupOperatorRelation($groups), $groups, ConstHub::OPERATOR_FIXED_INFO_COLS
        );
        $summaryGetFn = fn($target, $i) => $target['firstlogin_pay_user_new_all_' . $i] ?? 0;

        if (in_array('tday', $groups)) {
            $getDenominatorFn = fn($target) => $target['firstlogin_pay_user_new_all'] ?? 0;
        }
        else {
            $getDenominatorFn = fn($target, $i) => $target['firstlogin_pay_user_new_all_' . $i] ?? 0;
        }
        $works      = new ProcessLine();
        $minDay     = min($params['tday']);
        $maxDayNode = max(days_apart(date('Y-m-d'), $minDay) - 1, 0);

        $works
            ->addProcess(RemainCalculators::calcNode($remainList, $getDenominatorFn, $params['remain_type'] ?? 0, 'firstlogin_pay_user_new_all'))
            ->addProcess($this->replaceColumnDefine($constConfCollect))
            ->addProcess($this->appendRemainDays($maxDayNode, (in_array('tday', $groups) && $timeDimension <= ConstHub::DIMENSION_DAY)))
            ->addProcess($resetFn);

        $works->run($infoList);

        $summaryRow                = &$baseRe['summary'];
        $summaryRemain             = &$remainRe['summary'];
        $summaryRow['remain_days'] = $maxDayNode;
        $summaryWorks              = new ProcessLine();
        $summaryWorks
            ->addProcess(RemainCalculators::calcNode(['summary' => $summaryRemain[0] ?? []], $summaryGetFn, $params['remain_type'] ?? 0, 'firstlogin_pay_user_new_all'));

        $tmpSummary = ['summary' => &$summaryRow];
        $summaryWorks->run($tmpSummary);

        return [
            'list'    => array_values($baseRe['list']),
            'summary' => $baseRe['summary'],
            'total'   => $baseRe['total'],
            'time'    => $baseRe['summary']['last_update_time'] ?? ''
        ];
    }

    /**
     * 可调节付费金额和累计金额天数的付费留存查询逻辑
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \Exception
     */
    public function payRetainStretchable(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        if (empty($groups)) {
            $groups = ['tday', 'package_id'];
        }

        $groups        = ColumnManager::matchRelationByGroups(static::GROUP_RELATE, $groups);
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        // 格式化数据
        $today          = date('Y-m-d');
        $params['tday'] = [
            $params['range_date_start'] ?? $today,
            $params['range_date_end'] ?? $today
        ];

        $payStart     = $params['range_date_start'] ?? $today;
        $retainStart  = $params['range_date_start'] ?? $today;
        $rangeDateEnd = $params['range_date_end'];
        unset($params['range_date_start'], $params['range_date_end']);

        $paidAmountDays           = $params['paid_amount_days'] ?? 0;
        $params['pay_first_time'] = $payStart . ' 00:00:00';

        if ($paidAmountDays > 0) {
            $params['pay_last_time'] = (new \DateTime($rangeDateEnd))
                ->add(new \DateInterval('P' . $paidAmountDays . 'D'))
                ->format('Y-m-d 23:59:59');
        }
        else {
            $params['pay_last_time'] = date('Y-m-d', strtotime('-1day')) . ' 23:59:59';
        }

        $remainDays = $params['remain_days'] ?? 0;
        if ($remainDays > 0) {
            $retainEnd = (new \DateTime($retainStart))->add(new \DateInterval('P' . $remainDays . 'D'))->format('Y-m-d');
        }
        else {
            $retainEnd = date('Y-m-d', strtotime('-1day'));
        }

        if ($timeDimension == ConstHub::DIMENSION_MONTH) {
            $retainStart = date('Y-m-01', strtotime($retainStart));
            $retainEnd   = date('Y-m-t', strtotime($retainEnd));

            $params['pay_first_time'] = date('Y-m-01 00:00:00', strtotime($payStart));
        }

        $params['retain_days'] = [$retainStart, $retainEnd];

        $serv            = new FirstLoginPayRemainServ();
        $result          = $serv->payRetainInfo($params, $groups, $paginate, $sort);
        $list            = $result['list'] ?? [];
        $summary         = [];
        $info            = [];
        $dimension       = array_fill_keys(array_merge($groups, []), 0);
        $summaryMarkNode = [];

        // 整合指标
        foreach ($list as $foo) {
            $this->changeIndex($foo);
            $fooInfo = array_merge($dimension, array_intersect_key($foo, $dimension));
            $d       = implode('|', $fooInfo);

            if (!isset($info[$d])) {
                $info[$d] = $fooInfo;
            }

            $chill = &$info[$d];

            [
                'money_type' => $moneyType,
                'add_count'  => $payUser,
                'day_type'   => $dayType,
            ] = $foo;

            $this->changeRetain($chill, $foo);
            $this->changeRetain($summary, $foo, true);

            // 汇总行统计统计付费用户

            $summaryMark = $d . '|' . $moneyType;

            if (!in_array($summaryMark, $summaryMarkNode)) {
                if ($dayType == 0) {
                    if (!isset($summary['children'][$moneyType]['summary_pay_user'])) {
                        $summary['children'][$moneyType]['summary_pay_user'] = 0;
                    }
                    $summary['children'][$moneyType]['summary_pay_user'] += $payUser;
                    $summaryMarkNode[]                                   = $summaryMark;
                }
            }
        }

        unset($chill);

        $retainShow       = $this->retainShowFn($params['remain_type'] ?? 0);
        $constConfCollect = (new BasicServ())->getMultiOptions([
            'platform_id', 'promotion_id', 'department_id', 'user_id',
            'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
        ])->put('channel_id', (new GeneralOptionServ())->listChannelOptions());
        $replaceFn        = $this->replaceColumnDefine($constConfCollect);

        foreach ($info as $d => &$chill) {
            $replaceFn($chill);
            if (!empty($chill['children'])) {
                $this->calcRetainNodes($chill, $retainShow);
            }
            $chill['children'] = array_values($chill['children'] ?? []);
        }
        unset($chill, $foo);

        if (!empty($summary['children'])) {
            $this->calcRetainNodes($summary, $retainShow);
        }

        $summary['children'] = array_values($summary['children'] ?? []);


        $sortChildren = function (&$data) {
            $keys = array_column($data, 'pay_interval');
            foreach ($keys as &$k) {
                preg_match_all('/[0-9]+/', $k, $matches);
                $matches = $matches[0];
                $k       = $matches[0];
            }
            $d = array_combine($keys, $data);
            ksort($d);
            $data = array_values($d);
        };

        foreach ($info as &$item) {
            $chill = &$item['children'];
            $sortChildren($chill);
        }

        unset($item);
        $sortChildren($summary['children']);

        return [
            'list'    => array_values($info),
            'summary' => $summary,
        ];
    }

    /**
     * @param $result
     * @param $source
     * @param bool $isSummaryRow
     * @return void
     */
    protected function changeRetain(&$result, $source, bool $isSummaryRow = false)
    {
        [
            'money_type'   => $moneyType,
            'day_type'     => $dayType,
            'add_count'    => $payUser,
            'retain_count' => $retainUser,
        ] = $source;

        $retainNode = 'remain_' . $dayType;

        if (!isset($result['children'][$moneyType])) {
            $result['children'][$moneyType] = ['pay_interval' => $moneyType];
        }

        $item = &$result['children'][$moneyType];

        if (!$isSummaryRow) {
            if (!isset($item['summary_pay_user']) && $dayType == 0) {
                $item['summary_pay_user'] = $payUser;
            }
        }

        if (!isset($item[$retainNode])) {
            $item[$retainNode] = [
                'x' => $retainUser,
                'y' => $payUser,
            ];
        }
        else {
            $item[$retainNode]['x'] += $retainUser;
            $item[$retainNode]['y'] += $payUser;
        }

        unset($item);
    }

    /**
     * @param $data
     * @return void
     */
    private function changeIndex(&$data)
    {
        if (isset($data['group_day'])) {
            $data['tday'] = $data['group_day'];
            unset($data['group_day']);
        }
    }

    /**
     * 留存展示格式
     *
     * @param int $type
     * @return \Closure
     */
    protected function retainShowFn(int $type): \Closure
    {
        if ($type == 1) {
            return function ($x, $y) {
                return $x;
            };
        }
        elseif ($type == 2) {
            return function ($x, $y) {
                $x = is_numeric($x) ? floatval($x) : 0;
                $y = is_numeric($y) ? floatval($y) : 0;

                if (empty($x) || empty($y)) {
                    $r = '0.00%';
                }
                else {
                    $r = round($x / $y * 100, 2) . '%';
                }

                return "{$x}({$r})";
            };
        }
        else {
            return function ($x, $y) {
                $x = is_numeric($x) ? floatval($x) : 0;
                $y = is_numeric($y) ? floatval($y) : 0;

                if (empty($x) || empty($y)) return '0.00%';

                return round($x / $y * 100, 2) . '%';
            };
        }
    }

    /**
     * @param array $chill
     * @param \Closure $retainShow
     * @return void
     */
    protected function calcRetainNodes(array &$chill, \Closure $retainShow)
    {
        foreach ($chill['children'] as $k => &$foo) {
            preg_match_all('/(?<nodes>remain_\d+)/i', implode(',', array_keys($foo)), $matches);

            foreach ($matches['nodes'] as $n) {
                ['x' => $x, 'y' => $y] = $foo[$n];
                $foo[$n] = $retainShow($x, $y);
            }
        }
    }

}
<?php

namespace app\service\Media;

use app\extension\Exception\ParameterException;
use app\extension\FakeDB\FakeDB;
use app\service\Media\Helper\MediaTableConst;
use phpseclib3\Math\BigInteger\Engines\PHP\Montgomery;
use Plus\MVC\View\PHPView;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

/**
 *
 */
class AudienceAnalyzeServ
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $mode
     * @return array
     * @throws ParameterException
     */
    public function getInfo(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $rowOptions = \json_decode($params['row_options'], true);

        if (empty($rowOptions) || json_last_error() != JSON_ERROR_NONE) {
            throw new ParameterException('invalid argument: row_options');
        }

        $newParams = [];

        if (in_array('video_topic', $groups)) {
            $newParams['video_topic:exact'] = str_replace('&nbsp;', '', $rowOptions['video_topic_source'] ?? '');
            unset($rowOptions['video_topic']);
        }

        if (in_array('tday', $groups)) {
            $newParams['range_date_start'] = $newParams['range_date_end'] = $rowOptions['tday'];
            unset($rowOptions['range_date_start'], $rowOptions['range_date_end']);
        }

        $rowParams   = array_filter(
            array_intersect_key(array_diff_key($rowOptions, ['row_options']), array_flip(array_diff($groups, ['tday'])))
        );
        $newParams   = array_merge($newParams, $rowParams);
        $videoServ   = new MediaVideoServ();
        $relateQuery = $videoServ->commonTableQuery($newParams);
        $relateQuery->columns(['dwd_video.video_fitid'])->distinct();

        $qb = $this
            ->getConn()
            ->select()
            ->from(MediaTableConst::DWD_AUDIENCE_ANALYZE)
            ->where('video_fitid', 'IN', $relateQuery);

        return $qb->fetchAll();
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return void
     */
    private function whereMatch(&$qb, array $params)
    {
        if (!empty($params['task_id'])) {
            $d     = $params['task_id'];
            $subQb = $this
                ->getConn()
                ->select()
                ->from(MediaTableConst::MEDIA_PUBLISH_TASK)
                ->where('id', new Parameter($d))
                ->columns(['video_num'])
                ->distinct();

            $qb->where('video_id', 'IN', $subQb);
        }

        if (!empty($params['data_type'])) {
            $d = $params['data_type'];
            $qb->where('data_type', 'IN', new Parameter($d));
        }
    }


    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
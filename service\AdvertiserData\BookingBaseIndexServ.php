<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\AdvertiserData\Components\Matcher\PlanAdMatch;
use app\service\General\BizTagsServ;
use app\util\Common;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Table;

class BookingBaseIndexServ
{
    const MODE_ALL     = 3;
    const MODE_SUMMARY = 2;
    const MODE_INFO    = 1;

    const QB_MODE_ALL           = 511;
    const QB_MODE_AD_BASE       = 1;
    const QB_MODE_AD_EXT        = 2;
    const QB_MODE_PLAN_BASE     = 4;
    const QB_MODE_CAMPAIGN_BASE = 8;
    const QB_MODE_SVLINK        = 16;
    const QB_MODE_CHANNEL_BASE  = 32;
    const QB_MODE_ADP_OAUTH     = 64;
    const QB_MODE_ADMIN_USER    = 128;
    const QB_MODE_AD_ACCOUNT    = 256;

    private const COL_MODE_BASE_INFO  = 1;
    private const COL_MODE_BASE_GROUP = 2;
    private const COL_MODE_SUMMARY    = 3;

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int   $mode
     *
     * @return array
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $result = [];

        $qb      = $this->getQueryBuilder();
        $matcher = new PlanAdMatch($this->reflectFieldMap());

        $matcher->exec($qb, $params);

        $qb->where('t_base.is_appointment', 1);

        $qb->columns($this->getColumns(static::COL_MODE_BASE_INFO));

        if ($mode & self::MODE_INFO) {
            $infoQb = clone $qb;
            $newDb  = $this->getConn();
            $infoQb = $newDb->select()->from(new Fragment('(' . $infoQb->__toString() . ') as base_main'));

            if (!empty($groups)) {
                foreach ($this->reflectGroupGen($groups) as $g) {
                    $infoQb->groupBy($g);
                }
            }

            if (!empty($sort)) {
                $infoQb->orderBy($sort);
            }

            $infoQb->columns($this->getColumns(static::COL_MODE_BASE_GROUP));

            $notHasPageInfoQb = clone $infoQb;

            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;
                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }
            Common::dumpSql($infoQb->__toString());
            $result['list']  = $infoQb->fetchAll();
            $result['total'] = $newDb
                ->select()
                ->from(new Fragment('(' . $notHasPageInfoQb->__toString() . ') as totalBody'))
                ->count();
        }

        if ($mode & self::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            $summaryQb->columns($this->getColumns(self::COL_MODE_SUMMARY));
            Common::dumpSql($summaryQb->__toString());
            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * sql 构造主体
     *
     * @param int $qbMode
     *
     * @return \Spiral\Database\Query\SelectQuery
     */
    protected function getQueryBuilder(int $qbMode = -1): \Spiral\Database\Query\SelectQuery
    {
        $db       = $this->getConn();
        $qb       = $db->select();
        $powerSql = str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql());

        $qb
            ->from(TableCollect::DWS_PLAN_AD_BASE_DAILY . ' as t_base')
            ->innerJoin(new Fragment($powerSql), 'power')
            ->on('t_base.package_id', 'power.package_id');

        if ($qbMode & self::QB_MODE_AD_EXT) {
            $qb->leftJoin(TableCollect::DWS_PLAN_AD_BASE_EXT_DAILY, 'base_ext')
                ->on([
                    't_base.tday'       => 'base_ext.tday',
                    't_base.package_id' => 'base_ext.package_id',
                    't_base.channel_id' => 'base_ext.channel_id',
                    't_base.plan_id'    => 'base_ext.plan_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_PLAN_BASE) {
            $qb
                ->leftJoin(TableCollect::ADP_PLAN_BASE, 'base_plan')
                ->on([
                    'base_plan.channel_id' => 't_base.main_channel_id',
                    'base_plan.plan_id'    => 't_base.plan_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_CAMPAIGN_BASE) {
            $qb
                ->leftJoin(TableCollect::ADP_CAMPAIGN_BASE, 'base_campaign')
                ->on([
                    'base_campaign.channel_id'  => 'base_plan.channel_id',
                    'base_campaign.campaign_id' => 'base_plan.campaign_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_SVLINK) {
            $qb
                ->leftJoin(TableCollect::SPY_SVLINK_CONF, 'svlink')
                ->on([
                    'svlink.id' => new Fragment('base_ext.plan_id + 0'),
                ]);
        }

        if ($qbMode & self::QB_MODE_CHANNEL_BASE) {
            $qb
                ->leftJoin(TableCollect::BASE_CHANNEL_CONF, 'base_channel')
                ->on([
                    'base_channel.channel_id' => 't_base.channel_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_ADP_OAUTH) {
            $qb
                ->leftJoin(TableCollect::ADP_OAUTH, 'adp_oauth')
                ->on([
                    'adp_oauth.advertiser_id' => 't_base.account_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_ADMIN_USER) {
            $qb
                ->leftJoin(TableCollect::SPY_ADMIN, 'plan_admin')
                ->on([
                    'plan_admin.id' => 'base_plan.user_id',
                ]);

            $qb
                ->leftJoin(TableCollect::SPY_ADMIN, 'svlink_admin')
                ->on([
                    'svlink_admin.id' => 'svlink.user_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_AD_ACCOUNT) {
            $qb
                ->leftJoin(TableCollect::BASE_ACCOUNT_CONF, 'base_account')
                ->on([
                    'base_account.account_id' => 't_base.account_id',
                    'base_account.status'     => new Fragment('1'),
                ]);
        }


        return $qb;
    }

    protected function reflectFieldMap(): array
    {
        return [
            'tday'            => 't_base.tday',
            'package_id'      => 't_base.package_id',
            'cp_game_id'      => 't_base.cp_game_id',
            'game_id'         => 't_base.game_id',
            'channel_id'      => [
                't_base.channel_id',
                'svlink.channel_id',
                'power.channel_id',
            ],
            'channel_main_id' => [
                'base_channel.channel_main_id',
                'power.channel_main_id',
            ],
            'platform_id'     => 'power.platform_id',
            'promotion_id'    => 'power.popularize_v2_id',
            'department_id'   => [
                'plan_admin.department_id',
                'power.ad_department_id',
            ],
            'user_id'         => [
                't_base.user_id',
                'power.ad_user_id',
            ],

        ];
    }

    /**
     * @param $groups
     *
     * @return \Generator
     */
    protected function reflectGroupGen($groups): \Generator
    {
        $reflectMap = [
            'promotion_channel_id' => 'channel_id',
        ];

        foreach ($groups as $g) {
            if (isset($reflectMap[$g])) {
                $g = $reflectMap[$g];
            }

            yield $g;
        }
    }

    /**
     * @param int $mode
     *
     * @return array
     */
    protected function getColumns(int $mode = -1): array
    {
        $planChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString     = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0, IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) AS channel_id",
            $planChannelsString
        );
        $channelMainString = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );


        $infoCols = [
            'tday'            => 't_base.tday as tday',
            'cp_game_id'      => 't_base.cp_game_id as cp_game_id',
            'game_id'         => 't_base.game_id as game_id',
            'package_id'      => 't_base.package_id as package_id',
            'app_show_id'     => 'power.app_show_id as app_show_id',
            'platform_id'     => 'power.platform_id as platform_id',
            'promotion_id'    => 'power.popularize_v2_id as promotion_id',
            'channel_main_id' => new Fragment($channelMainString),
//            'channel_id'      => new Fragment("COALESCE(IF(power.channel_id NOT IN (6568, 6822, 5329), power.channel_id, IF(t_base.channel_id != 0, t_base.channel_id,IF(COALESCE(svlink.channel_id, 0) != 0, svlink.channel_id, power.channel_id))),0) as channel_id"),
            'channel_id'      => new Fragment($channelString),
            'department_id'   => new Fragment("COALESCE(IF(power.channel_id NOT IN (".$planChannelsString."), power.ad_department_id, IF(COALESCE(plan_admin.department_id, 0) != 0, plan_admin.department_id, IF(COALESCE(svlink_admin.department_id, 0) != 0, svlink_admin.department_id, power.ad_department_id))), 0) as department_id"),
            'user_id'         => new Fragment("COALESCE(IF(power.channel_id NOT IN (".$planChannelsString."), power.ad_user_id, IF(COALESCE(base_plan.user_id, 0) != 0, base_plan.user_id, IF(COALESCE(svlink.user_id, 0) != 0, svlink.user_id, power.ad_user_id))), 0) as user_id"),
            'ad_account'      => 'base_account.ad_account as ad_account',
            'account_name'    => 'adp_oauth.advertiser_name as account_name',
            'account_id'      => 't_base.account_id as account_id',
            'campaign_id'     => 'base_plan.campaign_id as campaign_id',
            'campaign_name'   => 'base_campaign.campaign_name as campaign_name',
            'plan_id'         => 't_base.plan_id as plan_id',
            'plan_name'       => new Fragment("COALESCE(IF(t_base.plan_id != 0, IF(COALESCE(base_plan.plan_name, '') != '' , base_plan.plan_name, svlink.aid) , ''), '') as plan_name"),
        ];

        $calcIndex = [
            'cost'                   => 't_base.cost',
            'cost_discount'          => 't_base.cost_discount',
            'show'                   => 't_base.show',
            'form'                   => 'base_ext.form',
            'button'                 => 'base_ext.button',
            'page_reservation_count' => 'base_ext.page_reservation_count',
            'reservation_uv'         => 'base_ext.reservation_uv',
            'event_appoint_form'     => 'base_ext.event_appoint_form',
            'event_1_d_behavior'     => 'base_ext.event_1_d_behavior',
            'submit_1_d_cnt'         => 'base_ext.submit_1_d_cnt',
            'submit_7_d_cnt'         => 'base_ext.submit_7_d_cnt',
            'submit_unit_price_cost' => 'base_ext.submit_unit_price_cost',
            'direct_submit_1_d_cnt'  => 'base_ext.direct_submit_1_d_cnt',
        ];

        if ($mode === static::COL_MODE_BASE_INFO) {
            return array_values(array_merge($infoCols, $calcIndex));
        }
        elseif ($mode === static::COL_MODE_BASE_GROUP) {
            $infoCols = array_keys($infoCols);
            $newCols  = [];

            foreach ($calcIndex as $k => $ignoreFoo) {
                $newCols[] = new Fragment("COALESCE(SUM(`{$k}`),0) as `{$k}`");
            }

            return array_merge($infoCols, $newCols);
        }
        elseif ($mode === static::COL_MODE_SUMMARY) {
            $newCols = [];

            foreach ($calcIndex as $k => $ignoreFoo) {
                $newCols[] = new Fragment("COALESCE(SUM(`{$k}`),0) as `{$k}`");
            }
            $newCols[] = new Fragment('MAX(t_base.update_time) as max_update_time');

            return $newCols;
        }

        return [];
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
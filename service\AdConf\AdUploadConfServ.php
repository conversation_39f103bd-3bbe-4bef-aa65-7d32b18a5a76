<?php

namespace app\service\AdConf;

use app\extension\FakeDB\FakeDB;
use app\service\AdConf\Components\Matcher\AdUploadConfMatch;
use app\service\General\Helpers\TableConst;
use Spiral\Database\Database;
use Spiral\Database\Injection\Fragment;


class AdUploadConfServ
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     *
     * @return array
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $result  = [];
        $db      = $this->getConn();
        $qb      = $db->table(TableConst::AD_DATA_UPLOAD_CONF)->select();
        $matcher = new AdUploadConfMatch([]);
        $matcher->exec($qb, $params);

        $notPageQb = clone $qb;

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $pageSize] = $paginate;
            $qb
                ->limit($pageSize)
                ->offset(($page - 1) * $pageSize);
        }

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        $qb->columns([
            'id as id',
            'channel_id as channel_id',
            'package_id as package_id',
            'game_id as game_id',
            'status as status',
            new Fragment('IF(`active_upload` = 0 AND `reg_upload` = 0 AND `login_upload` = 0 AND `pay_upload` = 0 AND `create_role_upload` = 0, 0, 1) AS `status`'),
            'os as os',
            'active_upload as active_upload',
            'reg_upload as reg_upload',
            'login_upload as login_upload',
            'pay_upload as pay_upload',
            'remain_upload as remain_upload',
            'notice as notice',
            'admin_id as user_id',
            'ext as ext',
            'create_role_upload as create_role_upload',
            'pay_virtual_upload as pay_virtual_upload',
        ]);

        $result['list']  = $qb->fetchAll();
        $result['total'] = $notPageQb->count();

        return $result;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('dataspy');
    }

    /**
     * @param $list
     *
     * @return mixed
     * @throws \Throwable
     */
    public function updateOrInsert($list)
    {
        $db = $this->getConn();

        return $db->transaction(static function (Database $ndb) use ($list) {
            foreach ($list as $item) {
                $id = $item['id'] ?? null;
                if (empty($id)) {
                    $ndb->table(TableConst::AD_DATA_UPLOAD_CONF)->insertOne($item);
                }
                else {
                    $count =
                        $ndb
                            ->select()
                            ->from(TableConst::AD_DATA_UPLOAD_CONF)
                            ->where('id', $id)->count();

                    if ($count == 0) {
                        throw new \Exception('没找到对应ID信息');
                    }

                    $updateItem = array_diff_key($item, ['id' => '']);
                    $ndb
                        ->table(TableConst::AD_DATA_UPLOAD_CONF)
                        ->update($updateItem)
                        ->where('id', $id)
                        ->run();
                }
            }

            return true;
        });
    }
}
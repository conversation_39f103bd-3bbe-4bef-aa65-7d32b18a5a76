<?php

namespace app\service\AdvertiseLive;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiseLive\Helpers\TableConst;
use Spiral\Database\Database;
use Spiral\Database\Injection\Parameter;


class LiveAccountServ
{
    /**
     * 列表
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     *
     * @return array
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $db = $this->getConn();
        $qb = $db->select()->from(TableConst::CONF_LIVE_ACCOUNT);

        $qb->columns([
            'id as id',
            'type as type',
            'account as account',
            'account_id as account_id',
            'collaborative_code as collaborative_code',
            'sorted as sorted',
            'operations_manager as operations_manager'
        ]);

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        return $qb->fetchAll();
    }

    /**
     * @param $list
     *
     * @return mixed
     * @throws \Throwable
     */
    public function updateMultiById($list)
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $nDb) use ($list) {
            foreach ($list as $item) {
                $accountId = Arr::get($item, 'account_id');
                $type      = Arr::get($item, 'type');
                $id        = Arr::pull($item, 'id');

                if (empty($accountId) || empty($type)) {
                    throw new \InvalidArgumentException('账号ID或直播账号类型缺失');
                }

                if (!is_numeric($id)) {
                    throw new \InvalidArgumentException('id缺失');
                }

                $count = $nDb
                    ->table(TableConst::CONF_LIVE_ACCOUNT)
                    ->select()
                    ->where('account_id', $accountId)
                    ->where('type', $type)
                    ->where('id', '!=', $id)
                    ->count();

                if ($count > 0) {
                    throw new \InvalidArgumentException('该直播账号已存在');
                }

                $nDb
                    ->table(TableConst::CONF_LIVE_ACCOUNT)
                    ->update($item)
                    ->where('account_id', $accountId)
                    ->where('type', $type)
                    ->run();
            }

            return true;
        });
    }

    /**
     * @throws \Throwable
     */
    public function updateByAccountId($list)
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $ndb) use ($list) {
            foreach ($list as $item) {
                $accountId = Arr::get($item, 'account_id');

                $count = $ndb
                    ->table(TableConst::CONF_LIVE_ACCOUNT)
                    ->select()
                    ->where('account_id', $accountId)
                    ->count();

                if ($count > 0) {
                    throw new \InvalidArgumentException('该直播账号已存在');
                }

                $ndb
                    ->table(TableConst::CONF_LIVE_ACCOUNT)
                    ->update($item)
                    ->where('account_id', $accountId)
                    ->run();
            }

            return true;
        });
    }

    /**
     * @param array $singleData
     *
     * @return mixed
     * @throws \Throwable
     */
    public function insert(array $singleData = [])
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $nDb) use ($singleData) {
            $baseQb = $nDb->table(TableConst::CONF_LIVE_ACCOUNT);

            if (
                empty($singleData['account_id'])
                || empty($singleData['type'])
            ) {
                throw new \InvalidArgumentException('必要参数缺失');
            }

            $accountId = $singleData['account_id'];
            $type      = $singleData['type'];
            $count     = $baseQb->select()->where('account_id', $accountId)->where('type', $type)->count();

            if ($count > 0) {
                throw new \InvalidArgumentException('该直播账号已存在');
            }

            return $baseQb->insertOne($singleData);
        });
    }

    /**
     * 硬删除
     *
     * @param array $ids
     *
     * @return mixed
     * @throws \Throwable
     */
    public function removeByAccountIds(array $ids)
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $nDb) use ($ids) {
            $baseQb = $nDb->table(TableConst::CONF_LIVE_ACCOUNT);

            $delQb = (clone $baseQb)->delete()->where('account_id', new Parameter($ids));

            return $delQb->run();
        });
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('base_conf_platform');
    }
}
with role_detail as (
SELECT *
FROM (
SELECT
to_date(t1.time)              AS tday,
t1.cp_game_id,
t1.role_id,
any(t1.role_name)             as role_name,
any(t1.`time`)                as create_role_time,
max_by(t2.source_id, t2.time) AS source_id
FROM ddc_platform.dwd_sdk_role_firstlogin t1
JOIN ddc_platform.dwd_sdk_user_newlogin_seven t2
ON t1.cp_game_id = t2.cp_game_id AND t1.core_account = t2.core_account AND t1.time >= t2.time
{* 搜索条件 *}
{if !empty($params)}
    {assign var="tag_first_where" value=1}

    {foreach $params as $key => $item}
        {if $key eq 'range_date'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            ( to_date(t1.time) BETWEEN '{$item[0]}' AND '{$item[1]}' and to_date(t2.time) BETWEEN '{$item[0]}' AND '{$item[1]}' )
        {/if}
    {/foreach}

{/if}
GROUP BY to_date(t1.time), t1.cp_game_id, t1.role_id
) t3
order by create_role_time desc
)
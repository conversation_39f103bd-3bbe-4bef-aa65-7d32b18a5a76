<?php

namespace app\service\AdvertiseLive;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiseLive\Components\Matcher\AdLiveMatch;
use app\service\AdvertiseLive\Helpers\TableConst;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\AdvertiserData\Components\Matcher\BaseMatch;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class LiveBaseServ
{
    const MODE_ALL     = 3;
    const MODE_SUMMARY = 2;
    const MODE_LIST    = 1;

    /**
     * @param SelectQuery $dutyQb
     * @param array       $params
     * @param array       $groups
     * @param array       $paginate
     * @param array       $sort
     * @param int         $mode
     *
     * @return array
     */
    public function getListByLiveDutyQb(
        SelectQuery $dutyQb, array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $mainQueryBuilder = $this->getLiveSubQuery($params);
        $db               = $this->getConn();
        $qb               = $db->select()->from(new Fragment('(' . $mainQueryBuilder->__toString() . ') as t1'));

        $qb
            ->innerJoin(new Fragment('(' . $dutyQb->__toString() . ')'), 't2')
            ->onWhere(
                new Fragment('t1.tday between DATE_SUB(t2.start_time, INTERVAL 30 MINUTE) and DATE_ADD(t2.end_time, INTERVAL 30 MINUTE)')
            )->on(['t1.package_id' => 't2.package_id']);

        $paramMode = $mode & (static::MODE_SUMMARY | static::MODE_LIST | static::MODE_ALL);

        $qb->columns([
            't2.tday',
            't2.start_time as start_time',
            't2.end_time as end_time',
            't1.package_id',
            't2.channel_id',
            't2.operation_account_id',
            't2.range_time as range_time',
            't2.account_id as account_id',
            't2.account_name as account_name',
            'cp_game_id',
            'game_id',
            'anchor_id',
            'anchor_name',
            'docking_partner',
            'live_platform',
            'collaborative_code',
            'live_team',
            'assistant_id',

            new Fragment('SUM(cost) as cost'),
            new Fragment('SUM(cost_discount) as cost_discount'),
            new Fragment('SUM(new_user) as new_user'),
            new Fragment('SUM(pay_user_new) as pay_user_new'),
            new Fragment('SUM(pay_money_new) as pay_money_new'),
        ]);

        $result  = [];
        $matcher = new AdLiveMatch([
            'package_id'      => 't1.package_id',
            'channel_id'      => 't2.channel_id',
            'live_account_id' => 'account_id',
        ]);
        $matcher->exec($qb, $params);

        if ($paramMode & static::MODE_LIST) {
            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    $infoQb->groupBy($g);
                }
            }

            $result['list'] = $infoQb->fetchAll();
        }

        if ($paramMode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;

            $summaryQb->columns([
                new Fragment('SUM(cost) as cost'),
                new Fragment('SUM(cost_discount) as cost_discount'),
                new Fragment('SUM(new_user) as new_user'),
                new Fragment('SUM(pay_user_new) as pay_user_new'),
                new Fragment('SUM(pay_money_new) as pay_money_new'),
            ]);

            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * @param SelectQuery $dutyQb
     * @param array       $params
     * @param array       $groups
     * @param array       $paginate
     * @param array       $sort
     * @param int         $mode
     *
     * @return array
     */
    public function getCostByLiveDutyQb(
        SelectQuery $dutyQb, array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $costQb = $this->getCostSubQuery($params);
        $db     = $this->getConn();
        $qb     = $db->select()->from(new Fragment('(' . $costQb->__toString() . ') as t1'));

        $qb
            ->innerJoin(new Fragment('(' . $dutyQb->__toString() . ')'), 't2')
            ->onWhere(new Fragment(
                '((`t1`.`start_time` <= `t2`.`end_time` AND t1.end_time >= t2.start_time) or (`t2`.`start_time` <= `t1`.`end_time` AND t2.end_time >= t1.start_time))'
            ))
            ->on(['t1.package_id' => 't2.package_id'])
            ->on(['t1.ad_account_id' => 't2.account_id']);

        $paramMode = $mode & (static::MODE_SUMMARY | static::MODE_LIST | static::MODE_ALL);


        $qb->columns([
            't2.tday',
            't2.start_time as start_time',
            't2.end_time as end_time',
            't1.package_id',
            't2.range_time',
            't2.account_id as account_id',
            't2.account_name as account_name',
            'cp_game_id',
            'game_id',
            'anchor_id',
            'anchor_name',
            'docking_partner',
            'live_platform',
            'collaborative_code',
            'live_team',
            'assistant_id',
            't2.channel_id',
            't2.operation_account_id',

            new Fragment('SUM(cost_1) as cost_1'),
            new Fragment('SUM(cost_discount_1) as cost_discount_1'),
            new Fragment('SUM(cost_2) as cost_2'),
            new Fragment('SUM(cost_discount_2) as cost_discount_2'),
            new Fragment('SUM(cost_3) as cost_3'),
            new Fragment('SUM(cost_discount_3) as cost_discount_3'),
        ]);

        $result  = [];
        $matcher = new AdLiveMatch([
            'package_id'      => 't1.package_id',
            'live_account_id' => 'account_id',
        ]);
        $matcher->exec($qb, $params);

        if ($paramMode & static::MODE_LIST) {
            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    $infoQb->groupBy($g);
                }
            }

            $result['list'] = $infoQb->fetchAll();
        }

        if ($paramMode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;

            $summaryQb->columns([
                new Fragment('SUM(cost_1) as cost_1'),
                new Fragment('SUM(cost_discount_1) as cost_discount_1'),
                new Fragment('SUM(cost_2) as cost_2'),
                new Fragment('SUM(cost_discount_2) as cost_discount_2'),
                new Fragment('SUM(cost_3) as cost_3'),
                new Fragment('SUM(cost_discount_3) as cost_discount_3'),
            ]);

            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * @param array $params
     *
     * @return SelectQuery
     */
    private function getCostSubQuery(array &$params): SelectQuery
    {
        $db       = $this->getConn();
        $qb       = $db->select()->from(TableCollect::DWD_AD_LIVE_COST . ' as live_cost');
        $powerSql = str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql());
        $qb
            ->innerJoin(new Fragment($powerSql), 'power')
            ->on([
                'live_cost.package_id' => 'power.package_id',
            ]);

        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            [$timeStart, $timeEnd] = (static function ($params) {
                $rangeDate = [
                    $params['range_date_start'],
                    $params['range_date_end'],
                ];
                sort($rangeDate);
                return $rangeDate;
            })($params);

            unset($params['range_date_start'], $params['range_date_end']);
            $timeStart .= ' 00:00:00';
            $timeEnd   .= ' 23:59:59';

            $qb
                ->where('live_cost.start_time', 'between', $timeStart, $timeEnd);
        }

        $matcher = new BaseMatch([
            'package_id' => 'live_cost.package_id',
        ]);
        $matcher->exec($qb, $params);

        $qb->columns([
            'live_cost.start_time as start_time',
            'live_cost.end_time as end_time',
            'live_cost.ad_account_id as ad_account_id',
            'live_cost.package_id as package_id',
            new Fragment('SUM(IF(cost_type = 1, cost, 0)) as cost_1'), // 主播成本(返点前)
            new Fragment('SUM(IF(cost_type = 1, cost_discount, 0)) as cost_discount_1'), // 主播成本(返点后)
            new Fragment('SUM(IF(cost_type = 2, cost, 0)) as cost_2'), // 福袋成本(返点前)
            new Fragment('SUM(IF(cost_type = 2, cost_discount, 0)) as cost_discount_2'), // 福袋成本(返点后)
            new Fragment('SUM(IF(cost_type = 3, cost, 0)) as cost_3'), // 小手柄成本(返点前)
            new Fragment('SUM(IF(cost_type = 3, cost_discount, 0)) as cost_discount_3'), // 小手柄成本(返点后)
        ]);

        $qb
            ->groupBy('start_time')
            ->groupBy('end_time')
            ->groupBy('live_cost.package_id')
            ->groupBy('ad_account_id');

        return $qb;
    }


    /**
     * @param array $params
     *
     * @return SelectQuery
     */
    private function getLiveSubQuery(array &$params): SelectQuery
    {
        $db       = $this->getConn();
        $qb       = $db->select()->from(TableCollect::DWS_PLAN_AD_BASE_HOURLY . ' as t_base');
        $powerSql = str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql());

        $qb
            ->innerJoin(TableCollect::DWS_PLAN_AD_PAYMENT_HOURLY, 't_payment')
            ->on([
                't_base.tday'       => 't_payment.tday',
                't_base.package_id' => 't_payment.package_id',
                't_base.channel_id' => 't_payment.channel_id',
                't_base.plan_id'    => 't_payment.plan_id',
            ])
            ->innerJoin(new Fragment($powerSql), 'power')
            ->on('t_base.package_id', 'power.package_id');

        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            [$timeStart, $timeEnd] = (static function ($params) {
                $rangeDate = [
                    $params['range_date_start'],
                    $params['range_date_end'],
                ];
                sort($rangeDate);
                return $rangeDate;
            })($params);
            unset($params['range_date_start'], $params['range_date_end']);

            $timeStart .= ' 00:00:00';
            $timeEnd   .= ' 23:59:59';

            $qb->where('t_base.tday', 'between', $timeStart, $timeEnd);
        }

        $matcher = new BaseMatch([
            'cp_game_id' => 't_base.cp_game_id',
            'package_id' => 't_base.package_id',
            'game_id'    => 't_base.game_id',
        ]);
        $matcher->exec($qb, $params);

        $qb->columns([
            't_base.tday as tday',
            't_base.package_id as package_id',
            't_base.channel_id as channel_id',
            new Fragment('SUM(t_base.new_user) as new_user'),
            new Fragment('SUM(t_base.cost) as cost'),
            new Fragment('SUM(t_base.cost_discount) as cost_discount'),
            new Fragment('SUM(t_payment.pay_user_new) as pay_user_new'),
            new Fragment('SUM(t_payment.pay_money_new) as pay_money_new'),
        ]);

        $qb
            ->groupBy('t_base.tday')
            ->groupBy('t_base.package_id')
            ->groupBy('t_base.channel_id');

        return $qb;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
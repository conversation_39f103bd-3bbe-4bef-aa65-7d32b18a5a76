<?php

namespace app\apps\logs\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\log\PaymentVoidedLogic;
use app\logic\log\RechargeSummaryLogic;
use app\logic\log\UserPaymentLogic;
use app\service\BusinessPlatform\OptionServ;
use app\service\ConfigService\BasicServ;
use app\service\SourceData\PaymentServ;
use Spiral\Database\Injection\Parameter;

/**
 * 日志 -> 退款明细
 *
 * @route /logs/payment-info/*
 *
 */
class PaymentVoidedController extends BaseTableController
{
    /**
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        try {
            return  (new PaymentVoidedLogic())->listInfo($params);

        }
        catch (\UnexpectedValueException $ue) {
            return ['list' => [], 'summary' => [], 'total' => 0];
        }
    }

    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '退款日期', 'dataIndex' => 'tday','sorter'=>true, 'classify' => ['attrs', 'base']],
            ['title' => '游戏原名', 'dataIndex' => 'cp_game_id', 'classify' => ['attrs', 'base']],
            ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'classify' => ['attrs', 'base']],
            ['title' => '包号', 'dataIndex' => 'package_id', 'classify' => ['attrs', 'base']],
            ['title' => '主渠道', 'dataIndex' => 'channel_main_id', 'classify' => ['attrs', 'base']],
            ['title' => '核心账号', 'dataIndex' => 'core_account', 'classify' => ['attrs', 'base']],
            ['title' => '登录账号', 'dataIndex' => 'login_account', 'classify' => ['attrs', 'base']],
            ['title' => '角色ID', 'dataIndex' => 'role_id', 'classify' => ['attrs', 'base']],
            ['title' => '区服编号', 'dataIndex' => 'game_server_id', 'classify' => ['attrs', 'base']],

            ['title' => '手盟订单', 'dataIndex' => 'order_id',  'classify' => ['attrs', 'order_index']],
            ['title' => '合作方订单号', 'dataIndex' => 'cp_order_id', 'classify' => ['attrs', 'order_index']],
            ['title' => '订单金额', 'dataIndex' => 'money',  'classify' => ['attrs', 'order_index']],
            ['title' => '支付渠道', 'dataIndex' => 'payway', 'classify' => ['attrs', 'order_index']],
            ['title' => '支付时间', 'dataIndex' => 'pay_time', 'classify' => ['attrs', 'order_index']],
            ['title' => '订单类型', 'dataIndex' => 'order_type', 'classify' => ['attrs', 'order_index']],
            ['title' => '退款类型', 'dataIndex' => 'voided_type','sorter'=>true,  'classify' => ['attrs', 'voided_index']],
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'order_index', 'label' => '订单信息'],
                    ['value' => 'voided_index', 'label' => '退款信息'],
                ],
            ],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $start = date('Y-m-d 00:00:00');
        $end   = date('Y-m-d 23:59:59');

        return collect([
            ['field' => 'range_date_start', 'default' => $start], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $end], // 统计日期结束时间
            ['field' => 'package_id'],
            ['field' => 'game_id'],
            ['field' => 'cp_game_id'],
            ['field' => 'order_id'],
            ['field' => 'core_account'],
            ['field' => 'login_account'],
            ['field' => 'voided_type'],
            ['field' => 'game_server_id'],
            ['field' => 'role_id'],
            ['field' => 'payway'],
            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

}
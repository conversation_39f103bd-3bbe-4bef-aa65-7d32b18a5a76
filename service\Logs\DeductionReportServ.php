<?php

namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;
use app\util\Common;
use Smarty\Exception;

class DeductionReportServ
{
    /**
     * @throws Exception
     * @throws \Exception
     */
    public function getList(
        array $params = [], array $paginate = [], array $sort = []
    ): array
    {
        $tpl      = \Plus::$app->sqlTemplates->createTemplate('sql/logs/deduction_report.tpl');
        $countTpl = \Plus::$app->sqlTemplates->createTemplate('sql/logs/deduction_report_count.tpl');
        $wrapSort = [];
        if(!empty($sort)) {
            foreach ($sort as $key => $value) {
                $key = str_replace('t1', 't_main', $key);
                $wrapSort[$key] = "{$value}";
            }
        }

        $tpl
            ->assign('params', $params)
            ->assign('wrap_sort', $wrapSort)
            ->assign('paginate', $paginate)
            ->assign('sort', $sort);

        $countTpl->assign('params', $params);

        $infoSql  = $tpl->fetch();
        $countSql = $countTpl->fetch();

        $db = $this->getConn();

        return [
            'list'  => $db->query($infoSql)->fetchAll(),
            'total' => $db->query($countSql)->fetch()['total_rows'] ?? 0,
        ];
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     * @throws \Exception
     */
    private function getConn()
    {
        if ($dorisIndex = Common::pingDorisIndex()) {
            return FakeDB::connection($dorisIndex);
        }
        else {
            throw new \RuntimeException('与数据库连接断开');
        }

    }
}
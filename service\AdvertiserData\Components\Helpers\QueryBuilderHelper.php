<?php

namespace app\service\AdvertiserData\Components\Helpers;

use Spiral\Database\Injection\Expression;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

class QueryBuilderHelper
{
    /**
     * @param SelectQuery|QueryInterface $qb
     * @param string|string[] $field
     * @param mixed $data
     *
     * @return void
     */
    public static function baseBuild(&$qb, $field, $data)
    {
        if (is_array($field)) {
            $qb->where(function (SelectQuery $query) use ($field, $data) {
                foreach ($field as $f) {
                    $query->orWhere($f, new Parameter($data));
                }
            });
        }
        else {
            $qb->where($field, new Parameter($data));
        }
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param string|string[] $field
     * @param mixed $data
     *
     * @return void
     */
    public static function priorityOrderBuild(&$qb, $field, $data)
    {
        if (is_array($field)) {
            // 如果是数组的话, 按优先级去匹配
            $qb->where(function (SelectQuery $query) use ($field, $data) {
                $shouldNullFields = [];

                foreach ($field as $f) {
                    if (empty($shouldNullFields)) {
                        $query->orWhere($f, new Parameter($data));
                    }
                    else {
                        $query->orWhere(function (SelectQuery $subQuery) use ($shouldNullFields, $f, $data) {
                            foreach ($shouldNullFields as $ff) {
                                $subQuery->where(new Expression("COALESCE({$ff}, 0) = 0"));
                            }

                            $subQuery->where($f, new Parameter($data));
                        });
                    }

                    $shouldNullFields[] = $f;
                }
            });
        }
        else {
            $qb->where($field, new Parameter($data));
        }
    }

    /**
     * 用作基本的模糊匹配
     *
     * @param SelectQuery $qb
     * @param $field
     * @param $data
     * @return void
     */
    public static function likeBuild(SelectQuery &$qb, $field, $data)
    {
        if (is_array($field)) {
            $qb->where(function (SelectQuery $query) use ($field, $data) {
                foreach ($field as $f) {
                    $query->orWhere($f, 'like', "%{$data}%");
                }
            });
        }
        else {
            $qb->where($field, 'like', "%{$data}%");
        }
    }
}
<?php

namespace app\service\ConfigService\Tables;

use app\extension\Support\Collections\Collection;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class FirstLogin implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    public function getFields($options = null): Collection
    {
        $rangeDateDimension = (int)($options['range_date_dimension'] ?? 1);
        $collect            = $this->getBaseFields(['baseOperationCollect', 'operatorBaseCollect']);

        if (1 === $rangeDateDimension) {
            $collect = $this->timeDimension($rangeDateDimension)->merge($collect);
            $collect = $collect->except(['pay_permeation_percent', 'cost', 'cost_discount', 'new_user_cost']);
        }
        elseif (3 === $rangeDateDimension || 4 === $rangeDateDimension) {
            $collect = $collect->except([
                'pay_permeation_percent', 'active_user_7days_ago',
                'active_pay_user', 'active_pay_user_percent','pay_money_all_with_hour'
            ]);
        }
        elseif(2 === $rangeDateDimension) {
            $collect = $collect->except(['pay_money_all_with_hour']);
        }

        $collect->merge(collect([
            'cost'          => ['title' => '返点前消耗金额', 'sorter' => true,],
            'cost_discount' => ['title' => '返点后消耗金额', 'sorter' => true,],
        ]));

        $groups = $options['groups'] ?? [];

        if (in_array('package_id', $groups)) {
            $collect = $collect->merge([
                'package_tags' => ['title' => '包号标签', 'classify' => ['attrs', 'tags']]
            ]);
        }

        if (
            in_array('package_id', $groups)
            || in_array('channel_id', $groups)
        ) {
            $collect = $collect->merge([
                'channel_tags' => ['title' => '推广子渠道标签', 'classify' => ['attrs', 'tags']]
            ]);
        }

        return $this->formatStandard($collect);
    }
}
{"name": "framework/plus_auth", "description": "plus框架 后台权限管理", "config": {"secure-http": false, "optimize-autoloader": true, "apcu-autoloader": false, "allow-plugins": {"composer/installers": true}}, "repositories": {"packagist": {"type": "composer", "url": "https://mirrors.cloud.tencent.com/composer"}, "shoumeng": {"type": "composer", "url": "https://dev.910app.com/satis"}}, "minimum-stability": "dev", "require": {"php": "^7.4.3", "framework/plus": "^3.0", "firebase/php-jwt": "^6.2", "composer/installers": "~1.0", "ext-json": "*", "ext-pdo": "*", "ext-mbstring": "*", "aura/sqlquery": "4.x-dev", "langleyfoxall/math_eval": "^2.0", "cycle/orm": "1.x-dev", "cycle/annotated": "2.x-dev", "framework/plus_rabbit": "^1.0", "ext-pcntl": "*", "ext-posix": "*", "ext-redis": "*", "phpoffice/phpspreadsheet": "^1.28", "guzzlehttp/guzzle": "^7.5@dev", "league/climate": "^3.8", "framework/php_crontab": "dev-master", "smarty/smarty": "^5.0@dev", "ifsnop/mysqldump-php": "^2.12"}, "require-dev": {"framework/phplint": "1.*", "phpunit/phpunit": "9.6.x-dev", "codeception/codeception": "^4.2@beta", "codeception/module-phpbrowser": "2.0.x-dev", "codeception/module-asserts": "2.0.x-dev"}, "autoload": {"psr-4": {"app\\": ""}, "files": ["extension/Support/Collections/helpers.php", "extension/Support/Helpers/helpers.php"]}, "autoload-dev": {"classmap": ["tests/module-plus"]}, "scripts": {"post-update-cmd": "lint\\Hook::install", "lint": "lint\\Hook::install", "del-git": "find ./vendor -name '.git' |xargs rm -rf"}}
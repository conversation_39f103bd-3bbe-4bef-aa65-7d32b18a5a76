<?php

namespace app\logic\advertise;

use app\extension\Support\Collections\Arr;
use app\models\baseConfPlatform\TbBaseChannelConf;
use app\models\baseConfPlatform\TbBaseCpGameConf;
use app\models\baseConfPlatform\TbBaseGameConf;
use app\service\Advertiser\AdPaymentProvider;
use app\service\Advertiser\PaidAccountProfileProvider;
use app\service\AdvertiserData\AdPaymentInfoServ;
use Smarty\Exception;

class AdPaymentInfoLogic
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     *
     * @return array
     * @throws Exception
     */
    public function getPaymentInfo(
        array $params = [], array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $newGroup = ['main_account'];

        if (empty($groups)) {
            $dimension = $params['dimension'] ?? 'plan';

            if ($dimension == 'plan') {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'plan_id'];
            }
            elseif ($dimension == 'campaign') {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'campaign_id'];
            }
            elseif ($dimension == 'creative') {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'creative_id'];
            }
        }

        // 解析每行的详细信息数据
        // 根据组合的汇总维度组合每行参数
        $rowOptions = $params['row_option'] ?? '';

        if (is_string($rowOptions)) {
            $rowOptions = \json_decode($rowOptions, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \InvalidArgumentException("行数据解析错误/缺失");
            }
        }

        // 部分数据需要转化
        foreach ($groups as $k) {
            $chill = $rowOptions[$k] ?? '-';
            if ($chill == '-') {
                $params[$k] = 0;
                continue;
            }

            if ($k == 'tday') {
                $params['range_date_start'] = $params['range_date_end'] = $chill;
            }
            else {
                $params[$k] = $chill;
            }
        }
        $today                = date('Y-m-d');
        $params['range_date'] = [
            $params['range_date_start'] ?? $today,
            $params['range_date_end'] ?? $today,
        ];
        unset($params['range_date_start'], $params['range_date_end']);

        if (isset($params['promotion_channel_id'])) {
            $params['channel_id'] = $params['promotion_channel_id'];
            unset($params['promotion_channel_id']);
        }


        $provider = new AdPaymentProvider();
        $result   = $provider->getCreativeInfo($params, $groups, $paginate);

        if (!empty($result['list'])) {
            $list              = &$result['list'];
            $accountList       = array_column($list, 'main_account');
            $exceptionAccounts = (new PaidAccountProfileProvider())->checkExceptionPaidAccount($accountList);

            foreach ($list as &$foo) {
                if (in_array($foo['main_account'], $exceptionAccounts)) {
                    $foo['is_exception'] = 1;
                }
                else {
                    $foo['is_exception'] = 0;
                }
            }

            unset($list);
        }


        return $result;
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws Exception
     */
    public function paymentInfoGroupMainAccount(
        array $params = [], array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $newGroups = ['core_account', 'main_account'];

        if (empty($groups)) {
            $dimension = $params['dimension'] ?? 'plan';

            if ($dimension == 'plan') {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'plan_id'];
            }
            elseif ($dimension == 'campaign') {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'campaign_id'];
            }
            elseif ($dimension == 'creative') {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'creative_id'];
            }
        }

        // 根据组合的汇总维度组合每行参数
        $rowOptions = $params['row_option'] ?? '';

        if (is_string($rowOptions)) {
            $rowOptions = \json_decode($rowOptions, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \InvalidArgumentException("行数据解析错误/缺失");
            }
        }

        foreach ($groups as $k) {
            $chill = $rowOptions[$k] ?? '-';
            if ($chill == '-') {
                $params[$k] = 0;
                continue;
            }

            if ($k == 'tday') {
                $params['range_date_start'] = $params['range_date_end'] = $chill;
            }
            else {
                $params[$k] = $chill;
            }
        }
        unset($params['data_scope']);
        unset($params['row_option']);

        $this->formatParams($params);

        return (new AdPaymentProvider())->groupByCore($params, $newGroups, $paginate, $sort);
    }

    /**
     * @param $params
     * @return void
     */
    private function formatParams(&$params)
    {
        $params['range_date'] = [
            Arr::pull($params, 'range_date_start') ?? date('Y-m-d'),
            Arr::pull($params, 'range_date_end') ?? date('Y-m-d'),
        ];

        if (!isset($params['target'])) {
            $params['target'] = 'pay_money';
        }

//        if (!empty($params)) {
//            foreach ($params as $k => &$foo) {
//                if (
//                    in_array($k, [
//                        'cp_game_id', 'game_id', 'package_id', 'core_account',
//                        'platform_id', 'promotion_id', 'department_id', 'user_id',
//                    ])
//                    && is_array($foo)
//                ) {
//                    $foo = implode(',', array_map(fn($item) => "'{$item}'", $foo));
//                }
//            }
//        }
    }

}
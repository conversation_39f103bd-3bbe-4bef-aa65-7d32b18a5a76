{include file="sql/advertise/ad_dashboard/creative_dash_info.tpl"}
select count(1) as total_row from (
select
{if !empty($groups)}
    {foreach $groups as $item}
        {if $item eq 'tday'} tday,
        {elseif $item eq 'cp_game_id'} cp_game_id,
        {elseif $item eq 'game_id'} game_id,
        {elseif $item eq 'app_show_id'} app_show_id,
        {elseif $item eq 'channel_main_id'} channel_main_id,
        {elseif $item eq 'promotion_channel_id'} promotion_channel_id,
        {elseif $item eq 'user_id'} dim_user_id as user_id,
        {elseif $item eq 'department_id'} department_id,
        {elseif $item eq 'package_id'} package_id,
        {elseif $item eq 'platform_id'} t1.platform_id,
        {elseif $item eq 'account_id'} account_id,
        {elseif $item eq 'ad_account'} ad_account,
        {elseif $item eq 'campaign_id'} campaign_id,any(campaign_name) as campaign_name,
        {elseif $item eq 'plan_id'} plan_id,
        {elseif $item eq 'creative_id'} creative_id,
        {elseif $item eq 'dim_user_os'} dim_user_os,
        {/if}
    {/foreach}
{/if}
{if !empty($hit_fields)}
    {foreach $hit_fields as $item}
        {if $item eq 'show_cnt'} sum(show_cnt) as show_cnt, {continue} {/if}
        {if $item eq 'click_cnt'} sum(click_cnt) as click_cnt, {continue} {/if}
        {if $item eq 'activate_cnt'} sum(activate_cnt) as activate_cnt, {continue} {/if}
        {if $item eq 'convert_cnt'} sum(convert_cnt) as convert_cnt, {continue} {/if}
        {if $item eq 'install_cnt'} sum(install_cnt) as install_cnt, {continue} {/if}
        {if $item eq 'lp_view'} sum(lp_view) as lp_view, {continue} {/if}
        {if $item eq 'lp_download'} sum(lp_download) as lp_download, {continue} {/if}
        {if $item eq 'download_start'} sum(download_start) as download_start, {continue} {/if}
        {if $item eq 'register'} sum(register) as register, {continue} {/if}
        {if $item eq 'cost'} sum(cost) as cost, {continue} {/if}
        {if $item eq 'cost_discount'} sum(cost_discount) as cost_discount, {continue} {/if}
        {if $item eq 'new_real_user'} sum(new_real_user) as new_real_user, {continue} {/if}
        {if $item eq 'new_user'} sum(new_user) as new_user, {continue} {/if}
        {if $item eq 'new_user_emulator'} sum(new_user_emulator) as new_user_emulator, {continue} {/if}
        {if $item eq 'activate_device'} sum(activate_device) as activate_device, {continue} {/if}
        {if $item eq 'create_role_new'} sum(create_role_new) as create_role_new, {continue} {/if}
        {if $item eq 'pay_new_user_7days'} sum(pay_new_user_7days) as pay_new_user_7days, {continue} {/if}
        {if $item eq 'pay_frequency_7days'} sum(pay_frequency_7days) as pay_frequency_7days, {continue} {/if}
        {if $item eq 'online_time'} sum(online_time) as online_time, {continue} {/if}
        {if $item eq 'first_online_time'} sum(first_online_time) as first_online_time,{continue} {/if}
        {if $item eq 'active_user'} sum(active_user) as active_user, {continue} {/if}
        {if $item eq 'active_user_week'} sum(active_user_week) as active_user_week, {continue} {/if}
        {if $item eq 'total_play'} sum(total_play) as total_play, {continue} {/if}
        {if $item eq 'play_time_per_play'} sum(play_time_per_play) as play_time_per_play, {continue} {/if}
        {if $item eq 'play_duration_3s'} sum(play_duration_3s) as play_duration_3s, {continue} {/if}
        {if $item eq 'pay_user'} sum(pay_user) as pay_user, {continue} {/if}
        {if $item eq 'pay_money'} sum(pay_money) as pay_money, {continue} {/if}
        {if $item eq 'pay_count'} sum(pay_count) as pay_count, {continue} {/if}
        {if $item eq 'pay_user_new'} sum(pay_user_new) as pay_user_new, {continue} {/if}
        {if $item eq 'pay_money_new'} sum(pay_money_new) as pay_money_new, {continue} {/if}
        {if $item eq 'pay_count_new'} sum(pay_count_new) as pay_count_new, {continue} {/if}
        {if $item eq 'pay_money_no_visual'} sum(pay_money_no_visual) as pay_money_no_visual, {continue} {/if}
        {if $item eq 'pay_money_new_no_visual'} sum(pay_money_new_no_visual) as pay_money_new_no_visual, {continue} {/if}
        {if $item eq 'first_online_time_avg'} coalesce(round((sum(first_online_time) / sum(new_user) / 60), 2),0.00) as first_online_time_avg, {continue} {/if}
        {if $item eq 'online_time_avg'} coalesce(round((sum(online_time) / sum(new_user) / 60), 2), 0) as online_time_avg, {continue} {/if}
        {if $item eq 'online_time_avg'} coalesce(round((sum(online_time) / sum(new_user) / 60), 2), 0) as online_time_avg, {continue} {/if}
        {if $item eq 'new_user_cost'}coalesce(round(sum(cost_discount) / sum(new_user), 2), 0)  as new_user_cost, {continue} {/if}
        {if $item eq 'cpc'}coalesce(round(sum(cost_discount) / sum(`click`), 2), 0) as cpc, {continue} {/if}
        {if $item eq 'click_show_percent'}coalesce(round(sum(`click`) / sum(`show`) * 100, 2), 0) as click_show_percent, {continue} {/if}
        {if $item eq 'qian_cost'}coalesce(round(sum(`cost`) * 1000 / sum(`show`), 2), 0) as qian_cost, {continue} {/if}
        {if $item eq 'lp_click_percent'}coalesce(round(sum(lp_download) / sum(lp_view) * 100, 2), 0) as lp_click_percent, {continue} {/if}
        {if $item eq 'convert_cost'}coalesce(round(sum(cost) / sum(convert), 2), 0.00) as convert_cost, {continue} {/if}
        {if $item eq 'convert_percent'}coalesce(round(sum(`convert`) / sum(`click`) * 100, 2), 0) as convert_percent, {continue} {/if}
        {if $item eq 'download_start_cost_percent'}coalesce(round(sum(`cost_discount`) / sum(download_start), 2), 0) as download_start_cost_percent, {continue} {/if}
        {if $item eq 'download_finish_percent'}coalesce(round(sum(`download`) / sum(`download_start`) * 100, 2), 0) as download_finish_percent, {continue} {/if}
        {if $item eq 'install_finish_num'}coalesce(round(sum(`lp_download`) / sum(`lp_view`), 2), 0) as install_finish_num, {continue} {/if}
        {if $item eq 'install_finish_percent'}coalesce(round(sum(`install`) / sum(`download`), 2), 0) as install_finish_percent, {continue} {/if}
        {if $item eq 'activate_cost'}coalesce(round(sum(`cost_discount`) / sum(`activate`), 2), 0) as activate_cost, {continue} {/if}
        {if $item eq 'activate_percent'}coalesce(round(sum(`activate`) / sum(`click`) * 100, 2), 0) as activate_percent, {continue} {/if}
        {if $item eq 'activate_install_percent'}coalesce(round(sum(`activate`) / sum(`install`) * 100, 2), 0) as activate_install_percent,{continue} {/if}
        {if $item eq 'register_cost'}coalesce(round(sum(`cost_discount`) / sum(`register`), 2), 0) as register_cost, {continue} {/if}
        {if $item eq 'register_percent'}coalesce(round(sum(`register`) / sum(`activate`), 2), 0) as register_percent, {continue} {/if}
        {if $item eq 'new_user_real_percent'}coalesce(round(sum(`new_real_user`) / sum(`new_user`) * 100, 2), 0) as new_user_real_percent, {continue} {/if}
        {if $item eq 'pay_user_new_percent'}coalesce(round(sum(`pay_user_new`) / sum(`new_user`) * 100, 2), 0) as pay_user_new_percent, {continue} {/if}
        {if $item eq 'arpu_new_user'}coalesce(round(sum(`pay_money_new`) / sum(`new_user`), 2), 0) as arpu_new_user, {continue} {/if}
        {if $item eq 'pay_penetration'}coalesce(round(sum(`pay_money_new`) / sum(`new_user`), 2), 0) as pay_penetration, {continue} {/if}
        {if $item eq 'create_role_percent'}coalesce(round(sum(`create_role_new`) / sum(`new_user`) * 100, 2), 0) as create_role_percent, {continue} {/if}
        {if $item eq 'create_role_cost'}coalesce(round(sum(`cost_discount`) / sum(`create_role_new`) * 100, 2), 0) as create_role_cost, {continue} {/if}
        {if $item eq 'pay_frequency_avg_7days'}coalesce(round(sum(`pay_frequency_7days`) / sum(`pay_new_user_7days`), 2), 0) as pay_frequency_avg_7days, {continue} {/if}
        {if $item eq 'pay_frequency_7days_cost'}coalesce(round(sum(`cost_discount`) / sum(`pay_frequency_7days`), 2), 0) as pay_frequency_7days_cost, {continue} {/if}
        {if $item eq 'new_user_payment_cost'}coalesce(round(sum(`cost_discount`) / sum(`pay_user_new`), 2), 0) as new_user_payment_cost, {continue} {/if}
        {if $item eq 'download_start_percent'}coalesce(round(sum(`download_start`) / sum(`click`) * 100, 2), 0) as download_start_percent, {continue} {/if}
        {if $item eq 'arppu_new_user'}coalesce(round(sum(`pay_money_new`) / sum(`pay_user_new`), 2), 0) as arppu_new_user,{continue} {/if}
        {if $item eq 'play_duration_3s_percent'}coalesce(round(sum(`play_duration_3s`) / sum(`total_play`) * 100, 2), 0) as play_duration_3s_percent, {continue} {/if}
        {if $item eq 'play_time_avg'}coalesce(round(sum(`play_time_per_play`) / sum(`total_play`), 2), 0) as play_time_avg, {continue} {/if}
        {if $item eq 'show_convert_percent'}coalesce(round(sum(`convert`) / sum(`show`) * 100, 2), 0) as show_convert_percent, {continue} {/if}
    {/foreach}
{else}
    sum(`show_cnt`)                                                               as show_cnt,
    sum(click_cnt)                                                                as click_cnt,
    sum(download_cnt)                                                             as download_cnt,
    sum(activate_cnt)                                                             as activate_cnt,
    sum(`convert_cnt`)                                                            as convert_cnt,
    sum(`install_cnt`)                                                            as install_cnt,
    sum(lp_view)                                                                  as lp_view,
    sum(lp_download)                                                              as lp_download,
    sum(download_start)                                                           as download_start,
    sum(register)                                                                 as register,
    sum(cost)                                                                     as cost,
    sum(cost_discount)                                                            as cost_discount,
    sum(new_real_user)                                                            as new_real_user,
    sum(new_user)                                                                 as new_user,
    sum(new_user_emulator)                                                        as new_user_emulator,
    sum(activate_device)                                                          as activate_device,
    sum(create_role_new)                                                          as create_role_new,
    sum(pay_new_user_7days)                                                       as pay_new_user_7days,
    sum(pay_frequency_7days)                                                      as pay_frequency_7days,
    sum(online_time)                                                              as online_time,
    sum(first_online_time)                                                        as first_online_time,
    sum(active_user)                                                              as active_user,
    sum(active_user_week)                                                         as active_user_week,
    sum(total_play)                                                               as total_play,
    sum(play_time_per_play)                                                       as play_time_per_play,
    sum(play_duration_3s)                                                         as play_duration_3s,
    sum(pay_user)                                                                 as pay_user,
    sum(pay_money)                                                                as pay_money,
    sum(pay_count)                                                                as pay_count,
    sum(pay_user_new)                                                             as pay_user_new,
    sum(pay_money_new)                                                            as pay_money_new,
    sum(pay_count_new)                                                            as pay_count_new,
    sum(pay_money_no_visual)                                                      as pay_money_no_visual,
    sum(pay_money_new_no_visual)                                                  as pay_money_new_no_visual,
    coalesce(round((sum(first_online_time) / sum(new_user) / 60), 2),0.00)        as first_online_time_avg,
    coalesce(round((sum(online_time) / sum(new_user) / 60), 2), 0)                as online_time_avg,
    coalesce(round(sum(cost_discount) / sum(new_user), 2), 0)                     as new_user_cost,
    coalesce(round(sum(cost_discount) / sum(`click_cnt`), 2), 0)                  as cpc,
    coalesce(round(sum(`click_cnt`) / sum(`show_cnt`) * 100, 2), 0)               as click_show_percent,
    coalesce(round(sum(`cost`) * 1000 / sum(`show_cnt`), 2), 0)                   as qian_cost,
    coalesce(round(sum(lp_download) / sum(lp_view) * 100, 2), 0)                  as lp_click_percent,
    coalesce(round(sum(cost) / sum(convert_cnt), 2), 0.00)                        as convert_cost,
    coalesce(round(sum(`convert_cnt`) / sum(`click_cnt`) * 100, 2), 0)            as convert_percent,
    coalesce(round(sum(`cost_discount`) / sum(download_start), 2), 0)             as download_start_cost_percent,
    coalesce(round(sum(`download_cnt`) / sum(`download_start`) * 100, 2), 0)      as download_finish_percent,
    coalesce(round(sum(`lp_download`) / sum(`lp_view`), 2), 0)                    as install_finish_num,
    coalesce(round(sum(`install_cnt`) / sum(`download_cnt`), 2), 0)               as install_finish_percent,
    coalesce(round(sum(`cost_discount`) / sum(`activate_cnt`), 2), 0)             as activate_cost,
    coalesce(round(sum(`activate_cnt`) / sum(`click_cnt`) * 100, 2), 0)           as activate_percent,
    coalesce(round(sum(`activate_cnt`) / sum(`install_cnt`) * 100, 2), 0)         as activate_install_percent,
    coalesce(round(sum(`cost_discount`) / sum(`register`), 2), 0)                 as register_cost,
    coalesce(round(sum(`register`) / sum(`activate_cnt`), 2), 0)                  as register_percent,
    coalesce(round(sum(`new_real_user`) / sum(`new_user`) * 100, 2), 0)           as new_user_real_percent,
    coalesce(round(sum(`pay_user_new`) / sum(`new_user`) * 100, 2), 0)            as pay_user_new_percent,
    coalesce(round(sum(`pay_money_new`) / sum(`new_user`), 2), 0)                 as arpu_new_user,
    coalesce(round(sum(`pay_money_new`) / sum(`new_user`), 2), 0)                 as pay_penetration,
    coalesce(round(sum(`create_role_new`) / sum(`new_user`) * 100, 2), 0)         as create_role_percent,
    coalesce(round(sum(`cost_discount`) / sum(`create_role_new`) * 100, 2), 0)    as create_role_cost,
    coalesce(round(sum(`pay_frequency_7days`) / sum(`pay_new_user_7days`), 2), 0) as pay_frequency_avg_7days,
    coalesce(round(sum(`cost_discount`) / sum(`pay_frequency_7days`), 2), 0)      as pay_frequency_7days_cost,
    coalesce(round(sum(`cost_discount`) / sum(`pay_user_new`), 2), 0)             as new_user_payment_cost,
    coalesce(round(sum(`download_start`) / sum(`click_cnt`) * 100, 2), 0)         as download_start_percent,
    coalesce(round(sum(`pay_money_new`) / sum(`pay_user_new`), 2), 0)             as arppu_new_user,
    coalesce(round(sum(`play_duration_3s`) / sum(`total_play`) * 100, 2), 0)      as play_duration_3s_percent,
    coalesce(round(sum(`play_time_per_play`) / sum(`total_play`), 2), 0)          as play_time_avg,
    coalesce(round(sum(`convert_cnt`) / sum(`show_cnt`) * 100, 2), 0)             as show_convert_percent,
{/if}
max(update_time)  as last_update_time
from dashboard_info t1
left join base_conf_platform.tb_base_channel_conf t2 on t1.promotion_channel_id = t2.channel_id
left join dataspy.admin_user t3 on t1.dim_user_id=t3.id
{* 搜索条件 *}
{* 搜索条件 *}
{if !empty($params)}
    {assign var="tag_first_where" value=1}
    {foreach $params as $key => $item}
        {if $kk eq 'channel_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                t1.promotion_channel_id in ({$item|join:','})
            {else}
                t1.promotion_channel_id = {$item}
            {/if}
        {/if}

        {if $kk eq 'channel_main_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                channel_main_id in ({$item|join:','})
            {else}
                channel_main_id = {$item}
            {/if}
        {/if}

        {if $kk eq 'user_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                t1.dim_user_id in ({$item|join:','})
            {else}
                t1.dim_user_id = {$item}
            {/if}
        {/if}

        {if $kk eq 'department_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                t3.department_id in ({$item|join:','})
            {else}
                t3.department_id = {$item}
            {/if}
        {/if}

    {/foreach}
{/if}
{* 汇总维度 *}
{if !empty($groups)}
    group by
    {if !empty($groups)}
        {foreach $groups as $item}
            {if $item eq 'tday'} tday {if !$item@last}, {/if}
            {elseif $item eq 'cp_game_id'} cp_game_id {if !$item@last}, {/if}
            {elseif $item eq 'game_id'} game_id {if !$item@last}, {/if}
            {elseif $item eq 'app_show_id'} app_show_id {if !$item@last}, {/if}
            {elseif $item eq 'channel_main_id'} channel_main_id {if !$item@last}, {/if}
            {elseif $item eq 'promotion_channel_id'} promotion_channel_id {if !$item@last}, {/if}
            {elseif $item eq 'promotion_id'} promotion_id {if !$item@last}, {/if}
            {elseif $item eq 'user_id'} user_id {if !$item@last}, {/if}
            {elseif $item eq 'department_id'} department_id {if !$item@last}, {/if}
            {elseif $item eq 'package_id'} package_id {if !$item@last}, {/if}
            {elseif $item eq 'platform_id'} t1.platform_id {if !$item@last}, {/if}
            {elseif $item eq 'account_id'} account_id {if !$item@last}, {/if}
            {elseif $item eq 'ad_account'} ad_account {if !$item@last}, {/if}
            {elseif $item eq 'campaign_id'} campaign_id {if !$item@last}, {/if}
            {elseif $item eq 'plan_id'} plan_id {if !$item@last}, {/if}
            {elseif $item eq 'creative_id'} creative_id {if !$item@last}, {/if}
            {elseif $item eq 'dim_user_os'} dim_user_os {if !$item@last}, {/if}
            {/if}
        {/foreach}
    {/if}
{/if}
{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}

) info_body


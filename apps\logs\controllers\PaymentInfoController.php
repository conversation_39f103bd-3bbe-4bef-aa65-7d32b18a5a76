<?php

namespace app\apps\logs\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\log\UserPaymentLogic;
use app\service\BusinessPlatform\OptionServ;
use app\service\ConfigService\BasicServ;
use app\service\SourceData\PaymentServ;
use Spiral\Database\Injection\Parameter;

/**
 * 日志 -> 充值明细
 *
 * @route /logs/payment-info/*
 *
 */
class PaymentInfoController extends BaseTableController
{
    /**
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        try {
            return (new UserPaymentLogic())->listInfo($params);
        }
        catch (\UnexpectedValueException $ue) {
            return ['list' => [], 'summary' => [], 'total' => 0];
        }
    }

    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '日期', 'dataIndex' => 'tday', 'classify' => ['attrs', 'base']],
            ['title' => '游戏原名', 'dataIndex' => 'cp_game_id', 'classify' => ['attrs', 'base']],
            ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'classify' => ['attrs', 'base']],
            ['title' => '包号', 'dataIndex' => 'package_id', 'classify' => ['attrs', 'base']],
            ['title' => '主渠道', 'dataIndex' => 'channel_main_id', 'classify' => ['attrs', 'base']],
            ['title' => '区服编号', 'dataIndex' => 'game_server_id', 'classify' => ['attrs', 'base']],
            ['title' => '核心账号', 'dataIndex' => 'core_account', 'classify' => ['attrs', 'base']],
            ['title' => '登录账号', 'dataIndex' => 'login_account', 'classify' => ['attrs', 'base']],
            ['title' => '角色id', 'dataIndex' => 'role_id', 'classify' => ['attrs', 'base']],
            ['title' => '手盟订单', 'dataIndex' => 'order_id', 'classify' => ['attrs', 'order_index']],
            ['title' => '合作方订单号', 'dataIndex' => 'cp_order_id', 'classify' => ['attrs', 'order_index']],
            ['title' => '订单金额', 'dataIndex' => 'amount', 'classify' => ['attrs', 'order_index']],
            ['title' => '支付金额', 'dataIndex' => 'pay_amount', 'classify' => ['attrs', 'order_index']],
            ['title' => '九玩币抵扣', 'dataIndex' => 'decuct_coin', 'classify' => ['attrs', 'order_index']],
            ['title' => '代金券抵扣', 'dataIndex' => 'coupon_amount', 'classify' => ['attrs', 'order_index']],
            ['title' => '游戏币', 'dataIndex' => 'game_coin', 'classify' => ['attrs', 'order_index']],
            ['title' => '支付渠道', 'dataIndex' => 'payway', 'classify' => ['attrs', 'order_index']],
            ['title' => '支付时间', 'dataIndex' => 'pay_time', 'classify' => ['attrs', 'order_index']],
            ['title' => '订单类型', 'dataIndex' => 'order_type', 'classify' => ['attrs', 'order_index']],
            ['title' => '订单状态', 'dataIndex' => 'order_status', 'classify' => ['attrs', 'order_index']],
            ['title' => '游戏回调状态', 'dataIndex' => 'game_back_status', 'classify' => ['attrs', 'order_index']],
            ['title' => '公司主体', 'dataIndex' => 'company', 'classify' => ['attrs', 'order_index']],
            ['title' => '打标方式', 'dataIndex' => 'tag_mode', 'classify' => ['attrs', 'tag_index']],
            ['title' => '测试账号归属部门', 'dataIndex' => 'test_department', 'classify' => ['attrs', 'tag_index']],
            ['title' => '打标人员', 'dataIndex' => 'tagging_operator', 'classify' => ['attrs', 'tag_index']],
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'order_index', 'label' => '订单信息'],
                    ['value' => 'tag_index', 'label' => '打标信息'],
                ],
            ],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $start = date('Y-m-d 00:00:00');
        $end   = date('Y-m-d 23:59:59');

        return collect([
            ['field' => 'range_date_start', 'default' => $start], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $end], // 统计日期结束时间
            ['field' => 'order_id'],
            ['field' => 'package_id'],
            ['field' => 'core_account'],
            ['field' => 'login_account'],
            ['field' => 'game_server_id'],
            ['field' => 'role_id'],
            ['field' => 'cp_game_id'],
            ['field' => 'game_id'],
            ['field' => 'order_type'],
            ['field' => 'order_status'],
            ['field' => 'game_back_status'],
            ['field' => 'pay_combo'],
            ['field' => 'payway'],
            ['field' => 'tagging_operator'], // 打标人员
            ['field' => 'test_department'],
            ['field' => 'tag_mode'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

    /**
     * 充值明细打标接口
     *
     * @return array
     */
    public function taggingAction(): array
    {
        $rawBody = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('json解析错误');
        }

        $operatorId = \Plus::$service->admin->getUserId();
        $orderIds   = $rawBody['order_id'] ?? [];
        $orderType  = $rawBody['order_type'] ?? -1;

        if ($orderType < 0 || empty($orderIds)) {
            return $this->error('参数缺失');
        }

        // 文伟的只打测试标
        if ($operatorId == 131) {
            $orderType = 1;
        }

        try {
            (new PaymentServ())->taggingWithOrderIds($orderIds, $orderType, $operatorId);
        }
        catch (\Throwable $e) {
            return $this->error('打标失败, 请稍后再试');
        }

        return $this->success([]);
    }
}
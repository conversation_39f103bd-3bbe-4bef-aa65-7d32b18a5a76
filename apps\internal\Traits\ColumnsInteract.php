<?php

namespace app\apps\internal\Traits;

use app\apps\internal\Helpers\IndicatorsHelpers;
use app\extension\Support\Collections\Collection;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;

trait ColumnsInteract
{
    /**
     * 重置分组后不需要的字段
     *
     * @param $groups
     *
     * @return \Closure
     * @deprecated 下一版弃用, 转为使用resetGroupsCols
     */
    protected function resetUnusedColumn($groups): \Closure
    {
        $resetCols = ColumnManager::groupFilterColumn($groups);

        return function (&$target) use ($resetCols) {
            $target = array_merge($target, $resetCols);
        };
    }

    /**
     * 重置汇总分组字段
     *
     * @param $relationship
     * @param $groups
     * @param $fixedInfoColumns
     *
     * @return \Closure
     */
    protected function resetGroupsCols($relationship, $groups, $fixedInfoColumns): \Closure
    {
        $diffCols  = array_diff($fixedInfoColumns, $relationship);
        $resetCols = array_fill_keys($diffCols, '-');

        return function (&$target) use ($resetCols) {
            $target = array_merge($target, $resetCols);
        };
    }


    /**
     * @param Collection|null $options
     *
     * @return \Closure
     */
    protected function replaceColumnDefine(?Collection $options): \Closure
    {
        $filterList = [];

        $options->each(function (Collection $item, $k) use (&$filterList) {
            $collect      = array_column($item->toArray(), 'val', 'key');
            $filterList[] = function (&$target) use ($k, $collect) {
                if (isset($target[$k])) {
                    $target[$k] = $collect[$target[$k]] ?? '-';
                }
            };
        });

        return function (&$target) use ($filterList) {
            if (!empty($filterList)) {
                foreach ($filterList as $callback) {
                    if (isset($target["plan_id"]) && !$target["plan_id"]) {
                        $target["plan_name"] = "自然量";
                    }
                    $callback($target);
                }
            }
        };
    }

    /**
     * 返回拼接唯一组合键方法
     *
     * @param $groups
     *
     * @return \Closure
     */
    protected function addUnionKeyForRow($groups): \Closure
    {
        $indexUnion = array_fill_keys($groups, 0);

        return fn($target) => IndicatorsHelpers::flattenUnionKey($target, $indexUnion);
    }

}
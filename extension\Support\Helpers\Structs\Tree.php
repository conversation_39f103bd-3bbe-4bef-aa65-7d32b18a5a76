<?php

namespace app\extension\Support\Helpers\Structs;

class Tree
{
    private array $data;
    private array $indexData;

    private string $idField;
    private string $parentId;


    public function __construct(array $data, string $id = 'id', string $parent = 'parent_id')
    {
        $this->data      = $data;
        $this->idField   = $id;
        $this->parentId  = $parent;
        $this->indexData = $this->indexData();
    }

    /**
     * @param null          $parentId
     * @param \Closure|null $callbackFn
     *
     * @return array
     */
    public function buildTree($parentId = null,\Closure $callbackFn = null): array
    {
        $tree = [];
        foreach ($this->data as $item) {
            if ($item[$this->parentId] === $parentId) {
                $children = $this->buildTree($item[$this->idField]);
                if (!empty($children)) {
                    if ($callbackFn) {
                        $item['children'] = $callbackFn($item, $children);
                    }
                    else {
                        $item['children'] = $children;
                    }
                }
                $tree[] = $item;
            }
        }

        return $tree;
    }


    /**
     * @return array
     */
    private function indexData(): array
    {
        $indexData = [];

        foreach ($this->data as $item) {
            $indexData[$item[$this->idField]] = $item;
        }

        return $indexData;
    }
}
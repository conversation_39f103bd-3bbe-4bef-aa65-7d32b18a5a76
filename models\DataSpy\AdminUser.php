<?php

namespace app\models\DataSpy;

use Plus\MVC\Model\ActiveRecord;


/**
 * @AdminUser
 *           SPY系统用户表
 *
 * @property int    ID
 * @property string USER_NAME
 * @property string REAL_NAME
 * @property string PASSWORD
 * @property int    DEPARTMENT_ID
 * @property string GROUP_IDS
 * @property int    LEVEL
 * @property int    STATUS
 * @property int    SEX
 * @property string EMAIL
 * @property int    ARRANGE_ALLOW
 * @property int    ADMIN_DEPARTMENT
 * @property int    IS_SHOUMENG
 * @property int    ROI_POWER
 */
class AdminUser extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->dataspy;
        parent::__construct($data);
    }

    public function getId(): int
    {
        return $this->ID;
    }

}
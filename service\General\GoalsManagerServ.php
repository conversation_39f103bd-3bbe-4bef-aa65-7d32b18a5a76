<?php
// phpcs:disable
namespace app\service\General;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use Spiral\Database\Database;
use Spiral\Database\DatabaseInterface;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

/**
 * 目标管理服务
 */
class GoalsManagerServ
{
    public const GOAL_CATEGORY_INITIALLY = 0;

    public const GOAL_CATEGORY_ROLLING = 1;

    private const TB_GOALS_ROLLING_CP_GAME = 'ddc_platform.goals_progress_rolling_cpgame';

    private const TB_GOALS_ROLLING = 'ddc_platform.goals_progress_rolling';

    private const TB_GOALS_INITIALLY = 'ddc_platform.goals_progress_initially';

    private const TB_GOALS_TEAM = 'ddc_platform.goals_team_reach_daily';

    /**
     * 目标类型标识
     */
    public const GOALS_TYPE = [
        1 => '新用户',
        2 => '消耗金额',
        3 => '新用户付费金额',
        4 => '老用户付费金额',
        5 => '总付费目标',
    ];

    /**
     * @param array $params
     * @param int   $goalCategory
     *
     * @return array
     */
    public function listGoalsGroupYear(array $params, int $goalCategory = self::GOAL_CATEGORY_ROLLING): array
    {
        $db = $this->getConn();

        if ($goalCategory == self::GOAL_CATEGORY_INITIALLY) {
            $table = self::TB_GOALS_INITIALLY;
        } else {
            $table = self::TB_GOALS_ROLLING;
        }

        $qb = $db->table($table)->select();

        if (!empty($params['year'])) {
            $qb->where('a_year', $params['year']);
        }

        $columns = ['a_year', 'a_month', new Fragment("concat(a_year, '-', LPAD(a_month, 2, 0)) as mo")];

        foreach (static::GOALS_TYPE as $i => $ignoreFoo) {
            $columns[] = new Fragment(sprintf('SUM(IF(GOAL_TYPE = %d, goal_num, 0)) as goal_%d', $i, $i));
        }

        $qb
            ->columns($columns)
            ->groupBy('a_year')
            ->groupBy('a_month')
            ->orderBy('a_month', $qb::SORT_DESC);

        return $qb->fetchAll();
    }

    /**
     * @param array $params
     *
     * @return array
     * @throws \Exception
     */
    public function listGoalsGroupCpGame(array $params): array
    {
        $db    = $this->getConn();
        $table = self::TB_GOALS_ROLLING_CP_GAME;
        $qb    = $db
            ->select()
            ->from($table . ' as t1')
            ->leftJoin('base_conf_platform.tb_base_cp_game_conf', 't2')
            ->on('t1.cp_game_id', 't2.cp_game_id');

        if (!empty($params['month'])) {
            $month      = $params['month'];
            $dateObject = new \DateTime($month);

            $qb
                ->where('a_year', $dateObject->format('Y'))
                ->where('a_month', $dateObject->format('m'));
        }

        $columns = [
            'a_year', 'a_month', 't1.cp_game_id',
            new Fragment('group_concat(distinct cp_game_name) as cp_game_name'),
            new Fragment("concat(a_year, '-', LPAD(a_month, 2, 0)) as mo"),
        ];

        foreach (static::GOALS_TYPE as $i => $ignoreFoo) {
            $columns[] = new Fragment(sprintf('SUM(IF(GOAL_TYPE = %d, goal_num, 0)) as goal_%d', $i, $i));
        }

        $qb
            ->columns($columns)
            ->groupBy('a_year')
            ->groupBy('a_month')
            ->groupBy('t1.cp_game_id')
            ->orderBy([
                'a_month' => $qb::SORT_DESC,
                'goal_1'  => $qb::SORT_DESC,
            ]);

        return $qb->fetchAll();
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return array
     */
    public function listGoals(array $params = [], array $groups = [], array $columns = []): array
    {
        $mode  = (int)($params['mode'] ?? 1);
        $db    = $this->getConn();
        $table = $mode === 0
            ? self::TB_GOALS_INITIALLY
            : self::TB_GOALS_ROLLING;

        if (empty($columns)) {
            $columns = ['*'];
        }

        $qb = $db
            ->select()
            ->from($table);

        if (!empty($params['year'])) {
            $qb->where('a_year', $params['year']);
        }

        if (!empty($params['month'])) {
            $qb->where('a_month', $params['month']);
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        $qb->columns($columns);

        return $qb->fetchAll();
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     * @param array $sort
     *
     * @return array
     */
    public function listGoalsCpGame(
        array $params = [],
        array $groups = [],
        array $columns = [],
        array $sort = []
    ): array {
        $db    = $this->getConn();
        $table = static::TB_GOALS_ROLLING_CP_GAME;

        if (empty($columns)) {
            $columns = ['a_year', 'a_month', 't1.cp_game_id', 'goal_type', 'goal_num'];
        }

        $qb = $db
            ->select()
            ->from($table . ' as t1')
            ->leftJoin('base_conf_platform.tb_base_cp_game_conf', 't2')
            ->on('t1.cp_game_id', 't2.cp_game_id');

        if (!empty($params['year'])) {
            $qb->where('a_year', $params['year']);
        }

        if (!empty($params['month'])) {
            $qb->where('a_month', $params['month']);
        }

        if (!empty($params['cp_game_id'])) {
            $qb->where('t1.cp_game_id', new Parameter(Arr::wrap($params['cp_game_id'])));
        }

        if (!empty($params['goal_type'])) {
            $goalType = $params['goal_type'];

            if (is_array($goalType)) {
                $qb->where('goal_type', new Parameter($goalType));
            } else {
                $qb->where('goal_type', $goalType);
            }
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        $qb->columns($columns);

        return $qb->fetchAll();
    }

    /**
     * @param array $params
     *
     * @return array
     */
    public function listHaveGoalCpGameIds(array $params): array
    {
        $db      = $this->getConn();
        $table   = static::TB_GOALS_ROLLING_CP_GAME;
        $columns = [new Fragment('distinct cp_game_id')];

        $qb = $db->select()->from($table);
        $qb->where('cp_game_id', '!=', 0);
        $qb->columns($columns);

        if (!empty($params['year'])) {
            $qb->where('a_year', $params['year']);
        }

        if (!empty($params['month'])) {
            $qb->where('a_month', $params['month']);
        }

        $data = $qb->fetchAll();

        return array_column($data, 'cp_game_id');
    }


    /**
     * 覆盖目标数据
     *
     * @param     $tableData
     * @param int $type
     *
     * @return bool
     * @throws \Throwable
     */
    public function coverGoalsWithFile($tableData, int $type = self::GOAL_CATEGORY_ROLLING): bool
    {
        $data = [];

        foreach ($tableData as $item) {
            $goalTime = $item['月份'] ?? null;

            if (empty($goalTime)) {
                continue;
            }
            try {
                $goalTime = new \DateTime($goalTime);
            } catch (\Exception $e) {
                continue;
            }

            $t = [
                'a_year'  => $goalTime->format('Y'),
                'a_month' => $goalTime->format('m'),
            ];

            if ($type == self::GOAL_CATEGORY_ROLLING) {
                if (empty($item['目标游戏'])) {
                    throw new \InvalidArgumentException('缺少目标游戏');
                }

                $t['cp_game'] = $item['目标游戏'];
            } else {
                if (!empty($item['目标游戏'])) {
                    throw new \InvalidArgumentException("原始目标不能包含'目标游戏'");
                }
            }

            $goalArray = $this->changeGoalNum($item);

            foreach ($goalArray as $chill) {
                $data[] = array_merge($t, $chill);
            }
        }// end foreach()

        return $this->updateGoals($data, $type);
    }


    /**
     * @param     $data
     * @param int $type
     *
     * @return bool
     * @throws \Throwable
     */
    protected function updateGoals($data, int $type = self::GOAL_CATEGORY_ROLLING): bool
    {
        $conn       = $this->getConn();
        $operatorId = \Plus::$service->admin->getUserId();
        $resetTime  = [];
        foreach ($data as $item) {
            $resetTime[] = $item['a_year'] . '-' . $item['a_month'];
        }
        $resetTime = array_unique($resetTime);
        $this->resetGoals($resetTime, $type);

        $conn->transaction(function (Database $db) use ($data, $type, $operatorId) {
            // 查找目的数据涉及的月份
            foreach ($data as $chill) {
                if ($type == static::GOAL_CATEGORY_ROLLING) {
                    $cpGameName = $chill['cp_game'];

                    // cp_game转换cp_game_id
                    if ($cpGameName === '其他游戏合计') {
                        $cpGameId = 0;
                    } else {
                        $cpGameId = $this->getCpGameIdByName($db, $chill['cp_game']);
                        if (empty($cpGameId)) {
                            continue;
                        }
                    }

                    $chill['cp_game_id'] = $cpGameId;
                }

                $chill['operator_id'] = $operatorId;

                if ($type == static::GOAL_CATEGORY_ROLLING) {
                    $this->coverGoalRollingCpGame($db, $chill);
                } elseif ($type == static::GOAL_CATEGORY_INITIALLY) {
                    $this->coverGoalInitially($db, $chill);
                }
            }// end foreach()

            return true;
        });

        if ($type == self::GOAL_CATEGORY_ROLLING) {
            $newDb       = $this->getConn();
            $updateRange = array_map(fn($d) => ['a_year' => $d['a_year'], 'a_month' => $d['a_month']], $data);
            $updateRange = array_unique($updateRange, SORT_REGULAR);

            $this->updateRollingGoals($newDb, $updateRange);
        }

        return true;
    }

    /**
     * @param $db
     * @param $data
     *
     * @return mixed
     */
    protected function coverGoalRollingCpGame(&$db, $data)
    {
        $fixedFields = [
            'a_year'      => '',
            'a_month'     => '',
            'cp_game_id'  => '',
            'goal_type'   => '',
            'goal_num'    => '',
            'operator_id' => '',
        ];

        $data  = array_intersect_key($data, $fixedFields);
        $table = static::TB_GOALS_ROLLING_CP_GAME;

        return $this->updateAGoalCpGame($db, $data, $table);
    }

    /**
     * @param $db
     * @param $data
     *
     * @return mixed
     */
    protected function coverGoalInitially(&$db, $data)
    {
        $fixedFields = [
            'a_year'      => '',
            'a_month'     => '',
            'goal_type'   => '',
            'goal_num'    => '',
            'operator_id' => '',
        ];

        $data  = array_intersect_key($data, $fixedFields);
        $table = static::TB_GOALS_INITIALLY;

        return $this->updateAGoal($db, $data, $table);
    }

    /**
     * @param Database|DatabaseInterface $db
     * @param array                      $rangeTime
     *
     * @return void
     */
    protected function updateRollingGoals(&$db, array $rangeTime = [])
    {
        sleep(1);// 避免主从延迟
        foreach ($rangeTime as $foo) {
            $year  = $foo['a_year'] ?? null;
            $month = $foo['a_month'] ?? null;

            if (empty($year)) {
                continue;
            }

            $selectQb = $db->select()->from(static::TB_GOALS_ROLLING_CP_GAME);

            $selectQb
                ->where('a_year', $year);

            if (!empty($month)) {
                $selectQb->where('a_month', $month);
            }

            $selectQb
                ->groupBy('a_year')
                ->groupBy('a_month')
                ->groupBy('goal_type');

            $selectQb->columns([
                'a_year', 'a_month', 'goal_type',
                new Fragment('sum(goal_num) as goal_num'),
            ]);

            $info = $selectQb->fetchAll();

            if (empty($info)) {
                continue;
            }

            foreach ($info as $chill) {
                $chill['operator_id'] = \Plus::$service->admin->getUserId();
                $this->updateAGoal($db, $chill, static::TB_GOALS_ROLLING);
            }
        }// end foreach()
    }

    /**
     * @param Database|DatabaseInterface $db
     * @param                            $data
     * @param string                     $table
     * @param string[]                   $updateFields
     *
     * @return mixed
     */
    protected function updateAGoal(
        &$db,
        $data,
        string $table = self::TB_GOALS_ROLLING,
        array $updateFields = ['goal_num', 'operator_id']
    ) {
        $result = $db
            ->select()
            ->from($table)
            ->where([
                'a_year'    => $data['a_year'],
                'a_month'   => $data['a_month'],
                'goal_type' => $data['goal_type'],
            ])->columns('id')->fetchAll();

        if (empty($result)) {
            $id = $db->table($table)->insertOne($data);
        } else {
            $id       = $result[0]['id'];
            $updateQb = $db
                ->table($table)
                ->update();

            foreach ($updateFields as $field) {
                $updateQb->set($field, $data[$field]);
            }

            $updateQb->where('id', $id)->run();
        }

        return $id;
    }

    /**
     * @param        $db
     * @param        $data
     * @param string $table
     * @param array  $updateFields
     *
     * @return mixed
     */
    protected function updateAGoalCpGame(
        &$db,
        $data,
        string $table = self::TB_GOALS_ROLLING,
        array $updateFields = ['goal_num', 'operator_id']
    ) {
        $result = $db
            ->select()
            ->from($table)
            ->where([
                'a_year'     => $data['a_year'],
                'a_month'    => $data['a_month'],
                'cp_game_id' => $data['cp_game_id'],
                'goal_type'  => $data['goal_type'],
            ])->columns('id')->fetchAll();

        if (empty($result)) {
            $id = $db->table($table)->insertOne($data);
        } else {
            $id       = $result[0]['id'];
            $updateQb = $db
                ->table($table)
                ->update();

            foreach ($updateFields as $field) {
                $updateQb->set($field, $data[$field]);
            }

            $updateQb->where('id', $id)->run();
        }

        return $id;
    }


    /**
     * @param Database $db
     * @param          $cpGameName
     *
     * @return int
     */
    protected function getCpGameIdByName(Database &$db, $cpGameName): int
    {
        $qb = $db->table('base_conf_platform.tb_base_cp_game_conf');
        return
            (int)($qb->select()
                      ->where(['cp_game_name' => $cpGameName])
                      ->columns(['cp_game_id'])->fetchAll()[0]['cp_game_id'] ?? 0);
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
//        return FakeDB::connection('dataspy_local');
    }

    /**
     * 格式化成二维数组
     *
     * @param $data
     *
     * @return array
     */
    protected function changeGoalNum($data): array
    {
        $result = [];

        foreach (static::GOALS_TYPE as $i => $field) {
            if (!isset($data[$field])) {
                continue;
            }

            $num = (float)$data[$field];

            if (!is_numeric($num)
                || $num < 0
            ) {
                throw new \InvalidArgumentException("格式错误, 不允许导入");
            };

            $chill    = ['goal_type' => $i, 'goal_num' => trim($data[$field])];
            $result[] = $chill;
        }

        return $result;
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function listReachNumForTeam(
        array $params,
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): array {
        $db = $this->getConn();
        $qb = $db->select()->from(static::TB_GOALS_TEAM);

        $dateDimension = $params['range_date_dimension'];

        if ($dateDimension == 3) {
            if (in_array('tday', $columns)) {
                unset($columns[array_search('tday', $columns)]);
                $columns = array_merge($columns, [
                    new Fragment('FROM_DAYS(TO_DAYS(`tday`) - MOD(TO_DAYS(`tday`) - 7, 7)) as my_week'),
                    new Fragment('MIN(tday) as start_day'),
                    new Fragment('MAX(tday) as end_day'),
                ]);
            }
        } elseif ($dateDimension == 4) {
            unset($columns[array_search('tday', $columns)]);
            $columns = array_merge($columns, [new Fragment("DATE_FORMAT(tday, '%Y-%m') as my_month")]);
        }

        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            $rangeStart = $params['range_date_start'];
            $rangeEnd   = $params['range_date_end'];

            $qb->where('tday', 'between', $rangeStart, $rangeEnd);
        }

        if (!empty($params['goal_type'])) {
            $qb->where('goal_type', $params['goal_type']);
        }

        if (!empty($params['cp_game_id'])) {
            $qb->where('cp_game_id', $params['cp_game_id']);
        }

        if (!empty($params['cp_game_id[!]'])) {
            $qb->where('cp_game_id', 'not in', new Parameter($params['cp_game_id[!]']));
        }

        $qb->columns($columns);

        if (!empty($sort)) {
            $qb->orderBy(Arr::wrap($sort));
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        return $qb->fetchAll();
    }

    /**
     *
     * @param $resetTime
     * @param $type
     *
     * @return void
     */
    protected function resetGoals($resetTime, $type)
    {
        if ($type == static::GOAL_CATEGORY_INITIALLY) {
            $tables = [static::TB_GOALS_INITIALLY];
        } else {
            $tables = [static::TB_GOALS_ROLLING_CP_GAME, static::TB_GOALS_ROLLING];
        }

        $db = $this->getConn();

        foreach ($resetTime as $ym) {
            [$year, $month] = explode('-', $ym);

            foreach ($tables as $table) {
                $qb = $db->table($table)->delete();
                $qb
                    ->where('a_year', $year)
                    ->where('a_month', $month);
                $qb->run();
            }
        }
    }
}

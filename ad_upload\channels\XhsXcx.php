<?php

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

/**
 * 小红书上报
 * https://doc.weixin.qq.com/doc/w3_ALkAXAY3ABMCNOseyZM3vTMO9d8ts?scode=AP0ADgeoAAk4lIcGpMAAoAQgY3ABM
 *
 * php cli.php -f ad_upload/AdUpload.php -s debug --p 'channel_id=8721 actions=active force=1'
 * <AUTHOR>
 * phpcs:disable
 */
class XhsXcx extends AdBaseInterface
{

    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'active');
    }

    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'pay');
    }

    private function uploadData($info, $type = 'active')
    {
        //查询广告id
        $sql       = "SELECT click_str FROM bigdata_dwd.dwd_sdk_activate_log_sv_game 
                 WHERE channel_id='{$info['CHANNEL_ID']}' and oaid='{$info['OAID']}'";
        $clickInfo = \Plus::$app->doris_entrance->query($sql)->fetchColumn();
        if (!$clickInfo) {
            \Plus::$app->log->alert($info, [], self::LOG_DIR);
            return;
        }
        $ext        = json_decode($clickInfo, true);

        $ext        = json_decode($ext['ext'], true);
        $account_id = $ext['origin_json']["advertiser_id"] ?? '';
        $clickId    = $ext['origin_json']["oaid"] ?? '';

        if ($account_id == '' || $clickId == '') {
            \Plus::$app->log->alert($info, [], self::LOG_DIR);
            return;
        }

        $params = [
            //'platform'          => 'manual',
            'timestamp'         => time() * 1000,
            'scene'             => '800', //固定为 800， 代表小程序
            'advertiser_id'     => $account_id,
            'mini_program_type' => '2', //1-红书小程序； 2-微信小游戏； 3-微信小程序
            //'caid1_md5'         => '8425EA6D7B37D4FDCAC5D1EF50D828F4',
            'click_id'          => $clickId,
            'event_type'        => $type == 'active' ? '411' : '404',
        ];
        if ($type == 'pay') {
            $params['context'] = [
                'properties' =>
                    [
                        'pay_amount' => intval($info['MONEY'] * 100),
                    ],
            ];
        }

        $url  = 'https://adapi.xiaohongshu.com/api/open/conversion';
        $http = new Http($url);
        $res  = $http->postJson($params);

        \Plus::$app->log->info(['req'=>$params, 'res'=>$res], [], self::LOG_DIR);

        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'xhs_xcx';
        $logInfo['log_type']     = 'reported_platform_log';
        $logInfo['request']      = json_encode(['url' => $url, 'params' => $params]);
        $logInfo['response']     = $res;
        //记录上报结果
        $resArr = json_decode($res, true);
        if (isset($resArr['code']) && $resArr['code'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
            if (!empty($info['paid_report_log'])) {
                $info['paid_report_log']['no_reported_origin'] = '接口返回编码异常';
            }
        }
        $this->log($info, $logInfo, $res, $url);
    }

    public function uploadRegister($info, $ext = [])
    {
        // TODO: Implement uploadRegister() method.
    }

    public function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }


    public function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

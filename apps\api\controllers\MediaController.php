<?php

namespace app\apps\api\controllers;

use app\extension\Exception\ParameterException;
use app\models\baseConfPlatform\TbOathConf;
use app\models\DataSpy\TbCpsSettlement;
use app\models\DdcPlatform\DwsSvkeyAdBaseDaily;
use app\service\Admin;
use app\util\Common;
use Plus\MVC\Controller\JsonController;
use Plus\Util\Pagination;

/**
 * Class Media
 * <AUTHOR>
 */
class MediaController extends JsonController
{

    /**
     * 视频数据
     * @return array
     * @throws ParameterException
     */
    public function videoAction(): array
    {
        return $this->success([], false);

//        $videoId = $this->getValue("video_id");
//
//        if (!$videoId) {
//            throw new ParameterException("请填写批创视频id！");
//        }
//
//        $startTime = $this->getValue('start_time', null);
//        $endTime   = $this->getValue('end_time', null);
//
//        if (!empty($startTime)) {
//            $startTime .= ' 00:00:00';
//        }
//
//        if (!empty($endTime)) {
//            $endTime   .= ' 23:59:59';
//        }
//
//        $wheres = [
//            "IF(mc_video_id=0, demo_video_id, mc_video_id)= '{$videoId}'",
//        ];
//
//        if (!empty($startTime) && !empty($endTime)) {
//            $wheres[] = "publish_time between '{$startTime}' and '{$endTime}' ";
//        }
//
//        $whereString = implode(' and ', $wheres);
//
//        $sql = "SELECT DATE(publish_time) as tday,video.*,task.media_platform_id,mc_video_id from dwd_media_video video
//        join media_publish_tasks task on video.TASK_ID=task.id
//        join media_demo_video demo on task.video_num=demo.video_num
//        where {$whereString} group by video.ID";
//
//        $data = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
//
//        foreach ($data as &$item) {
//            unset($item["ID"]);
//            unset($item["UPDATE_TIME"]);
//            $item = array_change_key_case($item, CASE_LOWER);
//
//            foreach ($item as $k => &$value) {
//                if ($k == 'account_id') {
//                    $value = (string)$value;
//                }
//
//                if (in_array($k, ['media_platform_id', 'task_id', 'mc_video_id'])) {
//                    $value = intval($value);
//                }
//
//                if ($k == 'video_duration') {
//                    $value = floatval($value);
//                }
//
//                if (strstr($k, '_count')) {
//                    $value = intval($value);
//                }
//                elseif (strstr($k, '_rate')) {
//                    $value = floatval($value);
//                }
//
//                if (strstr($k, 'avg_')) {
//                    $value = floatval($value);
//                }
//            }
//
//        }
//
//        return $this->success($data, false);
    }


}

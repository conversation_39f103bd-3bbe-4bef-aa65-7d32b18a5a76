<?php
/**
 * 热云数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class <PERSON><PERSON> extends AdBaseInterface
{
    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $appId       = $info['EXT']['ry_app_id'];
        $packageId   = $info['PACKAGE_ID'];
        $deviceIdKey = '_imei';
        $deviceId    = $info['DEVICE_ID'];
        if (substr($packageId, -2, 2) == '99') {
            $deviceIdKey = '_idfa';
            if ($deviceId=='********-0000-0000-0000-************') {
                $deviceId = $info["DEVICE_KEY"];
            }
        }


        //上报激活
        $data = [
            "appid" => $appId,
            "context" => [
                "_deviceid" => $deviceId,
                "_campaignid" => '',
                $deviceIdKey => $info['DEVICE_ID'],
                "_ip" => $info['IP'],
                "_ipv6"=>"",
                "_tz" => "+8",
                "_model"=> $info["DEVICE_TYPE"],
                "_manufacturer" => '',
                "_ryos" => $info['OS'] == '1' ? 'Android' : 'ios',
                "_ryosversion" => $info['OS_VERSION'],
                "_rydevicetype" => '',
                "_carrier" => '',
                "_network"=>'',
                "_network_type"=>'',
                "_resolution" => '',
                "_pkgname"=>"",
                "_op" => '',
            ],
        ];
        if ($info['OS'] == '1') {
            $data['context']['_androidid'] = $info['ANDROID_ID'];
            $data['context']['_oaid']      = $info['OAID'];
            $data['context']['_mac']       = "";
        } else {
            $data['context']['_ua'] = $info["USERAGENT"];
        }
        $url = 'http://log.reyun.com/receive/tkio/install';
        $this->uploadData($url, $data, $info, 'active');

        //上报启动
        $data = [
            "appid" => $appId,
            "context" => [
                "_deviceid" => $deviceId,
                $deviceIdKey => $info['DEVICE_ID'],
                "_ip" => $info['IP'],
                "_tz" => "+8",
                "_ipv6"=>'',
                "_ryos" => $info['OS'] == '1' ? 'Android' : 'ios',
                "_pkgname" => '',
            ],
        ];
        if ($info['OS'] == '1') {
            $data['context']['_androidid'] = $info['ANDROID_ID'];
            $data['context']['_oaid']      = $info['OAID'];
            $data['context']['_mac']       = "";
        }
        $url = 'http://log.reyun.com/receive/tkio/startup';
        $this->uploadData($url, $data, $info, 'active');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $appId       = $info['EXT']['ry_app_id'];
        $packageId   = $info['PACKAGE_ID'];
        $deviceIdKey = '_imei';
        $deviceId    = $info['DEVICE_ID'];
        if (substr($packageId, -2, 2) == '99') {
            $deviceIdKey = '_idfa';
            if ($deviceId=='********-0000-0000-0000-************') {
                $deviceId = $info["DEVICE_KEY"];
            }
        }
        $data = [
            "appid" => $appId,
            "who" => $info['CORE_ACCOUNT'],
            "context" => [
                "_deviceid" => $deviceId,
                $deviceIdKey => $info['DEVICE_ID'],
                "_idfv" => $info['DEVICE_ID'],
                "_ip" => $info['IP'],
                "_tz" => "+8",
                "_ipv6"=>'',
                "_ryos" => $info['OS'] == '1' ? 'Android' : 'ios',
                "_pkgname" => '',
                "_model"=> $info["DEVICE_TYPE"],
            ],
        ];
        if ($info['OS'] == '1') {
            $data['context']['_androidid'] = $info['ANDROID_ID'];
            $data['context']['_oaid']      = $info['OAID'];
            $data['context']['_mac']       = null;
        }
        $url = 'http://log.reyun.com/receive/tkio/register';
        $this->uploadData($url, $data, $info, 'register');
    }

    /**
     * 上报登录
     * @param array $info
     * @param array $ext
     */
    public function uploadLogin($info, $ext = [])
    {
        $appId       = $info['EXT']['ry_app_id'];
        $packageId   = $info['PACKAGE_ID'];
        $deviceIdKey = '_imei';
        $deviceId    = $info['DEVICE_ID'];
        if (substr($packageId, -2, 2) == '99') {
            $deviceIdKey = '_idfa';
            if ($deviceId=='********-0000-0000-0000-************') {
                $deviceId = $info["DEVICE_KEY"];
            }
        }
        $data = [
            "appid" => $appId,
            "who" => $info['CORE_ACCOUNT'],
            "context" => [
                "_deviceid" => $deviceId,
                $deviceIdKey => $info['DEVICE_ID'],
                "_ip" => $info['IP'],
                "_tz" => "+8",
                "_ipv6"=>'',
                "_ryos" => $info['OS'] == '1' ? 'Android' : 'ios',
                "_pkgname" => '',
                "_model"=> $info["DEVICE_TYPE"],
            ],
        ];
        if ($info['OS'] == '1') {
            $data['context']['_androidid'] = $info['ANDROID_ID'];
            $data['context']['_oaid']      = $info['OAID'];
            $data['context']['_mac']       = null;
        }
        $url = 'http://log.reyun.com/receive/tkio/loggedin';
        $this->uploadData($url, $data, $info, 'login');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $appId     = $info['EXT']['ry_app_id'];
        $packageId = $info['PACKAGE_ID'];
        $deviceId  = $info['DEVICE_ID'];
        if (substr($packageId, -2, 2) == '99') {
            $paymentType = 'apple';
            $deviceIdKey = '_idfa';
            if ($deviceId=='********-0000-0000-0000-************') {
                $deviceId = $info["DEVICE_KEY"];
            }
        } else {
            $paymentType = 'android';
            $deviceIdKey = '_imei';
        }
        $data = [
            'appid' => $appId,
            'who' => $info['CORE_ACCOUNT'],
            'context' => [
                '_deviceid' => $deviceId,
                '_transactionid' => $info['ORDER_ID'],
                '_paymenttype' => $paymentType,
                '_currencytype' => "RMB",
                '_currencyamount' => $info['MONEY'],
                $deviceIdKey => $info['DEVICE_ID'],
                '_ip' => isset($info['IP']) ? $info['IP'] : '',
                "_ipv6"=>'',
                "_ryos" => $info['OS'] == '1' ? 'Android' : 'ios',
                "_pkgname" => '',
                "_model"=> "",
            ],
        ];
        if (substr($packageId, -2, 2) != '99') {
            $data['context']['_androidid'] = $info['ANDROID_ID'];
            $data['context']['_oaid']      = $info['OAID'];
            $data['context']['_mac']       = null;
        }
        $url = 'http://log.reyun.com/receive/tkio/payment';
        $this->uploadData($url, $data, $info, 'pay');
    }

    /**
     * 公共上报
     * @param string $url  url
     * @param array  $data 上报数据
     * @param array  $info 查询数据
     * @param string $type 上报类型
     * @return void
     * @throws \Exception
     */
    private function uploadData($url, $data, $info, $type)
    {
        $http = new Http($url);
        $res  = $http->postJson($data);
        //记录上报结果
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'reyum';
        $logInfo['request']      = json_encode(['url' => $url, 'data' => $data]);
        $logInfo['response']     = $res;
        $resContent              = json_decode($res, true);

        if ($resContent['status'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        //写日志
        $this->log($info, $logInfo, $res, $url);
    }



    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

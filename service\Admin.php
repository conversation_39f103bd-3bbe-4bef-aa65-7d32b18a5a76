<?php


namespace app\service;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\models\baseConfPlatform\TbBaseChannelConf;
use app\models\baseConfPlatform\TbBaseGameConf;
use app\models\baseConfPlatform\TbPackageDetailConf;
use app\models\DataSpy\AdminUser;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Plus\MVC\Request\Request;
use Plus\Util\StringUtil;

/**
 * Class Admin Service
 * <AUTHOR>
 */
class Admin extends \Plus\Service\Children
{
    /**
     * 用户名
     * @var string
     */
    private $username = "";

    /**
     * 用户ID
     * @var int
     */
    private int $userId = -1;

    /**
     *  token data 扩展字段
     */
    private $data = "";

    /**
     * 用户特殊权限sql
     * @var string
     */
    private $adminPowerSql = "";

    /**
     * 游戏、渠道权限
     * @var array
     */
    private $cpGameIds      = [];
    private $gameIds        = [];
    private $channelMainIds = [];
    private $channelIds     = [];

    /**
     * 权限接口对象
     * @var Auth|mixed|null
     */
    public $authApi = NULL;

    public function __construct()
    {
        $this->authApi = \Plus::$service->auth;
    }

    /**
     * generate to token
     * @param $username
     * @return string
     */
    public static function generateAccessToken($data)
    {
        $jwtConfig = \Plus\Internal\Config::get('JwtToken');
        $jwtIss    = $jwtConfig['iss'];
        $jwtKey    = $jwtConfig['key'];
        $jwtAlg    = $jwtConfig['alg'];
        $nowTime   = strtotime(date('Y-m-d H:i:s'));
        $exp       = $jwtConfig['exp'] * 24 * 3600;
        $expTime   = strtotime(date('Y-m-d H:i:s', time() + $exp));
        $payload   = [
            "iss"  => $jwtIss,
            "aud"  => $data["username"],
            "iat"  => $nowTime,
            "nbf"  => $nowTime,
            "exp"  => $expTime,
            "data" => $data,
        ];
        return JWT::encode($payload, $jwtKey, $jwtAlg);
    }

    /**
     * parse token
     * @param $token
     * @return array
     */
    public function parseAccessToken($token)
    {
        $jwtConfig   = \Plus\Internal\Config::get('JwtToken');
        $jwtKey      = $jwtConfig['key'];
        $jwtAlg      = $jwtConfig['alg'];
        JWT::$leeway = 60;//当前时间减去60，把时间留点余地
        $key         = new Key($jwtKey, $jwtAlg);
        return JWT::decode($token, $key);
    }

    /**
     * check auth
     * @param $username
     * @param $path
     * @throws \Exception
     */
    public function checkAuth($path)
    {
        if (!$this->isSuperAdmin()) {

            //判断接口权限
            $params   = ["userList" => [$this->username], "type" => ["API"]];
            $authList = $this->authApi->getUserAuthorizeList($params);
            $authList = array_column($authList["data"][$this->username], "data");
            if (!in_array($path, $authList)) {
                throw new \Exception('没有权限操作', 404);
            }
        }
    }

    /**
     * 是否超级管理员
     * @return mixed
     */
    public function isSuperAdmin()
    {
        return isset($this->data->is_admin) ? $this->data->is_admin : 0;
    }

    /**
     * 特殊权限
     * @param Request $request
     * @throws \Exception
     */
    public function specAuthByUser(Request &$request, $userType = "")
    {
        //开放接口 给予全部数据权限
        [$gameIds, $channelMainIds, $cpGameIds, $packageIds] = [[], [], [], []];
        if ($userType != "api") {
            [$gameIds, $channelMainIds, $cpGameIds, $packageIds] = $this->getPrivilegedIdsByUser();
        }


        //构建权限sql
        $this->buildAdminPowerSql($request, $gameIds, $channelMainIds, $cpGameIds, $packageIds);

        //获取权限下拉筛选 （游戏，主渠道）
        if ($packageIds) {
            $_list = (new TbPackageDetailConf())->getGameChannelForPackages($packageIds);
            if (in_array(99999, $cpGameIds)) {
                $cpGameIds = $_list["cp_game_ids"];
            }
            if (in_array(99999, $gameIds)) {
                $gameIds = $_list["game_ids"];
            }
            if (!$channelMainIds) {
                $channelMainIds = $_list["channel_main_ids"];
            }
            else {
                if (!empty($channelMainIds)) {
                    $channelMainIds = array_merge($channelMainIds, $_list["channel_main_ids"]);
                }
            }
        }

        //查找子渠道
        $channelIds = [];
        if ($channelMainIds) {
            $channelIds = (new TbBaseChannelConf())->getAllChannel(["CHANNEL_MAIN_ID" => $channelMainIds]);
        }

        $this->gameIds        = $gameIds;
        $this->cpGameIds      = $cpGameIds;
        $this->channelMainIds = $channelMainIds;
        $this->channelIds     = $channelIds;
    }

    /**
     * 获取用户权限数据
     * @return array
     */
    private function getPrivilegedIdsByUser()
    {
        $cpGameIds      = [];
        $gameIds        = [];
        $channelMainIds = [];
        $packageIds     = [];

        if (!$this->isSuperAdmin()) {
            //查询游戏权限
            $params   = ["userList" => [$this->username]];
            $authList = $this->authApi->otherAuthorizeList($params);
            $authList = $authList["data"][$this->username];

            foreach ($authList as $key => $item) {
                $data = in_array(0, $item) ? "all" : $item;
                switch ($key) {
                    case "cpGames":
                        $cpGameIds = $data;
                        break;
                    case "games":
                        $gameIds = $data;
                        break;
                    case "mainChannel":
                        $channelMainIds = $data;
                        break;
                    case "pkg":
                        $packageIds = $data;
                        break;
                }
            }

            $gameModel     = new TbBaseGameConf();
            $cpGameIdsInit = $cpGameIds;
            if ($cpGameIds == "all" || $gameIds == "all") {
                $cpGameIds = [];
            }
            else {
                $cpGameIds = array_unique(array_filter(explode(',', implode(',', $cpGameIds))));
                //非超级管理员并且没游戏原名权限时，设置一个不存在的游戏ID
                $cpGameIds = !empty($cpGameIds) ? $cpGameIds : [99999];
            }

            if ($gameIds == "all" || $cpGameIdsInit == "all") {
                $gameIds = [];
            }
            else {
                $gameIds = array_unique(array_filter(explode(',', implode(',', $gameIds))));
                $gameIds = empty($gameIds) ? 0 : $gameIds;
                $gameIds = $gameModel->getAllGameIds([
                    "OR" => [
                        "GAME_ID"    => $gameIds,
                        "CP_GAME_ID" => $cpGameIds,
                    ],
                ]);
                //非超级管理员并且没游戏权限时，设置一个不存在的游戏ID
                $gameIds = !empty($gameIds) ? $gameIds : [99999];
            }

            //主渠道
            if ($channelMainIds == "all" || !$packageIds) {
                $channelMainIds = [];
            }
            else {
                $channelMainIds = array_unique(array_filter(explode(',', implode(',', $channelMainIds))));
                $channelMainIds = !empty($channelMainIds) ? $channelMainIds : [];
            }

            //包号
            if ($packageIds == "all" || !$packageIds) {
                $packageIds = [];
            }
            else {
                $packageIds = array_unique(array_filter($packageIds));
                $packageIds = !empty($packageIds) ? $packageIds : [];
            }
        }

        return [$gameIds, $channelMainIds, $cpGameIds, $packageIds];
    }


    /**
     * 构建权限关联sql
     * @param Request $request
     * @param         $gameIds
     * @param         $channelMainIds
     * @param         $cpGameIds
     * @param         $packageIds
     */
    private function buildAdminPowerSql(Request $request, $gameIds, $channelMainIds, $cpGameIds, $packageIds)
    {
        //请求参数
        $where = " 1=1 ";

        //下拉筛选条件
        $this->fiterRequestParams($where, $request, "package_id");
//        $this->fiterRequestParams($where, $request, "promotion_id", "POPULARIZE_V1_ID");
//        $this->fiterRequestParams($where, $request, "channel_main_id");
//        $this->fiterRequestParams($where, $request, "channel_id");
//        $this->fiterRequestParams($where, $request, "department_id", "AD_DEPARTMENT_ID");
//        $this->fiterRequestParams($where, $request, "user_id", "AD_USER_ID");
        $this->fiterRequestParams($where, $request, "platform_id");
        $this->fiterRequestParams($where, $request, "platform_id");
        $this->fiterRequestParams($where, $request, "cp_game_id");
        $this->fiterRequestParams($where, $request, "game_id");
        $this->fiterRequestParams($where, $request, "app_show_id");

        //游戏、渠道、包权限
        $specWhere = " 1=1 ";
        if (!$this->isSuperAdmin()) {
            $channelMainWhere = "";
            if ($channelMainIds) {
                $channelMainWhere = " AND CHANNEL_MAIN_ID IN (" . implode($channelMainIds) . ")";
            }

            //游戏权限
            if ($gameIds && $cpGameIds) {
                $specWhere .= " AND ((CP_GAME_ID IN (" . implode(",", $cpGameIds) . ") OR GAME_ID IN (" . implode(",", $gameIds) . ")) $channelMainWhere)";
            }
            else if ($gameIds || $cpGameIds) {
                if ($gameIds) {
                    $specWhere .= " AND (GAME_ID IN (" . implode(",", $gameIds) . ") $channelMainWhere )";
                }
                if ($cpGameIds) {
                    $specWhere .= " AND (CP_GAME_ID IN (" . implode(",", $cpGameIds) . ") $channelMainWhere )";
                }
            }
            else {
                $specWhere .= $channelMainWhere;
            }

            //包号权限
            if ($packageIds) {
                $specWhere .= " OR PACKAGE_ID IN (" . implode(",", $packageIds) . ")";
            }
        }

        $where      .= " AND (" . $specWhere . ")";
        $forceIndex = "";
        if (strpos($where, "package_id") !== false) {
            $forceIndex = "FORCE INDEX(UK_P)";
        }

        $sql                 = "( SELECT * FROM base_conf_platform.tb_package_detail_conf {$forceIndex} WHERE $where ) POWER";
        $this->adminPowerSql = $sql;
    }

    /**
     * 获取权限关联sql
     * @return string
     */
    public function getAdminPowerSql(): string
    {
        return $this->adminPowerSql;
    }

    /**
     * 获取权限下拉
     * @return array
     */
    public function getAdminPowerOption(): array
    {
        return [
            "cpGameIds"      => $this->cpGameIds,          //游戏原名
            "gameIds"        => $this->gameIds,            //游戏统计名
            "channelMainIds" => $this->channelMainIds,     //主渠道
            "channelIds"     => $this->channelIds,         //子渠道
        ];
    }

    /**
     * 获取菜单字段权限
     */
    public function getFiledsbyReport()
    {
        if ($this->isSuperAdmin()) {
            return [];
        }
        $router           = \Plus::$app->router;
        $app              = StringUtil::unCamelize($router->getApp(), '-');
        $controller       = StringUtil::unCamelize($router->getController(), '-');
        $action           = StringUtil::unCamelize($router->getAction(), '-');
        $path             = implode('/', [$app, $controller, $action]);
        $reportFieldsList = \Plus::$app->redis->get($this->username . ":menu_fields");
        if (!$reportFieldsList) {
            $reportFieldsList = $this->authApi->getFields();
        }
        else {
            $reportFieldsList = json_decode($reportFieldsList, true);
        }
        return isset($reportFieldsList[$path]) ? $reportFieldsList[$path]["columns"] : [];
    }

    /**
     * 参数处理
     * @param $where
     * @param $param
     */
    private function fiterRequestParams(&$where, Request $request, $key, $field = "")
    {
        $param = $request->getValue($key);

        if (is_string($param)) {
            if (str_contains($param, ',')) {
                $param = \explode(',', $param);
            }

            $param = Arr::wrap($param);
        }

        if (!empty($param)) {
            $param = array_filter($param);
        }

        if (!empty($param)) { //包号
            $param = "'" . implode("','", $param) . "'";
            $where .= " AND " . ($field ? $field : $key) . " in ({$param})";
        }
    }

    /**
     * set username
     * @param $username
     */
    public function setUserData($data)
    {
        $this->data     = $data;
        $this->username = $data->username;
    }

    /**
     * get username
     * @return string
     */
    public function getUsername()
    {
        return $this->username;
    }

    /**
     * 获取用户ID
     * @return int
     */
    public function getUserId(): int
    {
        $userType = $this->data->type ?? '';
        if ($this->userId < 0 && $userType != 'api') {
            $this->userId = AdminUser::getInstance()->find(['user_name' => $this->username])->asArray()->getId();
        }

        return $this->userId;
    }

    /**
     *
     * 获取权限控制
     *
     * @return mixed|string
     */
    public function powerSubSQL()
    {
        static $sql;

        if (!$sql) {

            if ($this->isSuperAdmin()) {
                $sql = ' base_conf_platform.tb_package_detail_conf ';
            }
            else {
                [$gameIds, $channelMainIds, $cpGameIds, $packageIds] = $this->getPrivilegedIdsByUser();

                $wheres      = [];
                $shouldWhere = [];

                if (!empty($cpGameIds)) {
                    $shouldWhere[] = " cp_game_id in (" . implode(",", $cpGameIds) . ") ";
                }

                if (!empty($gameIds)) {
                    $shouldWhere[] = " game_id in (" . implode(",", $gameIds) . ") ";
                }

                if (!empty($packageIds)) {
                    $shouldWhere[] = " package_id in (" . implode(",", $packageIds) . ") ";
                }

                if (!empty($channelMainIds)) {
                    $wheres[] = " channel_main_id in (" . implode(",", $channelMainIds) . ") ";
                }

                if (!empty($shouldWhere)) {
                    $shouldWhere = implode(' or ', $shouldWhere);
                    $wheres[]    = " ( {$shouldWhere} ) ";
                }

                if (empty($wheres)) {
                    $sql = ' base_conf_platform.tb_package_detail_conf ';
                    goto result;
                }

                $whereString = implode(' and ', $wheres);
                $sql         = "( select distinct cp_game_id, game_id, package_id, channel_main_id, channel_id, popularize_v2_id, platform_id,ad_user_id,ad_department_id from base_conf_platform.tb_package_detail_conf where {$whereString} )";
            }

        }

        result:
        return $sql;
    }

    /**
     * 判断是不是外部用户
     *
     * @return bool
     * @throws \RedisException
     */
    public function isOutsiders(): bool
    {
        $userInfo = $this->getUserInfo();
        $userType = $userInfo['user_type'] ?? 0;

        return $userType == 2 || $userType == 0;
    }

    /**
     * @return array|mixed
     * @throws \RedisException
     */
    public function getUserInfo()
    {
        static $userInfo;
        // 对应redis配置前缀
        $storeSuffix = 'spy';

        if (!$userInfo) {
            $cache = \Plus::$app->redis->get($this->getUsername());
            if (!$cache) {
                $response = $this->authApi->getAllUsersList(['username' => $this->getUsername()]);

                if (empty($response['data'])) {
                    $userInfo = [];
                }
                else {
                    $userInfoList = $response['data'];
                    $userInfo     = [];

                    foreach ($userInfoList as $item) {
                        $uu = $item['username'] ?? '';
                        if ($uu == $this->username) {
                            $userInfo = $item;
                            break;
                        }
                    }

                    \Plus::$app->redis->setex(
                        $storeSuffix . $this->getUsername(),
                        TimeUtil::getSecondsUntilTomorrow(),
                        json_encode($userInfo, JSON_UNESCAPED_UNICODE)
                    );
                }
            }
            else {
                $userInfo = json_decode($cache, true);
            }
        }

        return $userInfo;
    }

}

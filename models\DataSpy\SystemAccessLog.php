<?php

namespace app\models\DataSpy;

use Plus\MVC\Model\ActiveRecord;


/**
 * @AdminUser
 * SPY2.0系统访问日志表
 *
 * @property int    ID
 * @property string USER_NAME
 * @property string URI
 * @property string IP
 * @property int    SPENT_TIME
 * @property int SPENT_MEM
 * @property string REMARK
 * @property string TYPE
 * @property string ADD_TIME
 */
class SystemAccessLog extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->dataspy;
        parent::__construct($data);
    }

    public function getId(): int
    {
        return $this->ID;
    }

}
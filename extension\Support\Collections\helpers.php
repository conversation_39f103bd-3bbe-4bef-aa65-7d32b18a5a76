<?php


use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;

if (!function_exists('data_get')) {
    /**
     *
     * @param $target
     * @param $key
     * @param $default
     * @return mixed
     */
    function data_get($target, $key, $default = null)
    {
        if (is_null($key)) {
            return $target;
        }

        $key = is_array($key) ? $key : \explode('.', $key);

        foreach ($key as $i => $segment) {
            unset($key[$i]);

            if (is_null($segment)) {
                return $target;
            }

            if ($segment === '*') {
                if ($target instanceof Collection) {
                    $target = $target->all();
                }
                elseif (!is_iterable($target)) {
                    return value($default);
                }

                $result = [];

                foreach ($target as $item) {
                    $result[] = data_get($item, $key);
                }

                return in_array('*', $key) ? Arr::collapse($result) : $result;
            }

            if (Arr::accessible($target) && Arr::exists($target, $segment)) {
                $target = $target[$segment];
            }
            elseif (is_object($target) && isset($target->{$segment})) {
                $target = $target->{$segment};
            }
            else {
                return value($default);
            }

        }

        return $target;
    }
}

if (!function_exists('value')) {

    /**
     *
     * @param mixed $value
     * @param       ...$args
     * @return mixed
     */
    function value($value, ...$args)
    {
        return $value instanceof \Closure ? $value(...$args) : $value;
    }
}

if (!function_exists('collect')) {
    /**
     * @param $value
     * @return Collection
     */
    function collect($value = null): Collection
    {
        return new Collection($value);
    }
}

if (!function_exists('str_contains')) {
    /**
     * PHP8 自带
     *
     * @param $haystack
     * @param $needle
     * @return bool
     */
    function str_contains($haystack, $needle): bool
    {
        return $needle !== '' && \mb_strpos($haystack, $needle) !== false;
    }
}


if (! function_exists('head')) {
    /**
     * Get the first element of an array. Useful for method chaining.
     *
     * @param array $array
     * @return mixed
     */
    function head(array $array)
    {
        return reset($array);
    }
}
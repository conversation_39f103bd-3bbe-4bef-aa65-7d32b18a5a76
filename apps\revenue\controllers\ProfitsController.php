<?php

namespace app\apps\revenue\controllers;

use app\service\Revenue\ProfitsServ;
use Plus\MVC\Controller\JsonController;

class ProfitsController extends JsonController
{

    /**
     * 付费里类型映射
     *
     */
    const PAY_TYPE_MAP = [
        1  => '手盟充值',
        2  => '应用宝充值',
        3  => 'IOS手盟充值',
        4  => 'IOS充值',
        5  => '外放联运充值',
        6  => '平台币充值',
        7  => '应用宝充值(4:6)',
        8  => '应用宝充值(8:92)',
        9  => '第三方渠道充值',
        10 => '第三方渠道充值',
        11 => '微信广点通(手盟包)',
    ];


    /**
     * 分成查询接口
     *
     * @return array
     */
    public function dataAction(): array
    {
        $infoRe = (new ProfitsServ())->fetchRow(['version' => 0]);

        foreach ($infoRe as &$item) {
            foreach ($item as $k => &$f) {
                if ($this->isChangeFloatFields($k)) {
                    $f = (float)$f;
                }
            }

            $item['package_id'] = &$item['ext'];
        }

        return $this->success(['list' => $infoRe]);
    }

    /**
     * 分成配置更新
     *
     * @return array
     * @throws \Throwable
     */
    public function updateAction(): array
    {
        $request = \Plus::$app->request;
        $data    = \json_decode($request->getRawBody(), true);

        if (empty($data) && json_last_error()) {
            return $this->error(json_last_error_msg());
        }
        elseif (empty($data)) {
            return $this->error('no data');
        }

        $result = (new ProfitsServ())->updateProfits($data);

        return $this->success(['result' => 'success']);
    }

    /**
     * 判断是否需要转float类型
     *
     * @param $field
     *
     * @return bool
     */
    private function isChangeFloatFields($field): bool
    {
        return in_array($field, [
            'cp_devide',
            'channel_devide',
            'aisle_devide',
            'shoumeng_devide',
            'pay_type',
        ]);
    }

}
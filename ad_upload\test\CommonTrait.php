<?php

namespace app\ad_upload\test;

use app\ad_upload\tool\CustomProcess;
use Ifsnop\Mysqldump\Mysqldump;
use ReflectionClass;

/**
 * 同步线上数据库到测试环境
 * <AUTHOR>
 */
trait CommonTrait
{

    /**
     * 运行测试
     * @param string $id        id
     * @param string $action    action
     * @param string $timeBegin 开始时间
     * @param string $timeEnd   结束时间
     * @return void
     */
    public function runJob($id, $action, $timeBegin, $timeEnd)
    {
        \Plus::$app->dataspy2->update(
            'tb_ad_upload_log',
            ['last_action_id' => $id],
            ['channel_id' => $this->channel_id, 'action' => $action]
        );

        $exe = "php cli.php -f ad_upload/AdUpload.php -s debug --p 'channel_id={$this->channel_id} actions={$action}'";
        echo '3. 已处理上报点，运行命令：' . $exe . "\n";
        sleep(3);
        passthru($exe);
        $sql   = "select action_id from bigdata_dwd.dwd_reported_platform_log ";
        $where = "where time >= '$timeBegin' and time <= '$timeEnd' and channel_id = {$this->channel_id} and action = '$action'";
        $rs1   = \Plus::$app->doris_entrance->query($sql . $where)->fetchAll(\PDO::FETCH_ASSOC);
        $rs2   = \Plus::$app->doris_entrance2->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $rs1 = array_column($rs1, 'action_id');
        $rs2 = array_column($rs2, 'action_id');

        echo '正式数据库数量：' . count($rs1) . "\n";
        echo '测试数据库数量：' . count($rs2) . "\n";
        if (count($rs1) != count($rs2)) {
            echo '数据不一致，请检查！', PHP_EOL;
            $diff1 = array_diff($rs1, $rs2);
            $diff2 = array_diff($rs2, $rs1);
            echo '正式数据库多：' . implode(',', $diff1) . "\n";
            echo '测试数据库多：' . implode(',', $diff2) . "\n";
        } else {
            echo 'ok数据一致！', PHP_EOL;
        }
    }

    /**
     * 删除hash缓存
     * @param string $key key
     * @return void
     */
    public function delCache($key)
    {
        $data = \Plus::$app->redis82->hkeys($key);
        foreach ($data as $v) {
            \Plus::$app->redis82->hdel($key, $v);
        }
    }

    /**
     * 同步配置
     * @return void
     * @throws \Exception
     */
    public function syncConfig()
    {
        $config       = include ROOT . 'config/dbDev.php';
        $dbSourceConf = $config['dataspy'][1];
        $dbTargetConf = $config['dataspy'][0];

        echo '1导入数据库中..', PHP_EOL;
        $dbSource = $this->getDb($dbSourceConf, 'dataspy', ['tb_ad_upload_log', 'tb_ad_data_upload_conf']);
        $dbSource->start('./dump.sql');
        $dbTarget = $this->getDb($dbTargetConf, 'dataspy', []);
        $dbTarget->restore('./dump.sql');

        $dbSourceConf2 = $config['base_conf_platform'][1];
        $dbTargetConf2 = $config['base_conf_platform'][0];

        echo '2导入数据库中..', PHP_EOL;
        $dbSource2 = $this->getDb($dbSourceConf2, 'base_conf_platform', ['biz_tags', 'tb_base_channel_conf']);
        $dbSource2->start('./dump2.sql');
        $dbTarget2 = $this->getDb($dbTargetConf2, 'base_conf_platform', []);
        $dbTarget2->restore('./dump2.sql');


        echo '清空doris日志', PHP_EOL;
        \Plus::$app->doris_entrance2->exec('TRUNCATE TABLE bigdata_dwd.dwd_reported_platform_log');
        \Plus::$app->doris_entrance2->exec('TRUNCATE TABLE bigdata_dwd.dwd_reported_paid_platform_log');
        echo 'ok', PHP_EOL;
        //\Plus::$app->dataspy2->update('tb_ad_upload_log', ['unmatched_ids' => null]);
    }


    /**
     * 获取mysqldump对象
     * @param array  $config       配置
     * @param string $dbName       数据库
     * @param array  $tables       数据表
     * @param bool   $addDropTable 是否添加drop table
     * @return Mysqldump
     * @throws \Exception
     */
    public function getDb($config, $dbName, $tables, $addDropTable = true)
    {
        $username = $config['username'];
        $passport = $config['password'];
        $dsn      = "mysql:host={$config['server']};port={$config['port']};dbname=$dbName";
        $settings = [
            'include-tables' => $tables,
            'lock-tables'    => false,
            'skip-triggers'  => true,
            'add-drop-table' => $addDropTable,
        ];
        return new Mysqldump($dsn, $username, $passport, $settings);
    }
}

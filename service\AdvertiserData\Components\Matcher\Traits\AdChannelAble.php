<?php

namespace app\service\AdvertiserData\Components\Matcher\Traits;

use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Injection\Expression;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

trait AdChannelAble
{
    /**
     * 推广子渠道匹配搜索(得要优先配好映射关系)
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchChannelId(&$qb, array &$params)
    {
        if (empty($params['channel_id'])) return;

        $field = $this->getReflectField('channel_id');
        $data  = $params['channel_id'];

        QueryBuilderHelper::priorityOrderBuild($qb, $field, $data);
    }

    /**
     * 推广主渠道搜索匹配(得要优先配好映射关系)
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchChannelMainId(&$qb, array &$params)
    {
        if (empty($params['channel_main_id'])) return;

        $field = $this->getReflectField('channel_main_id');
        $data  = $params['channel_main_id'];

        QueryBuilderHelper::priorityOrderBuild($qb, $field, $data);
    }
}
<?php

namespace app\ad_upload\strategies;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\contract\AdUploadStrategyInterface;

/**
 * 登录数据上报 策略
 * <AUTHOR>
 */
class LoginUploadStrategy extends AdUploadStrategyInterface
{
    /**
     * 上报类型
     * @var string
     */
    public $action = AdBaseInterface::ACTION_LOGIN;

    /**
     * 最大id的数据
     * @var array
     */
    private $maxlastData = [];

    /**
     * 初始化上报点
     * @return void
     */
    public function initUploadLast()
    {
        parent::initUploadLast();
        $sql               = 'select max(id) as ID from tb_sdk_user_login';
        $this->maxlastData = \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
        //排除已过期的未归因匹配id
        // 数量大于100 才进行排除，减少查询次数
        if (count($this->unmatchedIds) < 100) {
            return;
        }
        $before             = date('Y-m-d H:i:s', strtotime($this->unmatchedTime));
        $data               = \Plus::$app->origin_platform->select('tb_sdk_user_login', 'id', [
            'id' => $this->unmatchedIds,
            'TIME[>]' => $before,
        ]);
        $this->unmatchedIds = $data;
        if (empty($data)) {
            $this->unmatchedIds = [0];
        }
    }
    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    public function getData($uploadConfig, $packages): array
    {
        //获取补漏上报ID
        $unmatchedIdString = implode(',', $this->unmatchedIds);

        if (empty($packages) || $this->lastId <= 0) {
            return [];
        }
        //查询登录数据
        $condition = " b.CHANNEL_ID={$this->channelId} AND a.PACKAGE_ID IN ({$packages})";

        if (!empty($this->timeBegin) && !empty($this->timeEnd)) {
            $condition .= " AND a.TIME BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'";
        } else {
            $condition .= " AND ( (a.ID > {$this->lastId} AND a.OAID != '') or a.ID IN ({$unmatchedIdString}) )";
        }

        $sql = "SELECT 
                    a.ID,a.PACKAGE_ID,
                    a.DEVICE_KEY,a.DEVICE_TYPE,
                    a.ANDROID_ID,a.DEVICE_CODE,
                    a.OAID,a.DEVICE_ID,a.MD5_DEVICE_ID,
                    a.GAME_ID,a.OS,a.CORE_ACCOUNT,
                    a.IP,b.CHANNEL_ID,
                    b.CLICK_ID,'{$this->action}' AS TYPE,
                    b.SV_KEY
                    FROM tb_sdk_user_login a join tb_sdk_user_newlogin_package b using(PACKAGE_ID,CORE_ACCOUNT) 
                    WHERE {$condition}
                    ORDER BY a.ID";
        return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 设置如果上一步getData 获取数据为空，设置最大上报id
     * 避免长期没有数据上报的渠道，查询过多数据
     * @return void
     */
    public function setMaxLastId():void
    {
        $this->setLastId($this->maxlastData);
    }

    /**
     * 过滤数据
     * @param array $data         data
     * @param array $uploadConfig config
     * @return array
     */
    public function filterData($data, $uploadConfig): array
    {
        $rs = parent::filterData($data, $uploadConfig);
        //每个用户在一个包的登录行为一天只上报一次
        foreach ($rs as $k => $v) {
            $key     = $v['CHANNEL_ID'] . '_' . $v['PACKAGE_ID'] . '_' . $v['CORE_ACCOUNT'];
            $cache   = \Plus::$app->redis82->hget('ad_data_upload_login', $key);
            $nowDate = date('Y-m-d');
            if ($cache != $nowDate) {
                \Plus::$app->redis82->hset('ad_data_upload_login', $key, $nowDate);
            } else {
                \Plus::$app->log->info('upload_login_登录过滤上报_'.($v['ID']), [], AdBaseInterface::LOG_DIR);
                //已经上报过，删除
                unset($rs[$k]);
            }
        }
        return $rs;
    }
}

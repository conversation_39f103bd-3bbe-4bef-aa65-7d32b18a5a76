<?php

namespace app\service\ConfigService\Tables;

use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\Mather;
use app\service\ConfigService\Constants\FieldTag;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class AdCreativePayRemain implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    /**
     * @param $options
     * @return Collection
     * @throws \Exception
     */
    public function getFields($options = null): Collection
    {
        $retainType = $options['retain_type'] ?? 1;

        $today  = new \DateTime();
        $NNodes = array_merge(
            range(1, 29), [44, 59], Mather::findNumInScope(59, 719, 30)
        );

        $collect = $this->getBaseFields([
            'baseCollect', 'AdAccountBaseCollect', 'campaignBaseCollect', 'planBaseCollect', 'creativeBaseCollect',
        ]);

        if ($retainType == 2)
            $collect = $collect->merge(['paid_retain_user' => ['title' => '付费新用户(7天内)', 'sorter' => 'true']]);
        else
            $collect = $collect->merge(['paid_retain_user' => ['title' => '付费新用户', 'sorter' => 'true']]);

        $collect = $collect->merge([
            'remain_days' => ['title' => '留存统计天数',],
            'remain_1000' => ['title' => '当前付费留存率',],
        ]);

        // $nDays      = days_apart($today, $options['range_date_start'] ?? $today);
        $nDays      = 720 - 1;
        $remainList = $this->remainNCollect(Mather::findIn($nDays, $NNodes), '付费');
        $remainList = FieldTag::tagClassifyToNField($remainList, 'remain',
            [
                ['range' => [1, 29], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_1]],
                ['range' => [44, 179], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_2]],
                ['range' => [209, 359], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_3]],
                ['range' => [389, 719], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_4]],
            ]
        );

        $collect = $collect->merge($remainList);

        return $this->formatStandard($collect);
    }
}
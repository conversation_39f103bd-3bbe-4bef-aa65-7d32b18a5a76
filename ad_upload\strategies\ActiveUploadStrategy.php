<?php

namespace app\ad_upload\strategies;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\contract\AdUploadStrategyInterface;

/**
 * 激活数据上报 策略
 * <AUTHOR>
 */
class ActiveUploadStrategy extends AdUploadStrategyInterface
{
    /**
     * 上报类型
     * @var string
     */
    public $action = AdBaseInterface::ACTION_ACTIVE;

    /**
     * 最大id的数据
     * @var array
     */
    private $maxlastData = [];


    /**
     * 初始化上报点
     * @return void
     */
    public function initUploadLast()
    {
        parent::initUploadLast();
        $sql               = 'select max(id) as ID from tb_sdk_active_log';
        $this->maxlastData = \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
        //排除已过期的未归因匹配id
        // 数量大于100 才进行排除，减少查询次数
        if (count($this->unmatchedIds) < 100) {
            return;
        }
        $before             = date('Y-m-d H:i:s', strtotime($this->unmatchedTime));
        $data               = \Plus::$app->origin_platform->select('tb_sdk_active_log', 'id', [
            'id' => $this->unmatchedIds,
            'TIME[>]' => $before
        ]);
        $this->unmatchedIds = $data;
        if (empty($data)) {
            $this->unmatchedIds = [0];
        }
    }

    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    public function getData($uploadConfig, $packages): array
    {
        //获取补漏上报ID
        $unmatchedIdString = implode(',', $this->unmatchedIds);
        if (empty($packages) || $this->lastId <= 0) {
            return [];
        }
        //查询激活数据：可根据时间段或者最大ID查询
        $condition = "CHANNEL_ID={$this->channelId} AND PACKAGE_ID IN ({$packages})";
        if (!empty($this->timeBegin) && !empty($this->timeEnd)) {
            $condition .= " AND TIME BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'";
        } else {
            $condition .= " AND ((ID > {$this->lastId} ) OR (ID IN ($unmatchedIdString)))";
        }
        $sql = "SELECT ID,PACKAGE_ID,DEVICE_TYPE,DEVICE_KEY,SCREEN_WIDTH,SCREEN_HEIGHT,DEVICE_CODE,OAID,
                           DEVICE_ID,MD5_DEVICE_ID,GAME_ID,OS,OS_VERSION,IP,USERAGENT,TIME_SERVER,ANDROID_ID,
                           CHANNEL_ID,SV_KEY,CLICK_ID,'{$this->action}' AS TYPE
                    FROM tb_sdk_active_log 
                    WHERE {$condition}
                    GROUP BY MD5_DEVICE_ID,PACKAGE_ID,ANDROID_ID,IP
                    ORDER BY ID";
        return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 设置如果上一步getData 获取数据为空，设置最大上报id
     * 避免长期没有数据上报的渠道，查询过多数据
     * @return void
     */
    public function setMaxLastId():void
    {
        $this->setLastId($this->maxlastData);
    }
}

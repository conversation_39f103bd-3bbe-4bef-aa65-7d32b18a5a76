<?php
/**
 * 网易有道数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class Wyyd extends AdBaseInterface
{

    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG');
    }


    /**
     * 公共上报方法
     * @param $info
     * @param $type
     * @return void
     */
    private function upload($info, $type)
    {
        //回调地址
        $callbackUrl = $info["CALLBACK_URL"];
        $arr         = explode("&", $callbackUrl);
        $callbackUrl = $arr[0]."&".$arr[2];
        $arr         = explode("=", $arr[1]);
        $os          = $arr[1];

        switch ($type) {
            case 'ACTIVE':
                if ($os == 'IOS') {
                    $callbackUrl .= "ios_activate";
                } else {
                    $callbackUrl .= "android_activate";
                }
                $typeName = '激活';
                break;
            case 'REG':
                if ($os == 'IOS') {
                    $callbackUrl .= "ios_register";
                } else {
                    $callbackUrl .= "android_register";
                }
                $typeName = '注册';
                break;
        }

        $http = new Http($callbackUrl);
        $res  = $http->get();

        //记录上报结果
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'wyyd';
        $logInfo['request']      = json_encode(['url' => $callbackUrl,]);
        $logInfo['response']     = $res;
        $resContent              = json_decode($res, true);

        if ($resContent['code'] == 'success') {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        $this->log($info, $logInfo, $res, $callbackUrl);
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadPay($info, $ext = [])
    {
        // TODO: Implement uploadPay() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

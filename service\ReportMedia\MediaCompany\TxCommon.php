<?php

namespace app\service\ReportMedia\MediaCompany;

use app\extension\Support\Helpers\Zakia;
use app\service\ReportMedia\Contract\ReportMediaContract;
use Plus\Net\Http;

class TxCommon implements ReportMediaContract
{
    private const EVENT_MAP = [
        'active'   => 'ACTIVATE_APP',
        'register' => 'REGISTER',
        'pay'      => 'PURCHASE',
    ];

    /**
     * @return array
     */
    public function report(): array
    {
        if (func_num_args() === 4) {
            [$callbackUri, $info, $event, $num] = func_get_args();
            $info['MONEY'] = $num;
        }
        else {
            [$callbackUri, $info, $event] = func_get_args();
        }

        $event = static::EVENT_MAP[$event] ?? '';

        return $this->reportRequest($callbackUri, $info, $event);
    }

    /**
     * @param string $callbackUri
     * @param array  $info
     * @param string $reportType
     *
     * @return array
     */
    private function reportRequest(string $callbackUri, array $info, string $reportType = 'ACTIVATE_APP'): array
    {
        $money = isset($info['MONEY']) ? $info['MONEY'] * 100 : 0;
        $imei  = $idfa = $androidId = '';

        if (Zakia::checkPackageIsIOS($info['PACKAGE_ID'])) {
            $idfa = $info['MD5_DEVICE_ID'] ?? '';
        }
        else {
            $imei      = $info['MD5_DEVICE_ID'] ?? '';
            $androidId = \md5($info['ANDROID_ID'] ?? '');
        }

        $reportData = [
            'actions' => [
                'outer_action_id' => $info['ID'] . $reportType, // 可根据此去重，防止重复上报
                'action_time'     => \time(),
                'user_id'         => [
                    'hash_imei'       => $imei,
                    'hash_idfa'       => $idfa,
                    'hash_android_id' => $androidId,
                ],
                'action_type'     => $reportType,
                'action_param'    => [
                    'value' => $money,
                ],
            ],
        ];

        try {
            echo '上报连接::' . $callbackUri . PHP_EOL;
            echo '上报参数::' . \json_encode($reportData) . PHP_EOL;
            $request = new Http($callbackUri);

            return [
                'request_uri' => $callbackUri,
                'prams'       => \json_encode($reportData),
                'response'    => $request->postJson($reportData),
            ];
        }
        catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            echo $e->getTraceAsString() . PHP_EOL;

            return [
                'request_uri' => $callbackUri,
                'prams'       => \json_encode($reportData),
                'message'     => $e->getMessage(),
            ];
        }
    }
}
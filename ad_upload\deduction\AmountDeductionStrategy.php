<?php

namespace app\ad_upload\deduction;

use app\ad_upload\contract\DeductionStrategyInterface;

/**
 * 按金额扣量
 * <AUTHOR>
 */
class AmountDeductionStrategy extends DeductionStrategyInterface
{
    /**
     * 返回是否通过扣量检查
     * @return bool
     * @throws \Exception
     */
    public function isPass(): bool
    {
        if ($this->config['kind'] != 2) {
            return false;
        }
        $conf = $this->config['config_data'];
        $data = $this->data;

        $reportRules = $conf['rule'] ?? [];
        $payMoney    = $data['MONEY'];

        foreach ($reportRules as $rule) {
            $rangeAmount   = $rule['amount'];
            $randAmountNum = $rule['report_amount_list'] ?? [];

            if (!($payMoney >= $rangeAmount[0] && $payMoney <= $rangeAmount[1])) {
                continue;
            }

            if (!empty($randAmountNum)) {
                $reportMoney          = $randAmountNum[array_rand($randAmountNum)];
                $data['MONEY_REPORT'] = $reportMoney;
                // 转换上报金额
                $data['MONEY_ACTUALY'] = $data['MONEY'];
                $data['MONEY']         = $data['MONEY_REPORT'];
                // 上报日志
                $data['paid_report_log']['reported_money']         = $data['MONEY_REPORT'];
                $data['paid_report_log']['reported_behavior']      = 2;
                $data['paid_report_log']['reported_rule_id']       = $this->config['id'] ?? 0;
                $data['paid_report_log']['reported_behavior_rule'] = json_encode($conf, JSON_UNESCAPED_UNICODE);
                $this->data                                        = $data;
                return true;
            }
        }
        return false;
    }


}
<?php

namespace app\service\OperationData\Scheme;

use app\extension\Support\Macroable\Traits\Macroable;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Traits\JoinFixAble;
use app\service\AdvertiserData\Traits\OperableQuery;
use app\service\AdvertiserData\Traits\Schemer;
use Aura\SqlQuery\QueryFactory;

class FirstLoginLtvDailyScheme implements SchemeContract
{
    use Macroable, Schemer, JoinFixAble, OperableQuery;

    protected array $joinTables = [];

    protected QueryFactory $queryFactory;

    protected $query;

    public const MAIN_TABLE = [
        'table' => 'ddc_platform.dws_firstlogin_ltv_daily',
        'alias' => 't_ltv',
    ];

    public static function NewOne(): self
    {
        return new FirstLoginLtvDailyScheme();
    }

    public function __construct()
    {
        $this->queryFactory = new QueryFactory('mysql');
    }

    public function __clone()
    {
        $this->queryFactory = clone $this->queryFactory;
        $this->query        = clone $this->query;
    }

    /**
     *
     * @return array
     */
    public function fieldReflect(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];

        return [
            'tday'            => $mainTable,
            'cp_game_id'      => $mainTable,
            'game_id'         => $mainTable,
            'package_id'      => $mainTable,
            'app_show_id'     => 'POWER',
            'channel_main_id' => 'POWER',
            'channel_id'      => 'POWER',
            'platform_id'     => 'POWER',
            'promotion_id'    => 'POWER.popularize_v2_id',
        ];
    }

    /**
     * 固定关联的表
     * @return array
     */
    protected function fixedTables(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];
        return [];
    }
}
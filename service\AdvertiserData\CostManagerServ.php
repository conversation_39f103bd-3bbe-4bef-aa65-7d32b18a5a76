<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\Zakia;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use http\Exception\RuntimeException;
use Spiral\Database\Database;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;
use Spiral\Database\Table;

class CostManagerServ
{
    const MODE_ALL = 3;
    const MODE_SUMMARY = 2;
    const MODE_LIST = 1;

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $mode
     *
     * @return array
     */
    public function fetchCostInfoForLive(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $result    = [];
        $allMode   = static::MODE_ALL | static::MODE_SUMMARY | static::MODE_LIST;
        $paramMode = $allMode & $mode;
        $db        = $this->getConn();
        $powerSql  = str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql());

        $qb = $db
            ->select()
            ->from(TableCollect::DWD_AD_LIVE_COST . ' as t_base')
            ->leftJoin(new Fragment($powerSql), 'power')
            ->on(['t_base.package_id' => 'power.package_id'])
            ->leftJoin('dataspy.admin_user', 'admin_user')
            ->on(['t_base.operator_id' => 'admin_user.id']);

        // 条件匹配
        if (!empty($params['range_date_start']) || !empty($params['range_date_end'])) {
            $rangeDate = array_filter([
                $params['range_date_start'] ?? null,
                $params['range_date_end'] ?? null,
            ]);

            sort($rangeDate);

            $qb->where('t_base.start_time', 'between', $rangeDate[0] . ' 00:00:00', $rangeDate[1] . ' 23:59:59');
        }

        if (!empty($params['ad_account'])) {
            $adAccount = $params['ad_account'];

            if (str_contains(',', $adAccount)) {
                $adAccount = \explode(',', $adAccount);
            }

            $adAccount = Arr::wrap($adAccount);

            $qb->where(function (SelectQuery $select) use ($adAccount) {
                $data = new Parameter($adAccount);
                $select
                    ->where('ad_account_id', 'IN', $data)
                    ->orWhere('ad_account_name', 'IN', $data);
            });
        }

        if (!empty($params['package_id'])) {
            $packages = $params['package_id'];
            if (is_string($packages) && str_contains(',', $packages)) {
                $packages = \explode(',', $packages);
            }

            $packages = Arr::wrap($packages);
            QueryBuilderHelper::baseBuild($qb, 't_base.package_id', $packages);
        }

        if (!empty($params['cost_type'])) {
            $costType = $params['cost_type'];

            if (is_string($costType) && str_contains(',', $costType)) {
                $costType = \explode(',', $costType);
            }

            QueryBuilderHelper::baseBuild($qb, 'cost_type', $costType);
        }

        if ($paramMode & static::MODE_LIST) {
            $infoQb = clone $qb;
            $infoQb->columns([
                't_base.id as id',
                new Fragment('DATE(t_base.start_time) as tday'),
                new Fragment("CONCAT(DATE_FORMAT(t_base.start_time, '%H:%i:%s') , '~' , DATE_FORMAT(t_base.end_time, '%H:%i:%s')) AS time_range"),
                'ad_account_id as ad_account_id',
                'ad_account_name as ad_account_name',
                't_base.package_id as package_id',
                'cost_type as cost_type',
                'cost as cost',
                'cost_discount as cost_discount',
                'admin_user.real_name as operator',
                't_base.create_time as create_time',
                'power.game_id as game_id',
                'power.channel_id as channel_id',
            ]);

            $notHavePageQb = clone $infoQb;

            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;
                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }

            if (!empty($sort)) {
                $infoQb->orderBy($sort);
            }

            $result['list']  = $infoQb->fetchAll();
            $chillDb         = $this->getConn();
            $result['total'] = $chillDb
                ->select()
                ->from(new Fragment('(' . (clone $notHavePageQb)->__toString() . ') as totalBody'))
                ->count();
        }

        if ($paramMode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            // something to do ...
        }

        return $result;
    }

    /**
     * @param array $data
     *
     * @return int|string|null
     * @throws \Exception
     */
    public function saveCostInfoForLive(array $data)
    {
        $keyMap = array_flip([
            'start_time'      => '开始时间',
            'end_time'        => '结束时间',
            'ad_account_id'   => '账号ID',
            'ad_account_name' => '账号',
            'package_id'      => '包号',
            'cost_type'       => '成本类型',
            'cost'            => '消费金额',
            'cost_discount'   => '返点后消耗金额',
        ]);

        $costTypeMap = array_flip($this->getCostTypeMap());
        $operator    = \Plus::$service->admin->getUserId();
        $insertData  = [];

        foreach ($data as $item) {
            $t = [];

            foreach ($item as $k => $foo) {
                $key = $keyMap[$k] ?? null;
                if (is_null($key)) continue;

                if ($key === 'tday') {
                    try {
                        $data = (new \DateTime($foo))->format('Y-m-d');
                    }
                    catch (\Exception $e) {
                        throw new \InvalidArgumentException("日期错误");
                    }
                }
                elseif ($key === 'start_time' || $key === 'end_time') {
                    $data = Zakia::convertChinesePunctuationToEnglish($foo);
                }
                elseif ($key === 'cost_type') {
                    $data = $costTypeMap[$foo] ?? null;

                    if (is_null($data)) {
                        throw new \InvalidArgumentException('不支持该成本类型:' . $foo);
                    }
                }
                elseif ($key === 'package_id') {
                    $pattern = '/[\'^£$%&*()}{@#~?><>,|=_+¬-]/';

                    if (preg_match($pattern, $foo)) {
                        throw new \InvalidArgumentException('包号格式错误');
                    }
                    $data = $foo;
                }
                elseif ($key === 'ad_account_id') {
                    $pattern = '/[\'^£$%&*()}{@#~?><>,|=_+¬-]/';

                    if (preg_match($pattern, $foo)) {
                        throw new \InvalidArgumentException('账号ID格式错误');
                    }
                    $data = $foo;
                }
                else {
                    $data = trim($foo);
                }

                $t[$key] = $data;
            }

            if (!empty($t)) {
                if (!empty($t['start_time']) && !empty($t['end_time'])) {
                    try {
                        $start = new \DateTime($t['start_time']);
                        $end   = new \DateTime($t['end_time']);
                    }
                    catch (\Exception $e) {
                        throw new \InvalidArgumentException('非正常日期格式');
                    }

                    $t['start_time'] = $start->format('Y-m-d H:i:s');
                    $t['end_time']   = $end->format('Y-m-d H:i:s');
                }

                $t['operator_id'] = $operator;
                $insertData[]     = $t;
            }
        }

        $insertCols = [
            'start_time', 'end_time', 'ad_account_id', 'ad_account_name',
            'package_id', 'cost_type', 'cost', 'cost_discount', 'operator_id',
        ];

        return $this->multiSave($insertData, $insertCols, TableCollect::DWD_AD_LIVE_COST);
    }

    /**
     * @return array
     */
    public function getCostTypeMap(): array
    {
        $db = $this->getConn();
        $qb = $db->select()->from(TableCollect::AD_LIVE_COST_TYPE);
        $qb->columns(['id', 'type_name']);

        return array_column($qb->fetchAll(), 'type_name', 'id');
    }

    /**
     * @throws \Throwable
     */
    public function delForLiveByIds(array $ids)
    {
        $db = $this->getConn();
        return $db->transaction(function (Database $db) use ($ids) {
            $qb = $db->delete()->from(TableCollect::DWD_AD_LIVE_COST);
            $qb->where('id', new Parameter($ids));

            return $qb->run();
        });
    }


    /**
     * @param array $list
     * @param array $columns
     * @param string $table
     *
     * @return int|string|null
     */
    protected function multiSave(array $list, array $columns, string $table)
    {
        $db = $this->getConn();
        $qb = $db->table($table)->insert();

        $qb->columns($columns);
        $fillCols = array_fill_keys($columns, '');

        foreach ($list as $item) {
            $qb->values(array_merge($fillCols, array_intersect_key($item, $fillCols)));
        }

        $onUpdate = [];
        foreach ($columns as $chill) {
            $onUpdate[] = "{$chill}=values({$chill})";
        }

        $onUpdateString = ' on duplicate key update ' . implode(',', $onUpdate);
        $sqlString      = $qb->__toString() . $onUpdateString;

        return $db->execute($sqlString);
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }


}
<?php

namespace app\service\OperationData\Components\MatchParams;

use app\service\AdvertiserData\Components\Helpers\Convert;
use app\service\AdvertiserData\Components\MatchParams\Contracts\MatcherContract;
use app\service\AdvertiserData\Components\MatchParams\Matcher;
use app\service\AdvertiserData\Components\MatchParams\Traits\CampaignMatch;
use app\service\AdvertiserData\Components\MatchParams\Traits\CreativeMatch;
use app\service\AdvertiserData\Components\MatchParams\Traits\GeneralMatch;
use app\service\AdvertiserData\Components\MatchParams\Traits\GroupConvertable;
use app\service\AdvertiserData\Components\MatchParams\Traits\Matchable;
use app\service\AdvertiserData\Components\MatchParams\Traits\PlanMatch;
use app\service\AdvertiserData\Contracts\GroupConvertContract;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\General\Matcher\Traits\TagsMatcher;
use Aura\SqlQuery\Common\SelectInterface;

class FirstLoginLtvMatcher extends Matcher implements MatcherContract, GroupConvertContract
{
    use Matchable, GeneralMatch, PlanMatch, CampaignMatch, CreativeMatch, GroupConvertable, TagsMatcher;

    public function __construct($reflectMap = [])
    {
        if (!empty($reflectMap)) {
            $this->setReflectMap($reflectMap);
        }
    }

    public function setParams($params): FirstLoginLtvMatcher
    {
        $this->processLine($params);

        return $this;
    }

    /**
     * @param SchemeContract $scheme
     *
     * @return void
     */
    public function execute(SchemeContract &$scheme)
    {
        $wheres = array_unique(array_filter($this->wheres));

        $scheme->scope(function (&$query) use ($wheres) {
            if (!$query instanceof SelectInterface) return;

            foreach ($wheres as $where) {
                $query->where($where);
            }
        });
    }

    private function processLine($params)
    {
        $line = [
            [$this, 'matchTDay'],
            [$this, 'matchThour'],
            [$this, 'matchCpGames'],
            [$this, 'matchGames'],
            [$this, 'matchAppShowIds'],
            [$this, 'matchChannelMainId'],
            [$this, 'matchChannelId'],
            [$this, 'matchPackageTags'],
            [$this, 'matchGameIdTags'],
            [$this, 'matchChannelTags'],
            [$this, 'matchChannelMainTags'],
            [$this, 'matchPlatformId'],
            [$this, 'matchPackageId'],
            [$this, 'matchPromotionId'],
            [$this, 'matchPromotionChannel'],
            [$this, 'matchDepartmentId'],
            [$this, 'matchUserId'],
            [$this, 'matchHasNoCost'],
            [$this, 'lessPayDate']
        ];

        foreach ($line as $callback) {
            if (is_callable($callback)) {
                $this->wheres[] = call_user_func_array($callback, [$params]);
            }
        }
    }

    /**
     * 是否包含无消费
     *
     * @param $params
     *
     * @return string
     */
    protected function matchHasNoCost($params): string
    {
        $hasNoCost = $params['has_no_cost'] ?? 1;

        if ((int)($hasNoCost = $params['has_no_cost'] ?? 1) === 0) {
            $field = $this->getField('cost');

            return "{$field} > 0";
        }

        return '';
    }

    /**
     * @param $params
     *
     * @return string
     */
    protected function matchDepartmentId($params): string
    {
        if (empty($params['department_id'])) return '';

        $departmentIds = Convert::convertInString($params['department_id']);

        return "POWER.ad_department_id IN ({$departmentIds})";
    }

    /**
     * @param $params
     *
     * @return string
     */
    protected function matchUserId($params): string
    {
        if (!isset($params['user_id'])) return '';

        $userId  = $params['user_id'] ?: 0;
        $userIds = Convert::convertInString($userId);

        return "POWER.ad_user_id IN ({$userIds})";
    }

    /**
     * @param $params
     * @return string
     */
    protected function matchChannelId($params): string
    {
        if (!isset($params['channel_id'])) return '';

        $data  = Convert::convertInString($params['channel_id']);
        $field = $this->getField('channel_id');

        return "{$field} IN ({$data})";
    }

    /**
     * @param $params
     * @return string
     */
    protected function lessPayDate($params): string
    {
        if (!isset($params['less_pay_date'])) return '';

        $data  = $params['less_pay_date'];
        $today = date("Y-m-d");

        return "pay_date <= '{$data}' and pay_date != '{$today}'";
    }
}
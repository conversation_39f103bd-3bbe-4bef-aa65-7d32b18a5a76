{include file="sql/advertise/ad_dashboard/creative_dash_alpha.tpl"}

{assign var="dash_col" value=[
'show_cnt','click_cnt','download_cnt','activate_cnt','convert_cnt','install_cnt','lp_view','lp_download','download_start',
'register','cost','cost_discount','new_real_user','new_user','new_user_emulator','activate_device','create_role_new','pay_new_user_7days',
'pay_frequency_7days','online_time','first_online_time','active_user','active_user_week','total_play','play_time_per_play',
'play_duration_3s','pay_user','pay_money','pay_count','pay_user_new','pay_money_new','pay_count_new','pay_money_no_visual',
'pay_money_new_no_visual',
['name' => 'first_online_time_avg', 'displayed_formula' => "coalesce(round((sum(first_online_time) / sum(new_user) / 60), 2),0.00)"],
['name' => 'online_time_avg', 'displayed_formula' => "coalesce(round((sum(online_time) / sum(new_user) / 60), 2), 0)"],
['name' => 'new_user_cost', 'displayed_formula' => "coalesce(round(sum(cost_discount) / sum(new_user), 2), 0)"],
['name' => 'cpc', 'displayed_formula' => "coalesce(round(sum(cost_discount) / sum(`click_cnt`), 2), 0)"],
['name' => 'click_show_percent', 'displayed_formula' => "coalesce(round(sum(`click_cnt`) / sum(`show_cnt`) * 100, 2), 0)"],
['name' => 'qian_cost', 'displayed_formula' => "coalesce(round(sum(`cost`) * 1000 / sum(`show_cnt`), 2), 0)"],
['name' => 'lp_click_percent', 'displayed_formula' => "coalesce(round(sum(lp_download) / sum(lp_view) * 100, 2), 0)"],
['name' => 'convert_cost', 'displayed_formula' => "coalesce(round(sum(cost) / sum(convert_cnt), 2), 0.00)"],
['name' => 'convert_percent', 'displayed_formula' => "coalesce(round(sum(`convert_cnt`) / sum(`click_cnt`) * 100, 2), 0)"],
['name' => 'download_start_cost_percent', 'displayed_formula' => "coalesce(round(sum(`cost_discount`) / sum(download_start), 2), 0)"],
['name' => 'download_finish_percent', 'displayed_formula' => "coalesce(round(sum(`download_cnt`) / sum(`download_start`) * 100, 2), 0)"],
['name' => 'install_finish_num', 'displayed_formula' => "coalesce(round(sum(`lp_download`) / sum(`lp_view`), 2), 0)"],
['name' => 'install_finish_percent', 'displayed_formula' => "coalesce(round(sum(`install_cnt`) / sum(`download_cnt`), 2), 0)"],
['name' => 'activate_cost', 'displayed_formula' => "coalesce(round(sum(`cost_discount`) / sum(`activate_cnt`), 2), 0)"],
['name' => 'activate_percent', 'displayed_formula' => "coalesce(round(sum(`activate_cnt`) / sum(`click_cnt`) * 100, 2), 0)"],
['name' => 'activate_install_percent', 'displayed_formula' => "coalesce(round(sum(`activate_cnt`) / sum(`install_cnt`) * 100, 2), 0)"],
['name' => 'register_cost', 'displayed_formula' => "coalesce(round(sum(`cost_discount`) / sum(`register`), 2), 0)"],
['name' => 'register_percent', 'displayed_formula' => "coalesce(round(sum(`register`) / sum(`activate_cnt`), 2), 0)"],
['name' => 'new_user_real_percent', 'displayed_formula' => "coalesce(round(sum(`new_real_user`) / sum(`new_user`) * 100, 2), 0)"],
['name' => 'pay_user_new_percent', 'displayed_formula' => "coalesce(round(sum(`pay_user_new`) / sum(`new_user`) * 100, 2), 0)"],
['name' => 'arpu_new_user', 'displayed_formula' => "coalesce(round(sum(`pay_money_new`) / sum(`new_user`), 2), 0)"],
['name' => 'pay_penetration', 'displayed_formula' => "coalesce(round(sum(`pay_money_new`) / sum(`new_user`), 2), 0)"],
['name' => 'create_role_percent', 'displayed_formula' => "coalesce(round(sum(`create_role_new`) / sum(`new_user`) * 100, 2), 0)"],
['name' => 'create_role_cost', 'displayed_formula' => "coalesce(round(sum(`cost_discount`) / sum(`create_role_new`) * 100, 2), 0)"],
['name' => 'pay_frequency_avg_7days', 'displayed_formula' => "coalesce(round(sum(`pay_frequency_7days`) / sum(`pay_new_user_7days`), 2), 0)"],
['name' => 'pay_frequency_7days_cost', 'displayed_formula' => "coalesce(round(sum(`cost_discount`) / sum(`pay_frequency_7days`), 2), 0)"],
['name' => 'new_user_payment_cost', 'displayed_formula' => "coalesce(round(sum(`cost_discount`) / sum(`pay_user_new`), 2), 0)"],
['name' => 'download_start_percent', 'displayed_formula' => "coalesce(round(sum(`download_start`) / sum(`click_cnt`) * 100, 2), 0)"],
['name' => 'arppu_new_user', 'displayed_formula' => "coalesce(round(sum(`pay_money_new`) / sum(`pay_user_new`), 2), 0)"],
['name' => 'play_duration_3s_percent', 'displayed_formula' => "coalesce(round(sum(`play_duration_3s`) / sum(`total_play`) * 100, 2), 0)"],
['name' => 'play_time_avg', 'displayed_formula' => "coalesce(round(sum(`play_time_per_play`) / sum(`total_play`), 2), 0)"],
['name' => 'show_convert_percent', 'displayed_formula' => "coalesce(round(sum(`convert_cnt`) / sum(`show_cnt`) * 100, 2), 0)"],
['name' => 'ltv_<n>', "displayed_formula"=>["ROUND(SUM(ltv_<n>) / SUM(IF(DATEDIFF(DATE(NOW()), tday) +1 >= <n>, new_user, 0)), 2)"], "format" => 'dynamicAssign', 'nodes' => 60],
['name' => 'ltv_amount_<n>', "displayed_formula"=>["SUM(ltv_<n>)"], "format" => 'dynamicAssign', 'nodes' => 7],
['name' => 'roi_<n>', "displayed_formula"=>["ROUND(SUM(ltv_<n>) / SUM(IF(DATEDIFF(DATE(NOW()), tday) +1 >= <n>, cost_discount, 0)) * 100, 2)"], "format" => 'dynamicAssign', 'nodes' => 60],
['name' => 'retain_<n>', 'displayed_formula'=>['ROUND(SUM(retain_<n>) / SUM(new_user) * 100, 2)'], 'format' => 'dynamicAssign', 'nodes'=>31],
['name' => 'paid_retain_<n>','displayed_formula'=>['ROUND(SUM(paid_retain_<n>)/SUM(pay_user_new) * 100, 2)'],"format" => 'dynamicAssign', 'nodes'=>31],
['name' => 'paid_retain_7_<n>', 'displayed_formula'=>['ROUND(SUM(paid_retain_7_<n>) / SUM(pay_new_user_7days) * 100, 2)'],"format" => 'dynamicAssign','nodes'=>31],
['name' => 'paid_cnt_with_new_node_<n>', 'displayed_formula'=>["SUM(paid_cnt_with_new_node_<n>)"], 'nodes'=>7,"format" => 'dynamicAssign'],
['name' => 'paid_user_with_new_node_<n>', 'displayed_formula'=>["SUM(paid_user_with_new_node_<n>)"], 'nodes'=>7,"format" => 'dynamicAssign'],
['name' => 'paid_cnt_cost_<n>', 'displayed_formula'=> ["coalesce(round(SUM(cost_discount) / SUM(paid_cnt_with_new_node_<n>),2), 0)"], 'nodes'=>7,"format" => 'dynamicAssign'],
['name' => 'paid_cnt_avg_with_user_<n>', 'displayed_formula'=>["coalesce(round( SUM(paid_cnt_with_new_node_<n>) / SUM(paid_user_with_new_node_<n>), 2))"], 'nodes'=>7,"format" => 'dynamicAssign'],
['name' => 'back_paid_user_new_node_<n>', 'displayed_formula'=>["SUM(back_paid_user_new_node_<n>)"], 'nodes'=>7,"format" => 'dynamicAssign','derived' => ['back_paid_cnt_cost_node_user']],
['name' => 'back_paid_amount_new_node_<n>', 'displayed_formula'=>["SUM(back_paid_amount_new_node_<n>)"], 'nodes'=>7, "format" => 'dynamicAssign'],
['name' => 'back_paid_new_roi_<n>', 'displayed_formula'=>["coalesce(round(SUM(back_paid_amount_new_node_<n>)/SUM(cost_discount) * 100, 2), 0.00)"], 'nodes'=>7, "format" => 'dynamicAssign'],
['name' => 'back_paid_cnt_new_node_<n>', 'displayed_formula'=>["sum(back_paid_cnt_new_node_<n>)"], 'nodes'=>7, "format" => 'dynamicAssign'],
['name' => 'back_paid_cnt_cost_node_<n>', 'displayed_formula'=>["coalesce(round(SUM(cost_discount) / SUM(back_paid_cnt_new_node_<n>), 2), 0.00)"], 'nodes'=>7, "format"=>"dynamicAssign"],
['name' => 'back_paid_cnt_cost_node_user', 'displayed_formula'=>["coalesce(round(SUM(cost_discount)/SUM(back_paid_user_new_node_1), 2), 0.00)"], 'nodes'=>7, "format"=>"dynamicAssign"],
['name' => 'back_paid_cnt_avg_node_<n>', 'displayed_formula'=>["coalesce(round(SUM(back_paid_cnt_new_node_<n>)/SUM(back_paid_user_new_node_<n>), 2), 0.00)"], 'nodes'=>7, "format"=>"dynamicAssign"],
['name' => 'pass_level_<n>', 'displayed_formula'=>["coalesce(round(SUM(role_pass_<n>_n)/SUM(role_pass_<n>_d) * 100, 2), 0.00)"], 'nodes' => 4,"format"=>"dynamicAssign"],
['name' => 'back_paid_percent', 'displayed_formula'=>"coalesce(round(sum(back_paid_user_new_node_1) /sum(new_user)*100, 2), 0.00)"],
'back_paid_user_new_within_24_hours','back_paid_amount_new_within_24_hours','back_paid_cnt_new_within_24_hours',
['name' => 'back_paid_roi_within_24_hours','displayed_formula'=>"coalesce(round(sum(back_paid_amount_new_within_24_hours) /sum(cost_discount)*100, 2), 0.00)"],
'tt_active_pay_intra_day_count',
'tt_game_in_app_ltv_1day','tt_game_in_app_ltv_4days','tt_game_in_app_ltv_8days',
['name' => 'tt_game_in_app_roi_1day','displayed_formula' => "coalesce(round(sum(tt_game_in_app_ltv_1day) /sum(cost)*100, 2), 0.00)"],
['name' => 'tt_game_in_app_roi_4days','displayed_formula' => "coalesce(round(sum(tt_game_in_app_ltv_4days) /sum(cost)*100, 2), 0.00)"],
['name' => 'tt_game_in_app_roi_8days','displayed_formula' => "coalesce(round(sum(tt_game_in_app_ltv_8days) /sum(cost)*100, 2), 0.00)"],
'tt_game_pay_7d_count', 'tt_active_pay_intra_one_day_count','tt_active_pay_intra_one_day_amount',
['name' => 'tt_active_pay_intra_one_day_roi', 'displayed_formula' => "coalesce(round(sum(tt_active_pay_intra_one_day_amount) /sum(cost)*100, 2), 0.00)"],
'gdt_mini_game_register_users',
'gdt_mini_game_paying_users_d1',
'gdt_minigame_1d_pay_count',
'gdt_mini_game_paying_amount_d1',
['name' => 'gdt_mini_game_first_day_paying_roi', 'displayed_formula' => "coalesce(round(sum(gdt_mini_game_paying_amount_d1) /sum(cost)*100, 2), 0.00)"],
'gdt_mini_game_pay_d3_uv',
'gdt_mini_game_d3_pay_count',
'gdt_mini_game_paying_amount_d3',
['name'=>'gdt_mini_game_pay_d3_roi', 'displayed_formula' => "coalesce(round(sum(gdt_mini_game_paying_amount_d3) /sum(cost)*100, 2), 0.00)"],
'gdt_mini_game_pay_d7_uv',
'gdt_mini_game_d7_pay_count',
'gdt_mini_game_paying_amount_d7',
['name'=>'gdt_mini_game_pay_d7_roi', 'displayed_formula' => "coalesce(round(sum(gdt_mini_game_paying_amount_d7) /sum(cost)*100, 2), 0.00)"],
'gdt_minigame_24h_pay_uv','gdt_minigame_24h_pay_amount',
['name' => 'gdt_minigame_24h_pay_roi' , 'displayed_formula' => "coalesce(round(sum(gdt_minigame_24h_pay_amount) /sum(cost)*100, 2), 0.00)"],
'gdt_first_day_first_pay_count',
'gdt_first_day_pay_count',
'gdt_first_day_pay_amount',
['name' => 'gdt_roi_activated_d1', 'displayed_formula' => "coalesce(round(sum(gdt_first_day_pay_amount) /sum(cost)*100, 2), 0.00)"],
'gdt_active_d3_pay_count',
'gdt_payment_amount_activated_d3',
['name'=>'gdt_roi_activated_d3', 'displayed_formula' => "coalesce(round(sum(gdt_payment_amount_activated_d3) /sum(cost)*100, 2), 0.00)"],
'gdt_active_d7_pay_count','gdt_payment_amount_activated_d7','gdt_reg_dedup_pv',
['name'=>'gdt_roi_activated_d7', 'displayed_formula' => "coalesce(round(sum(gdt_payment_amount_activated_d7) /sum(cost)*100, 2), 0.00)"]
]}

select
{if !empty($groups)}
    {foreach $groups as $item}
        {if $item eq 'tday'} tday,{continue}
        {elseif $item eq 'cp_game_id'} cp_game_id,{continue}
        {elseif $item eq 'game_id'} game_id,{continue}
        {elseif $item eq 'app_show_id'} app_show_id,{continue}
        {elseif $item eq 'channel_main_id'} channel_main_id,{continue}
        {elseif $item eq 'promotion_channel_id'} promotion_channel_id,{continue}
        {elseif $item eq 'promotion_id'} promotion_id,{continue}
        {elseif $item eq 'user_id'} dim_user_id as user_id,{continue}
        {elseif $item eq 'department_id'} department_id,{continue}
        {elseif $item eq 'package_id'} package_id,{continue}
        {elseif $item eq 'platform_id'} t1.platform_id,{continue}
        {elseif $item eq 'account_id'} account_id,{continue}
        {elseif $item eq 'account_name'} account_name,{continue}
        {elseif $item eq 'ad_account'} ad_account,{continue}
        {elseif $item eq 'campaign_id'} campaign_id,any(campaign_name) as campaign_name,{continue}
        {elseif $item eq 'plan_id'} plan_id,{continue}
        {elseif $item eq 'creative_id'} creative_id,{continue}
        {elseif $item eq 'dim_user_os'} dim_user_os,{continue}
        {/if}
    {/foreach}
{/if}
{foreach $dash_col as $chill}
    {if is_array($chill)}
        {if $chill['format']|isset}
            {call $chill['format'] fenir=$chill level=0 nodes=$chill['nodes'] cols=$columns hasCol=$columns|isset}
        {else}
            {if !isset($columns) || (isset($columns) and in_array($chill['name'], $columns)) }
                {$chill['displayed_formula']}  as {$chill['name']},
            {/if}
        {/if}
    {else}
        {if !isset($columns) || (isset($columns) and in_array($chill, $columns)) } sum({$chill}) as {$chill}, {/if}
    {/if}
{/foreach}
max(update_time) as last_update_time
from dashboard_info t1
left join base_conf_platform.tb_base_channel_conf t2 on t1.promotion_channel_id = t2.channel_id
left join dataspy.admin_user t3 on t1.dim_user_id=t3.id
{if !empty($params)}
    {assign var="tag_first_where" value=1}
    {foreach $params as $key => $item}
        {if $key eq 'channel_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                t1.promotion_channel_id in ({$item|join:','})
            {else}
                t1.promotion_channel_id = {$item}
            {/if}
        {/if}
        {if $key eq 'channel_id_tags'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            t1.promotion_channel_id in (
                select distinct data_id from base_conf_platform.biz_tags where tag_id in ({$item|join:','}) and table_name = 'app_channel'
            )
        {/if}
        {if $key eq 'channel_main_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                channel_main_id in ({$item|join:','})
            {else}
                channel_main_id = {$item}
            {/if}
        {/if}
        {if $key eq 'channel_main_id_tags'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            channel_main_id in (
                select distinct data_id from base_conf_platform.biz_tags where tag_id in ({$item|join:','}) and table_name = 'package_channel_main'
            )
        {/if}
        {if $key eq 'user_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                t1.dim_user_id in ({$item|join:','})
            {else}
                t1.dim_user_id = {$item}
            {/if}
        {/if}

        {if $key eq 'department_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                t3.department_id in ({$item|join:','})
            {else}
                t3.department_id = {$item}
            {/if}
        {/if}

    {/foreach}
{/if}
{* 汇总维度 *}
{if !empty($groups)}
    group by
    {if !empty($groups)}
        {foreach $groups as $item}
            {if $item eq 'tday'} tday {if !$item@last}, {/if}
            {elseif $item eq 'cp_game_id'} cp_game_id {if !$item@last}, {/if}
            {elseif $item eq 'game_id'} game_id {if !$item@last}, {/if}
            {elseif $item eq 'app_show_id'} app_show_id {if !$item@last}, {/if}
            {elseif $item eq 'channel_main_id'} channel_main_id {if !$item@last}, {/if}
            {elseif $item eq 'promotion_channel_id'} promotion_channel_id {if !$item@last}, {/if}
            {elseif $item eq 'promotion_id'} promotion_id {if !$item@last}, {/if}
            {elseif $item eq 'user_id'} user_id {if !$item@last}, {/if}
            {elseif $item eq 'department_id'} department_id {if !$item@last}, {/if}
            {elseif $item eq 'package_id'} package_id {if !$item@last}, {/if}
            {elseif $item eq 'platform_id'} t1.platform_id {if !$item@last}, {/if}
            {elseif $item eq 'account_id'} account_id {if !$item@last}, {/if}
            {elseif $item eq 'account_name'} account_name {if !$item@last}, {/if}
            {elseif $item eq 'ad_account'} ad_account {if !$item@last}, {/if}
            {elseif $item eq 'campaign_id'} campaign_id {if !$item@last}, {/if}
            {elseif $item eq 'plan_id'} plan_id {if !$item@last}, {/if}
            {elseif $item eq 'creative_id'} creative_id {if !$item@last}, {/if}
            {elseif $item eq 'dim_user_os'} dim_user_os {if !$item@last}, {/if}
            {/if}
        {/foreach}
    {/if}
{/if}
{* 排序 *}
{if !empty($sorts)}
    order by
    {foreach $sorts as $ss => $oo}
        {$ss} {$oo}
        {if !$oo@last}, {/if}
    {/foreach}
{/if}
{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}
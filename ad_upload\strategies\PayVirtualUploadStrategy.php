<?php

namespace app\ad_upload\strategies;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\contract\AdUploadStrategyInterface;
use app\ad_upload\contract\PayUploadStrategyTrait;
use Plus\Util\StringUtil;

/**
 * 虚拟付费 上报 策略
 * <AUTHOR>
 */
class PayVirtualUploadStrategy extends AdUploadStrategyInterface
{

    use PayUploadStrategyTrait;

    /**
     * 上报类型
     * @var string
     */
    public $action = AdBaseInterface::ACTION_PAY_VIRTUAL;

    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    public function getData($uploadConfig, $packages): array
    {
        if (empty($packages)) {
            return [];
        }
        $condition = " t2.PACKAGE_ID IN ({$packages})";

        $condition .= " and UPLOAD_STATUS = 0 and NOW() >= UPLOAD_TIME and IF(UPLOAD_EXPIRE_TIME != '1000-01-01 00:00:00', NOW() < UPLOAD_EXPIRE_TIME,TRUE) and t1.channel_id = t2.channel_id ";

        $sql = "
        select 
            t1.ID, t1.PACKAGE_ID,
            t1.CORE_ACCOUNT,t1.LOGIN_ACCOUNT,
            t1.UPLOAD_STATUS,t1.UPLOAD_TIME,
            t1.UPLOAD_EXPIRE_TIME,t1.RULE_ID,
            t2.CLICK_ID,'{$this->action}' as TYPE, t1.CHANNEL_ID,
            '1' as UPLOAD_VIRTUAL,
            ANDROID_ID, MONEY, DEVICE_CODE,
            t2.DEVICE_ID,
            t2.MD5_DEVICE_ID
        from ddc_platform.dwd_user_payment_upload_virtual t1 
        left join origin_platform.tb_sdk_user_newlogin_package t2 using (PACKAGE_ID, CORE_ACCOUNT)
        where {$condition} order by t1.ID limit 1";

        return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 上报数据 处理
     * @param array $data         data
     * @param array $uploadConfig 配置
     * @return array
     */
    public function processData(array $data, $uploadConfig): array
    {

        foreach ($data as $k => $v) {
            $data[$k]['PAY_TIME'] = date('Y-m-d H:i:s');
            $data[$k]['ORDER_ID'] = $v['ID'] . '_vir_' . uniqid(); //生成虚拟订单号
            // 上报日志
            $data[$k]['paid_report_log']                           = [];
            $data[$k]['paid_report_log']['reported_money']         = $v['MONEY'];
            $data[$k]['paid_report_log']['reported_behavior']      = 3;
            $data[$k]['paid_report_log']['reported_rule_id']       = $v['id'] ?? 0;
            $data[$k]['paid_report_log']['reported_behavior_rule'] = '{}';
        }
        return $this->processDataPay($data, $uploadConfig);
    }


    /**
     * 上传数据
     * @param array  $data         处理数据
     * @param string $uploadMethod 上传方法
     * @return mixed
     */
    public function uploadData(array $data, string $uploadMethod)
    {
        //最终仍标记为正常付费流程
        $uploadMethod = 'upload' . StringUtil::convertToCamelHump(AdBaseInterface::ACTION_PAY, '_');
        return parent::uploadData($data, $uploadMethod);
    }

    /**
     * 不需要 设置最后一条id 日志
     * @param array $data data
     * @return void
     */
    public function setLastId($data): void
    {
    }

    /**
     * 不需要 设置未匹配数据
     * @return void
     */
    public function setUnmatchedIds(): void
    {
    }

    /**
     * 不需要 设置最大最后一条id
     * @return void
     */
    public function setMaxLastId(): void
    {
    }
}

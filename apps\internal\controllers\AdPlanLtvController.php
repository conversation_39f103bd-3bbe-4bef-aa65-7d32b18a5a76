<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Helpers\Calculator;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Helpers\IndexCalculators;
use app\apps\internal\Helpers\IndicatorCalcHelpers;
use app\apps\internal\Helpers\IndicatorsHelpers;
use app\apps\internal\Helpers\LtvCalculator;
use app\apps\internal\Helpers\RoiCalculator;
use app\apps\internal\Traits\AdOfflineDash;
use app\apps\internal\Traits\AdRouteRequest;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\internal\Traits\OfflineDash;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\logic\advertise\AdCreativeLtvLogic;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\AdvertiserData\CreativeLtvIndex;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\AdvertiserData\RealtimePlanIndex;
use app\service\ConfigService\BasicServ;
use app\service\ConfigService\Tables\AdPlanLtv;
use app\service\General\GeneralOptionServ;

/**
 * @AdPlanLtvController 计划层离线LTV API
 */
class AdPlanLtvController extends BaseTableController
{
    use AdRouteRequest, ColumnsInteract, AdOfflineDash;

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];
        $groups = Arr::wrap($params->pull('groups'));

        if (!$groups) {
            $tab = $params->pull('tab', "plan");
            if ($tab == "plan") {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'plan_id']; // 默认组合
            }
            else {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'campaign_id']; // 默认组合
            }
        }

        $options    = $params->toArray();
        $today      = new \DateTime();
        $maxDayDiff = days_apart($params->get('range_date_start'), $today);
        $baseServ   = new RealtimePlanIndex();

        // 外部账号仅能显示自己账号归属的数据
        if (\Plus::$service->admin->isOutsiders()) {
            $options['user_id'] = \Plus::$service->admin->getUserId();
        }

        if ($params->has('sort')) {
            $sortC = $params->pull('sort');
            $sort  = [$sortC => ($params->pull('order') == 'ascend' ? 'asc' : 'desc')];
        }
        else {
            if (in_array('tday', $groups)) {
                $sort = ['tday' => 'asc', 'new_user' => 'desc'];
            }
            else {
                $sort = ['new_user' => 'desc'];
            }
        }

        $fullGroups = ColumnManager::groupAdFill($groups);
        $logic      = new AdCreativeLtvLogic();

        $options['last_pay_date'] = date('Y-m-d');

        return $logic->ltvCreativeData($options, $fullGroups, $sort, ['page_size' => $pageSize, 'offset' => ($page - 1) * $pageSize]);
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $baseCollect   = [
            'tday', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id',
            'promotion_channel_id', 'promotion_id', 'platform_id',
            'department_id', 'user_id',
        ];
        $adBaseCollect = [
            'ad_account', 'account_id', 'campaign_name', 'campaign_id',
            'plan_name', 'plan_id', 'creative_name', 'creative_id',
        ];

        $newBaseCollect = [
            'cost', 'cost_discount', 'new_user', 'new_user_cost',
            'new_user_total_pay', 'total_ltv', 'total_roi',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'ad_base', 'label' => '广告信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],

                    ['value' => 'ltv_group_1', 'label' => 'LTV1-30'],
                    ['value' => 'ltv_group_2', 'label' => 'LTV45-180'],
                    ['value' => 'ltv_group_3', 'label' => 'LTV210-360'],
                    ['value' => 'ltv_group_4', 'label' => 'LTV390-720'],

                    ['value' => 'roi_group_1', 'label' => 'ROI1-30'],
                    ['value' => 'roi_group_2', 'label' => 'ROI45-180'],
                    ['value' => 'roi_group_3', 'label' => 'ROI210-360'],
                    ['value' => 'roi_group_4', 'label' => 'ROI390-720'],
                ],
            ],
        ];

        $fields = $this->tableFields($params->toArray());

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'] ?? '';
            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $adBaseCollect)) {
                $field['classify'] = ['attrs', 'ad_base'];
            }
            elseif (in_array($dIndex, $newBaseCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @param $key
     *
     * @return Collection
     */
    protected function registerParams($key = null): Collection
    {
        return $this
            ->baseParams()
            ->merge([
                ['field' => 'groups'],
                ['field' => 'range_date_dimension', 'default' => ConstHub::DIMENSION_DAY],
                ['field' => 'ltv_type', 'default' => 0], // ltv展示方式
                ['field' => 'column_scope', 'default' => ['ltv', 'roi']], // 报表展示类型，可单独展示LTV/ROI
                ['field' => 'search_mode', 'default' => ['normal']], // 查询关联模式,控制关联的方式
                ['field' => 'max_day_type', 'default' => -1], // 最大节点限制
                ['field' => 'marketing_goal'], // 营销场景
            ]);
    }

    /**
     *
     * @route /internal/ad-plan-ltv/simple-search
     * @return array
     * @throws \Exception
     * @todo  整合到data
     */
    public function simpleSearchAction(): array
    {
        $params        = $this->wrapParams($this->request);
        $groups        = ['tday'];
        $columnScope   = $params->get('column_scope');
        $baseServ      = new RealtimePlanIndex();
        $ltvServ       = new CreativeLtvIndex();
        $timeDimension = (int)$params->get('range_date_dimension');

        $options = $params->toArray();

        if (ConstHub::DIMENSION_WEEK === $timeDimension) {
            $baseResult = $baseServ->simpleListByWeek($options, [], $groups);
        }
        elseif (ConstHub::DIMENSION_MONTH === $timeDimension) {
            $baseResult = $baseServ->simpleListByMonth($options, [], $groups);
        }
        else {
            $baseResult = $baseServ->simpleListByDay($options, [], $groups);
        }
        $ltvResult = $ltvServ->simpleList($options);
        $ltvInfo   = IndicatorsHelpers::dimReduction($ltvResult['list'], 'day_type', ['tday'], ['money', 'money_all']);
        $info      = &$baseResult['list'];

        $processLine = new ProcessLine();
        $processLine->addProcess(function (&$target) use ($ltvInfo) {
            $tDay = $target['tday'];
            if (!empty($ltvInfo[$tDay])) {
                $target['ltv_info']           = $ltvInfo[$tDay]['day_type'] ?? [];
                $target['new_user_total_pay'] = $target['ltv_info'][1000]['money_all'] ?? 0.00;
            }
        });

        $ltvGetUser = IndexCalculators::getSingleValue('new_user');
        $roiGetCost = IndexCalculators::getSingleValue('cost_discount');

        if (in_array('ltv', $columnScope)) {
            $processLine->addProcess(
                LtvCalculator::calcEachRow($ltvGetUser, ['ltv_type' => 0, 'max_days' => 7])
            );
        }

        if (in_array('roi', $columnScope)) {
            $processLine->addProcess(
                RoiCalculator::calcEachRow($roiGetCost, ['ltv_type' => 0, 'max_days' => 7])
            );
        }

        $processLine
            ->addProcess(LtvCalculator::cumulativeLtvEachRow('new_user'))
            ->addProcess(RoiCalculator::cumulativeRoiEachRow())
            ->addProcess(function (&$target) {
                unset($target['ltv_info']);
            });

        $processLine->run($info);

        /**
         * 汇总行处理
         */
        $summaryRow         = &$baseResult['summary'];
        $summaryLtv         = array_column($ltvResult['summary'], null, 'day_type');
        $summaryRow['tday'] = '汇总';

        // 累计已到天数数据
        $summaryNInfo = Calculator::cumulativeOnDays($info, ['new_user', 'cost_discount']);
        $summaryRow   = array_merge($summaryRow, $summaryNInfo);

        $summaryLtvGetUser = IndexCalculators::getValueInCollectByN('new_user_n');
        $summaryRoiGetCost = IndexCalculators::getValueInCollectByN('cost_discount_n');

        if (in_array('ltv', $columnScope)) {
            LtvCalculator::calcLtv($summaryRow, $summaryLtv, $summaryLtvGetUser);
            if (empty($summaryLtv[1000]) || empty((int)$summaryRow['new_user'])) {
                $summaryRow['total_ltv'] = 0.00;
            }
            else {
                $summaryRow['total_ltv'] = number_format(
                    math_eval('x/y', ['x' => $summaryLtv[1000]['money_all'], 'y' => $summaryRow['new_user']]), 2
                );
            }
        }

        if (in_array('roi', $columnScope)) {
            RoiCalculator::calcRoi($summaryRow, $summaryLtv, $summaryRoiGetCost);

            if (empty($summaryLtv[1000]['money_all']) || empty(floatval($summaryRow['cost_discount']))) {
                $summaryRow['total_roi'] = '0.00%';
            }
            else {
                $summaryRow['total_roi'] = number_format(
                        math_eval('x/y*100', ['x' => $summaryLtv[1000]['money_all'], 'y' => $summaryRow['cost_discount']]), 2
                    ) . '%';
            }
        }
        unset($summaryRow['new_user_n'], $summaryRow['cost_discount_n']);
        $fields = (new AdPlanLtv())->getSimpleFields($options)->toArray();

        return $this->success(array_merge($baseResult, ['fields' => $fields]));
    }


}
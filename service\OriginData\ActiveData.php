<?php

namespace app\service\OriginData;

use app\models\OriginPlatform\TbSdkActiveLog;
use app\models\OriginPlatform\TbSdkUserNewloginPackage;
use Plus\Util\Pagination;

class ActiveData
{
    protected const DB_PLATFORM = 'origin_platform';

    /**
     * @param array $codeList
     * @return array
     */
    public function listByDeviceCode(
        array $codeList = []
    ): array
    {
        $core = "'" . implode("','", $codeList) . "'";

        $sql = "
        select distinct device_code, package_id
        from origin_platform.tb_sdk_active_log
        where device_code in ({$core}) and package_id > 9 and CHANNEL_ID < ********* order by `TIME`";

        return \Plus::$app->origin_platform->query($sql)->fetchAll();
    }


    /**
     * @param array $coreAccount
     * @return array
     */
    public function listCoreInfoByAccount(
        array $coreAccount
    ): array
    {
        $accountString = "'" . implode("','", $coreAccount) . "'";

        $sql = "
        select distinct core_account,device_code from origin_platform.tb_sdk_user_newlogin_package
        where core_account in ({$accountString}) and package_id > 9 and device_code != ''";

        return \Plus::$app->origin_platform->query($sql)->fetchAll();
    }


    /**
     * @param $packageId
     * @return mixed
     */
    public function fetchCpGameByPackage($packageId)
    {
        $sql = "select cp_game_id, package_id from base_conf_platform.tb_package_detail_conf where  package_id = {$packageId}";

        return \Plus::$app->base_conf_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * @param $coreAccount
     * @param $cpGameId
     * @return mixed
     */
    public function fetchBeginActivePackage($coreAccount, $cpGameId)
    {
        $sql = "
        select CP_GAME_ID, DEVICE_CODE, t1.PACKAGE_ID,t1.`TIME`
                from origin_platform.tb_sdk_active_log t1
                left join base_conf_platform.tb_package_detail_conf power on  t1.PACKAGE_ID = power.PACKAGE_ID
                where device_code in
                      (select DEVICE_CODE from origin_platform.tb_sdk_user_newlogin_package where CORE_ACCOUNT = '{$coreAccount}')
                    and power.CP_GAME_ID = {$cpGameId}
                order by t1.`TIME` limit 1
        ";

        return \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * 获取首登包
     * @param $coreAccount
     * @param $cpGameId
     * @return mixed
     */
    public function fetchFristloginPackage($coreAccount, $cpGameId)
    {
        $sql = "
            select PACKAGE_ID,`TIME` from origin_platform.tb_sdk_user_newlogin_package a join base_conf_platform.tb_base_game_conf b using(GAME_ID) where   CORE_ACCOUNT = '{$coreAccount}'
                    and  b.CP_GAME_ID = {$cpGameId} order by  a.`TIME` limit 1
        ";
        return \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }
}
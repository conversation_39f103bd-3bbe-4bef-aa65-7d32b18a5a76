<?php

namespace app\ad_upload\strategies;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\contract\AdUploadStrategyInterface;

/**
 * 留存数据上报 策略
 * <AUTHOR>
 */
class RemainUploadStrategy extends AdUploadStrategyInterface
{
    /**
     * 上报类型
     * @var string
     */
    public $action = AdBaseInterface::ACTION_REMAIN;

    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    public function getData($uploadConfig, $packages): array
    {
        if (!empty($packages)) {
            $yesterday   = date("Y-m-d", strtotime("{$this->timeBegin} -1 day"));
            $whereCommon = " WHERE PACKAGE_ID IN ({$packages}) ";
            $sql         = "SELECT A.PACKAGE_ID,A.DEVICE_KEY,A.DEVICE_ID,A.MD5_DEVICE_ID,A.ID
                    FROM (
                        SELECT ID,PACKAGE_ID,DEVICE_KEY,DEVICE_ID,MD5_DEVICE_ID,DEVICE_CODE,OAID
                        FROM tb_sdk_user_login {$whereCommon}
                        AND TIME_SERVER BETWEEN '{$this->timeBegin}'
                        AND '{$this->timeEnd}'
                        GROUP BY PACKAGE_ID,MD5_DEVICE_ID
                    ) A
                    JOIN (
                        SELECT PACKAGE_ID,DEVICE_ID,MD5_DEVICE_ID
                        FROM tb_sdk_active_log {$whereCommon}
                        AND TIME_SERVER BETWEEN '{$yesterday} 00:00:00'
                        AND '{$yesterday} 23:59:59'
                        GROUP BY PACKAGE_ID,MD5_DEVICE_ID
                    ) B USING (PACKAGE_ID, MD5_DEVICE_ID)";

            return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        }// end if()

        return [];
    }

    /**
     * 设置如果上一步getData 获取数据为空，设置最大上报id
     * 避免长期没有数据上报的渠道，查询过多数据
     * @return void
     */
    public function setMaxLastId():void
    {
    }
}

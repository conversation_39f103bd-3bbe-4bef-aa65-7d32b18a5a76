<?php

namespace app\ad_upload\channels;

/**
 * 推广子渠道 -- 快手直播投流
 */
class KuaishouAd extends Kuaishou
{
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE', 'KUAISHOUAD_PARAM_');
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG', 'KUAISHOUAD_PARAM_');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY', 'KUAISHOUAD_PARAM_');
    }
}

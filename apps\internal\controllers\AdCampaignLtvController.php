<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Helpers\Calculator;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Helpers\IndexCalculators;
use app\apps\internal\Helpers\IndicatorCalcHelpers;
use app\apps\internal\Helpers\LtvCalculator;
use app\apps\internal\Helpers\RoiCalculator;
use app\apps\internal\Traits\AdOfflineDash;
use app\apps\internal\Traits\AdRouteRequest;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\internal\Traits\DefaultActionable;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\logic\advertise\AdCreativeLtvLogic;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\AdvertiserData\CreativeLtvIndex;
use app\service\AdvertiserData\RealtimeCreativeIndex;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\AdvertiserData\RealtimePlanIndex;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;

class AdCampaignLtvController extends BaseTableController
{
    use AdRouteRequest, ColumnsInteract, AdOfflineDash, DefaultActionable;

    /**
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];
        $groups = Arr::wrap($params->pull('groups'));

        if (!$groups) {
            $tab = $params->pull('tab', "plan");
            if ($tab == "plan") {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'plan_id']; // 默认组合
            }
            else {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'campaign_id']; // 默认组合
            }
        }

        $options    = $params->toArray();
        $today      = new \DateTime();
        $maxDayDiff = days_apart($params->get('range_date_start'), $today);
        $baseServ   = new RealtimePlanIndex();

        // 外部账号仅能显示自己账号归属的数据
        if (\Plus::$service->admin->isOutsiders()) {
            $options['user_id'] = \Plus::$service->admin->getUserId();
        }

        if ($params->has('sort')) {
            $sortC = $params->pull('sort');
            $sort  = [$sortC => ($params->pull('order') == 'ascend' ? 'asc' : 'desc')];
        }
        else {
            $sort = [
                'tday'     => 'ASC',
                'new_user' => 'DESC'
            ];
        }

        $fullGroups = ColumnManager::groupAdFill($groups);
        $logic      = new AdCreativeLtvLogic();

        $options['last_pay_date'] = date('Y-m-d');

        return $logic->ltvCreativeData($options, $fullGroups, $sort, ['page_size' => $pageSize, 'offset' => ($page - 1) * $pageSize]);
    }

    protected function registerParams($key = null): Collection
    {
        return $this->baseParams()->merge([
            ['field' => 'groups', 'default' => ['tday', 'package_id', 'campaign_id']],
            ['field' => 'ltv_type', 'default' => 0],
            ['field' => 'column_scope', 'default' => ['ltv', 'roi']], // 报表展示类型，可单独展示LTV/ROI
            ['field' => 'marketing_goal'], // 报表展示类型，可单独展示LTV/ROI
        ]);
    }


    protected function fields(Collection $params): array
    {
        $baseCollect   = [
            'tday', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id',
            'promotion_channel_id', 'promotion_id', 'platform_id',
            'department_id', 'user_id',
        ];
        $adBaseCollect = [
            'ad_account', 'account_id', 'campaign_name', 'campaign_id',
            'plan_name', 'plan_id', 'creative_name', 'creative_id',
        ];

        $newBaseCollect = [
            'cost', 'cost_discount', 'new_user', 'new_user_cost',
            'new_user_total_pay', 'total_ltv', 'total_roi',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'ad_base', 'label' => '广告信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],

                    ['value' => 'ltv_group_1', 'label' => 'LTV1-30'],
                    ['value' => 'ltv_group_2', 'label' => 'LTV45-180'],
                    ['value' => 'ltv_group_3', 'label' => 'LTV210-360'],
                    ['value' => 'ltv_group_3', 'label' => 'LTV210-360'],
                    ['value' => 'ltv_group_4', 'label' => 'LTV390-720'],

                    ['value' => 'roi_group_1', 'label' => 'ROI1-30'],
                    ['value' => 'roi_group_2', 'label' => 'ROI45-180'],
                    ['value' => 'roi_group_3', 'label' => 'ROI210-360'],
                    ['value' => 'roi_group_4', 'label' => 'ROI390-720'],
                ],
            ],
        ];

        $fields = $this->tableFields($params->toArray());

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'] ?? '';
            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $adBaseCollect)) {
                $field['classify'] = ['attrs', 'ad_base'];
            }
            elseif (in_array($dIndex, $newBaseCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }
}
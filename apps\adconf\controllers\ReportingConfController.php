<?php

namespace app\apps\adconf\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\adconf\ReportingConfLogic;
use app\models\baseConfPlatform\TbAdChannelConf;
use app\models\baseConfPlatform\TbBaseChannelConf;
use Plus\Net\Http;

/**
 * 广告数据上报配置
 *
 * @route /adconf/reporting-conf/*
 */
class ReportingConfController extends BaseTableController
{
    /**
     * 数据
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $sort = ['id' => 'desc'];

        $paginate = [
            'page'      => $params->pull('page', 1),
            'page_size' => $params->pull('page_size', 100),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }

        $options = $params->toArray();

        return (new ReportingConfLogic())->getInfo($options, [], $paginate, $sort);
    }

    /**
     * 表头
     *
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                ],
            ],
        ];

        $fields = [
            ['title' => 'ID', 'dataIndex' => 'id', 'classify' => ['attrs', 'base']],
            ['title' => '推广子渠道', 'dataIndex' => 'channel_name', 'classify' => ['attrs', 'base']],
            ['title' => '游戏统计名', 'dataIndex' => 'game_name', 'classify' => ['attrs', 'base']],
            ['title' => '包号', 'dataIndex' => 'package_id', 'classify' => ['attrs', 'base']],
            ['title' => '上报内容', 'dataIndex' => 'report_content', 'classify' => ['attrs', 'base']],
            ['title' => '状态', 'dataIndex' => 'status_name', 'classify' => ['attrs', 'base']],
            ['title' => '操作人', 'dataIndex' => 'user_name', 'classify' => ['attrs', 'base']],
            ['title' => '上报日志', 'dataIndex' => 'notice', 'classify' => ['attrs', 'base']],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return array
     */
    public function saveAction(): array
    {
        $requestBody = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('json 解析错误');
        }

        try {
            $logic = new ReportingConfLogic();
            $logic->updateOrInsert([$requestBody]);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        try {
            $channel = $requestBody['channel_id'] ?? '';
            if ($channel == 2 && !isset($requestBody['upload_switch'])) {
                $package = $requestBody['package_id'];
                \Plus::$app->redis82->set('cltj:uploadCondition:' . $channel . $package, 0);
                \Plus::$app->redis82->set('cltj:uploadMinNum:' . $channel . $package, 0);
            }
        }
        catch (\Throwable $e) {
            return $this->error('缓存刷新失败');
        }

        return $this->success([]);
    }

    /**
     * 上报配置 - 下载日志请求
     *
     * @return array
     */
    public function downloadLogAction(): array
    {
        $request   = \Plus::$app->request;
        $channelId = $request->getValue('channel_id');
        $packageId = $request->getValue('package_id');
        $date      = $request->getValue('date');
        $event     = $request->getValue('event');

        $channelMap = [
            1    => 'UC_PARAM',
            2    => 'TOUTIAO2_PARAM',
            3    => 'BAIDU_PARAM',
            4    => 'GDT_PARAM',
            5    => 'WEIXIN_PARAM',
            6    => 'KUAISHOU_PARAM',
            12   => 'BAIDU_KEYWORD_PARAM',
            18   => 'SM_PARAM',
            49   => 'AIQIYI_PARAM',
            64   => 'MOMO_PARAM',
            69   => 'GDT_NEW_PARAM',
            71   => 'ADLM_PARAM',
            100  => 'REYUN_PARAM',
            1002 => 'TOUTIAO_PARAM',
            6744 => 'Mtt',
        ];

        try {
            $date = (new \DateTime($date))->format('Ymd');
        }
        catch (\Exception $e) {
            return $this->error('时间格式有误');
        }

        $command = "cat%20/data/www/elk_log/cltj.910app.com/logs/ad_data_upload/_{$date}.log%20|%20grep%20{$packageId}";
        $channel = $channelMap[$channelId] ?? '';

        if (empty($channel)) {
            $r       = TbBaseChannelConf::getInstance()->asArray()->find(['channel_id' => $channelId], ['channel_code']);
            $channel = strtoupper($r['channel_code']) . '_PARAM';
        }

        $grepString = "grep%20'{$channel}'";
        if (!empty($event) && !in_array($channelId, [4, 5])) {
            if (is_array($event)) {
                $greps      = implode('|', array_map(fn($item) => $channel . '_' . strtoupper(str_replace('_upload', '', $item)), $event));
                $grepString = "grep%20-E '{$greps}'";
            }
            else {
                $event      = strtoupper(str_replace('_upload', '', $event));
                $grepString = "grep%20{$channel}_{$event}";
            }
        }

        $command .= "%20|{$grepString}";
        $url     = "http://159.75.114.2:8080/boss/system_exec/default?COMMAND=" . $command;

        try {
            $response = (new Http($url))->get();
            $response = \json_decode($response, true);

            if (json_last_error() != JSON_ERROR_NONE) {
                return $this->error('JSON解析错误');
            }
        }
        catch (\Exception $e) {
            return $this->error('请求失败', ['message' => $e->getMessage()]);
        }

        $data    = $response['data'] ?? [];
        $content = '';
        foreach ($data as $foo) {
            $content .= $foo . "\r\n";
        }

        $filename = implode('-', [$packageId, $channelId, $date]) . '.txt';

        header('Content-type: application/txt');
        header('Content-Disposition: attachment; filename="' . $filename . '.txt"');
        echo $content;
        die;
    }

    /**
     * @return array
     */
    public function reportingAction(): array
    {
        $request     = \Plus::$app->request;
        $requestBody = \json_decode($request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('json解析错误');
        }

        $packageId   = $requestBody['package_id'] ?? '';
        $deviceId    = $requestBody['device_id'] ?? '';
        $androidId   = $requestBody['android_id'] ?? '';
        $oaid        = $requestBody['oaid'] ?? '';
        $coreAccount = $requestBody['core_account'] ?? '';
        $money       = $requestBody['money'] ?? 0.00;
        $reportEvent = $requestBody['report_content'] ?? [];

        try {
            $logic = new ReportingConfLogic();
            if (in_array('active_upload', $reportEvent)) {
                $logic->reportActive($packageId, $deviceId, $androidId, $oaid);
            }

            if (in_array('reg_upload', $reportEvent)) {
                $logic->reportReg($packageId, $deviceId, $androidId, $coreAccount, $oaid);
            }

            if (in_array('pay_upload', $reportEvent)) {
                $logic->reportPay($packageId, $deviceId, $androidId, $coreAccount, $money, $oaid);
            }

//            if (in_array('create_role', $reportEvent)) {
//                $logic->reportCreateRole($packageId, $deviceId, $androidId, $coreAccount, $oaid);
//            }
        }
        catch (\Exception $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([], '上报成功');
    }

}
<?php

namespace app\ad_upload\tests\tool;

use app\ad_upload\tool\AlternatingProbability;
use PHPUnit\Framework\TestCase;

/**
 * 概率算法测试
 * <AUTHOR>
 * phpcs:disable
 */
class AlternatingProbabilityTest extends TestCase
{
    private $key = 'test';
    private $redis;
    protected function setUp(): void
    {
        // 初始化 Redis 实例
        $this->redis = \Plus::$app->redis;
        $this->redis->delete($this->key); // 清除测试键
    }


    public function testNext(): void
    {
        $ratio = 50; // 50% 概率
        $total = 10;

        $o = new AlternatingProbability($this->redis, $this->key, $ratio, $total);

        for ($i = 0; $i < 100; $i++) {
            $next = $o->next();
            if ($i % 2 == 0) {  // 偶数都是 true
                $this->assertTrue($next);
            } else {
                $this->assertFalse($next);
            }
        }
    }

    public function testNext2(): void
    {
        $ratio = 30.0; // 30% 的概率
        $total = 100;   // 总共 100 次

        $ap = new AlternatingProbability($this->redis, $this->key, $ratio, $total);

        // 统计返回 true 的次数
        $trueCount = 0;
        for ($i = 0; $i < $total; $i++) {
            if ($ap->next()) {
                $trueCount++;
            }
        }

        // 检查 true 的次数是否接近预期
        $expectedTrueCount = round($total * $ratio / 100);
        $this->assertEquals($expectedTrueCount, $trueCount);
    }

    public function testSequenceReinitialization()
    {
        $ratio = 50.0; // 50% 的概率
        $total = 5;    // 总共 5 次

        $ap = new AlternatingProbability($this->redis, $this->key, $ratio, $total);

        // 第一次获取所有值
        $firstRun = [];
        for ($i = 0; $i < $total; $i++) {
            $firstRun[] = $ap->next();
        }

        // 第二次获取所有值，应该重新初始化序列
        $secondRun = [];
        for ($i = 0; $i < $total; $i++) {
            $secondRun[] = $ap->next();
        }

        // 检查两次获取的序列是否相同
        $this->assertEquals($firstRun, $secondRun);
    }
}

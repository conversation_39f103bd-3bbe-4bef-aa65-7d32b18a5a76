<?php

namespace app\apps\api\controllers;

use app\ad_upload\contract\DeductionStrategyInterface;
use app\ad_upload\deduction\CustomDeductionStrategy;
use app\ad_upload\services\DeductionService;
use app\ad_upload\strategies\PayUploadStrategy;
use Plus\MVC\Controller\JsonController;

/**
 * 扣量上报接口, sdk 上报用
 * <AUTHOR>
 */
class AdUploadController extends JsonController
{

    /**
     * 扣量上报规则查询
     * @param array $data 订单数据
     * @return array
     */
    public function ruleAction($data)
    {
        $orderId = $data['order_id'] ?? 0;
        if (empty($orderId)) {
            return $this->error("订单参数错误");
        }
        $orderId   = str_replace('coin', '', $orderId); //去掉coin前缀
        $payUpload = new PayUploadStrategy(0, '', '', '', '', '');
        $rs        = $payUpload->getPaidWithXcx([$orderId], []);
        if (empty($rs)) {
            return $this->error("订单数据不存在");
        }
        if ($rs[0]['CHANNEL_ID'] == 0) {
            //3分钟后没有归因才算自然量
            if ((time() - strtotime($rs[0]['PAY_TIME'])) < 60 * 3) {
                return $this->error("订单还没有归因");
            }
        }// end if()
        foreach ($rs as $k => $v) {
            if ($v['PAY_GAME_ID'] != $v['SOURCE_GAME_ID']) {
                return $this->success([
                    'order_id'               => $orderId,
                    'actually_money'         => $rs[0]['ACTUALLY_MONEY'],
                    'reported_money'         => 0,
                    'reported_behavior'      => -1,
                    'reported_rule_id'       => 0,
                    'reported_behavior_rule' => '{}',
                    'no_reported_origin'     => '归因gameid和充值gameid不一致，不上报',
                ]);
            }
            $reportLog = $payUpload->initPaidReportLog($v);
            //补充支付相关上报日志
            if (isset($v['paid_report_log'])) {
                //合并数据
                $rs[$k]['paid_report_log'] = array_merge($reportLog, $v['paid_report_log']);
            } else {
                $rs[$k]['paid_report_log'] = $reportLog;
            }
        }// end foreach()

        //扣量上报
        $deductionService = new DeductionService();
        //扣量策略
        $strategies = [
            new CustomDeductionStrategy(),
        ];
        foreach ($rs as $k => $v) {
            /**
             * 策略
             * @var DeductionStrategyInterface $s
             */
            foreach ($strategies as $s) {
                $deductionService->setStrategy($s);
                $apply = $deductionService->applyDeduction($v, 1);
                if ($apply) {
                    $rs[$k] = $s->getData(); //获取处理后的数据
                    break; //满足一个策略就退出循环
                }
            }
        }// end foreach()
        //return $rs;
        if (isset($rs[0]['paid_report_log'])) {
            $log = $rs[0]['paid_report_log'];
            if (isset($log['reported_rule_id'])) { // 命中扣量规则
                $reported_behavior  = $log['reported_behavior'];
                $no_reported_origin = $log['no_reported_origin'];
                if ($rs[0]['ACTUALLY_MONEY'] <= 0) {
                    $reported_behavior  = 1; // 0元订单不上报
                    $no_reported_origin = '不上报原因：actually_money金额为0';
                }
                return $this->success([
                    'order_id'               => $log['order_id'],
                    'reported_money'         => $log['reported_money'],
                    'actually_money'         => $rs[0]['ACTUALLY_MONEY'],
                    'reported_behavior'      => $reported_behavior,
                    'reported_rule_id'       => $log['reported_rule_id'],
                    'reported_behavior_rule' => $log['reported_behavior_rule'],
                    'no_reported_origin'     => $no_reported_origin,
                ]);
            } else {
                $reported_behavior  = 0;
                $no_reported_origin = '没命中扣量规则，正常上报';
                if ($rs[0]['ACTUALLY_MONEY'] <= 0) {
                    $reported_behavior  = 1; // 0元订单不上报
                    $no_reported_origin = '不上报原因：actually_money金额为0';
                }
                return $this->success([
                    'order_id'               => $log['order_id'],
                    'reported_money'         => $log['actually_money'],
                    'actually_money'         => $rs[0]['ACTUALLY_MONEY'],
                    'reported_status'        => 1, //测试用
                    'reported_behavior'      => $reported_behavior,
                    'reported_rule_id'       => 0,
                    'reported_behavior_rule' => '{}',
                    'no_reported_origin'     => $no_reported_origin,
                ]);
            }// end if()
        } else {
            return $this->error("扣量失败");
        }// end if()
    }
}

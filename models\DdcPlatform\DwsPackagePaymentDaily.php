<?php

namespace app\models\DdcPlatform;

use Plus\MVC\Model\ActiveRecord;


/**
 *
 *
 * @property int    id
 * @property string tday
 * @property int    cp_game_id
 * @property int    game_id
 * @property int    package_id
 * @property int    firstlogin_pay_user_week
 * @property int    firstlogin_pay_user_all_week
 * @property int    pay_user_week
 * @property int    pay_user_all_week
 * @property float  firstlogin_pay_money
 * @property int    firstlogin_pay_user_new
 * @property float  firstlogin_pay_money_new
 * @property float  firstlogin_pay_money_all
 * @property int    firstlogin_pay_user_new_all
 * @property float  firstlogin_pay_money_new_all
 * @property int    firstlogin_pay_user
 * @property int    firstlogin_pay_user_all
 * @property int    order_count_all
 * @property float  pay_money_all
 * @property int    ORDER_SUCCESS_ALL
 * @property float  pay_money_newlogin_all
 * @property int    pay_user_newlogin_all
 * @property float  pay_money_reg_all
 * @property int    order_count
 * @property float  pay_money
 * @property int    order_success
 * @property float  pay_money_newlogin
 * @property int    pay_user_newlogin
 * @property float  pay_money_reg
 * @property int    pay_user
 * @property int    pay_user_all
 * @property int    is_multiple
 */
class DwsPackagePaymentDaily extends ActiveRecord
{
    protected $_primaryKey = 'id';

    public function __construct($data = [])
    {

        $this->_db = \Plus::$app->ddc_platform;
        parent::__construct($data);
    }
}
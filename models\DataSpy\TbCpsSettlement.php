<?php

namespace app\models\DataSpy;

use Plus\MVC\Model\ActiveRecord;


/**
 * @TbCpsSettlement
 * CPS结算数据表
 *
 * @property int    ID
 * @property string TIME
 * @property int    CHANNEL_ID
 * @property int    CP_GAME_ID
 * @property int    GAME_ID
 * @property int    PACKAGE_ID
 * @property int    PROMOTION_WAY_ID
 * @property string COMPANY
 * @property int    OS
 * @property int    ACTIVE_DEVICE_NUM
 * @property int    REG_DEVICE_NUM
 * @property int    PAY_PEOPLE_NUM
 * @property float  PAY_MONEY
 * @property float  SETTLEMENT_MONEY
 * @property float  BALANCE
 */
class TbCpsSettlement extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->dataspy;
        parent::__construct($data);
    }

}
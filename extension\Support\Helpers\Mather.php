<?php

namespace app\extension\Support\Helpers;

class Mather
{
    /**
     * 根据数值找出适合范围内的区间
     *
     * @param $haystack
     * @param $needle
     *
     * @return array
     */
    public static function findIn($haystack, $needle): array
    {
        $result = [];
        sort($needle);

        foreach ($needle as $i => $foo) {
            if ($foo > $haystack) break;
            $result[] = $foo;
        }

        return $result;
    }

    /**
     * 在额度范围区间内根据间隔找出特定集合
     *
     * @param int $startNum
     * @param int $endNum
     * @param int $numStep
     *
     * @return array
     */
    public static function findNumInScope(int $startNum, int $endNum, int $numStep): array
    {
        $result = [];
        $diff   = round(($endNum - $startNum) / $numStep);

        for ($i = 1; $i <= $diff; $i++) {
            $result[] = $i * $numStep + $startNum;
        }

        return $result;
    }

    /**
     * 广度优先搜索
     *
     * @param $tree
     * @param $node
     * @return int[]|string[]
     */
    public static function bfs($tree, $node): array
    {
        $queue     = [$node];
        $usedQueue = [$node => true];

        while (!empty($queue)) {
            $node = array_shift($queue);

            if (!isset($tree[$node])) continue;

            foreach ($tree[$node] as $childNode) {
                if (isset($usedQueue[$childNode])) continue;

                $queue[]               = $childNode;
                $usedQueue[$childNode] = true;
            }
        }

        return array_keys($usedQueue);
    }
}
<?php

namespace app\logic\advertise;

use app\service\Advertiser\AdDashLiveProvider;

class AdDashLiveLogic
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $paginate
     * @param array $columns
     * @param bool  $isApiRule
     * @return array
     * @throws \Smarty\Exception
     */
    public function tableBase(
        array $params,
        array $groups = [],
        array $sort = [],
        array $paginate = [],
        array $columns = [],
        bool  $isApiRule = false
    ): array
    {
        return (new AdDashLiveProvider())->getData(...func_get_args());
    }
}
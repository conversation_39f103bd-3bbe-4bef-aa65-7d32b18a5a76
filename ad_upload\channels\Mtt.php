<?php

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

/**
 * 芈兔兔(cps)
 */
class Mtt extends AdBaseInterface
{
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }

    /**
     * @param $info
     * @param $type
     *
     * @return void
     */
    private function upload($info, $type)
    {
        $callbackUri = \urldecode($info['CALLBACK_URL']);
        $http        = new Http($callbackUri);
        $res         = $http->get();

        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'mttt';
        $logInfo['request']      = json_encode(['url' => $callbackUri]);
        //记录上报结果
        $logInfo['response'] = $res;
        $resArr              = json_decode($res, true);
        if (isset($resArr['error']) && $resArr['error'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }

        $this->log($info, $logInfo, $res, $callbackUri);
    }

    public function uploadRegister($info, $ext = [])
    {
        // TODO: Implement uploadRegister() method.
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadPay($info, $ext = [])
    {
        // TODO: Implement uploadPay() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\Traits\InternalRoutes;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\CollectHelper;
use app\service\AdvertiseLive\LiveAnchorServ;
use Plus\MVC\Controller\JsonController;

/**
 * 主播信息管理
 *
 * @route /advertise/live-anchor/*
 */
class LiveAnchorController extends JsonController
{
    use InternalRoutes;

    /**
     * @return array
     */
    public function listAction(): array
    {
        $params = $this->wrapParams(\Plus::$app->request);
        $serv   = new LiveAnchorServ();
        $list   = $serv->getList(['state' => 1], [], [], ['sorted' => 'DESC']);
        $list   = Arr::crossMap($list, ['id' => 'key', 'anchor_name' => 'val']);

        return $this->success([
            'list' => $list,
        ]);
    }

    /**
     * @return array
     */
    public function updateSortedAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);
        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $orderList  = $params['list'];
        $serv       = new LiveAnchorServ();
        $updateList = [];
        $count      = \count($orderList);

        foreach ($orderList as $id) {
            $t            = [
                'id'     => $id,
                'sorted' => $count--,
            ];
            $updateList[] = $t;
        }

        try {
            $serv->updateMultiById($updateList);
        }
        catch (\Throwable $e) {
            return $this->error('更新失败');
        }
        return $this->success([]);
    }

    /**
     * @return array
     */
    public function saveAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        if (empty(trim($params['anchor_name']))) {
            return $this->error('缺失必要参数');
        }
        $serv = new LiveAnchorServ();

        try {
            $params['anchor_name'] = trim($params['anchor_name']);
            $params['operator_id'] = \Plus::$service->admin->getUserId();
            $serv->insert($params);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

    /**
     * @return array
     */
    public function updateAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);
        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $serv = new LiveAnchorServ();

        try {
            $data                = $params;
            $data['operator_id'] = \Plus::$service->admin->getUserId();
            $serv->updateMultiById([$data]);
        }
        catch (\Throwable $e) {
            return $this->error('更新时间');
        }

        return $this->success([]);
    }

    /**
     * @return array
     */
    public function delAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $id = $params['id'] ?? null;
        if (empty($id)) {
            return $this->error('参数缺失');
        }
        $serv = new LiveAnchorServ();

        try {
            $serv->removeByIds(Arr::wrap($id));
        }
        catch (\Throwable $e) {
            return $this->error('删除失败');
        }

        return $this->success([]);
    }
}
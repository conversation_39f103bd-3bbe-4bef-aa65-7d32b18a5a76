<?php

namespace app\apps\logs\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\log\DeductionReportLogic;
use PhpParser\Node\Expr\AssignOp\Plus;
use Smarty\Exception;

class DeductionReportController extends BaseTableController
{

    /**
     * @param Collection $params
     * @return array
     * @throws Exception
     */
    protected function data(Collection $params): array
    {
        $paginate = [
            'page'      => $params->pull('page', 1),
            'page_size' => $params->pull('page_size', 100),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = ['t1.time' => 'DESC'];
        }

        return (new DeductionReportLogic())->tableList($params->toArray(), $paginate, $sort);
    }

    /**
     * @param Collection $params
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                ],
            ],
        ];

        $fields = [
            ['title' => '规则类型', 'dataIndex' => 'rule_type', 'classify' => ['attrs', 'base']],
            ['title' => '规则名称', 'dataIndex' => 'report_rule_name', 'classify' => ['attrs', 'base']],
            ['title' => '核心账号', 'dataIndex' => 'core_account', 'sorter' => true, 'classify' => ['attrs', 'base']],
            ['title' => '归因计划', 'dataIndex' => 'plan_name', 'classify' => ['attrs', 'base']],
            ['title' => '归因推广子渠道', 'dataIndex' => 'channel_id', 'classify' => ['attrs', 'base']],
            ['title' => '包号', 'dataIndex' => 'package_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            ['title' => '游戏原名', 'dataIndex' => 'cp_game_id', 'classify' => ['attrs', 'base']],
            ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'classify' => ['attrs', 'base']],
            ['title' => '手盟订单号', 'dataIndex' => 'order_id', 'classify' => ['attrs', 'base']],
            ['title' => '上报结果', 'dataIndex' => 'report_result', 'classify' => ['attrs', 'base']],
            ['title' => '订单金额', 'dataIndex' => 'money', 'sorter' => true, 'classify' => ['attrs', 'base']],
            ['title' => '实际上报金额', 'dataIndex' => 'money_report', 'sorter' => true, 'classify' => ['attrs', 'base']],
            ['title' => '上报时间', 'dataIndex' => 'report_time', 'sorter' => true, 'classify' => ['attrs', 'base']],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

}
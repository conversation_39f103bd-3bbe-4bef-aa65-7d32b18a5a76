<?php

namespace app\service\AdvertiserData\Components\MatchParams\Traits;

use app\models\AdpPlatform\TbAdpPlanBase;
use app\service\AdvertiserData\Components\Helpers\Convert;

trait PlanMatch
{
    /**
     * 数据范围
     *
     * @param $params
     * @return string
     */
    protected function matchDataScope($params): string
    {
        if (empty($params['data_scope'])) return '';

        $field = $this->getField('is_ad_data');

        $data = (int)$params['data_scope'];

        if ($data === 1) {
            return "{$field} = 1";
        }
        elseif ($data === 2) {
            return "{$field} = 0";
        }

        return '';
    }

    /**
     * 计划名搜索
     *
     * @param $params
     * @return string
     */
    protected function matchPlanName($params): string
    {
        if (empty($params['plan_name'])) return '';

        $field = $this->getField('plan_id');
        $data  = $params['plan_name'];
        $planids = (new TbAdpPlanBase())->getPlanIdByName($data,$params["range_date_start"],$params["range_date_end"]);
        if($planids){
            return "({$field} IN ('".implode("','",$planids)."'))";
        }
        return "";
    }

    /**
     * 计划名搜索
     *
     * @param $params
     * @return string
     */
    protected function matchPlanNameSvLinkName($params): string
    {
        if (empty($params['plan_name'])) return '';

        $field = $this->getField('plan_id');
        $data  = $params['plan_name'];
        $planids = (new TbAdpPlanBase())->getPlanIdByName($data,$params["range_date_start"],$params["range_date_end"]);
        if($planids){
            return "({$field} IN ('".implode("','",$planids)."'))";
        }
        return "({$field} ='error' )";
    }

    /**
     * ### 计划ID搜索
     * **注意null也会作为筛选条件**
     * @param $params
     * @return string
     */
    protected function matchPlanId($params): string
    {
        if (!array_key_exists('plan_id', $params)) return '';

        $field = $this->getField('plan_id');

        if (is_null($params['plan_id'])) {
            return "{$field} IS NULL";
        }

        $data  = Convert::convertInString($params['plan_id']);

//        return "IF({$field} is not null,{$field},`svlink`.`id`) IN ({$data})";

        return "{$field} in ({$data})";
    }

}
<?php

namespace app\service\Logs\Components\Matcher;

use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\SelectQuery;

class RoleLoginMatch extends BaseLogMatch
{
    protected function matchFnList(): array
    {
        $new = [
            'game_server' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'role_id'     => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'role_name'   => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::likeBuild($qb, $key, $value);
            },
        ];

        return array_merge(parent::matchFnList(), $new);
    }
}
<?php

namespace app\apps\operator\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\operator\ConvertDataLogic;

/**
 * @description  转端数据看板
 */
class ConvertDataDashController extends BaseTableController
{

    /**
     * @param Collection $params
     * @return array
     * @throws \Smarty\Exception
     */
    protected function data(Collection $params): array
    {

        return (new ConvertDataLogic())->getList($params->toArray());
    }

    /**
     * @param Collection $params
     * @return array[]
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'android_base', 'label' => '安卓用户指标'],
                    ['value' => 'ios_base', 'label' => 'IOS用户指标'],
                ],
            ],
        ];

        return [
            'fields'   => [
                ['title' => '日期', 'dataIndex' => 'tday', 'classify' => ['attrs', 'base'], 'sorter' => true],
                ['title' => '游戏原名', 'dataIndex' => 'cp_game_id', 'classify' => ['attrs', 'base'], 'sorter' => true],
                ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'classify' => ['attrs', 'base'], 'sorter' => true],
                ['title' => '安卓首登用户数', 'dataIndex' => 'android_firstlogin_user', 'classify' => ['attrs', 'android_base']],
                ['title' => 'IOS首登用户数', 'dataIndex' => 'ios_firstlogin_user', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => '安卓广告新增用户数', 'dataIndex' => 'android_ad_new_user', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => 'IOS广告新增用户数', 'dataIndex' => 'ios_ad_new_user', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => '安卓原生流水', 'dataIndex' => 'android_source_transaction_records', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => '安卓转端流水', 'dataIndex' => 'android_covert_transaction_records', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => '安卓转端占比', 'dataIndex' => 'android_convert_percentage', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => '安卓当月新用户转端流水', 'dataIndex' => 'android_new_user_transaction_records_month', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => '安卓原生流水占比', 'dataIndex' => 'android_source_records_percentage', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => 'IOS流水占比', 'dataIndex' => 'ios_records_percentage', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS原生流水', 'dataIndex' => 'ios_source_transaction_records', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS转端流水', 'dataIndex' => 'ios_convert_transaction_records', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS转端占比', 'dataIndex' => 'ios_convert_percentage', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS当月新用户转端流水', 'dataIndex' => 'ios_new_user_transaction_records_month', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS原生流水占比', 'dataIndex' => 'ios_source_records_percentage', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => '安卓转端新用户', 'dataIndex' => 'android_convert_new_user', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => '安卓转端充值用户', 'dataIndex' => 'android_convert_paid_user', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => '安卓转端活跃用户', 'dataIndex' => 'android_convert_active_user', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => 'IOS转端新用户', 'dataIndex' => 'ios_convert_new_user', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS转端充值用户', 'dataIndex' => 'ios_convert_paid_user', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS转端活跃用户', 'dataIndex' => 'ios_convert_active_user', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => '安卓转端活跃用户(300<累充<=1000)', 'dataIndex' => 'android_active_paid_l1', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => '安卓转端活跃用户(1000<累充<=3000)', 'dataIndex' => 'android_active_paid_l2', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => '安卓转端活跃用户(3000<累充<=5000)', 'dataIndex' => 'android_active_paid_l3', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => '安卓转端活跃用户(累充>5000)', 'dataIndex' => 'android_active_paid_l4', 'classify' => ['attrs', 'android_base'], 'sorter' => true],
                ['title' => 'IOS转端活跃用户(300<累充<=1000)', 'dataIndex' => 'ios_active_paid_l1', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS转端活跃用户(1000<累充<=3000)', 'dataIndex' => 'ios_active_paid_l2', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS转端活跃用户(3000<累充<=5000)', 'dataIndex' => 'ios_active_paid_l3', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
                ['title' => 'IOS转端活跃用户(累充>5000)', 'dataIndex' => 'ios_active_paid_l4', 'classify' => ['attrs', 'ios_base'], 'sorter' => true],
            ],
            'classify' => $classify,
        ];
    }
}
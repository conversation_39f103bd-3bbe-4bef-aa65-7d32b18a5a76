<?php

namespace app\logic\advertise;

use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\extension\Support\Helpers\RemainCalculators;
use app\extension\Support\Helpers\Zakia;
use app\logic\advertise\Traits\RemainUtilTrait;
use app\service\AdvertiserData\AdPayRemainServ;
use app\service\AdvertiserData\AdPlanBaseServ;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\ConfigService\BasicServ;
use app\service\ConfigService\Tables\AdPlan;
use app\service\General\GeneralOptionServ;

class AdPlanPayRemainLogic
{
    use ColumnsInteract, RemainUtilTrait;

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \RedisException
     * @throws \Exception
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        if (empty($groups)) {
            $groups = ['tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id'];
        }

        $readCols = [
            'tday', 'cp_game_id', 'game_id', 'link_mark',
            'app_show_id', 'platform_id', 'tztz_promotion_channel_id',
            'tztz_channel_main_id', 'package_id',
            'promotion_id', 'campaign_id', 'plan_id',
            'tztz_department_id', 'tztz_user_id', 'pay_new_user_7days',
            'ad_account', 'account_id', 'account_name', 'marketing_goal',
            'is_appointment', 'is_ad_data', 'dim_user_os'
        ];

        if (
            !empty($params['range_date_start'])
            && !empty($params['range_date_end'])
        ) {
            $params['tday'] = [
                $params['range_date_start'], $params['range_date_end']
            ];
            sort($params['tday']);
            unset($params['range_date_start'], $params['range_date_end']);
        }

        // 获取基础数据调整
        $baseServ = new AdPlanBaseServ();
        $mode     = $baseServ::MODE_SUMMARY | $baseServ::MODE_TOTAL | $baseServ::MODE_DETAIL_QB | $baseServ::MODE_DETAIL;
        $baseRe   = $baseServ->getListForBigData($params, $groups, $paginate, $sort, $readCols, $mode);
        $baseQb   = $baseRe['detail_qb'];

        // 留存查询
        $remainServ = new AdPayRemainServ();
        $remainRe   = $remainServ->getListWithInfoQb($baseQb, $params, $groups, []);

        unset($baseRe['detail_qb']);

        // 数据处理流程
        $infoList   = &$baseRe['list'];
        $remainList = &$remainRe['list'];

        $tmpInfoGroupMap = [
            'channel_main_id'      => 'tztz_channel_main_id',
            'channel_id'           => 'tztz_promotion_channel_id',
            'promotion_channel_id' => 'tztz_promotion_channel_id',
            'user_id'              => 'tztz_user_id',
            'department_id'        => 'tztz_department_id',
        ];

        $tmpGroup   = array_map(fn($item) => $tmpInfoGroupMap[$item] ?? $item, $groups);
        $infoList   = Zakia::appendKeysWithDimension($infoList, $tmpGroup);
        $remainList = Zakia::appendKeysWithDimension($remainList, $tmpGroup);

        $configBasic      = new BasicServ();
        $constConfCollect = (new BasicServ())->getMultiOptions([
            'platform_id', 'promotion_id', 'department_id', 'user_id',
            'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
        ])->put('promotion_channel_id', (new GeneralOptionServ())->listChannelOptions());

        $resetFunc = $this->resetGroupsCols(
            ColumnManager::groupAdRelation($groups),
            $groups,
            array_diff(ConstHub::AD_FIXED_INFO_COLS, ['creative_id', 'creative_name'])
        );

        $summaryGetFn = fn($target, $i) => $target['pay_new_user_7days_' . $i] ?? 0;

        // 获取付费留存分母方式
        if (in_array('tday', $groups)) {
            $getDenominatorFn = fn($target) => $target['pay_new_user_7days'] ?? 0;
        }
        else {
            $getDenominatorFn = fn($target, $i) => $target['pay_new_user_7days_' . $i] ?? 0;
        }

        $works = new ProcessLine();
        // 根据汇总分组补全广告信息
        [$adInfo, $dimension] = (new RealtimeIndex())->getAdInfoByGroups($infoList, $groups);
        if ($dimension) {
            $works->addProcess(function (&$target, $k) use ($adInfo, $dimension) {
                $fields = [];
                switch ($dimension) {
                    case "campaign_id":
                        $fields = ["CAMPAIGN_NAME"];
                        break;
                    case "plan_id":
                        $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME"];
                        break;
                    case "creative_id":
                        $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME", "PLAN_ID", "CREATIVE_NAME"];
                        break;
                }
                foreach ($fields as $field) {
                    $key = $target[$dimension];
                    if ($dimension == "creative_id") {
                        $key = $target["plan_id"] . "_" . $target["creative_id"];
                    }
                    $target[strtolower($field)] = $adInfo[$key][$field] ?? null;
                }
            });
        }

        $minDay     = min($params['tday']);
        $maxDayNode = max(days_apart(date('Y-m-d'), $minDay) - 1, 0);

        $works
            ->addProcess(RemainCalculators::calcNode($remainList, $getDenominatorFn, $params['remain_type'] ?? 0, 'pay_new_user_7days'))
            ->addProcess($this->resetTzTz())
            ->addProcess($this->replaceColumnDefine($constConfCollect))
            ->addProcess($this->appendRemainDays($maxDayNode, in_array('tday', $groups)))
            ->addProcess($resetFunc);


        $works->run($infoList);

        $summaryRow                = &$baseRe['summary'];
        $summaryRemain             = &$remainRe['summary'];
        $summaryRow['remain_days'] = $maxDayNode;
        $summaryWorks              = new ProcessLine();
        $summaryWorks
            ->addProcess(RemainCalculators::calcNode(['summary' => $summaryRemain[0] ?? []], $summaryGetFn, $params['remain_type'] ?? 0, 'pay_new_user_7days'));

        $tmpSummary = ['summary' => &$summaryRow];
        $summaryWorks->run($tmpSummary);

        return [
            'list'    => array_values($baseRe['list']),
            'summary' => $baseRe['summary'],
            'total'   => $baseRe['total'],
            'time'    => $baseRe['summary']['last_update_time'] ?? ''
        ];
    }

    /**
     * 重置临时前缀的数据
     * @return \Closure
     */
    private function resetTzTz(): \Closure
    {
        return function (&$target) {
            foreach ($target as $k => $v) {
                if (str_contains($k, 'tztz_')) {
                    $nk          = str_replace('tztz_', '', $k);
                    $target[$nk] = $v;
                    unset($target[$k]);
                }
            }
        };
    }

}
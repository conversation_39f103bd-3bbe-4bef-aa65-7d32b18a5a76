<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="phplint-ruleset">
    <description>手盟php编码规范</description>
    <!-- 排除目录 -->
    <exclude-pattern>*vendor*</exclude-pattern>
    <exclude-pattern>*.html.php</exclude-pattern>
    <exclude-pattern>*.blade.php</exclude-pattern>

    <arg name="basepath" value="."/>
    <arg name="colors"/>
    <arg name="parallel" value="75"/>
    <arg value="np"/>
    <!-- Don't hide tokenizer exceptions -->
    <rule ref="Internal.Tokenizer.Exception">
        <type>error</type>
    </rule>

    <!-- psr-1编码规范 -->
    <rule ref="PSR1"/>
    <!-- psr-2编码规范 -->
    <rule ref="PSR2"/>


    <!-- 不允许长array() 语法，只能用 [], 只能在php >= 5.6 -->
    <rule ref="Generic.Arrays.DisallowLongArraySyntax"/>
    <!-- 自定义array缩进规则 -->
    <rule ref="Generic.Arrays.ArrayIndent"/>
    <rule ref="Squiz.Arrays.ArrayBracketSpacing"/>
    <rule ref="Squiz.Arrays.ArrayDeclaration.KeyNotAligned">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.Arrays.ArrayDeclaration.ValueNotAligned">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.Arrays.ArrayDeclaration.CloseBraceNotAligned">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.Arrays.ArrayDeclaration.CloseBraceNewLine">
        <severity>0</severity>
    </rule>
    <!--    不允许空语句-->
    <rule ref="Generic.CodeAnalysis.EmptyStatement"/>
    <!--    圈复杂度，默认20: 一个函数的圈复杂度就相当于至少需要多少个测试用例才能对这个函数做到全路径覆盖-->
    <rule ref="Generic.Metrics.CyclomaticComplexity">
        <properties>
            <property name="absoluteComplexity" value="20"/>
        </properties>
    </rule>
    <!--    嵌套层级，默认10-->
    <rule ref="Generic.Metrics.NestingLevel">
        <properties>
            <property name="absoluteNestingLevel" value="10"/>
        </properties>
    </rule>
    <!--类必须有注释，必须有 <AUTHOR>    -->
    <rule ref="Squiz.Commenting.ClassComment"/>
    <rule ref="PEAR.Commenting.ClassComment.MissingAuthorTag"/>
    <!--函数必须要写注释    -->
    <rule ref="PEAR.Commenting.FunctionComment"/>
    <!-- 类、属性、方法必须有短注释 -->
    <rule ref="Generic.Commenting.DocComment.MissingShort"/>
    <rule ref="Squiz.Commenting.VariableComment.Missing"/>

    <!-- Have code block comments look like // end foreach() etc. -->
    <rule ref="Squiz.Commenting.LongConditionClosingComment">
        <properties>
            <!--行数条件，默认20-->
            <property name="lineLimit" value="20"/>
            <!--结束 注释 end %()-->
            <property name="commentFormat" value="// end %s()"/>
        </properties>
    </rule>

    <!-- 禁用的函数 -->
    <rule ref="Generic.PHP.ForbiddenFunctions">
        <properties>
            <property name="forbiddenFunctions" type="array">
                <element key="sizeof" value="count"/>
                <element key="delete" value="unset"/>
                <element key="print" value="echo"/>
                <element key="create_function" value="null"/>
                <element key="exit" value="null"/>
                <element key="print_r" value="null"/>
                <element key="var_dump" value="null"/>
            </property>
        </properties>
    </rule>

    <!--多行语句对齐    -->
    <rule ref="Generic.Formatting.MultipleStatementAlignment">
        <properties>
            <property name="ignoreMultiLine" value="true"/>
            <property name="error" value="true"/>
        </properties>
    </rule>

</ruleset>
<?php

namespace app\apps\operator\Traits;

use app\extension\Support\Collections\Collection;

trait OperatorRequest
{
    /**
     * 请求参数限制
     *
     * @param $key
     *
     * @return Collection
     */
    protected function registerParams($key = null): Collection
    {
        $today = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start', 'default' => $today], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
            ['field' => 'range_date_dimension', 'default' => 2], // 时间维度, 1|null-时, 2-日, 3-周, 4-月
            ['field' => 'thour'], // 小时
            ['field' => 'cp_game_id',], // 游戏原名
            ['field' => 'game_id',], // 游戏统计名
            ['field' => 'app_show_id',], // 游戏前端名
            ['field' => 'channel_main_id',], // 主渠道
            ['field' => 'channel_id',], // 渠道
            ['field' => 'promotion_channel_id',], // 推广渠道
            ['field' => 'platform_id',], // 客户端ID
            ['field' => 'package_id',], // 包号
            ['field' => 'promotion_id',], // 推广分类
            ['field' => 'department_id',], // 部门
            ['field' => 'user_id',], // 投放人
            ['field' => 'has_no_cost',], // 是否含无消费
            ['field' => 'has_visual_pay',], // 是否含虚拟币
            ['field' => 'ltv_type'], // ltv值类型
            ['field' => 'remain_type'], // remain值类型 ['field' => 'column_scope', 'default' => ['ltv', 'roi']], // 报表展示类型，可单独展示LTV/ROI
            ['field' => 'search_mode', 'default' => ['normal']], // 查询关联模式,控制关联的方式
            ['field' => 'max_day_type', 'default' => -1], // 最大节点限制
            ['field' => 'column_scope', 'default' => ['ltv', 'roi']], // 报表展示类型，可单独展示LTV/ROI
            ['field' => 'max_data_day_ago', 'default' => 0], // 最大数据日期 0当天 1昨天 2前天 如此类推，一般用于ltv/roi、留存获取最大日期用
            ['field' => 'channel_id_tags'], // 渠道标签
            ['field' => 'channel_main_id_tags'], // 主渠道标签
            ['field' => 'package_id_tags'], // 包号标签
            ['field' => 'game_id_tags'], // 包号标签
            ['field' => 'pay_days_num'], // 首日统计天数

            ['field' => 'page_size', 'default' => 50],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }
}
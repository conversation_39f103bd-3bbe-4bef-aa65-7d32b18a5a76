<?php

namespace app\service\OperationData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\util\Common;
use Smarty\Exception;

/**
 *
 */
class FirstLoginDashProvider
{
    const RESULT_INFO = 1;
    const RESULT_SUMMARY = 2;
    const RESULT_TOTAL = 4;
    const RESULT_ALL = 7;

    /**
     * @throws Exception
     * @throws \Exception
     */
    public function getData(
        array $params, array $groups = [], array $paginate = [], array $sort = [], array $columns = [], int $resultMode = self::RESULT_ALL
    ): array
    {
        $result        = [];
        $db            = $this->getConn();
        $powerSQL      = \Plus::$service->admin->powerSubSQL();
        $timeDimension = Arr::pull($params, 'range_date_dimension', 2);

        if ($resultMode & self::RESULT_INFO) {
            if ($timeDimension == 3) {
                $tplName = 'firstlogin_dash_daily';
            }
            elseif ($timeDimension == 4) {
                $tplName = 'firstlogin_dash_month';
            }
            else {
                $tplName = 'firstlogin_dash_daily';
            }

            $infoTpl = \Plus::$app->sqlTemplates->createTemplate("sql/operator/firstlogin_dash/{$tplName}.tpl");

            if ($timeDimension == 3) {
                [
                    'begin' => $rangeDateStart,
                    'end'   => $rangeDateEnd,
                    'cycle' => $rangeDateCycle,
                ] = TimeUtil::divideWeekByRangeDate(...$params['range_date']);

                $params['range_date'] = [$rangeDateStart, $rangeDateEnd];
                $infoTpl->assign('range_days', $rangeDateCycle);
            }

            $infoTpl
                ->assign('power_join_sql', $powerSQL)
                ->assign('params', $params)
                ->assign('time_range_type', $timeDimension)
                ->assign('groups', $groups);

            if (!empty($paginate))
                $infoTpl->assign('paginate', $paginate);
            if (!empty($sort))
                $infoTpl->assign('sorts', $sort);
            if (!empty($columns))
                $infoTpl->assign('hit_fields', $columns);

            $infoSQL = $infoTpl->fetch();

            @Common::dumpSql($infoSQL);
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }

        if ($resultMode & self::RESULT_SUMMARY) {
            $summaryTpl = \Plus::$app->sqlTemplates->createTemplate("sql/operator/firstlogin_dash/{$tplName}.tpl");

            if ($timeDimension == 3) {
                [
                    'begin' => $rangeDateStart,
                    'end'   => $rangeDateEnd,
                    'cycle' => $rangeDateCycle,
                ] = TimeUtil::divideWeekByRangeDate(...$params['range_date']);

                $params['range_date'] = [$rangeDateStart, $rangeDateEnd];
                $summaryTpl->assign('range_days', $rangeDateCycle);
            }

            $summaryTpl
                ->assign('power_join_sql', $powerSQL)
                ->assign('time_range_type', $timeDimension)
                ->assign('params', $params);
            $summarySQL = $summaryTpl->fetch();
            @Common::dumpSql($summarySQL);
            $summaryResult     = $db->query($summarySQL)->fetch();
            $result['summary'] = $summaryResult;
            $result['time']    = $summaryResult['update_time'] ?? '';
        }

        if ($resultMode & self::RESULT_TOTAL) {
            $totalTpl = \Plus::$app->sqlTemplates->createTemplate("sql/operator/firstlogin_dash/{$tplName}_total.tpl");
            $totalTpl
                ->assign('power_join_sql', $powerSQL)
                ->assign('params', $params)
                ->assign('groups', $groups);
            $totalSQL = $totalTpl->fetch();

            $result['total'] = ($db->query($totalSQL)->fetch())['total_row'] ?? 0;
        }

        return $result;
    }

    /**
     * 获取数据库链接(doris)
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('doris_entrance');
    }
}
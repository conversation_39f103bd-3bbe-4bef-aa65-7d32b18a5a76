<?php

namespace app\ad_upload_tmp\contract;

/**
 * 上报日志记录到doris
 * <AUTHOR>
 */
trait LogDbTrait
{
    /**
     * 上报记录到doris
     * @param array $info data
     * @return void
     */
    private function logDoris($info)
    {
        $info = array_change_key_case($info);

        $sql = sprintf(
            "
            INSERT INTO bigdata_dwd.dwd_reported_platform_log 
            (package_id, channel_id, action, time, dt, cp_game_id, game_id, channel_code, 
             core_account,oaid, content, response_content, action_id, reported_status)
            VALUES (%s, %s, '%s', '%s','%s', %s, %s, '%s', '%s', '%s', '%s','%s',%s,%s)",
            $info['package_id'] ?? 0,
            $info['channel_id'] ?? 0,
            $info['action'] ?? '',
            $info['time'] ?? date('Y-m-d H:i:s'),
            $info['dt'] ?? date('Y-m-d'),
            $info['cp_game_id'] ?? 0,
            $info['game_id'] ?? 0,
            $info['channel_code'] ?? '',
            $info['core_account'] ?? '',
            $info['oaid'] ?? '',
            $info['request'] ?? '',
            $info['response'] ?? '',
            $info['action_id'] ?? ($info['id'] ?? 0),
            $info['reported_status'] ?? 1
        );
        \Plus::$app->doris_entrance->exec($sql);
        \Plus::$app->doris_entrance2->exec($sql);
    }

    /**
     * 支付日志
     * @param array  $info        上报数据
     * @param array  $logInfo     `bigdata_dwd`.`dwd_reported_platform_log` 日志
     * @param string $callbackUrl 回调地址
     * @return void
     */
    protected function logPaidToDoris($info, $logInfo, $callbackUrl)
    {
        if (empty($info['paid_report_log'])) {
            return;
        }
        $paidReportLog                     = $info['paid_report_log'];
        $paidReportLog['callback_url']     = $callbackUrl;
        $paidReportLog['content']          = $logInfo['request'] ?? '';
        $paidReportLog['response_content'] = $logInfo['response'] ?? '';
        $paidReportLog['channel_code']     = $logInfo['channel_code'] ?? '';
        $paidReportLog['reported_status']  = $logInfo['reported_status'] ?? 0;
        $paidReportLog['reported_money']   = $info['paid_report_log']['reported_money'] ?? $info['MONEY'];

        $data = array_change_key_case($paidReportLog);
        $key  = 'ad_up_repeat_pay';
        \Plus::$app->redis82->hset($key, $data['order_id'], 1);
        $dt  = date('Y-m-d', strtotime($data['pay_time']));
        $sql = sprintf(
            "INSERT INTO bigdata_dwd.dwd_reported_paid_platform_log 
                (order_id, pay_result, reported_status, actually_money, 
                 reported_money, game_id, package_id, channel_id, oaid, device_key, pay_time, 
                  `core_account` , `source_id`, `source_dimension`, reported_behavior, 
                 reported_rule_id, reported_behavior_rule, no_reported_origin, callback_url, content,
                 response_content,cp_game_id, channel_code, `time`, dt, create_time, update_time) 
                VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', 
                 '%s', '%s', '%s' , '%s', '%s', '%s', '%s', '%s', '%s','%s','%s','%s', NOW(), '%s', NOW(), NOW())",
            ($data['order_id'] ?? '0'),
            ($data['pay_result'] ?? 1),
            ($data['reported_status'] ?? 0),
            ($data['actually_money'] ?? 0),
            ($data['reported_money'] ?? 0),
            ($data['game_id'] ?? 0),
            ($data['package_id'] ?? 0),
            ($data['channel_id'] ?? 0),
            ($data['oaid'] ?? ''),
            ($data['device_key'] ?? ''),
            ($data['pay_time'] ?? ''),
            ($data['core_account'] ?? ''),
            ($data['source_id'] ?? ''),
            ($data['source_dimension'] ?? ''),
            ($data['reported_behavior'] ?? 0),
            ($data['reported_rule_id'] ?? '-1'),
            ($data['reported_behavior_rule'] ?? null),
            ($data['no_reported_origin'] ?? ''),
            ($data['callback_url'] ?? ''),
            ($data['content'] ?? ''),
            ($data['response_content'] ?? ''),
            ($data['cp_game_id'] ?? ''),
            ($data['channel_code'] ?? ''),
            $dt,
        );
        \Plus::$app->doris_entrance->exec($sql);
        \Plus::$app->doris_entrance2->exec($sql);
    }
}

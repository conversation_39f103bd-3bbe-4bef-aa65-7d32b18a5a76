{function ShowRetainFn show_type=1 current_index=0}
    {if $show_type eq 2}
        ROUND( SUM(IF(day_type={$current_index}, login_num, 0)) as remain_{$current_index},
    {elseif $show_type eq 3}
        CONCAT(ROUND( SUM(IF(day_type={$current_index}, login_num, 0)) , '(', ROUND( SUM(IF(day_type={$current_index}, login_num, 0)) / SUM(IF(day_type <= DATEDIFF(DATE(NOW()), tday) , new_user, 0 )) * 100, 2 ),')') as remain_{$current_index},
    {else}
        ROUND( SUM(IF(day_type={$current_index}, login_num, 0)) / SUM(IF(day_type <= DATEDIFF(DATE(NOW()),tday) , new_user, 0 )) * 100, 2 ) as remain_{$current_index},
    {/if}
{/function}

{include file="sql/advertise/ad_dashboard/creative_dash_alpha.tpl"}
,
creative_beta_info as (
    select
        u1.tday,
        u1.package_id,
        u1.promotion_channel_id,
        u1.campaign_id,
        u1.plan_id,
        u1.creative_id,
        any(u1.campaign_name) as campaign_name,
        any(game_id)          as game_id,
        any(cp_game_id)       as cp_game_id,
        any(dim_user_id)      as dim_user_id,
        any(u1.platform_id)   as platform_id,
        any(u1.app_show_id)   as app_show_id,
        any(channel_main_id)  as channel_main_id,
        any(department_id)    as department_id,
        any(u1.promotion_id)  as promotion_id,
        any(u1.account_id)    as account_id,
        any(u1.account_name)  as account_name,
        any(u1.ad_account)    as ad_account,
        any(u1.dim_user_os)   as dim_user_os,
        SUM(u1.new_user)      as new_user
    from dashboard_info u1
    left join base_conf_platform.tb_base_channel_conf u2 on u1.promotion_channel_id = u2.channel_id
    left join dataspy.admin_user u3 on u1.dim_user_id = u3.id
    {if !empty($params)}
        {assign var="first_mark" value=1}
        {foreach $params as $key => $item}
            {if $key eq 'channel_id'}
                {if !$first_mark} and {else} where {$first_mark=0} {/if}
                {if is_array($item)}
                    u1.promotion_channel_id  in ({$item|join:','})
                {else}
                    u1.promotion_channel_id  = '{$item}'
                {/if}
            {/if}
            {if $key eq 'channel_id_tags'}
                {if !$first_mark} and {else} where {$first_mark=0} {/if}
                u1.promotion_channel_id in (select distinct data_id from base_conf_platform.biz_tags where tag_id in ({$item|join:','}) and table_name = 'app_channel')
            {/if}
            {if $key eq 'channel_main_id'}
                {if !$first_mark} and {else} where {$first_mark=0} {/if}
                {if is_array($item)}
                    channel_main_id in ({$item|join:','})
                {else}
                    channel_main_id = {$item}
                {/if}
            {/if}
            {if $key eq 'channel_main_id_tags'}
                {if !$first_mark} and {else} where {$first_mark=0} {/if}
                channel_main_id in (select distinct data_id from base_conf_platform.biz_tags where tag_id in ({$item|join:','}) and table_name = 'package_channel_main')
            {/if}
            {if $key eq 'user_id'}
                {if !$first_mark} and {else} where {$first_mark=0} {/if}
                {if is_array($item)}
                    u1.dim_user_id in ({$item|join:','})
                {else}
                    u1.dim_user_id = {$item}
                {/if}
            {/if}
            {if $key eq 'department_id'}
                {if !$first_mark} and {else} where {$first_mark=0} {/if}
                {if is_array($item)}
                    u3.department_id in ({$item|join:','})
                {else}
                    u3.department_id = {$item}
                {/if}
            {/if}
        {/foreach}
    {/if}
    group by u1.tday, u1.package_id, u1.promotion_channel_id, u1.campaign_id, u1.plan_id, u1.creative_id
)
select
{if empty($max_retain_node)}
    {assign var="max_retain_node" value=720}
{/if}
{for $foo=1 to $max_retain_node}
    {call ShowRetainFn show_type=$show_type current_index=$foo}
{/for}
ROUND(SUM(IF(day_type=1000, login_num, 0)) / SUM(all_new_user) * 100, 2) as remain_current,
max(IF(day_type=1000, -999, day_type)) as remain_days,
SUM(all_new_user) as new_user,
max(update_time) as max_update_time
{if !empty($groups)}
    ,
    {foreach $groups as $item}
        {if $item eq 'tday'} tday{if !$item@last}, {/if}
        {elseif $item eq 'cp_game_id'} cp_game_id{if !$item@last}, {/if}
        {elseif $item eq 'game_id'} game_id{if !$item@last}, {/if}
        {elseif $item eq 'app_show_id'} app_show_id{if !$item@last}, {/if}
        {elseif $item eq 'channel_main_id'} channel_main_id{if !$item@last}, {/if}
        {elseif $item eq 'promotion_channel_id'} promotion_channel_id{if !$item@last}, {/if}
        {elseif $item eq 'promotion_id'} promotion_id{if !$item@last}, {/if}
        {elseif $item eq 'user_id'} dim_user_id as user_id{if !$item@last}, {/if}
        {elseif $item eq 'department_id'} department_id{if !$item@last}, {/if}
        {elseif $item eq 'package_id'} package_id{if !$item@last}, {/if}
        {elseif $item eq 'platform_id'} platform_id{if !$item@last}, {/if}
        {elseif $item eq 'account_id'} account_id{if !$item@last}, {/if}
        {elseif $item eq 'account_name'} account_name{if !$item@last}, {/if}
        {elseif $item eq 'ad_account'} ad_account{if !$item@last}, {/if}
        {elseif $item eq 'campaign_id'} campaign_id,any(campaign_name) as campaign_name{if !$item@last}, {/if}
        {elseif $item eq 'plan_id'} plan_id{if !$item@last}, {/if}
        {elseif $item eq 'creative_id'} creative_id{if !$item@last}, {/if}
        {elseif $item eq 'dim_user_os'} dim_user_os{if !$item@last}, {/if}
        {/if}
    {/foreach}
{/if}
from (
select
r1.*,
IF(day_type =
max(day_type) over (partition by tday, package_id, promotion_channel_id, campaign_id, plan_id, creative_id),
new_user, 0) as all_new_user
from (
select a2.*, COALESCE(a1.day_type, 0) as day_type, a1.login_num, a1.update_time
from (
select
tday,
z1.package_id,
{if isset($ad_channels)}
    COALESCE( IF(z2.channel_id not in ({$ad_channels|join:','}), z2.channel_id, IF(z1.channel_id != 0, IF(z1.channel_id =1013, 4, z1.channel_id), z2.channel_id) ) ,0) as channel_id,
{else}
    z1.channel_id as channel_id,
{/if}
IF(z1.campaign_id = 0, '', campaign_id) as campaign_id,
IF(z1.plan_id = 0, '', plan_id)         as plan_id,
IF(z1.creative_id = 0, '', creative_id) as creative_id,
day_type,
z1.cp_game_id,
z1.game_id,
main_channel_id,
login_num,
login_date,
z1.id,
z1.update_time
from  bigdata_tmp.dws_creative_ad_remain_daily_spy z1
join base_conf_platform.tb_package_detail_conf z2 on z1.package_id = z2.package_id
{if !empty($params)}
    {assign var="mark_first_2" value=1}
    {foreach $params as $kk => $foo}
        {if $kk eq "range_date"}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            z1.tday between '{$foo[0]}' and '{$foo[1]}' {continue}
        {/if}
        {if $kk eq 'cp_game_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                z1.cp_game_id in ({$foo|join:','})
            {else}
                z1.cp_game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                z1.game_id in ({$foo|join:','})
            {else}
                z1.game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id_tags'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            z1.game_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in ({$foo|join:','}) and table_name = 'games'
            )
            {continue}
        {/if}
        {if $kk eq 'package_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                z1.package_id in ({$foo|join:','})
            {else}
                z1.package_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'package_id_tags'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            z1.package_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in ({$foo|join:','}) and table_name = 'packages'
            )
            {continue}
        {/if}
        {if $kk eq 'last_retain_date'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            z1.login_date < '{$foo}'
            {continue}
        {/if}
    {/foreach}
{/if}
) a1
right join creative_beta_info a2
on a1.tday = a2.tday
and a1.package_id = a2.package_id
and a1.channel_id = a2.promotion_channel_id
and a1.campaign_id = a2.campaign_id
and a1.plan_id = a2.plan_id
and a1.creative_id = a2.creative_id
) r1
) rr1
{* 维度汇总 *}
{if !empty($groups)}
    group by
    {foreach $groups as $item}
        {if $item eq 'tday'} tday{if !$item@last}, {/if}
        {elseif $item eq 'cp_game_id'} cp_game_id{if !$item@last}, {/if}
        {elseif $item eq 'game_id'} game_id{if !$item@last}, {/if}
        {elseif $item eq 'app_show_id'} app_show_id{if !$item@last}, {/if}
        {elseif $item eq 'channel_main_id'} channel_main_id{if !$item@last}, {/if}
        {elseif $item eq 'promotion_channel_id'} promotion_channel_id{if !$item@last}, {/if}
        {elseif $item eq 'promotion_id'} promotion_id{if !$item@last}, {/if}
        {elseif $item eq 'user_id'} dim_user_id{if !$item@last}, {/if}
        {elseif $item eq 'department_id'} department_id{if !$item@last}, {/if}
        {elseif $item eq 'package_id'} package_id{if !$item@last}, {/if}
        {elseif $item eq 'platform_id'} platform_id{if !$item@last}, {/if}
        {elseif $item eq 'account_id'} account_id{if !$item@last}, {/if}
        {elseif $item eq 'account_name'} account_name{if !$item@last}, {/if}
        {elseif $item eq 'ad_account'} ad_account{if !$item@last}, {/if}
        {elseif $item eq 'campaign_id'} campaign_id{if !$item@last}, {/if}
        {elseif $item eq 'plan_id'} plan_id{if !$item@last}, {/if}
        {elseif $item eq 'creative_id'} creative_id{if !$item@last}, {/if}
        {elseif $item eq 'dim_user_os'} dim_user_os{if !$item@last}, {/if}
        {/if}
    {/foreach}
{/if}
{* 排序 *}
{if !empty($sorts)}
    order by
    {foreach $sorts as $ss => $oo}
        {$ss} {$oo}
        {if !$oo@last}, {/if}
    {/foreach}
{/if}
{* 分页 *}
{if !empty($paginate)}
    limit {$paginate.page_size} offset {$paginate.offset}
{/if}
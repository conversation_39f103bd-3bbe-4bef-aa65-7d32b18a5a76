<?php

namespace app\models\DdcPlatform;

use Plus\MVC\Model\ActiveRecord;


/**
 * @DwsSvKeyAdSplitDaily
 *          短链维度-广告新增(拆分自然量)
 *
 * @property int    ID
 * @property string TDAY
 * @property int    CP_GAME_ID
 * @property int    GAME_ID
 * @property int    PACKAGE_ID
 * @property int    CHANNEL_ID
 * @property int    SV_KEY
 * @property string ACCOUNT_ID
 * @property int    PAY_USER
 * @property int    PAY_MONEY
 * @property int    PAY_USER_NEW
 * @property int    PAY_MONEY_NEW
 * @property int    IS_NATURAL
 * @property string UPDATE_TIME
 */
class DwsSvKeyAdSplitDaily extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->ddc_platform;
        parent::__construct($data);
    }

}
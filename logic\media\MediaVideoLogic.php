<?php

namespace app\logic\media;

use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\Media\AudienceAnalyzeServ;
use app\service\Media\MediaAccountServ;
use app\service\Media\MediaVideoServ;

class MediaVideoLogic
{
    use ColumnsInteract;

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     *
     * @return array
     * @throws \Exception
     */
    public function getInfo(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $sort        = $this->orderReflect($sort);
        $infoRe      = (new MediaVideoServ())->getInfoList($params, $groups, $paginate, $sort);
        $list        = &$infoRe['list'];
        $summary     = &$infoRe['summary'];
        $setMap      = (new BasicServ())->getMultiOptions(['media_platform_id']);
        $platformMap = array_column($setMap->offsetGet('media_platform_id')->toArray(), 'val', 'key');

        if (!empty($groups)) {
            $resetGroupFn = $this->resetGroupsCols(
                $this->groupRelationMap($groups),
                $groups,
                [
                    'tday', 'account_id', 'publish_time',
                    'account_name', 'task_id',
                    'business_ownership', 'operations_manager',
                    'use_kind', 'media_platform_id', 'media_platform_id',
                    'media_video_title', 'media_video_topic',
                    'mc_id', 'mc_video_id',
                    'demo_video_id', 'demo_video_title',
                    'video_duration', 'task_script_name',
                    'kind', 'data_label', 'video_fitid', 'job_kind', 'video_topic',
                    'media_platform', 'business_ownership_name', 'use_kind_name',
                    'video_topic_source'
                ]
            );
        }
        else {
            $resetGroupFn = fn() => true;
        }

        $serv                 = new BasicServ();
        $keygen               = 'use_kind,business_ownership,job_kind';
        $result               = $serv->getMultiOptions($keygen);
        $useKindMap           = array_column($result->get('use_kind')->all(), 'val', 'key');
        $businessOwnershipMap = array_column($result->get('business_ownership')->all(), 'val', 'key');
        $jobKindMap           = array_column($result->get('job_kind')->all(), 'val', 'key');

        foreach ($list as &$item) {
            $platformId                      = $item['media_platform_id'] ?? '';
            $ownership                       = $item['business_ownership'] ?? '';
            $useKind                         = $item['use_kind'] ?? '';
            $jobKindId                       = $item['job_kind_id'] ?? '';
            $item['media_platform']          = $platformMap[$platformId] ?? '其他';
            $item['business_ownership_name'] = $businessOwnershipMap[$ownership] ?? $ownership;
            $item['use_kind_name']           = $useKindMap[$useKind] ?? $useKind;
            $item['video_topic_source']      = $item['video_topic'];
            $item['video_topic']             = str_replace('&nbsp;', ' ', $item['video_topic']);
            $item['job_kind']                = $jobKindMap[$jobKindId] ?? '';

            $this->countValue($item);

            // 汇总分组处理临时
            @$resetGroupFn($item);
        }

        $this->countValue($summary);

        return $infoRe;
    }

    /**
     * @param $target
     *
     * @return void
     */
    protected function countValue(&$target)
    {
        if (
            empty($target['completion_5s'])
            || empty($target['play_count'])
        ) {
            $target['completion_5s_rate'] = '0.00%';
        }
        else {
            $target['completion_5s_rate'] =
                \round($target['completion_5s'] / $target['play_count'] * 100, 2) . '%';
        }


        if (empty($target['completion_play']) || empty($target['play_count'])) {
            $target['completion_rate'] = '0.00%';
        }
        else {
            $target['completion_rate'] =
                \round($target['completion_play'] / $target['play_count'] * 100, 2) . '%';
        }

    }

    /**
     * @param array $groups
     *
     * @return array
     */
    public function groupRelationMap(array $groups): array
    {
        $relationMap = [
            'tday'                    => [], // 发布时间
            'media_platform'          => ['media_platform_id'], // 媒体平台
            'business_ownership_name' => ['business_ownership'], // 业务归属
            'task_id'                 => [], // 发布任务id
            'account_id'              => ['media_platform', 'business_ownership_name', 'account_name', 'use_kind_name', 'operations_manager'], // 账号ID
            'video_topic'             => [], // 视频话题
            'operations_manager'      => [], // 运营负责人
            'mc_id'                   => [],
            'demo_video_id'           => ['demo_video_title'], // 样片ID
            'task_script_name'        => ['job_kind'],// 发布脚本
            'use_kind_name'           => ['use_kind'], // 账号归属
            'job_kind'                => [],
            'media_video_topic'       => [],
            'data_label'              => []
        ];

        return ColumnManager::matchRelationByGroups($relationMap, $groups);
    }

    /**
     * @param array $options
     * @param array $groups
     * @return array
     * @throws \app\extension\Exception\ParameterException
     */
    public function audienceAnalyze(array $options = [], array $groups = []): array
    {
        if (empty($groups)) {
            $groups = ['account_id', 'video_fitid'];
        }

        $info = (new AudienceAnalyzeServ())->getInfo($options, $groups);

        $listGroupDimension = [];
        $summaryMap         = [];

        foreach ($info as $item) {
            $item      = array_change_key_case($item);
            $dimension = $item['dimension'];
            $dataType  = $item['data_type'];
            $num       = $item['num'] ?? 0;

            if (!isset($listGroupDimension[$dataType])) {
                $listGroupDimension[$dataType] = [];
            }

            isset($summaryMap[$dataType])
                ? $summaryMap[$dataType] += $num
                : $summaryMap[$dataType] = $num;

            $chillDim = &$listGroupDimension[$dataType];

            if (isset($chillDim[$dimension])) {
                $chillDim[$dimension]['value'] += $num;
            }
            else {
                $chillDim[$dimension] = [
                    'dimension' => $dimension,
                    'value'     => $num,
                ];
            }
        }
        unset($chillDim);

        foreach ($listGroupDimension as $t => $foo) {
            $d       = $foo;
            $summary = $summaryMap[$t];

            foreach ($d as &$chill) {
                $value = $chill['value'] ?? 0;

                if (empty($value) || empty($summary)) {
                    $chill['value'] = 0;
                }
                else {
                    $chill['value'] = round($value / $summary * 100, 2);
                }
            }

            usort($d, static function ($a, $b) {
                return $a['value'] < $b['value'];
            });

            $listGroupDimension[$t] = [
                'list' => array_values($d),
            ];
        }
        unset($chill);

        $typeList = Arr::wrap($options['data_type']);
        $result   = [];
        foreach ($typeList as $tt) {
            if (!isset($result[$tt])) {
                $result[$tt] = ['list' => []];
            }

            $chill  = &$result[$tt];
            $config = $this->getFieldsConfig($tt);
            $chill  = array_merge($chill, $config);

            if (isset($listGroupDimension[$tt])) {
                $list = $listGroupDimension[$tt];

                if ('age_data' == $tt) {
                    if (!empty($list['list'])) {
                        usort($list['list'], static function ($aa, $bb) {
                            return $aa['dimension'] > $bb['dimension'];
                        });
                    }
                }

                if ('interest_data' == $tt) {
                    $list = array_slice($list, 0, 10);
                }

                if ('gender_data' == $tt) {
                    foreach ($list['list'] as &$foo) {
                        if ($foo['dimension'] == 1) {
                            $foo['dimension'] = '男';
                        }
                        elseif ($foo['dimension'] == 2) {
                            $foo['dimension'] = '女';
                        }
                    }
                    unset($foo);
                }

                $chill = array_merge($chill, $list);
            }
        }
        unset($chill);

        return $result;
    }


    /**
     * @param $dataType
     *
     * @return array[]
     */
    protected function getFieldsConfig($dataType): array
    {
        if ('gender_data' == $dataType || 'age_data' == $dataType) {
            return [
                'config' => [
                    ['label' => '维度', 'prop' => 'dimension'],
                    ['label' => '数值', 'prop' => 'value'],
                ],
            ];
        }
        elseif ('interest_data' == $dataType) {
            return [
                'fields' => [
                    ['title' => '兴趣', 'dataIndex' => 'dimension'],
                    ['title' => '占比', 'dataIndex' => 'value'],
                ],
            ];
        }

        return [];
    }

    /**
     * @param array $sort
     * @return array
     */
    protected function orderReflect(array $sort): array
    {
        $reflectMap = [
            'media_platform'          => 'media_platform_id',
            'account_id'              => 'live_account.account_id',
            'job_kind'                => 'job_kind_id',
            'use_kind_name'           => 'use_kind',
            'business_ownership_name' => 'business_ownership',
            'completion_5s_rate'      => ['completion_5s', 'play_count'],
            'completion_rate'         => ['completion_play', 'play_count']
        ];

        foreach ($sort as $s => $o) {
            if (isset($reflectMap[$s])) {
                $change = $reflectMap[$s];

                if (is_string($change)) {
                    $sort[$change] = $o;
                    unset($sort[$s]);
                }
                elseif (is_array($change)) {
                    [$a, $b] = $change;
                    if ($o == 'DESC') {
                        $sort[$a] = 'DESC';
                        $sort[$b] = 'ASC';
                    }
                    else {
                        $sort[$a] = 'ASC';
                        $sort[$b] = 'DESC';
                    }
                    unset($sort[$s]);
                }
            }
        }

        return $sort;
    }

}
<?php

namespace app\ad_upload\tests\tool;

use app\ad_upload\tool\AlternatingLoop;
use PHPUnit\Framework\TestCase;

/**
 * AlternatingLoop 测试
 * <AUTHOR>
 * phpcs:disable
 */
class AlternatingLoopTest extends TestCase
{
    private $key = 'test2';
    private $redis;
    protected function setUp(): void
    {
        $this->key = $this->key. uniqid();
        // 初始化 Redis 实例
        $this->redis = \Plus::$app->redis;
        $this->redis->delete($this->key); // 清除测试键
    }


    public function testNext(): void
    {
        $start = 1;
        $stop  = 1;

        $o = new AlternatingLoop($this->redis, $this->key, $start, $stop);

        for ($i = 0; $i < 10; $i++) {
            $next = $o->next();
            if ($i % 2 == 0) {  // 偶数都是 true
                $this->assertTrue($next);
            } else {
                $this->assertFalse($next);
            }
        }
    }

    public function testNext2(): void
    {
        $start = 2;
        $stop  = 3;

        $o = new AlternatingLoop($this->redis, $this->key, $start, $stop);

        $t = 0;
        for ($i = 0; $i < 100; $i++) {
            $next = $o->next();
            if($t < 2 ){
                $this->assertTrue($next);
            }else{
                $this->assertFalse($next);
            }
            $t++;
            if ($t == 5) {
                $t = 0;
            }
        }
    }
}

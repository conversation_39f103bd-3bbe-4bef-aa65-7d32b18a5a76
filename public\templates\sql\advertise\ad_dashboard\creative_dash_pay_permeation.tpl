{include file="sql/advertise/ad_dashboard/creative_dash_alpha.tpl"}
select
{if !empty($groups)}
    {foreach $groups as $item}
        {if $item eq 'tday'} xb1.tday,{continue}
        {elseif $item eq 'cp_game_id'} xb1.cp_game_id,{continue}
        {elseif $item eq 'game_id'} xb1.game_id,{continue}
        {elseif $item eq 'app_show_id'} xb1.app_show_id,{continue}
        {elseif $item eq 'channel_main_id'} channel_main_id,{continue}
        {elseif $item eq 'promotion_channel_id'} xb1.promotion_channel_id,{continue}
        {elseif $item eq 'promotion_id'} xb1.promotion_id,{continue}
        {elseif $item eq 'user_id'} xb1.dim_user_id as user_id,{continue}
        {elseif $item eq 'department_id'} department_id,{continue}
        {elseif $item eq 'package_id'} xb1.package_id,{continue}
        {elseif $item eq 'platform_id'} xb1.platform_id,{continue}
        {elseif $item eq 'account_id'} account_id,{continue}
        {elseif $item eq 'account_name'} account_name,{continue}
        {elseif $item eq 'ad_account'} ad_account,{continue}
        {elseif $item eq 'campaign_id'} campaign_id,any(campaign_name) as campaign_name,{continue}
        {elseif $item eq 'plan_id'} plan_id,{continue}
        {elseif $item eq 'creative_id'} creative_id,{continue}
        {elseif $item eq 'dim_user_os'} dim_user_os,{continue}
        {/if}
    {/foreach}
{/if}
ROUND(SUM(pay_user_num) / sum(new_user) * 100, 2) as pay_permeation_percent
from (select xa1.tday,
xa1.cp_game_id,
xa1.game_id,
xa1.package_id,
xa1.campaign_id,
xa1.plan_id,
xa1.creative_id,
xa1.promotion_channel_id,
xa1.account_id,
xa1.dim_user_id,
xa1.ad_account,
xa1.campaign_name,
xa1.account_name,
xa1.dim_user_os,
xa1.app_show_id,
xa1.promotion_id,
xa1.platform_id,
row_number() over (partition by xa1.tday,
xa1.cp_game_id,
xa1.game_id,
xa1.package_id,
xa1.campaign_id,
xa1.plan_id,
xa1.creative_id,
xa1.promotion_channel_id,
xa1.account_id,
xa1.dim_user_id,
xa1.ad_account) as rn,
xa2.pay_user_num,
new_user
from ddc_platform.dws_creative_ad_pay_permeation_daily_v2 xa2
right join dashboard_info xa1
on xa1.package_id = xa2.package_id and xa1.tday = xa2.tday and xa1.campaign_id = xa2.campaign_id and
xa1.plan_id = xa2.plan_id and xa1.creative_id = xa2.creative_id
where xa2.id is not null) xb1
left join base_conf_platform.tb_base_channel_conf xb2 on xb1.promotion_channel_id = xb2.channel_id
left join dataspy.admin_user xb3 on xb1.dim_user_id=xb3.id
{* 汇总维度 *}
{if !empty($groups)}
    group by
    {if !empty($groups)}
        {foreach $groups as $item}
            {if $item eq 'tday'} xb1.tday {if !$item@last}, {/if}
            {elseif $item eq 'cp_game_id'} xb1.cp_game_id {if !$item@last}, {/if}
            {elseif $item eq 'game_id'} xb1.game_id {if !$item@last}, {/if}
            {elseif $item eq 'app_show_id'} xb1.app_show_id {if !$item@last}, {/if}
            {elseif $item eq 'channel_main_id'} channel_main_id {if !$item@last}, {/if}
            {elseif $item eq 'promotion_channel_id'} xb1.promotion_channel_id {if !$item@last}, {/if}
            {elseif $item eq 'promotion_id'} xb1.promotion_id {if !$item@last}, {/if}
            {elseif $item eq 'user_id'} xb1.dim_user_id {if !$item@last}, {/if}
            {elseif $item eq 'department_id'} department_id {if !$item@last}, {/if}
            {elseif $item eq 'package_id'} package_id {if !$item@last}, {/if}
            {elseif $item eq 'platform_id'} xb1.platform_id {if !$item@last}, {/if}
            {elseif $item eq 'account_id'} account_id {if !$item@last}, {/if}
            {elseif $item eq 'account_name'} account_name {if !$item@last}, {/if}
            {elseif $item eq 'ad_account'} ad_account {if !$item@last}, {/if}
            {elseif $item eq 'campaign_id'} campaign_id {if !$item@last}, {/if}
            {elseif $item eq 'plan_id'} plan_id {if !$item@last}, {/if}
            {elseif $item eq 'creative_id'} creative_id {if !$item@last}, {/if}
            {elseif $item eq 'dim_user_os'} dim_user_os {if !$item@last}, {/if}
            {/if}
        {/foreach}
    {/if}
{/if}
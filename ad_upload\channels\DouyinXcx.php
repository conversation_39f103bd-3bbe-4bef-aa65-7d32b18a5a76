<?php

/**
 * 抖音小程序
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class DouyinXcx extends AdBaseInterface
{


    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'ACTIVE');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'PAY');
    }


    /**
     * @param $info
     * @param string $type
     */
    private function uploadData($info, $type = '')
    {
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'douyin_xcx';
        $logInfo['log_type']     = 'reported_platform_log';

        $aValue = 'active';
        switch ($type) {
            case 'ACTIVE':
                $aValue   = 'active';
                $typeName = '激活';
                break;
            case 'REG':
                $aValue   = 'active_register';
                $typeName = '注册';
                break;
            case 'PAY':
                $aValue   = 'active_pay';
                $typeName = '付费';
                break;
        }

        $callbackOAID = $info['EXT_CLICK']['oaid'] ?? ($info['OAID'] ?? '');

        // 上传参数
        $requestParams = [
            'context'    => [
                "ad" => [
                    "callback" => $callbackOAID
                ]
            ],
            'timestamp'  => time() * 1000,
            'event_type' => $aValue,
        ];
        if ($type == 'PAY') {
            $requestParams['properties']['pay_amount'] = intval($info['MONEY'] * 100);
        }

        $url                = 'https://analytics.oceanengine.com/api/v2/conversion';
        $logInfo['request'] = \json_encode(['url' => $url, 'params' => $requestParams]);

        $http = new Http($url);
        $res  = $http->postJson($requestParams);

        $logInfo['response'] = $res;
        // 记录上报结果
        $resArr = json_decode($res, true);

        if ($resArr['code'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        $this->log($info, $logInfo, $res, $url);
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

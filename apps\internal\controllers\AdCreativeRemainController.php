<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Helpers\Calculator;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Helpers\IndexCalculators;
use app\apps\internal\Helpers\RemainCalculator;
use app\apps\internal\Traits\AdOfflineDash;
use app\apps\internal\Traits\AdRouteRequest;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\internal\Traits\DefaultActionable;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\AdvertiserData\CreativeRemainIndex;
use app\service\AdvertiserData\RealtimeCreativeIndex;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;

/**
 * @AdCreativeRemainController 广告留存(创意维度)
 * @route                      /internal/ad-creative-remain/*
 */
class AdCreativeRemainController extends BaseTableController
{
    use AdRouteRequest, ColumnsInteract, AdOfflineDash, DefaultActionable;

    /**
     * 数据处理逻辑
     *
     * @param Collection $params
     * @route  {parent}/data?method[]=data
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];
        $groups     = Arr::wrap($params->pull('groups'));
        $today      = new \DateTime();
        $maxDayDiff = days_apart($params->get('range_date_start'), $today);
        $remainType = $params->pull('remain_type');
        $options    = $params->toArray();

        // 外部账号仅能显示自己账号归属的数据
        if (\Plus::$service->admin->isOutsiders()) {
            $options['user_id'] = \Plus::$service->admin->getUserId();
        }

        if ($params->has('sort')) {
            $sort = $params->pull('sort');

            if ($params->has('order')) {
                $sort .= ' ' . ($params->pull('order') == 'ascend' ? 'asc' : 'desc');
            }
        }
        else {
            $sort = ['tday', 'new_user desc'];
        }

        $sort       = Arr::wrap($sort);
        $baseServ   = new RealtimeCreativeIndex();
        $baseResult = $baseServ->listBase($options, ['page' => $page, 'page_size' => $pageSize], $groups, $sort);

        $constConfCollect = (new BasicServ())->getMultiOptions([
            'platform_id', 'promotion_id', 'department_id', 'user_id',
            'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
        ])->put('promotion_channel_id', (new GeneralOptionServ())->listChannelOptions());


        $resetFunc = $this->resetGroupsCols(
            ColumnManager::groupAdRelation($groups), $groups, ConstHub::AD_FIXED_INFO_COLS
        );

        $info        = &$baseResult['list'];
        $remainServ  = new CreativeRemainIndex();
        $processLine = new ProcessLine();
        $clearFn     = function (&$target) {
            unset($target['remain_info'], $target['new_user_n'], $target['cost_discount_n']);
        };

        $backupFn = static function (&$target, $k) {
            $target['department_id'] = $target['new_department_id'] ?: null;
        };

        $processLine
            ->prependEachRow($backupFn)
            // 拆解每行对应的搜索条件
            ->prependEachRow($this->parseMatchForEachRow($options, $groups))
            // 每行追加留存信息
            ->addProcess($this->addRemainInfo([$remainServ, 'remainInfoByCreative'], $groups))
            // 替换ID信息字段
            ->addProcess($this->replaceColumnDefine($constConfCollect))
            // 清理汇总分组不必要的字段
            ->addProcess($resetFunc);

        if (in_array('tday', $groups)) {
            $getUserFn = IndexCalculators::getSingleValue('new_user');
        }
        else {
            $processLine->addProcess(
                $this->addEachRowInfo([$baseServ, 'listBase'], ['new_user'], $groups, ['operator' => '<'])
            );

            $getUserFn = IndexCalculators::getValueInCollectByN('new_user_n');
        }

        $lastRemainFn = function (&$target, $key) use ($remainType) {
            $getFn    = IndexCalculators::remainValueGet($remainType);
            $user     = $target['new_user'] ?? 0;
            $loginNum = $target['remain_current'] ?? 0;

            $target['remain_current'] = $getFn($loginNum, $user);
        };


        // 留存计算
        $processLine
            ->addProcess(RemainCalculator::calcEachRow($getUserFn, ['max_days' => $maxDayDiff], $remainType))
            ->addProcess(IndexCalculators::remainDayCalculators(ConstHub::DIMENSION_DAY, $groups, $maxDayDiff - 1))
            ->addProcess($lastRemainFn)
            ->addProcess($clearFn);


        // 补全广告信息
        [$adInfo, $dimension] = (new RealtimeIndex())->getAdInfoByGroups($info, $groups);
        if ($dimension) {
            $processLine->addProcess(function (&$target, $k) use ($adInfo, $dimension) {
                $fields = [];
                switch ($dimension) {
                    case "campaign_id":
                        $fields = ["CAMPAIGN_NAME"];
                        break;
                    case "plan_id":
                        $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME"];
                        break;
                    case "creative_id":
                        $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME", "PLAN_ID", "CREATIVE_NAME"];
                        break;
                }
                foreach ($fields as $field) {
                    $key = $target[$dimension];
                    if ($dimension == "creative_id") {
                        $key = $target["plan_id"] . "_" . $target["creative_id"];
                    }
                    $target[strtolower($field)] = $adInfo[$key][$field] ?? null;
                }
            });
        }
        $processLine->run($info);

        /**
         * ### 汇总处理
         */
        $summaryRow                = &$baseResult['summary'];
        $summaryRemain             = $remainServ->remainInfoByCreative($options, ['tday', 'day_type']);
        $lastUpdateTimeMap         = array_column($summaryRemain, 'last_update_time');
        $baseResult['time']        =empty($lastUpdateTimeMap) ? '' : max($lastUpdateTimeMap);
        $summaryRow['remain_days'] = max($maxDayDiff - 1, 0);

        $summaryRemainByDate = [];
        $summaryRowRemain    = [];

        foreach ($summaryRemain as $foo) {
            $dDay    = $foo['tday'];
            $dayType = $foo['day_type'];

            !isset($summaryRowRemain[$dayType])
                ? $summaryRowRemain[$dayType] = ['day_type' => $dayType, 'login_num' => $foo['login_num'] ?? 0]
                : $summaryRowRemain[$dayType]['login_num'] += ($foo['login_num'] ?? 0);


            if (isset($summaryRemainByDate[$dDay])) {
                if ($dayType > $summaryRemainByDate[$dDay]['day_type'] ?? 0) {
                    $summaryRemainByDate[$dDay]['login_num'] = $foo['login_num'];
                }
            }
            else {
                $summaryRemainByDate[$dDay] = ['day_type' => $dayType, 'login_num' => $foo['login_num'] ?? 0];
            }
        }


        if (
            $params->get('range_date_start') === $params->get('range_date_end')
        ) {
            $summaryGetUser = IndexCalculators::getSingleValue('new_user');
        }
        else {
            $summaryInfoByDate = $baseServ->listBase($options, [], ['tday'], null, [], false);
            $summaryNInfo      = Calculator::cumulativeOnDays($summaryInfoByDate['list'] ?? [], ['new_user'], '<');
            $summaryRow        = array_merge($summaryRow, $summaryNInfo);

            $summaryGetUser = IndexCalculators::getValueInCollectByN('new_user_n');
        }

        RemainCalculator::calcRemain($summaryRow, $summaryRowRemain, $summaryGetUser, $remainType);

        if (
            empty($summaryRow['remain_current'])
            || empty($summaryRow['new_user'])
        ) {
            $summaryRow['remain_current'] = '0.00%';
        }
        else {
            $summaryRow['remain_current'] = number_format(
                    math_eval('x/y*100', ['x' => $summaryRow['remain_current'], 'y' => $summaryRow['new_user']]), 2
                ) . '%';
        }

        $clearFn($summaryRow);

        return $baseResult;
    }


    protected function registerParams($key = null): Collection
    {
        return $this
            ->baseParams()
            ->merge([
                ['field' => 'groups', 'default' => ['tday', 'promotion_channel_id', 'package_id', 'campaign_id', 'plan_id', 'creative_id']],
                ['field' => 'remain_type', 'default' => RemainCalculator::REMAIN_VAL_DEFAULT], // remain值类型
                ['field' => 'marketing_goal'], // 营销场景
            ]);
    }

    protected function fields(Collection $params): array
    {
        $baseCollect   = [
            'tday', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id',
            'promotion_channel_id', 'promotion_id', 'platform_id',
            'department_id', 'user_id',
        ];
        $adBaseCollect = [
            'ad_account', 'account_id', 'campaign_name', 'campaign_id',
            'plan_name', 'plan_id', 'creative_name', 'creative_id', 'account_name',
        ];

        $newBaseCollect = [
            'new_user', 'remain_days', 'remain_current',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'ad_base', 'label' => '广告信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],

                    ['value' => 'remain_group_1', 'label' => '次留-30留'],
                    ['value' => 'remain_group_2', 'label' => '45留-180留'],
                    ['value' => 'remain_group_3', 'label' => '210留-360留'],
                    ['value' => 'remain_group_4', 'label' => '360留-720留'],
                ],
            ],
        ];

        $fields = $this->tableFields($params->toArray());

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'] ?? '';
            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $adBaseCollect)) {
                $field['classify'] = ['attrs', 'ad_base'];
            }
            elseif (in_array($dIndex, $newBaseCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

}
<?php

namespace app\models\baseConfPlatform;

use app\util\Common;
use Plus\MVC\Model\ActiveRecord;

/**
 * games
 *
 * @property int    $id             自增ID(CHANNEL_ID)
 * @property string $TABLE_NAME     表名
 * @property string $DATA_ID        数据id
 * @property string $TAG_ID         标签自身id
 * <AUTHOR>
 */
class BizTags extends ActiveRecord
{
    public $_primaryKey = 'id';

    /**
     * 初始化，设置数据、数据库连接类
     *
     * @param array $data 批量配置数据
     *
     */
    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->base_conf_platform;
    }

    /**
     * 获取小程序子渠道
     *
     * @param $where
     *
     * @return array
     */
    public function getXcxChannelId()
    {
        $list = $this->asArray()->findAll(["TAG_ID"=>885,"DATA_ID[!]"=>6568]);
        return array_column($list, 'DATA_ID');
    }

    /**
     * 获取一包多推渠道
     *
     * @param $where
     *
     * @return array
     */
    public function getOnePackageMultiplePromotionsChannelId()
    {
        $list = $this->asArray()->findAll(["TAG_ID"=>885]);
        return array_column($list, 'DATA_ID');
    }


    /**
     * 获取微信小程序包号
     *
     * @param $where
     *
     * @return array
     */
    public function getWeiXinXcxPackageId()
    {
        $data =   (new TbPackageDetailConf())->asArray()->findAll(["CHANNEL_ID"=>$this->getXcxChannelId()]);
        return array_column($data,"PACKAGE_ID");
    }

}

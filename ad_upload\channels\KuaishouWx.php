<?php

namespace app\ad_upload\channels;

class KuaishouWx extends Kuaishou
{
    /**
     * 激活上报
     * @param array $info
     * @param array $ext
     * @return void
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE', 'KUAISHOU_WX_PARAM_');
    }

    /**
     * 充值上报
     *
     * @param array $info
     * @param array $ext
     * @return void
     */
    function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG', 'KUAISHOU_WX_PARAM_');
    }

    /**
     * 付费上报
     *
     * @param array $info
     * @param array $ext
     * @return void
     */
    function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY', 'KUAISHOU_WX_PARAM_');
    }
}

<?php

namespace app\service\AdvertiserData\Traits;

use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Scheme\AdCreativeLtvScheme;

trait JoinBase
{
    /**
     * @return $this|SchemeContract
     */
    public function joinCreativeBase($method = 'inner')
    {
        $mainTable = static::MAIN_TABLE['alias'];

        $creativeBase = new JoinClause($method, 'ddc_platform.dws_creative_ad_base_daily', 't_base');
        $creativeBase
            ->on('t_base.tday', '=', $mainTable . '.tday')
            ->on('t_base.package_id', '=', $mainTable . '.package_id')
            ->on('t_base.channel_id', '=', $mainTable . '.channel_id')
            ->on('t_base.campaign_id', '=', $mainTable . '.campaign_id')
            ->on('t_base.plan_id', '=', $mainTable . '.plan_id')
            ->on('t_base.creative_id', '=', $mainTable . '.creative_id');

        $this->join($creativeBase);

        return $this;
    }

    /**
     * @return $this|SchemeContract
     */
    public function joinPlanBase($method = 'inner')
    {
        $mainTable = static::MAIN_TABLE['alias'];

        $planBase = new JoinClause($method, 'ddc_platform.dws_plan_ad_base_daily', 't_base');
        $planBase
            ->on('t_base.tday', '=', $mainTable . '.tday')
            ->on('t_base.package_id', '=', $mainTable . '.package_id')
            ->on('t_base.channel_id', '=', $mainTable . '.channel_id')
            ->on('t_base.plan_id', '=', $mainTable . '.plan_id');

        $this->join($planBase);

        return $this;
    }

    /**
     * @return $this|SchemeContract
     */
    public function joinAdpPlan($method = 'left')
    {
        $mainTable = static::MAIN_TABLE['alias'];
        $this->join(
            (new JoinClause($method, 'adp_platform.tb_adp_plan_base', 'base_plan'))
                ->on('base_plan.channel_id', '=', $mainTable . '.MAIN_CHANNEL_ID')
                ->on('base_plan.plan_id', '=', $mainTable . '.plan_id')
        );
        return $this;
    }

    /**
     * @return $this|SchemeContract
     */
    public function joinAdpCreative($method = 'left')
    {
        $mainTable = static::MAIN_TABLE['alias'];
        $this->join(
            (new JoinClause($method, 'adp_platform.tb_adp_creative_base', 'base_creative'))
                ->on('base_creative.channel_id', '=', $mainTable . '.MAIN_CHANNEL_ID')
                ->on('base_creative.campaign_id', '=', $mainTable . '.campaign_id')
                ->on('base_creative.plan_id', '=', $mainTable . '.plan_id')
                ->on('base_creative.creative_id', '=', $mainTable . '.creative_id')
        );
        return $this;
    }

    /**
     * @return $this|SchemeContract
     */
    public function joinAdpCampaign($method = 'left')
    {
        $mainTable = static::MAIN_TABLE['alias'];

        $this->join(
            (new JoinClause($method, 'adp_platform.tb_adp_campaign', 'base_campaign'))
                ->on('base_campaign.channel_id', '=', 'base_plan.channel_id')
                ->on('base_campaign.campaign_id', '=', 'base_plan.campaign_id')
        );
        return $this;
    }

    /**
     * @return $this|SchemeContract
     */
    public function joinSvlink($method = 'left')
    {
        $mainTable = static::MAIN_TABLE['alias'];

        $this->join(
            (new JoinClause($method, 'dataspy.tb_ad_svlink_conf', 'svlink'))
                ->on('svlink.ID', '=', ($mainTable . '.PLAN_ID +0'))
        );
        return $this;
    }

    /**
     * @return $this|SchemeContract
     */
    public function joinAdpOauth($method = 'left', $relateTable = "")
    {
        $mainTable = static::MAIN_TABLE['alias'];
        if ($relateTable) {
            $mainTable = $relateTable;
        }
        $this->join(
            (new JoinClause($method, 'adp_platform.tb_adp_oauth', 'adp_oauth'))
                ->on('adp_oauth.ADVERTISER_ID', '=', $mainTable . '.ACCOUNT_ID')
        );
        return $this;
    }
}
<?php


namespace app\ad_upload\tool;

use app\util\Common;

/**
 * 自定义错误处理
 * <AUTHOR>
 *
 */
class ErrorHandler extends \Plus\Internal\ErrorHandler
{
    /**
     * 企微机器人key
     */
    const KEY     = '6fb27adf-6407-46fa-bdcf-88737227d1f4';
    const KEY_DEV = '41e30e42-ee87-4bcc-8e9c-4cb5ffbb251e';
    /**
     * 致命错误显示
     *
     * @param array $error 错误
     * @return void
     */
    public function displayFatal($error)
    {
        $data            = [];
        $data['code']    = 0;
        $data['message'] = $error['message'];
        $data['file']    = $error['file'];
        $data['line']    = $error['line'];
        $data['type']    = 'fatal';
        $this->render('error', $data);
    }

    /**
     * 显示错误
     *
     * @param int    $code    code
     * @param string $message msg
     * @param string $file    file
     * @param int    $line    line
     * @return void
     */
    public function displayError($code, $message, $file, $line)
    {
        $data            = [];
        $data['code']    = $code;
        $data['message'] = $message;
        $data['file']    = $file;
        $data['line']    = $line;
        $data['type']    = 'error';
        $this->render('error', $data);
    }

    /**
     * 显示异常
     *
     * @param \Exception $exception 错误
     * @param string     $custom    自定义错误信息
     * @return  void
     */
    public function displayException($exception, $custom = '')
    {
        $data            = [];
        $data['code']    = $exception->getCode();
        $data['message'] = $exception->getMessage() . " ( {$custom} )";
        $data['file']    = $exception->getFile();
        $data['line']    = $exception->getLine();
        $data['type']    = get_class($exception);
        $data['trace']   = $exception->getTrace();
        $this->render('error', $data);
    }

    /**
     * 渲染错误，发送企业微信消息
     * @param string $fileName 文件名
     * @param array  $data     错误信息
     * @return void
     */
    protected function render($fileName, $data)
    {
        $msg        = "";
        $data['时间'] = date('Y-m-d H:i:s');
        $data["进程"] = cli_get_process_title();
        if (empty($data['进程'])) {
            $data["进程"] = getopt('f:s:', ['help', 'p:'])['p'];
        }
        foreach ($data as $k => $v) {
            if (!is_scalar($v)) {
                continue;
            } else {
                $msg .= $k." : ".$v."\n";
            }
        }
        //发送企业微信消息
        if (APP_EVN != 'DEV') {
            Common::qyWxBot(self::KEY, $msg);
        } else {
            Common::qyWxBot(self::KEY_DEV, $msg);
        }
        parent::render($fileName, $data);
    }
}

<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;

class PaidAccountProfileProvider
{
    /**
     * @param array $accountList
     * @return array
     */
    public function checkExceptionPaidAccount(array $accountList)
    {
        $accounts = "'" . implode("', '", $accountList) . "'";
        $db       = $this->getConn();
        $sql      = "select main_account,count(1) as cc from ddc_platform.dwd_sdk_ad_source_game_user_profile where main_account in ({$accounts}) group by main_account";
        $result   = $db->query($sql)->fetchAll();

        return array_column($result, 'main_account');
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }


}
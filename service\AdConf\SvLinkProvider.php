<?php

namespace app\service\AdConf;

use app\extension\FakeDB\FakeDB;

class SvLinkProvider
{
    /**
     * @param array $svLinks
     * @return array
     */
    public function findSvLinkId(array $svLinks): array
    {
        foreach ($svLinks as &$foo) {
            $foo = "'{$foo}'";
        }
        $svLinks = implode(',', $svLinks);

        $sql = "SELECT sv_link,id,aid as plan_name from dataspy.tb_ad_svlink_conf where sv_link in ($svLinks)";
        $db  = $this->getConn();

        return $db->query($sql)->fetchAll();
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('doris_entrance');
    }

}
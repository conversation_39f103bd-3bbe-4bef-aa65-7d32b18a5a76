<?php

namespace app\service\OperationData\Scheme;

use app\extension\Support\Macroable\Traits\Macroable;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Traits\JoinFixAble;
use app\service\AdvertiserData\Traits\OperableQuery;
use app\service\AdvertiserData\Traits\Schemer;
use Aura\SqlQuery\QueryFactory;

class PackageBaseMonthScheme implements SchemeContract
{
    use Macroable, Schemer, JoinFixAble, OperableQuery;

    public const MAIN_TABLE = [
        'table' => 'ddc_platform.dws_package_base_month',
        'alias' => 't_base',
        'daily_table' => 'ddc_platform.dws_package_base_daily',
    ];

    public const FIXED_TABLE = [
        'table' => 'ddc_platform.dws_package_payment_month',
        'alias' => 't_payment',
        'daily_table' => 'ddc_platform.dws_package_payment_daily',
    ];

    protected array $joinTables = [];


    protected QueryFactory $queryFactory;

    protected $query;


    public static function NewOne(): self
    {
        return new PackageBaseMonthScheme();
    }

    public function __construct()
    {
        $this->queryFactory = new QueryFactory('mysql');
    }

    public function __clone()
    {
        $this->queryFactory = clone $this->queryFactory;
        $this->query        = clone $this->query;
    }

    public function fieldReflect(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];
        return [
            'tday'            => $mainTable,
            'cp_game_id'      => $mainTable,
            'game_id'         => $mainTable,
            'package_id'      => $mainTable,
            'app_show_id'     => 'POWER',
            'channel_main_id' => 'POWER',
            'channel_id'      => 'POWER',
            'platform_id'     => 'POWER',
            'promotion_id'    => 'POWER.popularize_v2_id',
        ];
    }

    protected function fixedTables(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];
        $fixTable = self::FIXED_TABLE['alias'];
        $fixTables = [];

        $fixTables[] =
            (new JoinClause('left', self::FIXED_TABLE['table'], $fixTable))
                ->on($fixTable . '.tday', '=', $mainTable . '.tday')
                ->on($fixTable . '.package_id', '=', $mainTable . '.package_id');

        return $fixTables;
    }
}
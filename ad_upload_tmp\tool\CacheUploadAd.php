<?php

namespace app\ad_upload_tmp\tool;

/**
 * 数据上报，缓存管理
 * <AUTHOR>
 *
 */
class CacheUploadAd
{


    /**
     * 把包号当成渠道用，临时数据，后面要手动删除
     * @var int
     */
    private $channelId = 70560099;
    /**
     * 数据库
     * @var \Plus\SQL\Db
     */
    private $db;

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->db = \Plus::$app->dataspy;
        //测试环境 tb_ad_upload_log 读写测试环境库
        if (APP_EVN == 'DEV') {
            $this->db = \Plus::$app->dataspy2;
        }
    }
    //region 公共方法

    /**
     * 处理未匹配的 ID
     * @param array  $unmatchedIds 未匹配的
     *                             ID
     * @param array  $foo          数据
     * @param string $IdField      ID
     *                             字段
     *                             字段
     * @param string $clickId      CLICK_ID
     *                             字段
     * @return array|mixed
     */
    public function processUnmatchedIds($unmatchedIds, $foo, $IdField = "ID", $clickId = "CLICK_ID")
    {
        $id = $foo[$IdField] ?? 0; // 如果不存在 ID，使用 0
        if (!empty($foo[$clickId])) {
            // 如果 ID 在 unmatchedIds 中，移除它
            if (in_array($id, $unmatchedIds)) {
                $unmatchedIds = array_values(array_diff($unmatchedIds, [$id]));
            }
        } else {
            // 如果 CLICK_ID 为空，将 ID 添加到 unmatchedIds
            $unmatchedIds[] = $id;
        }
        return $unmatchedIds;
    }


    /**
     * 处理未匹配的 ID
     * @param array $unmatchedIds 未匹配的ID
     * @param array $foo          数据
     * @return array|mixed
     */
    public function processUnmatchedIds2($unmatchedIds, $foo)
    {
        if ($foo['CHANNEL_ID'] > 0) {
            // 如果 ID 在 unmatchedIds 中，移除它
            foreach ($unmatchedIds as $k => $v) {
                if ($v['idfv'] == $foo['IDFV'] && $foo['TIME'] == $v['time']) {
                    unset($unmatchedIds[$k]);
                }
            }
        } else {
            $flag = false;
            foreach ($unmatchedIds as $k => $v) {
                if ($v['idfv'] == $foo['IDFV'] && $foo['TIME'] == $v['time']) {
                    $flag = true;
                }
            }
            if ($flag) {
                return $unmatchedIds;
            }
            // 如果 CLICK_ID 为空，将 ID 添加到 unmatchedIds
            $unmatchedIds[] = [
                'idfv' => $foo['IDFV'],
                'time' => $foo['TIME'],
            ];
        }// end if()
        return $unmatchedIds;
    }

    /**
     * 保存未匹配的 ID 缓存
     * @param string $channelId    渠道ID
     * @param array  $unmatchedIds 未匹配的ID
     * @param string $action       上报类型
     * @return void
     */
    public function setUnmatchedIds($channelId, $unmatchedIds, $action)
    {
        $channelId = $this->channelId;
        if ($action != 'active') {
            $unmatchedIds = array_values(array_unique($unmatchedIds));
        }
        $storeJson = json_encode($unmatchedIds);
        //保存数据库
        $this->db->update('tb_ad_upload_log', [
            'update_time'   => date('Y-m-d H:i:s'),
            'unmatched_ids' => $storeJson,
        ], ['channel_id' => $channelId, 'action' => $action]);
    }

    /**
     * 获取未匹配的 ID 缓存
     *
     * @param string $action    上报类型
     * @param string $channelId 渠道ID
     * @return array|mixed
     */
    public function getUnmatchedIds($action, $channelId)
    {
        $channelId = $this->channelId;
        //查询数据库
        $sql = "SELECT
                  unmatched_ids
                FROM
                  `tb_ad_upload_log`
                WHERE channel_id = $channelId
                  AND action = '$action'
                LIMIT 1";

        $rs = $this->db->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if ($rs) {
            $data = json_decode($rs['unmatched_ids'], true);
            if (empty($data)) {
                return [0];
            }
            return $data;
        }
        return [0];
    }

    /**
     * 从数据库获取上一次上报的 数据ID
     * @param string $action    上报类型
     * @param string $channelId 渠道id
     * @return int
     */
    public function getLastId($action, $channelId)
    {
        $channelId = $this->channelId;
        //查询数据库
        $sql = "SELECT
                  last_action_id
                FROM
                  `tb_ad_upload_log`
                WHERE channel_id = $channelId
                  AND action = '$action'
                LIMIT 1";

        $rs = $this->db->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if ($rs) {
            return $rs['last_action_id'];
        }
        return 0;
    }

    /**
     * 上报点记录到数据库
     * @param int    $channelId 渠道id
     * @param string $action    上报类型
     * @param int    $actionId  上报点
     * @return void
     */
    public function setLastId($channelId, $action, $actionId)
    {
        $channelId = $this->channelId;
        $this->db->update('tb_ad_upload_log', [
            'update_time'    => date('Y-m-d H:i:s'),
            'last_action_id' => $actionId,
        ], ['channel_id' => $channelId, 'action' => $action]);
    }
    //endregion
}

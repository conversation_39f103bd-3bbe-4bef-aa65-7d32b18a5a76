<?php

namespace app\apps\auth\controllers;

use app\service\Admin;
use app\service\Auth;
use Plus\MVC\Controller\JsonController;
use Plus\Net\Http;

/**
 * Class Auth
 * <AUTHOR>
 */
class AuthController extends JsonController
{
    public function __construct(){
        $this->authService = new Auth();
    }

    /**
     * auth list
     * @param array $data 请求参数
     * @return array
     */
    public function listAction($data)
    {
        $data = $this->authService->getAuthorityList($data);
        return $this->success($data["data"]);
    }

    /**
     * create auth
     * @param array $data 请求参数
     * @return array
     */
    public function createAction($data)
    {
        $this->authService->createAuthority($data);
        return $this->success([]);
    }

    /**
     * update auth
     * @param array $data 请求参数
     * @return array
     */
    public function updateAction($data)
    {
        $this->authService->updateAuthority($data);
        return $this->success([]);
    }


    /**
     * delete auth
     * @param array $data 请求参数
     * @return array
     */
    public function deleteAction($data)
    {
        $this->authService->deleteAuthority($data);
        return $this->success([]);
    }

    /**
     * authorize auth
     * @param array $data 请求参数
     * @return array
     */
    public function authorizeAction($data)
    {
        $this->authService->authorizeAuthority($data);
        return $this->success([]);
    }

    /**
     * revoke auth
     * @param array $data 请求参数
     * @return array
     */
    public function revokeAction($data)
    {
        $this->authService->revokeAuthority($data);
        return $this->success([]);
    }
}

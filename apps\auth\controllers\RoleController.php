<?php

namespace app\apps\auth\controllers;

use app\service\Admin;
use app\service\Auth;
use Plus\MVC\Controller\JsonController;
use Plus\Net\Http;

/**
 * Class Role
 * <AUTHOR>
 */
class RoleController extends JsonController
{
    public function __construct(){
       $this->authService = new Auth();
    }

    /**
     * role list
     * @param array $data 请求参数
     * @return array
     */
    public function listAction($data)
    {
        $data = $this->authService->getRoleList($data);
        return $this->success($data["data"]);
    }

    /**
     * create role
     * @param array $data 请求参数
     * @return array
     */
    public function createAction($data)
    {
        $this->authService->createRole($data);
        return $this->success([]);
    }

    /**
     * update role
     * @param array $data 请求参数
     * @return array
     */
    public function updateAction($data)
    {
        $this->authService->updateRole($data);
        return $this->success([]);
    }


    /**
     * delete role
     * @param array $data 请求参数
     * @return array
     */
    public function deleteAction($data)
    {
        $this->authService->deleteRole($data);
        return $this->success([]);
    }

}

<?php

namespace app\apps\goals\controllers;

use app\apps\internal\Traits\InternalRoutes;
use app\extension\Support\CommonCenter\SettleCenter\Request;
use app\extension\Support\CommonCenter\SettleCenter\Uri;
use app\service\AdvertiserData\LtvServ;
use app\service\AdvertiserData\NewUserDistributeServ;
use app\service\AdvertiserData\PlanBaseServ;
use app\service\ConfigService\BasicServ;
use app\service\General\GoalsManagerServ;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Plus\MVC\Controller\JsonController;
use Spiral\Database\Injection\Fragment;


/**
 * 目标看板接口
 *
 */
class DashboardController extends JsonController
{
    /**
     * 目标概况接口
     * 返回(新增用户, 付费金额, 消耗金额, 毛利)
     *
     * @return array
     * @throws \Exception
     * @todo  接入财务系统的成本接口
     *
     * @route /goals/dashboard/overview
     */
    public function overviewAction(): array
    {
        $request    = \Plus::$app->request;
        $year       = $request->getValue('year', date('Y'));
        $month      = $request->getValue('month');
        $mode       = empty($month) ? 0 : 1;
        $params     = ['year' => $year, 'mode' => $mode];
        $rangeStart = $year . '-01-01';
        $rangeEnd   = $year . '-12-31';

        if (!empty($month)) {
            $params['month'] = $month;

            $rangeStart = $year . '-' . $month . '-01';
            $rangeEnd   = (new \DateTime($rangeStart))->format('Y-m-t');
            if ($month == date("m")) {
                $rangeEnd = date("Y-m-d", strtotime("-1 day"));
            }
        }

        $options = [
            'range_date_start' => $rangeStart,
            'range_date_end'   => $rangeEnd,
        ];

        $planBaseServ = new PlanBaseServ();
        $baseInfo     = $planBaseServ->simpleBothInfo(
            $options, ['t_month'], [], [
                new Fragment("DATE_FORMAT(t1.tday, '%Y-%m') as t_month"),
                new Fragment('SUM(new_user)  as new_user'),
                new Fragment('SUM(cost_discount) as cost_discount'),
                //                new Fragment('SUM(pay_money) as pay_money'),
                //                new Fragment('SUM(pay_money) - SUM(pay_money_new) as pay_money_old'),
            ]
        );

        $newUserDistributeOption = [
            'range_date_dimension' => 2,
            'range_date_start'     => $rangeStart,
            'range_date_end'       => $rangeEnd,
            'event_type'           => '3',
            'report_type'          => '1',
            'group_type'           => '2',
        ];

        $newUserDistributeColumns = [
            new Fragment("DATE_FORMAT(tday, '%Y-%m') as t_month"),
            new Fragment('SUM(num) - SUM(if(day_type =0, num, 0)) as pay_money_old'), // 当月维度新增付费金额day_type = 0
            new Fragment('SUM(num) as pay_money'), // 当月维度付费总金额
        ];

        $newUserDistributeList = (new NewUserDistributeServ())->simpleInfo($newUserDistributeOption, ['t_month'], [], $newUserDistributeColumns);
        $newUserDistributeList = array_column($newUserDistributeList, null, 't_month');

        foreach ($baseInfo as &$foo) {
            $tMonth = $foo['t_month'];
            if (isset($newUserDistributeList[$tMonth])) {
                $foo = array_merge($foo, $newUserDistributeList[$tMonth]);
            }
            else {
                $foo['pay_money']     = 0.00;
                $foo['pay_money_old'] = 0.00;
            }
        }

        $info = (new GoalsManagerServ())
            ->listGoals(
                $params,
                ['goal_type', 'mo'],
                [
                    new Fragment("concat(a_year, '-', LPAD(A_MONTH, 2, 0)) as mo"),
                    'goal_type',
                    new Fragment('SUM(goal_num) as goal_num'),
                    new Fragment('SUM(reach_num) as reach_num'),
                ]
            );

        $block = [];

        // 总付费
        $chartDataByGroup = ['1' => [], '2' => [], '5' => []];
        $infoGroupType    = [];
        $typeList         = [1, 2, 5, 4];

        if (!empty($baseInfo)) {
            foreach ($baseInfo as $item) {
                $tDay         = $item['t_month'];
                $newUser      = $item['new_user'] ?? 0;
                $costDiscount = $item['cost_discount'] ?? 0;
                $payMoney     = $item['pay_money'] ?? 0;
                $payMoneyOld  = $item['pay_money_old'] ?? 0;

                if (!isset($chartDataByGroup[1][$tDay])) {
                    $chartDataByGroup[1][$tDay] = [
                        'tday' => $tDay,
                        'num'  => 0,
                    ];
                }
                $chartDataByGroup[1][$tDay]['num'] += $newUser;

                if (!isset($chartDataByGroup[2][$tDay])) {
                    $chartDataByGroup[2][$tDay] = [
                        'tday' => $tDay,
                        'num'  => 0,
                    ];
                }
                $chartDataByGroup[2][$tDay]['num'] += $costDiscount;

                if (!isset($chartDataByGroup[5][$tDay])) {
                    $chartDataByGroup[5][$tDay] = [
                        'tday' => $tDay,
                        'num'  => 0,
                    ];
                }
                $chartDataByGroup[5][$tDay]['num'] += $payMoney;

                if (!isset($chartDataByGroup[4][$tDay])) {
                    $chartDataByGroup[4][$tDay] = [
                        'tday' => $tDay,
                        'num'  => 0,
                    ];
                }
                $chartDataByGroup[4][$tDay]['num'] += $payMoneyOld;
            }
        }

        if (!empty($info)) {
            foreach ($info as $item) {
                if (empty($item['goal_type'])) continue;

                $goalType = (int)$item['goal_type'];
                if (!in_array($goalType, $typeList)) continue;

                isset($infoGroupType[$goalType])
                    ? $infoGroupType[$goalType]['target'] += $item['goal_num']
                    : $infoGroupType[$goalType]['target'] = $item['goal_num'];
            }
        }

        if (!empty($month)) {
            if ($month == date("m")) {
                $dayNum = \date('d', strtotime('-1day'));
            }
            else {
                $dayNum = \date('t', strtotime($rangeStart));
            }

            $dayTotal = \date('t', strtotime($rangeStart));

            foreach ($infoGroupType as &$item) {
                $t = $item['target'] ?? 0;
                if (empty($t)) {
                    $item['should_complete'] = 0.00;
                }
                else {
                    $item['should_complete'] = round($t * $dayNum / $dayTotal, 2);
                }
            }

        }
        else {
            $infoGroupTypeMo = [];
            $currentMonth    = \date('Y-m');
            foreach ($info as $chill) {
                if (empty($item['goal_type'])) continue;

                $goalType = (int)$chill['goal_type'];
                if (!in_array($goalType, $typeList)) continue;
                $mo = $chill['mo'];

                isset($infoGroupTypeMo[$goalType][$mo])
                    ? $infoGroupTypeMo[$goalType][$mo]['target'] += $chill['goal_num']
                    : $infoGroupTypeMo[$goalType][$mo]['target'] = $chill['goal_num'];
            }

            foreach ($infoGroupType as $kk => &$item) {
                $d = $infoGroupTypeMo[$kk] ?? [];

                if (empty($d)) {
                    $item['should_complete'] = 0.00;
                }
                else {
                    $lastDayNum = \date('d', strtotime('-1day'));
                    $monthDays  = \date('t');
                    $num        = 0;
                    foreach ($d as $moMo => $ii) {
                        if ($moMo < $currentMonth) {
                            $num += ($ii['target'] ?? 0);
                        }

                        if ($moMo == $currentMonth) {
                            $tt = $ii['target'] ?? 0;

                            if (!empty($tt)) {
                                $num += ($tt * $lastDayNum / $monthDays);
                            }
                        }
                    }

                    $item['should_complete'] = $num;
                }
            }
        }

        unset($item);

        foreach ($typeList as $_type) {
            $title          = GoalsManagerServ::GOALS_TYPE[$_type];
            $unit           = ($_type == 1) ? '人' : '￥';
            $target         = $infoGroupType[$_type]['target'] ?? 0;
            $shouldComplete = $infoGroupType[$_type]['should_complete'] ?? 0.00;
            $_chartData     = $chartDataByGroup[$_type] ?? [];
            $current        = array_sum(array_column($_chartData, 'num') ?? []);

            if (count($_chartData) == 1) {
                foreach ($_chartData as &$foo) {
                    if (empty($target) || empty($foo['num'])) {
                        $foo['completed_total'] = 0.00;
                    }
                    else {
                        $foo['completed_total'] = (float)number_format(round($foo['num'] / $target * 100, 2), 2);
                    }
                }
            }
            else {
                ksort($_chartData);
                $lastNum = 0;
                foreach ($_chartData as &$foo) {
                    $lastNum = $lastNum + ($foo['num'] ?? 0);

                    if (empty($lastNum) || empty($target)) {
                        $foo['completed_total'] = 0.00;
                    }
                    else {
                        $foo['completed_total'] = (float)number_format(round($lastNum / $target * 100, 2), 2);
                    }
                }
            }

            if ($_type == 1) {
                $prop = 'new_user';
            }
            elseif ($_type == 2) {
                $prop = 'cost_discount';
            }
            elseif ($_type == 5) {
                $prop = 'pay_money';
            }
            elseif ($_type == 4) {
                $prop = 'pay_money_old';
            }
            else {
                $prop = '';
            }

            $block[$_type] = [
                'title'           => $title,
                'unit'            => $unit,
                'prop'            => $prop,
                'current'         => $current,
                'target'          => $target,
                'should_complete' => $shouldComplete,
                'chart_data'      => [
                    'config' => [
                        ['prop' => 'tday', 'label' => '时间'],
                        ['prop' => 'num', 'label' => '完成量'],
                    ],
                    'list'   => array_values($_chartData),
                ],
            ];
        }
        unset($foo);

        // 毛利(付费金额 - 成本)
        // 成本获取
        if ($rangeEnd > ($lastDay = \date('Y-m-d', strtotime('-1day')))) {
            $rangeEnd = $lastDay;
        }
        $options['range_date_end'] = $rangeEnd;

        $planBaseServ = new PlanBaseServ();
        $baseInfo     = $planBaseServ->simplePaymentInfo(
            $options, [], [], [
                new Fragment('SUM(pay_money) as pay_money'),
            ]
        );

        $payMoney   = $baseInfo[0]['pay_money'];
        $settleReq  = new Request(Uri::SETTLE_GROSS_DAILY);
        $response   = $settleReq->get(['start_date' => $rangeStart, 'end_date' => $rangeEnd]);
        $response   = \json_decode($response, true);
        $settleCost = 0;

        if (!empty($response['data'])) {
            $responseData = &$response['data'];
            foreach ($responseData as $foo) {
                $settleCost += ($foo['AMOUNT_CHANNEL_DISCOUNT'] ?? 0);
                $settleCost += ($foo['AMOUNT_CHANNEL_ROAD'] ?? 0);
                $settleCost += ($foo['AMOUNT_CHANNEL_INCOME'] ?? 0);
                $settleCost += ($foo['AMOUNT_CP'] ?? 0);
                $settleCost += ($foo['AMOUNT_CP_VIRTUAL'] ?? 0);
                $settleCost += ($foo['AMOUNT_BUY'] ?? 0);
            }
        }
        $profit  = $payMoney - $settleCost;
        $block[] = [
            'title'   => '业务毛利',
            'prop'    => 'gross_profit',
            'unit'    => '￥',
            'current' => $profit,
        ];


        return $this->success(array_values($block));
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function goalsBlockAction(): array
    {
        $request     = \Plus::$app->request;
        $year        = $request->getValue('year', date('Y'));
        $month       = $request->getValue('month');
        $params      = ['year' => $year];
        $showGoalsId = [5, 3, 4, 1, 2];
        $block       = [];
        $rangeStart  = $year . '-01-01';
        $rangeEnd    = $year . '-12-31';

        if (!empty($month)) {
            $params['month'] = $month;
            $rangeStart      = $year . '-' . $month . '-01';
            $rangeEnd        = (new \DateTime($rangeStart))->format('Y-m-t');
            if ($month == date("m")) {
                $rangeEnd = date("Y-m-d", strtotime("-1 day"));
            }
        }

        $columns = [
            't1.cp_game_id',
            new Fragment("IF(t1.`cp_game_id` = 0, '其他游戏合计', group_concat(distinct CP_GAME_NAME)) as cp_game_name"),
        ];

        foreach (GoalsManagerServ::GOALS_TYPE as $i => $ignoreFoo) {
            $columns[] = new Fragment(sprintf('SUM(IF(GOAL_TYPE = %d, goal_num, 0)) as goal_%d', $i, $i));
        }

        $options = [
            'range_date_start' => $rangeStart,
            'range_date_end'   => $rangeEnd,
        ];

        $infoColumns = [
            'power.cp_game_id',
            'cp_conf.cp_game_name',
            new Fragment('SUM(new_user) as new_user'),
            new Fragment('SUM(cost_discount) as cost_discount'),
            new Fragment('SUM(pay_money) as pay_money_old'),
            new Fragment('SUM(pay_money_new) as pay_money_new_old'),
        ];

        $newUserDistributeOption = [
            'range_date_dimension' => 2,
            'range_date_start'     => $rangeStart,
            'range_date_end'       => $rangeEnd,
            'event_type'           => '3',
            'report_type'          => '1',
            'group_type'           => '2',
        ];

        $newUserDistributeColumns = [
            'cp_game_id',
            new Fragment('SUM(if(day_type =0, num, 0)) as pay_money_new'), // 当月维度新增付费金额day_type = 0
            new Fragment('SUM(num) as pay_money'), // 当月维度付费总金额
        ];

        $re                    = (new GoalsManagerServ())->listGoalsCpGame($params, ['t1.cp_game_id'], $columns, ['goal_5' => 'DESC']);
        $infoRe                = (new PlanBaseServ())->simpleBothInfo($options, ['power.cp_game_id'], ['cost_discount' => 'DESC'], $infoColumns);
        $newUserDistributeList = (new NewUserDistributeServ())->simpleInfo($newUserDistributeOption, ['cp_game_id'], [], $newUserDistributeColumns);
        $re                    = array_column($re, null, 'cp_game_id');
        $infoRe                = array_column($infoRe, null, 'cp_game_id');
        $newUserDistributeList = array_column($newUserDistributeList, null, 'cp_game_id');
        $resultCollect         = [];

        // 获取游戏ID作为列表主体
        if (empty($re)) {
            $cpGames    = array_column($infoRe, 'cp_game_id');
            $cpGamesMap = array_column($infoRe, 'cp_game_name', 'cp_game_id');
        }
        else {
            $cpGames    = array_column($re, 'cp_game_id');
            $cpGamesMap = array_column($re, 'cp_game_name', 'cp_game_id');
        }

        sort($cpGames);
        ksort($cpGamesMap);

        $hasTargetCpGame = array_flip(array_diff($cpGames, [0]));

        $matchGoalFn = function (&$result, $item, $showGoalNum = [5, 3, 4, 2, 1]) {
            foreach ($showGoalNum as $i) {
                $k   = 'goal_' . $i;
                $num = $item[$k] ?? 0;
                isset($result[$k])
                    ? $result[$k] += $num
                    : $result[$k] = $num;
            }
        };

        foreach ($cpGames as $i) {
            $chill = [
                'cp_game_name' => $cpGamesMap[$i] ?? '其他游戏合计',
                'cp_game_id'   => $i,
            ];

            $newUserPay   = 0;
            $payMoney     = 0;
            $oldUserPay   = 0;
            $costDiscount = 0;
            $newUser      = 0;

            if ($i == 0) {
                // '其他游戏合计'处理
                $infoDiff = array_diff_key($infoRe, $hasTargetCpGame);
                $reDiff   = array_diff_key($re, $hasTargetCpGame);
                $disDiff  = array_diff_key($newUserDistributeList, $hasTargetCpGame);

                foreach ($infoDiff as $item) {
                    $newUser      += ($item['new_user'] ?? 0);
                    $costDiscount += ($item['cost_discount'] ?? 0);
                }

                foreach ($disDiff as $item) {
                    $payMoney   += ($item['pay_money'] ?? 0);
                    $newUserPay += ($item['pay_money_new'] ?? 0);
                    $oldUserPay += (($item['pay_money'] ?? 0) - ($item['pay_money_new'] ?? 0));
                }

                $chill['pay_money']     = $payMoney;
                $chill['pay_money_new'] = $newUserPay;
                $chill['pay_money_old'] = $oldUserPay;
                $chill['new_user']      = $newUser;
                $chill['cost_discount'] = $costDiscount;

                $matchGoalFn($chill, []);
                foreach ($reDiff as $item) {
                    $matchGoalFn($chill, $item);
                }
            }
            else {
                $chillInfo              = $infoRe[$i] ?? [];
                $chillReInfo            = $re[$i] ?? [];
                $chillDisInfo           = $newUserDistributeList[$i] ?? [];
                $chill['pay_money']     = $chillDisInfo['pay_money'] ?? 0;
                $chill['pay_money_new'] = $chillDisInfo['pay_money_new'] ?? 0;
                $chill['pay_money_old'] = $chill['pay_money'] - $chill['pay_money_new'];
                $chill['new_user']      = $chillInfo['new_user'] ?? 0;
                $chill['cost_discount'] = $chillInfo['cost_discount'] ?? 0.00;

                $matchGoalFn($chill, $chillReInfo);
            }

            $resultCollect[$i] = $chill;
        }
        // 排序
        // 踢出 “其他游戏合计”
        $otherIndex = -1;
        foreach ($resultCollect as $i => $item) {
            if ($item['cp_game_name'] == '其他游戏合计') {
                $otherIndex = $i;
                break;
            }
        }
        $otherCpGame     = array_splice($resultCollect, $otherIndex, 1)[0] ?? [];
        $resultCollect   = array_order($resultCollect, 'goal_2', SORT_DESC, 'pay_money', SORT_DESC);
        $resultCollect[] = $otherCpGame;

        unset($item);
        foreach ($resultCollect as &$item) {
            $block = [];

            foreach ($showGoalsId as $i) {
                $targetNum  = 0;
                $currentNum = 0;
                $title      = GoalsManagerServ::GOALS_TYPE[$i];
                $unit       = $i == 1 ? '人' : '￥';

                if ($i == 1) {
                    $currentNum = $item['new_user'] ?? 0;
                    $targetNum  = $item['goal_1'] ?? 0;
                }
                elseif ($i == 2) {
                    $currentNum = $item['cost_discount'] ?? 0;
                    $targetNum  = $item['goal_2'] ?? 0;
                }
                elseif ($i == 3) {
                    $currentNum = $item['pay_money_new'] ?? 0;
                    $targetNum  = $item['goal_3'] ?? 0;
                }
                elseif ($i == 4) {
                    $currentNum = $item['pay_money_old'] ?? 0;
                    $targetNum  = $item['goal_4'] ?? 0;
                }
                elseif ($i == 5) {
                    $currentNum = $item['pay_money'] ?? 0;
                    $targetNum  = ($item['goal_3'] ?? 0) + ($item['goal_4'] ?? 0);
                }

                if (\date('m') == \date('m', strtotime($rangeStart))) {
                    $lastDays = \date('d', strtotime('-1day'));
                }
                else {
                    $lastDays = \date('t', strtotime($rangeStart));
                }

                $shouldComplete = round($targetNum * $lastDays / (\date('t', strtotime($rangeStart))));

                $block[] = [
                    'title'           => $title,
                    'unit'            => $unit,
                    'current'         => $currentNum,
                    'target'          => $targetNum,
                    'should_complete' => $shouldComplete,
                ];
            }

            $item['data'] = array_values($block);
            $item         = array_intersect_key($item, ['cp_game_id' => '', 'cp_game_name' => '', 'data' => []]);
        }
        unset($item);

        return $this->success(array_values($resultCollect));
    }

    /**
     * 完成率折线图
     *
     * @return array
     */
    public function completionRateLineAction(): array
    {
        $request       = \Plus::$app->request;
        $year          = $request->getValue('year', date('Y'));
        $month         = $request->getValue('month', date('m'));
        $cpGameId      = $request->getValue('cp_game_id');
        $dateDimension = $request->getValue('range_date_dimension', 2);     // 汇总维度
        $dataType      = $request->getValue('data_type', 'new_user');       // 数据类型(1-消耗, 2-新增用户 , 3-付费金额)
        $rangeStart    = "{$year}-{$month}-01";
        $rangeEnd      = (new \DateTime("{$year}-{$month}-01"))->format('Y-m-t');
        if ($month == date("m")) {
            $rangeEnd = date("Y-m-d", strtotime("-1 day"));
        }
        $goalType = 0;

        if ($dataType == 'new_user') {
            $goalType = 1;
        }
        elseif ($dataType == 'cost') {
            $goalType = 2;
        }
        elseif ($dataType == 'payment') {
            $goalType = 5;
        }

        $goalServ  = new GoalsManagerServ();
        $goalsInfo = $goalServ->listGoalsCpGame([
            'year'       => $year,
            'month'      => $month,
            'cp_game_id' => $cpGameId,
            'goal_type'  => $goalType,
        ]);

        $goalsInfo = $goalsInfo[0] ?? [];
        $goalNum   = $goalsInfo['goal_num'] ?? 0;

        $options = [
            'range_date_start'     => $rangeStart,
            'range_date_end'       => $rangeEnd,
            'range_date_dimension' => $dateDimension,
            'cp_game_id'           => $cpGameId,
        ];

        $serv = new PlanBaseServ();

        //获取每月周数天数
        $numOfDays  = date("t", mktime(0, 0, 0, $month, 1, $year));
        $numOfWeeks = (int)date('W', strtotime("last day of $year-$month")) - (int)date('W', strtotime("$year-$month-01")) + 1;
        if ($dateDimension == 3) {
            $groups = ['my_week'];
            $avgNum = round($goalNum / $numOfWeeks);
        }
        elseif ($dateDimension == 4) {
            $groups = ['my_month'];
            $avgNum = $goalNum;
        }
        else {
            $groups = ['tday'];
            $avgNum = round($goalNum / $numOfDays);
        }
        $config = [
            ['label' => '时间', 'prop' => 'tday'],
        ];

        if ($cpGameId == 0) {
            $haveGoalCpIds = $goalServ->listHaveGoalCpGameIds(['year' => $year, 'month' => $month]);
            unset($options['cp_game_id']);
            if (empty($haveGoalCpIds)) {
                $options['cp_game_id'] = 0;
            }
            else {
                $options['cp_game_id[!]'] = $haveGoalCpIds;
            }
        }

        if ($dataType == 'new_user') {
            $geValueFn = fn($dd) => ($dd['new_user'] ?? 0);
            $re        = $serv->simpleBaseInfo($options, $groups, [], ['tday', new Fragment('SUM(new_user) as new_user')]);
            $config[]  = [
                'label' => '新用户', 'prop' => 'new_user', 'series' => ['type' => 'line'], 'markLine' => ['yAxis' => $avgNum],
            ];
        }
        elseif ($dataType == 'payment') {
            $geValueFn = fn($dd) => ($dd['pay_money'] ?? 0);
            $re        = $serv->simplePaymentInfo($options, $groups, [], ['tday', new Fragment('SUM(pay_money) as pay_money')]);
            $config[]  = [
                'label' => '付费金额', 'prop' => 'pay_money', 'series' => ['type' => 'line'], 'markLine' => ['yAxis' => $avgNum],
            ];
        }
        elseif ($dataType == 'cost') {
            $geValueFn = fn($dd) => ($dd['cost'] ?? 0);
            $re        = $serv->simpleBaseInfo($options, $groups, [], ['tday', new Fragment('SUM(cost_discount) as cost')]);
            $config[]  = [
                'label' => '消耗金额', 'prop' => 'cost', 'series' => ['type' => 'line'], 'markLine' => ['yAxis' => $avgNum],
            ];
        }
        else {
            $geValueFn = fn($dd) => 0;
            $re        = [];
        }

        if ($dateDimension == 3) {
            $tDayFn = function (&$item) {
                $item['tday'] = $item['start_day'] . '/' . $item['end_day'];
            };
        }
        elseif ($dateDimension == 4) {
            $tDayFn = function (&$item) {
                $item['tday'] = $item['my_month'];
            };
        }
        else {
            $tDayFn = fn() => true;
        }

        $lastNum = 0;
        foreach ($re as &$item) {
            $tDayFn($item);

            $num     = $geValueFn($item);
            $lastNum += $num;

            if (empty((float)$lastNum) || empty((float)$goalNum)) {
                $item['month_completed_total'] = 0.00;
            }
            else {
                $item['month_completed_total'] =
                    (float)number_format(round($lastNum / $goalNum * 100, 2), 2);
            }

        }

        return $this->success([
            'config' => $config,
            'list'   => $re,
        ]);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function infoGroupChannelAction(): array
    {
        $request       = \Plus::$app->request;
        $year          = $request->getValue('year', date('Y'));
        $month         = $request->getValue('month', date('m'));
        $cpGameId      = $request->getValue('cp_game_id');
        $dateDimension = $request->getValue('range_date_dimension', 2);     // 汇总维度
        $dataType      = $request->getValue('data_type', 'new_user');       // 数据类型(1-消耗, 2-新增用户 , 3-付费金额)
        $rangeStart    = "{$year}-{$month}-01";
        $rangeEnd      = (new \DateTime("{$year}-{$month}-01"))->format('Y-m-t');
        if ($month == date("m")) {
            $rangeEnd = date("Y-m-d", strtotime("-1 day"));
        }

        $options = [
            'range_date_start'     => $rangeStart,
            'range_date_end'       => $rangeEnd,
            'range_date_dimension' => $dateDimension,
            'cp_game_id'           => $cpGameId,
        ];
        $orderBy = [];

        if ($dateDimension == 3) {
            $groups             = ['my_week'];
            $orderBy['my_week'] = 'ASC';
            $getTDayFn          = fn($item) => $item['my_week'];
        }
        elseif ($dateDimension == 4) {
            $groups              = ['my_month'];
            $orderBy['my_month'] = 'ASC';
            $getTDayFn           = fn($item) => $item['my_month'];
        }
        else {
            $groups          = ['tday'];
            $orderBy['tday'] = 'ASC';
            $getTDayFn       = fn($item) => $item['tday'];
        }

        $config = [
            ['label' => '时间', 'prop' => 'tday'],
        ];

        $getNumFn = fn() => 0;

        if ($dataType == 'new_user') {
            $orderBy['new_user'] = 'DESC';
            $getNumFn            = fn($item) => $item['new_user'];
        }
        elseif ($dataType == 'cost') {
            $orderBy['cost'] = 'DESC';
            $getNumFn        = fn($item) => $item['cost'];
        }

        $goalServ = new GoalsManagerServ();

        if ($cpGameId == 0) {
            $hasGoalCpIds = $goalServ->listHaveGoalCpGameIds(['year' => $year, 'month' => $month]);
            unset($options['cp_game_id']);
            $options['cp_game_id[!]'] = $hasGoalCpIds;
        }

        $result = (new PlanBaseServ())
            ->simpleBaseInfo(
                $options,
                array_merge($groups, ['power.channel_main_id']),
                $orderBy,
                [
                    'tday',
                    'power.channel_main_id',
                    new Fragment('SUM(new_user) as new_user'),
                    new Fragment('SUM(cost_discount) as cost'),
                ]);


        $summaryRow = [];
        $list       = [];
        foreach ($result as $item) {
            $channelId   = $item['channel_main_id'];
            $channelMark = 'channel_' . $channelId;
            $num         = $getNumFn($item);
            $tDay        = $getTDayFn($item);

            isset($summaryRow[$channelMark])
                ? $summaryRow[$channelMark] += $num
                : $summaryRow[$channelMark] = $num;

            if (!isset($list[$tDay])) {
                if ($dateDimension == 3) {
                    $list[$tDay] = ['tday' => implode('/', $this->inferrWeekRange($tDay, $month))];
                }
                else {
                    $list[$tDay] = ['tday' => $tDay,];
                }
            }

            $chill = &$list[$tDay];

            isset($chill[$channelMark])
                ? $chill[$channelMark] += $num
                : $chill[$channelMark] = $num;
        }
        unset($chill);

        arsort($summaryRow);
        $summaryRowAfter = array_slice($summaryRow, 0, 5);
        $top5Channel     = array_keys($summaryRowAfter);

        foreach ($summaryRow as $k => $item) {
            if (!in_array($k, $top5Channel)) {
                !isset($summaryRow['channel_other'])
                    ? $summaryRow['channel_other'] = $item
                    : $summaryRow['channel_other'] += $item;

                unset($summaryRow[$k]);
            }
        }

        foreach ($list as &$foo) {
            foreach ($foo as $k => $chill) {
                if (!strstr($k, 'channel_')) continue;

                if (!in_array($k, $top5Channel)) {
                    !isset($foo['channel_other'])
                        ? $foo['channel_other'] = round($chill)
                        : $foo['channel_other'] += round($chill);

                    unset($foo[$k]);
                }
                else {
                    $foo[$k] = round($chill);
                }

            }
        }

        $fields           = [['title' => '时间', 'dataIndex' => 'tday']];
        $constConfCollect = (new BasicServ())->getMultiOptions(['channel_main_id']);
        $channelIdMap     = array_column($constConfCollect->get('channel_main_id')->toArray(), 'val', 'key');

        foreach ($top5Channel as $channelMark) {
            $channelId = (int)str_replace('channel_', '', $channelMark);
            if ($channelId == 0) {
                $channelName = '自然量';
            }
            else {
                $channelName = $channelIdMap[$channelId] ?? '';
            }

            $fields[] = ['title' => $channelName, 'dataIndex' => $channelMark];
        }
        $fields[] = ['title' => '其他', 'dataIndex' => 'channel_other'];

        foreach ($summaryRow as &$foo) {
            $foo = round($foo);
        }

        return $this->success([
            'fields'  => $fields,
            'summary' => $summaryRow,
            'list'    => array_values($list),
        ]);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function infoGroupTeamAction(): array
    {
        $request       = \Plus::$app->request;
        $year          = $request->getValue('year', date('Y'));
        $month         = $request->getValue('month', date('m'));
        $cpGameId      = $request->getValue('cp_game_id');
        $dateDimension = $request->getValue('range_date_dimension', 2);     // 汇总维度
        $dataType      = $request->getValue('data_type', 'new_user');       // 数据类型(1-消耗, 2-新增用户 , 3-付费金额)
        $rangeStart    = "{$year}-{$month}-01";
        $rangeEnd      = (new \DateTime("{$year}-{$month}-01"))->format('Y-m-t');
        if ($month == date("m")) {
            $rangeEnd = date("Y-m-d", strtotime("-1 day"));
        }
        $options = [
            'range_date_start'     => $rangeStart,
            'range_date_end'       => $rangeEnd,
            'range_date_dimension' => $dateDimension,
            'cp_game_id'           => $cpGameId,
        ];

        if ($dataType == 'new_user') {
            $options['goal_type'] = 1;
        }
        else {
            $options['goal_type'] = 2;
        }

        if ($dateDimension == 3) {
            $groups             = ['my_week'];
            $orderBy['my_week'] = 'ASC';
            $getTDayFn          = fn($item) => $item['my_week'];
        }
        elseif ($dateDimension == 4) {
            $groups              = ['my_month'];
            $orderBy['my_month'] = 'ASC';
            $getTDayFn           = fn($item) => $item['my_month'];
        }
        else {
            $groups          = ['tday'];
            $orderBy['tday'] = 'ASC';
            $getTDayFn       = fn($item) => $item['tday'];
        }

        $orderBy['reach_num'] = 'DESC';
        $groups[]             = 'team_name';


        $goalServ = new GoalsManagerServ();
        if ($cpGameId == 0) {
            $haveGoalCpIds = $goalServ->listHaveGoalCpGameIds(['year' => $year, 'month' => $month]);
            unset($options['cp_game_id']);
            $options['cp_game_id[!]'] = $haveGoalCpIds;
        }

        $info = $goalServ->listReachNumForTeam(
            $options, $groups, $orderBy,
            ['tday', 'team_name', 'goal_type', new Fragment('SUM(reach_num) as reach_num')]
        );

        $list       = [];
        $summaryRow = [];

        foreach ($info as $item) {
            $tDay     = $getTDayFn($item);
            $teamName = $item['team_name'];

            $num = $item['reach_num'] ?? 0;

            !isset($summaryRow[$teamName])
                ? $summaryRow[$teamName] = $num
                : $summaryRow[$teamName] += $num;

            if (!isset($list[$tDay])) {
                if ($dateDimension == 3) {
                    $list[$tDay] = ['tday' => implode('/', $this->inferrWeekRange($tDay, $month)),];
                }
                else {
                    $list[$tDay] = ['tday' => $tDay];
                }
            }

            $chill = &$list[$tDay];

            !isset($chill[$teamName])
                ? $chill[$teamName] = $num
                : $chill[$teamName] += $num;
        }

        $fields = [
            ['title' => '时间', 'dataIndex' => 'tday'],
        ];

        foreach (array_keys($summaryRow) as $team) {
            if ($team == '其他') continue;

            $fields[] = ['title' => $team, 'dataIndex' => $team];
        }
        $fields[] = ['title' => '其他', 'dataIndex' => '其他'];

        return $this->success([
            'fields'  => $fields,
            'list'    => array_values($list),
            'summary' => $summaryRow,
        ]);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function distributeInfoAction(): array
    {
        $request       = \Plus::$app->request;
        $year          = $request->getValue('year', date('Y'));
        $month         = $request->getValue('month', date('m'));
        $cpGameId      = $request->getValue('cp_game_id');
        $dateDimension = $request->getValue('range_date_dimension', 2);     // 汇总维度// 数据类型(1-消耗, 2-新增用户 , 3-付费金额)
        $rangeStart    = "{$year}-{$month}-01";
        $rangeEnd      = (new \DateTime("{$year}-{$month}-01"))->format('Y-m-t');
        if ($month == date("m")) {
            $rangeEnd = date("Y-m-d", strtotime("-1 day"));
        }
        $options = [
            'range_date_start'     => $rangeStart,
            'range_date_end'       => $rangeEnd,
            'range_date_dimension' => $dateDimension,
            'cp_game_id'           => $cpGameId,
            'event_type'           => 3,
            'group_type'           => 2,
            'report_type'          => 1,
        ];

        if ($cpGameId == 0) {
            $haveGoalCpIds = (new GoalsManagerServ())->listHaveGoalCpGameIds(['year' => $year, 'month' => $month]);
            unset($options['cp_game_id']);
            $options['cp_game_id[!]'] = $haveGoalCpIds;
        }

        if ($dateDimension == 3) {
            $groups             = ['my_week'];
            $orderBy['my_week'] = 'ASC';
            $getTDayFn          = fn($item) => $item['my_week'];
        }
        elseif ($dateDimension == 4) {
            $groups              = ['my_month'];
            $orderBy['my_month'] = 'ASC';
            $getTDayFn           = fn($item) => $item['my_month'];
        }
        else {
            $groups          = ['tday'];
            $orderBy['tday'] = 'ASC';
            $getTDayFn       = fn($item) => $item['tday'];
        }

        $columns = [
            'tday',
            new Fragment('SUM(NUM) as pay_money_all'),
            new Fragment(' SUM(IF(DAY_TYPE = 0, NUM, 0)) as pay_money_new'),
            new Fragment('SUM(IF(DAY_TYPE != 0, NUM, 0)) as pay_money_old'),
        ];

        $infoRe = (new NewUserDistributeServ())->simpleInfo($options, $groups, [], $columns);

        $summaryRow = ['pay_money_all' => 0, 'pay_money_new' => 0, 'pay_money_old' => 0];
        foreach ($infoRe as &$item) {
            if ($dateDimension == 3) {
                $item['tday'] = implode('/', $this->inferrWeekRange($getTDayFn($item), $month));
            }

            $payMoneyAll = $item['pay_money_all'];
            $payMoneyNew = $item['pay_money_new'];
            $payMoneyOld = $item['pay_money_old'];

            $summaryRow['pay_money_all'] += $payMoneyAll;
            $summaryRow['pay_money_new'] += $payMoneyNew;
            $summaryRow['pay_money_old'] += $payMoneyOld;

            if (empty($payMoneyNew) || empty($payMoneyAll)) {
                $item['pay_money_new_percent'] = '0.00%';
            }
            else {
                $item['pay_money_new_percent'] = number_format(round($payMoneyNew / $payMoneyAll * 100, 2), 2) . '%';
            }

            if (empty($payMoneyOld) || empty($payMoneyAll)) {
                $item['pay_money_old_percent'] = '0.00%';
            }
            else {
                $item['pay_money_old_percent'] = number_format(round($payMoneyOld / $payMoneyAll * 100, 2), 2) . '%';
            }

//            $item['pay_money_new_percent'] = number_format(round($payMoneyNew / $payMoneyAll * 100, 2), 2) . '%';
//            $item['pay_money_old_percent'] = number_format(round($payMoneyOld / $payMoneyAll * 100, 2), 2) . '%';
        }

        if (empty($summaryRow['pay_money_new']) || empty($summaryRow['pay_money_all'])) {
            $summaryRow['pay_money_new_percent'] = '0.00%';
        }
        else {
            $summaryRow['pay_money_new_percent'] = number_format(round($summaryRow['pay_money_new'] / $summaryRow['pay_money_all'] * 100, 2), 2) . '%';
        }

        if (empty($summaryRow['pay_money_old']) || empty($summaryRow['pay_money_all'])) {
            $summaryRow['pay_money_old_percent'] = '0.00%';
        }
        else {
            $summaryRow['pay_money_old_percent'] = number_format(round($summaryRow['pay_money_old'] / $summaryRow['pay_money_all'] * 100, 2), 2) . '%';
        }

//        $summaryRow['pay_money_new_percent'] = number_format(round($summaryRow['pay_money_new'] / $summaryRow['pay_money_all'] * 100, 2), 2) . '%';
//        $summaryRow['pay_money_old_percent'] = number_format(round($summaryRow['pay_money_old'] / $summaryRow['pay_money_all'] * 100, 2), 2) . '%';

        $fields = [
            ['title' => '时间', 'dataIndex' => 'tday'],
            ['title' => '总付费金额', 'dataIndex' => 'pay_money_all'],
            ['title' => '新用户付费', 'dataIndex' => 'pay_money_new'],
            ['title' => '新用户付费占比', 'dataIndex' => 'pay_money_new_percent'],
            ['title' => '老用户付费', 'dataIndex' => 'pay_money_old'],
            ['title' => '老用户付费占比', 'dataIndex' => 'pay_money_old_percent'],
        ];

        return $this->success([
            'summary' => $summaryRow,
            'list'    => array_values($infoRe),
            'fields'  => $fields,
        ]);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function ltvRoiInfoAction(): array
    {
        $request       = \Plus::$app->request;
        $year          = $request->getValue('year', date('Y'));
        $month         = $request->getValue('month', date('m'));
        $type          = $request->getValue('type', 'ltv');
        $cpGameId      = $request->getValue('cp_game_id');
        $dateDimension = $request->getValue('range_date_dimension', 2);     // 汇总维度
//        $dataType      = $request->getValue('data_type', 'new_user');       // 数据类型(1-消耗, 2-新增用户 , 3-付费金额)
        $rangeStart = "{$year}-{$month}-01";
        $rangeEnd   = (new \DateTime("{$year}-{$month}-01"))->format('Y-m-t');
        if ($month == date("m")) {
            $rangeEnd = date("Y-m-d", strtotime("-1 day"));
        }
        $options = [
            'range_date_start'     => $rangeStart,
            'range_date_end'       => $rangeEnd,
            'range_date_dimension' => $dateDimension,
            'cp_game_id'           => $cpGameId,
        ];

        $orderBy = [];
        $dayMark = 'tday';

        if ($dateDimension == 3) {
            $groups             = ['my_week'];
            $orderBy['my_week'] = 'ASC';
            $getTDayFn          = fn($item) => $item['my_week'];
            $dayMark            = 'my_week';
        }
        elseif ($dateDimension == 4) {
            $groups              = ['my_month'];
            $orderBy['my_month'] = 'ASC';
            $getTDayFn           = fn($item) => $item['my_month'];
            $dayMark             = 'my_month';
        }
        else {
            $groups          = ['tday'];
            $orderBy['tday'] = 'ASC';
            $getTDayFn       = fn($item) => $item['tday'];
        }

        if ($cpGameId == 0) {
            unset($options['cp_game_id']);
            $haveGoalCpIds            = (new GoalsManagerServ())->listHaveGoalCpGameIds(['year' => $year, 'month' => $month]);
            $options['cp_game_id[!]'] = $haveGoalCpIds;
        }

        $baseRe = (new PlanBaseServ())
            ->simpleBaseInfo(
                $options,
                $groups,
                $orderBy,
                [
                    'tday',
                    new Fragment('SUM(new_user) as new_user'),
                    new Fragment('SUM(cost_discount) as cost_discount'),
                ]);

        $baseInfoGroupDay = array_column($baseRe, null, $dayMark);

        $ltvRe = (new LtvServ())->simpleLtv(
            array_merge($options, ['max_day_type' => 7]),
            array_merge($groups, ['day_type']),
            $orderBy,
            ['tday', new Fragment('SUM(money_all) as money_all'), 'day_type',]
        );

        $list       = [];
        $summaryRow = [];

        foreach ($ltvRe as $item) {
            $tDay        = $getTDayFn($item);
            $dayType     = $item['day_type'];
            $dayTypeMark = 'day_type_' . $dayType;
            $num         = $item['money_all'] ?? 0.00;

            isset($summaryRow[$dayTypeMark])
                ? $summaryRow[$dayTypeMark] += $num
                : $summaryRow[$dayTypeMark] = $num;

            if (!isset($list[$tDay])) {
                if ($dateDimension == 3) {
                    $list[$tDay] = ['tday' => implode('/', $this->inferrWeekRange($tDay, $month))];
                }
                else {
                    $list[$tDay] = ['tday' => $tDay];
                }
            }

            $chill = &$list[$tDay];

            isset($chill[$dayTypeMark])
                ? $chill[$dayTypeMark] += $num
                : $chill[$dayTypeMark] = $num;
        }

        if ($type == 'roi') {
            $summaryMu = array_sum(array_column($baseRe, 'cost_discount'));
            $countFn   = function ($num, $mu) {
                if (empty((float)$num) || empty((float)$mu)) {
                    return '0.00' . '%';
                }
                else {
                    return number_format(round($num / $mu * 100, 2), 2) . '%';
                }
            };

            $fieldFn = function ($i) {
                return ['title' => 'ROI' . $i, 'dataIndex' => 'day_type_' . $i];
            };
        }
        else {
            $summaryMu = array_sum(array_column($baseRe, 'new_user'));

            $countFn = function ($num, $mu) {
                if (empty((float)$num) || empty((float)$mu)) {
                    return '0.00';
                }
                else {
                    return number_format(round($num / $mu, 2), 2);
                }
            };

            $fieldFn = function ($i) {
                return ['title' => 'LTV' . $i, 'dataIndex' => 'day_type_' . $i];
            };
        }


        foreach ($list as $k => &$foo) {
            preg_match_all('/day_type_\d+/', implode(',', array_keys($foo)), $matcher);
            $matcher = $matcher[0];

            $info = $baseInfoGroupDay[$k];
            if ($type == 'roi') {
                $mu = $info['cost_discount'] ?? 0;
            }
            else {
                $mu = $info['new_user'] ?? 0;
            }

            foreach ($matcher as $kk) {
                $num      = $foo[$kk];
                $foo[$kk] = $countFn($num, $mu);
            }

        }
        unset($foo);

        preg_match_all('/day_type_\d+/', implode(',', array_keys($summaryRow)), $summaryMatcher);
        $summaryMatcher = $summaryMatcher[0] ?? [];
        foreach ($summaryMatcher as $k) {
            $num            = $summaryRow[$k];
            $summaryRow[$k] = $countFn($num, $summaryMu);
        }

        $fields = [
            ['title' => '时间', 'dataIndex' => 'tday'],
        ];

        for ($i = 1; $i <= 7; $i++) {
            $fields[] = $fieldFn($i);
        }

        return $this->success([
            'fields'  => $fields,
            'list'    => array_values($list),
            'summary' => $summaryRow,
        ]);
    }

    /**
     * @param $weekMark
     * @param $inMonth
     *
     * @return array
     * @throws \Exception
     */
    protected function inferrWeekRange($weekMark, $inMonth): array
    {
        $weekStart = new \DateTime($weekMark);

        $weekRange = [
            'start' => $weekStart->format('Y-m-d'),
            'end'   => $weekStart->add(new \DateInterval('P6D'))->format('Y-m-d'),
        ];

        $rangeStart = new \DateTime($weekRange['start']);
        $rangeEnd   = new \DateTime($weekRange['end']);

        if ($rangeStart->format('m') < $inMonth) {
            $weekRange['start'] = $rangeStart->format('Y') . '-' . str_pad($inMonth, 2, 0, STR_PAD_LEFT) . '-01';
        }

        if ($rangeEnd->format('m') > $inMonth) {
            $weekRange['end'] = $rangeStart->format('Y-m-t');
        }

        return $weekRange;
    }


}
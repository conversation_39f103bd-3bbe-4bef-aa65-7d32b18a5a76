<?php

namespace app\models\baseConfPlatform;

use Plus\MVC\Model\ActiveRecord;
/**
 * 推广账号配置表
 * @property int $ID 自增ID(CHANNEL_ID)
 * @property string $CHANNEL_ID 渠道
 * @property string $ACCOUNT_ID 账号id
 * <AUTHOR>
 */
class TbAdAccountConf extends ActiveRecord
{
    public $_primaryKey = 'id';

    /**
     * 初始化，设置数据、数据库连接类
     *
     * @param array $data 批量配置数据
     *
     */
    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->base_conf_platform;
    }
}

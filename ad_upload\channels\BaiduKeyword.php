<?php
/**
 * 百度关键字数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class <PERSON>du<PERSON>eyword extends AdBaseInterface
{

    private $akeyArr = [];


    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info);
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info);
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info);
    }


    /**
     * 上报数据
     * @param array $info
     */
    public function uploadData($info)
    {

        $akey = '';
        if (empty($this->akeyArr[$info['SV_KEY']])) {
            $sql         = "SELECT REMARK FROM tb_ad_account_conf WHERE ACCOUNT_ID = '" . $info['EXT_CLICK']['user_id'] . "'";
            $accountConf = \Plus::$app->base_conf_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
            $akey        = !empty($accountConf) ? $accountConf['REMARK'] : '';
            if (empty($akey)) {
                $akey = $info['EXT']['akey'];
            }
        } else {
            $akey = $this->akeyArr[$info['SV_KEY']];
        }
        if (!empty($akey) && $info['CALLBACK_URL'] != '{{CALLBACK_URL}}') {
            $this->akeyArr[$info['SV_KEY']] = $akey;
            $money                          = $info['MONEY'] * 100;
            $callbackUrlActive              = str_replace(['{{ATYPE}}', '{{AVALUE}}'], ['activate', 0], $info['CALLBACK_URL']);
            $callbackUrlReg                 = str_replace(['{{ATYPE}}', '{{AVALUE}}'], ['register', 0], $info['CALLBACK_URL']);
            $callbackUrlPay                 = str_replace(['{{ATYPE}}', '{{AVALUE}}'], ['orders', $money], $info['CALLBACK_URL']);
            $typeName                       = '';

            $callbackUrl = '';
            switch ($info['TYPE']) {
                case 'active':
                    $callbackUrl = $callbackUrlActive;
                    $typeName    = 'ACTIVE';
                    break;
                case 'register':
                    $callbackUrl = $callbackUrlReg;
                    $typeName    = 'REG';
                    break;
                case 'pay':
                    $callbackUrl = $callbackUrlPay;
                    $typeName    = 'PAY';
                    break;
            }
            $logInfo                 = $info['log_info'] ?? [];
            $logInfo['channel_code'] = 'baidu_keyword';

            $sign               = md5($callbackUrl . $akey);
            $callbackUrl       .= '&sign=' . $sign;
            $logInfo['request'] = json_encode(['url' => $callbackUrl]);
            $http               = new Http($callbackUrl);
            $res                = $http->get();

            //记录上报结果
            $logInfo['response'] = $res;
            $resArr              = json_decode($res, true);
            if (isset($resArr['error_code']) && $resArr['error_code'] == 0) {
                $logInfo['reported_status'] = 1;
            } else {
                $logInfo['reported_status'] = -1;
            }

            $this->log($info, $logInfo, $res, $callbackUrl);
        }// end if()
    }


    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

<?php

namespace app\ad_upload_tmp\contract;

use app\ad_upload_tmp\strategies\ActiveUploadStrategy;
use app\ad_upload_tmp\strategies\PayUploadStrategy;
use app\ad_upload_tmp\strategies\PayVirtualUploadStrategy;
use app\ad_upload_tmp\tool\CacheUploadAd;
use app\ad_upload_tmp\tool\ChannelFactory;

/**
 * 上报策略接口
 * <AUTHOR>
 */
abstract class AdUploadStrategyInterface
{

    /**
     * 上报动作
     * @var string
     */
    public $action = '';

    /**
     * 缓存处理
     * @var CacheUploadAd
     */
    public CacheUploadAd $cache;

    /**
     * 上次上报点id
     * @var int
     */
    public $lastId = 0;

    /**
     * 未归因匹配id
     * @var array
     */
    public $unmatchedIds = [];

    /**
     * 渠道id
     * @var int
     */
    public $channelId = 0;

    /**
     * 开始时间
     * @var string
     */
    public $timeBegin = '';

    /**
     * 结束时间
     * @var string
     */
    public $timeEnd = '';
    /**
     * 是否重跑
     * @var bool
     */
    public $isReRun = false;

    /**
     * 数据id、订单号
     * @var string
     */
    public $ids = '';

    /**
     * 是否强制上报，跳过重复检查
     * @var int
     */
    public $force = 0;

    /**
     * 未归因匹配时间, 3小时
     * @var string
     */
    public $unmatchedTime = '-3 hours';

    /**
     * 构造函数
     * @param int    $channelId 渠道id
     * @param string $timeBegin 开始时间
     * @param string $timeEnd   结束时间
     * @param bool   $isReRun   是否重跑
     * @param string $ids       数据id、订单号
     * @param int    $force     是否强制上报，跳过重复检查
     * @return void
     */
    public function __construct($channelId, $timeBegin, $timeEnd, $isReRun = false, $ids = '', $force = 0)
    {
        $this->channelId = $channelId;
        $this->cache     = new CacheUploadAd();
        $this->timeEnd   = $timeEnd;
        $this->timeBegin = $timeBegin;
        $this->isReRun   = $isReRun;
        $this->ids       = $ids;
        $this->force     = $force;
    }

    /**
     * 初始化上报点
     * @return void
     */
    public function initUploadLast()
    {
        $this->lastId       = $this->cache->getLastId($this->action, $this->channelId);
        $this->unmatchedIds = $this->cache->getUnmatchedIds($this->action, $this->channelId);
        if ($this->lastId == 0 && !in_array($this->action, [AdBaseInterface::ACTION_PAY_VIRTUAL, AdBaseInterface::ACTION_REMAIN])) {
            \Plus::$app->log->alert("渠道ID:{$this->channelId} 上报点不存在", [], AdBaseInterface::LOG_DIR);
        }

        // $this->lastId = strtotime('2024-12-20 14:01:46'); // 测试用
    }

    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    abstract public function getData(array $uploadConfig, string $packages): array;

    /**
     * 设置如果上一步getData 获取数据为空，设置最大上报id
     * 避免长期没有数据上报的渠道，查询过多数据
     * @return void
     */
    abstract public function setMaxLastId(): void;

    /**
     * 上报数据过滤
     * @param array $data         data
     * @param array $uploadConfig uploadConfig
     * @return array
     */
    public function filterData($data, $uploadConfig): array
    {
        $unmatchedIds = $this->unmatchedIds;
        $dataActive   = [];
        foreach ($data as $key => $val) {
            $uploadConfigOne = $uploadConfig[$val['PACKAGE_ID']] ?? [];
            if (empty($uploadConfigOne)) {
                \Plus::$app->log->error('not_match_config渠道配置不存在' . json_encode($val), [], AdBaseInterface::LOG_DIR);
            }
            foreach ($uploadConfigOne as $v) {
                //1为全量上报，2为匹配点击上报
                if ($v['UPLOAD_METHOD'] == 1 || ($v['UPLOAD_METHOD'] == 2)) {
                    $channelId = $v['UPLOAD_METHOD'] == 1 ? $v['CHANNEL_ID'] : $val['CHANNEL_ID'];
                    $clickId   = $v['UPLOAD_METHOD'] == 1 ? 0 : $val['CLICK_ID'];

                    //未归因数据缓存
                    $unmatchedIds = $this->cache->processUnmatchedIds($unmatchedIds, $val, 'IDFV', 'CHANNEL_ID');
                    if (in_array($val["IDFV"], $unmatchedIds)) {
                        \Plus::$app->log->info('unmatched_' . $this->action . $val['IDFV'], [], AdBaseInterface::LOG_DIR);
                        //continue; //剔除未匹配的数据
                    }
                    // 没有force=1，剔除已上报的数据
                    if ($this->force == 0) {
                        $key = 'ad_up_repeat_' . $this->action;
                        if (\Plus::$app->redis82->hget($key, $val['IDFV'])) {
                            \Plus::$app->log->warning('repeat_' . $this->action . $val['IDFV'], [], AdBaseInterface::LOG_DIR);
                            continue;
                        }
                        \Plus::$app->redis82->hset($key, $val['IDFV'], 1);
                    }

                    $dataActive[] = array_merge($val, [
                        'CHANNEL_ID'    => $channelId,
                        'UPLOAD_METHOD' => $v['UPLOAD_METHOD'],
                        'EXT_ID'        => $v['ID'],
                        'EXT'           => $v['EXT'],
                        'CLICK_ID'      => $clickId,
                    ]);
                }// end if()
            }// end foreach()
        }// end foreach()

        $this->unmatchedIds = $unmatchedIds;

        return $dataActive;
    }

    /**
     * 处理数据
     * @param array $data         data
     * @param array $uploadConfig 上报配置
     * @return array
     */
    public function processData(array $data, $uploadConfig): array
    {
        $rs = [];
        foreach ($data as $val) {
            // 通过原始的MD5_DEVICE_ID匹配点击表
            $deviceId    = \Plus::$service->dataEncryptor->decrypt($val['DEVICE_ID'], 1)[$val['DEVICE_ID']];
            $md5DeviceId = \Plus::$service->dataEncryptor->decrypt($val['MD5_DEVICE_ID'], 2)[$val['MD5_DEVICE_ID']];
            //$dataTmp     = $this->fillUpClick($val);
            $dataTmp = $val;

            $dataTmp['DEVICE_ID']     = $deviceId != '' ? $deviceId : '111-111-111-111';
            $dataTmp['MD5_DEVICE_ID'] = $md5DeviceId != '' ? $md5DeviceId : md5('111-111-111-111');
            // 广告上报，并且将本次上报数据的最大id存入缓存
            $action              = $val['TYPE'];
            $dataTmp['log_info'] = [
                'package_id'   => $val['PACKAGE_ID'] ?? 0,
                'channel_id'   => $val['CHANNEL_ID'] ?? 0,
                'action'       => $action,
                'cp_game_id'   => $val['CP_GAME_ID'] ?? 0,
                'game_id'      => $val['GAME_ID'] ?? 0,
                'core_account' => $val['CORE_ACCOUNT'] ?? 0,
                'oaid'         => $val['OAID'] ?? '',
                'action_id'    => strtotime($val['TIME']),
            ];
            $rs[]                = $dataTmp;
        }// end foreach()
        return $rs;
    }

    /**
     * 设置上报点
     * @param array $data 数据
     * @return void
     */
    public function setLastId($data): void
    {
        $this->lastId = $data['ID'] ?? 0;
        //支付上报，日志记录真实的pay_time时间戳
        if (get_class($this) == PayUploadStrategy::class) {
            $this->lastId = strtotime($data['PAY_TIME']);
        } else {
            $this->lastId = strtotime($data['TIME']);
        }

        if (!$this->isReRun) {
            $this->cache->setLastId($this->channelId, $this->action, $this->lastId);
        }
    }

    /**
     * 设置未匹配归因上报点
     * @return void
     */
    public function setUnmatchedIds(): void
    {
        $this->cache->setUnmatchedIds($this->channelId, $this->unmatchedIds, $this->action);
    }

    /**
     * 填充点击
     * @param array $info 数据
     * @return array|mixed
     */
    public function fillUpClick($info)
    {
        //当上报方式为匹配点击上报 or 全量上报并且是taptap渠道 时，匹配点击回传
        if ($info['UPLOAD_METHOD'] == 2 ||
            ($info['UPLOAD_METHOD'] == 1 && $info["CHANNEL_ID"] == 131 && $info["CLICK_ID"])
        ) {
            $sql          = "SELECT CLICK_ID AS CLICK_ID_AD,TIME AS CLICK_TIME,OAID,CALLBACK_URL,EXT AS EXT_CLICK,CID FROM tb_ad_click_match_log WHERE ID = '{$info['CLICK_ID']}'";
            $dataClickTmp = \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
            if (!empty($dataClickTmp)) {
                $dataClickTmp['EXT_CLICK'] = json_decode($dataClickTmp['EXT_CLICK'], true);
                $info                      = array_merge($info, $dataClickTmp);
            } else {
                \Plus::$app->log->error('fillUpClick:' . $info['ID'], [], AdBaseInterface::LOG_DIR);
                $info = [];
            }
        }
        return $info;
    }

    /**
     * 上传数据
     * @param array  $data         处理数据
     * @param string $uploadMethod 上传方法
     * @return mixed
     */
    public function uploadData(array $data, string $uploadMethod)
    {
        $channel            = ChannelFactory::getChannel($this->channelId);
        $uploadMethodBefore = 'uploadBefore';
        $uploadMethodAfter  = 'uploadAfter';
        // before 前置方法
        if (method_exists($channel, $uploadMethodBefore)) {
            $rs = call_user_func_array([$channel, $uploadMethodBefore], [$data, $uploadMethod]);
            if (!is_array($rs)) {
                \Plus::$app->log->error('uploadBefore方法返回值不是数组' . $data['ID'], [], AdBaseInterface::LOG_DIR);
                return false;
            }
        } else {
            $rs = $data;
        }
        // uploadXXx 上传方法
        $up = call_user_func([$channel, $uploadMethod], $rs);
        // after 后置方法
        if (method_exists($channel, $uploadMethodAfter)) {
            return call_user_func_array([$channel, $uploadMethodAfter], [$up, $data]);
        }
        return $up;
    }
}

<?php

namespace app\models\baseConfPlatform;

use app\util\Common;
use Plus\MVC\Model\ActiveRecord;

class TbBaseCpGameConf extends ActiveRecord
{
    public $_primaryKey = 'id';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->base_conf_platform;
        parent::__construct($data);
    }

    /**
     * @param array $cpNames
     *
     * @return array
     */
    public function getCpGameIdByNames(array $cpNames): array
    {
        $sql = "SELECT cp_game_name, cp_game_id
                FROM base_conf_platform.tb_base_cp_game_conf
                WHERE IS_SHOW = 1;";
        if ($_db = Common::pingDoris()) {
            $this->_db = $_db;
        }

        $cpGameList = $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $cpGameList = array_column($cpGameList, 'cp_game_id', 'cp_game_name');

        return array_intersect_key($cpGameList, array_flip($cpNames));
    }


}
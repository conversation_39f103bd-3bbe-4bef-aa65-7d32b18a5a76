<?php

namespace app\service\Logs\Components\Matcher;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\General\Helpers\BigDataDwdTable;
use app\util\Common;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class UserLoginMatch extends BaseLogMatch
{
    /**
     * @return array
     * @throws \Exception
     */
    protected function matchFnList(): array
    {
        $new = [
            'game_server' => $this->matchGameServer(),
            'role_id'     => $this->matchRoleId(),
            'role_name'   => $this->matchRoleName()
        ];

        return array_merge(parent::matchFnList(), $new);
    }

    /**
     * @throws \Exception
     */
    protected function matchGameServer(): \Closure
    {
        $db = $this->getConn();
        return function (SelectQuery &$qb, $key, $value) use ($db) {
            $subQb = $db->select()
                ->from(BigDataDwdTable::DwdSdkRoleLogin)
                ->where('game_server', new Parameter(Arr::wrap($value)))
                ->columns(['core_account'])->distinct();

            $qb->where('core_account', 'IN', $subQb);
        };
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     * @throws \Exception
     */
    protected function getConn()
    {
        if ($dorisIndex = Common::pingDorisIndex()) {
            return FakeDB::connection($dorisIndex);
        }
        else {
            throw new \RuntimeException('与数据库连接断开');
        }
    }

    /**
     * @return \Closure
     * @throws \Exception
     */
    protected function matchRoleId(): \Closure
    {
        $db = $this->getConn();

        return function (SelectQuery &$qb, $key, $value) use ($db) {
            $subQb = $db->select()
                ->from(BigDataDwdTable::DwdSdkRoleLogin)
                ->where('role_id', new Parameter(Arr::wrap($value)))
                ->columns(['core_account'])->distinct();

            $qb->where('core_account', 'IN', $subQb);
        };
    }

    /**
     * @return \Closure
     * @throws \Exception
     */
    protected function matchRoleName(): \Closure
    {
        $db = $this->getConn();

        return function (SelectQuery &$qb, $key, $value) use ($db) {
            $subQb = $db->select()
                ->from(BigDataDwdTable::DwdSdkRoleLogin)
                ->where('role_name', 'like', "%{$value}%")
                ->columns(['core_account'])->distinct();

            $qb->where('core_account', 'IN', $subQb);
        };
    }
}
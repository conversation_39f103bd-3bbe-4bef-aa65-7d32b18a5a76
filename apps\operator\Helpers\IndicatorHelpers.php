<?php

namespace app\apps\operator\Helpers;


class IndicatorHelpers
{
    /**
     * @param       $target
     * @param array $groups
     * @param array $lines
     * @return void
     */
    protected static function processingLine(&$target, array $groups, array $lines = [])
    {
        $indexUnion = array_fill_keys($groups, 0);

        foreach ($target as &$item) {
            $unionKey = static::flattenUnionKey($item, $indexUnion);

            foreach ($lines as $callback) {
                if ($callback instanceof \Closure) {
                    $callback($item, $unionKey);
                    continue;
                }

                if (is_callable($callback)) {
                    call_user_func_array($callback, [&$item, $unionKey]);
                }
            }
        }
    }

    /**
     * @param        $data
     * @param        $unionIndex
     * @param string $separator
     * @return string
     */
    public static function flattenUnionKey($data, $unionIndex, string $separator = '|'): string
    {
        $keys = array_merge($unionIndex, array_intersect_key($data, $unionIndex));
        ksort($keys);

        return \implode($separator, $keys);
    }
}
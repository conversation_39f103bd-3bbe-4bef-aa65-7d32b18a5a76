<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Helpers\DBHelper\MatcherAbstract;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\General\BizTagsServ;
use app\service\General\Helpers\BaseConfPlatformTable;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class AdBaseMatch extends MatcherAbstract
{
    /**
     * @return array
     * @throws \RedisException
     */
    protected function matchFnList(): array
    {
        return [
            'tday'                 => $this->matchTDay(),
            'package_id'           => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'cp_game_id'           => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'game_id'              => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'app_show_id'          => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'platform_id'          => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'promotion_id'         => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'department_id'        => $this->matchDepartment(),
            'user_id'              => $this->matchUserId(),
            'channel_id'           => $this->matchChannel(),
            'channel_main_id'      => $this->matchChannelMain(),
            'ad_account_id'        => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'ad_account'           => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::likeBuild($qb, $key, $value);
            },
            'account_id'           => function (SelectQuery &$qb, $key, $value) {
                if (is_null($value)) {
                    $qb->where($key, '0');
                }
                else {
                    QueryBuilderHelper::baseBuild($qb, $key, $value);
                }
            },
            'package_id_tags'      => $this->matchPackageTags(),
            'channel_id_tags'      => $this->matchChannelTags(),
            'channel_main_id_tags' => $this->matchChannelMainTags(),
            'game_id_tags'         => $this->matchGameTags(),
        ];
    }

    /**
     * @return \Closure
     */
    protected function matchTDay(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value) {
            if (is_array($value) && count($value) > 1) {
                [$start, $end] = $value;
                $qb->where($key, 'between', $start, $end);
            }
            else {
                if (is_array($value)) {
                    $value = $value[0];
                }

                $qb->where($key, $value);
            }
        };
    }

    /**
     * @return \Closure
     * @throws \RedisException
     */
    protected function matchDepartment(): \Closure
    {
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        return function (SelectQuery &$qb, $key, $value) use ($planChannelsString) {
            $powerChannelCol = $this->getReflectKey('power.channel_id');
            $adDepartment    = $key;
            $powerDepartment = $this->getReflectKey('power.department_id');

            $value       = implode(',', $value);
            $whereString = sprintf(
                "COALESCE(IF(%s IN (%s), %s, %s), 0) IN (%s)",
                $powerChannelCol, $planChannelsString, $adDepartment, $powerDepartment, $value
            );

            $qb->where(new Fragment($whereString));
        };
    }

    /**
     * @return \Closure
     * @throws \RedisException
     */
    protected function matchUserId(): \Closure
    {
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        return function (SelectQuery &$qb, $key, $value) use ($planChannelsString) {
            $powerChannelCol = $this->getReflectKey('power.channel_id');
            $powerUser       = $this->getReflectKey('power.ad_user_id');

            $value       = implode(',', $value);
            $whereString = sprintf(
                "COALESCE(IF(%s IN (%s), %s, %s), 0) IN (%s)",
                $powerChannelCol, $planChannelsString, $key, $powerUser, $value
            );

            $qb->where(new Fragment($whereString));
        };

    }

    /**
     * @return \Closure
     * @throws \RedisException
     */
    protected function matchChannel(): \Closure
    {
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        return function (SelectQuery &$qb, $key, $value) use ($planChannelsString) {
            $powerChannelCol = $this->getReflectKey('power.channel_id');
            $value           = implode(',', $value);
            $whereString     = sprintf(
                "IF(%s NOT IN (%s), %s, IF(%s != 0, IF(%s = 1013, 4, %s), %s)) IN (%s)",
                $powerChannelCol, $planChannelsString, $powerChannelCol, $key, $key, $key, $powerChannelCol, $value
            );

            $qb->where(new Fragment($whereString));
        };
    }


    /**
     * @return \Closure
     * @throws \RedisException
     */
    protected function matchChannelMain(): \Closure
    {
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        return function (SelectQuery &$qb, $key, $value) use ($planChannelsString) {
            $powerChannelCol  = $this->getReflectKey('power.channel_id');
            $powerMainChannel = $this->getReflectKey('power.channel_main_id');
            $value            = implode(',', $value);

            $whereString = sprintf(
                "IF(%s NOT IN (%s), %s, IF(%s != 0, IF(%s = 1013, 4, %s), %s)) IN (%s)",
                $powerChannelCol, $planChannelsString, $powerMainChannel, $key, $key, $key, $powerChannelCol, $value
            );

            $qb->where(new Fragment($whereString));
        };
    }

    /**
     * @return \Closure
     */
    protected function matchPackageTags(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value) {
            $subQb = $this
                ->getConn()
                ->select()
                ->from(BaseConfPlatformTable::BizTags)
                ->where('table_name', 'packages')
                ->where('tag_id', 'IN', new Parameter($value))
                ->columns(['data_id'])->distinct();

            $qb->where($key, 'IN', $subQb);
        };
    }

    /**
     * @return \Closure
     * @throws \RedisException
     */
    protected function matchChannelTags(): \Closure
    {
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        return function (SelectQuery &$qb, $key, $value) use ($planChannelsString) {
            $subQb = $this
                ->getConn()
                ->select()
                ->from(BaseConfPlatformTable::BizTags)
                ->where('table_name', 'app_channel')
                ->where('tag_id', 'IN', new Parameter($value))
                ->columns(['data_id'])->distinct();


            $powerChannelCol = $this->getReflectKey('power.channel_id');
            $whereString     = sprintf(
                "IF(%s NOT IN (%s), %s, IF(%s != 0, IF(%s = 1013, 4, %s), %s)) IN (%s)",
                $powerChannelCol, $planChannelsString, $powerChannelCol, $key, $key, $key, $powerChannelCol, $subQb->__toString()
            );

            $qb->where(new Fragment($whereString));
        };

    }

    /**
     * @return \Closure
     * @throws \RedisException
     */
    protected function matchChannelMainTags(): \Closure
    {
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        return function (SelectQuery &$qb, $key, $value) use ($planChannelsString) {
            $subQb = $this
                ->getConn()
                ->select()
                ->from(BaseConfPlatformTable::BizTags)
                ->where('table_name', 'app_channel')
                ->where('tag_id', 'IN', new Parameter($value))
                ->columns(['data_id'])->distinct();

            $powerMainChannel = $this->getReflectKey('power.channel_main_id');
            $powerChannelCol  = $this->getReflectKey('power.channel_id');
            $value            = implode(',', $value);

            $whereString = sprintf(
                "IF(%s NOT IN (%s), %s, IF(%s != 0, IF(%s = 1013, 4, %s), %s)) IN (%s)",
                $powerChannelCol, $planChannelsString, $powerMainChannel, $key, $key, $key, $powerChannelCol, $subQb->__toString()
            );

            $qb->where(new Fragment($whereString));
        };
    }

    /**
     * @return \Closure
     */
    protected function matchGameTags(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value) {
            $subQb = $this
                ->getConn()
                ->select()
                ->from(BaseConfPlatformTable::BizTags)
                ->where('table_name', 'games')
                ->where('tag_id', 'IN', new Parameter($value))
                ->columns(['data_id'])->distinct();

            $gameCol = $this->getReflectKey('game_id');
            $qb->where($gameCol, 'IN', $subQb);
        };
    }


    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
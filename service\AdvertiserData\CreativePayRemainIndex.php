<?php

namespace app\service\AdvertiserData;

use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Components\MatchParams\AdCreativeMatcher;
use app\service\AdvertiserData\Components\MatchParams\AdPlanMatcher;
use app\service\AdvertiserData\Scheme\AdCreativePayRemainScheme;
use app\service\AdvertiserData\Traits\AdServiceable;
use app\service\AdvertiserData\Traits\Converter;
use app\service\General\BizTagsServ;
use app\util\Common;
use Aura\SqlQuery\Common\SelectInterface;

class CreativePayRemainIndex
{
    use Converter, AdServiceable;

    /**
     * @param       $params
     * @param array $groups
     *
     * @return array|false
     */
    public function fetchRealTimeByCreative($params, array $groups = [])
    {
        return $this->fetchAllByCreative($params, $groups, 30);
    }

    /**
     * @param       $params
     * @param array $groups
     *
     * @return null
     */
    public function fetchRealtimeByPlan($params, array $groups = [])
    {
        return $this->fetchAllByPlan($params, $groups, 30);
    }

    /**
     * @param       $params
     * @param array $groups
     * @param       $maxDayType
     *
     * @return array|false
     */
    public function fetchAllByCreative($params, array $groups = [], $maxDayType = null)
    {
        if (empty($groups)) {
            $groups = ['tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id', 'creative_id', 'day_type'];
        }

        if (!in_array('day_type', $groups)) {
            $groups[] = 'day_type';
        }

        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = AdCreativePayRemainScheme::NewOne()->select();

        $scheme
            ->joinPowerSql($powerSql)
            ->joinCreativeBase()
            ->join($this->joinAccount('t_base'))
            ->join((new JoinClause('left', 'dataspy.admin_user', 't_admin'))
                ->on('t_base.USER_ID', '=', 't_admin.ID'))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->joinAdpOauth('left', 't_base')
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params)));

        $matcher = new AdCreativeMatcher($scheme->fieldReflect());
        $matcher->setParams($params);
        $matcher->execute($scheme);

        if (!is_null($maxDayType)) {
            $scheme->scope($this->matchRemainParams(['max_day_type' => $maxDayType]));
        }
        Common::dumpSql((clone $scheme)->toSql());
        return $this->fetchAll($scheme->toSql());
    }

    /**
     * @param            $params
     * @param array|null $groups
     * @param int|null $maxDayType
     *
     * @return array|false
     */
    public function fetchAllByPlan($params, ?array $groups, ?int $maxDayType)
    {
        if (empty($groups)) {
            $groups = ['tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id', 'creative_id', 'day_type'];
        }

        if (!in_array('day_type', $groups)) {
            // 追加day_type维度
            $groups[] = 'day_type';
        }

        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = AdCreativePayRemainScheme::NewOne()->select();

        $scheme
            ->joinPowerSql($powerSql)
            ->joinPlanBase()
            ->join($this->joinAccount('t_base'))
            ->joinAdpOauth('left', 't_base')
            ->join((new JoinClause('left', 'dataspy.admin_user', 't_admin'))
                ->on('t_base.USER_ID', '=', 't_admin.ID'))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->join((new JoinClause('left', 'adp_platform.tb_adp_campaign', 'base_campaign'))
                ->on('base_campaign.channel_id', '=', 't_base.main_channel_id')
                ->on('base_campaign.campaign_id', '=', 't_base.campaign_id'))
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params)));

        $matcher = new AdPlanMatcher($scheme->fieldReflect());
        $matcher->setParams($params);
        $matcher->execute($scheme);

        if (!is_null($maxDayType)) {
            $scheme->scope($this->matchRemainParams(['max_day_type' => $maxDayType]));
        }

        Common::dumpSql((clone $scheme)->toSql());
        return $this->fetchAll($scheme->toSql());
    }


    /**
     * @param array $columns
     *
     * @return \Closure
     */
    public function buildColumn(array $columns = ['*']): \Closure
    {
        return function (&$query) use ($columns) {
            if (!$query instanceof SelectInterface) return;
            $query->cols($columns);
        };
    }

    /**
     * @param $params
     *
     * @return \Closure
     */
    protected function matchRemainParams($params): \Closure
    {
        return function (&$query) use ($params) {
            $mainAlias = AdCreativePayRemainScheme::MAIN_TABLE['alias'];

            if (!$query instanceof SelectInterface) return;

            if (!empty($maxDayTYpe = $params['max_day_type'])) {
                $query->where("{$mainAlias}.day_type <= {$maxDayTYpe}");
            }
        };
    }

    protected function buildGroup($groups): \Closure
    {
        $groups = $this->changeGroups($groups);

        return function (&$query) use ($groups) {
            if (!$query instanceof SelectInterface) return;

            $query->groupBy($groups);
        };
    }

    private function groupsReflect(): array
    {
        $mainAlias = AdCreativePayRemainScheme::MAIN_TABLE['alias'];

        return [
            'tday'          => $mainAlias . '.tday',
            'cp_game_id'    => $mainAlias . '.cp_game_id',
            'game_id'       => $mainAlias . '.game_id',
            'package_id'    => $mainAlias . '.package_id',
            'channel_id'    => $mainAlias . '.channel_id',
            //            'channel_main_id' => 'POWER.channel_main_id',
            'creative_id'   => $mainAlias . '.creative_id',
            'campaign_id'   => $mainAlias . '.campaign_id',
            'plan_id'       => $mainAlias . '.plan_id',
            'day_type'      => $mainAlias . '.day_type',
            'ad_account_id' => 'base_account.id',
        ];
    }

    protected function getColumns(array $params = [], bool $isTop = false, bool $isTotal = false): array
    {
        $mainAlias = AdCreativePayRemainScheme::MAIN_TABLE['alias'];

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString      = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0,IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) AS promotion_channel_id",
            $planChannelsString
        );
        $channelMainString  = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );

        $fixInfoIndex = [
            'tday'                 => ['source' => $mainAlias],
            'cp_game_id'           => ['source' => $mainAlias],
            'game_id'              => ['source' => $mainAlias],
            'app_show_id'          => ['source' => 'POWER'],
            //            'channel_main_id'      => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (6568, 6822, 5329), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id'],
            'channel_main_id'      => ['info', 'raw' => $channelMainString],
            //            'promotion_channel_id' => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id NOT IN (6568, 6822, 5329), POWER.channel_id,IF(t_base.channel_id != 0, t_base.channel_id,POWER.channel_id)), 0) AS promotion_channel_id'],
            'promotion_channel_id' => ['info', 'raw' => $channelString],
            'platform_id'          => ['source' => 'POWER'],
            'package_id'           => ['source' => $mainAlias],
            'campaign_id'          => ['source' => $mainAlias],
            'plan_id'              => ['source' => $mainAlias],
            'creative_id'          => ['source' => $mainAlias],
            'promotion_id'         => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            'department_id'        => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),t_admin.department_id, POWER.ad_department_id),0) as department_id'],
            'user_id'              => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),t_base.USER_ID, POWER.AD_USER_ID), 0) AS user_id'],
            'ad_account'           => ['source' => 'base_account'],
            'account_id'           => ['source' => 'base_account'],
            'account_name'         => ['source' => 'adp_oauth', 'source_field' => 'ADVERTISER_NAME'],
            'day_type'             => ['source' => $mainAlias],
            'dim_user_os'          => ['info', 'raw' => "case when USER_OS = JSON_ARRAY('ANDROID') then 'ANDROID' when USER_OS = JSON_ARRAY('IOS') then 'IOS' when JSON_CONTAINS(USER_OS, '[\"ANDROID\",\"IOS\"]', '$') = 1 then '混投' else '混投' end as dim_user_os"]
        ];

        $calculatedIndex = [
            'login_num' => ['source' => $mainAlias, 'aggregate' => 'sum'], // 登录人数
        ];

        $result  = [];
        $collect = collect();

        if (!$isTotal) {
            $collect = $collect->merge($fixInfoIndex);
        }
        $collect = $collect->merge($calculatedIndex);

        $collect->each(function (&$item, $key) use (&$result, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];
                $format    = "{$aggregate}(%s)";
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }

            $field .= $item['source_field'] ?? $key;

            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }


    /**
     * @param string $sql
     *
     * @return array|false
     */
    private function fetchAll(string $sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }
}
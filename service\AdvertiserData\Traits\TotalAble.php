<?php

namespace app\service\AdvertiserData\Traits;

use app\extension\Support\Collections\Arr;

/**
 * @deprecated
 */
trait TotalAble
{
    /**
     * 包裹一层求当前sql的总条数的SQL
     *
     * @return string
     */
    public function countRowOfQuery(): string
    {
        if (is_null($this->query)) {
            return '';
        }

        $stm = $this->wrapQuery($this->query->__toString(), 'total_body');
        $stm->cols(['count(1) as total']);

        return $stm->__toString();
    }

    /**
     * @param array $columns
     * @return string
     */
    public function getTotalRowSql(array $columns = []): string
    {
        $stm = $this->wrapQuery($this->query->__toString(), 'total_body');
        $stm->cols($columns);

        return $stm->__toString();
    }

    /**
     * 汇总行每日详细信息
     * @param array $columns
     * @return string
     */
    public function getSummaryByDateSql(array $columns = []): string
    {
        Arr::prepend($columns, 'tday');

        $stm = $this->wrapQuery($this->query->__toString(), 'total_body');
        $stm->cols($columns);
        $stm->groupBy(['tday']);

        return $stm->__toString();
    }

    /**
     * @param $sql
     * @param $alias
     * @return \Aura\SqlQuery\Common\DeleteInterface|\Aura\SqlQuery\Common\InsertInterface|\Aura\SqlQuery\Common\SelectInterface|\Aura\SqlQuery\Common\UpdateInterface
     */
    protected function wrapQuery($sql, $alias)
    {
        $builder = clone $this->queryFactory;
        return $builder->newSelect()->fromSubSelect($sql, $alias);
    }


}
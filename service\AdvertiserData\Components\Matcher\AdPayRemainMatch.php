<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\SelectQuery;

class AdPayRemainMatch extends AdCreativeMatch
{
    public function matchFnList(): array
    {
        $new = [
            'channel_id'      => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'channel_main_id' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'department_id'   => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'user_id'         => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
        ];


        return array_merge(parent::matchFnList(), $new);
    }
}
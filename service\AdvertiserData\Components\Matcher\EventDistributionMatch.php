<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\extension\Support\Helpers\TimeUtil;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\AdvertiserData\Components\Matcher\Traits\AdChannelAble;
use app\service\General\Matcher\Traits\TagsMatcher;
use Cycle\ORM\Select\QueryBuilder;
use Spiral\Database\Query\SelectQuery;

/**
 *
 */
class EventDistributionMatch extends BaseMatch
{
    use AdChannelAble, TagsMatcher;

    /**
     * @return array
     */
    public function processLine(): array
    {
        $newLine = [
            [$this, 'matchTDay'],
            [$this, 'matchChannelId'],
            [$this, 'matchChannelMainId'],
            [$this, 'matchEventType'],
            [$this, 'matchGroupType'],
            [$this, 'matchReportType'],
            [$this, 'matchPackageTagsQb'],
            [$this, 'matchGameTagsQb'],
            [$this, 'matchChannelTagsQb'],
            [$this, 'matchChannelMainTagsQb'],
        ];

        return array_merge(parent::processLine(), $newLine);
    }

    /**
     * 事件指标类型匹配
     *
     * @param SelectQuery|QueryBuilder $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchEventType(&$qb, array &$params)
    {
        if (empty($params['event_type'])) return;

        $eventType = $params['event_type'];
        $field     = $this->getReflectField('event_type');

        QueryBuilderHelper::baseBuild($qb, $field, $eventType);
    }

    /**
     *
     *
     * @param SelectQuery|QueryBuilder $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchReportType(&$qb, array &$params)
    {
        if (empty($params['report_type'])) return;

        $data  = $params['report_type'];
        $field = $this->getReflectField('report_type');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * 时间区间类型搜索
     *
     * @param SelectQuery|QueryBuilder $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchGroupType(&$qb, array &$params)
    {
        if (empty($params['group_type'])) return;

        $data  = $params['group_type'];
        $field = $this->getReflectField('group_type');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param SelectQuery|QueryBuilder $qb
     * @param array $params
     *
     * @return void
     * @throws \Exception
     */
    protected function matchTDay(&$qb, array &$params)
    {
        if (
            empty($params['range_date_start']) || empty($params['range_date_end'])
        ) {
            throw  new \InvalidArgumentException('missing arguments: range_date_start or range_date_end');
        }

        $dimension = (int)($params['range_date_dimension'] ?? 2);

        $rangeDate = array_filter([
            $params['range_date_start'],
            $params['range_date_end'],
        ]);

        sort($rangeDate);
        $field = $this->getReflectField('tday');

        if (3 === $dimension) {
            // (按周) 按选择时间的起始时间作为开头往后衍生7天为一个周期进行划分
            // 获取的时间范围为每个周期的第一天的数据 && report_type = 2
            // 按周维度只能选择自然月
            ['cycle' => $rangeCycle] = TimeUtil::divideWeekByRangeDate(...$rangeDate);
            $params['report_type'] = 2;
            $params['group_type']  = 2;

            $inTDay = array_unique(array_column($rangeCycle, 'begin_date'));
            QueryBuilderHelper::baseBuild($qb, $field, $inTDay);
        }
        elseif (4 === $dimension) {
            // (按月) 按给定的月份，按每月第一天作为标识获取数据, report_type = 3
            $params['report_type'] = 3;
            $params['group_type']  = 2;

            $rangeMonth = TimeUtil::getMonthRange(...$rangeDate);
            $inTDay     = array_unique($rangeMonth);

            QueryBuilderHelper::baseBuild($qb, $field, $inTDay);
        }
        else {
            if (!isset($params['report_type'])) {
                $params['report_type'] = 1;
            }
            $rangeDate = array_unique($rangeDate);

            if (count($rangeDate) === 1) {
                $qb->where($field, $rangeDate[0]);
            }
            else {
                $qb->where($field, 'between', ...$rangeDate);
            }
        }

    }

}
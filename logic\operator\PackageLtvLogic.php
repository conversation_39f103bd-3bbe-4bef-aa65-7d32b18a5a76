<?php

namespace app\logic\operator;

use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Helpers\LtvCalculators;
use app\extension\Support\Helpers\ProcessLine;
use app\extension\Support\Helpers\Zakia;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use app\service\OperationData\FirstLoginServ;
use app\service\OperationData\PackageLtvServ;

class PackageLtvLogic
{
    use ColumnsInteract;

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \Exception
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        [
            'time_dimension' => $timeDimension,
            'params'         => $params
        ] = $this->buildParams($params);

        $groups  = $this->changeGroups($groups);
        $ltvType = (int)($params['ltv_type'] ?? 0);

        $readCols = [
            'tday', 'cp_game_id', 'game_id',
            'app_show_id', 'platform_id', 'channel_id',
            'channel_main_id', 'package_id',
            'promotion_id', 'department_id', 'user_id', 'firstlogin_user', 'cost_discount', 'link_mark'
        ];

        $baseServ = new FirstLoginServ();
        $mode     = $baseServ::MODE_SUMMARY | $baseServ::MODE_TOTAL | $baseServ::MODE_DETAIL_QB | $baseServ::MODE_DETAIL;
        $baseRe   = $baseServ->getListWithOnlyBaseForBigData($params, $groups, $paginate, $sort, $readCols, $mode);
        $baseQb   = $baseRe['detail_qb'];

        // ltv查询
        $ltvServ = new PackageLtvServ('first_log.ltv');
        $ltvRe   = $ltvServ->getListWithInfoQb($baseQb, $params, $groups);
        unset($baseRe['detail_qb']);

        // 数据处理流程
        $infoList = &$baseRe['list'];
        $ltvList  = &$ltvRe['list'];
        $infoList = Zakia::appendKeysWithDimension($infoList, $groups);
        $ltvList  = Zakia::appendKeysWithDimension($ltvList, $groups);

        $configBasic      = new BasicServ();
        $constConfCollect = $configBasic
            ->getMultiOptions([
                                  'platform_id', 'promotion_id', 'department_id', 'user_id',
                                  'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
                              ])
            ->put('channel_id', (new GeneralOptionServ())->listChannelOptions());

        $resetFn      = $this->resetGroupsCols(
            ColumnManager::groupOperatorRelation($groups), $groups, ConstHub::OPERATOR_FIXED_INFO_COLS
        );
        $summaryGetFn = fn($target, $i) => ['ltv' => $target['firstlogin_user_' . $i] ?? 0, 'roi' => $target['cost_discount_' . $i] ?? 0];

        if (in_array('tday', $groups)) {
            $getDenominatorFn = fn($target) => ['ltv' => $target['firstlogin_user'] ?? 0, 'roi' => $target['cost_discount'] ?? 0];
        }
        else {
            $getDenominatorFn = fn($target, $i) => ['ltv' => $target['firstlogin_user_' . $i] ?? 0, 'roi' => $target['cost_discount_' . $i] ?? 0];
        }

        $works      = new ProcessLine();
        $minDay     = min($params['tday']);
        $maxDayNode = max(days_apart(date('Y-m-d'), $minDay) + 1, 0);
        $ltvCalc    = LtvCalculators::calcNode($ltvList, $getDenominatorFn, $ltvType, 'firstlogin_user');

        $works->addProcess($ltvCalc);

        if ($ltvType === 2) {
            $works->addProcess(LtvCalculators::ltvIncrementalEachRow());
        }
        elseif ($ltvType === 3) {
            $works->addProcess(LtvCalculators::ltvMultipleEachRow());
        }

        $works
            ->addProcess($this->appendChannelTag($infoList, $groups))
            ->addProcess($this->appendPackageTags($infoList, $groups))
            ->addProcess($this->replaceColumnDefine($constConfCollect))
            ->addProcess($resetFn)
            ->addProcess(function (&$target, &$context, $key) {
                $ltvMap                       = $context['ltv_key_map'] ?? [];
                $target['new_user_total_pay'] = $ltvMap['ltv_1000'] ?? 0.00;
                if (empty(floatval($target['cost_discount'])) || empty($target['firstlogin_user'])) {
                    $target['new_user_cost'] = 0.00;
                }
                else {
                    $target['new_user_cost'] = round($target['cost_discount'] / $target['firstlogin_user'], 2);
                }

                $target['total_ltv'] = $target['ltv_1000'] ?? '';
                $target['total_roi'] = $target['roi_1000'] ?? '';
            });

        if (
            in_array('tday', $groups)
            && $timeDimension == ConstHub::DIMENSION_DAY
            && !empty($params['align_date'])
        ) {
            $works->addProcess($this->clearLtvNode($params['align_date']));
        }

        $works->run($infoList);

        $summaryRow = ['summary' => &$baseRe['summary']];
        $summaryLtv = ['summary' => $ltvRe['summary'][0] ?? []];

        $summaryWorks = new ProcessLine();
        $summaryWorks
            ->addProcess(LtvCalculators::calcNode($summaryLtv, $summaryGetFn, $ltvType, 'firstlogin_user'))
            ->addProcess(function (&$target, &$context, $key) {
                $ltvMap                       = $context['ltv_key_map'] ?? [];
                $target['new_user_total_pay'] = $ltvMap['ltv_1000'] ?? 0.00;
                if (empty(floatval($target['cost_discount'])) || empty($target['firstlogin_user'])) {
                    $target['new_user_cost'] = 0.00;
                }
                else {
                    $target['new_user_cost'] = round($target['cost_discount'] / $target['firstlogin_user'], 2);
                }

                $target['total_ltv'] = $target['ltv_1000'] ?? '';
                $target['total_roi'] = $target['roi_1000'] ?? '';
            });
        $summaryWorks->run($summaryRow);

        return [
            'list'    => array_values($baseRe['list']),
            'summary' => $baseRe['summary'],
            'total'   => $baseRe['total'],
            'time'    => $baseRe['summary']['last_update_time'] ?? ''
        ];
    }

    /**
     * @param $params
     * @return array
     * @throws \Exception
     */
    protected function buildParams($params): array
    {
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            if ($timeDimension == ConstHub::DIMENSION_MONTH) {
                $params['range_date_start'] = date('Y-m-01', strtotime($params['range_date_start']));
                $params['range_date_end']   = date('Y-m-t', strtotime($params['range_date_end']));
            }

            $params['tday'] = [
                $params['range_date_start'], $params['range_date_end']
            ];

            sort($params['tday']);
            unset($params['range_date_start'], $params['range_date_end']);
        }

        if (!empty($params['pay_days_num'])) {
            $rangeStart             = $params['tday'][0];
            $targetDate             = date_add(new \DateTime($rangeStart), new \DateInterval('P' . ($params['pay_days_num'] - 1) . 'D'))->format('Y-m-d');
            $params['pay_date[<=]'] = $targetDate;
            // $params['align_date']   = date_add(new \DateTime($targetDate), new \DateInterval('P1D'))->format('Y-m-d');
            $params['align_date'] = $targetDate;

            unset($params['pay_days_num']);
        }

        return [
            'time_dimension' => $timeDimension,
            'params'         => $params,
        ];
    }

    /**
     * @param array $groups
     * @return array
     */
    protected function changeGroups(array $groups = []): array
    {
        if (empty($groups)) {
            $groups = ['tday', 'package_id'];
        }

        return ColumnManager::groupOperatorRelation($groups);
    }

    /**
     * 追加推广子渠道标签
     * @param array $list
     * @param array $groups
     * @return \Closure
     */
    protected function appendChannelTag(array $list, array $groups = []): \Closure
    {
        $optionsServ        = new GeneralOptionServ();
        $channelTagAppendFn = fn() => true;

        if (
            in_array('package_id', $groups)
            || in_array('channel_id', $groups)
        ) {
            $listChannel   = array_column($list, 'channel_id');
            $channelTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'channel_id', 'id' => $listChannel]) ?? []);

            if (!empty($channelTagMap)) {
                $channelTagAppendFn = function (&$target) use ($channelTagMap) {
                    if (
                        !empty($target['channel_id'])
                        && $target['channel_id'] != '-'
                    ) {
                        $channelId              = $target['channel_id'];
                        $target['channel_tags'] = array_values($channelTagMap[$channelId] ?? []);
                    }
                };
            }
        }

        return $channelTagAppendFn;
    }

    /**
     * 追加包号标签
     * @param array $list
     * @param array $groups
     * @return \Closure
     */
    protected function appendPackageTags(array $list, array $groups = []): \Closure
    {
        $fnA = fn() => true;

        if (in_array('package_id', $groups)) {
            $optionsServ   = new GeneralOptionServ();
            $listPackages  = array_column($list, 'package_id');
            $packageTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'package_id', 'id' => $listPackages]) ?? []);

            if (!empty($packageTagMap)) {
                $fnA = function (&$target) use ($packageTagMap) {
                    if (
                        !empty($target['package_id'])
                        && $target['package_id'] != '-'
                    ) {
                        $packageId              = $target['package_id'];
                        $target['package_tags'] = array_values($packageTagMap[$packageId] ?? []);
                    }
                };
            }
        }

        return $fnA;
    }

    /**
     * @param $targetDate
     * @return \Closure
     */
    protected function clearLtvNode($targetDate): \Closure
    {
        return function (&$target) use ($targetDate) {
            $keys = implode(',', array_keys($target));
            preg_match_all('/(?<ltv>[ltv_\d+]+)/i', $keys, $ltvMaps);
            preg_match_all('/(?<ltv>[roi_\d+]+)/i', $keys, $roiMaps);

            $diff = days_apart($target['tday'], $targetDate) + 1;

            if (!empty($ltvMaps['ltv'])) {
                $ltvMaps = $ltvMaps['ltv'];

                foreach ($ltvMaps as $foo) {
                    $ii = intval(str_replace('ltv_', '', $foo));

                    if ($ii > $diff) {
                        $target[$foo] = '-';
                    }
                }
            }

            if (!empty($roiMaps['ltv'])) {
                $roiMaps = $roiMaps['ltv'];

                foreach ($roiMaps as $foo) {
                    $ii = intval(str_replace('roi_', '', $foo));

                    if ($ii > $diff) {
                        $target[$foo] = '-';
                    }
                }
            }


        };
    }


}
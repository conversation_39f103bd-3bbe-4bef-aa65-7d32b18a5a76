<?php

namespace app\logic\advertise;

use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\AdIncomeProvider;
use Smarty\Exception;

class AdIncomeDashLogic
{
    /**
     * @throws Exception
     */
    public function getList(array $options): array
    {
        if (!empty($options['page']) || !empty($options['page_size'])) {
            $paginate = [
                'page'      => Arr::pull($options, 'page', 1),
                'page_size' => Arr::pull($options, 'page_size', 100)
            ];
        }
        else {
            $paginate = [];
        }

        $sort   = Arr::pull($options, 'sort', []);
        $groups = Arr::pull($options, 'groups', []);

        if (is_array($groups)) {
            $groups = implode(',', $groups);
        }

        if (!empty($sort)) {
            $order = Arr::pull($options, 'order');
            if ($order == 'ascend') {
                $order = 'ASC';
            }
            else {
                $order = 'DESC';
            }
            $orderBy = [$sort => $order];
        }
        else {
            $orderBy = ['tday' => 'ASC'];
        }

        if (!empty($options['range_date_start']) && !empty($options['range_date_end'])) {
            $options['range_time'] = [
                $options['range_date_start'], $options['range_date_end']
            ];
            sort($options['range_time']);
            unset($options['range_date_start'], $options['range_date_end']);
        }

        foreach ($options as $kk => &$item) {
            if (is_array($item) && in_array($kk, ['cp_game_id', 'game_id'])) {
                $item = implode(',', $item);
            }
        }

        $result = (new AdIncomeProvider())->getList($options, $groups, $paginate, $orderBy);
        $list   = &$result['list'];

//        foreach ($list as &$item) {
//            foreach (['exposure_percent', 'click_percent'] as $kk) {
//                if (isset($item[$kk])) {
//                    $item[$kk] = $item[$kk] . '%';
//                }
//                else {
//                    $item[$kk] = '0.00%';
//                }
//            }
//        }
//        unset($item);

        return $result;
    }
}
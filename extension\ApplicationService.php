<?php


namespace app\extension;

use app\service\Admin;
use app\service\Auth;
use app\service\DataEncryptor;
use app\service\FileIO;
use app\service\Options;
use app\service\Queue;
use app\service\User;

/**
 * Class ApplicationService
 *
 * @property User          $user
 * @property Options       $options
 * @property FileIO        $fileIO
 * @property Queue         $queue
 * @property Admin         $admin
 * @property Auth          $auth
 * @property DataEncryptor $dataEncryptor
 * @package app\extension
 * <AUTHOR>
 */
class ApplicationService extends \Plus\Service\Application
{

}

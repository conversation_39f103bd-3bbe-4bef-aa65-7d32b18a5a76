<?php

namespace app\service\AdvertiserData\Traits;

trait Schemer
{
    public function toSql(): string
    {
        $this->prependToSql();

        return $this->query->__toString();
    }

    /**
     * @return void
     */
    public function prependToSql()
    {
        $joinTables = $this->joinTables;

        if (method_exists($this, 'fixedTables')) {
            $joinTables = array_merge($joinTables, $this->fixedTables());
        }

        $this->completeJoins($joinTables);
    }
}
<?php
declare(ticks=1);

namespace app\crontab;

use Plus\CLI\DaemonProcess;
use app\service\Revenue\IncomeServ;
use app\service\SourceData\PaymentCountServ;
use app\service\ConfigService\BasicServ;
use Plus\Internal\Config;
use Plus\Util\Email;

/**
 * 邮件通知 代金券报表（每个月3号早上10点自动发送）
 *
 * @package app\crontab
 */
class MailCouponDashBoard extends DaemonProcess
{

    public $month = '';

    /*
    收邮件名单
    章咏<>
    孙亮<>
    陈佳<>
    周甜甜<>
    蔡琼<>
    何静雯<>
    谭托<>
    裴阳<>
    岑颖诗<>
    */
    public $mailUserList = [
        [
            'mail' => '<EMAIL>', // 技术同事
        ],
        [
            'mail' => '<EMAIL>', // 产品同事
        ],
        [
            'mail' => '<EMAIL>',
        ],
        [
            'mail' => '<EMAIL>',
        ],
        [
            'mail' => '<EMAIL>',
        ],
        [
            'mail' => '<EMAIL>',
        ],
        [
            'mail' => '<EMAIL>',
        ],
        [
            'mail' => '<EMAIL>',
        ],
        [
            'mail' => '<EMAIL>',
        ],
        [
            'mail' => '<EMAIL>',
        ],
        [
            'mail' => '<EMAIL>',
        ]
    ];

    // 分项目展示游戏，除这些游戏都归为其他游戏
    public $showGameIdList = [692, 627, 763, 543, 735, 789];

    // 分部门只统计指定部门ID 387 发行直播营销部  389 国内运营中心（可能包括0）
    public $depIdList = [389, 387, 0];

    // 数据列表
    public $dataList = [
        // 大盘数据
        'dapan'         => [
            // 平台数据总览
            'total_order_amount'    => 0, // 总订单金额
            'total_pay_rmb'         => 0, // 总流水（RMB支付）
            // 虚拟支付
            'virtual_deduct_amount' => 0, // 虚拟支付抵扣金额
            'virtual_deduct_ratio'  => 0, // 虚拟支付抵扣占比（虚拟支付抵扣金额/总订单金额）
            'virtual_deduct_cost'   => 0, // 虚拟支付支出成本（虚拟支付抵扣金额 * 20%）
            // 九玩支付
            'coin_deduct_amount'    => 0, // 九玩币抵扣金额
            'coin_deduct_ratio'     => 0, // 九玩币抵扣占比（九玩币抵扣金额/总订单金额）
            'coin_deduct_cost'      => 0, // 九玩币支出成本（九玩币抵扣金额 * 20%）
            // 代金券
            'coupon_deduct_amount'  => 0,// 代金券抵扣金额
            'coupon_deduct_ratio'   => 0,// 代金券抵扣占比（代金券抵扣金额/总订单金额）
            'coupon_deduct_cost'    => 0,// 代金券支出成本（代金券抵扣金额 * 20%）
        ],
        // 分部门数据
        'dep_list'      => [],
        // 投放类型数据
        'classify_list' => [],
        // 分项目数据
        'game_list'     => [],
        // 分项目运营活动数据
        'activity_list' => [],
    ];

    // 投放类型初始化数据
    public $initClassifyList = [
        'coupon_order_amount'  => 0, // 代金券支付订单金额
        'coupon_deduct_amount' => 0, // 代金券抵扣金额
        'coupon_deduct_ratio'  => 0, // 抵扣比例（类型抵扣金额/类型代金券支付订单金额）
        'coupon_deduct_cost'   => 0, // 成本（类型抵扣金额 * 20%
        'classify_used_ratio'  => 0, // 投放类型占比(类型抵扣金额/代金券大盘抵扣金额)
    ];

    // 分部门数据初始化数据
    public $initDepList = [
        'coupon_order_amount'  => 0, // 代金券支付订单金额
        'coupon_deduct_amount' => 0, // 抵扣金额
        'coupon_deduct_ratio'  => 0, // 抵扣比例(部门抵扣金额/部门代金券支付订单金额）
        'coupon_deduct_cost'   => 0, // 成本（部门抵扣金额 * 20%）
        'dep_used_ratio'       => 0, // 部门使用占比（部门抵扣金额/代金券大盘抵扣金额）
    ];

    // 分项目数据初始化数据
    public $initGameList = [
        'total_pay_rmb'             => 0, // 总流水（RMB支付）
        'total_order_amount'        => 0, // 总订单金额
        'coupon_order_amount'       => 0, // 代金券支付订单金额
        'coupon_deduct_amount'      => 0, // 代金券抵扣金额
        'coupon_deduct_ratio'       => 0, // 抵扣比例（项目抵扣金额/项目代金券支付订单金额）
        'coupon_total_ratio'        => 0, // 代金券抵扣占流水占比（项目抵扣金额/项目总订单金额）
        'coin_deduct_amount'        => 0, // 九玩币抵扣金额
        'game_cost_usable'          => 0, // 项目本月可用成本
        'coupon_deduct_cost'        => 0, // 成本（项目抵扣金额 * 20%
        'total_order_ratio'         => 0, // 项目充值占比(项目订单金额/总订单金额)
        'cost_ratio'                => 0, // 项目消耗占比(项目抵扣金额/代金券大盘抵扣金额)
        'year_total_pay_rmb'        => 0, // 总流水 (年度，计算可用成本)
        'year_coupon_deduct_amount' => 0, // 代金券抵扣金额（年度，计算可用成本）
        'year_coin_deduct_amount'   => 0, // 九玩币抵扣金额（年度，计算可用成本）
        'year_cost_usable'          => 0, // 可用成本

        // 分项目运营活动数据
        'classify_data'             => [], // 分项目运营活动数据 classify为键值
        /* 分项目运营活动数据 例子
        'classify_data' => [
            // 用户成长体系
            1 => [
                'coupon_order_amount'  => 0, // 用户成长体系代金券支付订单金额
                'coupon_deduct_amount' => 0, // 用户成长体系代金券抵扣金额
                'coupon_deduct_ratio'  => 0, // 抵扣比例（用户成长体系抵扣金额/用户成长体系代金券支付订单金额）
                'cost_ratio'           => 0, // 项目消耗占比(单项目用户成长体系动抵扣金额/所有项目用户成长体系抵扣金额)
            ],
            // 运营活动
            3 => [
                'coupon_order_amount'  => 0, // 运营活动代金券支付订单金额
                'coupon_deduct_amount' => 0, // 运营活动代金券抵扣金额
                'coupon_deduct_ratio'  => 0, // 抵扣比例（运营活动抵扣金额/运营活动代金券支付订单金额）
                'cost_ratio'           => 0, // 项目消耗占比(单项目运营活动动抵扣金额/所有项目运营活动抵扣金额)
            ],
        ],*/
    ];

    // 分项目运营活动数据初始化数据
    public $initGameClassifyData = [
        'coupon_order_amount'  => 0, // 投放类型代金券支付订单金额
        'coupon_deduct_amount' => 0, // 投放类型代金券抵扣金额
        'coupon_deduct_ratio'  => 0, // 抵扣比例（投放类型抵扣金额/投放类型代金券支付订单金额）
        'cost_ratio'           => 0, // 项目消耗占比(单项目当前运营活动抵扣金额/所有项目当前运营活动抵扣金额)
    ];

    // 部门大盘汇总数据
    public $allDepList = [];

    // 投放类型大盘汇总数据
    public $allClassifyList = [];

    // 投放类型分类数据 键值为 classify
    public $classifyList = [];


    public function run()
    {
        // 获取api全部权限
        $_SERVER['REQUEST_URI'] = ''; // 初始化REQUEST_URI, 不然获取 \Plus::$app->request 报错
        \Plus::$service->admin->specAuthByUser(\Plus::$app->request, 'api');

        // 格式化参数
        $args   = func_get_args();
        $params = $this->initParams(($args[0]["p"] ?? []));
        var_dump(1);
        // 获取充值数据、部门代金券使用情况、年度代金券情况
        [$incomeRe, $couponRe, $incomeYearRe] = $this->getData($params);
        var_dump(2);
        // 处理数据汇总
        $this->handleData($incomeRe, $couponRe, $incomeYearRe);
        var_dump(3);
        // 格式化数据
        $this->formatData($this->dataList);
        var_dump(4);
        // 替换中文映射
        $this->replaceColumns();
        var_dump(5);
        // 封装html
        $htmlData = $this->html();
        var_dump(6);
        // 发送邮件
        $this->sendMail($htmlData);
        var_dump('7' . '结束');
        return true;
    }

    /**
     * 格式化参数
     *
     * @param array $params
     * @return void|array
     */
    public function initParams($data = [])
    {
        $params             = [
            'start_date' => $data[0] ?? date('Y-m-01', strtotime('-1 month')),
            // 'end_date'   => $data[1] ?? date('Y-m-01', strtotime()),
        ];
        $params['end_date'] = $data[1] ?? (date('Y-m-d', strtotime("{$params['start_date']} +1 month -1 day")));

        $this->month = (int)date('m', strtotime($params['start_date']));

        return $params;
    }

    /**
     * 获取代金券和部门使用情况
     *
     * @param array $params
     * @return void
     */
    public function getData($params = [])
    {
        $options = [
            'range_date_start' => $params['start_date'],
            'range_date_end'   => $params['end_date'],
        ];

        $incomeOptions         = $options;
        $incomeOptions['type'] = 1;

        // 获取付费数据
        $incomeServ = new IncomeServ();
        $incomeRe   = $incomeServ->rechargeInfo($incomeOptions, ['cp_game_id'], [], ['sum' => 'DESC'], $incomeServ::MODE_LIST, ['cp_game_id', 'sum_amount', 'coin_deduct', 'coupon_deduct']);

        // 年度付费数据，计算可用成本
        $incomeOptions['range_date_start'] = date('Y-01-01');
        $incomeOptions['range_date_end']   = date('Y-m-d', strtotime("{$params['start_date']} -1 day"));

        $incomeYearRe = $incomeServ->rechargeInfo($incomeOptions, ['cp_game_id'], [], ['sum' => 'DESC'], $incomeServ::MODE_LIST, ['cp_game_id', 'sum_amount', 'coin_deduct', 'coupon_deduct']);

        $paymentCountServ         = new PaymentCountServ();
        $options['department_id'] = $this->depIdList;
        $couponRe                 = $paymentCountServ->fetchPayExtInfo($options, ['cp_game_id', 'coupon_department_id', 'classify'], [], [], $paymentCountServ::MODE_LIST);

// echo '<pre>';
// var_dump($couponRe);exit;

        return [$incomeRe, $couponRe, $incomeYearRe];
    }

    /**
     * 大盘数据
     *
     * @param array $data 充值明细
     * @return void
     */
    public function dapan($data = [])
    {

        // 平台数据总览

        // 总订单金额
        $this->dataList['dapan']['total_order_amount'] += (($data['sum'] ?? 0) + ($data['coin_deduct'] ?? 0) + ($data['coupon_deduct'] ?? 0));
        // 总流水（RMB支付
        $this->dataList['dapan']['total_pay_rmb'] += ($data['sum'] ?? 0);

        // 虚拟支付
        // 虚拟支付抵扣金额
        $this->dataList['dapan']['virtual_deduct_amount'] += (($data['coin_deduct'] ?? 0) + ($data['coupon_deduct'] ?? 0));
        // 虚拟支付抵扣占比（虚拟支付抵扣金额/总订单金额）
        $this->dataList['dapan']['virtual_deduct_ratio'] = $this->dataList['dapan']['total_order_amount'] > 0 ? $this->dataList['dapan']['virtual_deduct_amount'] / $this->dataList['dapan']['total_order_amount'] : 0;
        // 虚拟支付支出成本（虚拟支付抵扣金额 * 20%）
        $this->dataList['dapan']['virtual_deduct_cost'] = $this->dataList['dapan']['virtual_deduct_amount'] * 0.2;

        // 九玩支付
        // 九玩币抵扣金额
        $this->dataList['dapan']['coin_deduct_amount'] += ($data['coin_deduct'] ?? 0);
        // 九玩币抵扣占比（九玩币抵扣金额/总订单金额）
        $this->dataList['dapan']['coin_deduct_ratio'] = $this->dataList['dapan']['total_order_amount'] > 0 ? $this->dataList['dapan']['coin_deduct_amount'] / $this->dataList['dapan']['total_order_amount'] : 0;
        // 九玩币支出成本（九玩币抵扣金额 * 20%） */
        $this->dataList['dapan']['coin_deduct_cost'] = $this->dataList['dapan']['coin_deduct_amount'] * 0.2;

        // 代金券
        // 代金券抵扣金额
        $this->dataList['dapan']['coupon_deduct_amount'] += ($data['coupon_deduct'] ?? 0);
        // 代金券抵扣占比（代金券抵扣金额/总订单金额）
        $this->dataList['dapan']['coupon_deduct_ratio'] = $this->dataList['dapan']['total_order_amount'] > 0 ? $this->dataList['dapan']['coupon_deduct_amount'] / $this->dataList['dapan']['total_order_amount'] : 0;
        // 代金券支出成本（代金券抵扣金额 * 20%
        $this->dataList['dapan']['coupon_deduct_cost'] = $this->dataList['dapan']['coupon_deduct_amount'] * 0.2;

    }

    /**
     * Undocumented function
     *
     * @return void
     */
    public function yearGameIncomeData($data = [])
    {
        $cpGame = $data['cp_game_id'] ?? ($data['CP_GAME_ID'] ?? 0);

        if (isset($this->dataList['game_list'][$cpGame])) {
            // 总流水（总充值金额）（年度）
            $this->dataList['game_list'][$cpGame]['year_total_pay_rmb'] += ($data['sum'] ?? 0);
            // 代金券抵扣金额（年度）
            $this->dataList['game_list'][$cpGame]['year_coupon_deduct_amount'] += ($data['coupon_deduct'] ?? 0);
            // 九玩币抵扣金额（年度）
            $this->dataList['game_list'][$cpGame]['year_coin_deduct_amount'] += $data['coin_deduct'] ?? 0;

            /* 虚拟成本总预算 = 游戏项目总充值金额 * 2%
            已使用代金券成本 = 已使用代金券抵扣金额 * 20%
            已使用九玩币成本 = 已使用九玩币抵扣金额 * 20% */

            // 虚拟成本总预算（年度）
            $this->dataList['game_list'][$cpGame]['year_virtually_cost_budget'] = ($this->dataList['game_list'][$cpGame]['year_total_pay_rmb'] ?? 0) * 0.02;
            // 代金券已用成本（年度）
            $this->dataList['game_list'][$cpGame]['year_cost_coupon_used'] = ($this->dataList['game_list'][$cpGame]['year_coupon_deduct_amount'] ?? 0) * 0.2;
            // 九玩币已用成本（年度）
            $this->dataList['game_list'][$cpGame]['year_cost_coin_used'] = ($this->dataList['game_list'][$cpGame]['year_coin_deduct_amount'] ?? 0) * 0.2;
            // 可用成本（年度）
            $this->dataList['game_list'][$cpGame]['year_cost_usable'] = $this->dataList['game_list'][$cpGame]['year_virtually_cost_budget'] - $this->dataList['game_list'][$cpGame]['year_cost_coupon_used'] - $this->dataList['game_list'][$cpGame]['year_cost_coin_used'];

            $this->dataList['game_list'][$cpGame]['year_cost_usable'] = $this->dataList['game_list'][$cpGame]['year_cost_usable'] > 0 ? $this->dataList['game_list'][$cpGame]['year_cost_usable'] : 0;
        }
    }

    /**
     * 代金券数据-分项目数据
     *
     * @param array $data
     * @return void
     */
    public function gameIncomeData($data = [])
    {
        /* 分项目数据
        ---------------------------------------
        项目名称（游戏原名）   总订单金额   代金券支付订单金额   抵扣金额   抵扣比例（项目抵扣金额/项目代金券支付订单金额）   代金券抵扣占流水占比（项目抵扣金额/项目总订单金额）   项目本月可用成本   成本（项目抵扣金额 * 20%） */

        /* ["CP_GAME_ID"]=>
        string(3) "692"
        ["sum"]=>
        string(11) "10145250.00"
        ["coin_deduct"]=>
        string(7) "1804.00"
        ["coupon_deduct"]=>
        string(9) "265081.00" */


        $cpGame = $data['cp_game_id'] ?? ($data['CP_GAME_ID'] ?? 0);
        // 初始化数据
        $this->dataList['game_list'][$cpGame]               = $this->dataList['game_list'][$cpGame] ?? $this->initGameList;
        $this->dataList['game_list'][$cpGame]['cp_game_id'] = $cpGame;

        // 总订单金额
        $this->dataList['game_list'][$cpGame]['total_order_amount'] += (($data['sum'] ?? 0) + ($data['coupon_deduct'] ?? 0) + ($data['coin_deduct'] ?? 0));

        // 充值金额
        $this->dataList['game_list'][$cpGame]['total_pay_rmb'] += ($data['sum'] ?? 0);

        // 代金券支付订单金额

        // 代金券抵扣金额
        $this->dataList['game_list'][$cpGame]['coupon_deduct_amount'] += ($data['coupon_deduct'] ?? 0);
        // 九玩币抵扣金额
        $this->dataList['game_list'][$cpGame]['coin_deduct_amount'] += $data['coin_deduct'] ?? 0;

        // 抵扣比例（项目抵扣金额/项目代金券支付订单金额）


        // 代金券抵扣占流水占比（项目抵扣金额/项目总订单金额）
        $this->dataList['game_list'][$cpGame]['coupon_total_ratio'] = $this->dataList['game_list'][$cpGame]['total_order_amount'] > 0 ? $this->dataList['game_list'][$cpGame]['coupon_deduct_amount'] / $this->dataList['game_list'][$cpGame]['total_order_amount'] : 0;

        // 项目本月可用成本

        // 成本（项目抵扣金额 * 20%）
        $this->dataList['game_list'][$cpGame]['coupon_deduct_cost'] = $this->dataList['game_list'][$cpGame]['coupon_deduct_amount'] * 0.2;
    }

    /**
     * 代金券数据-分部门数据
     *
     * @param array $data
     * @return void
     */
    public function depCouponData($data = [])
    {
        /* 代金券数据
        ---------------------------------------
        分部门数据
        ---------------------------------------
        部门   代金券支付订单金额   抵扣金额   抵扣比例(部门抵扣金额/部门代金券支付订单金额）   成本（部门抵扣金额 * 20%）   部门使用占比（部门抵扣金额/代金券大盘抵扣金额）
        --------------------------------------- */

        $depId = ($data['coupon_department_id'] ?? 0) ? $data['coupon_department_id'] : 389;

        // 初始化大盘数值
        $this->allDepList = $this->allDepList ?: $this->initDepList;
        // 初始化部门数值
        $this->dataList['dep_list'][$depId]                  = $this->dataList['dep_list'][$depId] ?? $this->initDepList;
        $this->dataList['dep_list'][$depId]['department_id'] = $depId;

        // 代金券支付订单金额
        $this->dataList['dep_list'][$depId]['coupon_order_amount'] += ($data['money'] ?? 0);
        // 代金券抵扣金额
        $this->dataList['dep_list'][$depId]['coupon_deduct_amount'] += $data['coupon_money'] ?? 0;
        // 大盘抵扣金额
        $this->allDepList['coupon_deduct_amount'] += $data['coupon_money'] ?? 0;
        // 抵扣比例(部门抵扣金额/部门代金券支付订单金额）
        $this->dataList['dep_list'][$depId]['coupon_deduct_ratio'] = $this->dataList['dep_list'][$depId]['coupon_order_amount'] > 0 ? $this->dataList['dep_list'][$depId]['coupon_deduct_amount'] / $this->dataList['dep_list'][$depId]['coupon_order_amount'] : 0;
        // 成本（部门抵扣金额 * 20%）
        $this->dataList['dep_list'][$depId]['coupon_deduct_cost'] = $this->dataList['dep_list'][$depId]['coupon_deduct_amount'] * 0.2;
        /* // 部门使用占比（部门抵扣金额/代金券大盘抵扣金额）
        $this->dataList['dep_list'][$depId]['dep_used_ratio'] = $this->allDepList['coupon_deduct_amount'] > 0 ? $this->dataList['dep_list'][$depId]['coupon_deduct_amount']/$this->allDepList['coupon_deduct_amount'] : 0; */
    }

    /**
     * 代金券数据-分项目数据
     *
     * @param array $data
     * @return void
     */
    public function gameCouponData($data = [])
    {
        // 分项目数据
        $cpGame = $data['cp_game_id'] ?? ($data['CP_GAME_ID'] ?? 0);
        // 初始化数据
        $this->dataList['game_list'][$cpGame]               = $this->dataList['game_list'][$cpGame] ?? $this->initGameList;
        $this->dataList['game_list'][$cpGame]['cp_game_id'] = $cpGame;

        // 代金券支付订单金额
        $this->dataList['game_list'][$cpGame]['coupon_order_amount'] += ($data['money'] ?? 0);

        // 抵扣比例（项目抵扣金额/项目代金券支付订单金额）
        $this->dataList['game_list'][$cpGame]['coupon_deduct_ratio'] = $this->dataList['game_list'][$cpGame]['coupon_order_amount'] > 0 ? $this->dataList['game_list'][$cpGame]['coupon_deduct_amount'] / $this->dataList['game_list'][$cpGame]['coupon_order_amount'] : 0;


        // 分运营活动数据-------------------------
        $classify = $data['classify'] ?? ($data['CLASSIFY'] ?? 0);
        // 初始化数据
        $this->dataList['game_list'][$cpGame]['classify_data'][$classify]             = $this->dataList['game_list'][$cpGame]['classify_data'][$classify] ?? $this->initGameClassifyData;
        $this->dataList['game_list'][$cpGame]['classify_data'][$classify]['classify'] = $classify;

        // 代金券支付订单金额
        $this->dataList['game_list'][$cpGame]['classify_data'][$classify]['coupon_order_amount'] += ($data['money'] ?? 0);
        // 代金券抵扣金额
        $this->dataList['game_list'][$cpGame]['classify_data'][$classify]['coupon_deduct_amount'] += $data['coupon_money'] ?? 0;
        // 抵扣比例(部门抵扣金额/部门代金券支付订单金额）
        $this->dataList['game_list'][$cpGame]['classify_data'][$classify]['coupon_deduct_ratio'] = $this->dataList['game_list'][$cpGame]['classify_data'][$classify]['coupon_order_amount'] > 0 ? $this->dataList['game_list'][$cpGame]['classify_data'][$classify]['coupon_deduct_amount'] / $this->dataList['game_list'][$cpGame]['classify_data'][$classify]['coupon_order_amount'] : 0;

        // 投放类型各类型汇总数据
        $this->classifyList[$classify]['coupon_deduct_amount'] = ($this->classifyList[$classify]['coupon_deduct_amount'] ?? 0) + ($data['coupon_money'] ?? 0);

    }

    /**
     * 代金券数据-投放类型数据
     *
     * @param array $data
     * @return void
     */
    public function classifyCouponData($data = [])
    {
        /* 代金券数据
        ---------------------------------------
        分投放类型数据
        ---------------------------------------
        投放类型   代金券支付订单金额   抵扣金额   抵扣比例(投放类型抵扣金额/投放类型代金券支付订单金额）   成本（投放类型抵扣金额 * 20%）   投放类型使用占比（投放类型抵扣金额/代金券大盘抵扣金额）
        --------------------------------------- */

        $classify = ($data['classify'] ?? 0) ? $data['classify'] : 0;

        // 初始化大盘数值
        $this->allClassifyList = $this->allClassifyList ?: $this->initClassifyList;
        // 初始化投放类型数值
        $this->dataList['classify_list'][$classify]             = $this->dataList['classify_list'][$classify] ?? $this->initClassifyList;
        $this->dataList['classify_list'][$classify]['classify'] = $classify;

        // 代金券支付订单金额
        $this->dataList['classify_list'][$classify]['coupon_order_amount'] += ($data['money'] ?? 0);
        // 代金券抵扣金额
        $this->dataList['classify_list'][$classify]['coupon_deduct_amount'] += $data['coupon_money'] ?? 0;
        // 大盘抵扣金额
        $this->allClassifyList['coupon_deduct_amount'] += $data['coupon_money'] ?? 0;
        // 抵扣比例(投放类型抵扣金额/投放类型代金券支付订单金额）
        $this->dataList['classify_list'][$classify]['coupon_deduct_ratio'] = $this->dataList['classify_list'][$classify]['coupon_order_amount'] > 0 ? $this->dataList['classify_list'][$classify]['coupon_deduct_amount'] / $this->dataList['classify_list'][$classify]['coupon_order_amount'] : 0;
        // 成本（投放类型抵扣金额 * 20%）
        $this->dataList['classify_list'][$classify]['coupon_deduct_cost'] = $this->dataList['classify_list'][$classify]['coupon_deduct_amount'] * 0.2;
        // 投放类型使用占比（投放类型抵扣金额/代金券大盘抵扣金额）
        $this->dataList['classify_list'][$classify]['classify_used_ratio'] = $this->allClassifyList['coupon_deduct_amount'] > 0 ? $this->dataList['classify_list'][$classify]['coupon_deduct_amount'] / $this->allClassifyList['coupon_deduct_amount'] : 0;
    }

    /**
     * 发mail
     *
     * @return void
     */
    public function sendMail($html = '')
    {
        $mailConfig = \Plus\Internal\Config::get('Mail');
        $title      = '' . $this->month . '月代金券数据月报';

        foreach ($this->mailUserList as $key => $value) {
            if (($value['mail'] ?? '')) {
                (new Email(
                    [
                        // "host"       => "ssl://smtp.exmail.qq.com",
                        "host"       => $mailConfig[0]['SMTP_HOST'] ?? '',
                        "port"       => "465",
                        "auth_user"  => $mailConfig[0]['SMTP_AUTH_USER'] ?? '',
                        "auth_pwd"   => $mailConfig[0]['SMTP_AUTH_PASSWORD'] ?? '',
                        "from_name"  => 'spy报表',
                        "from_email" => $mailConfig[0]['FROM_EMAIL'] ?? '',
                        "timeout"    => 10,
                    ]
                ))->send($value['mail'], $value['mail'], $title, $html, 1);
            }
        }
    }

    /**
     * 处理数据
     *
     * @param array $incomeRe
     * @param array $couponRe
     * @return void
     */
    public function handleData($incomeRe = [], $couponRe = [], $incomeYearRe)
    {
        foreach (($incomeRe['list'] ?? []) as $key => $value) {
            // 展示指定游戏，否则规则其他游戏
            $value = $this->channgeShowGame($value);
            // 大盘
            $this->dapan($value);
            // 分游戏数据
            $this->gameIncomeData($value);
        }

        foreach (($couponRe['list'] ?? []) as $key => $value) {
            // 展示指定游戏，否则规则其他游戏
            $value = $this->channgeShowGame($value);

            // 分部门数据
            $this->depCouponData($value);

            // 分游戏数据
            $this->gameCouponData($value);

            // 投放类型数据
            $this->classifyCouponData($value);
        }

        // 年度预算成本等计算
        foreach (($incomeYearRe['list'] ?? []) as $key => $value) {
            // 展示指定游戏，否则规则其他游戏
            $value = $this->channgeShowGame($value);

            $this->yearGameIncomeData($value);
        }

        // 分部门占比
        foreach ($this->dataList['dep_list'] as $key => $value) {
            $this->dataList['dep_list'][$key]['dep_used_ratio'] = $this->allDepList['coupon_deduct_amount'] > 0 ? $value['coupon_deduct_amount'] / $this->allDepList['coupon_deduct_amount'] : 0;
        }

        // 项目数据大盘总数据占比计算
        foreach ($this->dataList['game_list'] as $key => $value) {
            // 项目充值占比(项目订单金额/总订单金额)（大盘有种订单金额）
            $this->dataList['game_list'][$key]['total_order_ratio'] = $this->dataList['dapan']['total_order_amount'] > 0 ? $this->dataList['game_list'][$key]['total_order_amount'] / $this->dataList['dapan']['total_order_amount'] : 0;

            // 项目消耗占比(项目抵扣金额/代金券大盘抵扣金额)
            $this->dataList['game_list'][$key]['cost_ratio'] = $this->dataList['dapan']['coupon_deduct_amount'] > 0 ? $this->dataList['game_list'][$key]['coupon_deduct_amount'] / $this->dataList['dapan']['coupon_deduct_amount'] : 0;

            // 分项目投放类型消耗占比(项目投放类型抵扣金额/投放类型代金券抵扣金额)
            foreach ($value['classify_data'] as $k => $v) {
                $this->dataList['game_list'][$key]['classify_data'][$k]['cost_ratio'] = ($this->classifyList[$v['classify']]['coupon_deduct_amount'] ?? 0) > 0 ? $v['coupon_deduct_amount'] / $this->classifyList[$v['classify']]['coupon_deduct_amount'] : 0;
            }
        }

    }

    /**
     * 格式化所有数据
     *
     * @return void
     */
    public function formatData(&$data = [])
    {
        foreach ($data as $key => &$tmp) {
            if (is_array($tmp)) {
                $this->formatData($tmp);
            }
            else {
                if (strstr($key, 'ratio')) {
                    $data[$key] = number_format(round($tmp * 100, 2), 2, '.', '') . '%';
                }
                else {
                    $data[$key] = round($data[$key]);
                }
            }
        }
    }

    /**
     * 中文映射
     *
     * @return void
     */
    public function replaceColumns()
    {
        $constConfCollect = (new BasicServ())->getMultiOptions(['cp_game_id:all', 'department_id', 'classify']);
        $columnsList      = [];
        foreach ($constConfCollect as $key => $value) {
            $columnsList[$key] = array_column($value->toArray(), 'val', 'key');
        }

        foreach ($this->dataList['classify_list'] as $key => $value) {
            $this->dataList['classify_list'][$key]['classify'] = $columnsList['classify'][$value['classify']] ?? $value['classify'];
            // 过滤某些内容
            if (in_array($this->dataList['classify_list'][$key]['classify'], ['内部测试', '0'])) {
                unset($this->dataList['classify_list'][$key]);
            }
        }

        foreach ($this->dataList['dep_list'] as $key => $value) {
            $this->dataList['dep_list'][$key]['department_id'] = $columnsList['department_id'][$value['department_id']] ?? $value['department_id'];
        }
        // 部门排序
        $tmpList = [];
        foreach ($this->depIdList as $key => $value) {
            if (isset($this->dataList['dep_list'][$value])) {
                $tmpList[$value] = $this->dataList['dep_list'][$value];
            }
        }
        $this->dataList['dep_list'] = $tmpList;

        // 调整其他项目排序到最后
        $lastGame = [];
        foreach ($this->dataList['game_list'] as $key => $value) {
            if ($this->dataList['game_list'][$key]['cp_game_id'] == 0) {
                $this->dataList['game_list'][$key]['cp_game_id'] = '其他项目';
                // 其他项目挪到最后
                $lastGame = $this->dataList['game_list'][$key];
                unset($this->dataList['game_list'][$key]);
            }
            else {
                $this->dataList['game_list'][$key]['cp_game_id'] = $columnsList['cp_game_id'][$value['cp_game_id']] ?? $value['cp_game_id'];
            }
        }
        if ($lastGame) $this->dataList['game_list'] = array_merge($this->dataList['game_list'], [$lastGame]);

        // 把同名项目临近排序
        $sameNameGameList = [];
        // 所有项目名称
        $allGameNameList = array_column($this->dataList['game_list'], 'cp_game_id');

        foreach ($this->dataList['game_list'] as $key => $value) {
            // 去掉最后一字符判断是否为1 2 3 4
            $strGameName = mb_substr($value['cp_game_id'], 0, -1);

            // 判断最后一位是否为2
            $lastChar = mb_substr($value['cp_game_id'], -1);

            // 判断是否有同名项目（纯中文，不带2 3 4）
            if (in_array($strGameName, $allGameNameList) && in_array($lastChar, [2, 3, 4])) {
                $sameNameGameList[$strGameName][] = $value;
                unset($this->dataList['game_list'][$key]);
            }
        }
        // 分配项目 2 3 4
        foreach ($this->dataList['game_list'] as $key => $value) {
            foreach (($sameNameGameList[$value['cp_game_id']] ?? []) as $k => $v) {
                array_splice($this->dataList['game_list'], $key + $k + 1, 0, [$v]);
            }
        }
    }

    /**
     * 判断展示的游戏
     *
     * @param array $value
     * @return void|array
     */
    public function channgeShowGame($value = [])
    {
        $cpGame = $value['cp_game_id'] ?? ($value['CP_GAME_ID'] ?? 0);
        if (!in_array($cpGame, $this->showGameIdList)) {
            $value['cp_game_id'] = 0;
        }

        return $value;
    }


    /**
     * 组装html
     *
     * @return void|array
     */
    public function html()
    {
        $header = "
Dear all,<br>
以下为九玩平台{$this->month}月代金券数据月报，请查收。<br>
如有疑问，请及时反馈，感谢。<br><br>
";
        // 大盘数据html
        $htmlDapan = $this->htmlDapan();

        // 部门数据html
        $htmlDepList = $this->htmlDepList();

        // 分投放类型html
        $htmlClassifyList = $this->htmlClassifyList();

        // 游戏数据html
        $htmlGameList = $this->htmlGameList();

        // 分项目运营活动（暂时还没有数据）
        $htmlActivityList = $this->htmlActivityList();

        $html = $header . $htmlDapan . $htmlDepList . $htmlClassifyList . $htmlGameList . $htmlActivityList;

        return $html;
    }

    /**
     * 大盘数据html
     *
     * @return void
     */
    public function htmlDapan()
    {
        $content = "
                    <div>
                        <span><b>大盘数据</b></span>
                        <table width='1480' border='1' cellspacing='0' cellpadding='3' style='word-break: break-all;text-align: center;'>
                        <tr style='text-align: center;'>
                            <td colspan='6' style='padding: 5px;background-color: #fce4d6;'>平台数据总览</td>
                        </tr>
                        <tr style='background-color: #f2f2f2;'>
                            <td colspan='2'>总订单金额</td>
                            <td colspan='1'>{$this->dataList['dapan']['total_order_amount']}</td>
                            <td colspan='2'>总流水 (RMB支付)</td>
                            <td colspan='1'>{$this->dataList['dapan']['total_pay_rmb']}</td>
                        </tr>
                        <tr>
                            <td>虚拟支付抵扣金额</td>
                            <td>{$this->dataList['dapan']['virtual_deduct_amount']}</td>
                            <td>九玩币抵扣金额</td>
                            <td>{$this->dataList['dapan']['coin_deduct_amount']}</td>
                            <td>代金券抵扣金额</td>
                            <td>{$this->dataList['dapan']['coupon_deduct_amount']}</td>
                        </tr>
                        <tr style='background-color: #f2f2f2;'>
                            <td>虚拟支付抵扣占比</td>
                            <td>{$this->dataList['dapan']['virtual_deduct_ratio']}</td>
                            <td>九玩币抵扣占比</td>
                            <td>{$this->dataList['dapan']['coin_deduct_ratio']}</td>
                            <td>代金券抵扣占比</td>
                            <td>{$this->dataList['dapan']['coupon_deduct_ratio']}</td>
                        </tr>
                        <tr>
                            <td>虚拟支付支出成本</td>
                            <td>{$this->dataList['dapan']['virtual_deduct_cost']}</td>
                            <td>九玩币支出成本</td>
                            <td>{$this->dataList['dapan']['coin_deduct_cost']}</td>
                            <td>代金券支出成本</td>
                            <td>{$this->dataList['dapan']['coupon_deduct_cost']}</td>
                        </tr>
                        </table>
                    </div>";

        return $content;
    }

    /**
     * 代金券数据-分部门数据html
     *
     * @return void
     */
    public function htmlDepList()
    {
        $content = "
                    <div style='margin-top:80px'>
                        <span><b>代金券数据</b></span>
                        <table width='1480' border='1' cellspacing='0' cellpadding='3' style='word-break: break-all;text-align: center;'>
                        <tr style='text-align: center;'>
                            <td colspan='6' style='padding: 5px;background-color: #fce4d6;'>分部门数据</td>
                        </tr>
                        <tr style='background-color: #f2f2f2;'>
                            <td style='width: 10%'>部门</td>
                            <td style='width: 10%'>代金券支付订单金额</td>
                            <td style='width: 10%'>抵扣金额</td>
                            <td style='width: 10%'>抵扣比例</td>
                            <td style='width: 10%'>成本</td>
                            <td style='width: 10%'>部门使用占比</td>
                        </tr>";

        $this->dataList['dep_list'] = array_values($this->dataList['dep_list']);
        foreach ($this->dataList['dep_list'] as $key => $value) {
            $styleTr = '';
            if ($key % 2 == 1) $styleTr = "style='background-color: #f2f2f2;'";

            $trHtml = "
                        <tr {$styleTr}>
                            <td style='width: 10%'>{$value['department_id']}</td>
                            <td style='width: 10%'>{$value['coupon_order_amount']}</td>
                            <td style='width: 10%'>{$value['coupon_deduct_amount']}</td>
                            <td style='width: 10%'>{$value['coupon_deduct_ratio']}</td>
                            <td style='width: 10%'>{$value['coupon_deduct_cost']}</td>
                            <td style='width: 10%'>{$value['dep_used_ratio']}</td>
                        </tr>";

            $content .= $trHtml;
        }

        $content .= "</table></div>";

        return $content;
    }

    /**
     * 代金券数据-分项目数据html
     *
     * @return void
     */
    public function htmlGameList()
    {
        $content = "
                    <div style='margin-top:40px'>
                        <table width='1820' border='1' cellspacing='0' cellpadding='3' style='word-break: break-all;text-align: center;'>
                        <tr style='text-align: center;'>
                            <td colspan='10' style='padding: 5px;background-color: #fce4d6;'>分项目数据</td>
                        </tr>
                        <tr style='background-color: #f2f2f2;'>
                            <td style='width: 10%'>项目名称</td>
                            <td style='width: 10%'>总订单金额</td>
                            <td style='width: 10%'>代金券支付订单金额</td>
                            <td style='width: 10%'>抵扣金额</td>
                            <td style='width: 10%'>抵扣比例</td>
                            <td style='width: 10%'>代金券抵扣占流水占比</td>
                            <td style='width: 10%'>项目本月可用成本</td>
                            <td style='width: 10%'>成本</td>
                            <td style='width: 10%'>项目充值占比</td>
                            <td style='width: 10%'>项目消耗占比</td>
                        </tr>";

        $this->dataList['game_list'] = array_values($this->dataList['game_list']);
        foreach ($this->dataList['game_list'] as $key => $value) {

            $styleTr = '';
            if ($key % 2 == 1) $styleTr = "style='background-color: #f2f2f2;'";

            $trHtml = "<tr {$styleTr}>
                            <td style='width: 10%'>{$value['cp_game_id']}</td>
                            <td style='width: 10%'>{$value['total_order_amount']}</td>
                            <td style='width: 10%'>{$value['coupon_order_amount']}</td>
                            <td style='width: 10%'>{$value['coupon_deduct_amount']}</td>
                            <td style='width: 10%'>{$value['coupon_deduct_ratio']}</td>
                            <td style='width: 10%'>{$value['coupon_total_ratio']}</td>
                            <td style='width: 10%'>{$value['year_cost_usable']}</td>
                            <td style='width: 10%'>{$value['coupon_deduct_cost']}</td>
                            <td style='width: 10%'>{$value['total_order_ratio']}</td>
                            <td style='width: 10%'>{$value['cost_ratio']}</td>
                        </tr>";

            $content .= $trHtml;
        }

        $content .= "</table></div>";

        return $content;
    }

    /**
     * 投放类型数据html
     *
     * @return void
     */
    public function htmlClassifyList()
    {
        $content = "
                    <div style='margin-top:40px'>
                        <table width='1820' border='1' cellspacing='0' cellpadding='3' style='word-break: break-all;text-align: center;'>
                        <tr style='text-align: center;'>
                            <td colspan='6' style='padding: 5px;background-color: #fce4d6;'>分投放类型数据</td>
                        </tr>
                        <tr style='background-color: #f2f2f2;'>
                            <td style='width: 10%'>投放类型</td>
                            <td style='width: 10%'>代金券支付订单金额</td>
                            <td style='width: 10%'>抵扣金额</td>
                            <td style='width: 10%'>抵扣比例</td>
                            <td style='width: 10%'>成本</td>
                            <td style='width: 10%'>投放类型占比</td>
                        </tr>";

        $this->dataList['classify_list'] = array_values($this->dataList['classify_list']);
        foreach ($this->dataList['classify_list'] as $key => $value) {

            $styleTr = '';
            if ($key % 2 == 1) $styleTr = "style='background-color: #f2f2f2;'";

            $trHtml = "<tr {$styleTr}>
                            <td style='width: 10%'>{$value['classify']}</td>
                            <td style='width: 10%'>{$value['coupon_order_amount']}</td>
                            <td style='width: 10%'>{$value['coupon_deduct_amount']}</td>
                            <td style='width: 10%'>{$value['coupon_deduct_ratio']}</td>
                            <td style='width: 10%'>{$value['coupon_deduct_cost']}</td>
                            <td style='width: 10%'>{$value['classify_used_ratio']}</td>
                        </tr>";

            $content .= $trHtml;
        }

        $content .= "</table></div>";

        return $content;
    }

    /**
     * 分运营活动数据html
     *
     * @return void
     */
    public function htmlActivityList()
    {
        $content = "
                    <div style='margin-top:80px'>
                    <table width='1820' border='1' cellspacing='0' cellpadding='3' style='word-break: break-all;text-align: center;'>
                        <tr style='text-align: center;'>
                            <td colspan='10' style='padding: 5px;background-color: #fce4d6;'>分项目运营活动数据</td>
                        </tr>
                        <tr>
                            <td rowspan=2 style='width: 10%'>项目名称</td>
                            <td colspan=3 style='width: 30%'>运营活动数据</td>
                            <td colspan=3 style='width: 30%'>用户成长体系数据</td>
                            <td rowspan=2 style='width: 10%'>项目充值占比</td>
                            <td rowspan=2 style='width: 10%'>项目消耗占比(运营活动)</td>
                            <td rowspan=2 style='width: 10%'>项目消耗占比(用户成长体系)</td>
                        </tr>
                        <tr>
                            <td style='width: 10%'>代金券支付订单金额</td>
                            <td style='width: 10%'>抵扣金额</td>
                            <td style='width: 10%'>抵扣比例</td>
                            <td style='width: 10%'>代金券支付订单金额</td>
                            <td style='width: 10%'>抵扣金额</td>
                            <td style='width: 10%'>抵扣比例</td>
                        </tr>";

        $this->dataList['game_list'] = array_values($this->dataList['game_list']);
        foreach ($this->dataList['game_list'] as $key => $value) {

            foreach ([1, 3] as $k => $v) {
                $value['classify_data'][$v] = $value['classify_data'][$v] ?? $this->initGameClassifyData;
            }

            $styleTr = '';
            if ($key % 2 == 1) $styleTr = "style='background-color: #f2f2f2;'";
            $trHtml = "<tr {$styleTr}>
                            <td style='width: 10%'>{$value['cp_game_id']}</td>
                            <td style='width: 10%'>{$value['classify_data'][3]['coupon_order_amount']}</td>
                            <td style='width: 10%'>{$value['classify_data'][3]['coupon_deduct_amount']}</td>
                            <td style='width: 10%'>{$value['classify_data'][3]['coupon_deduct_ratio']}</td>
                            <td style='width: 10%'>{$value['classify_data'][1]['coupon_order_amount']}</td>
                            <td style='width: 10%'>{$value['classify_data'][1]['coupon_deduct_amount']}</td>
                            <td style='width: 10%'>{$value['classify_data'][1]['coupon_deduct_ratio']}</td>
                            <td style='width: 10%'>{$value['cost_ratio']}</td>
                            <td style='width: 10%'>{$value['classify_data'][3]['cost_ratio']}</td>
                            <td style='width: 10%'>{$value['classify_data'][1]['cost_ratio']}</td>
                        </tr>";

            $content .= $trHtml;
        }

        $content .= "</table></div>";

        return $content;
    }

}
<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Helpers\Calculator;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Helpers\IndexCalculators;
use app\apps\internal\Helpers\IndicatorCalcHelpers;
use app\apps\internal\Helpers\IndicatorsHelpers;
use app\apps\internal\Helpers\LtvCalculator;
use app\apps\internal\Helpers\RoiCalculator;
use app\apps\internal\Traits\AdOfflineDash;
use app\apps\internal\Traits\AdRouteRequest;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\internal\Traits\OfflineDash;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\AdvertiserData\CreativeLtvIndex;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\AdvertiserData\RealtimePlanIndex;
use app\service\ConfigService\BasicServ;
use app\service\ConfigService\Tables\AdPlanLtv;
use app\service\OriginData\CostInfoServ;
use app\service\General\GeneralOptionServ;
use app\models\OriginPlatform\TbAdCost;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * @AdUploadCostController 计划层离线LTV API
 */
class AdUploadCostController extends BaseTableController
{
    use AdRouteRequest, ColumnsInteract, AdOfflineDash;

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];

        $join = [
            'tb_ad_account_conf',
            'tb_ad_account_ext_conf',
            'tb_base_package_conf',
            'tb_package_user_conf',
            'tb_base_channel_conf',
        ];

        $options = $params->toArray();

        $groups = ['t_cost.id']; // 默认组合
        if ($params->has('groups')) {
            $groups = Arr::wrap($params->pull('groups'));
        }

        $today    = new \DateTime();
        $baseServ = new RealtimePlanIndex();

        if ($params->has('sort')) {
            $sort = $params->pull('sort');

            if ($params->has('order')) {
                // $sort .= ' ' . ($params->pull('order') == 'ascend' ? 'asc' : 'desc');
                $sort = [$sort => ($params->pull('order') == 'ascend' ? 'asc' : 'desc')];
            }
        }
        else {
            $sort = ['t_cost.ID' => 'desc'];
        }
        $sort = Arr::wrap($sort);


        $constConfCollect = (new BasicServ())->getMultiOptions([
            'platform_id', 'ad_account', 'department_id', 'cp_game_id:all', 'game_id', 'channel_id', 'settlement_id', 'agent_id', 'operate_id', 'explain_id', 'cost_index_type', 'user:all'
        ]);
        $constConfCollect->put('update_user_id', $constConfCollect->get('user:all'));
        $constConfCollect->put('user_id', $constConfCollect->get('user:all'));

        $baseResult = (new CostInfoServ())->listBase(
            $options,
            ['page' => $page, 'page_size' => $pageSize],
            $groups,
            $sort,
            [],
            $join
        );

        $constConfCollect = $constConfCollect->map(function (Collection $item, $k) {
            if ('update_user_id' == $k) {
                // 过滤'0'
                $item = $item->except([0]);
                $item->push(['val' => '自动抓取', 'key' => '0']);
            }
            return $item;
        });


        $info = &$baseResult['list'];

        $processLine = new ProcessLine();

        $resetFunc = $this->resetGroupsCols(
            ColumnManager::groupAdRelation($groups),
            $groups,
            array_diff(ConstHub::AD_FIXED_INFO_COLS, ['creative_id', 'creative_name'])
        );

        $processLine
            // 拆解每行对应的搜索条件
            ->prependEachRow($this->parseMatchForEachRow($options, $groups))
            // 替换ID信息字段
            ->addProcess($this->replaceColumnDefine($constConfCollect));
        $processLine->run($info);

        return $baseResult;
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $baseCollect = [
            'tday', 'cp_game_id', 'game_id', 'platform_id',
            'channel_id', 'package_id', 'campaign_id',
            'campaign_name', 'ad_account', 'account_id',
            'settlement_id', 'agent_id', 'operate_id',
            'explain_id', 'department_id', 'adv_user_id',
            'user_id', 'update_time'
        ];

        $mediaIndexCollect = [
            'cost', 'cost_discount', 'show', 'click',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'media_index', 'label' => '媒体转化目标'],
                ],
            ],
        ];

        $fields = $this->tableFields($params->toArray());

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'] ?? '';
            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $mediaIndexCollect)) {
                $field['classify'] = ['attrs', 'media_index'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @param $key
     *
     * @return Collection
     */
    protected function registerParams($key = null): Collection
    {
        return $this
            ->baseParams()
            ->merge([
                ['field' => 'id'], // 广告组id
                ['field' => 'aid'], // 广告组名称
                ['field' => 'cost_index_type'], // 消耗类型标识
                ['field' => 'settlement_id'], //  结算方式
                ['field' => 'agent_id'], //  代理
                ['field' => 'operate_id'], //  运营方式
                ['field' => 'explain_id'], //  区分说明
                ['field' => 'update_user_id'], //  修改人
            ]);
    }

    /**
     *
     * @route /internal/ad-plan-ltv/simple-search
     * @return array
     * @throws \Exception
     * @todo  整合到data
     */
    public function simpleSearchAction(): array
    {
        $params        = $this->wrapParams($this->request);
        $groups        = ['tday'];
        $columnScope   = $params->get('column_scope');
        $baseServ      = new RealtimePlanIndex();
        $ltvServ       = new CreativeLtvIndex();
        $timeDimension = (int)$params->get('range_date_dimension');

        $options = $params->toArray();

        if (ConstHub::DIMENSION_WEEK === $timeDimension) {
            $baseResult = $baseServ->simpleListByWeek($options, [], $groups);
        }
        elseif (ConstHub::DIMENSION_MONTH === $timeDimension) {
            $baseResult = $baseServ->simpleListByMonth($options, [], $groups);
        }
        else {
            $baseResult = $baseServ->simpleListByDay($options, [], $groups);
        }
        $ltvResult = $ltvServ->simpleList($options);
        $ltvInfo   = IndicatorsHelpers::dimReduction($ltvResult['list'], 'day_type', ['tday'], ['money', 'money_all']);
        $info      = &$baseResult['list'];

        $processLine = new ProcessLine();
        $processLine->addProcess(function (&$target) use ($ltvInfo) {
            $tDay = $target['tday'];
            if (!empty($ltvInfo[$tDay])) {
                $target['ltv_info']           = $ltvInfo[$tDay]['day_type'] ?? [];
                $target['new_user_total_pay'] = $target['ltv_info'][1000]['money_all'] ?? 0.00;
            }
        });

        $ltvGetUser = IndexCalculators::getSingleValue('new_user');
        $roiGetCost = IndexCalculators::getSingleValue('cost_discount');

        if (in_array('ltv', $columnScope)) {
            $processLine->addProcess(
                LtvCalculator::calcEachRow($ltvGetUser, ['ltv_type' => 0, 'max_days' => 7])
            );
        }

        if (in_array('roi', $columnScope)) {
            $processLine->addProcess(
                RoiCalculator::calcEachRow($roiGetCost, ['ltv_type' => 0, 'max_days' => 7])
            );
        }

        $processLine
            ->addProcess(LtvCalculator::cumulativeLtvEachRow('new_user'))
            ->addProcess(RoiCalculator::cumulativeRoiEachRow())
            ->addProcess(function (&$target) {
                unset($target['ltv_info']);
            });

        $processLine->run($info);

        /**
         * 汇总行处理
         */
        $summaryRow         = &$baseResult['summary'];
        $summaryLtv         = array_column($ltvResult['summary'], null, 'day_type');
        $summaryRow['tday'] = '汇总';

        // 累计已到天数数据
        $summaryNInfo = Calculator::cumulativeOnDays($info, ['new_user', 'cost_discount']);
        $summaryRow   = array_merge($summaryRow, $summaryNInfo);

        $summaryLtvGetUser = IndexCalculators::getValueInCollectByN('new_user_n');
        $summaryRoiGetCost = IndexCalculators::getValueInCollectByN('cost_discount_n');

        if (in_array('ltv', $columnScope)) {
            LtvCalculator::calcLtv($summaryRow, $summaryLtv, $summaryLtvGetUser);
            if (empty($summaryLtv[1000]) || empty((int)$summaryRow['new_user'])) {
                $summaryRow['total_ltv'] = 0.00;
            }
            else {
                $summaryRow['total_ltv'] = number_format(
                    math_eval('x/y', ['x' => $summaryLtv[1000]['money_all'], 'y' => $summaryRow['new_user']]), 2
                );
            }
        }

        if (in_array('roi', $columnScope)) {
            RoiCalculator::calcRoi($summaryRow, $summaryLtv, $summaryRoiGetCost);

            if (empty($summaryLtv[1000]['money_all']) || empty(floatval($summaryRow['cost_discount']))) {
                $summaryRow['total_roi'] = '0.00%';
            }
            else {
                $summaryRow['total_roi'] = number_format(
                        math_eval('x/y*100', ['x' => $summaryLtv[1000]['money_all'], 'y' => $summaryRow['cost_discount']]), 2
                    ) . '%';
            }
        }
        unset($summaryRow['new_user_n'], $summaryRow['cost_discount_n']);
        $fields = (new AdPlanLtv())->getSimpleFields($options)->toArray();

        return $this->success(array_merge($baseResult, ['fields' => $fields]));
    }


    /**
     * @return array
     */
    public function delAction($data): array
    {
        $ids = $data['id'] ?? null;

        if (!$ids) {
            return $this->error("数据不能为空！");
        }

        // 断点测试
        // return $this->error("测试，删除成功，不进行数据库操作");

        $res = (new TbAdCost())->delete(["id" => $ids]);

        if ($res) {
            return $this->success([]);
        }
        else {
            return $this->error("删除失败");
        }
    }

    /**
     * update role
     * @return array
     */
    public function updateAction($data)
    {
        $ids = $data['id'] ?? null;
        $aid = $data['aid'] ?? '';

        if (!$ids) {
            return $this->error('数据不能为空！');
        }
        if (!$aid) {
            return $this->error('修改内容不能为空');
        }

        // 断点测试
        // return $this->error("测试，修改成功，不进行数据库操作");

        $data = [
            'aid'         => $aid,
            'update_time' => date('Y-m-d H:i:s'),
            'user_id'     => \Plus::$service->admin->getUserId(),
        ];

        $res = (new TbAdCost())->updateByWhere($data, ["id" => $ids]);

        if ($res) {
            return $this->success([]);
        }
        else {
            return $this->error("更新失败");
        }
    }

    /**
     * 通过csv/xls/xlsx导入
     *
     *
     * @return array
     */
    public function inputByFileAction($data): array
    {
        $input = \Plus::$app->request->getFileItem('file');

        if (empty($input)) {
            return $this->error('missing file');
        }

        $fileInfo = $input->save(CACHE_DIR . $input->getFilename());
        $filePath = $fileInfo->getPathname();

        if (empty($filePath)) {
            return $this->error('发生未知错误, 请稍后再试');
        }

        $againstFormats = [
            IOFactory::WRITER_CSV,
            IOFactory::WRITER_XLSX,
            IOFactory::WRITER_XLS,
        ];

        $reader    = IOFactory::load($filePath, 0, $againstFormats);
        $tableData = $reader->getActiveSheet()->toArray();

        if (empty($tableData)) {
            return $this->error("未成功读取文件数据, 请稍后再试");
        }

        // return $this->error('测试内容导入成功', $tableData);

        try {
            $title = Arr::pull($tableData, 0);
            $title = array_reverse(array_filter(array_reverse($title), fn($value) => $value !== null));

            if ($title != ['统计日期', '推广子渠道', '投放账户', '账号ID', '游戏原名', '游戏统计名', '包号', '展示', '点击', '返点前消耗金额', '返点后消耗金额', '类型']) {
                throw new \InvalidArgumentException('表头不规范，请联系开发排查表头是否新增字段或更改命名');
            }

            $titleLen = count($title);
            foreach ($tableData as &$item) {
                $item = array_filter($item, fn($foo) => $foo != null);
                if (empty($item)) {
                    $item = null;
                    continue;
                };

                $item = array_slice($item, 0, $titleLen);
                $item = array_combine($title, $item);
            }

            unset($item);
            $tableData = array_filter($tableData);
            (new CostInfoServ())->saveCostInfoForLive($tableData);
            $result = $this->success([]);
        }
        catch (\RuntimeException|\Exception $e) {
            $result = $this->error($e->getMessage(), ['trace' => $e->getTraceAsString()]);
        }
        catch (\Throwable $th) {
            $result = $this->error('未知错误', ['trace' => $th->getTraceAsString()]);
        }
        finally {
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        return $result;
    }

}
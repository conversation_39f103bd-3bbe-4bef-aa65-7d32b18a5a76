<?php

namespace app\ad_upload\channels;

/**
 * 星图(短视频)
 */
class XingtuVideo extends Toutiao2
{
    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->toutiaoUpload($info, 'ACTIVE', 'XINGTUVIDEO_PARAM_');
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->toutiaoUpload($info, 'REG', 'XINGTUVIDEO_PARAM_');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->toutiaoUpload($info, 'PAY', 'XINGTUVIDEO_PARAM_');
    }
}

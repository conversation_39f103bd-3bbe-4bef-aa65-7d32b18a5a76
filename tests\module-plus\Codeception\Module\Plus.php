<?php

namespace Codeception\Module;

use Codeception\Lib\Framework;
use Codeception\Lib\Connector\Plus as PlusConnector;

class Plus extends Framework
{
    protected $config = [
        'applicationClass'        => '',
        'applicationServiceClass' => '',
    ];

    protected $requiredFields = ['configFile', 'serviceFile'];

    public function _initialize()
    {
    }

    protected function configureClient(array $settings)
    {
        $settings['configFile'] = codecept_absolute_path($settings['configFile']);
        foreach ($settings as $key => $value) {
            if (property_exists($this->client, $key)) {
                $this->client->$key = $value;
            }
        }
        $this->client->resetApplication();
    }

    protected function recreateClient()
    {
        $this->client = new PlusConnector();
        $this->configureClient($this->config);
    }

    public function _before(\Codeception\TestCase $test)
    {
        $this->recreateClient();
        $this->client->startApp();
    }

    public function _after(\Codeception\TestCase $test)
    {
        $_SESSION = [];
        $_FILES   = [];
        $_GET     = [];
        $_POST    = [];
        $_COOKIE  = [];
        $_REQUEST = [];
        if ($this->client !== null) {
            $this->client->resetApplication();
        }
        parent::_after($test);
    }
}

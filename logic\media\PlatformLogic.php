<?php

namespace app\logic\media;

use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\UtilClosure;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\Media\MediaAccountServ;
use app\service\Media\MediaPlatformServ;

/**
 * @
 */
class PlatformLogic
{
    use ColumnsInteract;


    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     *
     * @return void
     * @throws \Exception
     */
    public function getInfo(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $sort = $this->orderReflect($sort);
//        $infoRe      = (new MediaPlatformServ())->getInfoList($params, $groups, $paginate, $sort);
        $infoRe = (new MediaAccountServ())->getInfoList($params, $groups, $paginate, $sort);

        $list      = &$infoRe['list'];
        $summary   = &$infoRe['summary'];
        $setMap    = (new BasicServ())->getMultiOptions(['media_platform_id', 'use_kind', 'business_ownership']);
        $replaceFn = UtilClosure::changeDataWithCollectFn(
            $setMap,
            [
                'media_platform_id'  => ['default' => '其他', 'target_key' => 'media_platform'],
                'business_ownership' => ['source_key' => 'business_ownership', 'target_key' => 'business_ownership_name'],
                'use_kind'           => ['source_key' => 'use_kind', 'target_key' => 'use_kind_name']
            ]
        );

        $resetGroupFn = $this->resetGroupsCols(
            $this->groupRelationMap($groups),
            $groups,
            [
                'tday', 'media_platform_id', 'media_platform',
                'business_ownership_name', 'business_ownership',
                'operations_manager', 'use_kind', 'use_kind_name'
            ]
        );

        foreach ($list as &$item) {
            $this->countValue($item);
            $replaceFn($item);
            $resetGroupFn($item);

        }
        unset($item);
        $this->countValue($summary);

        return $infoRe;
    }


    /**
     * @param $target
     *
     * @return void
     */
    protected function countValue(&$target)
    {
        // 普通任务发布成功率
        if (empty($target['normal_task_post']) || empty($target['normal_task_post_success'])) {
            $target['normal_task_post_success_rating'] = '0.00%';
        }
        else {
            $target['normal_task_post_success_rating'] =
                \round($target['normal_task_post_success'] / $target['normal_task_post'] * 100, 2) . '%';
        }

        if (empty($target['contri_task_post']) || empty($target['contri_task_post_success'])) {
            $target['contri_task_post_rate'] = '0.00%';
        }
        else {
            $target['contri_task_post_success_rating'] =
                \round($target['contri_task_post_success'] / $target['contri_task_post'] * 100, 2) . '%';
        }

        $target['all_video_task']         = ($target['normal_task_post'] ?? 0) + ($target['contri_task_post'] ?? 0);
        $target['all_video_task_success'] = ($target['normal_task_post_success'] ?? 0) + ($target['contri_task_post_success'] ?? 0);
    }

    /**
     * @param array $groups
     * @return array
     */
    public function groupRelationMap(array $groups): array
    {
        $relationMap = [
            'tday'                    => [],
            'media_platform'          => ['media_platform_id'],
            'business_ownership_name' => ['business_ownership'],
            'operations_manager'      => [],
            'use_kind_name'           => ['use_kind']
        ];

        return ColumnManager::matchRelationByGroups($relationMap, $groups);
    }

    /**
     * @param array $sort
     * @return array
     */
    protected function orderReflect(array $sort): array
    {
        // reverse - 反向
        // forward - 正向
        // 需要特殊处理的排序映射关系
        $reflectMap = [
            'media_platform'                  => 'media_platform_id',
            'use_kind'                        => 'use_kind_name',
            'all_video_task'                  => [
                'forward' => ['normal_task_post', 'contri_task_post'],
            ],
            'all_video_task_success'          => [
                'forward' => ['normal_task_post_success', 'contri_task_post_success']
            ],
            'normal_task_post_success_rating' => [
                'forward' => ['normal_task_post_success'],
                'reverse' => ['normal_task_post']
            ],
            'contri_task_post_success_rating' => [
                'forward' => ['contri_task_post_success'],
                'reverse' => ['contri_task_post'],
            ]
        ];


        foreach ($sort as $s => $o) {
            if (!isset($reflectMap[$s])) continue;

            $r = $reflectMap[$s];
            if (is_string($r)) {
                $sort[$r] = $o;
                unset($sort[$s]);
            }
            elseif (is_array($r)) {
                $reverse = $r['reverse'] ?? [];
                $forward = $r['forward'] ?? [];

                if (!empty($forward)) {
                    foreach (Arr::wrap($forward) as $f) {
                        $sort[$f] = $o;
                    }
                }

                if (!empty($reverse)) {
                    if ($o == 'DESC') {
                        $o = 'ASC';
                    }
                    else {
                        $o = 'DESC';
                    }

                    foreach (Arr::wrap($reverse) as $f) {
                        $sort[$f] = $o;
                    }
                }

                unset($sort[$s]);
            }
        }

        return $sort;
    }

}
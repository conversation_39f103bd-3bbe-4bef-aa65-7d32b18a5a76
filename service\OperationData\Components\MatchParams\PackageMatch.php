<?php

namespace app\service\OperationData\Components\MatchParams;

use app\extension\Support\Helpers\DBHelper\MatcherAbstract;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\SelectQuery;

class PackageMatch extends MatcherAbstract
{
    protected function matchFnList(): array
    {
        return [
            'tday' => $this->matchTDay(),
            'package_id'           => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'cp_game_id'           => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'game_id'              => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'app_show_id'          => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'platform_id'          => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'promotion_id'         => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'department_id'        => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'user_id'              => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'channel_id'           => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'channel_main_id'      => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
        ];
    }

    protected function matchTDay(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value) {
            if (is_array($value) && count($value) > 1) {
                [$start, $end] = $value;
                $qb->where($key, 'between', $start, $end);
            }
            else {
                if (is_array($value)) {
                    $value = $value[0];
                }

                $qb->where($key, $value);
            }
        };
    }


}
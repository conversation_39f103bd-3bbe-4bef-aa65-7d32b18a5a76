<?php
declare(ticks=1);

namespace app\ad_upload;

use app\ad_upload\tool\CommonFunc;
use app\ad_upload\tool\CustomProcess;
use app\util\BizConfig;

/**
 * 同步订单 到  支付上报日志
 * 每分钟运行一次，同步前30分钟的数据
 * <AUTHOR>
 */
class SyncOrder extends CustomProcess
{
    /**
     * 开始时间
     * @var string
     */
    public $timeBegin = '';
    /**
     * 结束时间
     * @var string
     */
    public $timeEnd = '';
    /**
     * 全量表
     * @var string
     */
    private $tbName = 'bigdata_dwd.dwd_reported_paid_platform_log';
    /**
     * 临时表
     * @var string
     */
    private $tmpTable = 'bigdata_tmp.dwd_reported_paid_platform_log_tmp';

    /**
     * run
     * @return void
     */
    public function run()
    {
        if (empty($this->timeBegin)) {
            $this->timeBegin = date('Y-m-d H:i:s', strtotime('-30 minutes'));
        }
        if (empty($this->timeEnd)) {
            $this->timeEnd = date('Y-m-d H:i:s');
        }
        $this->syncAllOrder();
        $this->syncNoReportOrder();
        if (APP_EVN != 'DEV') { //测试环境不执行
            $this->syncInterceptOrder();
            $this->syncIncreaseOrder();
            $this->syncSdkOrder();
        }
        $this->updateNotAdOrder();
    }


    /**
     * 同步所有订单， 不存在则插入
     * @return void
     */
    protected function syncAllOrder()
    {
        $sql       = "SELECT t1.order_id,
                   t1.pay_result,
                   t1.money as actually_money,
                   t1.cp_game_id,
                   t1.game_id,
                   t1.package_id,
                   coalesce(t2.oaid, '') as oaid,
                   t1.device_key,
                   t1.pay_time,
                   t1.core_account,
                   t1.source_id,
                   now() as create_time
            FROM (
                 SELECT *
                 FROM ddc_platform.dwd_sdk_user_payment
                 WHERE pay_result = 1
                   AND pay_time BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
                 ) t1
            LEFT JOIN (
                      SELECT *
                      FROM origin_platform.tb_sdk_user_payment
                      WHERE pay_result = 1
                        AND pay_time BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
                      ) t2
            ON t1.order_id = t2.order_id";
        $data      = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $insertSql = "INSERT INTO {$this->tmpTable} (
                     `order_id`, 
                     `dt`, 
                     `pay_result`,
                     `actually_money`, 
                     `cp_game_id`, 
                     `game_id`, 
                     `package_id`, 
                     `oaid`, 
                     `device_key`, 
                     `pay_time`, 
                     `core_account`, 
                     `source_id`, 
                     `create_time`,
                     `no_reported_origin`
                     ) VALUES";
        $values    = [];
        foreach ($data as $v) {
            $v        = array_change_key_case($v);
            $values[] = sprintf(
                "('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')",
                $v['order_id'],
                date('Y-m-d', strtotime($v['pay_time'])),
                $v['pay_result'],
                $v['actually_money'],
                $v['cp_game_id'],
                $v['game_id'],
                $v['package_id'],
                $v['oaid'],
                $v['device_key'],
                $v['pay_time'],
                $v['core_account'],
                $v['source_id'],
                $v['create_time'],
                'sync'
            );
        }// end foreach()
        if (!empty($values)) {
            //先同步到临时表
            $insertSql .= implode(",\n", $values);
            \Plus::$app->doris_entrance->exec($insertSql);
            \Plus::$app->doris_entrance2->exec($insertSql);
            //再同步到正式表,排除已经存在的订单
            $sql = "insert into {$this->tbName}
            select a1.*
            from {$this->tbName} a2
                     right join {$this->tmpTable} a1 on a1.order_id=a2.order_id
            where a2.order_id is null";
            \Plus::$app->doris_entrance->exec($sql);
            \Plus::$app->doris_entrance2->exec($sql);
            //清空临时表
            \Plus::$app->doris_entrance->exec("TRUNCATE TABLE $this->tmpTable");
            \Plus::$app->doris_entrance2->exec("TRUNCATE TABLE $this->tmpTable");
        }
        echo '同步所有订单完成:' . count($values), PHP_EOL;
    }


    /**
     * 同步不需要上报订单，强制插入
     * 配置中心/渠道媒体配置/出包子渠道配置 标签：广告新增用户（961）
     *
     * @return void
     */
    protected function syncNoReportOrder()
    {
        $sql    = "SELECT
                  a1.order_id,
                  a1.money,
                  t3.channel_id,
                  a1.pay_time,
                  DATE(NOW()) AS dt,
                  a3.channel_code,
                  a1.package_id,
                  a2.cp_game_id,
                  a2.game_id,
                  a1.oaid,
                  a1.core_account,
                  t3.source_id,
                  t2.device_key
                FROM
                  (
                    SELECT
                      * 
                    FROM
                      origin_platform.tb_sdk_user_payment 
                    WHERE
                      pay_result = 1 
                      AND pay_time BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}' 
                  AND NOT PACKAGE_ID LIKE '%0099') a1 -- 非ios包
                   -- 非小程序包号
                  JOIN (
                    SELECT
                      * 
                    FROM
                      base_conf_platform.tb_package_detail_conf 
                    WHERE
                      channel_id NOT IN (
                    SELECT DATA_ID FROM base_conf_platform.biz_tags WHERE TAG_ID = 961 AND TABLE_NAME = 'app_channel')
                    ) a2 ON a1.package_id = a2.package_id
                  LEFT JOIN base_conf_platform.tb_base_channel_conf a3 ON a2.channel_id = a3.channel_id
                  LEFT JOIN ddc_platform.dwd_sdk_user_payment t2 ON a1.order_id = t2.order_id
                  LEFT JOIN ddc_platform.dwd_sdk_adsource_game t3 ON t2.source_id = t3.source_id";
        $orders = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        if (empty($orders)) {
            echo '没有数据', PHP_EOL;
            return;
        }
        $packages      = array_unique(array_column($orders, 'PACKAGE_ID'));
        $biz           = new BizConfig();
        $mediaParams   = $biz->getMediaParams($packages);
        $mediaPackages = []; //有媒体参数的包号
        if (isset($mediaParams['data'])) {
            foreach ($mediaParams['data'] as $k => $v) {
                if ($v['find']) {
                    $mediaPackages[] = $k;
                }
            }
        }
        $insertSql = "INSERT INTO {$this->tmpTable}(
                  order_id,
                  pay_result,
                  reported_status,
                  actually_money,
                  reported_money,
                  cp_game_id,
                  game_id,
                  package_id,
                  channel_id,
                  oaid,
                  pay_time,
                  dt,
                  time,
                  channel_code,
                  core_account,
                  source_dimension, 
                  reported_behavior, 
                  no_reported_origin,
                  source_id,
                  device_key
                  )VALUES";
        $value     = [];
        foreach ($orders as $order) {
            $order = array_change_key_case($order);
            if (in_array($order['package_id'], $mediaPackages)) {
                $order['reported_status']    = $order['money'] > 0 ? 1 : 0; //订单金额大于0才上报
                $order['actually_money']     = $order['money'] ?? 0;
                $order['reported_money']     = $order['money'] ?? 0;
                $order['source_dimension']   = 1;
                $order['reported_behavior']  = 0;
                $order['no_reported_origin'] = $order['money'] > 0 ? "sync2" : '不上报原因：actually_money金额为0';
            } else {
                $order['reported_status']    = 0;
                $order['actually_money']     = 0;
                $order['reported_money']     = 0;
                $order['source_dimension']   = 1;
                $order['reported_behavior']  = 0;
                $order['no_reported_origin'] = "不上报原因：充值统计名，没配置媒体参数，归因统计名不是小游戏/ios包，不上报";
            }

            $value[] = sprintf(
                "('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')",
                $order['order_id'],
                1,
                $order['reported_status'],
                $order['actually_money'],
                $order['reported_money'],
                $order['cp_game_id'],
                $order['game_id'],
                $order['package_id'],
                $order['channel_id'],
                $order['oaid'],
                $order['pay_time'],
                $order['dt'],
                $order['pay_time'],
                $order['channel_code'],
                $order['core_account'],
                $order['source_dimension'],
                $order['reported_behavior'],
                $order['no_reported_origin'],
                $order['source_id'],
                $order['device_key'],
            );
        }// end foreach()
        //先同步到临时表
        \Plus::$app->doris_entrance->exec($insertSql . implode(",\n", $value));
        \Plus::$app->doris_entrance2->exec($insertSql . implode(",\n", $value));
        //再同步到正式表,排除已经存在的订单、有归因的订单
        $sql = "insert into {$this->tbName}
            select a1.*
            from {$this->tbName} a2
                     right join {$this->tmpTable} a1 on a1.order_id=a2.order_id
            where a2.order_id is null or a2.source_dimension=''";
        \Plus::$app->doris_entrance->exec($sql);
        \Plus::$app->doris_entrance2->exec($sql);
        //清空临时表
        \Plus::$app->doris_entrance->exec("TRUNCATE TABLE $this->tmpTable");
        \Plus::$app->doris_entrance2->exec("TRUNCATE TABLE $this->tmpTable");
    }

    /**
     * 同步sdk客户端扣量订单, 强制插入
     * @return void
     */
    protected function syncInterceptOrder()
    {

        $sql = "INSERT INTO {$this->tbName} (
    order_id,
    reported_status,
    actually_money,
    reported_money,
    channel_id,
    `time`,
    dt,
    channel_code,
    source_dimension,
    reported_behavior,
    reported_rule_id,
    update_time,
    pay_time,
    pay_result,
    source_id,
    cp_game_id,
    game_id,
    package_id,
    device_key,
    core_account,
    no_reported_origin
)
SELECT t_intercept.order_id_actually AS order_id,
       if(t_intercept.report_type = 1,0,1)  AS reported_status,
       t1.money AS actually_money,
       -- 当report_type=1时返回0，否则返回原值
       IF(t_intercept.report_type = 1, 0, t_intercept.money_report) AS reported_money,
       coalesce(t1.CHANNEL_ID, 0) AS channel_id,
       t_intercept.report_time AS `time`,
       date(t_intercept.report_time) AS dt,
       coalesce(t3.channel_code, '') AS channel_code,
       1 AS source_dimension,
       t_intercept.report_type AS reported_behavior,
       t_intercept.report_rule_id AS reported_rule_id,
       now(),
       t1.pay_time AS pay_time,
       t1.pay_result as pay_result,
       t4.source_id,
       t4.cp_game_id,
       t4.game_id,
       t4.package_id,
       t4.device_key,
       t4.core_account,
       IF(t_intercept.report_type = 1, '命中扣减次数规则', '') AS no_reported_origin
FROM (
     SELECT *
     FROM origin_platform.tb_sdk_user_payment
     WHERE pay_result = 1
     ) t1
RIGHT JOIN (
            SELECT *
            FROM bigdata_dwd.dwd_sdk_payment_intercept
            WHERE create_time BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
              -- 非ios包,非小程序包
              AND !(ends_with(PACKAGE_ID, '0099')
              or package_id in (SELECT package_id
                                 FROM base_conf_platform.tb_base_package_conf
                                 WHERE CHANNEL_ID IN (6822,5329,6447,6695,5327)
                                 )
                    )
           ) t_intercept
ON t1.ORDER_ID = t_intercept.order_id_actually
LEFT JOIN base_conf_platform.tb_base_channel_conf t3
ON t1.CHANNEL_ID = t3.CHANNEL_ID
LEFT JOIN ddc_platform.dwd_sdk_user_payment AS t4 ON t4.order_id=t1.order_id";
        \Plus::$app->doris_entrance->exec($sql);
        \Plus::$app->doris_entrance2->exec($sql);
    }


    /**
     * 同步sdk客户端上报订单, 强制插入
     * @return void
     */
    protected function syncSdkOrder()
    {

        $sql = "INSERT INTO {$this->tbName} (
    order_id,
    reported_status,
    actually_money,
    reported_money,
    channel_id,
    `time`,
    dt,
    channel_code,
    source_dimension,
    reported_behavior,
    reported_rule_id,
    reported_behavior_rule,
    update_time,
    pay_time,
    pay_result,
    source_id,
    cp_game_id,
    game_id,
    package_id,
    device_key,
    core_account,
    no_reported_origin
)
SELECT t_intercept.order_id AS order_id,
       IF(t_intercept.report_type = 1,0,1)  AS reported_status,
       t1.money AS actually_money,
       -- 当report_type=1时返回0，否则返回原值
       IF(t_intercept.report_type = 1, 0, t_intercept.money_report) AS reported_money,
       COALESCE(t1.CHANNEL_ID, 0) AS channel_id,
       t_intercept.time AS `time`,
       DATE(t_intercept.time) AS dt,
       COALESCE(t3.channel_code, '') AS channel_code,
       1 AS source_dimension,
       t_intercept.report_type AS reported_behavior,
       t_intercept.report_rule_id AS reported_rule_id,
       CONCAT('{\"rule\":{\"conditionName\":\"', t_intercept.report_rule_name, '\"}}') AS reported_behavior_rule,
       NOW(),
       t1.pay_time AS pay_time,
       t1.pay_result AS pay_result,
       t4.source_id,
       t4.cp_game_id,
       t4.game_id,
       t4.package_id,
       t4.device_key,
       t4.core_account,
       IF((t_intercept.report_type = 1 and report_reason = ''), '命中扣减次数规则', t_intercept.report_reason) AS no_reported_origin
FROM (
     SELECT *
     FROM origin_platform.tb_sdk_user_payment
     WHERE pay_result = 1
     ) t1
RIGHT JOIN (
            SELECT *
            FROM bigdata_dwd.dwd_sdk_client_pay
            WHERE create_time BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
              -- 非ios包,非小程序包
              AND !(ends_with(PACKAGE_ID, '0099')
              OR package_id IN (SELECT package_id
                                 FROM base_conf_platform.tb_base_package_conf
                                 WHERE CHANNEL_ID IN (6822,5329,6447,6695,5327)
                                 )
                    )
           ) t_intercept
ON t1.ORDER_ID = t_intercept.order_id
LEFT JOIN base_conf_platform.tb_base_channel_conf t3
ON t1.CHANNEL_ID = t3.CHANNEL_ID
LEFT JOIN ddc_platform.dwd_sdk_user_payment AS t4 ON t4.order_id=t1.order_id
WHERE t1.pay_result IS NOT NULL";
        \Plus::$app->doris_entrance->exec($sql);
        \Plus::$app->doris_entrance2->exec($sql);
    }


    /**
     * 同步sdk客户端增量订单, 强制插入
     * @return void
     */
    protected function syncIncreaseOrder()
    {
        $sql = "INSERT INTO {$this->tbName} (
    order_id,
    reported_status,
    reported_money,
    channel_id,
    `time`,
    dt,
    channel_code,
    source_dimension,
    reported_behavior,
    reported_rule_id,
    update_time,
    pay_time,
    pay_result
)
SELECT t_increase.order_id                               AS order_id,
       1                                                 AS reported_status,
       json_extract_string(order_detail_json,'$.money')  AS reported_money,
       coalesce(t1.CHANNEL_ID, 0)                        AS channel_id,
       t_increase.time                                   AS `time`,
       date(t_increase.time)                             AS dt,
       coalesce(t3.channel_code, '')                     AS channel_code,
       1                                                 AS source_dimension,
       3                                                 AS reported_behavior,
       coalesce(json_extract_int(order_detail_json,'$.rule_id'),0)    AS reported_rule_id,
       now(),
       t1.pay_time AS pay_time,
       t1.pay_result as pay_result
FROM (
     SELECT *
     FROM origin_platform.tb_sdk_user_payment
     WHERE pay_result = 1
     ) t1
RIGHT JOIN (
            SELECT *
            FROM bigdata_dwd.dwd_sdk_user_payment_increase
            WHERE create_time BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
              -- 非ios包,非小程序包
              AND !(ends_with(PACKAGE_ID, '0099')
              or package_id in (SELECT package_id
                                 FROM base_conf_platform.tb_base_package_conf
                                 WHERE CHANNEL_ID IN (6822,5329,6447,6695,5327)
                                 )
                    )
           ) t_increase
ON t1.ORDER_ID = t_increase.order_id
LEFT JOIN base_conf_platform.tb_base_channel_conf t3
ON t1.CHANNEL_ID = t3.CHANNEL_ID
";
        \Plus::$app->doris_entrance->exec($sql);
        \Plus::$app->doris_entrance2->exec($sql);
    }

    /**
     * 更新非广告订单
     * 超过6小时的不上报订单 标记为 ：自然量
     * @return void
     */
    protected function updateNotAdOrder()
    {
        $this->timeBegin = date('Y-m-d H:i:s', strtotime('-7 hours', strtotime($this->timeEnd)));
        $this->timeEnd   = date('Y-m-d H:i:s', strtotime('-6 hours', strtotime($this->timeEnd)));

        //$dt = date('Y-m-d', strtotime('-1 day'));

        $sql = "UPDATE
                      `bigdata_dwd`.`dwd_reported_paid_platform_log`
                    SET
                      no_reported_origin = '不上报原因：自然量'
                    WHERE
                       pay_time BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
                      AND reported_status = 0
                      AND (
                        no_reported_origin = ''
                        OR no_reported_origin LIKE 'sync%'
                      )";

        \Plus::$app->doris_entrance->exec($sql);
        \Plus::$app->doris_entrance2->exec($sql);
    }
}

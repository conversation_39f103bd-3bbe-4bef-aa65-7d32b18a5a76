<?php

namespace app\logic\operator;


use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\OperationData\FirstLoginDashProvider;
use Smarty\Exception;


class FirstLoginDashLogic
{
    /**
     * @throws Exception
     */
    public function tableBase(
        array $params, array $groups = [], array $paginate = [], array $sort = [], array $columns = [], int $mode = FirstLoginDashProvider::RESULT_ALL
    ): array
    {
        $timeDimension        = (int)$params['range_date_dimension'] ?? 2;
        $today                = date('Y-m-d');
        $params['range_date'] = [
            $params['range_date_start'] ?? $today,
            $params['range_date_end'] ?? $today,
        ];
        sort($params['range_date']);
        unset($params['range_date_start'], $params['range_date_end']);
        $provider = new FirstLoginDashProvider();

        if ($timeDimension === 4) {
            foreach ($params['range_date'] as &$dd) {
                $dd = date('Ym', strtotime($dd));
            }
        }
        $fillGroups = ColumnManager::groupOperatorRelation($groups);
        $result     = $provider->getData($params, $fillGroups, $paginate, $sort, $columns, $mode);

        if (!empty($result['list'])) {
            $list = $list = &$result['list'];

            foreach ($list as &$item) {
                foreach ($item as $k => &$foo) {
                    if (str_contains($k, '_percent') || $k == 'fistlogin_real_name' || $k == 'device_conversion_rate') {
                        $foo .= '%';
                    }
                }
            }
        }

        if (!empty($result['summary'])) {
            unset($chill);
            foreach ($result['summary'] as $kk => &$chill) {
                if (str_contains($kk, '_percent') || $kk == 'fistlogin_real_name' || $kk == 'device_conversion_rate') {
                    $chill .= '%';
                }
            }
        }

        return $result;
    }
}
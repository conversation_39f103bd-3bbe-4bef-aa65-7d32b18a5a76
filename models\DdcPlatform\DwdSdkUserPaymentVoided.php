<?php

namespace app\models\DdcPlatform;

use Plus\MVC\Model\ActiveRecord;


/**
 *
 * 退款表
 * @property int       ID
 * @property string    ORDER_ID
 * @property string    VOIDED_TIME
 * @property string    VOIDED_TYPE
 * */
class DwdSdkUserPaymentVoided extends ActiveRecord
{
    protected $_primaryKey = 'id';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->ddc_platform;
        parent::__construct($data);
    }
}
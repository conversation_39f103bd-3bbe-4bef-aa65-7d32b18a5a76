<?php

namespace app\service\Tool;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\General\Helpers\DataSpyTable;
use Spiral\Database\Database;
use Spiral\Database\Injection\Parameter;

class IndicatorReferenceServ
{

    /**
     * @param array $data
     * @return mixed
     * @throws \Throwable
     */
    public function insertOrUpdateOne(array $data)
    {
        $db = $this->getConnect();

        return $db->transaction(function (Database $cdb) use ($data) {
            if (!empty($data['id'])) {
                $updateData = array_intersect_key($data, ['name' => '', 'tags' => '', 'operator' => '']);
                $id         = $data['id'];

                $cdb
                    ->update(
                        DataSpyTable::CONF_INDICATOR_REFERENCE,
                        $updateData
                    )
                    ->where('id', $data['id'])
                    ->run();

                if (!empty($data['children'])) {
                    $cdb->delete(DataSpyTable::CONF_INDICATOR_REFERENCE_SECONDARY, ['parent_id' => $data['id']])->run();

                    foreach ($data['children'] as $chill) {
                        $chill['parent_id'] = $id;
                        $cdb->table(DataSpyTable::CONF_INDICATOR_REFERENCE_SECONDARY)->insertOne($chill);
                    }
                }
            }
            else {

                $count = $cdb->select()->from(DataSpyTable::CONF_INDICATOR_REFERENCE)->where('name', $data['name'])->count();
                if ($count > 0) {
                    throw new \Exception('命名重复');
                }
                $d        = array_intersect_key($data, ['name' => '', 'tags' => '', 'operator' => '']);
                $parentId = $cdb->table(DataSpyTable::CONF_INDICATOR_REFERENCE)->insertOne($d);

                if (!empty($data['children'])) {
                    $cdb->delete(DataSpyTable::CONF_INDICATOR_REFERENCE_SECONDARY, ['parent_id' => $parentId])->run();

                    foreach ($data['children'] as $chill) {
                        $chill['parent_id'] = $parentId;
                        $cdb->table(DataSpyTable::CONF_INDICATOR_REFERENCE_SECONDARY)->insertOne($chill);
                    }
                }
            }
        });

    }

    /**
     * @param array $paginate 分页
     * @return array
     */
    public function getAllIndicator(array $paginate = []): array
    {
        $db  = $this->getConnect();
        $stm = $db
            ->table(DataSpyTable::CONF_INDICATOR_REFERENCE)
            ->select()
            ->columns(['id as id', 'name as name', 'tags as tags', 'operator as operator', 'create_at as create_at', 'update_at as update_at'])
            ->orderBy(['id' => 'DESC', 'update_at' => 'DESC']);

        $totalStm = clone $stm;

        if (!empty($paginate)) {
            $page     = Arr::get($paginate, 'page', 1);
            $pageSize = Arr::get($paginate, 'page_size', 100);
            $stm->limit($pageSize)->offset(($page - 1) * $pageSize);
        }

        return [
            'list'  => $stm->fetchAll(),
            'total' => $totalStm->count(),
        ];
    }

    /**
     * @param array $params
     * @param array $paginate
     * @return array
     */
    public function getInfoWithParentId(array $params = [], array $paginate = []): array
    {
        $db  = $this->getConnect();
        $stm = $db
            ->table(DataSpyTable::CONF_INDICATOR_REFERENCE_SECONDARY)
            ->select()
            ->columns([
                'id as id',
                'indicator_title as indicator_title',
                'standard as standard',
                'indicator_index as indicator_index',
                'parent_id as parent_id',
                'create_at as create_at',
                'update_at as update_at'
            ]);

        if (!empty($params['parent_ids'])) {
            $stm->where('parent_id', new Parameter(Arr::wrap($params['parent_ids'])));
        }

        $totalStm = clone $stm;

        if (!empty($paginate)) {
            $page     = Arr::get($paginate, 'page', 1);
            $pageSize = Arr::get($paginate, 'page_size', 100);
            $stm->limit($pageSize)->offset(($page - 1) * $pageSize);
        }

        return [
            'list'  => $stm->fetchAll(),
            'total' => $totalStm->count(),
        ];
    }

    /**
     * @throws \Throwable
     */
    public function deleteIndicatorWithId(array $ids)
    {
        $db = $this->getConnect();

        return $db->transaction(function (Database $cdb) use ($ids) {
            $cdb->delete(DataSpyTable::CONF_INDICATOR_REFERENCE, ['id' => new Parameter(Arr::wrap($ids))])->run();
            $cdb->delete(DataSpyTable::CONF_INDICATOR_REFERENCE_SECONDARY, ['parent_id' => new Parameter(Arr::wrap($ids))])->run();
        });
    }


    /**
     * @return Database|\Spiral\Database\DatabaseInterface
     */
    private function getConnect()
    {
        return FakeDB::connection('dataspy');
    }

}
<?php


namespace app\extension;

use app\extension\FakeDB\FakeDB;
use app\extension\Faker\FakeRabbit\FakeRabbit;
use app\util\SSDB\SSDB;
use framework\rabbit\RabbitQueue;
use Nsq\NsqQueue;
use Plus\Cache\RedisCache;
use Plus\SQL\Db;
use Redis;

/**
 * 框架扩展类
 * Class Application
 *
 * @property Db $db                    om数据库
 * @property Db $ddc_platform          ddc数据库
 * @property Db $adp_platform          智推数据库
 * @property Db $py_platform           业务py库
 * @property Db $base_conf_platform    配置数据库
 * @property Db $origin_platform       origin数据库
 * @property Db $dataspy               dataspy数据库
 * @property Db $ddc_doris             doris数据库
 * @property Db $doris_entrance        doris数据库
 * @property Db $base_conf_platform_doris        doris.base_conf_platform_doris数据库
 * @property RedisCache|\Redis $redis                 缓存
 * @property Redis $redis_ext             缓存(使用\Redis)
 * @property SSDB $ssdb                  缓存
 * @property Context $context               上下文
 * @property NsqQueue $queue                 队列
 * @property FakeRabbit $universal_mq          MQ服务
 * @property RabbitQueue $export_mq          MQ服务
 * @property RedisCache $redis82
 * @property SqlTemplates $sqlTemplates
 * @property Db $base_conf_platform_doris_new
 * @package app\extension
 * <AUTHOR>
 */
class Application extends \Plus\MVC\Application
{
    public function __construct($config = [])
    {
        FakeDB::initialize($config);
        parent::__construct($config);
    }
}

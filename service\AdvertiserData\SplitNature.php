<?php

namespace app\service\AdvertiserData;

use app\models\AdpPlatform\TbAdpOauth;
use app\models\baseConfPlatform\TbPackageDetailConf;
use app\models\DataSpy\AdminUser;
use app\models\DataSpy\TbAdSvlinkConf;
use app\models\DdcPlatform\DwsSvkeyAdBaseDaily;
use app\models\DdcPlatform\DwsSvKeyAdSplitDaily;
use Plus\SQL\Db;
use Plus\Util\Pagination;

/**
 * @SplitNature
 *             拆分自然量聚合查询
 *
 */
class SplitNature
{
    protected const DB_PLATFORM = 'ddc_platform';


    /**
     * @param array           $wheres
     * @param array           $column
     * @param array           $group
     * @param Pagination|null $pagination
     * @return array|null
     * @todo 抽象where条件拼接
     * @todo 待优化功能
     *
     */
    public function list(
        array      $wheres = [],
        array      $column = [],
        array      $group = [],
        Pagination $pagination = null
    ): ?array
    {
        $dbPlatform = static::DB_PLATFORM;
        /**
         * @var Db $stm
         */
        $stm = \Plus::$app->$dbPlatform;
        $tDay = $wheres['TDAY'];
        $splitTable     = 'ddc_platform.dws_svkey_ad_split_daily';
        $packageTable   = 'base_conf_platform.' . (new TbPackageDetailConf())->getTableName();
        $svLinkTable    = 'dataspy.' . (new TbAdSvlinkConf())->getTableName();
        $adpOauthTable  = 'adp_platform.' . (new TbAdpOauth())->getTableName();
        $adminUserTable = 'dataspy.' . (new AdminUser())->getTableName();
        $updateTime = date("Y-m-d",strtotime(date('Y-m-t',strtotime($tDay[0])))+24*3600);
        if(date("m",strtotime($tDay[0])) == date("m")){
            $updateTime = $tDay[0];
        }
        $whereR = [
            "t1.UPDATE_TIME >= '".$updateTime."'",
            't1.IS_NATURAL = 1'
            //'t1.SV_KEY != 0',
        ];

        $groupR = [];

        $mainTable = "
        {$splitTable} as t1
        join {$packageTable} as power on t1.PACKAGE_ID = power.PACKAGE_ID
        left join {$svLinkTable} as tasc on t1.SV_KEY = tasc.ID
        left join {$adpOauthTable} as t3 on t1.ACCOUNT_ID = t3.ADVERTISER_ID
        left join {$adminUserTable} as t4 on tasc.USER_ID = t4.ID
        ";

        // 以下部分暂时写死
        $column = [
            'SUM(t1.PAY_MONEY) as total_amount',
        ];

        $wheres = array_filter($wheres);

        if (!empty($wheres['PACKAGE_ID'])) {
            $packageIds = implode(',', $wheres['PACKAGE_ID']);
            $column[]   = 't1.PACKAGE_ID as package_id';
            $whereR[]   = "t1.PACKAGE_ID IN ({$packageIds})";
            $groupR[]   = 'package_id';
        }

        if (!empty($wheres['DEPARTMENT_ID'])) {
            $departmentIds = implode(',', $wheres['DEPARTMENT_ID']);
            $column[]      = 'IFNULL(t4.DEPARTMENT_ID, 0) as department';
            $whereR[]      = "IFNULL(t4.DEPARTMENT_ID, 0) IN ({$departmentIds} )";
            $groupR[]      = 'department';
        }

        if (!empty($wheres['TDAY'])) {
            sort($tDay);
            $whereR[] = "t1.TDAY between '{$tDay[0]}' and '{$tDay[1]}'";
        }


        $whereString  = '';
        $columnString = '';
        $groupString  = '';

        if (!empty($whereR)) {
            $whereString = ' where ' . implode(' and ', $whereR);
        }

        if (!empty($groupR)) {
            $groupString = ' group by ' . implode(',', $groupR);
        }

        if (!empty($column)) {
            $columnString = implode(',', $column);
        }

        $sql = "select {$columnString} from {$mainTable} {$whereString} {$groupString}";

        return $stm->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }
}
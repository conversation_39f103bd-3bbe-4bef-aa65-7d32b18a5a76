#!/usr/bin/python3
# coding=utf-8
APP_ENV = "pro"

#正式环境
proConfig = {
    "master":{
        "host": "master.spy.910app.com",
        "user": "project_spy",
        "password": "1DQk0i42js%$7ZXCEyX6",
    },
    "slave":{
        "host": "slave.spy.910app.com",
        "user": "project_spy",
        "password": "1DQk0i42js%$7ZXCEyX6",
    }
}

#开发环境
devConfig = {
    "master": {
        "host": "127.0.0.1",
        "user": "root",
        "password": "qq123456",
    },
    "slave":{
        "host": "127.0.0.1",
        "user": "root",
        "password": "qq123456",
    }
}

#根据环境获取配置
def getDb(database,mode="slave"):
    config =  switch_db_case(mode=mode)
    config["database"] = database
    return config

def switch_db_case(mode="default"):
    switcher = {
        "dev": devConfig[mode],
        "pro": proConfig[mode],
        "default":devConfig[mode],
    }
    return switcher[APP_ENV]


<?php

namespace app\service\AdvertiseLive;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\Zakia;
use app\service\AdvertiseLive\Components\Matcher\AdLiveMatch;
use app\service\AdvertiseLive\Helpers\TableConst;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\General\GeneralOptionServ;
use Spiral\Database\Database;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class LiveOnDutyServ
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     *
     * @return array
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $db = $this->getConn();
        $qb = $db->select()->from(TableConst::CONF_LIVE_ON_DUTY . ' as t1');

        $qb
            ->innerJoin('base_conf_platform.tb_package_detail_conf', 'power')
            ->on(['power.package_id' => 't1.package_id'])
            ->leftJoin('base_conf_platform.tb_ad_live_account_conf', 'live_account')
            ->on(['t1.live_account_id' => 'live_account.account_id'])
            ->leftJoin(TableConst::CONF_LIVE_ANCHOR, 'anchor_info')
            ->on(['t1.anchor_id' => 'anchor_info.id'])
            ->leftJoin('dataspy.admin_user', 'admin_user')
            ->on(['admin_user.id' => 't1.operator_id']);

        $matcher = new AdLiveMatch([
            'live_platform'   => 'live_account.type',
            'live_team'       => 'anchor_info.team_properties',
            'docking_partner' => 'anchor_info.docking_partner',
            'package_id'      => 't1.package_id',
            'operator_id'     => 't1.operator_id',
            'live_account_id' => 'live_account.account_id',
        ]);

        $matcher->exec($qb, $params);

        $qb->columns([
            new Fragment("DATE(start_time) as live_date"),
            new Fragment("DATE(start_time) as tday"),
            new Fragment("CONCAT(TIME_FORMAT(start_time, '%H:%i') , '~' , TIME_FORMAT(end_time,  '%H:%i')) as time_range"),
            new Fragment("CONCAT(TIME_FORMAT(start_time, '%H:%i') , '~' , TIME_FORMAT(end_time,  '%H:%i')) as range_time"),
            't1.start_time as start_time',
            't1.end_time as end_time',
            'anchor_id as anchor_id',
            'anchor_name as anchor_name',
            'docking_partner as docking_partner',
            'assistant_id as assistant_id',
            'operation_account_id as operation_account_id',
            't1.package_id as package_id',
            'power.cp_game_id as cp_game_id',
            'power.game_id as game_id',
            't1.operator_id as operator_id',
            't1.create_time as create_time',
            'live_account.account as live_account_name',
            'live_account.account as account_name',
            'live_account.account_id as live_account',
            't1.live_account_id as live_account_id',
            't1.live_account_id as account_id',
            'power.channel_id as channel_id',
            'admin_user.real_name as operator',
            'live_account.type as live_platform',
            'live_account.collaborative_code as collaborative_code',
            'team_properties as live_team',
            't1.id as id',
        ]);

        $noPageQb = clone $qb;

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $pageSize] = $paginate;
            $qb->limit($pageSize)->offset(($page - 1) * $pageSize);
        }

        $total = (clone $noPageQb)->columns(['1 as row'])->count();

        return [
            'list'    => (clone $qb)->fetchAll(),
            'total'   => $total,
            'duty_qb' => clone $qb,
        ];
    }

    /**
     * @param $data
     *
     * @return mixed
     * @throws \Throwable
     */
    public function insert($data)
    {
        $db = $this->getConn();

        if (
            empty($data['package_id'])
            || empty($data['live_account_id'])
            || empty($data['anchor_id'])
        ) {
            throw new \InvalidArgumentException('参数缺失');
        }

        if (empty($data['start_time']) || empty($data['end_time'])) {
            throw new \InvalidArgumentException('参数缺失:开始时间/结束时间');
        }
        else {
            // 判断时间选择范围是否超过了当天
            try {
                $startTime = new \DateTime($data['start_time']);
                $endTime   = new \DateTime($data['end_time']);
            }
            catch (\Throwable $e) {
                throw new \InvalidArgumentException('时间格式不规范');
            }

            if ($startTime > $endTime) {
                throw  new \InvalidArgumentException('选择时间范围异常');
            }

//            $timeDiff = (int)$startTime->diff($endTime)->format('%R%a');
//
//            if ($timeDiff != 0) {
//                throw new \InvalidArgumentException('选择时间范围不能超过当天');
//            }
        }

        // 检查包号是否存在
        if ((new GeneralOptionServ())->checkPackages($data['package_id']) == 0) {
            throw new \InvalidArgumentException('该包号不存在');
        }

        return $db->transaction(function (Database $ndb) use ($data) {
            $startTime  = $data['start_time'];
            $endTime    = $data['end_time'];
            $baseQb     = $ndb->table(TableConst::CONF_LIVE_ON_DUTY);
            $accountMap = $ndb->table(TableConst::CONF_LIVE_ACCOUNT)->select('account_id', 'account')->fetchAll();
            $accountMap = array_column($accountMap, 'account', 'account_id');

            [
                'package_id'      => $packageId,
                'live_account_id' => $liveAccount,
                'anchor_id'       => $anchorId,
            ] = $data;

            $checkColumns = [
                new Fragment("SUM(IF(live_account_id = '{$liveAccount}', 1, 0)) > 0 as is_duplicate_account"),
                new Fragment("SUM(IF(anchor_id = {$anchorId}, 1, 0)) > 0 as is_duplicate_anchor"),
                new Fragment("SUM(IF(package_id = {$packageId}, 1, 0)) > 0 as is_duplicate_package"),
            ];

//            if (!empty($data['assistant_id'])) {
//                $checkColumns[] = new Fragment("SUM(IF(assistant_id = {$data['assistant_id']}, 1, 0)) > 0 as is_duplicate_assistant");
//            }
//
//            if (!empty($data['operation_account_id'])) {
//                $checkColumns[] = new Fragment("SUM(IF(operation_account_id = {$data['operation_account_id']}, 1, 0)) > 0 as is_duplicate_operation_account");
//            }

            // 查询是否有已经重复的数据
            $existInfo = $baseQb
                             ->select()
                             ->where('start_time', '<', $endTime)
                             ->where('end_time', '>', $startTime)
                             ->columns($checkColumns)
                             ->fetchAll()[0] ?? [];

            $isDuplicateAccount = $existInfo['is_duplicate_account'] ?? 0;
            $isDuplicateAnchor  = $existInfo['is_duplicate_anchor'] ?? 0;
            $isDuplicatePackage = $existInfo['is_duplicate_package'] ?? 0;

            if ($isDuplicateAnchor) {
                throw new \InvalidArgumentException("同个时间段内, 主播不能重复");
            }

            if ($isDuplicateAccount) {
                throw new \InvalidArgumentException("同个时间段内, 直播账号不能重复");
            }

            if ($isDuplicatePackage) {
                throw new \InvalidArgumentException("同个时间段内, 包号不能重复");
            }

//            if (isset($existInfo['is_duplicate_assistant'])) {
//                if ($existInfo['is_duplicate_assistant']) {
//                    throw new \InvalidArgumentException("同个时间段内, 运营助手不能重复");
//                }
//            }
//
//            if (isset($existInfo['is_duplicate_operation_account'])) {
//                if ($existInfo['is_duplicate_operation_account']) {
//                    throw new \InvalidArgumentException("同个时间段内, 运营账号不能重复");
//                }
//            }

            $dataOnDuty = array_intersect_key($data, [
                'start_time'           => '',
                'end_time'             => '',
                'live_account_id'      => '',
                'anchor_id'            => 0,
                'assistant_id'         => 0,
                'operation_account_id' => 0,
                'package_id'           => 0,
                'operator_id'          => 0,
            ]);

            $resultOnDuty = $baseQb->insertOne($dataOnDuty);

            $costIndex = array_intersect_key($data, [
                'start_time' => '',
                'end_time'   => '',
                'package_id' => ''
            ]);

            $costIndex['ad_account_id']   = $data['live_account_id'] ?? '';
            $costIndex['ad_account_name'] = $accountMap[$data['live_account_id']] ?? '';

            // 消耗导入
            foreach (['anchor_cost', 'lucky_cost', 'knob_cost'] as $costKey) {
                if (!empty($data[$costKey])) {
                    $c = $costIndex;

                    if ($costKey == 'anchor_cost') {
                        $t = 1;
                    }
                    elseif ($costKey == 'lucky_cost') {
                        $t = 2;
                    }
                    elseif ($costKey == 'knob_cost') {
                        $t = 3;
                    }
                    else {
                        $t = 0;
                    }

                    $c['cost_type'] = $t;
                    $c['cost']      = $c['cost_discount'] = $data[$costKey];

                    $rowNum = $ndb
                        ->table(TableCollect::DWD_AD_LIVE_COST)
                        ->select()
                        ->where([
                            'start_time'    => $c['start_time'],
                            'end_time'      => $c['end_time'],
                            'ad_account_id' => $c['ad_account_id'],
                            'package_id'    => $c['package_id'],
                            'cost_type'     => $c['cost_type']
                        ])->count();

                    if ($rowNum > 0) {
                        $ndb
                            ->table(TableCollect::DWD_AD_LIVE_COST)
                            ->update([
                                'cost'          => $c['cost'],
                                'cost_discount' => $c['cost_discount']
                            ])
                            ->where([
                                'start_time'    => $c['start_time'],
                                'end_time'      => $c['end_time'],
                                'ad_account_id' => $c['ad_account_id'],
                                'package_id'    => $c['package_id'],
                                'cost_type'     => $c['cost_type']
                            ])
                            ->run();
                    }
                    else {
                        $ndb->table(TableCollect::DWD_AD_LIVE_COST)->insertOne($c);
                    }
                }
            }

            return true;
        });
    }

    /**
     * @param $data
     *
     * @return mixed
     * @throws \Throwable
     */
    public function updateById($data)
    {
        $db = $this->getConn();

        if (
            empty($data['package_id'])
            || empty($data['live_account_id'])
            || empty($data['anchor_id'])
        ) {
            throw new \InvalidArgumentException('参数缺失');
        }

        if (empty($data['start_time']) || empty($data['end_time'])) {
            throw new \InvalidArgumentException('参数缺失:开始时间/结束时间');
        }
        else {
            // 判断时间选择范围是否超过了当天
            try {
                $startTime = new \DateTime($data['start_time']);
                $endTime   = new \DateTime($data['end_time']);
            }
            catch (\Throwable $e) {
                throw new \InvalidArgumentException('时间格式不规范');
            }

            if ($startTime > $endTime) {
                throw  new \InvalidArgumentException('选择时间范围异常');
            }
        }

        // 检查包号是否存在
        if ((new GeneralOptionServ())->checkPackages($data['package_id']) == 0) {
            throw new \InvalidArgumentException('该包号不存在');
        }

        return $db->transaction(function (Database $ndb) use ($data) {
            $startTime  = $data['start_time'];
            $endTime    = $data['end_time'];
            $baseQb     = $ndb->table(TableConst::CONF_LIVE_ON_DUTY);
            $accountMap = $ndb->table(TableConst::CONF_LIVE_ACCOUNT)->select('account_id', 'account')->fetchAll();
            $accountMap = array_column($accountMap, 'account', 'account_id');

            [
                'package_id'      => $packageId,
                'live_account_id' => $liveAccount,
                'anchor_id'       => $anchorId,
            ] = $data;

            $id = Arr::pull($data, 'id');

            $checkColumns = [
                new Fragment("SUM(IF(live_account_id = '{$liveAccount}', 1, 0)) > 0 as is_duplicate_account"),
                new Fragment("SUM(IF(anchor_id = {$anchorId}, 1, 0)) > 0 as is_duplicate_anchor"),
                new Fragment("SUM(IF(package_id = {$packageId}, 1, 0)) > 0 as is_duplicate_package"),
            ];

//            if (!empty($data['assistant_id'])) {
//                $checkColumns[] = new Fragment("SUM(IF(assistant_id = {$data['assistant_id']}, 1, 0)) > 0 as is_duplicate_assistant");
//            }
//
//            if (!empty($data['operation_account_id'])) {
//                $checkColumns[] = new Fragment("SUM(IF(operation_account_id = {$data['operation_account_id']}, 1, 0)) > 0 as is_duplicate_operation_account");
//            }

            // 查询是否有已经重复的数据
            $existInfo = $baseQb
                             ->select()
                             ->where('start_time', '<', $endTime)
                             ->where('end_time', '>', $startTime)
                             ->where('id', '!=', $id)
                             ->columns($checkColumns)
                             ->fetchAll()[0] ?? [];

            $isDuplicateAccount = $existInfo['is_duplicate_account'] ?? 0;
            $isDuplicateAnchor  = $existInfo['is_duplicate_anchor'] ?? 0;
            $isDuplicatePackage = $existInfo['is_duplicate_package'] ?? 0;

            if ($isDuplicateAnchor) {
                throw new \InvalidArgumentException("同个时间段内, 主播不能重复");
            }

            if ($isDuplicateAccount) {
                throw new \InvalidArgumentException("同个时间段内, 直播账号不能重复");
            }

            if ($isDuplicatePackage) {
                throw new \InvalidArgumentException("同个时间段内, 包号不能重复");
            }

            $dataOnDuty   = array_intersect_key($data, [
                'start_time'           => '',
                'end_time'             => '',
                'live_account_id'      => '',
                'anchor_id'            => 0,
                'assistant_id'         => 0,
                'operation_account_id' => 0,
                'package_id'           => 0,
                'operator_id'          => 0,
            ]);
            $sourceDuty   = array_change_key_case(((clone $baseQb)->select()->where('id', $id)->fetchAll())[0] ?? []);
            $resultOnDuty = (clone $baseQb)->update($dataOnDuty)->where('id', $id)->run();

            $costIndex = array_intersect_key($data, [
                'start_time' => '',
                'end_time'   => '',
                'package_id' => ''
            ]);

            $costIndex['ad_account_id']   = $data['live_account_id'] ?? '';
            $costIndex['ad_account_name'] = $accountMap[$data['live_account_id']] ?? '';

            // 消耗导入
            foreach (['anchor_cost', 'lucky_cost', 'knob_cost'] as $costKey) {
                $c = $costIndex;

                if ($costKey == 'anchor_cost') {
                    $t = 1;
                }
                elseif ($costKey == 'lucky_cost') {
                    $t = 2;
                }
                elseif ($costKey == 'knob_cost') {
                    $t = 3;
                }
                else {
                    $t = 0;
                }

                $c['cost_type'] = $t;
                $c['cost']      = $c['cost_discount'] = $data[$costKey] ?? 0;

                // 重刷消费数据
                $ndb
                    ->table(TableCollect::DWD_AD_LIVE_COST)
                    ->delete()
                    ->where([
                        'start_time'    => $sourceDuty['start_time'],
                        'end_time'      => $sourceDuty['end_time'],
                        'ad_account_id' => $sourceDuty['live_account_id'],
                        'package_id'    => $sourceDuty['package_id'],
                        'cost_type'     => $c['cost_type']
                    ])
                    ->run();

                if ($c['cost'] > 0) {
                    $ndb->table(TableCollect::DWD_AD_LIVE_COST)->insertOne($c);
                }
            }

            return true;
        });

    }

    /**
     * @param array $ids
     *
     * @return mixed
     * @throws \Throwable
     */
    public function removeByIds(array $ids)
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $ndb) use ($ids) {
            $baseQb     = $ndb->table(TableConst::CONF_LIVE_ON_DUTY);
            $sourceData = (clone $baseQb)->select()->where('id', new Parameter($ids))->fetchAll();
            $delQb      = (clone $baseQb)->delete()->where('id', new Parameter($ids));

            // 删除消耗金额
            foreach ($sourceData as $foo) {
                $foo = array_change_key_case($foo);
                $ndb
                    ->table(TableCollect::DWD_AD_LIVE_COST)
                    ->delete()
                    ->where([
                        'start_time'    => $foo['start_time'],
                        'end_time'      => $foo['end_time'],
                        'ad_account_id' => $foo['live_account_id'],
                        'package_id'    => $foo['package_id'],
                    ])
                    ->run();
            }

            return $delQb->run();
        });
    }

    /**
     * @return array
     */
    public function getOperatorList(): array
    {
        return $this
            ->getConn()
            ->select()
            ->from(TableConst::CONF_LIVE_ON_DUTY)
            ->columns(['operator_id as operator_id'])
            ->distinct()
            ->fetchAll();
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('base_conf_platform');
    }


    /**
     * 批量写入值班配置
     *
     * @param array $data
     *
     * @return mixed
     * @throws \Throwable
     */
    public function multiInsert(array $data)
    {
        $db = $this->getConn();
        return $db->transaction(static function (Database $ndb) use ($data) {
            $ids = [];
            foreach ($data as $chill) {
                if (!Zakia::arrayPropRequired(
                    $chill, ['package_id', 'live_account_id', 'anchor_id', 'start_time', 'end_time']
                )) {
                    throw new \InvalidArgumentException('参数缺失');
                }

                try {
                    $startTime = new \DateTime($chill['start_time']);
                    $endTime   = new \DateTime($chill['end_time']);
                }
                catch (\Throwable $e) {
                    throw new \InvalidArgumentException('时间格式不规范');
                }

                if ($startTime > $endTime) {
                    throw  new \InvalidArgumentException('选择时间范围异常');
                }

//                $timeDiff = (int)$startTime->diff($endTime)->format('%R%a');
//
//                if ($timeDiff != 0) {
//                    throw new \InvalidArgumentException('选择时间范围不能超过当天');
//                }

                // 检查包号是否存在
                if ((new GeneralOptionServ())->checkPackages($chill['package_id']) == 0) {
                    throw new \InvalidArgumentException('该包号不存在');
                }

                $baseQb = $ndb->table(TableConst::CONF_LIVE_ON_DUTY);

                [
                    'package_id'      => $packageId,
                    'live_account_id' => $liveAccount,
                    'anchor_id'       => $anchorId,
                ] = $chill;

                $checkColumns = [
                    new Fragment("SUM(IF(live_account_id = '{$liveAccount}', 1, 0)) > 0 as is_duplicate_account"),
                    new Fragment("SUM(IF(anchor_id = '{$anchorId}', 1, 0)) > 0 as is_duplicate_anchor"),
                    new Fragment("SUM(IF(package_id = '{$packageId}', 1, 0)) > 0 as is_duplicate_package"),
                ];

                // 查询是否有已经重复的数据
                $existInfo = $baseQb
                                 ->select()
                                 ->where('start_time', '<', $endTime->format('Y-m-d H:i:s'))
                                 ->where('end_time', '>', $startTime->format('Y-m-d H:i:s'))
                                 ->columns($checkColumns)
                                 ->fetchAll()[0] ?? [];

                $isDuplicateAccount = $existInfo['is_duplicate_account'] ?? 0;
                $isDuplicateAnchor  = $existInfo['is_duplicate_anchor'] ?? 0;
                $isDuplicatePackage = $existInfo['is_duplicate_package'] ?? 0;

                if ($isDuplicateAnchor) {
                    throw new \InvalidArgumentException("同个时间段内, 主播不能重复");
                }

                if ($isDuplicateAccount) {
                    throw new \InvalidArgumentException("同个时间段内, 直播账号不能重复");
                }

                if ($isDuplicatePackage) {
                    throw new \InvalidArgumentException("同个时间段内, 包号不能重复");
                }

                $ids[] = $baseQb->insertOne($chill);
            }

            return $ids;
        });
    }

}
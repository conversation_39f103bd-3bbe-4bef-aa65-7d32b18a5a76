<?php

namespace app\apps\internal\Helpers;

use app\apps\operator\Helpers\ConstFirstLogin;
use app\extension\Support\Collections\Arr;

/**
 *
 */
class IndexCalculators
{
    /**
     * ltv计算
     *
     * @param array     $ltvCollect ltv集合
     * @param array     $options    筛选参数
     * @param int|array $dayTypes   节点或生成最大节点数
     *
     * @return \Closure
     */
    public static function ltvCalculators(
        array    $ltvCollect = [], array $options = [],
        \Closure $getNewUserFunc = null, $dayTypes = null, $field = 'money_all'
    ): \Closure
    {
        $groups  = $options['groups'];
        $ltvType = $options['ltv_type'] ?? 0;

        return function (&$target, $k) use (
            $ltvCollect, $groups, $dayTypes, $ltvType, $field, $getNewUserFunc
        ) {
            $ltvInfo = $ltvCollect[$k]['day_type'] ?? [];

            if (!empty($ltvInfo)) {
                static::ltvCalc($target, $ltvInfo, $getNewUserFunc, $field, $ltvType);
            }

            static::fillNodes($target, $dayTypes, 0.00, 'ltv');
        };
    }

    /**
     * 增量LTV展示计算
     *
     * @return \Closure
     */
    public static function ltvIncrementalCalculators(): \Closure
    {
        return function (&$target, $k) {
            static::ltvIncrementalCalc($target);
        };
    }

    /**
     * 增量LTV展示计算过程
     *
     * @param $target
     *
     * @return void
     */
    public static function ltvIncrementalCalc(&$target)
    {
        preg_match_all('/ltv_\d+/', implode(',', array_keys($target)), $keyMatches);
        $keys = Arr::pull($keyMatches, 0, []);

        if (empty($keys)) return;
        rsort($keys);

        foreach ($keys as $kk) {
            $ii          = (int)str_replace('ltv_', '', $kk);
            $target[$kk] = round($target[$kk] - ($target['ltv_' . ($ii - 1)] ?? 0.00), 2);
        }
    }

    /**
     * 计算累计LTV
     *
     * @return \Closure
     */
    public static function latestLtvCalculators($userField = 'firstlogin_user'): \Closure
    {
        return function (&$target, $k) use ($userField) {
            static::latestLtvCalc($target, $userField);
        };
    }

    /**
     * @param $target
     *
     * @return void
     */
    public static function latestLtvCalc(&$target, $userField = 'firstlogin_user')
    {
        $target['total_ltv'] = IndicatorsHelpers::division(
            ($target['new_user_total_pay'] ?? 0.00),
            ($target[$userField] ?? 0.00)
        );
    }

    /**
     * @param $newUserField
     *
     * @return \Closure
     */
    public static function lastRemainCalculators(): \Closure
    {
        return function (&$target, $k) {
            static::lastRemainCalc($target);
        };
    }

    /**
     * @param $target
     * @param $newUserField
     *
     * @return void
     */
    public static function lastRemainCalc(&$target)
    {
        preg_match_all('/remain_\d+/', implode(',', array_keys($target)), $keyMatches);
        $keys = Arr::pull($keyMatches, 0, []);

        if (empty($keys)) {
            $target['remain_current'] = '0.00%';
            return;
        }

        $target['remain_current'] = $target[$keys[count($keys) - 1]];
    }

    /**
     * 倍数LTV展示计算
     *
     * @return \Closure
     */
    public static function ltvMultipleCalculators(): \Closure
    {
        return function (&$target, $k) {
            static::ltvMultipleCalc($target);
        };
    }

    /**
     * @param $target
     *
     * @return void
     */
    public static function ltvMultipleCalc(&$target)
    {
        preg_match_all('/\bltv_\d+\b/', implode(',', array_keys($target)), $keyMatches);
        $keys = Arr::pull($keyMatches, 0, []);

        if (empty($keys)) return;

        $ltv1 = $target['ltv_1'] ?? 0.00;

        foreach ($keys as $kk) {
            if ($ltv1 == 0.00) {
                $target[$kk] = '-';
            }
            else {
                $target[$kk] = IndicatorsHelpers::division($target[$kk], $ltv1);
            }
        }
    }

    /**
     * @param array     $ltvCollect
     * @param array     $options
     * @param int|array $dayTypes
     *
     * @return \Closure
     */
    public static function roiCalculators(
        array $ltvCollect = [], \Closure $getCostFunc = null, array $options = [], $dayTypes = null
    ): \Closure
    {
        $groups = $options['groups'];

        return function (&$target, $k) use ($ltvCollect, $groups, $dayTypes, $getCostFunc) {
            $ltvInfo = $ltvCollect[$k]['day_type'] ?? [];

            if (!empty($ltvCollect)) {
                static::roiCalc($target, $getCostFunc, $ltvInfo);
            }
            static::fillNodes($target, $dayTypes, '0.00%', 'roi');
        };
    }

    /**
     * ltv 计算
     *
     * @param               $target
     * @param array $ltvInfo
     * @param \Closure|null $getUserFunc
     * @param string $field
     * @param int $ltvType
     *
     * @return void
     */
    public static function ltvCalc(
        &$target,
        array $ltvInfo = [],
        \Closure $getUserFunc = null,
        string $field = 'money_all',
        int $ltvType = 0
    )
    {
        $ltvGetFunc = static::ltvValueGet($ltvType);

        if (empty($ltvInfo)) return;

        /**
         * 获取最新的ltv金额作为新用户累计付费
         * 固定1000的节点为累计的付费金额
         */
        $target['new_user_total_pay'] = $ltvInfo[1000][$field] ?? 0.00;

        foreach ($ltvInfo as $i => $foo) {
            $target['ltv_' . $i] = $ltvGetFunc($foo[$field], $getUserFunc($target, $i));
        }
    }

    /**
     * @param string $field
     *
     * @return \Closure
     */
    public static function getSingleValue(string $field): \Closure
    {
        return fn($data) => $data[$field];
    }

    /**
     * @param string $collectName
     *
     * @return \Closure
     */
    public static function getValueInCollectByN(string $collectName): \Closure
    {
        return fn($data, $n) => $data[$collectName][$n] ?? 0;
    }


    /**
     * 定义ltv返回值类型
     *
     * @param int $ltvType
     *
     * @return \Closure
     */
    public static function ltvValueGet(int $ltvType): \Closure
    {
        if (1 === $ltvType) {
            return fn($ltvMoney) => $ltvMoney;
        }
        else {
            return function ($ltvMoney, $userNew) {
                return (float)IndicatorsHelpers::division($ltvMoney, $userNew);
            };
        }
    }

    /**
     * roi计算
     *
     * @param          $target
     * @param \Closure $getCostFunc
     * @param array $ltvInfo
     * @param string $field
     * @param bool $hasN
     *
     * @return void
     */
    public static function roiCalc(
        &$target, \Closure $getCostFunc, array $ltvInfo = [], string $field = 'money_all', bool $hasN = false
    )
    {
        foreach ($ltvInfo as $i => $foo) {
            $target['roi_' . $i] = IndicatorsHelpers::division($foo[$field], $getCostFunc($target, $i), 2, true);
        }
    }

    /**
     * 留存计算
     *
     * @param array $remainCollect
     * @param array $options
     * @param \Closure|null $getNewUserFunc
     * @param null $dayTypes
     * @param string $field
     *
     * @return \Closure
     */
    public static function remainCalculators(
        array    $remainCollect = [], array $options = [],
        \Closure $getNewUserFunc = null, $dayTypes = null, $maxDayDiff = 0, $field = 'money_all'
    ): \Closure
    {
        $groups     = $options['groups'];
        $remainType = $options['remain_type'] ?? 0;

        return function (&$target, $k) use (
            $remainCollect, $groups, $dayTypes,
            $remainType, $field, $getNewUserFunc, $maxDayDiff
        ) {
            $remainInfo = $remainCollect[$k]['day_type'] ?? [];
            $today      = new \DateTime();

            if (!empty($remainInfo)) {
                static::remainCalc($target, $remainInfo, $getNewUserFunc, 'login_num', $remainType);
            }

            if (in_array('tday', $groups) && $dayTypes == ConstFirstLogin::DIMENSION_DAY) {
                $dayDiff = days_apart($target['tday'], $today);
            }
            else {
                $dayDiff = $maxDayDiff;
            }

            static::fillNodes($target, $dayDiff, 0.00, 'remain');
            static::fillNodes($target, $maxDayDiff, '-', 'remain');
        };
    }

    /**
     * @param        $target
     * @param array $remainInfo
     * @param string $field
     * @param int $remainType
     * @param bool $hasNUser
     *
     * @return void
     */
    public static function remainCalc(
        &$target,
        array $remainInfo = [],
        \Closure $getUserFunc = null,
        string $field = 'login_num',
        int $remainType = 0
    )
    {
        $remainGetFunc = static::remainValueGet($remainType);

        if (empty($remainInfo)) return;

        foreach ($remainInfo as $i => $foo) {
            if ($i == 1000) {
                $target['remain_current'] = $foo[$field];
            }
            $target['remain_' . $i] = $remainGetFunc($foo[$field], $getUserFunc($target, $i));
        }
    }

    /**
     * 留存值展示形式
     *
     * @param int $remainType
     *
     * @return \Closure
     */
    public static function remainValueGet(int $remainType): \Closure
    {
        if (1 === $remainType) {
            // 留存用户数
            return fn($loginNum) => $loginNum;
        }
        elseif (2 === $remainType) {
            return function ($loginNum, $userNew) {
                $percentNum = IndicatorsHelpers::division($loginNum, $userNew, 2, true);
                return $loginNum . "({$percentNum})";
            };
        }
        else {
            return function ($loginNum, $userNew) {
                return IndicatorsHelpers::division($loginNum, $userNew, 2, true);
            };
        }
    }


    /**
     * 填充默认数据
     *
     * @param           $target
     * @param int|array $nodes
     * @param           $default
     * @param string $keyPrefix
     *
     * @return void
     */
    public static function fillNodes(&$target, $nodes, $default = null, string $keyPrefix = '')
    {
        if (is_array($nodes)) {
            $gen = function () use ($nodes) {
                foreach ($nodes as $node) {
                    yield $node;
                }
            };
        }
        elseif (is_numeric($nodes)) {
            $gen = function () use ($nodes) {
                for ($i = 1; $i <= $nodes; $i++) {
                    yield $i;
                }
            };
        }
        else {
            return;
        }

        foreach ($gen as $i) {
            $key = $keyPrefix . '_' . $i;
            if (isset($target[$key])) continue;
            $target[$key] = $default;
        }
    }

    /**
     * 计算留存天数
     *
     * @param int $dateDimension 时间维度
     * @param array $groups 汇总行选项
     * @param int $default
     *
     * @return \Closure
     */
    public static function remainDayCalculators(int $dateDimension, array $groups, int $default = 0): \Closure
    {
        return function (&$target, $k) use ($dateDimension, $groups, $default) {
            static::daysApartCalc(
                $target, 'remain_days', $dateDimension, in_array('tday', $groups), $default
            );
        };
    }

    /**
     * @param      $target
     * @param      $field
     * @param int $dateDimension [2-按天, 3-按周,4-按月]
     * @param bool $groupHasDay 如果汇总分组没有选择日期分组将直接返回最大的时间差
     * @param int $default 默认传当天到查询开始时间的最大时间差
     *
     * @return void
     * @throws \Exception
     */
    public static function daysApartCalc(
        &$target, $field, int $dateDimension = 2, bool $groupHasDay = true, int $default = 0
    )
    {
        if (empty($target['tday'])) return;

        if (!$groupHasDay || $target['tday'] === '汇总') {
            // 如果汇总分组没有选择日期分组将直接返回最大的时间差
            $target[$field] = $default - 1;
            return;
        }
        $today = new \DateTime();

        if (2 === $dateDimension) {
            $target[$field] = max(days_apart($target['tday'], $today) - 1, 0);
            return;
        }

        if (3 === $dateDimension) {
            $weekScope      = \explode('/', $target['tday']);
            $target[$field] = max(days_apart(Arr::get($weekScope, 0, $today), $today) - 1, 0);
            return;
        }

        if (4 === $dateDimension) {
            $monthStart     = $target['tday'] . '-01';
            $target[$field] = max(days_apart($monthStart, $today) - 1, 0);
        }
    }

}
<?php

namespace app\apps\operator\Traits;

use app\apps\operator\Helpers\IndicatorCalcHelpers;

trait OperationCalculators
{
    /**
     * @return \Closure
     */
    protected function firstLoginGeneralCalculators(): \Closure
    {
        return function (&$target) {
            IndicatorCalcHelpers::deviceConversionRate($target);
            IndicatorCalcHelpers::fistLoginCreateRole($target);
            IndicatorCalcHelpers::fistLoginRealName($target);
            IndicatorCalcHelpers::firstLoginUserPayPercent($target);
            IndicatorCalcHelpers::firstLoginUserARPU($target);
            IndicatorCalcHelpers::firstLoginUserARPPU($target);
            IndicatorCalcHelpers::firstLoginCost($target);
            IndicatorCalcHelpers::payActivePercent($target);
            IndicatorCalcHelpers::activeARPU($target);
            IndicatorCalcHelpers::activeARPPU($target);
            IndicatorCalcHelpers::activeUserOld($target);
            IndicatorCalcHelpers::oldUserPay($target);
            IndicatorCalcHelpers::oldUser<PERSON>ayMoney($target);
            IndicatorCalcHelpers::oldUserPayPercent($target);
            IndicatorCalcHelpers::oldUserARPU($target);
            IndicatorCalcHelpers::oldUserARPPU($target);
            IndicatorCalcHelpers::oldUserPercent($target);
            IndicatorCalcHelpers::firstLoginActiveUserPayPercent($target);
            IndicatorCalcHelpers::serverRollPercent($target);
            IndicatorCalcHelpers::payPermeationPercent($target);
        };
    }

    /**
     * @return \Closure
     */
    protected function newLoginGeneralCalculators(): \Closure
    {
        return function (&$target) {
            IndicatorCalcHelpers::deviceConversionRateNewLogin($target);
            IndicatorCalcHelpers::newLoginCost($target);
        };
    }


    protected function operatorLtvCalculators(): \Closure
    {
        return function (&$target) {
            IndicatorCalcHelpers::firstLoginCost($target);
        };
    }



}
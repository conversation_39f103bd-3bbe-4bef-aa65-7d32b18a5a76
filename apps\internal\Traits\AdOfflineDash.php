<?php

namespace app\apps\internal\Traits;

use app\apps\internal\Helpers\Calculator;
use app\apps\internal\Helpers\ConstHub;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\ProcessLineContext;
use app\service\AdvertiserData\CreativeLtvIndex;

trait AdOfflineDash
{
    /**
     * 解析每行精确匹配的查询条件
     *
     * @param array $params
     * @param array $groups
     *
     * @return \Closure
     */
    public function parseMatchForEachRow(array $params, array $groups): \Closure
    {
        return function (&$target, ProcessLineContext $context) use ($params, $groups) {
            $today         = new \DateTime();
            $groupIndex    = array_fill_keys($groups, 0);
            $options       = array_intersect_key($target, array_diff_key($groupIndex, ['tday' => '']));
            $timeDimension = (int)Arr::get($params, 'range_date_dimension', ConstHub::DIMENSION_DAY);

            if (in_array('tday', $groups)) {
                $tDay = $target['tday'];

                if ($timeDimension === ConstHub::DIMENSION_WEEK) {
                    $days = \explode('/', $tDay);
                    sort($days);

                    $options['range_date_start'] = $days[0];
                    $options['range_date_end']   = $days[1];
                }
                elseif ($timeDimension === ConstHub::DIMENSION_MONTH) {
                    $options['range_date_start'] = $tDay . '-01';
                    $options['range_date_end']   = (new \DateTime($options['range_date_start']))->format('Y-m-t');
                }
                else {
                    $options['range_date_start'] = $options['range_date_end'] = $tDay;
                }
            }

            $otherOption = array_diff_key($params, $options);
            $options     = array_merge($options, $otherOption);

            $context->put('row_params', $options);

        };
    }

    /**
     * 追加LTV详情
     *
     * @param callable $ltvCallback
     * @param array    $groups
     *
     * @return \Closure
     */
    public function addLtvInfo(callable $ltvCallback, array $groups = []): \Closure
    {
        return function (&$target, ProcessLineContext $context) use ($ltvCallback, $groups) {
            $rowParams = $context->get('row_params');
            if (!in_array('day_type', $groups)) {
                $groups[] = 'day_type';
            }
            $rowParams['remove_realtime'] = 1;

            $ltvInfo = call_user_func_array($ltvCallback, [$rowParams, $groups, false]);

            if (!empty($ltvInfo)) {
                $target['ltv_info'] = $ltvInfo;
            }
        };
    }

    /**
     * 追加留存信息
     *
     * @param callable $remainCallback
     * @param array    $groups
     * @param int      $maxDayType
     *
     * @return \Closure
     */
    public function addRemainInfo(callable $remainCallback, array $groups = [], int $maxDayType = -1): \Closure
    {
        return function (&$target, ProcessLineContext $context) use ($remainCallback, $groups, $maxDayType) {
            $rowParams = $context->get('row_params');

            if (isset($rowParams['promotion_channel_id'])) {
                unset($rowParams['channel_id']);
            }

            if (!in_array('day_type', $groups)) {
                $groups[] = 'day_type';
            }
            $rowParams['login_date[<]'] = date('Y-m-d');
            $opt                        = [$rowParams, $groups];

            if ($maxDayType >= 0) {
                $opt[] = $maxDayType;
            }

            $remainInfo = call_user_func_array($remainCallback, $opt);

            if (!empty($remainInfo)) {
                $target['remain_info'] = $remainInfo;
            }

        };
    }

    /**
     * @param callable $baseCallback
     * @param array $infoKeys
     * @param array $groups
     * @param array $options
     * @return \Closure
     */
    public function addEachRowInfo(callable $baseCallback, array $infoKeys, array $groups = [], $options = []): \Closure
    {
        $operator = $options['operator'] ?? '<=';

        return function (&$target, ProcessLineContext $context) use ($baseCallback, $infoKeys, $groups, $operator) {
            $rowParams = $context->get('row_params');

            $baseResult = call_user_func_array($baseCallback, [$rowParams, [], $groups, null, [], false]);

            if (!empty($baseResult)) {
                if (isset($baseResult['list'])) {
                    $baseResult = $baseResult['list'];
                }

                $nInfo  = Calculator::cumulativeOnDays($baseResult, $infoKeys, $operator);
                $target = array_merge($target, $nInfo);
            }
        };
    }

}
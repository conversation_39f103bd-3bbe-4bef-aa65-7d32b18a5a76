<?php

namespace app\service\ConfigService;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\CommonCenter\ConfCenter\Request;
use app\extension\Support\CommonCenter\ConfCenter\Uri;
use app\extension\Support\Helpers\CollectHelper;
use app\extension\Support\Helpers\Structs\Tree;
use app\logic\tool\IndicatorReferenceLogic;
use app\models\baseConfPlatform\TbBaseChannelConf;
use app\models\baseConfPlatform\TbBaseGameConf;
use app\models\DataSpy\AdminDepartment;
use app\models\DataSpy\AdminUser;
use app\service\AdvertiseLive\LiveAccountServ;
use app\service\AdvertiserData\CostManagerServ;
use app\service\BusinessPlatform\OptionServ;
use app\service\General\GeneralOptionServ;
use app\util\BizConfig;
use Plus\Net\Http;

/**
 *
 * 基础配置服务
 * @BasicServ
 *
 * phpcs:disable
 */
class BasicServ
{

    //缓存过期时间
    const cacheExpire = 1200;

    /**
     * 固定的投放部门ID
     */
    private const FIX_AD_DEPARTMENT = [
        187, 389, 391, 507, 553, 562, 605, 684,
        100031, 100040, 100041, 100043, 100047, 387, 427, 100098,
    ];

    /**
     * 请求API的选项
     *
     * @template TKey     中台接口字段名映射
     * @template TValue   本地字段映射
     *
     * @var array<TKey, TValue>
     */
    private const FIELD_OPTIONS_API = [
        'app_show_id'       => [
            'param'     => 'app_show_name',
            'match_key' => ['app_show_name', 'app_show_id'],
        ],
        'cp_game_id'        => [
            //            'param'     => 'cp_games:2',
            'param'     => 'cp_games',
            'match_key' => ['cp_games', 'cp_game_id'],
        ],
        'cp_game_id:all'    => [
            'param'     => 'cp_games',
            'match_key' => ['cp_games', 'cp_game_id'],
        ],
        //        'game_id'         => [
        //            'param'     => 'games:0:2',
        //            'match_key' => ['games', 'game_id'],
        //        ],
        //        'channel_id'      => [
        //            'param'     => 'channel',
        //            'match_key' => ['channel'],
        //        ],
        'channel_main_id'   => [
            'param'     => 'channel_main',
            'match_key' => ['channel_main', 'channel_main_id'],
        ],
        'package_id'        => [
            'param'     => 'package_id',
            'match_key' => ['package_id', 'package_id'],
        ],
        'platform_id'       => [
            'param'     => 'common_kv:platform:1',
            'match_key' => ['platform', 'platform_id', 'common_kv:platform:1'],
        ],
        'promotion_id'      => [
            'param'     => 'common_kv:promotion:1',
            'match_key' => ['promotion', 'promotion:1', 'common_kv:promotion:1'],
        ],
        'order_type'        => [
            'param'     => 'common_kv:spy_pay_order_type',
            'match_key' => ['common_kv:spy_pay_order_type', 'spy_pay_order_type'],
            //            'param'     => 'common_kv:spy_order_type',
            //            'match_key' => ['common_kv:spy_order_type', 'spy_order_type'],
        ],
        'live_team'         => [
            'param'     => 'common_kv:live_team',
            'match_key' => ['live_team', 'common_kv:live_team'],
        ],
        'handle_order_type' => [
            'param'     => 'common_kv:spy_hand_order_type',
            'match_key' => ['spy_hand_order_type', 'common_kv:spy_hand_order_type'],
        ],
        'voided_type'       => [
            'param'     => 'common_kv:voided_type',
            'match_key' => ['voided_type', 'common_kv:voided_type'],
        ],
    ];

    private const FIELD_OPTIONS_LOCAL = [
        'department_id'                 => 'queryDepartment',
        'user_id'                       => 'queryAdUser',
        'groups'                        => 'queryGroups',
        'data_scope'                    => 'queryDataScope',
        'promotion_channel_id'          => 'queryPromotionChannel',
        'has_no_cost'                   => 'queryNoCost',
        'has_visual_pay'                => 'queryHasVisualSelect',
        'ltv_type'                      => 'queryLtvType',
        'remain_type'                   => 'queryRemainType',
        'report_dimension'              => 'queryDimension',
        'goal:type'                     => 'queryGoalType',
        'goal:range_date_dimension'     => 'queryRangeDateDimension',
        'goal:data_type'                => 'queryGoalDashBoardDataType',
        'revenue:cost_type'             => 'queryCostType',
        'revenue:income_type'           => 'queryIncomeType',
        'live:account_id'               => 'queryLiveAccount',
        'event_distribute:event_type'   => 'queryEventType',
        'event_distribute:group_type'   => 'queryGroupType',
        'event_distribute:display_type' => 'queryDisplayType',
        'channel_id'                    => 'queryChannel',
        'game_id'                       => 'queryGame',
        'live_cost:cost_type'           => 'queryLiveCostType',
        'order_status'                  => 'queryOrderStatus',
        'game_back_status'              => 'queryGameStatus',
        'pay_combo'                     => 'queryPayCombo',
        'payway'                        => 'queryPayWay',
        'tag_mode'                      => 'queryTagMode',
        'tagging_status'                => 'queryTaggingStatus',
        'tagging_operator'              => 'queryTaggingOperator',
        'test_department'               => 'queryTestDepartment',
        'department:all'                => 'queryAllDepartment',
        'user:all'                      => 'queryAllUser',
        'media_platform_id'             => 'queryMediaPlatform',
        'media_account_id'              => 'queryMediaAccount',
        'business_ownership'            => 'queryBusinessOwnership',
        'use_kind'                      => 'queryUseKind',
        'demo_video_id'                 => 'queryDemoVideoId',
        'operations_manager'            => 'queryOperationsManager',
        'is_ad_new'                     => 'queryIsAdNew',
        'is_sub_account'                => 'queryIsSubAccount',
        'marketing_goal'                => 'queryMarketingGoal',
        'task_script_id'                => 'queryTaskScript',
        'reporting-conf:status'         => 'queryReportingConfStatus',
        'reporting-conf:report_result'  => 'queryReportResult',
        'reporting-conf:report_content' => 'queryReportReportContent',
        'cost_index_type'               => 'queryCostIndexType',
        'settlement_id'                 => 'querySettlementId',
        'agent_id'                      => 'queryAgentId',
        'operate_id'                    => 'queryOperateId',
        'explain_id'                    => 'queryExplainId',
        'classify'                      => 'queryClassify',
        'job_kind'                      => 'queryJobKind',
        'sdk_logs:event_type'           => 'querySdkLogEvent',
        'live_platform'                 => 'queryMediaPlatform',
        'deduction-report:rule_type'    => 'queryDeductionReportRuleType',
        'payment_virtual:rule_id'       => 'queryPaymentVirtualRuleType',
        'indicator_reference_first'     => 'queryIndicatorReferenceFirst',
        'user_os'                       => 'queryUserOs',
        'reported_deduct_rule'          => 'queryDeductReportedRule',
        'reported_status'               => 'queryReportedStatus',
        'reported_behavior'             => 'queryReportedBehavior',
        'retain_type'                   => 'queryRetainType',
    ];

    /**
     * 大盘看板的报表维度选择类型
     *
     * @param array $options
     *
     * @return Collection
     */
    public function queryDimension(array $options = []): Collection
    {
        return collect([
            ['val' => '广告新增', 'key' => 1],
            ['val' => '首登', 'key' => 2],
            ['val' => '新增', 'key' => 3],
        ]);
    }

    /**
     * 推广分类
     *
     * @param array $options
     *
     * @return Collection
     */
    public function queryPromotionId(array $options = []): Collection
    {
        return collect([
            ['val' => '安卓买量', 'key' => 351],
            ['val' => '安卓第三方', 'key' => 352],
            ['val' => 'ios买量', 'key' => 353],
            ['val' => 'H5买量', 'key' => 354],
            ['val' => 'ios外放联运', 'key' => 355],
            ['val' => 'ios刷榜/aso', 'key' => 356],
            ['val' => '安卓外放联运', 'key' => 358],
            ['val' => 'H5外放联运', 'key' => 359],
            ['val' => 'CPA/CPS', 'key' => 360],
            ['val' => '直播引流', 'key' => 311],
            ['val' => '平台运营', 'key' => 312],
        ]);
    }

    /**
     * LTV返回格式选项
     *
     * @param array $options
     *
     * @return Collection
     */
    public function queryLtvType(array $options = []): Collection
    {
        return collect([
            ['val' => 'LTV值', 'key' => 0],
            ['val' => 'LTV金额', 'key' => 1],
            ['val' => 'LTV增量', 'key' => 2],
            ['val' => 'LTV倍数', 'key' => 3],
        ]);
    }

    /**
     * 留存返回格式选项
     *
     * @param array $options
     *
     * @return Collection
     */
    public function queryRemainType(array $options = []): Collection
    {
        return collect([
            ['val' => '留存率', 'key' => 0],
            ['val' => '留存用户数', 'key' => 1],
            ['val' => '留存用户数+留存率', 'key' => 2],
        ]);
    }

    /**
     * @param array $options
     *
     * @return Collection
     */
    public function queryCostType(array $options = []): Collection
    {
        return collect([
            ['val' => '返点前消耗金额', 'key' => 'cost'],
            ['val' => '返点后消耗金额', 'key' => 'cost_discount'],
        ]);
    }

    /**
     * @param array $options
     *
     * @return Collection
     */
    public function queryIncomeType(array $options = []): Collection
    {
        return collect([
            ['val' => '充值', 'key' => 1],
            ['val' => '业务收入', 'key' => 2],
        ]);
    }

    /**
     *
     * @param array $options
     * @param array $fieldMap
     *
     * @return Collection
     */
    public function queryDepartment(array $options = [], array $fieldMap = ['id' => 'key', 'name' => 'val']): Collection
    {
        $data = AdminDepartment::getInstance()
            ->asArray()
            ->findAll(['id' => static::FIX_AD_DEPARTMENT], ['id', 'name']);

        return CollectHelper::crossMap($data, $fieldMap);
    }

    /**
     *
     * @param array $options
     * @param array $fieldMap
     *
     * @return Collection
     */
    public function queryAdUser(array $options = [], array $fieldMap = ['id' => 'key', 'real_name' => 'val', 'department_id' => 'pid']): Collection
    {
        $data = AdminUser::getInstance()
            ->asArray()
            ->findAll(['department_id' => static::FIX_AD_DEPARTMENT], ['id', 'real_name', 'department_id']);

        return CollectHelper::crossMap($data, $fieldMap);
    }

    /**
     * 获取推广媒体渠道
     *
     * @param array $options
     * @param array $fieldMap
     *
     * @return Collection
     * @throws \Exception
     */
    public function queryPromotionChannel(array $options = [], array $fieldMap = ['id' => 'key', 'name' => 'val']): Collection
    {
        $uri = Uri::getHost() . Uri::URI_POPULARIZE_MEDIA;

        $request = new Request($uri);
        $result  = $request->get(["order" => "ASC"]);

        if (!$result) {
            throw new \RuntimeException('[WARNING]: fail to get response on popularize-media');
        }

        $result = \json_decode($result, true);

        if (empty($result['data']['items'])) {
            return collect();
        }

        $collect = collect($result['data']['items']);

        return CollectHelper::crossMap($collect, $fieldMap);
    }

    /**
     * 推广子渠道查询
     *
     * @param array $options
     * @param array $fieldMap
     *
     * @return Collection
     */
    public function queryChannel(array $options = [], array $fieldMap = ['channel_id' => 'key', 'channel_name' => 'val', 'channel_main_id' => 'pid']): Collection
    {
        $qb   = TbBaseChannelConf::getInstance();
        $data = $qb
            ->asArray()
            ->findAll([
                'is_show'         => 1,
                'channel_name[!]' => '',
            ], ['channel_id', 'channel_name', 'channel_main_id']);

        return CollectHelper::crossMap($data, $fieldMap);
    }

    /**
     * @param array $options
     * @param array $fieldMap
     *
     * @return Collection
     */
    public function queryGame(array $options = [], array $fieldMap = ['game_id' => 'key', 'sm_game_name' => 'val', 'cp_game_id' => 'pid']): Collection
    {
        $qb   = TbBaseGameConf::getInstance();
        $data = $qb->asArray()
            ->findAll([
                'is_show' => 1,
                "ORDER" => ["GAME_ID" => "DESC"],
            ], ['game_id', 'sm_game_name', 'cp_game_id']);

        return CollectHelper::crossMap($data, $fieldMap);
    }


    /**
     * 汇总分组集合
     *
     * @param array $options
     *
     * @return Collection
     */
    public function queryGroups(array $options = []): Collection
    {
        $collect = collect();

        // 广告组汇总分组选项
        $collect->put('ad_campaign_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '游戏前端名', 'key' => 'app_show_id'],
            ['val' => '主渠道', 'key' => 'channel_main_id'],
            ['val' => '推广子渠道', 'key' => 'promotion_channel_id'],
            ['val' => '投放人', 'key' => 'user_id'],
            ['val' => '投放部门', 'key' => 'department_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
            ['val' => '账号ID', 'key' => 'account_id'],
            ['val' => '账户名称', 'key' => 'account_name'],
            ['val' => '广告组ID', 'key' => 'campaign_id'],
            ['val' => '系统定向', 'key' => 'dim_user_os'],
        ]);
        // 计划汇总分组选项
        $collect->put('ad_plan_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '游戏前端名', 'key' => 'app_show_id'],
            ['val' => '主渠道', 'key' => 'channel_main_id'],
            ['val' => '推广子渠道', 'key' => 'promotion_channel_id'],
            ['val' => '投放人', 'key' => 'user_id'],
            ['val' => '投放部门', 'key' => 'department_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
            ['val' => '账号ID', 'key' => 'account_id'],
            ['val' => '账户名称', 'key' => 'account_name'],
            ['val' => '广告组ID', 'key' => 'campaign_id'],
            ['val' => '计划ID', 'key' => 'plan_id'],
            ['val' => '系统定向', 'key' => 'dim_user_os'],
        ]);
        // 创意汇总分组选项
        $collect->put('ad_creative_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '游戏前端名', 'key' => 'app_show_id'],
            ['val' => '主渠道', 'key' => 'channel_main_id'],
            ['val' => '推广子渠道', 'key' => 'promotion_channel_id'],
            ['val' => '投放人', 'key' => 'user_id'],
            ['val' => '投放部门', 'key' => 'department_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
            ['val' => '账号ID', 'key' => 'account_id'],
            ['val' => '账户名称', 'key' => 'account_name'],
            ['val' => '广告组ID', 'key' => 'campaign_id'],
            ['val' => '计划ID', 'key' => 'plan_id'],
            ['val' => '创意ID', 'key' => 'creative_id'],
            ['val' => '系统定向', 'key' => 'dim_user_os'],
        ]);
        // 运营首登汇总分组选项
        $collect->put('firstlogin_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '小时', 'key' => 'thour'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '游戏前端名', 'key' => 'app_show_id'],
            ['val' => '主渠道', 'key' => 'channel_main_id'],
            ['val' => '推广子渠道', 'key' => 'channel_id'],
            ['val' => '推广分类', 'key' => 'promotion_id'],
            ['val' => '投放人', 'key' => 'user_id'],
            ['val' => '投放部门', 'key' => 'department_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
        ]);

        $collect->put('newlogin_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '游戏前端名', 'key' => 'app_show_id'],
            ['val' => '主渠道', 'key' => 'channel_main_id'],
            ['val' => '推广子渠道', 'key' => 'channel_id'],
            ['val' => '推广分类', 'key' => 'promotion_id'],
            ['val' => '投放人', 'key' => 'user_id'],
            ['val' => '投放部门', 'key' => 'department_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
        ]);

        $collect->put('ad_month_groups', [
            ['val' => '每月汇总', 'key' => 't_month'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '主渠道', 'key' => 'channel_main_id'],
            ['val' => '推广子渠道', 'key' => 'channel_id'],
            ['val' => '推广分类', 'key' => 'promotion_id'],
            ['val' => '投放人', 'key' => 'user_id'],
            ['val' => '投放部门', 'key' => 'department_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
        ]);

        $collect->put('event_distribute_groups', [
            ['val' => '日期', 'key' => 'tday'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '主渠道', 'key' => 'channel_main_id'],
            ['val' => '推广子渠道', 'key' => 'channel_id'],
            ['val' => '推广分类', 'key' => 'promotion_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
        ]);

        $collect->put('live_room_groups', [
            ['val' => '直播日期', 'key' => 'tday'],
            ['val' => '直播时间', 'key' => 'range_time'],
            ['val' => '主播', 'key' => 'anchor_id'],
            ['val' => '主播团队', 'key' => 'live_team'],
            ['val' => '运营助手', 'key' => 'assistant_id'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '包号', 'key' => 'package_id'],
        ]);

        $collect->put('media_platform_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '媒体平台', 'key' => 'media_platform'],
            ['val' => '业务归属', 'key' => 'business_ownership_name'],
            ['val' => '账号归属', 'key' => 'use_kind_name'],
            ['val' => '运营负责人', 'key' => 'operations_manager'],
        ]);

        $collect->put('media_account_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '媒体平台', 'key' => 'media_platform'],
            ['val' => '业务归属', 'key' => 'business_ownership_name'],
            ['val' => '账号归属', 'key' => 'use_kind_name'],
            ['val' => '账号ID', 'key' => 'account_id'],
            ['val' => '运营负责人', 'key' => 'operations_manager'],
        ]);

        $collect->put('media_video_groups', [
            ['val' => '发布时间', 'key' => 'tday'],
            ['val' => '媒体平台', 'key' => 'media_platform'],
            ['val' => '业务归属', 'key' => 'business_ownership_name'],
            ['val' => '账号归属', 'key' => 'use_kind_name'],
            ['val' => '运营负责人', 'key' => 'operations_manager'],
            ['val' => '账号ID', 'key' => 'account_id'],
            ['val' => '视频话题', 'key' => 'video_topic'],
            ['val' => '媒体视频话题', 'key' => 'media_video_topic'],
            ['val' => '样片ID', 'key' => 'demo_video_id'],
            ['val' => '任务类型', 'key' => 'job_kind'],
            ['val' => '发布脚本', 'key' => 'task_script_name'],
            ['val' => '发布任务ID', 'key' => 'task_id'],
            ['val' => '数据标签', 'key' => 'data_label'],
        ]);

        $collect->put('recharge_summary_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
            ['val' => '支付渠道', 'key' => 'payway'],
        ]);

        $collect->put('payment_voided_groups', [
            ['val' => '退款日期', 'key' => 'tday'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '核心账号', 'key' => 'core_account'],
            ['val' => '角色id', 'key' => 'role_id'],
        ]);

        // 首登留存汇总
        $collect->put('firstlogin_remain_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '游戏前端名', 'key' => 'app_show_id'],
            ['val' => '主渠道', 'key' => 'channel_main_id'],
            ['val' => '推广子渠道', 'key' => 'channel_id'],
            ['val' => '推广分类', 'key' => 'promotion_id'],
            ['val' => '投放人', 'key' => 'user_id'],
            ['val' => '投放部门', 'key' => 'department_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
        ]);

        $collect->put('newlogin_change_groups', [
            ['val' => '统计日期', 'key' => 'tday'],
            ['val' => '游戏原名', 'key' => 'cp_game_id'],
            ['val' => '游戏统计名', 'key' => 'game_id'],
            ['val' => '推广子渠道', 'key' => 'channel_id'],
            ['val' => '包号', 'key' => 'package_id'],
            ['val' => '客户端', 'key' => 'platform_id'],
        ]);

        return $collect;
    }

    /**
     * @param array $options
     *
     * @return Collection
     */
    public function queryDataScope(array $options = []): Collection
    {
        return collect([
            ['val' => '全部数据', 'key' => 0],
            ['val' => '投放数据', 'key' => 1],
            ['val' => '非广告投放', 'key' => 2],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryHasVisualSelect(): Collection
    {
        return collect([
            ['val' => '否', 'key' => 0],
            ['val' => '是', 'key' => 1],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryNoCost(): Collection
    {
        return collect([
            ['val' => '不包含', 'key' => 0],
            ['val' => '包含', 'key' => 1],
        ]);
    }

    /**
     * 获取通用下拉选择查询接口(无权限控制)
     *
     * @param array<string> $options
     *
     * @return Collection
     * @throws \Exception
     */
    public function queryMultiOptions(array $options = []): Collection
    {
        $options = empty($options)
            ? array_keys(static::FIELD_OPTIONS_API)
            : $options;

        $intersectOption = array_intersect_key(static::FIELD_OPTIONS_API, array_flip($options));
        $param           = array_column($intersectOption, 'param');

        $uri        = Uri::getHost() . Uri::URI_MULTI_OPTIONS;
        $httpClient = new Http($uri);
        $result     = \json_decode($httpClient->postJson(\json_encode($param)) ?? '', true);

        if (empty($result['data'])) {
            throw new \RuntimeException('[WARNING]: fail to get multi option value');
        }

        $collect    = collect();
        $resultData = (array)$result['data'];

        foreach ($intersectOption as $k => $item) {
            $matchKeys = $item['match_key'] ?? $k;

            if (is_string($matchKeys)) {
                $matchKeys = Arr::wrap($matchKeys);
            }

            foreach ($matchKeys as $kk) {
                if (!isset($resultData[$kk])) {
                    continue;
                }
                $ddd = $resultData[$kk];

                if (in_array($k, ['cp_game_id:all', 'cp_game_id'])) {
                    foreach ($ddd as $c => &$chill) {
                        if ($chill['key'] == 0) {
                            unset($ddd[$c]);
                        }
                    }
                }

                if (str_contains($k, ':')) {
                    $k = substr($k, 0, strpos($k, ':'));
                }

                $collect->put($k, collect(array_values($ddd)));
                break;
            }// end foreach()
        }// end foreach()

        return $collect;
    }

    /**
     * @param $keygen
     *
     * @return Collection
     * @throws \Exception
     */
    public function getMultiOptions($keygen = null): Collection
    {
        if (is_string($keygen)) {
            $keygen = \explode(',', $keygen);
        } else {
            $keygen = Arr::wrap($keygen);
        }

        $collect = collect();

        if (empty($keygen)) {
            return $collect;
        }

        $optionKey = array_intersect($keygen, array_keys(static::FIELD_OPTIONS_API));

        if (!empty($optionKey)) {
            $r       = $this->queryMultiOptions($optionKey);
            $collect = $collect->merge($r);
        }

        $otherKey = array_intersect(
            array_diff($keygen, $optionKey),
            array_keys(static::FIELD_OPTIONS_LOCAL)
        );

        if (!empty($otherKey)) {
            //数据库查询
//            $cacheKeyList = [
//                "game_id", "channel_id",
//                "promotion_channel_id",
//                "department_id", "user_id",
//                "live:account_id", "live_cost:cost_type",
//                "payway", "media_account_id", "media_platform_id",
//                "demo_video_id", "operations_manager",
//                "task_script_id", "settlement_id",
//                "agent_id", "operate_id",
//                "explain_id", 'payment_virtual:rule_type'
//            ];

            $cacheKeyList = ['reported_deduct_rule'];

            // $cacheKeyList = [];
            foreach ($otherKey as $k) {
                if (in_array($k, $cacheKeyList)) {
                    //需要做缓存的配置
                    $key  = "conf:{$k}";
                    $data = \Plus::$app->redis->get($key);
                    if (!$data) {
                        $func = static::FIELD_OPTIONS_LOCAL[$k];
                        if (!method_exists($this, $func)) {
                            continue;
                        }
                        $data = $this->{$func}();
                        \Plus::$app->redis->setex($key, self::cacheExpire, serialize($data));
                    } else {
                        $data = unserialize($data);
                    }
                } else {
                    $func = static::FIELD_OPTIONS_LOCAL[$k];
                    if (!method_exists($this, $func)) {
                        continue;
                    }
                    $data = $this->{$func}();
                }// end if()

                $collect->put($k, $data);
            }// end foreach()
        }// end if()

        return $collect;
    }

    /**
     * 目标管理类型选项
     *
     * @return Collection
     */
    public function queryGoalType(): Collection
    {
        return collect([
            ['val' => '每月滚动目标', 'key' => 1],
            ['val' => '每月原始目标', 'key' => 0],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryRangeDateDimension(): Collection
    {
        return collect([
            ['val' => '按天汇总', 'key' => 2],
            ['val' => '按周汇总', 'key' => 3],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryGoalDashBoardDataType(): Collection
    {
        return collect([
            ['val' => '消耗', 'key' => 'cost'],
            ['val' => '新用户', 'key' => 'new_user'],
            ['val' => '付费金额', 'key' => 'payment'],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryLiveAccount(): Collection
    {
        return collect((new GeneralOptionServ())->listLiveAccountOptions());
    }

    public function queryEventType(): Collection
    {
        return collect([
            ['val' => '活跃用户', 'key' => 1],
            ['val' => '付费用户', 'key' => 2],
            ['val' => '付费金额', 'key' => 3],
            ['val' => '活跃付费率', 'key' => 991],
            ['val' => '活跃ARPU', 'key' => 992],
        ]);
    }

    public function queryGroupType(): Collection
    {
        return collect([
            ['val' => '自然日', 'key' => 1],
            ['val' => '自然月', 'key' => 2],
            ['val' => '累计天', 'key' => 3],
        ]);
    }

    public function queryDisplayType(): Collection
    {
        return collect([
            ['val' => '数值+百分比', 'key' => 0],
            ['val' => '数值', 'key' => 1],
            ['val' => '百分比', 'key' => 2],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryLiveCostType(): Collection
    {
        return collect((new GeneralOptionServ())->listLiveCostType());
    }

    /**
     * @return Collection
     */
    public function queryOrderStatus(): Collection
    {
        return collect([
            ['val' => '有效订单', 'key' => 1],
            ['val' => '无效订单', 'key' => 0],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryGameStatus(): Collection
    {
        return collect([
            ['val' => '回调成功', 'key' => 1],
            ['val' => '回调失败', 'key' => 0],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryPayCombo(): Collection
    {
        return collect([
            ['val' => '现金', 'key' => 1],
            ['val' => '九玩币', 'key' => 2],
            ['val' => '代金券', 'key' => 3],
        ]);
    }

    public function queryPayWay(): Collection
    {
        $list   = (new OptionServ())->getPayWay();
        $result = collect();
        foreach ($list as $item) {
            $result->push(['val' => $item['name'], 'key' => $item['code']]);
        }

        return $result;
    }

    /**
     * @return Collection
     */
    public function queryTagMode(): Collection
    {
        return collect([
            ['val' => '自动', 'key' => 0],
            ['val' => '手动', 'key' => 1],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryTaggingStatus(): Collection
    {
        return collect([
            ['val' => '内部测试', 'key' => 1],
            ['val' => 'GS测试', 'key' => 2],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryTaggingOperator(): Collection
    {
        $result = (new OptionServ())->getTaggingOperator();
        array_unshift($result, ['val' => '系统打标', 'key' => 0]);

        return collect(Arr::crossMap($result, ['id' => 'key', 'real_name' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryTestDepartment(): Collection
    {
        $result = (new OptionServ())->getOaDepartment();
        $tree   = new Tree($result);
        $re     = $tree->buildTree('0');
        $data   = Arr::pull($re, 0);

        return collect($data['children'] ?: 0);
    }

    public function queryAllDepartment(): Collection
    {
        $result = (new OptionServ())->getAllDepartment();
        return collect(Arr::crossMap($result, ['id' => 'key', 'name' => 'val']));
    }

    public function queryAllUser(): Collection
    {
        $result = (new OptionServ())->getAllUser();
        array_unshift($result, ['real_name' => '系统打标', 'id' => 0]);

        return collect(Arr::crossMap($result, ['id' => 'key', 'real_name' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryMediaPlatform(): Collection
    {
        $result = (new OptionServ())->getMediaPlatform(['status' => 1]);

        return collect(Arr::crossMap($result, ['id' => 'key', 'name' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryMediaAccount(): Collection
    {
        $result = (new LiveAccountServ())->getList([]);

        return collect(Arr::crossMap($result, ['account_id' => 'key', 'account' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryBusinessOwnership(): Collection
    {
        return collect([
            ['val' => '短视频', 'key' => 0],
            ['val' => '直播', 'key' => 1],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryUseKind(): Collection
    {
        return collect([
            ['val' => '手机群控', 'key' => 0],
            ['val' => 'PC群控', 'key' => 1],
            ['val' => '众包群控', 'key' => 2],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryDemoVideoId(): Collection
    {
        $result = (new OptionServ())->getDemoVideoId([], 'demo_video_id');

        return collect(Arr::crossMap($result, ['demo_video_id' => 'key', 'demo_video_title' => 'val']));
    }


    /**
     * @return Collection
     */
    public function queryOperationsManager(): Collection
    {
        $list   = (new LiveAccountServ())->getList([]);
        $result = [];
        foreach ($list as $key => $value) {
            $value['operations_manager1']         = $value['operations_manager'];
            $result[$value['operations_manager']] = $value;
        }

        return collect(Arr::crossMap($result, ['operations_manager1' => 'key', 'operations_manager' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryClassify(): Collection
    {
        return collect([
            ['val' => '用户成长体系', 'key' => 1],
            ['val' => 'VIP维护服务', 'key' => 2],
            ['val' => '运营活动', 'key' => 3],
            ['val' => '公众号活动', 'key' => 4],
            ['val' => '直播营销活动', 'key' => 5],
            ['val' => '不朽大陆用户转移', 'key' => 6],
            ['val' => '私域营销', 'key' => 7],
            ['val' => '内部测试', 'key' => 8],
        ]);
    }

    public function querySettlementId()
    {
        $result = (new OptionServ())->getCustomFieldConf(['FIELD_TYPE' => 'SETTLEMENT']);

        return collect(Arr::crossMap($result, ['field_id' => 'key', 'field_desc' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryAgentId()
    {
        $result = (new OptionServ())->getCustomFieldConf(['FIELD_TYPE' => 'AGENT']);

        return collect(Arr::crossMap($result, ['field_id' => 'key', 'field_desc' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryOperateId()
    {
        $result = (new OptionServ())->getCustomFieldConf(['FIELD_TYPE' => 'OPERATE']);

        return collect(Arr::crossMap($result, ['field_id' => 'key', 'field_desc' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryExplainId()
    {
        $result = (new OptionServ())->getCustomFieldConf(['FIELD_TYPE' => 'EXPLAIN']);

        return collect(Arr::crossMap($result, ['field_id' => 'key', 'field_desc' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryCostIndexType()
    {
        return collect([
            ['val' => '信息流广告', 'key' => 1],
            ['val' => '游戏预约', 'key' => 9999],
        ]);
    }


    /**
     * @return Collection
     */
    public function queryIsAdNew(): Collection
    {
        return collect([
            ['val' => '否', 'key' => 0],
            ['val' => '是', 'key' => 1],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryIsSubAccount(): Collection
    {
        return collect([
            ['val' => '否', 'key' => 0],
            ['val' => '是', 'key' => 1],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryReportingConfStatus(): Collection
    {
        return collect([
            ['val' => '暂停上报', 'key' => 0],
            ['val' => '上报中', 'key' => 1],
        ]);
    }

    public function queryMarketingGoal(): Collection
    {
        return collect([
            //            ['val' => '无', 'key' => 0],
            ['val' => '非直播', 'key' => 1],
            ['val' => '直播', 'key' => 2],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryTaskScript(): Collection
    {
        $list = FakeDB::connection('ddc_platform')
            ->select()
            ->from('ddc_platform.media_publish_task_scripts')
            ->fetchAll();

        return collect(Arr::crossMap($list, ['id' => 'key', 'name' => 'val']));
    }

    /**
     * @return Collection
     */
    public function queryReportResult(): Collection
    {
        return collect([
            ['val' => '成功', 'key' => 1],
            ['val' => '失败', 'key' => 0],
        ]);
    }

    /**
     * @return Collection
     */
    public function queryReportReportContent(): Collection
    {
        return collect([
            ['val' => '激活', 'key' => 'active_upload'],
            ['val' => '注册', 'key' => 'reg_upload'],
            ['val' => '登录', 'key' => 'login_upload'],
            ['val' => '付费', 'key' => 'pay_upload'],
            ['val' => '留存', 'key' => 'remain_upload'],
            ['val' => '创角', 'key' => 'create_role_upload'],
            ['val' => '虚拟付费', 'key' => 'pay_virtual_upload'],
        ]);
    }

    public function queryJobKind(): Collection
    {
        return collect([
            ['val' => '其他', 'key' => 0],
            ['val' => '其他脚本', 'key' => 1],
            ['val' => '普通发布', 'key' => 2],
            ['val' => '投稿发布', 'key' => 3],
            ['val' => '周期任务', 'key' => 4],
            ['val' => '数据抓取', 'key' => 5],
            ['val' => '维护工具', 'key' => 6],
        ]);
    }


    public function querySdkLogEvent(): Collection
    {
        return collect([
            ['val' => '设备激活', 'key' => 'sdk_activate'],
            ['val' => '账号注册', 'key' => 'user_register'],
            ['val' => '账号登录', 'key' => 'user_login'],
            ['val' => '角色登录', 'key' => 'role_login'],
            ['val' => '用户充值', 'key' => 'user_payment'],
            ['val' => '崩溃日志', 'key' => 'sdk_crash'],
            ['val' => '角色升级', 'key' => 'role_rank'],
            ['val' => '城堡升级', 'key' => 'castle_rank'],
            ['val' => '新增创角', 'key' => 'create_role_first'],
        ]);
    }

    public function queryDeductionReportRuleType(): Collection
    {
        return collect([
            ['val' => '次数扣减', 'key' => 1],
            ['val' => '金额扣减', 'key' => 2],
        ]);
    }

    /**
     * @return Collection
     * @throws \app\extension\Exception\ParameterException
     * @throws \Exception
     */
    public function queryPaymentVirtualRuleType(): Collection
    {
        $uri     = Uri::getHost() . Uri::URI_VIRTUAL_PAYMENT_RULES;
        $request = new Request($uri);
        $result  = $request->get(["page" => 1, 'page_size' => 10000]);
        $result  = json_decode($result, true);
        $list    = $result['data']['items'] ?? [];
        $d       = [
            [
                'val' => '手动指定账号',
                'key' => 0,
            ],
        ];

        foreach ($list as $foo) {
            $d[] = [
                'val' => $foo['title'],
                'key' => $foo['id'],
            ];
        }

        return collect($d);
    }

    /**
     * @return Collection
     */
    public function queryIndicatorReferenceFirst(): Collection
    {
        try {
            $data   = (new IndicatorReferenceLogic())->getFirstGradeList();
            $result = [];
            foreach ($data as $k => $d) {
                $result[] = [
                    'val' => $d,
                    'key' => $k,
                ];
            }

            return collect($result);
        } catch (\Exception $e) {
            return collect();
        }
    }

    public function queryUserOs(): Collection
    {
        return collect([
            ['val' => 'IOS', 'key' => 1],
            ['val' => 'ANDROID', 'key' => 2],
            ['val' => '混投', 'key' => 3],
        ]);
    }

    /**
     * 扣量上报规则下拉获取
     * @date 2025/01/09
     * @return Collection
     */
    public function queryDeductReportedRule(): Collection
    {
        $result    = (new BizConfig())->getReportDeduction(['report_type' => 2, 'kind'=> 3]);
        $sdkResult = (new BizConfig())->getReportDeduction(['report_type' => 1, 'kind'=> 3]);
        $items     = array_map(
            fn($item) => array_intersect_key($item, ['id' => 1, 'name' => 1]),
            array_merge($result['data']['items'] ?? [], $sdkResult['data']['items'] ?? [])
        );

        if (empty($items)) {
            return collect([]);
        }

        $data = [];

        foreach ($items as $chill) {
            if (empty($data[$chill['name']])) {
                $data[$chill['name']] = [$chill['id']];
            } else {
                $data[$chill['name']][] = $chill['id'];
            }
        }

        $data = array_combine(array_keys($data), array_map(fn($foo) => implode(',', $foo), $data));
        $r    = [];

        foreach ($data as $k => $v) {
            $r[] = [
                'val' => $k,
                'key' => $v,
            ];
        }

        return collect($r);
    }

    /**
     * 上报状态下拉选项
     *
     * @date 2025/01/09
     * @return Collection
     */
    public function queryReportedStatus(): Collection
    {
        return collect([
            ['val' => '上报成功', 'key' => 1],
            ['val' => '上报失败', 'key' => -1],
            ['val' => '不上报', 'key' => 0],
        ]);
    }

    /**
     * 上报行为下拉选项
     *
     * @date 2025/01/09
     * @return Collection
     */
    public function queryReportedBehavior(): Collection
    {
        return collect([
            ['val' => '正常上报', 'key' => 0],
            ['val' => '次数扣减', 'key' => 1],
            ['val' => '金额扣减', 'key' => 2],
            ['val' => '虚拟订单上报', 'key' => 3],
        ]);
    }

    /**
     * 留存类型下拉选项
     * @return Collection
     */
    public function queryRetainType(): Collection
    {
        return collect([
            ['val' => '新增当天付费', 'key' => 1],
            ['val' => '新增7天内付费', 'key' => 2],
        ]);
    }
}

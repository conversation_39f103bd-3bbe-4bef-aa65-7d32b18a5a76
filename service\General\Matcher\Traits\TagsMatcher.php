<?php

namespace app\service\General\Matcher\Traits;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\Convert;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

trait TagsMatcher
{
    /**
     * @param $params
     *
     * @return string
     */
    public function matchPackageTags($params): string
    {
        if (empty($params['package_id_tags'])) return '';

        $packageTags = Convert::convertInString($params['package_id_tags']);
        $field       = $this->getField('package_id');
        $subSql      = $this->getTagSubSql($packageTags, 'packages');

        return "{$field} IN ({$subSql})";
    }

    /**
     * @param $params
     *
     * @return string
     */
    public function matchChannelTags($params): string
    {
        if (empty($params['channel_id_tags'])) return '';

        $channelTags = Convert::convertInString($params['channel_id_tags']);
        $field       = $this->getField('channel_id');
        $subSql      = $this->getTagSubSql($channelTags, 'app_channel');

        if ($field != 'power.channel_id') {
            return "({$field} IN ({$subSql}) or (power.channel_id IN ({$subSql})))";
        }
        else {
            return "{$field} IN ({$subSql})";
        }
    }

    /**
     * @param $params
     *
     * @return string
     */
    public function matchChannelMainTags($params): string
    {
        if (empty($params['channel_main_id_tags'])) return '';

        $channelMainTags = Convert::convertInString($params['channel_id_tags']);
        $field           = $this->getField('channel_main_id');
        $subSql          = $this->getTagSubSql($channelMainTags, 'package_channel_main');

        return "{$field} IN ({$subSql})";
    }

    /**
     * @param $params
     * @return string
     */
    public function matchGameIdTags($params): string
    {
        if (empty($params['game_id_tags'])) return '';

        $gameTags = Convert::convertInString($params['game_id_tags']);
        $field    = $this->getField('game_id');
        $subSql   = $this->getTagSubSql($gameTags, 'games');

        return "{$field} IN ({$subSql})";
    }


    /**
     * @param SelectQuery |QueryInterface $qb
     * @param array $params
     *
     * @return void
     */
    public function matchPackageTagsQb(&$qb, array &$params)
    {
        if (empty($params['package_id_tags'])) return;

        $packageTags = Convert::convertInString($params['package_id_tags']);
        $field       = $this->getReflectField('package_id');
        $subSql      = $this->getTagSubSql($packageTags, 'packages');

        $qb->where($field, 'IN', new Fragment('(' . $subSql .')'));
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return string|void
     */
    public function matchChannelTagsQb(&$qb, array &$params)
    {
        if (empty($params['channel_id_tags'])) return;

        $channelTags = Convert::convertInString($params['channel_id_tags']);
        $field       = $this->getReflectField('channel_id');
        $subSql      = $this->getTagSubSql($channelTags, 'app_channel');


        if (is_array($field)) {
            $qb->where(static function (SelectQuery $select) use ($field, $subSql) {
                foreach ($field as $f) {
                    $select->orWhere($f, 'IN', new Fragment('(' . $subSql .')'));
                }
            });
        }
        else {
            $qb->where($field, 'IN', new Fragment('(' . $subSql .')'));
        }
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return void
     */
    public function matchChannelMainTagsQb(&$qb, array &$params)
    {
        // channel_main_id_tags
        if (empty($params['channel_main_id_tags'])) return;

        $channelTags = Convert::convertInString($params['channel_main_id_tags']);
        $field       = $this->getReflectField('channel_main_id_tags');
        $subSql      = $this->getTagSubSql($channelTags, 'package_channel_main');

        if (is_array($field)) {
            $qb->where(static function (SelectQuery $select) use ($field, $subSql) {
                foreach ($field as $f) {
                    $select->orWhere($f, 'IN', new Fragment('(' . $subSql .')'));
                }
            });
        }
        else {
            $qb->where($field, 'IN', new Fragment('(' . $subSql .')'));
        }
    }

    /**
     * @param $qb
     * @param array $params
     * @return void
     */
    public function matchGameTagsQb(&$qb, array &$params)
    {
        if (empty($params['game_id_tags'])) return;

        $gameTags = Convert::convertInString($params['game_id_tags']);
        $field    = $this->getReflectField('game_id');
        $subSql   = $this->getTagSubSql($gameTags, 'games');

        if (is_array($field)) {
            $qb->where(static function (SelectQuery $select) use ($field, $subSql) {
                foreach ($field as $f) {
                    $select->orWhere($f, 'IN', new Fragment('(' . $subSql .')'));
                }
            });
        }
        else {
            $qb->where($field, 'IN', new Fragment('(' . $subSql .')'));
        }
    }

    /**
     * @param $tags
     * @param $type
     *
     * @return string
     */
    protected function getTagSubSql($tags, $type): string
    {
        return "SELECT 
                    DISTINCT DATA_ID 
                from base_conf_platform.biz_tags 
                where TAG_ID IN ({$tags}) and TABLE_NAME = '{$type}'";
    }
}
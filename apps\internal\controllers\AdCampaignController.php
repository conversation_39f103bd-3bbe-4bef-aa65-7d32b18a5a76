<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Helpers\IndicatorCalcHelpers;
use app\apps\internal\Helpers\IndicatorsHelpers;
use app\apps\internal\Traits\AdIndexCalculators;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\internal\Traits\InternalRoutes;
use app\extension\Constant\AD\AdEnum;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ArrayZoom;
use app\extension\Support\Helpers\LtvCalculators;
use app\extension\Support\Helpers\RemainCalculators;
use app\extension\Support\Helpers\Zakia;
use app\logic\advertise\AdCreativeLtvLogic;
use app\logic\advertise\AdCreativePassLogic;
use app\logic\advertise\AdDashLogic;
use app\service\Advertiser\AdDashProvider;
use app\service\AdvertiserData\AdCreativePassIndex;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\AdvertiserData\CreativeLtvIndex;
use app\service\AdvertiserData\CreativePayPerDailyIndex;
use app\service\AdvertiserData\CreativePayRemainIndex;
use app\service\AdvertiserData\CreativeRemainIndex;
use app\service\AdvertiserData\RealtimeCreativeIndex;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;

/**
 * 广告组维度
 */
class AdCampaignController extends BaseController
{

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        ini_set('memory_limit', '4096M');
        ini_set('max_execution_time', '300');

        $paginate = [
            'page'      => $params->pull('page', 1),
            'page_size' => $params->pull('page_size', 100),
        ];

        if ($params->has('groups')) {
            $groups = Arr::wrap($params->pull('groups'));
        }
        else {
            $groups = array_keys(AdEnum::RELATE_AD_CAMPAIGN);
        }

        $fullGroups = ColumnManager::matchRelationByGroups(ColumnManager::returnAdRelation(AdEnum::AD_CAMPAIGN), $groups);
        $fullGroups = array_diff($fullGroups, ['plan_name', 'campaign_name', 'creative_name']);
        $cols       = $params->pull('cols', []);
        if (!empty($cols)) {
            $cols = str_replace(['plan_name', 'campaign_name', 'creative_name'], '', $cols);
            $cols = array_values(array_filter(Arr::wrap(\explode(',', $cols))));
        }

        $dimensionCols = ColumnManager::matchRelationByGroups(ColumnManager::returnAdRelation(AdEnum::AD_CAMPAIGN), array_keys(ColumnManager::returnAdRelation(AdEnum::AD_CAMPAIGN)));
        $dimensionCols = array_merge($dimensionCols, ['package_tags', 'channel_tags', 'game_tags']);
        $sort          = [];
        if ($params->has('sort')) {
            $sf = $params->pull('sort');

            if (!empty($cols)) {
                if (!in_array($sf, $cols)) {
                    goto DEFAULT_SORT;
                }
                else {
                    $order     = $params->pull('order', 'desc') == 'ascend' ? 'asc' : 'desc';
                    $sort[$sf] = $order;
                }
            }
        }
        else {
            DEFAULT_SORT:
            if (in_array('tday', $fullGroups)) {
                $sort = array_merge($sort, ['tday' => 'asc']);
            }

            if (!empty($cols)) {
                $calcCols = array_filter($cols, fn($item) => !in_array($item, $dimensionCols));

                if (empty($calcCols))
                    $sort[$fullGroups[0]] = 'desc';
                else
                    $sort[array_shift($calcCols)] = 'desc';
            }
        }

        $options = $params->toArray();
        // 外部账号仅能显示自己账号归属的数据
        if (\Plus::$service->admin->isOutsiders()) {
            $options['user_id'] = \Plus::$service->admin->getUserId();
        }

        $adLogic = new AdDashLogic();
        $result  = $adLogic->dataDash($options, $fullGroups, $paginate, $sort, $cols);

        if (!empty($result['list'])) {
            $list             = &$result['list'];
            $groupIndex       = array_fill_keys($groups, '');
            $configBasic      = new BasicServ();
            $optionsServ      = new GeneralOptionServ();
            $constConfCollect = $configBasic->getMultiOptions([
                'platform_id', 'promotion_id', 'department_id', 'user_id',
                'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
            ])->put('promotion_channel_id', $optionsServ->listChannelOptions());

            $processFn   = [];
            $processFn[] = static function (&$target) use ($groupIndex) {
                $rowOption            = array_intersect_key($target, $groupIndex);
                $target['row_option'] = $rowOption;
            };
            $processFn[] = $this->replaceColumnDefine($constConfCollect);

            // 包号标签追加
            if (in_array('package_id', $groups)) {
                $listPackages  = array_filter(array_column($list, 'package_id'));
                $packageTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'package_id', 'id' => $listPackages]) ?? []);

                if (!empty($packageTagMap)) {
                    $packageTagAppendFn = function (&$target) use ($packageTagMap) {
                        if (
                            !empty($target['package_id'])
                            && $target['package_id'] != '-'
                        ) {
                            $packageId              = $target['package_id'];
                            $target['package_tags'] = array_values($packageTagMap[$packageId] ?? []);
                        }
                    };

                    array_unshift($processFn, $packageTagAppendFn);
                }
            }
            // 推广子渠道标签
            if (
                in_array('promotion_channel_id', $groups)
                || in_array('package_id', $groups)
            ) {
                $listChannel   = array_column($list, 'promotion_channel_id');
                $channelTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'channel_id', 'id' => $listChannel]) ?? []);

                if (!empty($channelTagMap)) {
                    $channelTagAppendFn = function (&$target) use ($channelTagMap) {
                        if (!empty($target['promotion_channel_id']) && $target['promotion_channel_id'] != '-') {
                            $channelId              = $target['promotion_channel_id'];
                            $target['channel_tags'] = array_values($channelTagMap[$channelId] ?? []);
                        }
                    };

                    array_unshift($processFn, $channelTagAppendFn);
                }
            }
            // 游戏统计名标签
            if (
                in_array('game_id', $groups)
                || in_array('package_id', $groups)
            ) {
                $listGames  = array_column($list, 'game_id');
                $gameTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'game_id', 'id' => $listGames]) ?? []);

                if (!empty($gameTagMap)) {
                    $gameTagAppendFn = function (&$target) use ($gameTagMap) {
                        if (!empty($target['game_id']) && $target['game_id'] != '-') {
                            $gameId              = $target['game_id'];
                            $target['game_tags'] = array_values($gameTagMap[$gameId] ?? []);
                        }
                    };
                    array_unshift($processFn, $gameTagAppendFn);
                }
            }
            // section: 补全广告信息
            $adLogic->fillAdDimensionName($list, $fullGroups);
            $this->processingLine($list, $groups, $processFn);
        }

        return $result;
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $baseCollect = [
            'tday', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id',
            'promotion_channel_id', 'promotion_id', 'platform_id',
            'department_id', 'user_id',
        ];

        $adBaseCollect = [
            'ad_account', 'account_id', 'campaign_name', 'campaign_id',
            'plan_name', 'plan_id', 'creative_name', 'creative_id', 'account_name',
        ];

        $newBaseCollect = [
            'show', 'click', 'click_show_percent', 'activate_device',
            'new_user', 'create_role_new', 'create_role_percent',
            'first_online_time_avg', 'online_time_avg', 'new_user_real_percent',
        ];

        $payCollect = [
            'pay_new_user_7days', 'pay_money_new',
            'pay_user_new_percent', 'pay_count_new',
            'pay_permeation_percent', 'arpu_new_user',
            'pay_new_user', 'pay_frequency_7days', 'pay_frequency_avg_7days',
            'pay_user', 'pay_count', 'pay_user', 'pay_user_new', 'arpu_new_user', 'pay_money',
            'arppu_new_user',
        ];

        $costCollect   = [
            'cost', 'cost_discount', 'cpc', 'qian_cost', 'new_user_cost',
            'create_role_cost', 'pay_frequency_7days_cost', 'remain_1_cost', 'convert_cost',
            'download_start_cost_percent', 'activate_cost', 'register_cost',
        ];
        $passerCollect = [
            'pass_level_1', 'pass_level_2', 'pass_level_3', 'pass_level_4',
        ];

        $mediaCollect = [
            'lp_click_percent', 'lp_download', 'lp_view',
            'convert', 'download_start', 'download_start_percent', 'download_finish_percent',
            'install_finish_percent', 'activate', 'activate_percent', 'activate_install_percent',
            'register', 'register_percent', 'play_duration_3s_percent', 'play_time_avg',
            'show_convert_percent', 'convert_percent',
        ];


        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'ad_base', 'label' => '广告信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],
                    ['value' => 'pay_info', 'label' => '用户付费指标'],
                    ['value' => 'ad_cost', 'label' => '广告成本指标'],
                    ['value' => 'user_passer', 'label' => '用户通过率'],
                    ['value' => 'media_index', 'label' => '媒体转化目标'],
                    ['value' => 'ltv_index', 'label' => 'ltv指标'],
                    ['value' => 'roi_index', 'label' => 'roi指标'],
                    ['value' => 'remain_index', 'label' => '留存指标'],
                    ['value' => 'pay_remain_index', 'label' => '付费留存指标'],
                    ['value' => 'tags', 'label' => '标签'],
                    ['value' => 'our_side_ad', 'label' => '手盟侧(完整)'],
                    ['value' => 'back_side_ad', 'label' => '手盟侧(上报)'],
                    ['value' => 'media_side_ad', 'label' => '媒体侧'],
                ],
            ],
        ];


        $fields = $this->tableFields($params->toArray());

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'] ?? '';
            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $adBaseCollect)) {
                $field['classify'] = ['attrs', 'ad_base'];
            }
            elseif (in_array($dIndex, $newBaseCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
            elseif (in_array($dIndex, $payCollect)) {
                $field['classify'] = ['attrs', 'pay_info'];
            }
            elseif (in_array($dIndex, $costCollect)) {
                $field['classify'] = ['attrs', 'ad_cost'];
            }
            elseif (in_array($dIndex, $passerCollect)) {
                $field['classify'] = ['attrs', 'user_passer'];
            }
            elseif (in_array($dIndex, $mediaCollect)) {
                $field['classify'] = ['attrs', 'media_index'];
            }
            elseif (strstr($dIndex, 'ltv')) {
                $field['classify'] = ['attrs', 'ltv_index'];
            }
            elseif (strstr($dIndex, 'roi')) {
                $field['classify'] = ['attrs', 'roi_index'];
            }
            elseif (strstr($dIndex, 'remain_') && !strstr($dIndex, 'pay_remain_')) {
                $field['classify'] = ['attrs', 'remain_index'];
            }
            elseif (strstr($dIndex, 'pay_remain_')) {
                $field['classify'] = ['attrs', 'pay_remain_index'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * LTV合并方法
     *
     * @return \Closure
     */
    protected function mergeLtvFunc(): \Closure
    {
        return function (&$target, $source) {
            if (!isset($source['day_type'])) return;

            $dayInfo = $source['day_type'];

            foreach ($dayInfo as $i => $foo) {
                if (!isset($target['day_type'][$i])) {
                    Arr::set(
                        $target,
                        implode('.', ['day_type', $i]),
                        ['money_all' => 0.00, 'money' => 0.00]
                    );
                }

                $target['day_type'][$i]['money_all'] += $foo['money_all'];
                $target['day_type'][$i]['money']     += $foo['money'];
            }
        };
    }

    protected function initializeValueFunc($infoColumn): \Closure
    {
        $infoColumn = array_flip($infoColumn);

        return function ($data) use ($infoColumn) {
            return array_intersect_key($data, $infoColumn);
        };
    }
}
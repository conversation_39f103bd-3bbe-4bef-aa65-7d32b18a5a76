<?php
/* Smarty version 5.0.0-rc3, created on 2025-06-30 11:07:31
  from 'file:sql/advertise/ad_income_dash/dash.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.0.0-rc3',
  'unifunc' => 'content_6861ff734ccf60_91640937',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'e9c48cc78348f50c0e738e0f04936916af8de3d1' => 
    array (
      0 => 'sql/advertise/ad_income_dash/dash.tpl',
      1 => 1749631986,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:sql/advertise/ad_income_dash/dash_match.tpl' => 1,
  ),
))) {
function content_6861ff734ccf60_91640937 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = '/mnt/e/project/spy2.0-api/public/templates/sql/advertise/ad_income_dash';
?>select
<?php if ((null !== ($_smarty_tpl->getValue('is_summary') ?? null))) {?>
    '汇总'
<?php } else { ?> dt <?php }?> as tday, a1.game_id, a2.cp_game_id,
sum(pull_cnt)                                              as pull_cnt,
sum(exposure_cnt)                                          as exposure_cnt,
round(SUM(exposure_cnt) / SUM(pull_cnt) * 100, 2)          as exposure_percent,
sum(click_cnt)                                             as click_cnt,
round(SUM(click_cnt) / SUM(exposure_cnt) * 100, 2)         as click_percent,
round(SUM(income_rmb) * 1000 / SUM(exposure_cnt), 2)       as ecpm,
sum(income_rmb)                                            as income_rmb
from origin_platform.tb_ad_income_crawler a1 left join  base_conf_platform.tb_base_game_conf a2 on a1.game_id = a2.game_id
<?php $_smarty_tpl->renderSubTemplate("file:sql/advertise/ad_income_dash/dash_match.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), (int) 0, $_smarty_current_dir);
?>

<?php if ((null !== ($_smarty_tpl->getValue('group_by') ?? null))) {?>
    GROUP BY <?php echo $_smarty_tpl->getValue('group_by');?>

<?php }?>

<?php if ((null !== ($_smarty_tpl->getValue('sort') ?? null))) {?>
    order by
    <?php $_smarty_tpl->assign('is_first', 1, false, NULL);?>
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('sort'), 'foo', false, 'k');
$foreach0DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('k')->value => $_smarty_tpl->getVariable('foo')->value) {
$foreach0DoElse = false;
?>
        <?php if ($_smarty_tpl->getValue('is_first') == 1) {?>
            <?php echo $_smarty_tpl->getValue('k');?>
 <?php echo $_smarty_tpl->getValue('foo');?>

            <?php $_smarty_tpl->assign('is_first', 0, false, NULL);?>
        <?php } else { ?>
            , <?php echo $_smarty_tpl->getValue('k');?>
 <?php echo $_smarty_tpl->getValue('foo');?>

        <?php }?>
    <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}?>

<?php if (!empty($_smarty_tpl->getValue('paginate'))) {?>
    <?php $_smarty_tpl->assign('page_size', $_smarty_tpl->getValue('paginate')['page_size'], false, NULL);?>
    <?php $_smarty_tpl->assign('page', $_smarty_tpl->getValue('paginate')['page'], false, NULL);?>
    limit <?php echo $_smarty_tpl->getValue('page_size');?>
 offset <?php echo ($_smarty_tpl->getValue('page')-1)*$_smarty_tpl->getValue('page_size');?>

<?php }?>

<?php }
}

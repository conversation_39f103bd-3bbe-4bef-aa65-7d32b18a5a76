<?php

namespace app\extension\Support\Helpers;

use app\apps\internal\Helpers\IndicatorsHelpers;
use app\extension\Support\Collections\Arr;

class LtvCalculators
{
    /**
     * @param array $ltvList
     * @param \Closure|null $getDenominatorFn
     * @param int $ltvType
     * @param string $ltvTarget
     * @param string $roiTarget
     * @param string $prefix
     * @param string $roiPrefix
     * @return \Closure
     */
    public static function calcNode(
        array    $ltvList,
        \Closure $getDenominatorFn = null,
        int      $ltvType = 0,
        string   $ltvTarget = 'new_user',
        string   $roiTarget = 'cost_discount',
        string   $prefix = 'ltv_',
        string   $roiPrefix = 'roi_'
    ): \Closure
    {
        $ltvCalcFn = static::getLtvShowFn($ltvType);
        $roiCalcFn = static::getRoiShowFn();

        return function (&$target, &$context, $key)
        use ($ltvList, $getDenominatorFn, $ltvCalcFn, $roiCalcFn, $prefix, $ltvTarget, $roiTarget, $roiPrefix) {
            $ltvMap = $ltvList[$key] ?? [];
            preg_match_all('/' . $prefix . '\d+/', implode(',', array_keys($ltvMap)), $keyMatches);

            $keys   = Arr::pull($keyMatches, 0, []);
            $ltvMap = array_intersect_key($ltvMap, array_flip($keys));
            // 把匹配到的LTV列表存放
            $context['ltv_key_map'] = $ltvMap;

            foreach ($ltvMap as $k => $foo) {
                $ii = (int)str_replace($prefix, '', $k);

                if ($ii == 1000) {
                    // 累计LTV
                    $target[$prefix . $ii]    = $ltvCalcFn(floatval($foo), floatval($target[$ltvTarget] ?? 0.00));
                    $target[$roiPrefix . $ii] = $roiCalcFn(floatval($foo), floatval($target[$roiTarget] ?? 0.00));
                }
                else {
                    [
                        'ltv' => $ltvDenominator,
                        'roi' => $roiDenominator,
                    ] = $getDenominatorFn($target, $ii);

                    $target[$prefix . $ii]    = $ltvCalcFn((floatval($foo) ?? 0.00), (floatval($ltvDenominator) ?? 0.00));
                    $target[$roiPrefix . $ii] = $roiCalcFn((floatval($foo) ?? 0.00), (floatval($roiDenominator) ?? 0.00));
                }
            }
        };
    }

    /**
     * @param int $ltvType
     * @return \Closure
     */
    public static function getLtvShowFn(int $ltvType = 0): \Closure
    {

        if ($ltvType === 1) {
            return fn($money) => $money;
        }
        else {
            return function ($money, $user) {
                if (empty($money) || empty($user)) {
                    return 0.00;
                }

                return round($money / $user, 2);
            };
        }
    }

    /**
     * @return \Closure
     */
    public static function getRoiShowFn(): \Closure
    {
        return function ($money, $cost) {
            if (empty($money) || empty($cost)) {
                return '0.00%';
            }

            return round($money / $cost * 100, 2) . '%';
        };
    }

    /**
     * LTV增量计算
     * @return \Closure
     */
    public static function ltvIncrementalEachRow(): \Closure
    {
        return function (&$target) {
            $keys = static::catchLtvKeys($target);

            if (empty($keys)) return;

            rsort($keys);

            foreach ($keys as $kk) {
                $ii          = (int)str_replace('ltv_', '', $kk);
                $target[$kk] = round($target[$kk] - ($target['ltv_' . ($ii - 1)] ?? 0.00), 2);
            }
        };
    }

    /**
     * @return \Closure
     */
    public static function ltvMultipleEachRow(): \Closure
    {
        return function (&$target) {
            $keys = static::catchLtvKeys($target);

            if (empty($keys)) return;

            $ltv1 = $target['ltv_1'] ?? 0.00;

            foreach ($keys as $kk) {
                $target[$kk] = IndicatorsHelpers::division($target[$kk] ?? 0, $ltv1);
            }
        };
    }

    /**
     * @param $data
     * @return mixed
     */
    public static function catchLtvKeys($data)
    {
        preg_match_all('/ltv_\d+/', implode(',', array_keys($data)), $keyMatches);

        return Arr::pull($keyMatches, 0, []);
    }

    /**
     * 组合LTV维度
     *
     * @param array $data
     * @param \Closure $mergeFn
     * @param string $hitDay
     * @return array
     * @throws \Exception
     */
    public static function ltvCombo(
        array $data, \Closure $mergeFn, string $hitDay = 'tday'
    ): array
    {
        $result = [];

        foreach ($data as $foo) {
            $nodeKey  = $mergeFn($foo);
            $dayNode  = $foo['day_type'];
            $moneyAll = $foo['money_all'] ?? 0.00;
            $money    = $foo['money'] ?? 0.00;

            if (empty($result[$nodeKey])) {
                $result[$nodeKey] = ['nodes' => []];
            }

            $chill = &$result[$nodeKey]['nodes'];

            if (!empty($chill[$dayNode])) {
                $chill[$dayNode]['money_all'] += $moneyAll;
                $chill[$dayNode]['money']     += $money;
            }
            else {
                $chill[$dayNode] = [
                    'day_type'  => $dayNode,
                    'money_all' => $moneyAll,
                    'money'     => $money
                ];
            }
        }

        return $result;
    }


}
<?php
/**
 * 陌陌数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class Momo extends AdBaseInterface
{
    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $http = new Http($info['CALLBACK_URL']);
        $res  = $http->get();
        //记录上报结果
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'momo';
        $logInfo['request']      = json_encode(['url' => $info['CALLBACK_URL']]);
        $logInfo['response']     = $res;
        $resContent              = json_decode($res, true);

        if ($resContent['ec'] == 200) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        //写日志
        $this->log($info, $logInfo, $res, $info['CALLBACK_URL']);
    }

    public function uploadRegister($info, $ext = [])
    {
        // TODO: Implement uploadRegister() method.
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadPay($info, $ext = [])
    {
        // TODO: Implement uploadPay() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

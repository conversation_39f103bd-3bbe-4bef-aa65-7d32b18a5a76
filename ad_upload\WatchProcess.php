<?php
declare(ticks=1);

namespace app\ad_upload;

use Plus\CLI\ProcessManage;

/**
 * 定时任务，每分钟运行一次
 * 运行上报媒体
 * <AUTHOR>
 */
class WatchProcess extends ProcessManage
{

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->config = [
            $this->conf(8535, 'active,register,pay'), //快手-微信小程序
            $this->conf(70, 'active,register,pay'), //B站-微信小程序
            $this->conf(1105, 'create_role,pay'), //广点通-微信小程序
            $this->conf(1107, 'pay'), //今日头条-微信小程序
            $this->conf(1113, 'register,active,pay,login'), //百度-微信小程序

            $this->conf(2, 'register,active,pay'), //头条feed
            $this->conf(3, 'register,active,pay'), //百度feed
            $this->conf(4, 'register,active,pay'), //广点通
            $this->conf(6, 'register,active,pay'), //快手feed
            $this->conf(8603, 'register,active,pay'), //星广联投AD(达人)-微信小程序
            $this->conf(8621, 'register,active,pay'), //星广联投AD(达人)-微信小程序
            $this->conf(8721, 'active,pay'), //小红书-微信小程序
        ];
    }

    /**
     * 单条配置
     * @param int    $channelId 渠道id
     * @param string $actions   动作
     * @return array
     */
    private function conf(int $channelId, string $actions = 'active,pay,register,create_role')
    {
        $actions = $actions.',pay_virtual_upload';
        return [
            'file' => AdUpload::class, 'num' => 1, 'param' => "channel_id=$channelId actions=$actions",
        ];
    }
}

<?php

namespace app\logic\operator;

use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use app\service\OperationData\PackageBaseServ;

class NewLoginBaseLogic
{
    use ColumnsInteract;

    /**
     * 运营新增数据查询
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \Exception
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        if (empty($groups)) {
            $groups = ['tday', 'package_id'];
        }

        $readCols = [
            'tday', 'cp_game_id', 'game_id', 'package_id', 'channel_id', 'channel_main_id'
        ];

        if (
            !empty($params['range_date_start'])
            && !empty($params['range_date_end'])
        ) {

            if ($timeDimension == ConstHub::DIMENSION_MONTH) {
                $rangeStart = date('Y-m-01', strtotime($params['range_date_start']));
                $rangeEnd   = date('Y-m-t', strtotime($params['range_date_end']));
                $params['tday'] = [$rangeStart, $rangeEnd];

            }
            else {
                $params['tday'] = [
                    $params['range_date_start'], $params['range_date_end']
                ];
            }

            sort($params['tday']);
            unset($params['range_date_start'], $params['range_date_end']);
        }

        $serv = new PackageBaseServ();
        $mode = $serv::MODE_ALL ^ $serv::MODE_DETAIL_QB ^ $serv::MODE_SUMMARY_QB;
        $r    = $serv->getBaseInfo($params, $groups, $paginate, $sort, $readCols, $mode);

        $configBasic      = new BasicServ();
        $constConfCollect = $configBasic
            ->getMultiOptions([
                                  'platform_id', 'promotion_id', 'department_id', 'user_id',
                                  'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
                              ])->put('channel_id', (new GeneralOptionServ())->listChannelOptions());

        $resetFn          = $this->resetGroupsCols(
            ColumnManager::groupOperatorRelation($groups), $groups, ConstHub::OPERATOR_FIXED_INFO_COLS
        );

        $process = new ProcessLine();
        $process->addProcess(function (&$target) {
            $target['pay_user_new_percent'] = '0.00%';
            $target['active_arpu']          = 0.00;
            $target['active_pay_percent']   = '0.00%';
            // 新增用户付费率
            if (!empty($target['pay_user_newlogin_all']) && !empty($target['new_user'])) {
                $target['pay_user_new_percent'] = round($target['pay_user_newlogin_all'] / $target['new_user'] * 100, 2) . '%';
            }
            // 活跃ARPU
            if (!empty($target['active_user']) && !empty($target['pay_money_all'])) {
                $target['active_arpu'] = round($target['pay_money_all'] / $target['active_user'], 2);
            }
            // 活跃付费率
            if (!empty($target['active_user']) && !empty($target['pay_user_all'])) {
                $target['active_pay_percent'] = round($target['pay_user_all'] / $target['active_user'] * 100, 2) . '%';
            }
        });

        if (!empty($r['list'])) {
            $listProcess = clone $process;

            $listProcess
                ->addProcess(fn(&$target) => $target['tday'] = $target['group_day'] ?? '')
                ->addProcess($this->resetGroupsCols(ColumnManager::groupOperatorRelation($groups), $groups, ConstHub::OPERATOR_FIXED_INFO_COLS))
                ->addProcess($this->replaceColumnDefine($constConfCollect))
                ->addProcess($resetFn);

            $list = &$r['list'];
            $listProcess->run($list);
        }

        if (!empty($r['summary'])) {
            $summaryRow     = [&$r['summary']];
            $summaryProcess = clone $process;
            $summaryProcess->run($summaryRow);
        }

        return $r;
    }
}
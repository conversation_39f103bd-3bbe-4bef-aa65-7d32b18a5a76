<?php
/**
 * UC-微信小程序 上报, 复刻UC上报
 */

namespace app\ad_upload\channels;

class UcWx extends Uc
{
    /**
     * 激活上报
     *
     * @param array $info
     * @param array $ext
     * @return void
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE', 'uc_wx');
    }

    /**
     * 注册上报
     * @param array $info
     * @param array $ext
     * @return void
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG', 'uc_wx');
    }

    /**
     * 付费上报
     * @param array $info
     * @param array $ext
     * @return void
     */
    public function uploadPay($info, $ext = [])
    {
        // 上报扣减检测
        $this->upload($info, 'PAY', 'uc_wx');
    }

    /**
     * 创角
     *
     * @param array $info
     * @param array $ext
     * @return void
     */
    public function uploadCreateRole($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE', 'uc_wx');
        $this->upload($info, 'REG', 'uc_wx');
        $this->upload($info, 'CREATE_ROLE', 'uc_wx');
    }
}

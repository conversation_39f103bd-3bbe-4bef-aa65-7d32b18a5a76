<?php

namespace app\logic\log;

use app\service\Advertiser\AdDimensionProvider;
use app\service\ConfigService\BasicServ;
use app\service\Logs\LogReportedOrderProvider;

/**
 * 付费上报查询看板接口数据处理逻辑层
 * phpcs:disable
 */
class LogReportedOrderLogic
{
    /**
     * @param array $params
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \Smarty\Exception
     * @throws \Exception
     */
    public function dashboardData(
        array $params,
        array $paginate = [],
        array $sort = []
    ): array {
        $provider = new LogReportedOrderProvider();
        $result   = $provider->getDashboardData(...func_get_args());

        if (!empty($result['list'])) {
            $chill   = &$result['list'];
            $planMap = $this->loadPlanName($chill);
            $this->formatData($chill, $planMap);
        }

        return $result;
    }

    /**
     * @param array $list
     * @return array
     */
    private function loadPlanName(array $list): array
    {
        $planId = array_column($list, 'source_plan_id');
        $planId = array_filter(array_unique($planId));

        return array_column((new AdDimensionProvider())->loadDimensionName($planId), 'plan_name', 'plan_id');
    }

    /**
     * @param       $data
     * @param array $planMap
     * @return void
     * @throws \Exception
     */
    private function formatData(&$data, array $planMap = [])
    {
        $optionsCollect      = (new BasicServ())->getMultiOptions([
            'cp_game_id:all', 'channel_id', 'reported_deduct_rule', 'reported_status', 'reported_behavior',
        ])->toArray();
        $cpGameMap           = array_column($optionsCollect['cp_game_id'] ?? [], 'val', 'key');
        $channelMap          = array_column($optionsCollect['channel_id'] ?? [], 'val', 'key');
        $reportStatusMap     = array_column($optionsCollect['reported_status'] ?? [], 'val', 'key');
        $reportedBehaviorMap = array_column($optionsCollect['reported_behavior'] ?? [], 'val', 'key');
        $reportDeductMap     = array_column($optionsCollect['reported_deduct_rule'] ?? [], 'val', 'key');
        $sourceDimensionMap  = [
            '1' => '来自客户端归因',
            '2' => '服务端广告归因',
            '3' => '服务端包号归因,空为自然量',
        ];

        if (!empty($reportDeductMap)) {
            $reportDeductMap    = array_flip($reportDeductMap);
            $newReportDeductMap = [];
            foreach ($reportDeductMap as $kk => $foo) {
                $foo = \explode(',', $foo);
                foreach ($foo as $chill) {
                    $newReportDeductMap[$chill] = $kk;
                }
            }

            $reportDeductMap = $newReportDeductMap;
        }

        $fnMap = [
            fn(&$chill) => $chill['plan_name']           = $planMap[$chill['source_plan_id']] ?? '',
            fn(&$chill) => $chill['source_channel_name'] = $channelMap[$chill['source_channel_id']] ?? '',
            fn(&$chill) => $chill['cp_game_name']        = $cpGameMap[$chill['cp_game_id']] ?? '',
            fn(&$chill) => $chill['source_dimension']    = $sourceDimensionMap[$chill['source_dimension']] ?? '',
            fn(&$chill) => $chill['reported_status']     = $reportStatusMap[$chill['reported_status']] ?? '',
            fn(&$chill) => $chill['reported_behavior']   = $reportedBehaviorMap[$chill['reported_behavior']] ?? '',
            fn(&$chill) => $chill['reported_rule_id']    = $reportDeductMap[$chill['reported_rule_id']] ?? '',
        ];

        foreach ($data as $kk => &$chill) {
            $condition               = json_decode($chill['reported_behavior_rule'], true);
            $chill['condition_name'] = $condition['rule']['conditionName'] ?? '';
            foreach ($fnMap as $fn) {
                if (is_callable($fn)) {
                    $fn($chill, $kk);
                }
            }
        }
    }
}

<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Helpers\Calculator;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Helpers\IndexCalculators;
use app\apps\internal\Helpers\IndicatorsHelpers;
use app\apps\internal\Helpers\RemainCalculator;
use app\apps\internal\Traits\AdOfflineDash;
use app\apps\internal\Traits\AdRouteRequest;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\extension\Support\Helpers\TimeUtil;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\AdvertiserData\CreativeRemainIndex;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\AdvertiserData\RealtimePlanIndex;
use app\service\ConfigService\BasicServ;
use app\service\ConfigService\Tables\AdPlanRemain;
use app\service\General\GeneralOptionServ;
use MathParser\Interpreting\Evaluator;
use MathParser\StdMathParser;


/**
 *
 * @AdPlanRemainController 广告计划留存查询
 * @route                  /internal/ad-plan_remain/data
 */
class AdPlanRemainController extends BaseTableController
{
    use AdRouteRequest, ColumnsInteract, AdOfflineDash;

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];

        $groups = Arr::wrap($params->pull('groups'));
        if (!$groups) {
            $tab = $params->pull('tab', "plan");
            if ($tab == "plan") {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'plan_id']; // 默认组合
            }
            else {
                $groups = ['tday', 'promotion_channel_id', 'package_id', 'campaign_id']; // 默认组合
            }
        }
        $today         = new \DateTime();
        $maxDayDiff    = days_apart($params->get('range_date_start'), $today);
        $timeDimension = (int)$params->get('range_date_dimension', ConstHub::DIMENSION_DAY);

        if ($params->has('sort')) {
            $sort = $params->pull('sort');

            if ($params->has('order')) {
                $sort .= ' ' . ($params->pull('order') == 'ascend' ? 'asc' : 'desc');
            }
        }
        else {
            $sort = ['tday', 'new_user desc'];
        }

        $sort = Arr::wrap($sort);

        if (ConstHub::DIMENSION_WEEK === $timeDimension) {
            [
                'begin' => $dateStart,
                'end'   => $dateEnd,
            ] = TimeUtil::divideWeekByRangeDate(
                $params->pull('range_date_start', $today),
                $params->pull('range_date_end', $today)
            );

            $params->put('range_date_start', $dateStart);
            $params->put('range_date_end', $dateEnd);
        }
        elseif (ConstHub::DIMENSION_MONTH === $timeDimension) {
            $params->put(
                'range_date_start',
                (new \DateTime($params->get('range_date_start', date('Y-m-d'))))->format('Y-m-01')
            );

            $params->put(
                'range_date_end',
                (new \DateTime($params->get('range_date_end', date('Y-m-d'))))->format('Y-m-t')
            );
        }
        $remainType = $params->pull('remain_type');
        $options    = $params->toArray();

        // 外部账号仅能显示自己账号归属的数据
        if (\Plus::$service->admin->isOutsiders()) {
            $options['user_id'] = \Plus::$service->admin->getUserId();
        }

        $baseServ = new RealtimePlanIndex();

        $baseResult = $baseServ->listBase(
            $options, ['page' => $page, 'page_size' => $pageSize],
            $groups, $sort
        );

        $constConfCollect = (new BasicServ())->getMultiOptions([
            'platform_id', 'promotion_id', 'department_id', 'user_id',
            'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
        ])->put('promotion_channel_id', (new GeneralOptionServ())->listChannelOptions());

        $resetFunc = $this->resetGroupsCols(
            ColumnManager::groupAdRelation($groups),
            $groups,
            array_diff(ConstHub::AD_FIXED_INFO_COLS, ['creative_id', 'creative_name'])
        );

        $info        = &$baseResult['list'];
        $remainServ  = new CreativeRemainIndex();
        $processLine = new ProcessLine();
        $clearFn     = function (&$target) {
            unset($target['remain_info'], $target['new_user_n'], $target['cost_discount_n']);
        };

        $backupFn = static function (&$target, $k) {
            $target['department_id'] = $target['new_department_id'] ?: null;
        };

        $processLine
            ->prependEachRow($backupFn)
            // 拆解每行对应的搜索条件
            ->prependEachRow($this->parseMatchForEachRow($options, $groups))
            // 每行追加留存信息
            ->addProcess($this->addRemainInfo([$remainServ, 'remainInfoByPlan'], $groups))
            // 替换ID信息字段
            ->addProcess($this->replaceColumnDefine($constConfCollect))
            // 清理汇总分组不必要的字段
            ->addProcess($resetFunc);

        if (!in_array('tday', $groups) && $timeDimension === ConstHub::DIMENSION_DAY) {
            $getUserFn = IndexCalculators::getSingleValue('new_user');
        }
        else {
            $processLine->addProcess(
                $this->addEachRowInfo([$baseServ, 'listByDaily'], ['new_user', 'cost_discount'], $groups, ['operator' => '<'])
            );

            $getUserFn = IndexCalculators::getValueInCollectByN('new_user_n');
        }

        $lastRemainFn = function (&$target, $key) use ($remainType) {
            $getFn    = IndexCalculators::remainValueGet($remainType);
            $user     = $target['new_user'] ?? 0;
            $loginNum = $target['remain_current'] ?? 0;

            $target['remain_current'] = $getFn($loginNum, $user);
        };

        // 留存计算
        $processLine
            ->addProcess(RemainCalculator::calcEachRow($getUserFn, ['max_days' => $maxDayDiff], $remainType))
            ->addProcess(IndexCalculators::remainDayCalculators(ConstHub::DIMENSION_DAY, $groups, $maxDayDiff - 1))
            ->addProcess($lastRemainFn);
        // 清理
        $processLine->addProcess($clearFn);

        // 补全广告信息
        [$adInfo, $dimension] = (new RealtimeIndex())->getAdInfoByGroups($info, $groups);
        if ($dimension) {
            $processLine->addProcess(function (&$target, $k) use ($adInfo, $dimension) {
                $fields = [];
                switch ($dimension) {
                    case "campaign_id":
                        $fields = ["CAMPAIGN_NAME"];
                        break;
                    case "plan_id":
                        $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME"];
                        break;
                    case "creative_id":
                        $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME", "PLAN_ID", "CREATIVE_NAME"];
                        break;
                }
                foreach ($fields as $field) {
                    $key = $target[$dimension];
                    if ($dimension == "creative_id") {
                        $key = $target["plan_id"] . "_" . $target["creative_id"];
                    }
                    $target[strtolower($field)] = $adInfo[$key][$field] ?? null;
                }
            });
        }

        $processLine->run($info);

        /**
         * ### 汇总处理
         */
        $summaryRow          = &$baseResult['summary'];
        $summaryRemain       = $remainServ->remainInfoByPlan($options, ['tday', 'day_type']);
        $lastUpdateTimeMap   = array_column($summaryRemain, 'last_update_time');
        $baseResult['time']  =empty($lastUpdateTimeMap) ? '' : max($lastUpdateTimeMap);
        $summaryRemainByDate = [];
        $summaryRowRemain    = [];

        foreach ($summaryRemain as $foo) {
            $dDay    = $foo['tday'];
            $dayType = $foo['day_type'];

            !isset($summaryRowRemain[$dayType])
                ? $summaryRowRemain[$dayType] = ['day_type' => $dayType, 'login_num' => $foo['login_num'] ?? 0]
                : $summaryRowRemain[$dayType]['login_num'] += ($foo['login_num'] ?? 0);


            if (isset($summaryRemainByDate[$dDay])) {
                if ($dayType > $summaryRemainByDate[$dDay]['day_type'] ?? 0) {
                    $summaryRemainByDate[$dDay]['login_num'] = $foo['login_num'];
                }
            }
            else {
                $summaryRemainByDate[$dDay] = ['day_type' => $dayType, 'login_num' => $foo['login_num'] ?? 0];
            }
        }

        $summaryRowRemain          = array_column($summaryRowRemain, null, 'day_type');
        $summaryRow['remain_days'] = max($maxDayDiff - 1, 0);

        if (
            ConstHub::DIMENSION_DAY === $timeDimension
            && ($params->get('range_date_start') === $params->get('range_date_end'))
        ) {
            $summaryGetUser = IndexCalculators::getSingleValue('new_user');
        }
        else {
            $summaryInfoByDate = $baseServ->listByDaily($options, [], ['tday'], null, [], false);
            $summaryNInfo      = Calculator::cumulativeOnDays($summaryInfoByDate['list'] ?? [], ['new_user'], '<');
            $summaryRow        = array_merge($summaryRow, $summaryNInfo);

            $summaryGetUser = IndexCalculators::getValueInCollectByN('new_user_n');
        }

        RemainCalculator::calcRemain($summaryRow, $summaryRowRemain, $summaryGetUser, $remainType);

        if (
            empty($summaryRow['remain_current'])
            || empty($summaryRow['new_user'])
        ) {
            $summaryRow['remain_current'] = '0.00%';
        }
        else {
            $summaryRow['remain_current'] = number_format(
                    math_eval('x/y*100', ['x' => $summaryRow['remain_current'], 'y' => $summaryRow['new_user']]), 2
                ) . '%';
        }

        $clearFn($summaryRow);

        return $baseResult;
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $baseCollect   = [
            'tday', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id',
            'promotion_channel_id', 'promotion_id', 'platform_id',
            'department_id', 'user_id',
        ];
        $adBaseCollect = [
            'ad_account', 'account_id', 'campaign_name', 'campaign_id',
            'plan_name', 'plan_id', 'creative_name', 'creative_id', 'account_name',
        ];

        $newBaseCollect = [
            'new_user', 'remain_days', 'remain_current',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'ad_base', 'label' => '广告信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],

                    ['value' => 'remain_group_1', 'label' => '次留-30留'],
                    ['value' => 'remain_group_2', 'label' => '45留-180留'],
                    ['value' => 'remain_group_3', 'label' => '210留-360留'],
                    ['value' => 'remain_group_4', 'label' => '360留-720留'],
                ],
            ],
        ];

        $fields = $this->tableFields($params->toArray());

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'] ?? '';
            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $adBaseCollect)) {
                $field['classify'] = ['attrs', 'ad_base'];
            }
            elseif (in_array($dIndex, $newBaseCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }


    protected function registerParams($key = null): Collection
    {
        return $this
            ->baseParams()
            ->merge([
                ['field' => 'groups'],
                ['field' => 'range_date_dimension', 'default' => ConstHub::DIMENSION_DAY],
                ['field' => 'remain_type', 'default' => RemainCalculator::REMAIN_VAL_DEFAULT], // remain值类型
                ['field' => 'marketing_goal'], // 营销场景
            ]);
    }


    /**
     * @return array
     * @throws \Exception
     * @todo 过渡
     *
     */
    public function simpleSearchAction(): array
    {
        $params     = $this->wrapParams($this->request);
        $groups     = ['tday'];
        $baseServ   = new RealtimePlanIndex();
        $remainServ = new CreativeRemainIndex();
        $options    = $params->toArray();

        $baseRe     = $baseServ->simpleListByDay($options, [], $groups);
        $remainRe   = $remainServ->simpleList($options);
        $info       = &$baseRe['list'];
        $remainInfo = IndicatorsHelpers::dimReduction($remainRe['list'], 'day_type', ['tday'], ['login_num']);
        $getUserFn  = IndexCalculators::getSingleValue('new_user');

        $processLine = new ProcessLine();
        $processLine->addProcess(function (&$target) use ($remainInfo) {
            $tDay = $target['tday'];

            if (!empty($remainInfo[$tDay])) {
                $target['remain_info'] = $remainInfo[$tDay]['day_type'] ?? [];
            }
        });

        $processLine
            ->addProcess(RemainCalculator::calcEachRow($getUserFn, ['max_days' => 7], 0))
            ->addProcess(IndexCalculators::remainDayCalculators(ConstHub::DIMENSION_DAY, $groups, 7))
            ->addProcess(IndexCalculators::lastRemainCalculators())
            ->addProcess(function (&$target) {
                $tDay                  = $target['tday'];
                $target['remain_days'] = days_apart(new \DateTime(), $tDay) - 1;
            })
            ->addProcess(function (&$target) {
                unset($target['remain_info']);
            });

        $processLine->run($info);

        /**
         * 汇总
         */
        $summaryRow       = &$baseRe['summary'];
        $summaryRowRemain = array_column($remainRe['summary'], null, 'day_type');
        $summaryNInfo     = Calculator::cumulativeOnDays($info, ['new_user'], '<');
        $summaryRow       = array_merge($summaryRow, $summaryNInfo);
        $summaryGetUser   = IndexCalculators::getValueInCollectByN('new_user_n');

        RemainCalculator::calcRemain($summaryRow, $summaryRowRemain, $summaryGetUser, 0);

        $dayMax = [];
        foreach ($remainRe['list'] as $item) {
            $tDay    = $item['tday'];
            $dayType = $item['day_type'];
            $value   = $item['login_num'];

            if (!isset($dayMax[$tDay])) {
                $dayMax[$tDay] = ['day_type' => $dayType, 'login_num' => $value];
            }
            else {
                if ($dayType > ($dayMax[$tDay]['day_type'] ?? 0)) {
                    $dayMax[$tDay] = ['day_type' => $dayType, 'login_num' => $value];
                }
            }
        }
        $remainCurrent = array_sum(array_column($dayMax, 'login_num'));

        if (empty($summaryRow['new_user']) || empty($remainCurrent)) {
            $summaryRow['remain_current'] = '0.00%';
        }
        else {
            $summaryRow['remain_current'] = number_format(
                    math_eval('x/y*100', ['x' => $remainCurrent, 'y' => $summaryRow['new_user']]), 2
                ) . '%';
        }

        $summaryRow['remain_days'] = days_apart(date('Y-m-d'), $params->get('range_date_start')) - 1;
        $fields                    = (new AdPlanRemain())->getSimpleFields($options)->toArray();
        $summaryRow['tday']        = '汇总';

        return $this->success(array_merge($baseRe, ['fields' => $fields]));
    }
}
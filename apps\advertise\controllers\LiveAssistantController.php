<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\Traits\InternalRoutes;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\CollectHelper;
use app\service\AdvertiseLive\LiveAssistantServ;
use Plus\MVC\Controller\JsonController;

/**
 * 运营助手管理
 *
 * @route /advertise/live-assistant/*
 */
class LiveAssistantController extends JsonController
{
    use InternalRoutes;

    /**
     *  运营助手列表
     *
     * @return array
     */
    public function listAction(): array
    {
        $params = $this->wrapParams(\Plus::$app->request);
        $serv   = new LiveAssistantServ();
        $list   = $serv->getList(['state' => 1], [], [], ['sorted' => 'DESC']);
        $list   = Arr::crossMap($list, ['id' => 'key', 'assistant_name' => 'val']);

        return $this->success([
            'list' => $list,
        ]);
    }

    /**
     * 更新排序
     *
     * @return array
     */
    public function updateSortedAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);
        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $orderList  = $params['list'];
        $serv       = new LiveAssistantServ();
        $updateList = [];
        $count      = \count($orderList);

        foreach ($orderList as $id) {
            $t            = [
                'id'     => $id,
                'sorted' => $count--,
            ];
            $updateList[] = $t;
        }

        try {
            $serv->updateMultiById($updateList);
        }
        catch (\Throwable $e) {
            return $this->error('更新失败');
        }

        return $this->success([]);
    }

    /**
     * 新增运营助手
     *
     * @return array
     */
    public function saveAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        if (empty(trim($params['assistant_name']))) {
            return $this->error('缺失必要参数');
        }

        $serv = new LiveAssistantServ();

        try {
            $params['assistant_name'] = trim($params['assistant_name']);
            $params['operator_id']    = \Plus::$service->admin->getUserId();
            $serv->insert($params);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

    /**
     * 更新接口
     *
     * @return array
     */
    public function updateAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);
        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $serv = new LiveAssistantServ();

        try {
            $data                = $params;
            $data['operator_id'] = \Plus::$service->admin->getUserId();
            $serv->updateMultiById([$data]);
        }
        catch (\Throwable $e) {
            return $this->error('更新失败', ['trace' => $e->getMessage()]);
        }

        return $this->success([]);
    }

    /**
     * @return array
     */
    public function delAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $id = $params['id'] ?? null;
        if (empty($id)) {
            return $this->error('参数缺失');
        }
        $serv = new LiveAssistantServ();

        try {
            $serv->removeByIds(Arr::wrap($id));
        }
        catch (\Throwable $e) {
            return $this->error('删除失败');
        }

        return $this->success([]);
    }

}
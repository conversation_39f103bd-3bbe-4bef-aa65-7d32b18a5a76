<?php
declare(ticks=1);

namespace app\daemon;

use app\models\baseConfPlatform\LivePackagesSpy;
use app\service\Queue;
use PhpAmqpLib\Message\AMQPMessage;
use Plus\CLI\DaemonProcess;

/**
 * 同步直播平台数据
 * @date create by 2025/04/28
 */
class SyncLivePlatform extends DaemonProcess
{
    /**
     * @return void
     */
    public function run()
    {
        echo "SyncLivePlatform==>" . PHP_EOL;
        try {
            $queue = \Plus::$service->queue->liveSyncQueue;
            $queue->subscribe([$this, 'job'], 'live_packages', Queue::LIVE_PACKAGE_SPY);
        }
        catch (\Exception $e) {
            \Plus::$app->log->error($e->getTraceAsString(), [], 'live_packages_sync');
        }
        echo "==>SyncLivePlatform, 运行终止" . PHP_EOL;
    }

    /**
     * @param AMQPMessage $message
     * @return void
     */
    public function job(AMQPMessage $message)
    {
        $rawMessage = $message->getBody();
        $message->ack();
        @\Plus::$app->log->info($rawMessage, [], 'live_packages_sync');
        $data = json_decode($rawMessage, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            @\Plus::$app->log->error(json_last_error_msg(), [], 'live_packages_sync');
            return;
        }

        if (empty($data)) {
            @\Plus::$app->log->error("empty data", [], 'live_packages_sync');
            return;
        }

        try {
            $method    = $data['type'] ?? '';
            $dataBody  = $data['body'] ?? [];
            $primaryId = $dataBody['id'] ?? 0;
            if (empty($primaryId)) {
                @\Plus::$app->log->error("empty primaryId|body:{$rawMessage}", [], 'live_packages_sync');
                return;
            }
            $livePackageSpyModel = new LivePackagesSpy();

            switch ($method):
                case 'create':
                    if (!empty($livePackageSpyModel->findById($primaryId)->getRawData())) {
                        @\Plus::$app->log->error("exists primaryId|body:{$rawMessage}", [], 'live_packages_sync');
                        break;
                    }
                    $livePackageSpyModel->id                    = $primaryId;
                    $livePackageSpyModel->product_id            = $dataBody['product_id'] ?? 0;
                    $livePackageSpyModel->product_name          = $dataBody['product_name'] ?? '';
                    $livePackageSpyModel->product_source        = $dataBody['product_source'] ?? 2;
                    $livePackageSpyModel->product_source_show   = $dataBody['product_source_show'] ?? '';
                    $livePackageSpyModel->platform              = $dataBody['platform'] ?? 0;
                    $livePackageSpyModel->platform_show         = $dataBody['platform_show'] ?? '';
                    $livePackageSpyModel->package_id_and        = $dataBody['package_id_and'] ?? '';
                    $livePackageSpyModel->package_id_ios        = $dataBody['package_id_ios'] ?? '';
                    $livePackageSpyModel->download_url_and      = $dataBody['download_url_and'] ?? '';
                    $livePackageSpyModel->ios_package_type      = $dataBody['ios_package_type'] ?? 0;
                    $livePackageSpyModel->ios_package_type_show = $dataBody['ios_package_type_show'] ?? '';
                    $livePackageSpyModel->download_url_ios      = $dataBody['download_url_ios'] ?? '';
                    $livePackageSpyModel->plan_name             = $dataBody['plan_name'] ?? '';
                    $livePackageSpyModel->cooperate_id          = $dataBody['cooperate_id'] ?? 0;
                    $livePackageSpyModel->cooperate_show        = $dataBody['cooperate_show'] ?? '';
                    $livePackageSpyModel->sub_cooperate_id      = $dataBody['sub_cooperate_id'] ?? 0;
                    $livePackageSpyModel->sub_cooperate_show    = $dataBody['sub_cooperate_show'] ?? '';
                    $livePackageSpyModel->landing_page          = $dataBody['landing_page'] ?? '';
                    $livePackageSpyModel->channel_classify      = $dataBody['channel_classify'] ?? '';
                    $livePackageSpyModel->leader_show           = $dataBody['leader_show'] ?? '';
                    $livePackageSpyModel->ios_attribution_type  = $dataBody['ios_attribution_type'] ?? 0;
                    $livePackageSpyModel->channel_type          = $dataBody['channel_type'] ?? 0;
                    $livePackageSpyModel->plan_id               = $dataBody['plan_id'] ?? 0;
                    $livePackageSpyModel->insert();
                    $this->doubleShot($dataBody);
                    break;
                case 'update':
                    $livePackageSpyModel->updateByWhere([
                        'product_id'            => $dataBody['product_id'] ?? 0,
                        'product_name'          => $dataBody['product_name'] ?? '',
                        'product_source'        => $dataBody['product_source'] ?? '',
                        'product_source_show'   => $dataBody['product_source_show'] ?? '',
                        'platform'              => $dataBody['platform'] ?? '',
                        'platform_show'         => $dataBody['platform_show'] ?? '',
                        'package_id_and'        => $dataBody['package_id_and'] ?? '',
                        'package_id_ios'        => $dataBody['package_id_ios'] ?? '',
                        'download_url_and'      => $dataBody['download_url_and'] ?? '',
                        'ios_package_type'      => $dataBody['ios_package_type'] ?? '',
                        'ios_package_type_show' => $dataBody['ios_package_type_show'] ?? '',
                        'download_url_ios'      => $dataBody['download_url_ios'] ?? '',
                        'plan_name'             => $dataBody['plan_name'] ?? '',
                        'cooperate_id'          => $dataBody['cooperate_id'] ?? '',
                        'cooperate_show'        => $dataBody['cooperate_show'] ?? '',
                        'sub_cooperate_id'      => $dataBody['sub_cooperate_id'] ?? '',
                        'sub_cooperate_show'    => $dataBody['sub_cooperate_show'] ?? '',
                        'landing_page'          => $dataBody['landing_page'] ?? '',
                        'channel_classify'      => $dataBody['channel_classify'] ?? '',
                        'leader_show'           => $dataBody['leader_show'] ?? '',
                        'ios_attribution_type'  => $dataBody['ios_attribution_type'] ?? '',
                        'channel_type'          => $dataBody['channel_type'] ?? '',
                        'plan_id'               => $dataBody['plan_id'] ?? '',
                    ], ['id' => $primaryId]);
                    $this->doubleShot($dataBody);
                    break;
                case 'delete':
                    $livePackageSpyModel->delete(['id' => $primaryId]);
                    $this->doubleDeleteShot($primaryId);
                    break;
                default:
                    @\Plus::$app->log->error("method not found|body:{$rawMessage}", [], 'live_packages_sync');
            endswitch;
        }
        catch (\Exception $e) {
            @\Plus::$app->log->error($e->getTraceAsString(), [], 'live_packages_sync');
            return;
        }
    }

    /**
     * 双写
     *
     * @param $data
     * @return void
     */
    public function doubleShot($data)
    {
        $data = array_intersect_key($data, array_flip([
            'id', 'product_id', 'product_name', 'product_source', 'product_source_show',
            'platform', 'platform_show', 'package_id_and', 'package_id_ios', 'download_url_and',
            'ios_package_type', 'ios_package_type_show', 'download_url_ios', 'plan_name', 'cooperate_id',
            'cooperate_show', 'sub_cooperate_id', 'sub_cooperate_show', 'landing_page', 'channel_classify',
            'leader_show', 'ios_attribution_type', 'channel_type', 'plan_id',
        ]));

        $columns = \implode(', ', array_keys($data));
        $values  = "'" . implode("', '", array_values($data)) . "'";

        $sql = "INSERT INTO base_conf_platform.live_packages_spy ({$columns}) VALUES ({$values})";

        try {
            \Plus::$app->base_conf_platform_doris_new->query($sql)->execute();
        }
        catch (\Exception $e) {
            @\Plus::$app->log->error($e->getTraceAsString(), [], 'live_packages_sync');
        }
    }

    /**
     * @param $primaryId
     * @return void
     */
    public function doubleDeleteShot($primaryId)
    {
        $sql = "DELETE FROM base_conf_platform.live_packages_spy WHERE id = {$primaryId}";
        try {
            \Plus::$app->base_conf_platform_doris_new->query($sql)->execute();
        }
        catch (\Exception $e) {
            @\Plus::$app->log->error($e->getTraceAsString(), [], 'live_packages_sync');
        }
    }
}
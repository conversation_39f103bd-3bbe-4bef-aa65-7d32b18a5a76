<?php

namespace app\apps\api\controllers;

use app\extension\Exception\ParameterException;
use app\models\baseConfPlatform\TbOathConf;
use app\service\Admin;
use Plus\MVC\Controller\JsonController;

/**
 * Class Oath
 * <AUTHOR>
 */
class OathController extends JsonController
{


    /**
     * get access token
     * @param array $data 请求参数
     * @return array
     */
    public function getAccessTokenAction()
    {
        $code = $this->getValue("code");
        $time = $this->getValueInt("time");
        $sign = $this->getValue("sign");
        if (empty($code) || empty($time) || empty($sign)) {
            throw new ParameterException("参数错误");
        }
        //check sign
        if ((time() - 10 * 60) > $time) {
            throw new ParameterException("sign无效");
        }
        $key = (new TbOathConf())->getkey($code);
        if (!$key) {
            throw new ParameterException("sign无效");
        }
        if (md5(sha1($code . $time . $key)) != $sign) {
            throw new ParameterException("sign无效");
        }
        //token
        $token = (new Admin())->generateAccessToken(["username"=>$code,"type"=>"api"]);
        return $this->success([
            "access_token" => $token,
        ]);
    }

}

<?php

namespace app\service\OriginData;

use app\extension\FakeDB\FakeDB;
use Spiral\Database\Injection\Parameter;

/**
 *
 */
class ClickLogServ
{
    const QB_MODE_ALL     = 99;
    const QB_MODE_SV_CONF = 2;

    /**
     * 获取点击信息
     *
     * @param array $params
     * @param array $paginate
     *
     * @return array
     */
    public function getClickInfo(array $params, array $paginate = []): array
    {
        $rangeDate = $params['range_date'] ?? date('Y-m-d');

        $qb = $this->getQueryBuilder($params);

        if (!empty($paginate['page_size'])) {
            $limit = $paginate['page_size'];
            $qb->limit($limit);
        }

        $qb->where('t_base.time', 'between', $rangeDate . ' 00:00:00', $rangeDate . ' 23:59:59');

        if (!empty($params['package_id'])) {
            $qb->where('t_base.package_id', $params['package_id']);
        }

        if (!empty($params['plan_id'])) {
            $qb->where('t_base.aid', $params['plan_id']);
        }

        if (!empty($params['channel_id'])) {
            $qb->where('t_base.channel_id', $params['channel_id']);
        }

        if (!empty($params['id[!]'])) {
            $idNotIn = $params['id[!]'];
            $qb->where('t_base.id', 'not in', new Parameter($idNotIn));
        }

        $qb->columns(['t_base.*']);

        echo (clone $qb)->__toString() . PHP_EOL;

        return $qb->fetchAll();
    }

    /**
     * 获取查询结构体
     *
     * @param     $options
     * @param int $mode
     *
     * @return \Spiral\Database\Query\SelectQuery
     */
    private function getQueryBuilder($options, int $mode = -1): \Spiral\Database\Query\SelectQuery
    {
        $allMode   = static::QB_MODE_ALL | static::QB_MODE_SV_CONF;
        $paramMode = $mode | $allMode;

        $db        = $this->getConn();
        $rangeDate = $options['range_date'] ?? date('Y-m-d');
        $table     = $this->getTable($rangeDate);

        return $db->select()->from($table . ' as t_base');
    }

    /**
     * @param string|null $date
     *
     * @return string
     */
    private function getTable(?string $date): string
    {
        $table = 'origin_platform.tb_ad_click_log_';

        if (empty($date)) {
            $table .= \date('W');
        }
        else {
            $table .= \date('W', strtotime($date));
        }

        return $table;
    }

    /**
     * 获取点击表数组
     * @param string $time 时间
     * @param int $day N天前
     * @return array
     */
    public static function getAdClickTableForTime($time = NULL, $day = 30)
    {
        if (empty($time)) {
            $time = date("Y-m-d");
        }
        $prefix = "tb_ad_click_log_";
        $dt_end = strtotime($time);
        $dt_start = strtotime(date("Y-m-d", $dt_end) . " -{$day} day");

        //日期列表
        $dateArr = [];
        while ($dt_end >= $dt_start) {
            $dateArr[] = date("Y-m-d", $dt_end);
            $dt_end = strtotime('-1 day', $dt_end);
        }

        // 过滤时间(只保留30天), 从10月02日开始有数据表
        $filterTime = date('Y-m-d', strtotime('-28 days'));
        $tableTime = '2023-10-02';
        $filterTime = $filterTime <= $tableTime ? $tableTime : $filterTime;


        //日表
        $table = [];
        foreach ($dateArr as $date) {
            $w = date("Ymd", strtotime($date));
            if ($date < $filterTime) {
                continue;
            }
            $table[$w] = $prefix . $w;
        }

        return $table;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('origin_platform');
    }
}
<?php

namespace app\apps\internal\Traits;

use app\apps\internal\Helpers\Calculator;
use app\apps\internal\Helpers\ConstHub;
use app\apps\operator\Helpers\ConstFirstLogin;
use app\service\OperationData\BasicLtvIndex;
use app\service\OperationData\FirstLoginIndex;
use app\service\OperationData\NewLoginIndex;

trait OfflineDash
{
    /**
     * @param array $params
     * @param array $groups
     * @param string[] $baseInfoKeys
     *
     * @return \Closure
     */
    public function addInfoEachRow(array $params, array $groups, array $baseInfoKeys = []): \Closure
    {
        $timeDimension = (int)$params['range_date_dimension'] ?? ConstFirstLogin::DIMENSION_DAY;

        /**
         * @var array|mixed $target processLine::run中的循环每行的数据
         */
        return function (&$target) use ($params, $groups, $timeDimension, $baseInfoKeys) {
            $this->addInfoForSingleRow($target, $params, $groups, $timeDimension, $baseInfoKeys);
        };
    }

    /**
     * @throws \Exception
     */
    protected function addInfoForSingleRow(
        &$target, array $params, $groups, $timeDimension = 0, $baseInfoKeys = []
    )
    {
        $today      = new \DateTime();
        $groupIndex = array_fill_keys($groups, 0);

        // 提取汇总分组的主要字段参数
        $options = array_intersect_key($target, array_diff_key($groupIndex, ['tday' => '']));

        // 时间提取处理
        if (in_array('tday', $groups)) {
            if ($timeDimension === ConstFirstLogin::DIMENSION_WEEK) {
                $rangeDate                   = explode('/', $target['tday']);
                $options['range_date_start'] = $rangeDate[0] ?? $today;
                $options['range_date_end']   = $rangeDate[1] ?? $today;
            }
            elseif ($timeDimension === ConstFirstLogin::DIMENSION_MONTH) {
                $month                       = $target['tday'];
                $monthStart                  = $month . '-01';
                $options['range_date_start'] = $monthStart;
                $options['range_date_end']   = date('Y-m-d', strtotime($monthStart . ' +1month -1day'));
            }
            else {
                $options['range_date_start'] = $options['range_date_end'] = $target['tday'];
            }
        }

        // 合并剩余选择参数
        $otherOption = array_diff_key($params, $options);
        $options     = array_merge($options, $otherOption);

        // 查询每行LTV详情
        $ltvInfo            = $this->getLtvInfoForEveryRow($options, $groups);
        var_dump($ltvInfo);exit();
        $target['ltv_info'] = array_column($ltvInfo['list'] ?? [], null, 'day_type');

        if (
            !in_array('tday', $groups)
            || ConstFirstLogin::DIMENSION_DAY !== $timeDimension
        ) {
            $infoByEveryDay = $this->getBaseInfoForEveryRowByEveryDate($options, $groups);

            if (!empty($infoByEveryDay)) {
                if (isset($params['range_date_dimension'])) {
                    $timeDimension = $params['range_date_dimension'];
                    if ($timeDimension == ConstHub::DIMENSION_DAY && isset($params['less_pay_date'])) {
                        $referDate = (new \DateTime($params['less_pay_date']))->add(new \DateInterval('P1D'))->format('Y-m-d');
                    }
                }

                $nInfo  = Calculator::cumulativeOnDays($infoByEveryDay, $baseInfoKeys, '<=', 'tday', ($referDate ?? null));
                $target = array_merge($target, $nInfo);
            }

        }
    }

    /**
     *
     * @param        $params
     * @param        $groups
     * @param string $servKey
     *
     * @return array
     * @throws \Exception
     */
    protected function getLtvInfoForEveryRow($params, $groups, string $servKey = 'ltv'): array
    {
        $serv = $this->getServFormStorage($servKey);

        if (is_null($serv)) return [];

        if ($serv instanceof BasicLtvIndex) {
            return $serv->ltvInfo($params, $groups, false);
        }

        return [];
    }

    /**
     * @param        $params
     * @param        $groups
     * @param string $servKey
     *
     * @return array|false
     */
    protected function getBaseInfoForEveryRowByEveryDate($params, $groups, string $servKey = 'base')
    {
        if (!in_array('tday', $groups)) {
            $groups[] = 'tday';
        }

        $serv = $this->getServFormStorage($servKey);

        if (is_null($serv)) return [];

        if (
            $serv instanceof FirstLoginIndex
            || $serv instanceof NewLoginIndex
        ) {
            return $serv->getInfoByFixedDate($params, $groups);
        }

        return [];
    }
}
<?php
declare(ticks=1);

namespace app\ad_upload_tmp;

use Plus\CLI\ProcessManage;

/**
 * 定时任务，每分钟运行一次
 * 运行上报媒体
 * <AUTHOR>
 */
class WatchProcess extends ProcessManage
{

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->config = [
            $this->conf(2, 'register,active,pay'), //头条feed
        ];
    }

    /**
     * 单条配置
     * @param int    $channelId 渠道id
     * @param string $actions   动作
     * @return array
     */
    private function conf(int $channelId, string $actions)
    {
        return [
            'file' => AdUpload::class, 'num' => 1, 'param' => "channel_id=$channelId actions=$actions package_ids=70560099",
        ];
    }
}

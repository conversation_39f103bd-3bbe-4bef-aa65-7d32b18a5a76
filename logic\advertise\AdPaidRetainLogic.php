<?php

namespace app\logic\advertise;


use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\ProcessLine;
use app\service\Advertiser\AdPaidRetainProvider;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;

/**
 * Class AdPaidRetainLogic
 * 广告付费留存逻辑层处理
 * @date create by 2025/03/25
 */
class AdPaidRetainLogic
{
    use ColumnsInteract;

    /**
     * 基础付费留存数据查询
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $columns
     * @return array
     * @throws \Exception
     */
    public function listNor(
        array $params = [], array $groups = [], array $paginate = [], array $sort = [], array $columns = []
    ): array
    {
        $isNotReplaceChinese  = Arr::pull($params, 'is_not_replace_chinese', false);
        $params['range_date'] = [
            $params['range_date_start'] ?? date('Y-m-d'),
            $params['range_date_end'] ?? date('Y-m-d'),
        ];
        sort($params['range_date']);
        unset($params['range_date_start'], $params['range_date_end']);

        $params['show_type'] = $params['remain_type'] ?? 0;
        unset($params['remain_type']);

        $result = (new AdPaidRetainProvider())->list(...func_get_args());

        if (!empty($result['list'])) {
            $list    = &$result['list'];
            $process = new ProcessLine();

            if (!$isNotReplaceChinese) {
                $replaceFn = $this->replaceColumnDefine((new BasicServ())->getMultiOptions([
                    'platform_id', 'promotion_id', 'department_id', 'user_id', 'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
                ])->put('promotion_channel_id', (new GeneralOptionServ())->listChannelOptions()));

                $process->addProcess($replaceFn);
                [$adInfo, $dimension] = (new RealtimeIndex())->getAdInfoByGroups($list, $groups);

                if ($dimension) {
                    $process->addProcess(function (&$target, $k) use ($adInfo, $dimension) {
                        $fields = [];
                        switch ($dimension) {
                            case "campaign_id":
                                $fields = ["CAMPAIGN_NAME"];
                                break;
                            case "plan_id":
                                $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME"];
                                break;
                            case "creative_id":
                                $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME", "PLAN_ID", "CREATIVE_NAME"];
                                break;
                        }
                        foreach ($fields as $field) {
                            $key = $target[$dimension];
                            if ($dimension == "creative_id") {
                                $key = $target["plan_id"] . "_" . $target["creative_id"];
                            }
                            $target[strtolower($field)] = $adInfo[$key][$field] ?? null;
                        }
                    });
                }
            }

            $process->run($list);
        }

        return $result;
    }
}
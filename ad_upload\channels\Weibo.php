<?php
/**
 * 微博数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class <PERSON>bo extends AdBaseInterface
{
    const CONVERT_ACTIVE   = 1;
    const CONVERT_REGISTER = 3;
    const CONVERT_PURCHASE = 4;

    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY');
    }

    /**
     * 公共上报
     * @param $info
     * @param $type
     */
    private function upload($info, $type)
    {
        $ext         = $info['EXT'];
        $callbackUrl = "https://appmonitor.biz.weibo.com/sdkserver/active?IMP=" . $info['CALLBACK_URL'];
        $activeTime  = time();
        switch ($type) {
            case 'ACTIVE':
                $callbackUrl = $callbackUrl . '&company=lizao&action_type=' . self::CONVERT_ACTIVE . '&active_time=' . $activeTime;
                $typeName    = '激活';
                break;
            case 'REG':
                $callbackUrl = $callbackUrl . '&company=lizao&action_type=' . self::CONVERT_REGISTER . '&active_time=' . $activeTime;
                $typeName    = '注册';
                break;
            case 'PAY':
                $callbackUrl = $callbackUrl . '&company=lizao&action_type=' . self::CONVERT_PURCHASE . '&active_time=' . $activeTime . '&price=' . (int)$info['MONEY'];
                $typeName    = '付费';
                break;
        }

        $otherParams = ["idfa", "imei", "oaid", "ip", "ua", "model", "language"];
        foreach ($otherParams as $field) {
            if ($ext[$field]) {
                $callbackUrl .= "&" . $field . "=" . $ext[$field];
            }
        }

        $http = new Http($callbackUrl);
        $res  = $http->get();

        //记录上报结果
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'huawei';
        $logInfo['request']      = json_encode(['url' => $callbackUrl]);
        $logInfo['response']     = $res;
        $resContent              = json_decode($res, true);

        if ($resContent['Code'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        //写日志
        $this->log($info, $logInfo, $res, $callbackUrl);
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

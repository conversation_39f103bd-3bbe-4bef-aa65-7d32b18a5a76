<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;
use app\util\Common;


class AdIndicatorsProvider
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \Smarty\Exception
     * @throws \Exception
     */
    public function createRole(
        array $params, array $paginate = [], array $groups = [], array $sort = []
    ): array
    {
        $result   = ['list' => []];
        $db       = $this->getConn();
        $infoTpl  = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_targets/create_role.tpl');
        $totalTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_targets/create_role_total.tpl');
        $infoTpl->assign('params', $params);
        $totalTpl->assign('params', $params);

        if (!empty($paginate)) {
            $infoTpl->assign('paginate', $paginate);
        }

        $totalSQL        = $totalTpl->fetch();
        $total           = $db->query($totalSQL)->fetch()['total_row'] ?? 0;
        $result['total'] = $total;

        if ($total > 0) {
            $infoSQL = $infoTpl->fetch();
            @Common::dumpSql($infoSQL);
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }

        return $result;
    }

    /**
     * @param array $params
     * @param array $paginate
     * @param array $groups
     * @param array $sort
     * @return array
     * @throws \Smarty\Exception
     * @throws \Exception
     */
    public function adPayment(
        array $params, array $paginate = [], array $groups = [], array $sort = []
    ): array
    {
        $db       = $this->getConn();
        $infoTpl  = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_targets/ad_payment.tpl');
        $totalTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_targets/ad_payment_total.tpl');

        $infoTpl->assign('params', $params);
        $totalTpl->assign('params', $params);

        if (!empty($paginate)) {
            $infoTpl->assign('paginate', $paginate);
        }

        $totalSQL        = $totalTpl->fetch();
        $total           = $db->query($totalSQL)->fetch()['total_row'] ?? 0;
        $result['total'] = $total;

        if ($total > 0) {
            $infoSQL = $infoTpl->fetch();
            @Common::dumpSql($infoSQL);
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }
        else {
            $result['list'] = [];
        }

        return $result;
    }

    public function adPaymentLiveExclude(
        array $params, array $paginate = [], array $groups = [], array $sort = []
    ):array
    {
        $db       = $this->getConn();
        $infoTpl  = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_targets/ad_payment_live_exclude.tpl');
        $totalTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_targets/ad_payment_live_exclude_total.tpl');

        $infoTpl->assign('params', $params);
        $totalTpl->assign('params', $params);

        if (!empty($paginate)) {
            $infoTpl->assign('paginate', $paginate);
        }

        $totalSQL        = $totalTpl->fetch();
        $total           = $db->query($totalSQL)->fetch()['total_row'] ?? 0;
        $result['total'] = $total;

        if ($total > 0) {
            $infoSQL = $infoTpl->fetch();
            @Common::dumpSql($infoSQL);
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }
        else {
            $result['list'] = [];
        }

        return $result;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('doris_entrance');
    }
}
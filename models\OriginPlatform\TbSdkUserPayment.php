<?php

namespace app\models\OriginPlatform;

use Plus\MVC\Model\ActiveRecord;

/**
 * @property int    ID
 * @property int    GAME_ID
 * @property int    PACKAGE_ID
 * @property string GAME_SERVER_ID
 * @property string LOGIN_ACCOUNT
 * @property string CORE_ACCOUNT
 * @property string ROLE_ID
 * @property string ROLE_NAME
 * @property string ROLE_RANK
 * @property string ROLE_VIP
 * @property string ORDER_ID
 * @property string CP_ORDER_ID
 * @property string CHANNEL_ORDER_ID
 * @property string COMPANY
 * @property string PAYWAY
 * @property string MONEY_TYPE
 * @property int    MONEY
 * @property string GAME_COIN
 * @property string GOODS_NAME
 * @property string ORDER_TIME
 * @property string PAY_TIME
 * @property int    PAY_RESULT
 * @property int    GAME_RESULT
 * @property string ORDER_TYPE
 * @property string DEVICE_ID
 * @property string MD5_DEVICE_ID
 * @property string DEVICE_CODE
 * @property string ANDROID_ID
 * @property string OAID
 * @property string DEVICE_KEY
 * @property string SDK_VERSION
 * @property string GAME_VERSION
 * @property string IP
 * @property string REFER
 * @property string REFER_PARAM
 * @property string CHANNEL_ID
 * @property string SV_KEY
 * @property string CLICK_ID
 * @property string CLICK_TIME
 */
class TbSdkUserPayment extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->origin_platform;
        parent::__construct($data);
    }
}
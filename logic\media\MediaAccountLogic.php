<?php

namespace app\logic\media;

use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\Media\MediaAccountServ;

class MediaAccountLogic
{
    use ColumnsInteract;

    /**
     * @var MediaAccountServ|null
     */
    protected ?MediaAccountServ $mediaAccountServ = null;

    public function __construct()
    {
        $this->mediaAccountServ = new MediaAccountServ();
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     *
     * @return array
     * @throws \Exception
     */
    public function getInfo(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $rangeDateDimension = $params['range_date_dimension'] ?? 2;
        $serv               = new MediaAccountServ();

        if ($rangeDateDimension == 4) {
            $infoRe  = $serv->getInfoListWithMonth($params, $groups, $paginate, $sort);
            $countFn = $this->countFnWithMonth();
        }
        else {
            $infoRe  = $serv->getInfoList($params, $groups, $paginate, $sort);
            $countFn = $this->countFnWithDaily();
        }

        $list        = &$infoRe['list'];
        $summary     = &$infoRe['summary'];
        $setMap      = (new BasicServ())->getMultiOptions(['media_platform_id', 'use_kind']);
        $platformMap = array_column($setMap->offsetGet('media_platform_id')->toArray(), 'val', 'key');
        $useKindMap  = array_column($setMap->get('use_kind')->all(), 'val', 'key');

        $resetGroupFn = $this->resetGroupsCols(
            $this->groupRelationMap($groups),
            $groups,
            [
                'tday', 'account_id', 'account_name',
                'business_ownership', 'business_ownership_name',
                'media_platform_id', 'media_platform', 'operations_manager',
                'use_kind', 'use_kind_name'
            ]
        );

        $audienceListMap = null;
        $audienceFn      = fn() => true;
        // 特定条件才支持粉丝受众信息
        if (
            (
                empty(array_diff($groups, ['tday', 'account_id', 'media_platform_id', 'business_ownership_name']))
                && empty(array_diff(['tday', 'account_id', 'media_platform_id', 'business_ownership_name'], $groups))
            )
            || (
                empty(array_diff($groups, ['tday', 'account_id']))
                && empty(array_diff(['tday', 'account_id'], $groups))
            )
        ) {
            if (!empty($list)) {
                $audienceRe      = $this->getAudienceMap($list);
                $audienceListMap = &$audienceRe['list'];

                $audienceFn = $this->audienceFn();
            }
            else {
                $audienceFn = fn() => true;
            }
        }

        foreach ($list as &$item) {
            $audienceFn($audienceListMap, $item, $groups);

            $platformId             = $item['media_platform_id'] ?? '';
            $ownership              = $item['business_ownership'] ?? null;
            $useKind                = $item['use_kind'] ?? '';
            $item['media_platform'] = $platformMap[$platformId] ?? '其他';
            $item['use_kind_name']  = $useKindMap[$useKind] ?? $useKind;

            if (is_numeric($ownership)) {
                if ($ownership == 1) {
                    $item['business_ownership_name'] = '直播';
                }
                elseif ($ownership == 0) {
                    $item['business_ownership_name'] = '短视频';
                }
                else {
                    $item['business_ownership_name'] = '';
                }
            }
            else {
                $item['business_ownership_name'] = '';
            }

            $countFn($item);
            $resetGroupFn($item);
        }

        $countFn($summary);

        return $infoRe;

    }

    /**
     * @return \Closure
     */
    protected function countFnWithDaily(): \Closure
    {
        return function (&$target) {
            if (empty($target['normal_task_post']) || empty($target['normal_task_post_success'])) {
                $target['normal_task_post_success_rating'] = '0.00%';
            }
            else {
                $target['normal_task_post_success_rating'] =
                    \round($target['normal_task_post_success'] / $target['normal_task_post'] * 100, 2) . '%';
            }

            if (empty($target['contri_task_post']) || empty($target['contri_task_post_success'])) {
                $target['contri_task_post_rate'] = '0.00%';
            }
            else {
                $target['contri_task_post_success_rating'] =
                    \round($target['contri_task_post_success'] / $target['contri_task_post'] * 100, 2) . '%';
            }
        };
    }

    /**
     * @return \Closure
     */
    protected function countFnWithMonth(): \Closure
    {
        return function (&$target) {
            $allVideoPlay = ($target['normal_video_play_month'] ?? 0) + ($target['contri_video_play_month'] ?? 0);

            $convertFans = 0.00;
            // 转粉率
            if (!empty($allVideoPlay) && !empty($target['month_add_fans'])) {
                $convertFans = \round($target['month_add_fans'] / $allVideoPlay, 4);
            }
            $target['convert_fans'] = $convertFans;
            // 涨粉评级
            if ($convertFans < 0.001) {
                $target['up_fans_grading'] = 0;
            }
            elseif ($convertFans >= 0.001 && $convertFans < 0.0015) {
                $target['up_fans_grading'] = 1;
            }
            elseif ($convertFans >= 0.0015 && $convertFans < 0.002) {
                $target['up_fans_grading'] = 2;
            }
            elseif ($convertFans >= 0.002) {
                $target['up_fans_grading'] = 3;
            }
            else {
                $target['up_fans_grading'] = 0;
            }
            // 当月新增视频平均播放量

            $monthPlay     = ($target['normal_new_video_play_month'] ?? 0) + ($target['contri_new_video_play_month'] ?? 0);
            $monthNewVideo = ($target['normal_new_video_month'] ?? 0) + ($target['contri_new_video_month'] ?? 0);

            if (empty($monthPlay) || empty($monthNewVideo)) {
                $target['avg_new_video_play_month'] = '0.00';
            }
            else {
                $target['avg_new_video_play_month'] = \round($monthPlay / $monthNewVideo, 2);
            }

            $avgNewVideo  = $target['avg_new_video_play_month'];
            $monthMidFans = $target['fans_mid_month'] ?? 0;

            if (empty($avgNewVideo) || $monthMidFans) {
                $target['flow_rate_grading'] = 0;
            }
            else {
                if ($avgNewVideo >= ($monthMidFans * 20)) {
                    $target['flow_rate_grading'] = 3;
                }
                elseif ($avgNewVideo >= ($monthNewVideo * 10)) {
                    $target['flow_rate_grading'] = 2;
                }
                elseif ($avgNewVideo >= ($monthNewVideo * 5)) {
                    $target['flow_rate_grading'] = 1;
                }
                else {
                    $target['flow_rate_grading'] = 0;
                }
            }

            if (empty($target['normal_task_post']) || empty($target['normal_task_post_success'])) {
                $target['normal_task_post_success_rating'] = '0.00%';
            }
            else {
                $target['normal_task_post_success_rating'] =
                    \round($target['normal_task_post_success'] / $target['normal_task_post'] * 100, 2) . '%';
            }

            if (empty($target['contri_task_post']) || empty($target['contri_task_post_success'])) {
                $target['contri_task_post_rate'] = '0.00%';
            }
            else {
                $target['contri_task_post_success_rating'] =
                    \round($target['contri_task_post_success'] / $target['contri_task_post'] * 100, 2) . '%';
            }

        };


    }

    /**
     * @param array $groups
     *
     * @return array
     */
    public function groupRelationMap(array $groups): array
    {
        $relationMap = [
            'tday'                    => [],
            'media_platform'          => ['media_platform_id'],
            'business_ownership_name' => ['business_ownership'],
            'account_id'              => ['media_platform', 'account_name'],
            'operations_manager'      => [],
            'use_kind_name'           => ['use_kind'],
        ];

        return ColumnManager::matchRelationByGroups($relationMap, $groups);
    }

    /**
     * @param array $params
     * @param array $groups
     *
     * @return array
     */
    public function getAudienceMap(array $params): array
    {
        $serv = new MediaAccountServ();
        $re   = $serv->getDimensionGroupTA($params);
        $list = &$re['list'];
        $list = $this->arrayCategorize($list, ['tday', 'account_id', 'data_type']);

        return $re;
    }

    /**
     * @param array $list
     * @param array $dimension
     *
     * @return array
     */
    protected function arrayCategorize(array $list, array $dimension): array
    {
        $dim    = array_fill_keys($dimension, 0);
        $result = [];
        foreach ($list as $foo) {
            $foo       = array_change_key_case($foo);
            $info      = array_merge($dim, array_intersect_key($foo, $dim));
            $uniqueKey = implode('|', $info);

            if (!isset($result[$uniqueKey])) {
                $result[$uniqueKey] = $info;
            }

            $chill                   = &$result[$uniqueKey];
            $dd                      = $foo['dimension'];
            $chill['dimension'][$dd] = array_diff_key($foo, $dim);
        }

        return $result;
    }

    /**
     * @param array $map
     * @param array $data
     * @param array $groups
     * @param array|string $dataType
     *
     * @return void
     */
    private function getAudienceInfo(array $map, array $data, array $groups, $dataType)
    {
        $dimension = array_fill_keys($groups, 0);
        $uniqueKey = implode('|', array_merge($dimension, array_intersect_key($data, $dimension), Arr::wrap($dataType)));

        return $map[$uniqueKey] ?? null;
    }

    /**
     * @param array $params
     * @param array $groups
     *
     * @return array
     */
    public function audienceAnalyze(
        array $params = [], array $groups = []
    ): array
    {
        $audienceRe = $this->getAudienceMap($params, []);

        $result = [
            'fans_age_group'     => [
                'list'   => [],
                'config' => [
                    ['label' => '维度', 'prop' => 'dimension'],
                    ['label' => '数值比例', 'prop' => 'value'],
                ],
            ],
            'gender'             => [
                'list'   => [],
                'config' => [
                    ['label' => '维度', 'prop' => 'dimension'],
                    ['label' => '数值比例', 'prop' => 'value'],
                ],
            ],
            'fans_interests'     => [
                'list'   => [],
                'fields' => [
                    ['title' => '兴趣', 'dataIndex' => 'dimension'],
                    ['title' => '占比', 'dataIndex' => 'value'],
                ],
            ],
            'fans_base_region'   => [
                'list'   => [],
                'fields' => [
                    ['title' => '地区', 'dataIndex' => 'dimension'],
                    ['title' => '占比', 'dataIndex' => 'value'],
                ],
            ],
            'mobile_phone_brand' => [
                'list'   => [],
                'fields' => [
                    ['title' => '设备', 'dataIndex' => 'dimension'],
                    ['title' => '占比', 'dataIndex' => 'value'],
                ],
            ]
        ];

        $list = $audienceRe['list'];

        foreach ($list as $item) {
            $dataType  = $item['data_type'];
            $dimension = $item['dimension'];

            if (!isset($result[$dataType])) {
                $config            = [];
                $result[$dataType] = ['list' => [], 'config' => $config];
            }

            $chill = &$result[$dataType]['list'];

            foreach ($dimension as $foo) {
                $dim = $foo['dimension'];
                $num = $foo['num'];

                $chill[] = ['dimension' => $dim, 'value' => $num];
            }
        }
        unset($chill);
//        var_dump($result);exit();

        foreach ($result as $type => &$foo) {
            if ($type == 'fans_age_group') {
                uasort($foo['list'], function ($current, $next) {
                    preg_match_all("/\d+/", $current['dimension'], $a);
                    preg_match_all("/\d+/", $next['dimension'], $b);

                    $a = array_shift($a);
                    $b = array_shift($b);
                    $a = array_pop($a);
                    $b = array_pop($b);

                    return $a > $b;
                });
            }

            if (
                $type == 'fans_interests'
                || $type == 'fans_base_region'
                || $type == 'mobile_phone_brand'
                || $type == 'gender'
            ) {
                uasort($foo['list'], function ($current, $next) {
                    return $current['value'] < $next['value'];
                });
            }

            $foo['list'] = array_values($foo['list']);
        }

        return $result;
    }

    /**
     * @return \Closure
     */
    protected function audienceFn(): \Closure
    {
        return function ($listMap, &$item) {
            // 男女比例获取
            $genderInfo = $this->getAudienceInfo($listMap, $item, ['tday', 'account_id'], 'gender');

            if ($genderInfo) {
                $dim = $genderInfo['dimension'];

                $item['fans_man_percent']   = $dim['男']['num'] . '%';
                $item['fans_women_percent'] = $dim['女']['num'] . '%';
            }

            // 年龄段处理
            $ageGroupInfo = $this->getAudienceInfo($listMap, $item, ['tday', 'account_id'], 'fans_age_group');

            if ($ageGroupInfo) {
                $dim = $ageGroupInfo['dimension'];
                $dim = array_column($dim, 'num', 'dimension');
                arsort($dim);
                $keys = array_keys($dim);

                $item['fans_age_top1'] = array_shift($keys) ?? null;
                $item['fans_age_top2'] = array_shift($keys) ?? null;
            }

            // 粉丝兴趣处理
            $interestInfo = $this->getAudienceInfo($listMap, $item, ['tday', 'account_id'], 'fans_interests');
            if ($interestInfo) {
                $dim = $interestInfo['dimension'];
                $dim = array_column($dim, 'num', 'dimension');
                arsort($dim);
                $keys = array_keys($dim);

                $item['fans_interest_top1'] = array_shift($keys) ?? null;
                $item['fans_interest_top2'] = array_shift($keys) ?? null;
                $item['fans_interest_top3'] = array_shift($keys) ?? null;
            }

            // 粉丝设备
            $mobileInfo = $this->getAudienceInfo($listMap, $item, ['tday', 'account_id'], 'mobile_phone_brand');

            if ($mobileInfo) {
                $dim = $mobileInfo['dimension'];
                $dim = array_column($dim, 'num', 'dimension');
                arsort($dim);
                $dim = array_keys($dim);

                $item['fans_device_top1'] = array_shift($dim) ?? null;
                $item['fans_device_top2'] = array_shift($dim) ?? null;
                $item['fans_device_top3'] = array_shift($dim) ?? null;
            }

            $regionInfo = $this->getAudienceInfo($listMap, $item, ['tday', 'account_id'], 'fans_base_region');

            if ($regionInfo) {
                $dim = $regionInfo['dimension'];
                $dim = array_column($dim, 'num', 'dimension');
                arsort($dim);
                $dim = array_keys($dim);

                $item['fans_area_top1'] = array_shift($dim) ?? null;
                $item['fans_area_top2'] = array_shift($dim) ?? null;
                $item['fans_area_top3'] = array_shift($dim) ?? null;
            }
        };
    }


}
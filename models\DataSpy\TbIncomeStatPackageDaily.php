<?php

namespace app\models\DataSpy;

use Plus\MVC\Model\ActiveRecord;

/**
 * 根据账单分类的充值汇总表(按天)
 *
 * @property int id
 * @property float sm_amount
 * @property float sm_yyb_amount
 * @property float sm_ios_amount
 * @property float ios_amount
 * @property float wely_amount
 * @property float coin_amount
 * @property float yyb_amount_normal
 * @property float yyb_amount_special_first
 * @property float yyb_amount_special_second
 * @property float other_channel_amount_normal
 * @property float other_channel_amount_special
 * @property float other_amount
 * @property float gdt_weixin_amount
 * @property float sum_amount
 * @property int type
 * @property int is_remove
 * @property int tday
 * @property int package_id
 */
class TbIncomeStatPackageDaily extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->dataspy;
        parent::__construct($data);
    }
}
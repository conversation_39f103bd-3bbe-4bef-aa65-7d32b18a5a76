<?php

namespace app\service\OperationData;

use app\apps\internal\Helpers\ConstHub;
use app\apps\operator\Helpers\ConstFirstLogin;
use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\service\AdvertiserData\Components\Matcher\AdPlanMatch;
use app\service\General\Helpers\DdcPlatformTable;
use app\service\OperationData\Components\MatchParams\PackageMatch;
use app\util\Common;
use PhpOffice\PhpSpreadsheet\Calculation\Statistical\Distributions\F;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Query\SelectQuery;

/**
 * range_date_dimension:
 * 1 - 小时
 * 2 - 天
 * 3 - 周
 * 4 - 月
 */
class FirstLoginServ
{
    const MODE_ALL        = 31;
    const MODE_DETAIL     = 1;
    const MODE_TOTAL      = 2;
    const MODE_SUMMARY    = 4;
    const MODE_DETAIL_QB  = 8;
    const MODE_SUMMARY_QB = 16;
    const QB_MODE_BASE    = 1;
    const QB_MODE_PAYMENT = 2;
    const QB_MODE_POWER   = 4;
    const QB_MODE_ALL     = 7;

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $cols
     * @param int $mode
     * @return array
     * @throws \Exception
     */
    public function getListWithOnlyPaymentForBigData(
        array $params, array $groups = [], array $paginate = [], array $sort = [], array $cols = [], int $mode = -1
    ): array
    {
        $timeDimension = $params['range_date_dimension'];
        $groupsMap     = [];
        $result        = [];
        $qbMode        = self::QB_MODE_ALL ^ self::QB_MODE_BASE;
        $qb            = $this->queryBuilder($qbMode, $params, $groups);
        $matcher       = new PackageMatch([
                                              'package_id'      => 'power.package_id',
                                              'cp_game_id'      => 'power.cp_game_id',
                                              'game_id'         => 'power.game_id',
                                              'platform_id'     => 'power.platform_id',
                                              'app_show_id'     => 'power.app_show_id',
                                              'channel_main_id' => 'power.channel_main_id',
                                              'channel_id'      => 'power.channel_id',
                                              'promotion_id'    => 'power.popularize_v2_id',
                                              'department_id'   => 'power.ad_department_id',
                                              'user_id'         => 'power.ad_user_id',
                                          ]);

        $dayCols  = $this->getDayWithDimension($params, 't_payment');
        $infoCols = $this->searchCols($qbMode, false, $params, $groups, $cols);
        $matcher($qb, $params);
        $infoQb = clone $qb;

        if (in_array('tday', $groups)) {
            $infoCols = array_merge($infoCols, $dayCols);
        }

        $infoQb->columns($infoCols);

        $groupsMap = [
            'tday' => 'group_day'
        ];

        if (!empty($groups)) {
            foreach ($groups as $g) {
                if (isset($groupsMap[$g])) {
                    $infoQb->groupBy($groupsMap[$g]);
                }
                else {
                    $infoQb->groupBy($g);
                }
            }
        }

        $notPageInfoQb = clone $infoQb;

        if (!empty($sort)) {
            $infoQb->orderBy($sort);
        }

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $pageLen] = $paginate;
            $infoQb->limit($pageLen)->offset(($page - 1) * $pageLen);
        }

        if ($mode & self::MODE_DETAIL_QB) {
            // 返回查询构造对象
            $result['detail_qb'] = clone $infoQb;
        }

        if ($mode & self::MODE_DETAIL) {
            if (
                (!empty($groups) && !in_array('tday', $groups))
                || ($timeDimension > ConstHub::DIMENSION_DAY)
            ) {
                $newCols = array_merge($infoCols, $this->getNodeCols($params));
                $infoQb->columns($newCols);
            }

            @Common::dumpSql($infoQb->__toString());
            $result['list'] = $infoQb->fetchAll();
        }

        if ($mode & self::MODE_TOTAL) {
            $totalQb         = clone $notPageInfoQb;
            $result['total'] = $this->getConn()->select()->from(new Fragment('(' . $totalQb->__toString() . ') as total'))->count();
        }

        $summaryQb   = clone $qb;
        $summaryCols = $this->searchCols($qbMode, true, $params, $groups, $cols);

        if ($mode & self::MODE_SUMMARY_QB) {
            $result['summary_qb'] = $summaryQb;
        }

        if ($mode & self::MODE_SUMMARY) {
            $summaryCols = array_merge($summaryCols, $this->getNodeCols($params));
            $summaryQb->columns($summaryCols);
            @Common::dumpSql($summaryQb->__toString());
            $result['summary'] = ($summaryQb->fetchAll())[0] ?? [];
        }

        return $result;
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $cols
     * @param int $mode
     * @return array
     * @throws \Exception
     */
    public function getListWithOnlyBaseForBigData(
        array $params, array $groups = [], array $paginate = [], array $sort = [], array $cols = [], int $mode = -1
    ): array
    {

        $timeDimension = $params['range_date_dimension'];
        $groupsMap     = [];
        $result        = [];
        $qbMode        = self::QB_MODE_ALL ^ self::QB_MODE_PAYMENT;
        $qb            = $this->queryBuilder($qbMode, $params, $groups);
        $matcher       = new PackageMatch([
                                              'package_id'      => 'power.package_id',
                                              'cp_game_id'      => 'power.cp_game_id',
                                              'game_id'         => 'power.game_id',
                                              'platform_id'     => 'power.platform_id',
                                              'app_show_id'     => 'power.app_show_id',
                                              'channel_main_id' => 'power.channel_main_id',
                                              'channel_id'      => 'power.channel_id',
                                              'promotion_id'    => 'power.popularize_v2_id',
                                              'department_id'   => 'power.ad_department_id',
                                              'user_id'         => 'power.ad_user_id',
                                          ]);

        $dayCols  = $this->getDayWithDimension($params, 't_base');
        $infoCols = $this->searchCols($qbMode, false, $params, $groups, $cols);
        $matcher($qb, $params);
        $infoQb = clone $qb;

        if (in_array('tday', $groups)) {
            $infoCols = array_merge($infoCols, $dayCols);
        }

        $infoQb->columns($infoCols);

        $groupsMap = [
            'tday' => 'group_day'
        ];

        if (!empty($groups)) {
            foreach ($groups as $g) {
                if (isset($groupsMap[$g])) {
                    $infoQb->groupBy($groupsMap[$g]);
                }
                else {
                    $infoQb->groupBy($g);
                }
            }
        }

        $notPageInfoQb = clone $infoQb;

        if (!empty($sort)) {
            $infoQb->orderBy($sort);
        }

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $pageLen] = $paginate;
            $infoQb->limit($pageLen)->offset(($page - 1) * $pageLen);
        }

        if ($mode & self::MODE_DETAIL_QB) {
            // 返回查询构造对象
            $result['detail_qb'] = clone $infoQb;
        }

        if ($mode & self::MODE_DETAIL) {
            if (
                (!empty($groups) && !in_array('tday', $groups))
                || ($timeDimension > ConstHub::DIMENSION_DAY)
            ) {
                if (isset($params['align_date'])) {
                    $operator = '>=';
                }
                else {
                    $operator = '>';
                }

                $newCols = array_merge($infoCols, $this->getNodeCols($params, 't_base', 'firstlogin_user', $operator, 'ltv'));
                $newCols = array_merge($newCols, $this->getNodeCols($params, 't_base', 'cost_discount', $operator, 'ltv'));

                $infoQb->columns($newCols);
            }

            @Common::dumpSql($infoQb->__toString());
            $result['list'] = $infoQb->fetchAll();
        }

        if ($mode & self::MODE_TOTAL) {
            $totalQb         = clone $notPageInfoQb;
            $result['total'] = $this->getConn()->select()->from(new Fragment('(' . $totalQb->__toString() . ') as total'))->count();
        }

        $summaryQb   = clone $qb;
        $summaryCols = $this->searchCols($qbMode, true, $params, $groups, $cols);

        if ($mode & self::MODE_SUMMARY_QB) {
            $result['summary_qb'] = $summaryQb;
        }

        if ($mode & self::MODE_SUMMARY) {
            if(isset($params['align_date'])) {
                $operator = '>=';
            }
            else {
                $operator = '>';
            }

            $summaryCols = array_merge($summaryCols, $this->getNodeCols($params, 't_base', 'firstlogin_user', $operator, 'ltv'));
            $summaryCols = array_merge($summaryCols, $this->getNodeCols($params, 't_base', 'cost_discount', $operator, 'ltv'));
            $summaryQb->columns($summaryCols);

            @Common::dumpSql($summaryQb->__toString());
            $result['summary'] = ($summaryQb->fetchAll())[0] ?? [];
        }

        return $result;
    }

    /**
     * @param int $qbMode
     * @param array $params
     * @param array $groups
     * @return SelectQuery
     */
    protected function queryBuilder(int $qbMode = -1, array $params = [], array $groups = []): SelectQuery
    {
        $main          = '';
        $db            = $this->getConn();
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;
        $qb            = $db->select();
        if ($qbMode & self::QB_MODE_BASE) {
            $main = 't_base';

            if ($timeDimension == ConstHub::DIMENSION_HOUR) {
                $qb->from(DdcPlatformTable::DwsPackageBaseHourly . ' as t_base');
            }
            else {
                $qb->from(DdcPlatformTable::DwsPackageBaseDaily . ' as t_base');
            }
        }

        if ($qbMode & self::QB_MODE_PAYMENT) {
            if ($qbMode & self::QB_MODE_BASE) {
                if ($timeDimension == ConstHub::DIMENSION_HOUR) {
                    $qb
                        ->leftJoin(DdcPlatformTable::DwsPackagePaymentHourly, 't_payment')
                        ->on([
                                 't_base.tday'       => 't_payment.tday',
                                 't_base.thour'      => 't_payment.thour',
                                 't_base.package_id' => 't_payment.package_id',
                             ]);
                }
                else {
                    $qb
                        ->leftJoin(DdcPlatformTable::DwsPackagePaymentDaily, 't_payment')
                        ->on([
                                 't_base.tday'       => 't_payment.tday',
                                 't_base.package_id' => 't_payment.package_id',
                             ]);
                }
            }
            else {
                $main = 't_payment';

                if ($timeDimension == ConstHub::DIMENSION_HOUR) {
                    $qb->from(DdcPlatformTable::DwsPackagePaymentHourly . ' as t_payment');
                }
                else {
                    $qb->from(DdcPlatformTable::DwsPackagePaymentDaily . ' as t_payment');
                }
            }
        }

        if ($qbMode & self::QB_MODE_POWER) {
            $qb
                ->innerJoin(new Fragment(str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql())), 'power')
                ->on([
                         $main . '.package_id' => 'power.package_id',
                     ]);
        }

        return $qb;
    }

    /**
     * @param array $params
     * @param string $table
     * @param string $col
     * @param string $operator
     * @return array
     * @throws \Exception
     */
    protected function getNodeCols(
        array $params = [], string $table = 't_payment', string $col = 'firstlogin_pay_user_new_all', string $operator = '>', string $mode = 'remain'
    ): array
    {
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;
        $tDays         = $params['tday'] ?? [];

        if (empty($tDays)) {
            throw new \Exception('缺少参数: tday');
        }

        $baseTime   = min($tDays);
        $targetTime = $params['align_date'] ?? date('Y-m-d');
        $cols       = [];
        $dayDiff    = days_apart($baseTime, $targetTime);

        if ($mode == 'ltv') {
            if (!empty($params['align_date'])) {
                $dayDiff  += 1;
            }
            $generate = fn($i) => "SUM(IF(DATEDIFF('{$targetTime}', {$table}.tday) +1 {$operator} {$i}, {$table}.{$col}, 0)) as {$col}_{$i}";
        }
        else {
            $generate = fn($i) => "SUM(IF(DATEDIFF('{$targetTime}', {$table}.tday) {$operator} {$i}, {$table}.{$col}, 0)) as {$col}_{$i}";
        }

        for ($i = 1; $i <= $dayDiff; $i++) {
            $cols[] = new Fragment($generate($i));
        }

        return $cols;
    }


    /**
     * @param int $qbMode
     * @param bool $isSummary
     * @param array $params
     * @param array $groups
     * @param array $eqCols
     * @return Fragment[]
     */
    protected function searchCols(
        int $qbMode = -1, bool $isSummary = false, array $params = [], array $groups = [], array $eqCols = []
    ): array
    {
        $main = 't_base';

        if (!($qbMode & self::QB_MODE_BASE)) {
            $main = 't_payment';
        }

        $infoColsMap = [
            'cp_game_id'      => 'power.cp_game_id as cp_game_id',
            'game_id'         => 'power.game_id as game_id',
            'app_show_id'     => 'power.app_show_id as app_show_id',
            'platform_id'     => 'power.platform_id as platform_id',
            'promotion_id'    => 'power.popularize_v2_id as promotion_id',
            'package_id'      => 'power.package_id as package_id',
            'is_multiple'     => $main . '.is_multiple as is_multiple',
            'department_id'   => 'power.ad_department_id as department_id',
            'user_id'         => 'power.ad_user_id as user_id',
            'link_mark'       => new Fragment('1 as link_mark'),
            'channel_id'      => 'power.channel_id as channel_id',
            'channel_main_id' => 'power.channel_main_id as channel_main_id'
        ];

        $calcColsMap = [
            't_base'    => [
                'activate_device', 'cost',
                'cost_discount', 'firstlogin_user',
                'firstlogin_device', 'firstlogin_active_user',
                'firstlogin_role', 'firstlogin_role2',
                'create_role', 'firstlogin_real_user',
                'firstlogin_active_user_week', 'active_user_week',
                'firstlogin_active_user_7_days_ago', 'firstlogin_active_user_pay',
                'active_user_7_days_ago', 'new_user',
                'new_device', 'new_real_user',
                'new_real_user_der', 'reg_user',
                'reg_device', 'active_user',
                'new_user_imulator', 'active_user_imulator',
                'active_user_imulator_bind',
            ],
            't_payment' => [
                'pay_user', 'pay_money', 'pay_count', 'pay_money_new', 'pay_count_new',
                'firstlogin_pay_user_week', 'firstlogin_pay_user_all_week',
                'pay_user_week', 'pay_user_all_week',
                'firstlogin_pay_money', 'firstlogin_pay_user_new',
                'firstlogin_pay_money_new', 'firstlogin_pay_money_all',
                'firstlogin_pay_user_new_all', 'firstlogin_pay_money_new_all',
                'firstlogin_pay_user', 'firstlogin_pay_user_all',
                'order_count_all', 'pay_money_all',
                'order_success_all', 'pay_money_newlogin_all',
                'pay_user_newlogin_all', 'pay_money_reg_all',
                'order_count', 'pay_money',
                'order_success', 'pay_money_newlogin',
                'pay_user_newlogin', 'pay_money_reg',
                'pay_user', 'pay_user_all',
            ],
        ];
        $colsFilter  = null;
        if (!empty($eqCols)) {
            $infoColsMap = array_intersect_key($infoColsMap, array_flip($eqCols));
            $colsFilter  = fn($item) => in_array($item, $eqCols);
        }

        if (!($qbMode & self::QB_MODE_BASE)) {
            unset($calcColsMap['t_base']);
        }

        if (!($qbMode & self::QB_MODE_PAYMENT)) {
            unset($calcColsMap['t_payment']);
        }

        $cols = [];

        if (!$isSummary) {
            $cols = array_merge($cols, array_values($infoColsMap));
        }
        else {
            $cols = [new Fragment($main . '.update_time as last_update_time')];
        }

        $calcMap = Arr::flatten($calcColsMap);

        if ($colsFilter != null) {
            $calcMap = array_filter($calcMap, $colsFilter);
        }

        // 拼装需要统计的指标列
        if ($isSummary || !empty($groups)) {
            foreach ($calcMap as $item) {
                $cols[] = new Fragment("IFNULL(SUM({$item}), 0) as `{$item}`");
            }
        }
        else {
            foreach ($calcMap as $item) {
                $cols[] = "{$item} as {$item}";
            }
        }

        return $cols;


    }

    /**
     * @param array $params
     * @param string $table
     * @return array|Fragment[]|string[]
     * @throws \Exception
     */
    protected function getDayWithDimension(array &$params = [], string $table = 't_base'): array
    {
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        if ($timeDimension == ConstHub::DIMENSION_WEEK) {
            $tDays = $params['tday'];
            sort($tDays);

            [
                'begin' => $rangeDateStart,
                'end'   => $rangeDateEnd,
                'cycle' => $rangeDateCycle,
            ] = TimeUtil::divideWeekByRangeDate(
                ...$tDays
            );

            $params['tday'] = [
                $rangeDateStart, $rangeDateEnd
            ];

            $dayCols = [];
            foreach ($rangeDateCycle as $item) {
                ['begin_date' => $begin, 'end_date' => $end] = $item;
                $dayCols[] = sprintf(
                    "when %s.tday between '%s' and '%s' then '%s'",
                    $table, $begin, $end, $begin . '/' . $end
                );
            }
            $caseString    = implode(' ', $dayCols);
            $tDayString    = sprintf(" CONCAT(case %s end) as tday ", $caseString);
            $dayGroupAlias = sprintf(" CONCAT(case %s end) as group_day ", $caseString);

            return [new Fragment($tDayString), new Fragment($dayGroupAlias)];
        }
        elseif ($timeDimension == ConstHub::DIMENSION_MONTH) {
            return [
                new Fragment(" DATE_FORMAT({$table}.tday, '%Y-%m') as tday"),
                new Fragment(" DATE_FORMAT({$table}.tday, '%Y-%m') as group_day"),
            ];
        }
        else {
            return [
                "{$table}.tday as tday",
                "{$table}.tday as group_day",
            ];
        }
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\service\AdvertiseLive\LiveAssistantServ;
use app\service\AdvertiseLive\LiveBaseServ;
use app\service\AdvertiseLive\LiveLtvServ;
use app\service\AdvertiseLive\OperationalAccountServ;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\AdvertiserData\LiveRoomServ;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;

/**
 * 直播间数据
 *
 * @route /advertise/live-room-manager/*
 *
 */
class LiveRoomManagerController extends BaseTableController
{
    use ColumnsInteract;

    /**
     * @route /advertise/live-room-manager/data?method[]=data
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = ['start_time' => 'DESC'];
        }

        $groups  = Arr::wrap($params->pull('groups'));
        $options = $params->toArray();
        $serv    = new LiveRoomServ();

        $infoRe = $serv->getList($options, $groups, ['page' => $page, 'page_size' => $pageSize], $sort);
        // 提取查询的query_builder
        $infoQb = Arr::pull($infoRe, 'mix_dimension_select_qb');
        $baseRe = (new LiveBaseServ())->getListByLiveDutyQb($infoQb, $options, $groups, [], $sort);
        $costRe = (new LiveBaseServ())->getCostByLiveDutyQb($infoQb, $options, $groups, [], $sort);
        $ltvRe  = (new LiveLtvServ())->ltvInfoByDutyQb($infoQb, array_merge($options, ['day_type' => 1000]), $groups, [], $sort);

        $insertList = &$infoRe['list'];
        $baseIndex  = &$baseRe['list'];
        $costList   = &$costRe['list'];
        $ltvList    = &$ltvRe['list'];
        $resultList = [];
        $groupsFill = array_fill_keys($groups, 0);

        $getUniqueKeyFn = function ($data) use ($groupsFill) {
            $key = array_merge($groupsFill, array_intersect_key($data, $groupsFill));

            return implode('|', $key);
        };

        // 拼接维度
        foreach ($insertList as $chill) {
            $uniqueKey = $getUniqueKeyFn($chill);

            if (!isset($resultList[$uniqueKey])) {
                $resultList[$uniqueKey] = $chill;
            }
            $item                        = &$resultList[$uniqueKey];
            $item['watch']               = $chill['watch'] ?? 0;
            $item['max_user']            = $chill['max_user'] ?? 0;
            $item['avg_online']          = $chill['avg_online'] ?? 0;
            $item['speed_push']          = $chill['speed_push'] ?? 0;
            $item['avg_user_duration']   = $chill['avg_user_duration'] ?? 0;
            $item['fans']                = $chill['fans'] ?? 0;
            $item['fans_club']           = $chill['fans_club'] ?? 0;
            $item['interaction_percent'] = $chill['interaction_percent'] ?? 0;
            $item['convert_fans']        = $chill['convert_fans'] ?? 0;
        }
        unset($item);

        foreach ($baseIndex as $chill) {
            $uniqueKey = $getUniqueKeyFn($chill);

            if (!isset($resultList[$uniqueKey])) {
                continue;
            }
            $item = &$resultList[$uniqueKey];

            isset($item['cost'])
                ? $item['cost'] += ($chill['cost'] ?? 0)
                : $item['cost'] = ($chill['cost'] ?? 0);

            isset($item['cost_discount'])
                ? $item['cost_discount'] += ($chill['cost_discount'] ?? 0)
                : $item['cost_discount'] = ($chill['cost_discount'] ?? 0);

            isset($item['new_user'])
                ? $item['new_user'] += ($chill['new_user'] ?? 0)
                : $item['new_user'] = ($chill['new_user'] ?? 0);

            isset($item['pay_user_new'])
                ? $item['pay_user_new'] += ($chill['pay_user_new'] ?? 0)
                : $item['pay_user_new'] = ($chill['pay_user_new'] ?? 0);

            isset($item['pay_money_new'])
                ? $item['pay_money_new'] += ($chill['pay_money_new'] ?? 0)
                : $item['pay_money_new'] = ($chill['pay_money_new'] ?? 0);
        }
        unset($item);

        foreach ($costList as $chill) {
            $uniqueKey = $getUniqueKeyFn($chill);
            if (!isset($resultList[$uniqueKey])) {
                continue;
            }
            $item = &$resultList[$uniqueKey];

            isset($item['cost_1'])
                ? $item['cost_1'] += ($chill['cost_1'] ?? 0)
                : $item['cost_1'] = ($chill['cost_1'] ?? 0);

            isset($item['cost_discount_1'])
                ? $item['cost_discount_1'] += ($chill['cost_discount_1'] ?? 0)
                : $item['cost_discount_1'] = ($chill['cost_discount_1'] ?? 0);

            isset($item['cost_discount_2'])
                ? $item['cost_discount_2'] += ($chill['cost_discount_2'] ?? 0)
                : $item['cost_discount_2'] = ($chill['cost_discount_2'] ?? 0);

            isset($item['cost_discount_3'])
                ? $item['cost_discount_3'] += ($chill['cost_discount_3'] ?? 0)
                : $item['cost_discount_3'] = ($chill['cost_discount_3'] ?? 0);
        }
        unset($item);

        // 只获取累计的金额
        foreach ($ltvList as $chill) {
            $uniqueKey = $getUniqueKeyFn($chill);
            if (!isset($resultList[$uniqueKey])) {
                continue;
            }
            $item = &$resultList[$uniqueKey];

            isset($item['money_all'])
                ? $item['money_all'] += ($chill['money_all'] ?? 0)
                : $item['money_all'] = ($chill['money_all'] ?? 0);
        }
        unset($item);

        $optionsServ            = new GeneralOptionServ();
        $liveAssistant          = (new LiveAssistantServ())->getList([]);
        $liveAssistant          = array_column($liveAssistant, 'assistant_name', 'id');
        $liveOperationalAccount = (new OperationalAccountServ())->getList([]);
        $liveOperationalAccount = array_column($liveOperationalAccount, 'account_name', 'id');
        $constConfCollect       = (new BasicServ())
            ->getMultiOptions(['platform_id', 'promotion_id', 'department_id', 'user_id', 'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id', 'live_team', 'live_platform'])
            ->put('channel_id', (new GeneralOptionServ())->listChannelOptions());
        $replaceFn              = $this->replaceColumnDefine($constConfCollect);
        $packageList            = array_filter(array_unique(array_column($resultList, 'package_id')));
        $channelList            = array_filter(array_unique(array_column($resultList, 'channel_id')));
        $channelMainList        = array_filter(array_unique(array_column($resultList, 'channel_main_id')));
        $packageTagMap          = !empty($packageList) ? $optionsServ->listTagsName(['option_type' => 'package_id', 'id' => $packageList]) : [];
        $channelTagMap          = !empty($channelList) ? $optionsServ->listTagsName(['option_type' => 'channel_id', 'id' => $channelList]) : [];
        $channelMainTagMap      = !empty($channelMainList) ? $optionsServ->listTagsName(['option_type' => 'channel_main_id', 'id' => $channelMainList]) : [];

        if (!empty($packageTagMap)) {
            $newPackageTagMap = [];
            foreach ($packageTagMap as $item) {
                $package     = $item['data_id'];
                $tag         = $item['name'];
                $tagParent   = $item['parent_name'];
                $tagParentId = $item['parent_id'];

                if (!isset($newPackageTagMap[$package])) {
                    $newPackageTagMap[$package] = [];
                }

                $chill = &$newPackageTagMap[$package];

                if (is_null($tagParentId)) {
                    $chill[] = [
                        'name' => $tag,
                    ];
                }
                else {
                    if (!isset($chill[$tagParentId])) {
                        $chill[$tagParentId] = [
                            'name'     => $tagParent,
                            'children' => [['name' => $tag]],
                        ];
                    }
                    else {
                        $chill[$tagParentId]['children'][] = ['name' => $tag];
                    }
                }
            }
            $packageTagMap = $newPackageTagMap;
            unset($newPackageTagMap);
            unset($chill);
        }

        if (!empty($channelTagMap)) {
            $newChannelTagMap = [];
            foreach ($channelTagMap as $item) {
                $channel     = $item['data_id'];
                $tag         = $item['name'];
                $tagParent   = $item['parent_name'];
                $tagParentId = $item['parent_id'] ?? null;

                if (!isset($newChannelTagMap[$channel])) {
                    $newChannelTagMap[$channel] = [];
                }

                $chill = &$newChannelTagMap[$channel];

                if (is_null($tagParentId)) {
                    $chill[] = [
                        'name' => $tag,
                    ];
                }
                else {
                    if (!isset($chill[$tagParentId])) {
                        $chill[$tagParentId] = [
                            'name'     => $tagParent,
                            'children' => [['name' => $tag]],
                        ];
                    }
                    else {
                        $chill[$tagParentId]['children'][] = ['name' => $tag];
                    }
                }
            }

            $channelTagMap = $newChannelTagMap;
            unset($newChannelTagMap);
            unset($chill);
        }

        if (!empty($channelMainTagMap)) {
            $newChannelMainTagMap = [];
            foreach ($channelMainTagMap as $item) {
                $channel     = $item['data_id'];
                $tag         = $item['name'];
                $tagParent   = $item['parent_name'];
                $tagParentId = $item['parent_id'] ?? null;

                if (!isset($newChannelMainTagMap[$channel])) {
                    $newChannelMainTagMap[$channel] = [];
                }

                $chill = &$newChannelMainTagMap[$channel];

                if (is_null($tagParentId)) {
                    $chill[] = [
                        'name' => $tag,
                    ];
                }
                else {
                    if (!isset($chill[$tagParentId])) {
                        $chill[$tagParentId] = [
                            'name'     => $tagParent,
                            'children' => [['name' => $tag]],
                        ];
                    }
                    else {
                        $chill[$tagParentId]['children'][] = ['name' => $tag];
                    }
                }
            }

            $channelMainTagMap = $newChannelMainTagMap;
            unset($newChannelMainTagMap);
            unset($chill);
        }

        $summaryRow     = array_merge(
            ($infoRe['summary'] ?? []),
            ($baseRe['summary'] ?? []),
            ($costRe['summary'] ?? []),
            ($ltvRe['summary'] ?? [])
        );
        $wrapSummaryRow = [&$summaryRow];
        $process        = new ProcessLine();

        $process->addProcess(function (&$target) {
            $target['total_cost']      = round(($target['cost_discount'] ?? 0) + ($target['cost_discount_1'] ?? 0) + ($target['cost_discount_2'] ?? 0) + ($target['cost_discount_3'] ?? 0), 2);
            $target['speed_push_cost'] = $target['cost_discount'] ?? 0;

            if (!empty($target['pay_money_new']) && !empty($target['total_cost'])) {
                [
                    'pay_money_new' => $payMoneyNew,
                    'total_cost'    => $costDiscount,
                ] = $target;
                $target['roi_1'] = round($payMoneyNew / $costDiscount * 100, 2) . '%';
            }
            else {
                $target['roi_1'] = '0.00%';
            }

            if (!empty($target['money_all']) && !empty($target['total_cost'])) {
                [
                    'money_all'  => $moneyAll,
                    'total_cost' => $costDiscount,
                ] = $target;
                $target['total_roi'] = round($moneyAll / $costDiscount * 100, 2) . '%';
            }
            else {
                $target['total_roi'] = '0.00%';
            }
        });

        $resetFn = $this->resetGroupsCols(
            ColumnManager::matchRelationByGroups($this->getGroupRelate(), $groups),
            $groups,
            [
                'tday',
                'range_time',
                'live_platform',
                'account_name',
                'account_id',
                'anchor_id',
                'live_team',
                'docking_partner',
                'assistant',
                'operational_account',
                'cp_game_id',
                'game_id',
                'package_id',
                'user_id',
                'channel_id',
                'channel_main_id',
                'package_tags',
                'channel_tags',
                'channel_main_tags',
            ]
        );

        $summaryProcess = clone $process;
        $process->addProcess(fn(&$target) => $target['assistant_id'] = ($liveAssistant[$target['assistant_id'] ?? ''] ?? ''));
        $process->addProcess(fn(&$target) => $target['operational_account'] = ($liveOperationalAccount[$target['operation_account_id'] ?? ''] ?? ''));
        $process->addProcess(fn(&$target) => $target['anchor_team'] = ($target['live_team'] ?? 1) == 2 ? '外部' : '内部');
        $process->addProcess(function (&$target) use ($channelMainTagMap, $packageTagMap, $channelTagMap) {
            $target['anchor_id'] = $target['anchor_name'] ?? '';

            if (!empty($target['package_id'])) {
                $packageId              = $target['package_id'];
                $target['package_tags'] = array_values($packageTagMap[$packageId] ?? []);
            }

            if (!empty($target['channel_id'])) {
                $channel                = $target['channel_id'];
                $target['channel_tags'] = array_values($channelTagMap[$channel] ?? []);
            }

            if (!empty($target['channel_main_id'])) {
                $channelMain                 = $target['channel_main_id'];
                $target['channel_main_tags'] = array_values($channelMainTagMap[$channelMain] ?? []);
            }
        });
        $process->addProcess($replaceFn);
//        $process->addProcess($resetFn);

        $process->run($resultList);
        $summaryProcess->run($wrapSummaryRow);

        return [
            'list'    => array_values($resultList),
            'summary' => $summaryRow,
            'total'   => $infoRe['total'] ?? 0,
        ];
    }

    protected function fields(Collection $params): array
    {
        $groups = Arr::wrap($params->pull('groups'));

        $infoFields = [
            'tday'                => ['title' => '直播日期', 'dataIndex' => 'tday', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'range_time'          => ['title' => '直播时间', 'dataIndex' => 'range_time', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'live_platform'       => ['title' => '直播平台', 'dataIndex' => 'live_platform', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'account_name'        => ['title' => '直播账号', 'dataIndex' => 'account_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'account_id'          => ['title' => '直播账号ID', 'dataIndex' => 'account_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'anchor_id'           => ['title' => '主播', 'dataIndex' => 'anchor_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'live_team'           => ['title' => '主播团队', 'dataIndex' => 'live_team', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'docking_partner'     => ['title' => '主播对接人', 'dataIndex' => 'docking_partner', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'assistant'           => ['title' => '运营助手', 'dataIndex' => 'assistant_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'operational_account' => ['title' => '运营账号', 'dataIndex' => 'operational_account', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'cp_game_id'          => ['title' => '游戏原名', 'dataIndex' => 'cp_game_id', 'classify' => ['attrs', 'package_index']],
            'game_id'             => ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'classify' => ['attrs', 'package_index']],
            'package_id'          => ['title' => '包号', 'dataIndex' => 'package_id', 'classify' => ['attrs', 'package_index']],
            'user_id'             => ['title' => '投放人', 'dataIndex' => 'user_id', 'classify' => ['attrs', 'package_index']],
            'channel_id'          => ['title' => '推广子渠道', 'dataIndex' => 'channel_id', 'classify' => ['attrs', 'package_index']],
            'channel_main_id'     => ['title' => '主渠道', 'dataIndex' => 'channel_main_id', 'classify' => ['attrs', 'package_index']],
            'package_tags'        => ['title' => '包号标签', 'dataIndex' => 'package_tags', 'classify' => ['attrs', 'tag_index']],
            'channel_tags'        => ['title' => '推广子渠道标签', 'dataIndex' => 'channel_tags', 'classify' => ['attrs', 'tag_index']],
            'channel_main_tags'   => ['title' => '主渠道标签', 'dataIndex' => 'channel_main_tags', 'classify' => ['attrs', 'tag_index']],
        ];

        $intersectFields = ColumnManager::matchRelationByGroups($this->getGroupRelate(), $groups);
        $infoFields      = array_intersect_key($infoFields, array_flip($intersectFields));

        $fields = [
            ['title' => '观看人次', 'dataIndex' => 'watch', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
            ['title' => '观看人数', 'dataIndex' => 'watch_ucount', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
//            ['title' => '人数峰值', 'dataIndex' => 'max_user', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
            //            ['title' => '平均在线', 'dataIndex' => 'avg_online', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
            ['title' => '推流速度', 'dataIndex' => 'speed_push', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
            ['title' => '人均停留时长(秒)', 'dataIndex' => 'avg_user_duration', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
            ['title' => '新增粉丝', 'dataIndex' => 'fans', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
            //            ['title' => '互动率', 'dataIndex' => 'interaction_percent', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
            ['title' => '新增粉丝团', 'dataIndex' => 'fans_club', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
            ['title' => '转粉率', 'dataIndex' => 'convert_fans', 'sorter' => true, 'classify' => ['attrs', 'live_index']],
            ['title' => '成本合计', 'dataIndex' => 'total_cost', 'classify' => ['attrs', 'cost_index']],
            ['title' => '投流成本', 'dataIndex' => 'speed_push_cost', 'classify' => ['attrs', 'cost_index']],
            ['title' => '福袋成本', 'dataIndex' => 'cost_discount_2', 'classify' => ['attrs', 'cost_index']],
            ['title' => '小手柄成本', 'dataIndex' => 'cost_discount_3', 'classify' => ['attrs', 'cost_index']],
            ['title' => '主播成本', 'dataIndex' => 'cost_discount_1', 'classify' => ['attrs', 'cost_index']],
            ['title' => '广告新增用户', 'dataIndex' => 'new_user', 'classify' => ['attrs', 'convert_index']],
            ['title' => '付费新用户', 'dataIndex' => 'pay_user_new', 'classify' => ['attrs', 'convert_index']],
            ['title' => '新用户付费金额', 'dataIndex' => 'pay_money_new', 'classify' => ['attrs', 'convert_index']],
            ['title' => 'ROI1', 'dataIndex' => 'roi_1', 'classify' => ['attrs', 'convert_index']],
            ['title' => '累计ROI', 'dataIndex' => 'total_roi', 'classify' => ['attrs', 'convert_index']],
        ];

        $fields = array_merge(array_values($infoFields), $fields);

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'package_index', 'label' => '关联包号信息'],
                    ['value' => 'live_index', 'label' => '直播间指标'],
                    ['value' => 'cost_index', 'label' => '成本指标'],
                    ['value' => 'convert_index', 'label' => '转化指标'],
                    ['value' => 'tag_index', 'label' => '标签'],
                ],
            ],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $today = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start', 'default' => $today], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
            ['field' => 'account_id'], // 主渠道
            ['field' => 'account'], // 主渠道
            ['field' => 'anchor_id'], // 主渠道
            ['field' => 'live_platform'],
            ['field' => 'live_team'],
            ['field' => 'docking_partner'],
            ['field' => 'assistant_id'],
            ['field' => 'live_account_id'],
            ['field' => 'operation_account_id'],
            ['field' => 'channel_main_id'],
            ['field' => 'channel_id'],
            ['field' => 'channel_id_tags'],
            ['field' => 'package_id_tags'],
            ['field' => 'channel_main_id_tags'],
            ['field' => 'package_id'],
            ['field' => 'cp_game_id'],
            ['field' => 'game_id'],
            ['field' => 'user_id'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups', 'default' => ['tday', 'range_time', 'live_platform', 'live_team', 'anchor_id', 'account_id', 'docking_partner', 'operation_account_id', 'assistant_id', 'package_id']],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

    /**
     * @return array
     */
    protected function getGroupRelate(): array
    {
        return [
            'tday'                 => [],
            'range_time'           => ['tday'],
            'anchor_id'            => [],
            'live_team'            => [],
            'assistant_id'         => ['assistant'],
            'cp_game_id'           => [],
            'game_id'              => ['cp_game_id'],
            'package_id'           => ['cp_game_id', 'game_id', 'user_id', 'department_id', 'package_tags', 'channel_id'],
            'channel_id'           => ['channel_tags', 'channel_main_id'],
            'channel_main_id'      => ['channel_main_tags'],
            'live_platform'        => [],
            'account_id'           => ['account_name'],
            'docking_partner'      => [],
            'operation_account_id' => ['operational_account'],
            'user_id'              => [],
        ];
    }
}
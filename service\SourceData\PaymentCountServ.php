<?php

namespace app\service\SourceData;

use app\extension\FakeDB\FakeDB;
use app\util\Common;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

class PaymentCountServ
{
    private const DWD_PAYMENT_EXT = 'ddc_platform.dwd_sdk_user_payment_virtual_ext';
    private const DWD_PAYMENT     = 'ddc_platform.dwd_sdk_user_payment';

    const MODE_ALL     = 3;
    const MODE_SUMMARY = 2;
    const MODE_LIST    = 1;


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * @throws \Exception
     */
    private function getDorisConn()
    {
        if ($dorisIndex = Common::pingDorisIndex()) {
            return FakeDB::connection($dorisIndex);
        }
        else {
            throw new \RuntimeException('与数据库连接断开');
        }
    }


    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $mode
     * @param array $columns
     *
     * @return array
     * @throws \Exception
     */
    public function fetchPayExtInfo(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1, array $columns = []
    ): array
    {
        $result    = [];
        $paramMode = $mode & (static::MODE_ALL | static::MODE_SUMMARY | static::MODE_LIST);
        $db        = $this->getConn();
//        $db        = $this->getDorisConn();
        $qb        = $db
            ->select()
            ->from(static::DWD_PAYMENT_EXT . ' t_base')
            ->innerJoin(static::DWD_PAYMENT, 't_payment')
            ->on([
                't_base.order_id' => 't_payment.order_id',
            ]);

        $this->matchPayExt($qb, $params);
        $qb->where('coupon_money', '>', 0);

        $qb->columns([
            't_base.cp_game_id as cp_game_id',
//            't_base.classify as classify',
            new Fragment("IF(t_base.coupon_department_id = 0, 389, t_base.coupon_department_id) AS coupon_department_id"),
            new Fragment('SUM(t_base.coupon_money) as coupon_money'),
            new Fragment('SUM(t_base.decuct_coin) as decuct_coin'),
            new Fragment('SUM(t_base.virtual_money) as virtual_money'),
            new Fragment('SUM(t_base.money) as money'),
        ]);

        if ($paramMode & static::MODE_LIST) {
            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    $infoQb->groupBy($g);
                }
            }

            $notHasPageQb = clone $infoQb;
            $result['list'] = $infoQb->fetchAll();
//            $result['total'] = $db
//                ->select()
//                ->from(new Fragment('(' . $notHasPageQb->__toString() . ') as total_body'))
//                ->count();
        }

        if ($paramMode & static::MODE_SUMMARY) {
            $summaryQb         = clone $qb;
            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    private function matchPayExt(&$qb, array $params)
    {
        if (
            !empty($params['range_date_start'])
            && !empty($params['range_date_end'])
        ) {
            $rangeDate = [
                $params['range_date_start'],
                $params['range_date_end'],
            ];

            sort($rangeDate);

            $rangeStart = $rangeDate[0] . ' 00:00:00';
            $rangeEnd   = $rangeDate[1] . ' 23:59:59';

            $qb->where('t_base.pay_time', 'between', $rangeStart, $rangeEnd);
        }

        if (!empty($params['cp_game_id'])) {
            $cpGames = $params['cp_game_id'];

            if (is_string($cpGames) && str_contains($cpGames, ',')) {
                $cpGames = \explode(',', $cpGames);
            }

            if (is_array($cpGames)) {
                $cpGames = new Parameter($cpGames);
            }

            $qb->where('t_base.cp_game_id', $cpGames);
        }

        if (!empty($params['department_id'])) {

            if ($params['department_id'] == 389) {
                $depIds = [389, 0];
            }
            else {
                $depIds = $params['department_id'];
            }

            $qb->where('t_base.coupon_department_id', new Parameter($depIds));
        }

        $qb->where('t_payment.pay_result', 1);
    }


}
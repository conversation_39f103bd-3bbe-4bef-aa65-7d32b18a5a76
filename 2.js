var YLWSDK = {
    CFG: {
        sdk_version: "1.0.0.0",
        channel_code: "hwmini",
        extension: {},
        callback: console.log,
        platform: "android",
        version: "1.0.0",
        SDKINITED: !1,
        fnLogin: console.log,
        fnPay: console.log,
        systemInfo: {},
        ResponsePid: ""
    },
    API: {
        CNLLOGIN: "https://wechatminigame.whqianxiaosheng.com/account/login/hw/code",
        CNLORDER: "https://wechatminigame.whqianxiaosheng.com/pay/order/hw/create",
        YFYLOGIN: "https://yfy-api.whqianxiaosheng.com/login/h5/place",
        YFYORDER: "https://yfy-api.whqianxiaosheng.com/pay/h5/place",
        SDKREVIEW: "https://wechatminigame.whqianxiaosheng.com/common/log/write"
    },
    fnInitCheck: function (e) {
        return "object" != typeof e ? (e.callback && e.callback(t.cb(100, {
            SDKINIT: !1
        })), t.fnReview({
            key: "SDKINIT",
            val: 100
        }), !1) : e.game_code ? e.game_app_key ? !!e.game_app_id || (e.callback && e.callback(t.cb(109, {
            SDKINIT: !1
        })), t.fnReview({
            key: "SDKINIT",
            val: 109
        }), !1) : (e.callback && e.callback(t.cb(102, {
            SDKINIT: !1
        })), t.fnReview({
            key: "SDKINIT",
            val: 102
        }), !1) : (e.callback && e.callback(t.cb(101, {
            SDKINIT: !1
        })), t.fnReview({
            key: "SDKINIT",
            val: 101
        }), !1)
    },
    INIT: function (e) {
        var a = YLWSDK;
        if (a.fnUserFlag(), a.fnInitCheck(e)) return e.callback && e.callback({
            SDKINIT: !0,
            code: 200,
            msg: "success"
        }), a.CFG = {
            ...a.CFG,
            ...e
        }, qg.getSystemInfo({
            success(e) {
                a.CFG.platform = e.platform, a.CFG.systemInfo = e, a.fnReview({
                    key: "client-systemInfo",
                    val: e
                })
            }
        }), a.fnReview({
            key: "SDKINIT",
            val: {
                msg: "success",
                CFG: a.CFG
            }
        }), !0
    },
    LOGIN: function (e) {
        var a = YLWSDK;
        a.CFG.fnLogin = e || console.log;
        let n = {
            forceLogin: 1,
            appid: a.CFG.game_app_id,
            success(n) {
                console.log("qg.login", JSON.stringify(n)), n.playerId ? (a.CFG.extension = n, a.channelLogin(
                    JSON.stringify({
                        playerId: n.playerId,
                        playerLevel: n.playerLevel,
                        playerSSign: n.gameAuthSign,
                        huawei: n
                    })), a.fnReview({
                    key: "LOGIN-success",
                    data: a.CFG.extension
                })) : (console.log("登录失败！", n.errMsg), e && e(a.cb(103, n)), a.fnReview({
                    key: "LOGIN-fail",
                    data: n || ""
                }))
            },
            fail(e) {
                a.fnReview({
                        key: "LOGIN-fail",
                        data: e
                    }), console.log("game login with real fail:" + data + ", code:" + code), 7004 != code &&
                    2012 != code || (console.log("玩家取消登录，返回游戏界面让玩家重新登录。"), a.CFG.fnLogin(a.cb(110, {}))),
                    7021 == code && (console.log(
                        "The player has canceled identity verification. Forbid the player from entering the game."
                    ), a.toast({
                        title: "请实名后进入游戏",
                        type: "error"
                    }), a.CFG.fnLogin(a.cb(111, {})))
            }
        };
        a.fnReview({
            key: "LOGIN-request",
            data: n
        }), qg.gameLoginWithReal(n)
    },
    channelLogin: function (e) {
        var a = this,
            n = {
                code: e,
                game_code: a.CFG.game_code,
                channel_code: a.CFG.channel_code
            };
        console.log("request-data", JSON.stringify(n));
        var t = {
            url: a.API.CNLLOGIN,
            method: "POST",
            data: n,
            cbFn: function (e) {
                if (console.log("登录", JSON.stringify(e)), "error" == e.status) return a.toast({
                    title: e.msg || "登录失败",
                    icon: "fail"
                }), void a.CFG.fnLogin(a.cb(104, e));
                a.CFG.extension = e.data, a.CFG.openId = e.data.openid, e.data.pid && (a.CFG.ResponsePid =
                    e.data.pid), a.yfyLogin()
            }
        };
        console.log("login-options", JSON.stringify(t)), a.ajax(t)
    },
    yfyLogin: function () {
        var e = YLWSDK,
            a = e.CFG.channel_code,
            n = e.CFG.sdk_version,
            t = e.CFG.game_app_key,
            o = e.CFG.game_code,
            i = encodeURIComponent(e.CFG.extension.token);
        e.CFG.extension.token = i;
        var s = "string" == typeof e.CFG.extension ? e.CFG.extension : JSON.stringify(e.CFG.extension);
        e.ajax({
            url: e.API.YFYLOGIN,
            method: "POST",
            data: {
                channel_code: a,
                game_app_key: t,
                game_code: o,
                extension: s,
                sdk_version: n
            },
            cbFn: function (a) {
                if ("error" == a.status) return e.toast({
                    title: a.msg || "登录失败",
                    icon: "fail"
                }), void e.CFG.fnLogin(e.cb(105, a));
                console.log("channelLogin-response-data->", a.data), e.CFG.fnLogin(a.data)
            }
        })
    },
    versionGreaterThan(e, a) {
        let n = e.split("."),
            t = a.split("."),
            o = 0;
        return n.forEach((e, a) => {
            t[a] > e && o++
        }), o >= 2
    },
    PAY: function (e, a) {
        var n = YLWSDK;
        e.channel_code = n.CFG.channel_code, e.media = n.CFG.platform.toLowerCase(), e.pid = n.CFG.ResponsePid,
            n.CFG.fnPay = a || console.log, console.log(JSON.stringify(e)), n.ajax({
                url: n.API.YFYORDER,
                method: "POST",
                data: e,
                cbFn: function (e) {
                    console.log(JSON.stringify(e)), "error" != e.data.status ? (console.log("渠道支付参数",
                        JSON.stringify(e.data)), n.CNLPAY(e.data)) : a && a(n.cb(106, e))
                }
            })
    },
    CNLPAY: function (e) {
        var a = YLWSDK;
        e.platform = a.CFG.platform.toLowerCase(), e.engineVersion = a.CFG.systemInfo.platformVersionCode,
            console.log("CNLPAY", JSON.stringify(e)), a.ajax({
                url: a.API.CNLORDER,
                method: "POST",
                data: e,
                cbFn: function (e) {
                    "error" != e.data.status ? (console.log("支付参数", JSON.stringify(e.data)), a.toast({
                        title: "支付中",
                        icon: "loading"
                    }), qg.isEnvReady({
                        isEnvReadyReq: {
                            applicationID: e.data.applicationID
                        },
                        success: function (n) {
                            console.log("isEnvReady data =", JSON.stringify(n));
                            var t = {
                                productInfoReq: e.data,
                                success: function (n) {
                                    console.log("调用支付成功", JSON.stringify(n)), a
                                        .CFG.fnPay({
                                            code: 200,
                                            msg: "PAY api call success"
                                        }), a.fnReview({
                                            key: "GamePayment-success",
                                            val: e.data
                                        })
                                },
                                fail: function (n) {
                                    console.log("调用支付失败", JSON.stringify(n)), a
                                        .CFG.fnPay(a.cb(108, n)), a.fnReview({
                                            key: "GamePayment-fail",
                                            val: e.data
                                        })
                                },
                                complete: function (n) {
                                    console.log("调用完成", JSON.stringify(n)), a.fnReview({
                                        key: "PAY-complete",
                                        val: e.data
                                    })
                                }
                            };
                            console.log("qgPurchaseEnvState", qg.getPurchaseEnvState()),
                                console.log("paydata", JSON.stringify(t)), a.fnReview({
                                    key: "QgPurchaseEnvState",
                                    val: {
                                        req: e.data,
                                        state: qg.getPurchaseEnvState()
                                    }
                                }), qg.createPurchaseIntent(JSON.stringify(t))
                        },
                        fail: function (e, n) {
                            console.log("isEnvReady fail data =" + e, "code =" + n), a.fnReview({
                                key: "PAY-isEnvReady-failed",
                                val: e,
                                code: n
                            }), a.CFG.fnPay(a.cb(112, {}))
                        }
                    })) : a.CFG.fnPay && a.CFG.fnPay(a.cb(107, e))
                }
            })
    },
    ajax: function (e) {
        let a = new XMLHttpRequest;
        a.open(e.method || "POST", e.url, !0), a.setRequestHeader("content-type",
            "application/x-www-form-urlencoded");
        let n = [];
        if (e.data)
            for (let a in e.data) n.push(a + "=" + e.data[a]);
        a.onreadystatechange = function () {
            if (4 === a.readyState && 200 === a.status) {
                var n = a.responseText;
                try {
                    n = "string" == typeof n ? JSON.parse(n) : n
                } catch (e) {
                    console.log(e)
                }
                console.log("XHR-reponse", JSON.stringify(n)), e.cbFn && e.cbFn(n)
            }
        };
        var t = n.join("&");
        a.send(t)
    },
    toast: function (e) {
        qg.showToast({
            title: e.title || "",
            duration: 2e3,
            type: e.icon || "success",
            success(e) {
                console.log(`${e}`)
            },
            fail(e) {
                console.log("showToast调用失败")
            }
        })
    },
    fnRandomString() {
        let e = 32,
            a = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
            n = a.length,
            t = "";
        for (let o = 0; o < e; o++) t += a.charAt(Math.floor(Math.random() * n));
        return t
    },
    fnSetUserFlag: function () {
        var e = YLWSDK;
        try {
            let a = e.fnRandomString();
            e.CFG.UserFlag = a, localStorage.setItem("UserFlag", a);
            let n = Date.now();
            localStorage.setItem("UserFlagTime", n);
            let t = qg.getLaunchOptionsSync();
            localStorage.setItem("launchOptions", JSON.stringify(t)), t.query.platform && (e.CFG.launchOptions =
                t), e.fnReview({
                key: "fnSetUserFlag-create-success",
                val: a,
                timestamp: n,
                launchOptions: t
            })
        } catch (a) {
            e.fnReview({
                key: "fnSetUserFlag-create-error",
                val: a,
                timestamp: n,
                launchOptions: t
            })
        }
    },
    fnUserFlag: function () {
        var e = YLWSDK;
        try {
            var a = localStorage.getItem("UserFlag");
            a ? (e.CFG.UserFlag = a, e.CFG.launchOptions = localStorage.getItem("launchOptions"), e.fnReview({
                key: "fnUserFlag-get-success",
                val: a,
                launchOptions: localStorage.getItem("launchOptions")
            })) : this.fnSetUserFlag()
        } catch (a) {
            e.fnReview({
                key: "UserFlag",
                val: a,
                msg: "getStorageSync UserFlag error"
            })
        }
    },
    fnReview(e) {
        var a = YLWSDK,
            n = e || {};
        n.key = "WXGAMESDK-> " + n.key, n.timestamp || (n.timestamp = Date.now()), n.UserFlag = a.CFG.UserFlag ||
            "", a.ajax({
                url: a.API.SDKREVIEW,
                header: {
                    "Content-Type": "application/json"
                },
                data: {
                    game_code: a.CFG.game_code || "",
                    timestamp: Math.floor(Date.now() / 1e3),
                    sign: a.fnRandomString(),
                    content: JSON.stringify(n)
                },
                cbFn(e) {
                    console.log("YLWSDK-Review-response->", JSON.stringify(e))
                }
            })
    },
    eventMsg: function (e) {
        var a = "";
        switch (e) {
            case 100:
                a = "INIT was failed, the params must be Object";
                break;
            case 101:
                a = "INIT was failed, game_code is required";
                break;
            case 102:
                a = "INIT was failed, game_app_key is required";
                break;
            case 103:
                a = "Api Login was failed";
                break;
            case 104:
                a = "Channel Login was failed";
                break;
            case 105:
                a = "YFY Login was failed";
                break;
            case 106:
                a = "YFY Order create was failed";
                break;
            case 107:
                a = "Channel Order create was failed";
                break;
            case 108:
                a = "Api callback was failed";
                break;
            case 109:
                a = "Huawei game app id is required";
                break;
            case 110:
                a = "玩家取消登录，返回游戏界面让玩家重新登录。";
                break;
            case 111:
                a = "The player has canceled identity verification. Forbid the player from entering the game.";
                break;
            case 112:
                a = "Call huawei payment failed. Please try again later."
        }
        return {
            code: code,
            msg: a
        }
    },
    cb(e, a) {
        var n = YLWSDK,
            t = n.eventMsg(e);
        return t.data = a || "", t
    }
};
module.exports = YLWSDK;
<?php

namespace app\logic\log;

use app\apps\internal\Helpers\ConstHub;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\BusinessPlatform\OptionServ;
use app\service\ConfigService\BasicServ;
use app\service\Logs\PaymentVoided;
use app\service\Logs\RechargeSummary;
use Spiral\Database\Injection\Parameter;

class PaymentVoidedLogic
{

    /**
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    public function listInfo(Collection $params): array
    {
        $page     = $params->pull('page');
        $pageSize = $params->pull('page_size');
        $sort     = $params->pull('sort', null);
        $groups  = Arr::wrap($params->pull('groups'));
        if (!empty($sort)) {
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sort => $order];
        }
        else {
            $sort = ['tday' => 'DESC'];
        }

        try {
            $serv   = new PaymentVoided();
            $result = $serv->getList($params->toArray(), $groups, ['page' => $page, 'page_size' => $pageSize], $sort);
        }
        catch (\UnexpectedValueException $ue) {
            return ['list' => [], 'summary' => [], 'total' => 0];
        }
        $relationship = ColumnManager::groupPaymentVoidedRelation($groups);
        $resetCols = [];
        if($relationship){
            $diffCols  = array_diff(["tday","cp_game_id","game_id","core_account","role_id"], $relationship);
            $resetCols = array_fill_keys($diffCols, '-');
        }

        if(!empty($result["list"])){
            $constMap        = (new BasicServ())->getMultiOptions(['order_type', 'order_type','voided_type']);
            $orderTypeMap    = array_column($constMap->offsetGet('order_type')->toArray() ?? [], 'val', 'key');
            $voidedTypeMap    = array_column($constMap->offsetGet('voided_type')->toArray() ?? [], 'val', 'key');
            $coreAccountList = array_column($result["list"], 'core_account');
            // 批量查询登录账号
            $loginAccountList = (new OptionServ())
                ->getAccountInfo([
                    'core_user' => new Parameter($coreAccountList),
                    'status'    => 1,
                ], [
                    'login_account', 'core_user',
                ]);
            $loginAccountMap  = [];

            foreach ($loginAccountList as $foo) {
                $coreUser = $foo['core_user'] ?? null;
                $loginA   = $foo['login_account'] ?? null;

                if (empty($coreUser) || empty($loginA)) continue;

                if (!isset($loginAccountMap[$coreUser])) {
                    $loginAccountMap[$coreUser] = [$loginA];
                }
                else {
                    $loginAccountMap[$coreUser][] = $loginA;
                }
            }
        }
        foreach ($result["list"] as &$item){
            $orderType      = $item['order_type'] ?? null;
            $voidedType     = $item['voided_type'] ?? null;
            if (in_array($orderType, ['339', '596', '342', '343', '340', '344', '345', '346', '347'])) {
                $orderType = 1;
            }
            $item['order_type']       = $orderTypeMap[$orderType] ?? '';
            $item['voided_type'] = $voidedTypeMap[$voidedType]??'';
            $coreUser                 = $item['core_account'];
            $loginAccount             = implode(',', $loginAccountMap[$coreUser] ?? []);
            $item['login_account']    = $loginAccount;
            $item = array_merge($item, $resetCols);
        }



        return $result;
    }
}
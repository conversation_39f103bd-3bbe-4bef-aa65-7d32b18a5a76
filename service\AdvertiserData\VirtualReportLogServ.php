<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;

class VirtualReportLogServ
{
    private const TB_VIRTUAL_REPORT_LOG = 'ddc_platform.virtual_report_log';

    /**
     * 插入一条记录
     *
     * @param $data
     *
     * @return int|string|null
     */
    public function insertLog($data)
    {
        $db = $this->getConn();
        return $db->table(static::TB_VIRTUAL_REPORT_LOG)->insertOne($data);
    }

    /**
     * @param string|int $id
     * @param int        $page
     * @param int        $pageSize
     *
     * @return array
     */
    public function getListByReportId($id, int $page, int $pageSize): array
    {
        $db = $this->getConn();
        $qb = $db
            ->select()
            ->from(static::TB_VIRTUAL_REPORT_LOG . ' as t_base')
            ->leftJoin('base_conf_platform.tb_package_detail_conf', 'power')
            ->on('t_base.package_id', 'power.package_id');

        $qb
            ->where('report_id', $id)
            ->columns([
                'report_id as report_id',
                't_base.package_id as package_id',
                'request_params',
                'report_result',
                'report_time',
                'event_type',
                'state',
                'create_time as create_time',
                't_base.update_time as update_time',
            ]);

        $totalQb = clone $qb;

        $qb
            ->limit($pageSize)
            ->offset(($page - 1) * $page);

        $data = $qb->fetchAll();

        return [
            'list'  => $data,
            'total' => $totalQb->count(),
        ];
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
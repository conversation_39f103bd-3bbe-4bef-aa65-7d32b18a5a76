<?php

namespace app\extension\Support\Helpers;

use app\extension\Support\Collections\Collection;


class UtilClosure
{
    /**
     * @param Collection $map
     * @param array $optionsMap
     * @param array $keyMap
     * @return \Closure
     */
    public static function changeDataWithCollectFn(
        Collection $map, array $optionsMap = [], array $keyMap = ['key' => 'key', 'val' => 'val']
    ): \Closure
    {
        $fnMap = [];
        $key   = $keyMap['key'];
        $val   = $keyMap['val'];

        $map->each(function (Collection $item, $k) use (&$fnMap, $key, $val, $optionsMap) {
            $chillMap  = array_column($item->toArray(), $val, $key);
            $prepareFn = fn() => true;
            $targetKey = $k;
            $isDel     = false;
            $default   = '-';
            $sourceKey = null;

            if (isset($optionsMap[$k])) {

                $options = $optionsMap[$k];
                if (isset($options['prepare']) && is_callable($options['prepare'])) {
                    $prepareFn = $optionsMap;
                }
                if (isset($options['target_key'])) {
                    $targetKey = $options['target_key'];
                }
                if (isset($options['is_del'])) {
                    $isDel = true;
                }
                if (isset($options['default'])) {
                    $default = $options['default'];
                }
                if (isset($options['source_key'])) {
                    $sourceKey = $options['source_key'];
                }
            }

            $fnMap[$k] = function (&$target) use (
                $chillMap, $prepareFn, $k, $targetKey, $sourceKey, $isDel, $default
            ) {
                $key = $sourceKey ?? $k;

                if (isset($target[$key])) {
                    $prepareFn($target);

                    $target[$targetKey] = $chillMap[$target[$key]] ?? $default;

                    if ($isDel && ($targetKey != $key)) {
                        unset($target[$key]);
                    }
                }
            };

        });

        return function (&$target) use ($fnMap) {
            if (empty($fnMap)) return;

            foreach ($fnMap as $callback) {
                if (is_callable($callback)) {
                    $callback($target);
                }
            }
        };
    }
}
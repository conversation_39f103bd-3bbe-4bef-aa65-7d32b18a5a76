with creative_ocean_table as (
select
    *
from
bigdata_dws.dws_ad_creative_daily_live
{if !empty($params)}
    {assign var="first_mark" value=1}
    {foreach $params as $kk => $foo}
        {if $kk eq "range_date"}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            tday between '{$foo[0]}' and '{$foo[1]}' {continue}
        {/if}
        {if $kk eq 'cp_game_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                cp_game_id in ({$foo|join:','})
            {else}
                cp_game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                game_id in ({$foo|join:','})
            {else}
                game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'package_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                package_id in ({$foo|join:','})
            {else}
                package_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'account_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            account_id like  '{'%'|cat:$foo|cat:'%'}'
            {continue}
        {/if}
        {if $kk eq 'campaign_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                campaign_id in ({$foo|join:','})
            {else}
                campaign_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'campaign_name'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            campaign_id in (select distinct campaign_id from adp_platform.tb_adp_campaign where campaign_name like '{'%'|cat:$foo|cat:'%'}')
            {continue}
        {/if}
        {if $kk eq 'plan_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                plan_id in ({$foo|join:','})
            {else}
                plan_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'plan_name'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            (
            plan_id in (select distinct plan_id from adp_platform.tb_adp_plan_base where plan_name like '{'%'|cat:$foo|cat:'%'}')
            or plan_id in (select distinct id  from dataspy.tb_ad_svlink_conf where aid like '{'%'|cat:$foo|cat:'%'}')
            )
            {continue}
        {/if}
        {if $kk eq 'creative_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                creative_id in ({$foo|join:','})
            {else}
                creative_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'data_scope'}
            {if $foo eq 1}
                {if !$first_mark} and {else} where {$first_mark=0} {/if} is_ad_data = 1
            {elseif $foo eq 2}
                {if !$first_mark} and {else} where {$first_mark=0} {/if} is_ad_data = 0
            {/if}
            {continue}
        {/if}
        {if $kk eq 'marketing_goal'}
            {if $foo != [1,2] || $foo != [2,1]}
                {if in_array(1, $foo)}
                    {if !$first_mark} and {else} where {$first_mark=0} {/if} marketing_goal != 2
                {/if}
                {if in_array(2, $foo)}
                    {if !$first_mark} and {else} where {$first_mark=0} {/if} marketing_goal = 2
                {/if}
            {/if}
            {continue}
        {/if}
        {if $kk eq 'is_has_appointment'}
            {if empty($foo)} {if !$first_mark} and {else} where {$first_mark=0} {/if} is_appointment != 1 {/if}
            {continue}
        {/if}
        {if $kk eq 'is_has_natural'}
            {if empty($foo)} {if !$first_mark} and {else} where {$first_mark=0} {/if} channel_id > 0 {/if}
            {continue}
        {/if}
    {/foreach}
{/if}
),
dashboard_info as (

select
    b1.tday,
    b1.cp_game_id,
    b1.game_id,
    b1.package_id,
    b1.campaign_id,
    any(coalesce(b2.campaign_name, '')) as campaign_name,
    b1.plan_id,
    b1.creative_id,
    b1.main_channel_id,
    b1.account_id,
    b1.ad_account,
    any(b1.platform_id) as platform_id,
    any(b1.account_name) as account_name,
    any(b1.is_ad_data) as is_ad_data,
    any(b1.is_appointment) as is_appointment,
    any(b1.marketing_goal) as marketing_goal,
    any(case when user_os = '["ANDROID"]' then 'ANDROID' when user_os = '["IOS"]' then 'IOS' else '混投' end)  as dim_user_os,
    any(b1.popularize_v2_id) as promotion_id,
    any(b1.app_show_id) as app_show_id,
    b1.promotion_channel_id,
    b1.dim_user_id
{if empty($base_only_dimension)}
    ,SUM(create_role_new) as create_role_new,
    SUM(active_user) as active_user,
    SUM(pay_new_user_7days) as pay_new_user_7days,
    SUM(pay_frequency_7days) as pay_frequency_7days,
    SUM(pay_user) as pay_user,
    SUM(pay_money) as pay_money,
    SUM(pay_count) as pay_count,
    SUM(pay_user_new) as pay_user_new,
    SUM(pay_money_new) as pay_money_new,
    SUM(pay_count_new) as pay_count_new,
    max(b1.update_time) as update_time
{/if}
from
adp_platform.tb_adp_campaign b2
right join (
select
{if isset($ad_channels)}
    COALESCE( IF(power.channel_id not in ({$ad_channels|join:','}), power.channel_id, IF(a1.channel_id != 0, IF(a1.channel_id =1013, 4, a1.channel_id), power.channel_id) ) ,0) as promotion_channel_id,
    COALESCE( IF(power.channel_id not in ({$ad_channels|join:','}), power.ad_user_id, IF(a1.user_id != 0, a1.user_id, power.ad_user_id)),0 ) as dim_user_id,
{else}
    a1.channel_id as promotion_channel_id,
    a1.user_id as dim_user_id,
{/if}
a1.*, a2.ad_account,
a3.advertiser_name as account_name,
power.platform_id,
power.popularize_v2_id,
power.app_show_id
from creative_ocean_table a1
{if !empty($power_join_sql) && $power_join_sql == 'base_conf_platform.tb_package_detail_conf'}
    join ({$power_join_sql}) power on a1.package_id = power.package_id
{else}
    join base_conf_platform.tb_package_detail_conf power on a1.package_id = power.package_id
{/if}
left join base_conf_platform.tb_ad_account_conf a2 on a1.account_id = a2.account_id
left join adp_platform.tb_adp_oauth a3 on a1.account_id = a3.advertiser_id and a1.main_channel_id = a3.channel_id
{if !empty($params)}
    {assign var="first_mark_1" value=1}
    {foreach $params as $kk => $foo}
        {if $kk eq 'ad_account'}
            {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
            a2.ad_account like '{'%'|cat:$foo|cat:'%'}'
            {continue}
        {/if}
        {if $kk eq 'app_show_id'}
            {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
            {if is_array($foo)}
                power.app_show_id in ({$foo|join:','})
            {else}
                power.app_show_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'platform_id'}
            {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
            {if is_array($foo)}
                power.platform_id in ({$foo|join:','})
            {else}
                power.platform_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'promotion_id'}
            {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
            {if is_array($foo)}
                power.popularize_v2_id in ({$foo|join:','})
            {else}
                power.popularize_v2_id = '{$foo}'
            {/if}
            {continue}
        {/if}
    {/foreach}
{/if}
) b1 on b1.campaign_id = b2.campaign_id and b1.main_channel_id = b2.channel_id
{if !empty($params)}
    {assign var="first_mark_2" value=1}
    {foreach $params as $key => $foo}
        {if $kk eq 'user_os'}
            {if is_array($foo)}
                {if !$first_mark_2} and {else} where {$first_mark_2=0} {/if}
                (
                {foreach $foo as $ii => $chill}
                    {if !$chill@first} or {/if}
                    {if $chill eq 1} b2.user_os =  '["IOS"]'{/if}
                    {if $chill eq 2} b2.user_os =  '["ANDROID"]'{/if}
                    {if $chill eq 3} (b2.user_os != '["IOS"]' and b2.user_os != '["ANDROID"]'){/if}
                {/foreach}
                )
            {/if}
            {continue}
        {/if}
        {if $kk eq 'campaign_name'}
            {if !$first_mark_2} and {else} where {$first_mark_2=0} {/if}
            b2.campaign_name like '{'%'|cat:$foo|cat:'%'}'
        {/if}
    {/foreach}
{/if}
group by tday, b1.cp_game_id, b1.game_id, b1.package_id, promotion_channel_id, b1.campaign_id, b1.plan_id,
b1.creative_id, b1.main_channel_id, b1.account_id, dim_user_id, b1.ad_account
)
select
{if !empty($groups)}
    {foreach $groups as $item}
        {if $item eq 'tday'} tday,
        {elseif $item eq 'tmonth'} DATE_FORMAT(tday, '%Y-%m') as tmonth,
        {elseif $item eq 'cp_game_id'} cp_game_id,
        {elseif $item eq 'game_id'} game_id,
        {elseif $item eq 'app_show_id'} app_show_id,
        {elseif $item eq 'channel_main_id'} channel_main_id,
        {elseif $item eq 'promotion_channel_id'} promotion_channel_id,
        {elseif $item eq 'promotion_id'} promotion_id,
        {elseif $item eq 'user_id'} dim_user_id as user_id,
        {elseif $item eq 'department_id'} department_id,
        {elseif $item eq 'package_id'} package_id,
        {elseif $item eq 'platform_id'} t1.platform_id,
        {elseif $item eq 'account_id'} account_id,account_name,
        {elseif $item eq 'ad_account'} ad_account,
        {elseif $item eq 'campaign_id'} campaign_id,any(campaign_name) as campaign_name,
        {elseif $item eq 'plan_id'} plan_id,
        {elseif $item eq 'creative_id'} creative_id,
        {elseif $item eq 'dim_user_os'} dim_user_os,
        {/if}
    {/foreach}
{/if}
SUM(create_role_new) as create_role_new,
SUM(active_user) as active_user,
SUM(pay_new_user_7days) as pay_new_user_7days,
SUM(pay_frequency_7days) as pay_frequency_7days,
SUM(pay_user) as pay_user,
SUM(pay_money) as pay_money,
SUM(pay_count) as pay_count,
SUM(pay_user_new) as pay_user_new,
SUM(pay_money_new) as pay_money_new,
SUM(pay_count_new) as pay_count_new,
max(update_time)  as last_update_time
from dashboard_info t1
left join base_conf_platform.tb_base_channel_conf t2 on t1.promotion_channel_id = t2.channel_id
left join dataspy.admin_user t3 on t1.dim_user_id=t3.id
{* 搜索条件 *}
{if !empty($params)}
    {assign var="tag_first_where" value=1}
    {foreach $params as $key => $item}
        {if $key eq 'channel_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                t1.promotion_channel_id in ({$item|join:','})
            {else}
                t1.promotion_channel_id = {$item}
            {/if}
        {/if}

        {if $key eq 'channel_main_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                channel_main_id in ({$item|join:','})
            {else}
                channel_main_id = {$item}
            {/if}
        {/if}

        {if $key eq 'user_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                t1.dim_user_id in ({$item|join:','})
            {else}
                t1.dim_user_id = {$item}
            {/if}
        {/if}

        {if $key eq 'department_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($item)}
                t3.department_id in ({$item|join:','})
            {else}
                t3.department_id = {$item}
            {/if}
        {/if}

    {/foreach}
{/if}
{* 汇总维度 *}
{if !empty($groups)}
    group by
    {if !empty($groups)}
        {foreach $groups as $item}
            {if $item eq 'tday'} tday {if !$item@last}, {/if}
            {elseif $item eq 'tmonth'} tmonth {if !$item@last}, {/if}
            {elseif $item eq 'cp_game_id'} cp_game_id {if !$item@last}, {/if}
            {elseif $item eq 'game_id'} game_id {if !$item@last}, {/if}
            {elseif $item eq 'app_show_id'} app_show_id {if !$item@last}, {/if}
            {elseif $item eq 'channel_main_id'} channel_main_id {if !$item@last}, {/if}
            {elseif $item eq 'promotion_channel_id'} promotion_channel_id {if !$item@last}, {/if}
            {elseif $item eq 'promotion_id'} promotion_id {if !$item@last}, {/if}
            {elseif $item eq 'user_id'} user_id {if !$item@last}, {/if}
            {elseif $item eq 'department_id'} department_id {if !$item@last}, {/if}
            {elseif $item eq 'package_id'} package_id {if !$item@last}, {/if}
            {elseif $item eq 'platform_id'} t1.platform_id {if !$item@last}, {/if}
            {elseif $item eq 'account_id'} account_id,account_name {if !$item@last}, {/if}
            {elseif $item eq 'ad_account'} ad_account {if !$item@last}, {/if}
            {elseif $item eq 'campaign_id'} campaign_id {if !$item@last}, {/if}
            {elseif $item eq 'plan_id'} plan_id {if !$item@last}, {/if}
            {elseif $item eq 'creative_id'} creative_id {if !$item@last}, {/if}
            {elseif $item eq 'dim_user_os'} dim_user_os {if !$item@last}, {/if}
            {/if}
        {/foreach}
    {/if}
{/if}
{* 排序 *}
{if !empty($sorts)}
    order by
    {foreach $sorts as $ss => $oo}
        {$ss} {$oo}
        {if !$oo@last}, {/if}
    {/foreach}
{/if}
{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}
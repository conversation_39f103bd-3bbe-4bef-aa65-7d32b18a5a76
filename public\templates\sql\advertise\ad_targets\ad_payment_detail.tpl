with payment_detail as (
select
    a1.*,
    coalesce(a2.real_money, 0)   as real_money,
    coalesce(a2.coupon_money, 0) as coupon_money,
    coalesce(a2.decuct_coin, 0)  as decuct_coin,
    IFNULL(a2.MONEY,a1.MONEY) as order_money
from ddc_platform.dwd_sdk_user_payment_virtual_ext a2
right join (
select *
from ddc_platform.dwd_sdk_user_payment
where pay_result = 1
{if !empty($params)}
    {foreach $params as $kk => $chill}
        {if $kk eq 'range_date'}
           and to_date(pay_time) between '{$chill[0]}' and '{$chill[1]}'
        {/if}
    {/foreach}
{/if}
) a1
on a1.order_id = a2.order_id)

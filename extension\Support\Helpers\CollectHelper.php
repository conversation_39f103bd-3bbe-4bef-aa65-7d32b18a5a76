<?php

namespace app\extension\Support\Helpers;

use app\extension\Support\Collections\Collection;

class CollectHelper
{
    /**
     * @param       $array
     * @param array $keyMap
     * @param bool  $isFlip
     * @return Collection
     */
    public static function crossMap($array, array $keyMap = [], bool $isFlip = false): Collection
    {
        $result     = collect();
        $keyMap     = $isFlip ? array_flip($keyMap) : $keyMap;
        $keys       = array_fill_keys(array_keys($keyMap), null);
        $targetKeys = array_values($keyMap);

        foreach ($array as $item) {
            $kk = array_intersect_key($item, $keys);
            $c  = collect(array_combine($targetKeys, $kk));

            $result->push($c);
        }

        return $result;
    }
}
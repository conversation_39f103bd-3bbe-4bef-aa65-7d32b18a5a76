<?php

namespace app\models\baseConfPlatform;

use Plus\MVC\Model\ActiveRecord;
/**
 * 授权表
 * @property int $id 自增ID(CHANNEL_ID)
 * @property string $project 渠道编码
 * @property string $key 密钥
 * <AUTHOR>
 */
class TbOathConf extends ActiveRecord
{
    public $_primaryKey = 'id';

    /**
     * 初始化，设置数据、数据库连接类
     *
     * @param array $data 批量配置数据
     *
     */
    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->base_conf_platform;
    }

    /**
     * 获取密钥
     * @param $code
     * @return TbOathConf
     */
    public function getkey($code){
        return $this->asArray()->find(["CODE"=>$code,"STATUS"=>1],"KEY");
    }
}

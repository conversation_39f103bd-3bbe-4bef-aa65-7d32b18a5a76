<?php

namespace app\service\OriginData;

use app\extension\FakeDB\FakeDB;

class SdkLoginProvider
{
    /**
     * @param array $accounts
     * @return array
     */
    public function getLastLoginTimeWithAccounts(array $accounts)
    {
        $coreAccount = "'" . implode("','", $accounts) . "'";
        $sql         = "select max(login_time) as last_login_time, core_account
                from bigdata_dwd.dwd_sdk_user_login
                where core_account in ({$coreAccount})
                group by core_account";
        $db          = $this->getConn();

        return $db->query($sql)->fetchAll();
    }

    private function getConn()
    {
        return FakeDB::connection('doris_entrance');
    }
}
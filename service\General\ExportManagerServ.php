<?php

namespace app\service\General;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\General\Exceptions\ExistJobException;
use app\service\General\Exceptions\LockOverException;
use app\service\General\Exceptions\MissJobException;
use app\service\Util;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Laminas\Hydrator\Exception\RuntimeException;
use League\CLImate\CLImate;
use PhpAmqpLib\Message\AMQPMessage;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use PhpOffice\PhpSpreadsheet\Writer\Exception;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpParser\Node\Expr\AssignOp\Plus;
use Plus\Cache\RedisCache;
use Plus\Net\Http;
use Spiral\Database\Database;
use Spiral\Database\Injection\Expression;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

class ExportManagerServ
{
    // 信息集合
    private const MESSAGE_LIST = [
        'no_pass'      => '暂不允许导出, 过多一段时间再来',
        'exist_finish' => '该任务已被完成,可能为重复推送',
        'missing_job'  => '任务记录丢失,请联系相关人员排查或再重试',
    ];
    // 记录表
    private const TABLE_LOG = 'dataspy.user_export_log';
    // 队列标记
    private const JOB_TOPIC = 'report_export';
    // redis缓存拼接
    private const EXPORT_KEY = ':export_job';
    // redis锁前缀
    private const JOB_NX_PRE = self::EXPORT_KEY . ':lock';
    // redis进度前缀
    private const JOB_INFO_PRE = self::EXPORT_KEY . ':info';

    /**
     * 输出终端
     *
     * @var CLImate
     */
    protected CLImate $climate;

    /**
     * 是否打印输出
     *
     * @var bool
     */
    protected bool $isStdout = false;

    public function __construct(CLImate &$climate = null)
    {
        if (!empty($climate)) {
            $this->isStdout = true;
            $this->climate  = &$climate;
        }
    }

    /**
     * 创建导出后台任务
     *
     * @param array $jobInfo
     *
     * @return array
     */
    public function createExportJob(array $jobInfo): array
    {
        $db     = $this->getConn();
        $result = [
            'code'    => 200,
            'message' => 'success',
        ];

        try {
            $id = $db->transaction(function (Database $tdb) use ($jobInfo) {
                /**
                 * 存放记录
                 */
                return $this->saveJobInfo($tdb, $jobInfo);
            });
        }
        catch (\Throwable $e) {
            $db->rollback();
            return ['message' => $e->getMessage(), 'code' => 500];
        }

        if ($id) {
            /**
             * 推送任务到队列
             */
            try {
                $db->transaction(function (Database $tdb) use ($jobInfo, $id) {
                    $this->updateJobStatus($tdb, $id);
                    $queueMsg = \json_encode(['action' => 'export_excel', 'user_id' => $jobInfo['user_id'], 'id' => $id]);
                    \Plus::$app->universal_mq->put(static::JOB_TOPIC, $queueMsg);
                });
            }
            catch (\Throwable $e) {
                $db->rollback();
                $this->dropJobById($db, $id);

                return ['message' => $e->getMessage(), 'code' => 500];
            }
        }

        return $result;
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('dataspy');
    }

    /**
     * 限制
     * 2分钟内同一用户相同条件不允许导出
     *
     * @param Database $db
     * @param array $params
     *
     * @return bool
     */
    private function jobCreatePassport(Database &$db, array $params): bool
    {
        $today = date('Y-m-d');
        $qb    = $db->select()->from(static::TABLE_LOG);

        if (!empty($params['request_body'])) {
            ksort($params['request_body']);
        }

        $request = &$params['request_body'];
        ksort($request['params']);

        $parmaJson = \json_encode($request['params']);

        $qb
            ->where('user_id', $params['user_id'])
            ->where(new Fragment("`request_body` -> '$.request_uri' = '{$request['request_uri']}'"))
            ->where(new Fragment("`request_body` -> '$.params' = CAST('{$parmaJson}' as JSON)"))
            ->where('create_at', 'between', ($today . ' 00:00:00'), ($today . ' 23:59:59'))
            ->where(new Fragment("MINUTE(TIMEDIFF(NOW(), create_at)) <= 2"));

        $re = $qb->count('1');

        return !($re || 0);
    }


    /**
     * @param Database $db
     * @param array $options
     *
     * @return int|string|null
     */
    private function saveJobInfo(Database &$db, array $options)
    {
        $insert = $db->table(static::TABLE_LOG);

        return $insert->insertOne([
            'job_id'       => new Expression("replace(uuid(), '-', '')"),
            'user_id'      => $options['user_id'],
            'request_body' => \json_encode($options['request_body']),
        ]);
    }

    /**
     * @param Database $db
     * @param          $id
     * @param int $status
     *
     * @return int
     */
    public function updateJobStatus(Database &$db, $id, int $status = -1): int
    {
        $update = $db->table(static::TABLE_LOG)->update();

        if ($status >= 0) {
            $update->set('job_status', $status);
        }
        else {
            $update->set('job_status', new Expression('job_status +1'));
        }

        return $update
            ->where('id', $id)
            ->run();
    }

    /**
     * 根据ID删除任务
     *
     * @param Database $db
     * @param          $id
     *
     * @return int
     */
    public function dropJobById(Database &$db, $id): int
    {
        $delete = $db->table(static::TABLE_LOG)->delete();
        $delete->where('id', $id);

        return $delete->run();
    }

    /**
     * @param array $options
     * @return void
     * @throws \RedisException
     * @throws MissJobException
     * @throws \Exception
     */
    public function exportTask(array $options)
    {
        ['user_id' => $userId, 'id' => $id] = $options;

        $db      = $this->getConn();
        $redis   = \Plus::$app->redis;
        $pid     = posix_getpid();
        $infoKey = $redis->getCacheKey($this->getCacheInfoKey($userId, $id));

        // 进度写入缓存中
        $this->updateJobStatus($db, $id, 2);
        $redis->hSet($infoKey, 'progress', 0);
        $redis->hSet($infoKey, 'id', $id);
        $redis->expire($infoKey, 60 * 60 * 24);
        print '进度缓存已初始化' . PHP_EOL;

        $jobCacheKi     = $redis->getCacheKey($this->getCacheInfoKey($userId, $id));
        $jobInfo        = $this->getJobInfo($db, ['user_id' => $userId, 'id' => $id]);
        $specificFields = [];

        if (empty($jobInfo) || empty($jobInfo['request_body'])) {
            throw new MissJobException(static::MESSAGE_LIST['missing_job']);
        }

        $requestBody = \json_decode($jobInfo['request_body'], true);

        [
            'params'       => $params,
            'request_uri'  => $requestUri,
            'access_token' => $token,
        ] = $requestBody;

        if (!empty($requestBody['fields'])) {
            $specificFields = $requestBody['fields'];
        }

        $data       = [];
        $field      = [];
        $dataIndex  = [];
        $summaryRow = [];
        $maxPage    = [];
        $page       = 1;

        $params = array_filter($params, function ($item) {
            return $item != '';
        });

        try {
            foreach (
                $this->getContentGen($params, $requestUri, $token) as $key => $item
            ) {
                if ($key === 'fields') {
                    /**
                     * 获取表头和对应的数据索引
                     */
                    [
                        'title' => $field, 'index' => $dataIndex,
                    ] = $this->getFields(($item['fields'] ?? []), $specificFields);

                    $dataIndex = array_fill_keys($dataIndex, 0);
                    print '获取表头成功' . PHP_EOL;
                    continue;
                }

                /**
                 * 以下为列表数据操作
                 */
                if (empty($item['data']['list'])) continue;

                if (empty($summaryRow)) {
                    $summary = $item['data']['summary'] ?? [];
                    if (isset($summary['children'])) {
                        $summaryChill = $summary['children'];
                        $summaryRow   = [];
                        foreach ($summaryChill as $fuzz) {
                            $summaryRow[] = array_merge($dataIndex, array_intersect_key($fuzz, $dataIndex));
                        }
                    }
                    else {
                        $summaryRow = array_merge($dataIndex, array_intersect_key($summary, $dataIndex));
                        $summaryRow = [$summaryRow];
                    }
                }

                if ((int)$key === 1) {
                    $maxPage = $item['data']['max_page'] ?? 0;
                }

                @$this->updateCacheJobStatus($redis, $jobCacheKi, $key, $maxPage);

                $list = &$item['data']['list'];
                foreach ($list as $foo) {
                    if (isset($foo['children'])) {
                        $fooChill   = $foo['children'];
                        $otherIndex = array_diff_key($foo, ['children' => '']);

                        if (is_array($fooChill)) {
                            foreach ($fooChill as $fuzz) {
                                $newChill = array_merge($otherIndex, $fuzz);
                                $dd       = array_merge($dataIndex, array_intersect_key($newChill, $dataIndex));

                                foreach ($dd as &$chill) {
                                    // 预防数字类型被截取
                                    if (is_numeric($chill) && strlen($chill) > 10) {
                                        $chill .= "\t";
                                    }

                                    if (is_array($chill)) {
                                        $chill = implode(',', Arr::flatten($chill));
                                    }
                                }

                                $data[] = $dd;
                            }
                        }


                    }
                    else {
                        $dd = array_merge($dataIndex, array_intersect_key($foo, $dataIndex));

                        foreach ($dd as &$chill) {
                            // 预防数字类型被截取
                            if (is_numeric($chill) && strlen($chill) > 10) {
                                $chill .= "\t";
                            }

                            if (is_array($chill)) {
                                $chill = implode(',', Arr::flatten($chill));
                            }
                        }

                        $data[] = $dd;
                    }

                }

                print "成功获取第{$page}页数据" . PHP_EOL;
                ++$page;
            }
            print '数据读取完成' . PHP_EOL;
        }
        catch (GuzzleException $e) {
            print $e->getTraceAsString() . PHP_EOL;
            print $e->getMessage() . PHP_EOL;
        }

        $sp = new Spreadsheet();
        if (empty($summaryRow)) {
            $data = array_filter(array_merge([$field], $data), fn($item) => !empty(array_filter($item)));
        }
        else {
            $data = array_filter(array_merge([$field], $summaryRow, $data), fn($item) => !empty(array_filter($item)));
        }

        // 特殊字符处理
        foreach ($data as &$row) {
            foreach ($row as &$chillItem) {
                if ($chillItem && strpos($chillItem, '=') === 0) {
                    $chillItem = "'" . $chillItem;
                }
            }
        }

        $sp->getActiveSheet()->fromArray($data);

        $filePath = $this->newFilePath($userId, $params, ($jobInfo['job_id'] ?? ''), 'csv');
        $writer   = new Csv($sp);

        $writer
            ->setUseBOM(false)
            ->setOutputEncoding('GBK')
            ->setDelimiter(',')
            ->setEnclosure('"')
            ->setLineEnding("\r\n")
            ->setSheetIndex(0);

        $writer->save($filePath);
        $this->finishJob($db, $id, $filePath);

        // 企微通知
        Util::sendWXAppMsg($userId, '手盟数据下载通知', '您有下载任务已完成,请到手盟数据后台查看');
    }


    /**
     * @param array $options
     *
     * @return array
     */
    public function exportOffLine(array $options): array
    {
        [
            'user_id' => $userId,
            'id'      => $id,
        ] = $options;

        $db    = $this->getConn();
        $redis = \Plus::$app->redis;
        $pid   = posix_getpid();

        [
            'is_allow' => $isAllow,
            'is_ack'   => $isAck,
            'message'  => $message,
        ] = $this->lockJob($db, $redis, $pid, $options);

        if (!$isAllow) {
            return ['is_ack' => $isAck, 'message' => $message];
        }

        /**********************
         * is_allow == true
         * 允许往下一步进行导出操作
         ***********************/

        $data       = [];
        $field      = [];
        $dataIndex  = [];
        $summaryRow = [];
        $maxPage    = [];

        try {
//            $loki           = $this->getLoki($userId, $id);
            $jobCacheKi     = $redis->getCacheKey($this->getCacheInfoKey($userId, $id));
            $jobInfo        = $this->getJobInfo($db, ['user_id' => $userId, 'id' => $id]);
            $specificFields = []; // 指定表头, 为空则不指定

            if (empty($jobInfo) || empty($jobInfo['request_body'])) {
                throw new MissJobException(static::MESSAGE_LIST['missing_job']);
            }

            $requestBody = \json_decode($jobInfo['request_body'], true);

            [
                'params'       => $params,
                'request_uri'  => $requestUri,
                'access_token' => $token,
            ] = $requestBody;

            if (!empty($requestBody['fields'])) {
                $specificFields = $requestBody['fields'];
            }

//            if ($this->isStdout) {
//                $progress = $this->climate->progress();
//            }

            foreach (
                $this->getContentGen($params, $requestUri, $token) as $key => $item
            ) {
                if ($key === 'fields') {
                    /**
                     * 获取表头和对应的数据索引
                     */
                    [
                        'title' => $field, 'index' => $dataIndex,
                    ] = $this->getFields(($item['fields'] ?? []), $specificFields);

                    $dataIndex = array_fill_keys($dataIndex, 0);
                    continue;
                }

                /**
                 * 以下为列表数据操作
                 */
                if (empty($item['data']['list'])) continue;

                if (empty($summaryRow)) {
                    $summary    = $item['data']['summary'] ?? [];
                    $summaryRow = array_merge($dataIndex, array_intersect_key($summary, $dataIndex));
                }

                if ((int)$key === 1) {
                    $maxPage = $item['data']['max_page'] ?? 0;
                }

                @$this->updateCacheJobStatus($redis, $jobCacheKi, $key, $maxPage);

                $list = &$item['data']['list'];
                foreach ($list as $foo) {
                    $dd = array_merge($dataIndex, array_intersect_key($foo, $dataIndex));

                    foreach ($dd as &$chill) {
                        // 预防数字类型被截取
                        if (is_numeric($chill) && strlen($chill) > 10) {
                            $chill .= "\t";
                        }

                        if (is_array($chill)) {
                            $chill = implode(',', Arr::flatten($chill));
                        }
                    }

                    $data[] = $dd;
                }
            }
            echo '读取完成' . PHP_EOL;
        }
        catch (\Throwable $throwable) {
            $message = $throwable->getMessage();
            echo $message . PHP_EOL;
//            $this->unlockJob($redis, $options);
            return ['is_ack' => false, 'message' => $message];
        }

        $sp   = new Spreadsheet();
        $data = array_filter(array_merge([$field], [$summaryRow], $data));

        foreach ($data as &$row) {
            foreach ($row as &$chillItem) {
                if ($chillItem && strpos($chillItem, '=') === 0) {
                    $chillItem = "'" . $chillItem;
                }
            }
        }

        $sp->getActiveSheet()->fromArray($data);

        $filePath = $this->newFilePath($userId, $params, ($jobInfo['job_id'] ?? ''), 'csv');
        $writer   = new Csv($sp);

        $writer
            ->setUseBOM(false)
            ->setOutputEncoding('GBK')
            ->setDelimiter(',')
            ->setEnclosure('"')
            ->setLineEnding("\r\n")
            ->setSheetIndex(0);

        try {
            $writer->save($filePath);
            $this->finishJob($db, $id, $filePath);

            // 企微通知
            Util::sendWXAppMsg($userId, '手盟数据下载通知', '您有下载任务已完成,请到手盟数据后台查看');
        }
        catch (Exception $e) {
            $message = $e->getMessage();
            $isAck   = false;
        }
        catch (\Exception $e) {
        }

        return ['is_ack' => $isAck, 'message' => $message];
    }

    /**
     *
     * @param          $params
     * @param          $requestUri
     * @param          $token
     *
     * @return \Generator
     * @throws GuzzleException
     */
    public function getContentGen($params, $requestUri, $token): \Generator
    {
        $isRunning = true;
        $page      = 1;
        $pageSize  = 10000;
        $total     = 0;
        $maxPage   = 0;

        // 请求表头
        $fieldOp = array_merge($params, ['page' => $page, 'page_size' => $pageSize, 'method' => ['fields']]);
        $rre     = $this->getContentByRequest($requestUri, $fieldOp, ['Access-Token' => $token]);

        if ($rre['code'] != 200 || empty($rre['data'])) {
            throw new RuntimeException("请求表头时发生错误");
        }

        yield 'fields' => $rre['data'];

        do {
            $option = array_merge($params, ['page' => $page, 'page_size' => $pageSize, 'method' => ['data']]);
            $rre    = $this->getContentByRequest($requestUri, $option, ['Access-Token' => $token]);

            if (
                $rre['code'] != 200
                || empty($rre['data'])
            ) {
                $isRunning = false;
            }

            if ($isRunning) {
                $data = $rre['data'];

                if (1 === $page) {
                    $total            = $data['total'] ?? 0;
                    $maxPage          = ceil($total / $pageSize);
                    $data['max_page'] = $maxPage;
                }

                yield $page => [
                    'data' => $data,
                ];
            }

            if ($maxPage <= $page) {
                $isRunning = false;
            }

            $page++;
        }
        while ($isRunning);
    }

    /**
     * @param        $uri
     * @param array $params
     * @param array $header
     * @param string $method
     *
     * @return mixed
     * @throws \Exception
     * @throws GuzzleException
     */
    protected function getContentByRequest($uri, array $params = [], array $header = [], string $method = 'get')
    {
        $method = strtolower($method);
        $method = $method === 'get' ? 'get' : 'post';

        $options = [];
        $httpC   = new Client();

        if (!empty($header)) {
            $options['headers'] = $header;
        }

        if ($method === 'post') {
            $options['form_params'] = $params;
            $result                 = $httpC->post($uri, $options);
        }
        else {
            $options['query'] = $params;
            $result           = $httpC->get($uri, $options);
        }

        return \json_decode($result->getBody(), true);
    }


    /**
     * 获取未被标记成功或异常状态的导出任务
     *
     * @param Database $db
     * @param array $params
     *
     * @return int
     */
    public function getJobIfNotFinish(Database &$db, array $params = []): int
    {
        $qb = $db->select();

        if (!empty($params['id'])) $qb->where('id', $params['id']);

        $qb
            ->from(static::TABLE_LOG)
            ->where('job_status', '<=', 2)
            ->where('job_status', '>=', 0);

        return $qb->count();
    }

    /**
     * @param Database $db
     * @param array $params
     *
     * @return array|mixed
     */
    public function getJobInfo(Database &$db, array $params = [])
    {
        $qb = $db->select();
        if (!empty($params['id'])) $qb->where('id', $params['id']);
        if (!empty($params['user_id'])) $qb->where('user_id', $params['user_id']);

        return $qb->from(static::TABLE_LOG)->fetchAll()[0] ?? [];
    }

    /**
     * 获取锁
     *
     * @param RedisCache $redis
     * @param            $key
     * @param mixed $value
     *
     * @return array|bool|\Redis
     */
    private function getLock(RedisCache &$redis, $key, $value = 1)
    {
        return $redis->setnx($key, $value);
    }

    /**
     * 锁定任务
     *
     * @param Database $db
     * @param RedisCache|\Redis $redis
     * @param                   $pid
     * @param                   $options
     *
     * @return bool[]
     */
    private function lockJob(Database &$db, &$redis, $pid, $options): array
    {
        $isAllow = true;
        $isAck   = true;
        $message = 'success';

        try {
            [
                'user_id' => $userId,
                'id'      => $id,
            ] = $options;

            $infoKey = $redis->getCacheKey($this->getCacheInfoKey($userId, $id));

            $this->updateJobStatus($db, $id, 2);
            $redis->hSet($infoKey, 'progress', 0);
            $redis->hSet($infoKey, 'id', $id);
            $redis->expire($infoKey, 60 * 60 * 24);
        }
        catch (ExistJobException|LockOverException|\Throwable $throwable) {
            $isAllow = false;
            $isAck   = false;
            $message = $throwable->getMessage();
            echo $message . PHP_EOL;
        }

        return [
            'is_allow' => $isAllow,
            'is_ack'   => $isAck,
            'message'  => $message,
        ];
    }

    /**
     * @param RedisCache|\Redis $redis
     * @param                   $options
     *
     * @return void
     * @throws \RedisException
     */
    private function unlockJob(&$redis, $options)
    {
        [
            'user_id' => $userId,
            'id'      => $id,
        ] = $options;

        $loki = $redis->getCacheKey($this->getLoki($userId, $id));
        $redis->del($loki);
    }

    /**
     * @param RedisCache|\Redis $redis
     * @param string $loki
     * @param                   $ttl
     *
     * @return bool|\Redis
     * @throws \RedisException
     */
    private function lokiExpire(&$redis, string $loki, $ttl)
    {
        return $redis->expire($redis->getCacheKey($loki), $ttl);
    }

    /**
     * 延长锁时间
     *
     * @param RedisCache|\Redis $redis
     * @param string $loki
     * @param                   $ttl
     *
     * @return void
     * @throws \RedisException
     */
    private function extendLokiTTL(&$redis, string $loki, $ttl)
    {
        $rPid = $redis->get($loki);

        if ($rPid == posix_getpid()) {
            $redis->expire($redis->getCacheKey($loki), $ttl);
        }
    }

    /**
     * 获取锁key
     *
     * @param $userId
     * @param $id
     *
     * @return string
     */
    private function getLoki($userId, $id): string
    {
        return static::JOB_NX_PRE . ':' . $userId . ':' . $id;
    }

    /**
     * @param            $userId
     * @param string|int $id
     *
     * @return string
     */
    private function getCacheInfoKey($userId, $id = '*'): string
    {
        return static::JOB_INFO_PRE . ':' . $userId . ':' . $id;
    }

    /**
     * @param array $fieldList
     *
     * @return array[]
     */
    private function getFields(array $fieldList, array $specificFields = []): array
    {
        $title = [];
        $index = [];

        if (!empty($specificFields)) {
            $indexFields = array_fill_keys($specificFields, '');
        }
        else {
            $indexFields = [];
        }

        foreach ($fieldList as $i => $field) {
            $dataIndex = $field['dataIndex'] ?? '';

            if (!empty($specificFields)) {
                if (!in_array($dataIndex, $specificFields)) {
                    continue;
                }
            }
            $indexFields[$dataIndex] = $field['title'] ?? '';
        }

        return ['title' => array_values($indexFields), 'index' => array_keys($indexFields)];
    }

    /**
     * @param        $userId
     * @param        $params
     * @param        $uuid
     * @param string $suffix
     *
     * @return string
     */
    public function newFilePath($userId, $params, $uuid, string $suffix = 'xlsx'): string
    {
        $storagePath = '/data/www/spy.910admin.com/';
        $today       = date('Y-m-d');
        $path        = $storagePath . 'public/download/report_form';
        $path        .= '/' . $userId . '/' . $today;

        if (!is_dir($path)) {
            mkdir($path, 0777, true);
        }

        $rangeDate = ($params['range_date_start'] ?? '') . '_' . ($params['range_date_end'] ?? '');
        $fileName  = $rangeDate . '_' . $uuid . '.' . $suffix;

        return $path . '/' . $fileName;
    }

    /**
     * @param Database $db
     * @param          $id
     * @param          $filePath
     *
     * @return int
     */
    private function finishJob(Database &$db, $id, $filePath): int
    {
        $update = $db->table(static::TABLE_LOG)->update();

        $update->set('job_status', 3);
        $update->set('file_path', $filePath);

        return $update->where('id', $id)->run();
    }

    /**
     * @param          $id
     * @param array $options
     *
     * @return int
     */
    public function updateJob($id, array $options = []): int
    {
        $db     = $this->getConn();
        $update = $db->table(static::TABLE_LOG)->update();

        foreach ($options as $field => $value) {
            $update->set($field, $value);
        }

        return $update->where('id', $id)->run();
    }


    /**
     * @param RedisCache|\Redis $redis
     * @param                   $key
     * @param                   $page
     * @param                   $maxPage
     *
     * @return void
     * @throws \RedisException
     */
    private function updateCacheJobStatus(&$redis, $key, $page, $maxPage)
    {
        if (empty($page) || empty($maxPage)) {
            $progress = 100;
        }
        else {
            $progress = number_format(math_eval('x/y * 100', ['x' => $page, 'y' => $maxPage]), 2);
        }

        $redis->hSet($key, 'progress', $progress);
    }

    /**
     * @param array $params
     *
     * @return array
     * @throws \RedisException
     */
    public function getExportJob(array $params): array
    {
        $db = $this->getConn();
        /**
         * @var RedisCache|\Redis $redis
         */
        $redis = \Plus::$app->redis;

        $qb = $db->select()->from(static::TABLE_LOG);

        if (empty($params['user_id'])) {
            return [];
        }

        $userId = $params['user_id'];

        $qb
            ->where('user_id', $userId)
            ->orderBy('id', $qb::SORT_DESC);

        if (!empty($params['id'])) {
            $qb->where('id', $params['id']);
        }

        $data = $qb->fetchAll();

        foreach ($data as &$item) {

            if ($item['job_status'] == 3) {
                $item['progress'] = '100%';
            }
            else {
                $progress         = $redis->hGet($redis->getCacheKey($this->getCacheInfoKey($userId, $item['id'])), 'progress') ?: 0;
                $item['progress'] = ($progress ?? 0) . '%';
            }
        }

        return ['list' => $data];
    }


}
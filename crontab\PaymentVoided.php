<?php declare(ticks=1);

namespace app\crontab;

use app\models\baseConfPlatform\TbAdAccountExtConf;
use app\models\DdcPlatform\DwdSdkUserPaymentVoided;
use Plus\CLI\DaemonProcess;


/**
 * 同步付费退款订单
 * @package app/daemon
 */
class PaymentVoided extends DaemonProcess
{



    public function __construct()
    {

    }

    /**
     * @throws \Exception
     */
    public function run()
    {
        $args     = func_get_args();
        $params =   $args[0]["p"]?? "";
        $beginDate = $params[0]??date("Y-m-d");
        $endDate = $params[1]??date("Y-m-d");

        $startTime = strtotime($beginDate);
        $endTime = strtotime($endDate);


        while ($startTime <= $endTime) {
            $tDay = date("Ymd", $startTime);
            var_dump("日期:".$tDay);
            $tBeginTime = date("Y-m-d 00:00:00", $startTime);
            $tEndTime = date("Y-m-d 23:59:59", $startTime);

            //ios退款
            $sql = "select * from ios_receipt_voided where voided_time BETWEEN '{$tBeginTime}' and '{$tEndTime}' and is_voided=1";
            $data = \Plus::$app->py_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
            foreach ($data as $item){
                $sql = "INSERT IGNORE INTO dwd_sdk_user_payment_voided(ORDER_ID,VOIDED_TIME,VOIDED_TYPE) VALUES('".$item['order_id']."','".$item["voided_time"]."','ios')";
                \Plus::$app->ddc_platform->exec($sql);
            }
            $startTime += 86400;
        }


    }

}



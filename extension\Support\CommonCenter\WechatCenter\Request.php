<?php

namespace app\extension\Support\CommonCenter\WechatCenter;

use Plus\Net\Http;
use app\extension\Support\CommonCenter\ConfCenter\Uri as BizUri;
use app\extension\Support\CommonCenter\ConfCenter\Request as BizRequest;

class Request extends Http
{
    private string $tokenSuffix = 'cltj:wechat_appid_token:';

    /**
     * @throws \RedisException
     * @throws \Exception
     */
    public function get($params = null, $headers = [])
    {
        if (empty($params['access_token'])) {
            $params['access_token'] = $this->getAccessTokenFromBiz($params);
        }

        $params = array_diff_key($params, ['appid' => '', 'secret' => '']);

        return parent::get($params, $headers);
    }


    /**
     * 刷新请求Token
     * @param array $params
     * @return void
     * @throws \RedisException
     * @throws \Exception
     */
    public function refreshToken(array $params = [])
    {
        $uri                  = Uri::HOST . Uri::URI_REFRESH_TOKEN;
        $params['grant_type'] = 'client_credential';
        $params               = array_intersect_key($params, ['grant_type' => '', 'appid' => '', 'secret' => '']);

        $appId     = $params['appid'] ?? '';
        $http      = new Http($uri);
        $ret       = $http->get($params);
        $ret       = \json_decode($ret, true);
        $token     = $ret['access_token'] ?? '';
        $expiresIn = $ret['expires_in'] ?? 0;

        if (
            JSON_ERROR_NONE !== \json_last_error() || empty($token)
        ) {
            throw new \RuntimeException('[fail to get token]response data is null...');
        }

        $storeKey = $this->tokenSuffix . $appId;
        \Plus::$app->redis->setex($storeKey, $expiresIn, $token);

        return $token;
    }

    /**
     * @param array $params
     * @return void
     * @throws \RedisException|\Exception
     */
    public function getAccessToken(array $params = [])
    {
        $appId = $params['appid'] ?? '';

        if (empty($appId)) {
            throw new \RuntimeException('[fail to get token]missing required argument...');
        }

        $storeKey = $this->tokenSuffix . $appId;
        $token    = \Plus::$app->redis->get($storeKey);

        if (empty($token)) {
            $token = $this->refreshToken($params);
        }

        return $token;
    }

    /**
     * @throws \Exception
     */
    public function getAccessTokenFromBiz(array $params = [])
    {
        $appId = $params['appid'] ?? '';

        if (empty($appId)) {
            throw new \RuntimeException('[fail to get token]missing required argument...');
        }

        $req       = new BizRequest(BizUri::getHost() . BizUri::URI_WX_TOKEN);
        $responser = $req->get(['app_id' => $appId]);
        $ret       = \json_decode($responser, true);

        if (JSON_ERROR_NONE !== \json_last_error() || empty($ret['data']['token'])) {
            throw new \RuntimeException('[fail to get token]response data is null...');
        }

        return $ret['data']['token'];
    }

}
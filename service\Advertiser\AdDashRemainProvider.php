<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\BaseProvider;
use app\service\General\BizTagsServ;
use Smarty\Exception;

/**
 * 留存查询
 * @date 2025/06/25
 */
class AdDashRemainProvider extends BaseProvider
{
    const RESULT_INFO    = 1;
    const RESULT_SUMMARY = 2;
    const RESULT_TOTAL   = 4;
    const RESULT_ALL     = 7;


    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $columns
     * @param int   $mode
     * @param bool  $isApiRule
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function getData(
        array $params,
        array $groups = [],
        array $paginate = [],
        array $sort = [],
        array $columns = [],
        int   $mode = self::RESULT_ALL,
        bool  $isApiRule = false
    ): array
    {
        $tplMap        = [
            'info'  => 'sql/advertise/ad_remain/creative_retain.tpl',
            'total' => 'sql/advertise/ad_remain/creative_retain_total.tpl',
        ];
        $totalCnt      = 1;
        $result        = ['total' => 0, 'list' => [], 'summary' => []];
        $isRealTime    = $params['is_realtime'] ?? 0;
        $minDate       = min($params['range_date']);
        $showType      = Arr::pull($params, 'show_type', 0);
        $db            = $this->dorisConn();
        $adChannels    = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $maxRetainNode = days_apart(new \DateTime(), $minDate, true) - 1;

        $globalAssign = [
            'params'          => $params,
            'columns'         => ['new_user'],
            'ad_channels'     => $adChannels,
            'show_type'       => $showType,
            'max_retain_node' => $maxRetainNode,
        ];

        if (!$isRealTime) $globalAssign['last_retain_date'] = date('Y-m-d');
        if (!$isApiRule) $globalAssign['power_join_sql'] = \Plus::$service->admin->powerSubSQL();

        if ($mode & self::RESULT_TOTAL) {
            $totalTpl = \Plus::$app->sqlTemplates->createTemplate($tplMap['total']);
            $this->tplAssign($totalTpl, $globalAssign);
            $totalSQL = $totalTpl->fetch();
            $totalCnt = $result['total'] = $db->query($totalSQL)->fetch()['total_count'] ?? 0;
        }

        if (
            (($mode & self::RESULT_INFO) && ($mode & self::RESULT_TOTAL) && $totalCnt > 0)
            || ($mode & self::RESULT_INFO && !($mode & self::RESULT_TOTAL))
        ) {
            $infoTpl    = \Plus::$app->sqlTemplates->createTemplate($tplMap['info']);
            $infoAssign = $globalAssign;

            if (!empty($groups)) $infoAssign['groups'] = $groups;
            if (!empty($paginate)) $infoAssign['paginate'] = $paginate;
            if (!empty($sort)) $infoAssign['sorts'] = $sort;

            $this->tplAssign($infoTpl, $infoAssign);

            $infoSQL        = $infoTpl->fetch();
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }

        if ($mode & self::RESULT_SUMMARY) {
            $summaryTpl = \Plus::$app->sqlTemplates->createTemplate($tplMap['info']);
            $this->tplAssign($summaryTpl, $globalAssign);
            $summarySQL        = $summaryTpl->fetch();
            $result['summary'] = $db->query($summarySQL)->fetch();
            $result['time']    = $result['summary']['max_update_time'] ?? '';
        }

        return $result;
    }
}
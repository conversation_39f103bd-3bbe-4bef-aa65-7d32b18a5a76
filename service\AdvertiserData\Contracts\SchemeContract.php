<?php

namespace app\service\AdvertiserData\Contracts;

interface SchemeContract
{
    /**
     * @param \Closure $callback
     *
     * @return mixed
     */
    public function scope(\Closure $callback);

    /**
     * @return string
     */
    public function toSql(): string;

    /**
     * toSql执行前的准备动作
     *
     * @return mixed
     */
    public function prependToSql();

    /**
     * @param        $table
     * @param string $join
     * @param string $cond
     *
     * @return static|mixed
     */
    public function join($table, string $join = 'inner', string $cond = '');

    /**
     * @param string $sql
     * @param string $mainTable
     *
     * @return static|mixed
     */
    public function joinPowerSql(string $sql, string $mainTable = '');
}
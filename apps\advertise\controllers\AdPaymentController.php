<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\advertise\AdPaymentInfoLogic;
use Smarty\Exception;

/**
 * 广告付费详情查询
 *
 * @date 2023/08/11
 */
class AdPaymentController extends BaseTableController
{

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];

        $groups = Arr::wrap($params->pull('groups', []));
        $sort   = [];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = ['pay_money' => 'DESC'];
        }

        try {
            $options = $params->toArray();
            $logic   = new AdPaymentInfoLogic();
            $infoRe  = $logic->getPaymentInfo($options, $groups, ['page' => $page, 'page_size' => $pageSize], $sort);

            if (!empty($infoRe['list'])) {
                $list = &$infoRe['list'];

                foreach ($list as &$item) {
                    if ($item['is_exception']) {
                        $item['remark'] = '异常用户预警';
                    }
                }
            }
        }
        catch (\Throwable $th) {
            return $this->error($th->getMessage());
        }

        return $infoRe;
    }

    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '核心账号', 'dataIndex' => 'main_account'],
            ['title' => '新增时间', 'dataIndex' => 'newlogin_time'],
            ['title' => '付费金额', 'dataIndex' => 'pay_money'],
            ['title' => '付费次数', 'dataIndex' => 'pay_count'],
            ['title' => '累计付费金额', 'dataIndex' => 'total_paid_money'],
            ['title' => '备注', 'dataIndex' => 'remark', 'tip' => '"创角后5分钟内就充值,或者新增2天内购买非6元的钻石直购礼包"的用户标识为异常预警'],
        ];

        return ['fields' => $fields];
    }


    protected function registerParams($key = null): Collection
    {
        $today = date('Y-m-d');

        $collect = collect([
            ['field' => 'dimension'],
            ['field' => 'target'],
            ['field' => 'range_date_start', 'default' => $today], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
            ['field' => 'cp_game_id'], // 游戏原名
            ['field' => 'game_id'], // 游戏统计名
            ['field' => 'app_show_id'], // 游戏前端名
            ['field' => 'channel_main_id'], // 主渠道
            ['field' => 'channel_id'], //渠道
            ['field' => 'promotion_channel_id'], // 推广渠道
            ['field' => 'platform_id'], // 客户端ID
            ['field' => 'package_id'], // 包号
            ['field' => 'promotion_id'], // 推广分类
            ['field' => 'department_id'], // 部门
            ['field' => 'user_id'], // 投放人
            ['field' => 'ad_account'], // 投放账号
            ['field' => 'advertiser_id'], // 广告账号ID
            ['field' => 'campaign_id'], // 广告组ID
            ['field' => 'campaign_name'], // 广告组名称
            ['field' => 'plan_name'], // 计划名
            ['field' => 'plan_id'], // 计划ID
            ['field' => 'creative_name'], // 创意名
            ['field' => 'creative_id'], // 创意ID
            ['field' => 'data_range'], // 数据范围
            ['field' => 'is_has_natural', 'default' => 1], // 是否含自然量
            ['field' => 'account_id'], // 账号ID搜索
            ['field' => 'data_scope'], // 数据范围搜索
            ['field' => 'channel_id_tags'], // 渠道标签
            ['field' => 'channel_main_id_tags'], // 主渠道标签
            ['field' => 'package_id_tags'], // 包号标签
            ['field' => 'game_id_tags'], // 包号标签
            ['field' => 'row_option'],
            ['field' => 'page_size', 'default' => 50],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups'],
            ['field' => 'method', 'default' => ['data', 'fields']],
            ['field' => 'marketing_goal'],
        ]);

        if (!empty($key)) {
            $collect = $collect->where('field', $key);
        }

        return $collect;
    }


    /**
     * 付费明细按核心账号汇总
     *
     * @return array
     * @throws Exception
     */
    public function paymentCoreAccountAction(): array
    {
        $request = $this->getAllParam(\Plus::$app->request);
        $methods = Arr::wrap($request->pull('method'));
        $data    = [];

        foreach ($methods as $foo) {
            if ($foo == 'data') {
                $data = array_merge($data, $this->paymentCoreData($request));
            }
            elseif ($foo == 'fields') {
                $data = array_merge($data, $this->paymentCoreFields($request));
            }
        }

        return $this->success($data);
    }

    /**
     * @throws Exception
     */
    private function paymentCoreData(Collection $request): array
    {
        $paginate = [
            'page'      => $request->pull('page', 1),
            'page_size' => $request->pull('page_size', 100),
        ];
        $groups   = Arr::wrap($request->pull('groups', []));
        $params   = $request->toArray();
        $logic    = new AdPaymentInfoLogic();

        return $logic->paymentInfoGroupMainAccount($params, $groups, $paginate);
    }

    /**
     * @param Collection $request
     * @return array[]
     */
    private function paymentCoreFields(Collection $request): array
    {
        $fields = [
            ['title' => '核心账号', 'dataIndex' => 'core_account'],
            ['title' => '新增时间', 'dataIndex' => 'newlogin_time'],
            ['title' => '付费金额', 'dataIndex' => 'pay_money'],
            ['title' => '付费次数', 'dataIndex' => 'pay_count'],
        ];

        return ['fields' => $fields];
    }


}
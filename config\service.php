<?php
/**
 * 服务配置
 */

use app\service\FileIO;
use app\service\Queue;
use app\service\queue\FileQueue;
use app\service\User;
use app\service\Admin;
use app\service\Auth;
use app\service\user\Login;
use app\service\Options;

return [
    'user'          => [
        'class'           => User::class,
        //子服务
        'childrenService' => [
            'login' => [
                'class'   => Login::class,
                'maxTime' => 1200,
            ],
        ],
    ],
    'options'       => [
        'class' => Options::class,
    ],
    'fileIO'        => [
        'class' => FileIO::class,
    ],
    'queue'         => [
        'class'           => Queue::class,
        'childrenService' => [
            'fileQueue'     => [
                'class' => FileQueue::class,
                // 保存路径
                '/dev/shm/queue',
                // 注册队列
                [
                    Queue::QUEUE_EXPORT_TASK_LOG,
                ],
            ],
            'rabbitQueue'   => [
                'class'    => \framework\rabbit\RabbitQueue::class,
                'host'     => 'PRO' == APP_EVN ? 'rabbitmq.910app.com' : '**************',
                'port'     => 'PRO' == APP_EVN ? '5672' : 5673,
                'vhost'    => 'oa',
                'username' => 'spy',
                'password' => 'Spy910app',
                'debug'    => false,
                'auto_ack' => false,
                [
                    Queue::BASIC_USER_SPY,
                ],
            ],
            'liveSyncQueue' => [
                'class'    => \framework\rabbit\RabbitQueue::class,
                'host'     => 'PRO' == APP_EVN ? 'rabbitmq.910app.com' : '**************',
                'port'     => 'PRO' == APP_EVN ? '5672' : 5673,
                'vhost'    => 'live',
                'username' => 'spy',
                'password' => 'Spy910app',
                'debug'    => false,
                'auto_ack' => false,
                [
                    Queue::LIVE_PACKAGE_SPY,
                ],
            ],
        ],
    ],
    'admin'         => [
        'class' => Admin::class,
    ],
    'auth'          => [
        'class' => Auth::class,
    ],
    // 数据共用加解密
    'dataEncryptor' => [
        'class' => \app\service\DataEncryptor::class,
    ],
];

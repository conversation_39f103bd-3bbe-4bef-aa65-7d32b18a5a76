<?php

namespace app\apps\api\controllers;

use app\extension\Exception\ParameterException;
use app\models\baseConfPlatform\TbOathConf;
use app\service\Admin;
use app\util\Common;
use Plus\MVC\Controller\JsonController;

/**
 * Class user
 * <AUTHOR>
 */
class UserController extends JsonController
{

    /**
     * get access token
     * @param array $data 请求参数
     * @return array
     */
    public function onlineTimeAction()
    {
        //params
        $coreAccount = $this->getValue("core_account");
        $date        = $this->getValue("date");
        $cpGameId    = $this->getValue("cp_game_id");
        $gameId      = $this->getValue("game_id");
        if (!$coreAccount) {
            throw new ParameterException("账户不能为空");
        }
        $isDate = strtotime($date) ? strtotime($date) : false;
        if ($isDate == false) {
            throw new ParameterException("日期格式错误");
        }

        //where
        $where = " TDAY = '{$date}' AND  CORE_ACCOUNT='$coreAccount' ";
        if ($cpGameId) {
            $where.= " AND CP_GAME_ID = {$cpGameId}";
        }
        if ($gameId) {
            $where.= " AND GAME_ID = {$gameId}";
        }

        //构造活跃表名
        $m     = (int)date("m", strtotime($date));
        $qoq   = ceil($m / 3);
        $table = "dwd_sdk_user_active_daily_" . date("Y", strtotime($date)) . $qoq;

        //search
        $sql = "SELECT cp_game_id,SUM(ONLINE_TIME) online_time FROM {$table} where {$where} GROUP BY CP_GAME_ID";
        $data = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        return $this->success($data);
    }

    //活动在线时长数据
    function activityByOnlineTimeAction()
    {
        $coreAccount = $this->getValue("core_account");
        if (!$coreAccount) {
            throw new ParameterException("账户不能为空");
        }
        $cachKey = "activity1:user:{$coreAccount}";
        $maxtime = 24*60;
        if ($db = Common::pingDoris()) {
            $data = \Plus::$app->redis->get($cachKey);
            if($data){
                $data = json_decode($data,true);
                $data["max_online_time"] = $data["max_online_time"]>$maxtime?$maxtime:$data["max_online_time"];
            }else{
                //最近半年累计在线时长
                $date = date("Y-m-d H:i:s",strtotime("-6 months"));
                $sql = "SELECT  sum(ONLINE_TIME) total_online_time FROM ddc_platform.dwd_sdk_user_active_daily
                WHERE core_account = '{$coreAccount}' and TDAY >='{$date}'";
                $data = $db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                $totalOnlineTime = $data[0]["total_online_time"]??0;

                //历史最大在线时长 和 日期 排除异常数据
                $sql = "SELECT MAX_BY(TDAY,ONLINE_TIME) max_date,max(ONLINE_TIME) max_online_time FROM ddc_platform.dwd_sdk_user_active_daily
                    WHERE core_account = '{$coreAccount}' and ONLINE_TIME <1440";
                $data = $db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                if(!$data[0]["max_date"]){
                    //历史最大在线时长
                    $sql = "SELECT MAX_BY(TDAY,ONLINE_TIME) max_date,max(ONLINE_TIME) max_online_time FROM ddc_platform.dwd_sdk_user_active_daily
                    WHERE core_account = '{$coreAccount}'";
                    $data = $db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
                    $maxOnlineTime = $data[0]["max_online_time"]??0;
                    $maxDate = $data[0]["max_date"]??'';
                }else{
                    $maxOnlineTime = $data[0]["max_online_time"]??0;
                    $maxDate = $data[0]["max_date"]??'';
                }

                $data= [
                    "max_date"=>$maxDate,
                    "total_online_time" => $totalOnlineTime,
                    "max_online_time" => $maxOnlineTime>$maxtime?$maxtime:$maxOnlineTime
                ];
                //结果缓存一天
                \Plus::$app->redis->set(  $cachKey, json_encode($data),3600*24);
            }

            return $this->success($data);
        }else{
            return $this->error("服务器繁忙，请稍后再试");
        }
    }

}

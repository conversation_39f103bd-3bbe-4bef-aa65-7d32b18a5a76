<?php

namespace app\service\ComprehensiveIndex;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Macroable\Traits\Macroable;
use app\models\DdcPlatform\DwsPackageBaseDaily;
use app\models\DdcPlatform\DwsPackagePaymentDaily;
use Aura\SqlQuery\QueryFactory;
use Plus\SQL\Medoo;

class OperatorServ
{
    /**
     * 看板运营指标
     *
     * @param array $wheres
     * @return array
     * @throws \Exception
     */
    public function fetchDashboardIndex(array $wheres = []): array
    {
        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $powerSql = \trim(str_replace('POWER', '', $powerSql));
        $powerSql = \trim($powerSql, '()');

        $today = date('Y-m-d');
        [
            $rangeStart, $rangeEnd,
        ] = [
            $wheres['range_time_start'] ?? $today,
            $wheres['range_time_end'] ?? $today,
        ];


        $builder = new QueryFactory('mysql');
        $stm     = $builder->newSelect();

        $stm
            ->from('ddc_platform.dws_package_base_daily as t1')
            ->leftJoin(
                'ddc_platform.dws_package_payment_daily t2',
                't1.tday = t2.tday and t1.package_id = t2.package_id')
            ->joinSubSelect('inner', $powerSql, 'power', 'power.package_id = t1.package_id');

        $stm->where("t1.tday between '{$rangeStart}' and '{$rangeEnd}'");

        if (!empty($wheres['cp_game_id'])) {
            $stm->where("t1.cp_game_id = {$wheres['cp_game_id']}");
        }

        $cols = [
            't1.tday',
            'SUM(activate_device) as activate_device',
            'SUM(firstlogin_user) as firstlogin_user',
            'SUM(firstlogin_role) as firstlogin_role',
            'SUM(firstlogin_active_user) as firstlogin_active_user ',
            'SUM(firstlogin_pay_user) as  firstlogin_pay_user',
            'SUM(firstlogin_pay_money) as firstlogin_pay_money ',
            'SUM(firstlogin_pay_money_new) as firstlogin_pay_money_new',
            'SUM(firstlogin_pay_money) as firstlogin_pay_money',
            'SUM(firstlogin_role) as create_role_new',
        ];

        $stm->cols($cols);
        $stm->groupBy(['t1.tday']);

        $result = \Plus::$app->ddc_platform->query($stm->__toString())->fetchAll(\PDO::FETCH_ASSOC);

        return empty($result)
            ? []
            : array_column($result, null, 'tday');
    }

}
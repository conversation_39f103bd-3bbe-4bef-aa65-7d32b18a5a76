<?php
/**
 * 百度小游戏数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 * phpcs:disable
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class BaiduXcx extends AdBaseInterface
{
    private $tokenArr;
    const CONVERT_ACTIVE   = 4;
    const CONVERT_REGISTER = 25;
    const CONVERT_PURCHASE = 26;
    const CONVERT_LOGIN    = 49;


    /**
     * 上报登录
     * @param array $info
     * @param array $ext
     */
    public function uploadLogin($info, $ext = [])
    {
        $this->upload($info, 'LOGIN');
    }


    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY');
    }

    /**
     * 公共上报方法
     * @param $info
     * @param $type
     */
    private function upload($info, $type)
    {
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'baidu_xcx';
        $logInfo['log_type']     = 'reported_platform_log';
        $packageId               = $info['PACKAGE_ID'];

        if (empty($packageId)) {
            return;
        }

        $clickId = $info['EXT_CLICK']['bd_vid'] ?? '';
        /**
         * @date 2024/02/02
         * @desc 百度小程序根据他们给定的logidUrl进行上报, 现在暂时写死配置
         */
        if (in_array($packageId, ['65560001', '65560002'])) {
            $logidUrl = 'https://aisite.wejianzhan.com/site/wjzltul9/f2fec7df-412d-400a-a966-28a24b37a7ba?bd_vid=' . $clickId;
        } else {
            $logidUrl = 'https://aisite.wejianzhan.com/site/wjzy2nd0/8f13cb0b-1604-4ae2-80e0-a83b7be5655c?bd_vid=' . $clickId;
        }

        $requestParams = [
            "conversionTypes" => [
                [
                    "logidUrl"    => $logidUrl,
                    "convertTime" => time(),
                ],
            ],
        ];

        switch ($type) {
            case 'ACTIVE':
                $typeName                                       = "激活";
                $requestParams["conversionTypes"][0]["newType"] = self::CONVERT_ACTIVE;
                break;
            case 'REG':
                $typeName                                       = "注册";
                $requestParams["conversionTypes"][0]["newType"] = self::CONVERT_REGISTER;
                break;
            case 'PAY':
                $typeName                                            = "付费";
                $requestParams["conversionTypes"][0]["newType"]      = self::CONVERT_PURCHASE;
                $requestParams["conversionTypes"][0]["convertValue"] = ($info["MONEY"] ? $info["MONEY"] : 0) * 100;
                break;
            case 'LOGIN':
                $typeName                                       = "登录";
                $requestParams["conversionTypes"][0]["newType"] = self::CONVERT_LOGIN;
                break;
            default:
                return;
        }// end switch()

        $url   = "https://ocpc.baidu.com/ocpcapi/api/uploadConvertData";
        $svKey = $info['SV_KEY'] ?? 'none';
        $token = $this->tokenArr[$svKey] ?? '';
        if (!$token) {
            if (!isset($info['EXT_CLICK']['userid'])) {
                \Plus::$app->log->alert(json_encode($info).'百度小程序没有找到userid', [], self::LOG_DIR);
                return;
            }
            $sql                = "SELECT EXT FROM tb_ad_account_conf WHERE ACCOUNT_ID= '" . $info['EXT_CLICK']['userid'] . "'";
            $accountConf        = \Plus::$app->base_conf_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
            $accountConf["EXT"] = $accountConf["EXT"] ? json_decode($accountConf["EXT"], true) : [];
            $token              = !empty($accountConf["EXT"]["xcxToken"]) ? $accountConf["EXT"]['xcxToken'] : 'ahQhoBNRR71iIFMARR2r5ydDXgnONnmu@T5884NEoaEd2dtpvACl9DmZdUGGZ1aAH';
        }
        if (!empty($token)) {
            $logInfo['request']     = json_encode(['url' => $url, 'params' => $requestParams]);
            $this->tokenArr[$svKey] = $token;
            $requestParams["token"] = $token;

            $http = new Http($url);
            $res  = $http->postJson($requestParams);

            //记录上报结果
            $logInfo['response'] = $res;
            $resArr              = json_decode($res, true);

            if (isset($resArr['header']['status']) && $resArr['header']['status'] == 0) {
                $logInfo['reported_status'] = 1;
            } else {
                $logInfo['reported_status'] = -1;
            }
            $this->log($info, $logInfo, $res, $url);
        } else {
            \Plus::$app->log->alert(json_encode($info).'百度小程序没有找到token', [], self::LOG_DIR);
        }// end if()
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

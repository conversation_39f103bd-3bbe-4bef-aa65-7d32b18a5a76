{include file="sql/advertise/ad_targets/ad_payment_detail.tpl"}
select
b1.role_id, b1.role_name, b1.pay_time, b1.order_id,
order_money,
IF(b1.real_money =0, b1.money, b1.real_money) as cash,
b1.coupon_money, b1.decuct_coin as platform_coins,b1.payway as pay_way
from ddc_platform.dwd_sdk_adsource_game b2
right join payment_detail b1 on b1.source_id = b2.source_id
{if !empty($params)}
    {assign var="tag_first_where" value=1}
    {foreach $params as $key => $foo}
        {if $key eq 'plan_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                b2.plan_id in ('{$foo|join:"', '"}')
            {else}
                b2.plan_id = '{$foo}'
            {/if}
        {/if}
        {if $key eq 'package_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                b2.package_id in ('{$foo|join:"', '"}')
            {else}
                b2.package_id = '{$foo}'
            {/if}
        {/if}
    {/foreach}
{/if}
order by b1.pay_time desc
{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}
<?php

namespace app\models\OriginPlatform;

use Plus\MVC\Model\ActiveRecord;


/**
 * @TbSdkUserNewloginPackage
 *                          用户首次登录PACKAGE日志
 *
 *
 * @property int    ID
 * @property int    GAME_ID
 * @property int    PACKAGE_ID
 * @property string LOGIN_ACCOUNT
 * @property string CORE_ACCOUNT
 * @property string TIME
 * @property string TIME_SERVER
 * @property string DEVICE_ID
 * @property string MD5_DEVICE_ID
 * @property string DEVICE_CODE
 * @property string DEVICE_KEY
 * @property string ANDROID_ID
 * @property string OAID
 * @property string USERAGENT
 * @property string DEVICE_TYPE
 * @property string OS
 * @property string OS_VERSION
 * @property string SDK_VERSION
 * @property string GAME_VERSION
 * @property string NETWORK_TYPE
 * @property string MOBILE_TYPE
 * @property string SCREEN_WIDTH
 * @property string SCREEN_HEIGHT
 * @property string IP
 * @property string LBS
 * @property string REFER
 * @property string REFER_PARAM
 * @property int    CHANNEL_ALTER
 * @property int    CHANNEL_ID
 * @property int    SV_KEY
 * @property int    CLICK_ID
 * @property string CLICK_TIME
 */
class TbSdkUserNewloginPackage extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->origin_platform;
        parent::__construct($data);
    }

}
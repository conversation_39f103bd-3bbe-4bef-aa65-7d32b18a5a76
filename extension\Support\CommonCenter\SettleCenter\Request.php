<?php

namespace app\extension\Support\CommonCenter\SettleCenter;

use app\extension\Support\Contracts\AuthAble;
use app\extension\Support\Contracts\RequireAccessToken;
use Plus\Net\Http;

class Request extends Http
{
    private const SIGN_KEY = 'KJUL3%FPO83!KF(2SL3#';


    /**
     * @return array
     */
    private function getSign(): array
    {
        $timestamp = \time();

        return [
            'time' => $timestamp,
            'sign' => \md5($timestamp . static::SIGN_KEY),
        ];
    }


    /**
     * @param       $params
     * @param array $headers
     *
     * @return bool|string
     */
    public function get($params = null, $headers = [])
    {
        if (!isset($params['sign'])) {
            $params = array_merge($params, $this->getSign());
        }

        return parent::get($params, $headers);
    }

    /**
     * @param $params
     * @param $fileUpload
     * @param $headers
     *
     * @return bool|string
     */
    public function post($params = null, $fileUpload = false, $headers = [])
    {
        if (!isset($params['sign'])) {
            $params = array_merge($params, $this->getSign());
        }

        return parent::post($params, $fileUpload, $headers);
    }


}
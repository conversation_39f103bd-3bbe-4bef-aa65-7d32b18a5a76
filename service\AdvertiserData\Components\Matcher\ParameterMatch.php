<?php

namespace app\service\AdvertiserData\Components\Matcher;

abstract class ParameterMatch
{
    protected array $newLine      = [];
    protected array $filterKeygen = [];
    protected array $fieldReflect = [];
    abstract protected function processLine(): array;

    /**
     * @param callable ...$callback
     *
     * @return void
     */
    public function addLine(callable ...$callback)
    {
        $this->newLine = array_unique(array_merge($this->newLine, $callback));
    }

    /**
     * @param string[] $keygen
     *
     * @return void
     */
    public function addFilter(array $keygen)
    {
        $this->filterKeygen = array_unique(array_merge($this->filterKeygen, $keygen));
    }

    /**
     * @param mixed $field
     *
     * @return mixed
     */
    public function getReflectField(string $field)
    {
        if (isset($this->fieldReflect[$field])) {
            return $this->fieldReflect[$field];
        }

        return $field;
    }
}
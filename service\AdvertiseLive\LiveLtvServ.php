<?php

namespace app\service\AdvertiseLive;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiseLive\Components\Matcher\AdLiveMatch;
use app\service\AdvertiseLive\Components\Matcher\LiveBaseMatch;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class LiveLtvServ
{
    const MODE_ALL     = 3;
    const MODE_SUMMARY = 2;
    const MODE_LIST    = 1;

    /**
     * 根据排班和直播子查询关联
     *
     * @param SelectQuery $dutyQb
     * @param array       $params
     * @param array       $groups
     * @param array       $paginate
     * @param array       $sort
     * @param int         $mode
     *
     * @return array
     */
    public function ltvInfoByDutyQb(
        SelectQuery $dutyQb, array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $ltvQb = $this->getLtvHourlySubQb($params);
        $db    = $this->getConn();
        $qb    = $db->select()->from(new Fragment('(' . $ltvQb->__toString() . ') as t1'));

        $qb
            ->innerJoin(new Fragment('(' . $dutyQb->__toString() . ')'), 't2')
            ->onWhere(
                new Fragment('t1.tday between t2.start_time and t2.end_time')
            )->on(['t1.package_id' => 't2.package_id']);

        $paramMode = $mode & (static::MODE_SUMMARY | static::MODE_LIST | static::MODE_ALL);

        $qb->columns([
            't2.tday',
            't2.start_time as start_time',
            't2.end_time as end_time',
            't2.package_id',
            't2.range_time as range_time',
            't2.account_id as account_id',
            't2.account_name as account_name',
            'cp_game_id',
            'game_id',
            'anchor_id',
            'anchor_name',
            'docking_partner',
            'live_platform',
            'collaborative_code',
            'live_team',
            'assistant_id',
            't2.channel_id',
            't2.operation_account_id',

            new Fragment('SUM(t1.money) as money'),
            new Fragment('SUM(t1.money_all) as money_all'),
            new Fragment('SUM(t1.day_type) as day_type'),
        ]);

        $result  = [];
        $matcher = new AdLiveMatch([
            'package_id'      => 't1.package_id',
            'live_account_id' => 'account_id',
        ]);
        $matcher->exec($qb, $params);

        if ($paramMode & static::MODE_LIST) {
            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    $infoQb->groupBy($g);
                }
            }
            $result['list'] = $infoQb->fetchAll();
        }

        if ($paramMode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;

            $summaryQb->columns([
                new Fragment('SUM(t1.money) as money'),
                new Fragment('SUM(t1.money_all) as money_all'),
                new Fragment('SUM(t1.day_type) as day_type'),
            ]);

            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * @param array $params
     *
     * @return SelectQuery
     */
    private function getLtvHourlySubQb(array &$params): SelectQuery
    {
        $db       = $this->getConn();
        $qb       = $db->select()->from(TableCollect::DWS_CREATIVE_AD_LTV_HOURLY . ' as t_base');
        $powerSql = str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql());
        $qb
            ->innerJoin(new Fragment($powerSql), 'power')
            ->on([
                't_base.package_id' => 'power.package_id',
            ]);

        $matcher = new LiveBaseMatch([
            'channel_id'      => 'power.channel_id',
            'channel_main_id' => 'power.channel_main_id',
            'package_id'      => 't_base.package_id',
            'cp_game_id'      => 't_base.cp_game_id',
            'game_id'         => 't_base.game_id',
            'user_id'         => 'power.ad_user_id',
        ]);

        $matcher->addLine(function (SelectQuery &$qb, array &$params) {
            if (empty($params['day_type'])) return;

            $qb->where('day_type', new Parameter($params['day_type']));
        });

        $matcher->exec($qb, $params);

        $qb->columns([
            't_base.tday as tday',
            't_base.package_id as package_id',
            't_base.day_type as day_type',
            't_base.money as money',
            't_base.money_all as money_all',
        ]);

        $qb
            ->groupBy('t_base.tday')
            ->groupBy('t_base.package_id')
            ->groupBy('t_base.day_type');

        return $qb;
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }


}
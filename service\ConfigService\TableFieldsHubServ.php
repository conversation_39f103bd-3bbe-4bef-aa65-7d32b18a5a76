<?php

namespace app\service\ConfigService;

use app\extension\Exception\ParameterException;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Macroable\Traits\Macroable;
use app\service\ConfigService\Contracts\TableFieldAble;

/**
 * @method Collection|mixed getFields($options = null) 获取对应表头
 *
 */
class TableFieldsHubServ
{
    use Macroable {
        __call as macroCall;
    }

    /**
     * 服务提供者
     * @var TableFieldAble|mixed;
     */
    protected $provider;

    /**
     * @param string $method
     * @param mixed  ...$parameters
     */
    public function __construct(string $method, ...$parameters)
    {
        $this->registerProvider($method, $parameters);
    }

    /**
     * @param string $className
     * @return bool
     */
    protected function isCallAble(string $className): bool
    {
        return class_exists($className);
    }

    /**
     * @param string $className
     * @return string
     */
    protected function changeClass(string $className): string
    {
        return __NAMESPACE__ . '\\Tables\\' . $className;
    }

    public function __call($method, $parameters)
    {
        if (static::hasMacro($method)) {
            return $this->macroCall($method, $parameters);
        }

        if (is_callable([$this->provider, $method])) {
            return $this->provider->{$method}(...$parameters);
        }

        throw new \RuntimeException('unknown method');
    }

    /**
     * 注册提供者
     *
     * @param $className
     * @param ...$parameters
     * @return void
     */
    protected function registerProvider($className, ...$parameters)
    {
        if (
            !$this->isCallAble($class = $this->changeClass($className))
        ) {
            return;
        }

        $this->provider = new $class(...$parameters);
    }

}
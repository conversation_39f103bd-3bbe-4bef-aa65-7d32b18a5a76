<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;
use app\service\General\BizTagsServ;
use app\util\Common;
use Smarty\Exception;

class AdDashProvider
{
    const RESULT_INFO    = 1;
    const RESULT_SUMMARY = 2;
    const RESULT_TOTAL   = 4;
    const RESULT_ALL     = 7;


    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $columns
     * @param int   $resultMode
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function getData(
        array $params,
        array $groups = [],
        array $paginate = [],
        array $sort = [],
        array $columns = [],
        int   $resultMode = self::RESULT_ALL,
        bool  $isApiRule = false
    ): array
    {
        $result     = [];
        $db         = $this->getConn();
        $adChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');

        if (!$isApiRule) {
            $powerSQL = \Plus::$service->admin->powerSubSQL();
        }

        if ($resultMode & self::RESULT_INFO) {
            $infoTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_dashboard/ad_creative_dash.tpl');
            $infoTpl
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params)
                ->assign('groups', $groups);

            if (isset($powerSQL)) {
                $infoTpl->assign('power_join_sql', $powerSQL);
            }

            if (!empty($paginate)) {
                $infoTpl->assign('paginate', $paginate);
            }

            if (empty($sort)) {
                if (!empty($groups)) {
                    $sort = array_fill_keys($groups, '');
                }
            }

            if (!empty($sort)) {
                $infoTpl->assign('sorts', $sort);
            }

            if (!empty($columns)) {
                $infoTpl->assign('hit_fields', $columns);
            }

            $infoSQL = $infoTpl->fetch();
            @Common::dumpSql($infoSQL);
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }
        if ($resultMode & self::RESULT_SUMMARY) {
            $summaryTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_dashboard/ad_creative_dash.tpl');
            $summaryTpl
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params);

            if (isset($powerSQL)) {
                $summaryTpl->assign('power_join_sql', $powerSQL);
            }

            $summarySQL = $summaryTpl->fetch();
            @Common::dumpSql($summarySQL);

            $summaryResult     = $db->query($summarySQL)->fetch();
            $result['summary'] = $summaryResult;
            $result['time']    = $summaryResult['last_update_time'] ?? '';
        }
        if ($resultMode & self::RESULT_TOTAL) {
            $totalTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_dashboard/ad_creative_dash_total.tpl');
            $totalTpl
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params)
                ->assign('groups', $groups);

            if (isset($powerSQL)) {
                $totalTpl->assign('power_join_sql', $powerSQL);
            }

            $totalSQL = $totalTpl->fetch();

            $result['total'] = ($db->query($totalSQL)->fetch())['total_row'] ?? 0;
        }

        return $result;
    }


    /**
     * 获取数据库链接(使用doris)
     *
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('doris_entrance');
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $columns
     * @param int   $resultMode
     * @param bool  $isApiRule
     * @return void
     * @throws Exception
     * @throws \Exception
     */
    public function listFull(
        array $params, array $groups = [], array $paginate = [], array $sort = [], array $columns = [],
        int   $resultMode = self::RESULT_ALL, bool $isApiRule = false
    ): array
    {
        $tplMap = [
            'info_tpl'  => 'sql/advertise/ad_dashboard/creative_dash.tpl',
            'total_tpl' => 'sql/advertise/ad_dashboard/creative_dash_total.tpl',
        ];

        $result     = ['total' => 0, 'list' => [], 'summary' => []];
        $db         = $this->getConn();
        $adChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');

        if (!$isApiRule) {
            $powerSQL = \Plus::$service->admin->powerSubSQL();
        }

        if ($resultMode & self::RESULT_TOTAL) {
            $totalTpl = \Plus::$app->sqlTemplates->createTemplate($tplMap['total_tpl']);
            $totalTpl
                ->assign('ad_channels', $adChannels)
                ->assign('groups', $groups)
                ->assign('params', $params);

            if (isset($powerSQL)) {
                $totalTpl->assign('power_join_sql', $powerSQL);
            }

            $totalTpl->assign('columns', []);
            $totalSQL        = $totalTpl->fetch();
            $result['total'] = $db->query($totalSQL)->fetch()['total_row'] ?? 0;
        }

        if ($resultMode & self::RESULT_INFO) {
            if (isset($result['total'])) {
                if ($result['total'] == 0) {
                    return $result;
                }
            }
            $infoTpl = \Plus::$app->sqlTemplates->createTemplate($tplMap['info_tpl']);
            $infoTpl
                ->assign('ad_channels', $adChannels)
                ->assign('groups', $groups)
                ->assign('params', $params);

            if (isset($powerSQL)) {
                $infoTpl->assign('power_join_sql', $powerSQL);
            }

            if (!empty($paginate)) {
                $infoTpl->assign('paginate', $paginate);
            }

            if (empty($sort)) {
                if (!empty($groups)) {
                    $sort = array_fill_keys($groups, '');
                }
            }

            if (!empty($sort)) {
                $infoTpl->assign('sorts', $sort);
            }

            if (!empty($columns)) {
                $infoTpl->assign('columns', $columns);
            }
            $infoSQL = $infoTpl->fetch();
            @Common::dumpSql($infoSQL);
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }

        if ($resultMode & self::RESULT_SUMMARY) {
            $summaryTpl = \Plus::$app->sqlTemplates->createTemplate($tplMap['info_tpl']);
            $summaryTpl
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params);
            if (!empty($columns)) {
                $summaryTpl->assign('columns', $columns);
            }
            $summarySQL = $summaryTpl->fetch();
            @Common::dumpSql($summarySQL);
            $result['summary'] = $db->query($summarySQL)->fetch();
            $result['time']    = $result['summary']['last_update_time'] ?? '';
        }

        return $result;
    }


}
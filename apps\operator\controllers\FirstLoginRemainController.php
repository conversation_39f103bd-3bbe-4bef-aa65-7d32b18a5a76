<?php

namespace app\apps\operator\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Helpers\Calculator;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Helpers\IndexCalculators;
use app\apps\internal\Helpers\IndicatorsHelpers;
use app\apps\internal\Helpers\RemainCalculator;
use app\apps\internal\Traits\AdIndexCalculators;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\operator\Helpers\ConstFirstLogin;
use app\apps\operator\Traits\FirstLoginTrait;
use app\apps\operator\Traits\OperationCalculators;
use app\apps\operator\Traits\OperatorRequest;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\extension\Support\Helpers\Zakia;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\ConfigService\Tables\FirstLoginRemain;
use app\service\General\GeneralOptionServ;
use app\service\OperationData\BasicRemainIndex;
use app\service\OperationData\FirstLoginIndex;
use MongoDB\Driver\Exception\AuthenticationException;

/**
 * @FirstLoginRemainController 首登留存
 * @Route                      /operator/first-login-remain/*
 */
class FirstLoginRemainController extends BaseTableController
{
    use OperatorRequest, OperationCalculators,
        ColumnsInteract, AdIndexCalculators, FirstLoginTrait;

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $dateDimension = (int)$params->get('range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        [
            'pagination' => $pagination,
            'sort'       => $sort,
        ] = $this->changeDefaultParams($params);

        if ($params->has('groups')) {
            $groups = Arr::wrap($params->pull('groups'));
        }
        else {
            $groups = ['tday', 'package_id'];
        }

        $options                     = $params->toArray();
        $options['max_data_day_ago'] = 2;

        // 格式化月份日期
        if ($dateDimension === ConstFirstLogin::DIMENSION_MONTH) {
            $options['range_date_start'] = date("Y-m-d", strtotime($options['range_date_start']));
            $options['range_date_end']   = date("Y-m-d", strtotime("{$options['range_date_end']} +1 month -1day"));
        }
        // 看情况是否限制查询的最新日期是哪一天
        $options['range_date_start'] = $options['range_date_start'] > date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) ? date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) : $options['range_date_start'];
        $options['range_date_end']   = $options['range_date_end'] > date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) ? date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) : $options['range_date_end'];

        $today      = new \DateTime();
        $maxDayDiff = days_apart($options['range_date_start'], $today);

        $serv = new FirstLoginIndex();

        if ($dateDimension === ConstFirstLogin::DIMENSION_WEEK) {
            $result = $serv->listByWeek($options, $pagination, $groups, $sort);
        }
        elseif ($dateDimension === ConstFirstLogin::DIMENSION_MONTH) {
            $result = $serv->listByMonth($options, $pagination, $groups, $sort);
        }
        else {
            $result = $serv->listByDaily($options, $pagination, $groups, $sort);
        }

        $remainResult =
            (new BasicRemainIndex('ddc_platform.dws_firstlogin_remain_daily', 't_remain'))
                ->remainInfo($options, $groups);

        $remainInfo = IndicatorsHelpers::dimReduction(
            ($remainResult['list'] ?? []), 'day_type', $groups, ['login_num']
        );

        $list             = &$result['list'];
        $configBasic      = new BasicServ();
        $constConfCollect = $configBasic->getMultiOptions([
            'platform_id', 'promotion_id', 'department_id', 'user_id',
            'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
        ])->put('channel_id', (new GeneralOptionServ())->listChannelOptions());

        $replaceFunc = $this->replaceColumnDefine($constConfCollect);
        $resetFunc   = $this->resetUnusedColumn($groups);
        $remainType  = (int)Arr::get($options, 'remain_type', 0);// N留内容展示选项
        $workLine    = [];

        if (
            in_array('tday', $groups)
            && $dateDimension === ConstFirstLogin::DIMENSION_DAY
        ) {
            $getUserByDate = IndexCalculators::getSingleValue('firstlogin_user');
        }
        else {
            $workLine[]    = $this->addInfoInGroupDateByRemain($options, $groups);
            $getUserByDate = IndexCalculators::getValueInCollectByN('firstlogin_user_n');
        }


        $lastRemainFn = function (&$target, $key) use ($remainType) {
            $getFn    = IndexCalculators::remainValueGet($remainType);
            $user     = $target['firstlogin_user'] ?? 0;
            $loginNum = $target['remain_current'] ?? 0;

            $target['remain_current'] = $getFn($loginNum, $user);
        };

        $workLine = array_merge($workLine, [
            // 留存计算
            IndexCalculators::remainCalculators($remainInfo, ['groups' => $groups, 'remain_type' => $remainType], $getUserByDate),
            // 留存天数
            IndexCalculators::remainDayCalculators($dateDimension, $groups, $maxDayDiff),
            // 当前留存率
            // IndexCalculators::lastRemainCalculators(),
            $lastRemainFn,
        ]);

        $optionsServ = new GeneralOptionServ();

        if (in_array('package_id', $groups)) {
            $listPackages  = array_column($list, 'package_id');
            $packageTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'package_id', 'id' => $listPackages]) ?? []);

            if (!empty($packageTagMap)) {
                $packageTagAppendFn = function (&$target) use ($packageTagMap) {
                    if (
                        !empty($target['package_id'])
                        && $target['package_id'] != '-'
                    ) {
                        $packageId              = $target['package_id'];
                        $target['package_tags'] = array_values($packageTagMap[$packageId] ?? []);
                    }
                };

                $workLine[] = $packageTagAppendFn;
            }
        }

        if (
            in_array('channel_id', $groups)
            || in_array('package_id', $groups)
        ) {
            $listChannel = array_column($list, 'channel_id');

            $channelTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'channel_id', 'id' => $listChannel]) ?? []);

            if (!empty($channelTagMap)) {
                $channelTagAppendFn = function (&$target) use ($channelTagMap) {
                    if (!empty($target['channel_id']) && $target['channel_id'] != '-') {
                        $channelId              = $target['channel_id'];
                        $target['channel_tags'] = array_values($channelTagMap[$channelId] ?? []);
                    }
                };

                array_unshift($workLine, $channelTagAppendFn);
            }
        }

        $workLine = array_merge($workLine, [$replaceFunc, $resetFunc]);

        $this->processingLine($list, $groups, $workLine);
        $summaryRow                = &$result['summary'];
        $summaryRow['remain_days'] = $maxDayDiff - 1;
        $addSummaryInfoByDate      = $this->addSummaryInfoGroupDateByRemain($options, $groups);
        $summaryRow                = array_merge($summaryRow, $addSummaryInfoByDate);
        $summaryRemain             = array_column($remainResult['summary_row'], null, 'day_type');
        $summaryGetUser            = IndexCalculators::getValueInCollectByN('firstlogin_user_n');

        IndexCalculators::remainCalc($summaryRow, $summaryRemain, $summaryGetUser, 'login_num', $remainType);

        // 汇总行当前留存率
        $summaryTotalRemain = 0;
        foreach ($remainInfo as $item) {
            $remainDayInfo = $item['day_type'] ?? null;

            if (empty($remainDayInfo)) continue;

            $maxRemainLoginNum  = $remainDayInfo[1000]['login_num'] ?? 0;
            $summaryTotalRemain += $maxRemainLoginNum;
        }
        $summaryRow['remain_current'] = $summaryTotalRemain;
        $lastRemainFn($summaryRow, 0);

        return $result;
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $baseCollect = [
            'tday', 'thour', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id', 'promotion_id',
            'platform_id', 'department_id', 'user_id',
        ];

        $newUserCollect = [
            'firstlogin_user', 'remain_days', 'remain_current',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],
                    ['value' => 'tags', 'label' => '标签'],

                    ['value' => 'remain_group_1', 'label' => '次留-30留'],
                    ['value' => 'remain_group_2', 'label' => '45留-180留'],
                    ['value' => 'remain_group_3', 'label' => '210留-360留'],
                    ['value' => 'remain_group_4', 'label' => '360留-720留'],
                ],
            ],
        ];

        $options = $params->toArray();
        if (empty($options['groups'])) {
            $options['groups'] = ['tday', 'package_id'];
        }

        $fields = $this->tableFields($options);

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'];

            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $newUserCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    private function changeDefaultParams(Collection &$params): array
    {
        // 分页获取
        [$page, $pageSize] = [
            $params->pull('page', 1), $params->pull('page_size', 20),
        ];

        // 排序获取
        if ($params->has('sort')) {
            $sort = $params->pull('sort');
            if ($params->has('order')) {
                $sort .= ' ' . ($params->pull('order') == 'ascend' ? 'asc' : 'desc');
            }
        }
        else {
            $sort = ['tday', 'new_user desc'];
        }

        return [
            'pagination' => ['page' => $page, 'page_size' => $pageSize],
            'sort'       => $sort,
        ];
    }

    /**
     * @return array
     * @throws \Exception
     * @todo 过渡
     */
    public function simpleSearchAction(): array
    {
        $params     = $this->wrapParams($this->request);
        $groups     = ['tday'];
        $options    = $params->toArray();
        $baseServ   = new FirstLoginIndex();
        $remainServ = new BasicRemainIndex('ddc_platform.dws_firstlogin_remain_daily', 't_remain');

        $baseRe     = $baseServ->simpleListByDay($options, [], $groups);
        $remainRe   = $remainServ->simpleListByDay($options);
        $info       = &$baseRe['list'];
        $remainInfo = IndicatorsHelpers::dimReduction($remainRe['list'], 'day_type', ['tday'], ['login_num']);
        $getUserFn  = IndexCalculators::getSingleValue('new_user');

        $processLine = new ProcessLine();
        $processLine->addProcess(function (&$target) use ($remainInfo) {
            $tDay = $target['tday'];

            if (!empty($remainInfo[$tDay])) {
                $target['remain_info'] = $remainInfo[$tDay]['day_type'] ?? [];
            }
        });

        $processLine
            ->addProcess(RemainCalculator::calcEachRow($getUserFn, ['max_days' => 7], 0))
            ->addProcess(IndexCalculators::remainDayCalculators(ConstHub::DIMENSION_DAY, $groups, 7))
            ->addProcess(IndexCalculators::lastRemainCalculators())
            ->addProcess(function (&$target) {
                $tDay                  = $target['tday'];
                $target['remain_days'] = days_apart(new \DateTime(), $tDay) - 1;
            })
            ->addProcess(function (&$target) {
                unset($target['remain_info']);
            });

        $processLine->run($info);
        /**
         * 汇总
         */
        $summaryRow       = &$baseRe['summary'];
        $summaryRowRemain = array_column($remainRe['summary'], null, 'day_type');
        $summaryNInfo     = Calculator::cumulativeOnDays($info, ['new_user'], '<');
        $summaryRow       = array_merge($summaryRow, $summaryNInfo);
        $summaryGetUser   = IndexCalculators::getValueInCollectByN('new_user_n');

        RemainCalculator::calcRemain($summaryRow, $summaryRowRemain, $summaryGetUser, 0);

        $dayMax = [];
        foreach ($remainRe['list'] as $item) {
            $tDay    = $item['tday'];
            $dayType = $item['day_type'];
            $value   = $item['login_num'];

            if (!isset($dayMax[$tDay])) {
                $dayMax[$tDay] = ['day_type' => $dayType, 'login_num' => $value];
            }
            else {
                if ($dayType > ($dayMax[$tDay]['day_type'] ?? 0)) {
                    $dayMax[$tDay] = ['day_type' => $dayType, 'login_num' => $value];
                }
            }
        }
        $remainCurrent = array_sum(array_column($dayMax, 'login_num'));

        if (empty($summaryRow['new_user']) || empty($remainCurrent)) {
            $summaryRow['remain_current'] = '0.00%';
        }
        else {
            $summaryRow['remain_current'] = number_format(
                    math_eval('x/y*100', ['x' => $remainCurrent, 'y' => $summaryRow['new_user']]), 2
                ) . '%';
        }

        $summaryRow['remain_days'] = days_apart(date('Y-m-d'), $params->get('range_date_start')) - 1;
        $fields                    = (new FirstLoginRemain())->getSimpleFields($options)->toArray();

        return $this->success(array_merge($baseRe, ['fields' => $fields]));
    }

    protected function resetUnusedColumn($groups): \Closure
    {
        $resetCols = ColumnManager::groupFilterOperatorColumn($groups);

        return function (&$target) use ($resetCols) {
            $target = array_merge($target, $resetCols);
        };
    }

}
<?php

namespace app\service\OperationData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\General\Helpers\BaseConfPlatformTable;
use Spiral\Database\Database;

class ConvertConfServ
{
    /**
     * @param array $data
     * @return mixed
     * @throws \Throwable
     */
    public function insertOrUpdate(array $data)
    {
        $conn = $this->getConn();

        return $conn->transaction(function (Database $db) use ($data) {
            if (!empty($data['id'])) {
                $id = Arr::pull($data, 'id');

                return $db
                    ->table(BaseConfPlatformTable::TbConvertPlatformConf)
                    ->update($data)
                    ->where('id', $id)
                    ->run();
            }
            else {
                return $db
                    ->table(BaseConfPlatformTable::TbConvertPlatformConf)
                    ->insertOne($data);
            }
        });
    }

    /**
     * @param array $params
     * @param array $paginate
     * @return array
     */
    public function fetchAll(array $params = [], array $paginate = []): array
    {
        $db = $this->getConn();
        $qb = $db
            ->table(BaseConfPlatformTable::TbConvertPlatformConf)
            ->select()
            ->columns([
                'id as id',
                'cp_game_id as cp_game_id',
                'game_id as game_id',
                'android_packages as android_packages',
                'ios_packages as ios_packages',
            ])
            ->orderBy('id', 'DESC');

        if (!empty($paginate)) {
            [
                'page'      => $page,
                'page_size' => $pageSize
            ] = $paginate;

            $qb->limit($pageSize)->offset(($page - 1) * $pageSize);
        }

        return [
            'list' => $qb->fetchAll(),
        ];
    }


    protected function getConn()
    {
        return FakeDB::connection('base_conf_platform');
    }
}
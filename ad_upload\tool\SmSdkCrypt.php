<?php

namespace app\ad_upload\tool;

/**
 * api接口加解密算法1（传简和手盟密钥不同）
 * User: pc23
 * Date: 2018/6/23
 * Time: 12:10
 * phpcs:disable
 */
class SmSdkCrypt
{
    const CHARS        = '0123456789abcdefghijklmnopqrxtuvwxyzABCDEFGHIJKLMNOPQRXTUVWXYZ';
    const AES_KEY_POOL = [
        '0vptT3BcVan9JmVU',
        'DEFZ9aRuLm9bXIg0',
        '6oXgpm4nUmodxz3M',
        'X83PrKbUpFpRH3xy',
        'oMFfCODoWT0JtijQ',
        'vnXURFtxwTDtxoqR',
        'cEjPbdtZxVBR4Ge7',
        'qwLYHzBL7Pmmhuko',
        'Ytdy47Bm44kXv63x',
        'I0p79irnCqUd5iai',
        '9tQMy3rh42JfX3i3',
        '6QnxCQCVHLQh3r0j',
        'mfMaleKEGonpw4XU',
        'qY6jiWB8CVN2PZ5V',
        'z7yNLMqvp8bJ2w7R',
        'w7R7M1Z1tyi8GtxG',
        'IUGXVm52Eotx8EXt',
        'aA0v3kh8Nm7bFhFc',
        '8Qyoyyl4PrZjgZoV',
        'l0ULFEoXNyIWMJrB',
        '9xzh4yB6FluRBme4',
        '0vjcaZGCve9GiNPH',
        'in71h2N4eCrXe6MY',
        'bKat1LHTUIxPnpkb',
        'IW6uy3LoZLAj8qYC',
        'xOw3dcaj1NkHxMd3',
        '1lrVFvd1q7DXoIBu',
        '2AJ8kHATBoVm6D0b',
        'zJdbNFJOJul3u3oO',
        'zL9pJnGqmXXwJNPC',
        'mT2tuJ1RxbJ6ghXq',
        '7zra3YrE067gNtyq',
    ];
    const AES_IV       = 'smSdkAesIv123456';
    const AES_KEY      = 'smSdkTagKey12345';


    public function encrypt($jsonData)
    {
        $len           = strlen(self::CHARS);
        $indexSum      = 0;
        $stringBuilder = '';
        for ($k = 0; $k < $len; $k++) {
            $randomIndex = rand(0, $len - 1);
            $detail_ch   = substr(self::CHARS, $randomIndex, 1);
            $detail      = ord($detail_ch);

            if ($k == 4 || $k == 9 || $k == 16 || $k == 25) {
                $indexSum += $detail;

                $arr[]  = $detail;
                $arr2[] = $detail_ch;
            }
            $stringBuilder .= $detail_ch;
        }

        $index   = $indexSum % 32;
        $aes_key = self::AES_KEY_POOL[$index];
        $aes     = new CryptAES();

        $aes->setKey(self::AES_KEY);
        $aes->setIv(self::AES_IV);
        $encryptTag = $aes->encrypt($stringBuilder);
        $finalTag   = base64_encode($encryptTag);

        $aes->setKey($aes_key);
        $encryptValue = $aes->encrypt(gzencode($jsonData));
        $finalValue   = base64_encode($encryptValue);
        $re           = [
            $finalTag => $finalValue,
        ];
        return json_encode($re);
    }

    public function decrypt($data)
    {
        foreach ($data as $k => $v) {
            $encryptTag   = $k;
            $encryptValue = $v;
        }

        $aes = new CryptAES();
        $aes->setKey(self::AES_KEY);
        $aes->setIv(self::AES_IV);
        $decryptedJsonKey = $aes->decrypt(base64_decode($encryptTag));

        $indexSum = 0;
        $len      = strlen($decryptedJsonKey);
        for ($k = 0; $k < $len; $k++) {
            if ($k == 4 || $k == 9 || $k == 16 || $k == 25) {
                $arr2[]   = substr($decryptedJsonKey, $k, 1);
                $detail   = ord(substr($decryptedJsonKey, $k, 1));
                $indexSum += $detail;
                $arr[]    = $detail;
            }
        }

        $index   = $indexSum % 32;
        $aes_key = self::AES_KEY_POOL[$index];

        $aes->setKey($aes_key);

        $decryptedJsonValue = gzdecode($aes->decrypt(base64_decode($encryptValue)));
        $decryptedJsonValue = json_decode($decryptedJsonValue, true);
        return $decryptedJsonValue;
    }
}

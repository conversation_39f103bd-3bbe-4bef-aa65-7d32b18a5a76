<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use Spiral\Database\Injection\Fragment;

/**
 * LTV
 */
class LtvServ
{
    private const TB_CREATIVE_LTV = 'ddc_platform.dws_creative_ad_ltv_daily';


    /**
     * @return void
     */
    public function simpleLtv(
        array $params = [],
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): array
    {
        $db            = $this->getConn();
        $qb            = $db->select()->from(static::TB_CREATIVE_LTV);
        $dateDimension = $params['range_date_dimension'];

        if (in_array('tday', $columns)) {
            unset($columns[array_search('tday', $columns)]);
            $columns = array_merge($columns, $this->getFixedDimensionTimeField($dateDimension));
        }

        if (
            !empty($params['range_date_start'])
            && !empty($params['range_date_end'])
        ) {
            $rangeStart = $params['range_date_start'];
            $rangeEnd   = $params['range_date_end'];

            $qb->where('tday', 'between', $rangeStart, $rangeEnd);
        }

        if (!empty($params['cp_game_id'])) {
            $qb->where('cp_game_id', $params['cp_game_id']);
        }

        if (!empty($params['max_day_type'])) {
            $qb->where('day_type', '<=' , $params['max_day_type']);
        }


        if (!empty($columns)) {
            $qb->columns($columns);
        }

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }
        $sql = (clone $qb)->__toString();
        return $qb->fetchAll();
    }

    /**
     * @param $dateDimension
     *
     * @return Fragment[]|string[]
     */
    private function getFixedDimensionTimeField($dateDimension): array
    {
        if ($dateDimension == 3) {
            // 按周汇总
            return [
                new Fragment('FROM_DAYS(TO_DAYS(`tday`) - MOD(TO_DAYS(`tday`) - 7, 7)) as my_week'),
                new Fragment('MIN(tday) as start_day'),
                new Fragment('MAX(tday) as end_day'),
            ];
        }
        elseif ($dateDimension == 4) {
            // 按月汇总
            return [new Fragment("DATE_FORMAT(tday, '%Y-%m') as my_month")];
        }
        else {
            return ['tday'];
        }
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
<?php

namespace app\service\Media;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\Convert;
use app\service\Media\Helper\MediaTableConst;
use phpseclib3\Crypt\DSA\Formats\Keys\Raw;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

/**
 *
 */
class MediaPlatformServ
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int   $mode
     *
     * @return array
     */
    public function getInfoList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $qb = $this->getQueryBuilder();
        $this->whereMatch($qb, $params);

        $qb->columns([
            'tday AS tday',
            'media_platform_id AS media_platform_id',
            new Fragment('SUM(active_account) AS active_account'),
            new Fragment('SUM(video_publish_total) AS video_publish_total'),
            new Fragment('SUM(normal_video_play) AS normal_video_play'),
            new Fragment('SUM(normal_video_play_today) AS normal_video_play_today'),
            new Fragment('SUM(normal_new_video_play_today) AS normal_new_video_play_today'),
            new Fragment('SUM(normal_task_account) AS normal_task_account'),
            new Fragment('SUM(normal_task_publish) AS normal_task_publish'),
            new Fragment('SUM(normal_task_account_success) AS normal_task_account_success'),
            new Fragment('SUM(normal_task_publish_success) AS normal_task_publish_success'),
            new Fragment('SUM(submiss_video_play) AS submiss_video_play'),
            new Fragment('SUM(submiss_video_play_today) AS submiss_video_play_today'),
            new Fragment('SUM(submiss_new_video_play_today) AS submiss_new_video_play_today'),
            new Fragment('SUM(submiss_task_account) As submiss_task_account'),
            new Fragment('SUM(submiss_task_account_success) AS submiss_task_account_success'),
            new Fragment('SUM(submiss_task_publish_success) AS submiss_task_publish_success'),
            new Fragment('SUM(submiss_task_publish) AS submiss_task_publish'),
        ]);

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        $noPageQb = clone $qb;

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $pageSize] = $paginate;
            $qb->limit($pageSize)->offset(($page - 1) * $pageSize);
        }

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        $total = $this
            ->getConn()
            ->select()
            ->from(new Fragment('(' . (clone $noPageQb)->__toString() . ') as total_body'))
            ->count();

        $summaryQb = $this->getConn()->select()->from(new Fragment('(' . (clone $noPageQb)->__toString() . ') as summary_body'));
        $summaryQb->columns([
            new Fragment('SUM(active_account) AS active_account'),
            new Fragment('SUM(video_publish_total) AS video_publish_total'),
            new Fragment('SUM(normal_video_play) AS normal_video_play'),
            new Fragment('SUM(normal_video_play_today) AS normal_video_play_today'),
            new Fragment('SUM(normal_new_video_play_today) AS normal_new_video_play_today'),
            new Fragment('SUM(normal_task_account) AS normal_task_account'),
            new Fragment('SUM(normal_task_publish) AS normal_task_publish'),
            new Fragment('SUM(normal_task_account_success) AS normal_task_account_success'),
            new Fragment('SUM(normal_task_publish_success) AS normal_task_publish_success'),
            new Fragment('SUM(submiss_video_play) AS submiss_video_play'),
            new Fragment('SUM(submiss_video_play_today) AS submiss_video_play_today'),
            new Fragment('SUM(submiss_new_video_play_today) AS submiss_new_video_play_today'),
            new Fragment('SUM(submiss_task_account) As submiss_task_account'),
            new Fragment('SUM(submiss_task_account_success) AS submiss_task_account_success'),
            new Fragment('SUM(submiss_task_publish_success) AS submiss_task_publish_success'),
            new Fragment('SUM(submiss_task_publish) AS submiss_task_publish'),
        ]);

        return [
            'list'    => $qb->fetchAll(),
            'total'   => $total,
            'summary' => $summaryQb->fetchAll()[0] ?? [],
        ];
    }

    /**
     * @param SelectQuery $qb
     * @param array       $params
     *
     * @return void
     */
    protected function whereMatch(SelectQuery &$qb, array $params = [])
    {
        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            $rangeDate = [
                $params['range_date_start'],
                $params['range_date_end'],
            ];

            sort($rangeDate);
            $rangeDate = array_unique($rangeDate);

            if (count($rangeDate) === 1) {
                $qb->where('tday', $rangeDate[0]);
            }
            else {
                $qb->where('tday', 'between', $rangeDate[0], $rangeDate[1]);
            }
        }

        if (!empty($params['media_platform_id'])) {
            $d = $params['media_platform_id'];
            $qb->where('media_platform_id', new Parameter($d));
        }
    }

    /**
     * @param int $qbMode
     *
     * @return SelectQuery
     */
    private function getQueryBuilder(int $qbMode = -1): SelectQuery
    {
        $db = $this->getConn();

        return $db->select()->from(MediaTableConst::DWS_MEDIA_PLATFORM_DAILY . ' as dws_plat');
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
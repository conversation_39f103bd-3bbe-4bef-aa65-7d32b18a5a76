<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Helpers\ConstHub;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\advertise\AdCreativePayRemainLogic;
use app\logic\advertise\AdPaidRetainLogic;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;

class AdCampaignPayRemainController extends BaseTableController
{
    /**
     * @param Collection $params
     * @return array
     * @throws \RedisException
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $groups   = [];
        $page     = $params->pull('page', 1);
        $pageSize = $params->pull('page_size', 100);
        $paginate = [
            'page_size' => $pageSize,
            'offset'    => ($page - 1) * $pageSize,
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = ['paid_retain_user' => 'DESC'];
        }

        if ($params->has('groups')) {
            $groups = Arr::wrap($params->pull('groups'));
        }
        else {
            $groups = ['tday', 'promotion_channel_id', 'package_id', 'campaign_id'];
        }

        $options = $params->toArray();

        // 外部账号仅能显示自己账号归属的数据
        if (\Plus::$service->admin->isOutsiders()) {
            $options['user_id'] = \Plus::$service->admin->getUserId();
        }

        $fullGroups = ColumnManager::groupAdFill($groups);
        $options['last_login_date'] = date('Y-m-d');
        return (new AdPaidRetainLogic())->listNor($options, $fullGroups, $paginate, $sort);
    }

    protected function fields(Collection $params): array
    {
        $fieldRelateMap = [
            'creative_id'          => ['creative_name', 'package_id'],
            'plan_id'              => ['plan_name', 'package_id'],
            'campaign_id'          => ['campaign_name', 'package_id'],
            'package_id'           => ['cp_game_id', 'channel_main_id', 'user_id'],
            'channel_main_id'      => [],
            'game_id'              => ['cp_game_id'],
            'user_id'              => [],
            'platform_id'          => [],
            'account_id'           => ['ad_account', 'account_name'],
            'ad_account'           => [],
            'account_name'         => [],
            'app_show_id'          => [],
            'promotion_channel_id' => ['channel_main_id'],
            'promotion_id'         => [],
            'cp_game_id'           => [],
            'tday'                 => [],
        ];


        $fields = $this->tableFields($params->toArray());
        $fields = array_column($fields, null, 'dataIndex');

        if ($params->has('groups')) {
            $groups     = $params->get('groups');
            $relateMaps = ColumnManager::groupAdRelation($groups);
            $infoCols   = ConstHub::AD_FIXED_INFO_COLS;

            $fields = array_filter($fields, fn($item, $k) => !in_array($k, $infoCols) || in_array($k, $relateMaps), ARRAY_FILTER_USE_BOTH);
        }

        $fields = array_values($fields);

        $baseCollect   = [
            'tday', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id',
            'promotion_channel_id', 'promotion_id', 'platform_id',
            'department_id', 'user_id',
        ];
        $adBaseCollect = [
            'ad_account', 'account_id', 'campaign_name', 'campaign_id',
            'plan_name', 'plan_id', 'creative_name', 'creative_id', 'account_name',
        ];

        $newBaseCollect = [
            'pay_new_user_7days', 'remain_days', 'remain_1000',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'ad_base', 'label' => '广告信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],

                    ['value' => 'remain_group_1', 'label' => '次留-30留'],
                    ['value' => 'remain_group_2', 'label' => '45留-180留'],
                    ['value' => 'remain_group_3', 'label' => '210留-360留'],
                    ['value' => 'remain_group_4', 'label' => '360留-720留'],
                ],
            ],
        ];

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'] ?? '';
            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $adBaseCollect)) {
                $field['classify'] = ['attrs', 'ad_base'];
            }
            elseif (in_array($dIndex, $newBaseCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }


    protected function registerParams(): Collection
    {
        $today = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start', 'default' => $today], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
            ['field' => 'cp_game_id'], // 游戏原名
            ['field' => 'game_id'], // 游戏统计名
            ['field' => 'app_show_id'], // 游戏前端名
            ['field' => 'channel_main_id'], // 主渠道
            ['field' => 'channel_id'], //渠道
            ['field' => 'promotion_channel_id'], // 推广渠道
            ['field' => 'platform_id'], // 客户端ID
            ['field' => 'package_id'], // 包号
            ['field' => 'promotion_id'], // 推广分类
            ['field' => 'department_id'], // 部门
            ['field' => 'user_id'], // 投放人
            ['field' => 'ad_account'], // 投放账号
            ['field' => 'advertiser_id'], // 广告账号ID
            ['field' => 'campaign_id'], // 广告组ID
            ['field' => 'campaign_name'], // 广告组名称
            ['field' => 'plan_name'], // 计划名
            ['field' => 'plan_id'], // 计划ID
            ['field' => 'creative_name'], // 创意名
            ['field' => 'creative_id'], // 创意ID
            ['field' => 'data_range'], // 数据范围
            ['field' => 'tab'],
            ['field' => 'is_has_natural', 'default' => 0], // 是否含自然量
            ['field' => 'is_has_appointment', 'default' => 0], // 是否包含预约数据
            ['field' => 'account_id'], // 账号ID搜索
            ['field' => 'data_scope'], // 数据范围搜索
            ['field' => 'channel_id_tags'], // 渠道标签
            ['field' => 'channel_main_id_tags'], // 主渠道标签
            ['field' => 'marketing_goal'], // 营销场景
            ['field' => 'package_id_tags'], // 包号标签
            ['field' => 'remain_type'],
            ['field' => 'game_id_tags'],
            ['field' => 'user_os'],
            ['field' => 'mode_test'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);

    }
}
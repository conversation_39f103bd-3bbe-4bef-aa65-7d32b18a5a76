<?php

namespace app\apps\logs\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\log\UserLogQueryLogic;

/**
 * SDK用户日志查询
 */
class UserLogQueryController extends BaseTableController
{
    /**
     * @param Collection $params
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        return (new UserLogQueryLogic())->overview($params->toArray());
    }

    /**
     * @param Collection $params
     * @return array[]
     */
    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '游戏原名', 'dataIndex' => 'cp_game_id'],
            ['title' => '游戏统计名', 'dataIndex' => 'game_id'],
            ['title' => '包号', 'dataIndex' => 'package_id'],
            ['title' => '事件类型', 'dataIndex' => 'event_type'],
            ['title' => '记录次数', 'dataIndex' => 'log_times'],
            ['title' => '最近发生时间', 'dataIndex' => 'last_event_time'],
        ];

        return ['fields' => $fields];
    }
}
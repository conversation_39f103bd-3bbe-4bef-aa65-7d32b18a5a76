<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use phpseclib3\Math\BigInteger\Engines\PHP;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

class PlanBaseServ
{
    protected const TB_PLAN_BASE_DAILY    = 'ddc_platform.dws_plan_ad_base_daily';
    protected const TB_PLAN_PAYMENT_DAILY = 'ddc_platform.dws_plan_ad_payment_daily';

    const MODE_ALL           = 99;
    const MODE_BASE          = 1;
    const MODE_PAYMENT       = 2;
    const MODE_PLAN_BASE     = 3;
    const MODE_CAMPAIGN_BASE = 4;
    const MODE_SV_LINK_CONF  = 5;
    const MODE_AD_ACCOUNT    = 6;
    const MODE_ADMIN_USER    = 7;
    const MODE_ADP_OAUTH     = 8;


    /**
     * 简单的获取部分基础指标数据
     *
     *
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function simpleBaseInfo(
        array $params = [],
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): array
    {
        $db = $this->getConn();
        $qb = $db
            ->select()
            ->from(static::TB_PLAN_BASE_DAILY . ' as t1')
            ->leftJoin('base_conf_platform.tb_package_detail_conf', 'power')
            ->on('t1.package_id', 'power.package_id');;

        $dateDimension = $params['range_date_dimension'] ?? 2;

        if (
            !empty($params['range_date_start'])
            && !empty($params['range_date_end'])
        ) {
            $rangeStart = $params['range_date_start'];
            $rangeEnd   = $params['range_date_end'];

            $qb->where('tday', 'between', $rangeStart, $rangeEnd);
        }

        if (in_array('tday', $columns)) {
            unset($columns[array_search('tday', $columns)]);
            $columns = array_merge($columns, $this->getFixedDimensionTimeField($dateDimension));
        }

        if (!empty($params['cp_game_id'])) {
            $qb->where('t1.cp_game_id', $params['cp_game_id']);
        }

        if (!empty($params['cp_game_id[!]'])) {
            $qb->where('t1.cp_game_id', 'not in', new Parameter($params['cp_game_id[!]']));
        }

        if (!empty($columns)) {
            $qb->columns($columns);
        }

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        return $qb->fetchAll();
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function simplePaymentInfo(
        array $params = [],
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): array
    {
        $db = $this->getConn();
        $qb = $db
            ->select()
            ->from(static::TB_PLAN_PAYMENT_DAILY . ' as t1')
            ->leftJoin('base_conf_platform.tb_package_detail_conf', 'power')
            ->on('t1.package_id', 'power.package_id');

        $dateDimension = $params['range_date_dimension'] ?? 2;

        if (
            !empty($params['range_date_start'])
            && !empty($params['range_date_end'])
        ) {
            $rangeStart = $params['range_date_start'];
            $rangeEnd   = $params['range_date_end'];

            $qb->where('tday', 'between', $rangeStart, $rangeEnd);
        }

        if (in_array('tday', $columns)) {
            unset($columns[array_search('tday', $columns)]);
            $columns = array_merge($columns, $this->getFixedDimensionTimeField($dateDimension));
        }

        if (isset($params['cp_game_id'])) {
            $qb->where('t1.cp_game_id', $params['cp_game_id']);
        }

        if (isset($params['cp_game_id[!]'])) {
            $qb->where('t1.cp_game_id', 'not in', new Parameter($params['cp_game_id[!]']));
        }

        if (!empty($columns)) {
            $qb->columns($columns);
        }

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        return $qb->fetchAll();
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function simpleBothInfo(
        array $params = [],
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): array
    {
        $db = $this->getConn();
        $qb = $db->select()->from(static::TB_PLAN_BASE_DAILY . ' as t1')
            ->leftJoin('base_conf_platform.tb_package_detail_conf', 'power')
            ->on('t1.package_id', 'power.package_id')
            ->leftJoin(' base_conf_platform.tb_base_cp_game_conf', 'cp_conf')
            ->on('cp_conf.cp_game_id', 'power.cp_game_id')
            ->leftJOin(static::TB_PLAN_PAYMENT_DAILY, 't2')
            ->on([
                't1.tday'       => 't2.tday',
                't1.package_id' => 't2.package_id',
                't1.channel_id' => 't2.channel_id',
                't1.plan_id'    => 't2.plan_id',
            ]);

        $dateDimension = $params['range_date_dimension'] ?? 2;

        if (
            !empty($params['range_date_start'])
            && !empty($params['range_date_end'])
        ) {
            $rangeStart = $params['range_date_start'];
            $rangeEnd   = $params['range_date_end'];

            $qb->where('t1.tday', 'between', $rangeStart, $rangeEnd);
        }

        if (in_array('tday', $columns)) {
            unset($columns[array_search('tday', $columns)]);
            $columns = array_merge($columns, $this->getFixedDimensionTimeField($dateDimension));
        }

        if (!empty($columns)) {
            $qb->columns($columns);
        }

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        return $qb->fetchAll();
    }


    /**
     *
     * @return Fragment[]
     */
    private function getFixedDimensionTimeField($dateDimension): array
    {
        if ($dateDimension == 3) {
            // 按周汇总
            return [
                new Fragment('FROM_DAYS(TO_DAYS(`tday`) - MOD(TO_DAYS(`tday`) - 7, 7)) as my_week'),
                new Fragment('MIN(tday) as start_day'),
                new Fragment('MAX(tday) as end_day'),
            ];
        }
        elseif ($dateDimension == 4) {
            // 按月汇总
            return [new Fragment("DATE_FORMAT(tday, '%Y-%m') as my_month")];
        }
        else {
            return ['tday'];
        }
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }


    /**
     * 获取计划基础指标
     *
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int   $mode
     *
     * @return void
     */
    public function getInfo(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    )
    {
        // todo: code here
    }

    /**
     * @param int $mode
     *
     * @return void
     */
    private function getQueryBuilder(int $mode = -1)
    {
        $db           = $this->getConn();
        $qb           = $db->select();
        $paramMode    = $mode & $this->queryAllMode();
        $hasBaseTable = false;
        $mainTable    = null;
        $powerSql     = str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql());

        if ($paramMode & self::MODE_BASE) {
            $mainTable    = 't_base';
            $hasBaseTable = true;
            $qb->from(self::TB_PLAN_BASE_DAILY . ' as ' . $mainTable);
        }

        if ($paramMode & self::MODE_PAYMENT) {
            if ($hasBaseTable) {
                $qb
                    ->leftJoin(self::TB_PLAN_PAYMENT_DAILY, 't_payment')
                    ->on([
                        $mainTable . '.tday'       => 't_payment.tday',
                        $mainTable . '.package_id' => 't_payment.package_id',
                        $mainTable . '.channel_id' => 't_payment.channel_id',
                        $mainTable . '.plan_id'    => 't_payment.plan_id',
                    ]);
            }
            else {
                $qb->from(self::TB_PLAN_PAYMENT_DAILY . ' as t_payment');
                $mainTable = 't_payment';
            }
        }

        $qb
            ->innerJoin(new Fragment($powerSql), 'power')
            ->on($mainTable . '.package_id', 'power.package_id');

        if ($paramMode & self::MODE_PLAN_BASE) {
            $qb
                ->leftJoin(TableCollect::ADP_PLAN_BASE, 'base_plan')
                ->on([
                    'base_plan.channel_id' => $mainTable . '.main_channel_id',
                    'base_plan.plan_id'    => $mainTable . '.plan_id',
                ]);
        }

        if ($paramMode & self::MODE_CAMPAIGN_BASE) {
            $qb
                ->leftJoin(TableCollect::ADP_CAMPAIGN_BASE, 'base_campaign')
                ->on([
                    'base_campaign.channel_id'  => $mainTable . '.main_channel_id',
                    'base_campaign.campaign_id' => $mainTable . '.campaign_id',
                ]);
        }

        if ($paramMode & self::MODE_SV_LINK_CONF) {
            $qb
                ->leftJoin(TableCollect::SPY_SV_LINK, 'svlink')
                ->on([
                    'svlink.id' => new Fragment($mainTable . '.plan_id + 0'),
                ]);
        }

    }

    /**
     * @return int
     */
    private function queryAllMode(): int
    {
        return self::MODE_ALL
            | self::MODE_BASE
            | self::MODE_PAYMENT
            | self::MODE_PLAN_BASE
            | self::MODE_CAMPAIGN_BASE
            | self::MODE_SV_LINK_CONF
            | self::MODE_AD_ACCOUNT
            | self::MODE_ADMIN_USER;
    }


}
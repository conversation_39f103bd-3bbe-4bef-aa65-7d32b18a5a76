<?php

namespace app\apps\api\controllers;

use app\extension\Exception\ParameterException;
use app\models\OriginPlatform\TbSdkActiveLog;
use app\models\OriginPlatform\TbSdkUserNewloginPackage;
use app\service\OriginData\ActiveData;
use Plus\MVC\Controller\JsonController;

/**
 *
 */
class OriginDataController extends JsonController
{
    const TIPS_NULL_ACCOUNT_PARAM = '缺少必填参数';

    /**
     * @return ParameterException|array
     */
    public function listActivePackageAction()
    {
        $account   = $this->getValue('core_account');
        $packageId = $this->getValue('package_id');

        if (empty($account)) {
            return new ParameterException(static::TIPS_NULL_ACCOUNT_PARAM);
        }
        if (!is_array($account)) {
            $account = explode(',', $account);
        }

        $serv   = new ActiveData();
        $result = [];

        ['cp_game_id' => $cpGameId] = $serv->fetchCpGameByPackage($packageId);

        foreach ($account as $item) {
            $r        = $serv->fetchBeginActivePackage($item, $cpGameId);
            $tPackage = $r['PACKAGE_ID'] ?? "";
            //查询首登包
            $fristloginData = $serv->fetchFristloginPackage($item, $cpGameId);

            //没激活取首登包
            if (!$tPackage) {
                $tPackage = $fristloginData["PACKAGE_ID"];
            } else {
                //有激活有首登 比较时间取值
                if ($fristloginData["TIME"] < $r["TIME"]) {
                    $tPackage = $fristloginData["PACKAGE_ID"];
                } else {
                    $tPackage = $r['PACKAGE_ID'];
                }
            }
            $result[$item]['package_id'] = [$tPackage];
        }

        return $this->success($result);
    }
}
<?php

namespace app\apps\adconf\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\adconf\SvlinkManagerLogic;

/**
 *
 */
class SvlinkManagerController extends BaseTableController
{

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $sort = ['id' => 'desc'];

        $paginate = [
            'page'      => $params->pull('page', 1),
            'page_size' => $params->pull('page_size', 100),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }

        $options = $params->toArray();
        $logic   = new SvlinkManagerLogic();

        return $logic->getInfo($options, [], $paginate, $sort);
    }

    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                ],
            ],
        ];

        $fields = [
            ['title' => 'ID', 'dataIndex' => 'id', 'classify' => ['attrs', 'base']],
            ['title' => '推广子渠道', 'dataIndex' => 'channel_id', 'classify' => ['attrs', 'base']],
            ['title' => '游戏原名', 'dataIndex' => 'cp_game_id', 'classify' => ['attrs', 'base']],
            ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'classify' => ['attrs', 'base']],
            ['title' => '包号', 'dataIndex' => 'package_id', 'classify' => ['attrs', 'base']],
            ['title' => '分包', 'dataIndex' => 'ext', 'classify' => ['attrs', 'base']],
            ['title' => '短链名称', 'dataIndex' => 'aid', 'classify' => ['attrs', 'base']],
            ['title' => '监控链接', 'dataIndex' => 'sv_link', 'classify' => ['attrs', 'base']],
            ['title' => '创建人', 'dataIndex' => 'user_id', 'classify' => ['attrs', 'base']],
            ['title' => '创建时间', 'dataIndex' => 'add_time', 'classify' => ['attrs', 'base']],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * 参数过滤
     *
     * @param $key
     *
     * @return Collection
     */
    protected function registerParams($key = null): Collection
    {
        return collect([
            ['field' => 'channel_id'],
            ['field' => 'game_id'],
            ['field' => 'package_id'],
            ['field' => 'aid'],
            ['field' => 'user_id'],
            ['field' => 'ad_version'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order',],
            ['field' => 'sort'],
            ['field' => 'groups'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

    /**
     * @return array
     */
    public function saveAction(): array
    {
        $request     = \Plus::$app->request;
        $requestBody = \json_decode($request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON解析错误');
        }

        if (empty($requestBody['num'])) $requestBody['num'] = 1;

        try {
            $result = (new SvlinkManagerLogic())->multiInsert([$requestBody]);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success($result, 'success');
    }

    /**
     *
     * @return array
     */
    public function delAction(): array
    {
        $request = \Plus::$app->request;
        $body    = \json_decode($request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON解析错误');
        }

        if (empty($body['id'])) {
            return $this->error('缺少必要参数');
        }

        $id = $body['id'];

        try {
            (new SvlinkManagerLogic())->deleteById($id);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }
}
<?php
/* Smarty version 5.0.0-rc3, created on 2025-06-23 15:41:11
  from 'file:sql/advertise/ad_dashboard/creative_dash_alpha.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.0.0-rc3',
  'unifunc' => 'content_68590517b1aaf5_16163516',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '2777a8a4fe5a0fdec812a1998796c20fa9436701' => 
    array (
      0 => 'sql/advertise/ad_dashboard/creative_dash_alpha.tpl',
      1 => 1750664466,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
))) {
function content_68590517b1aaf5_16163516 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = '/mnt/e/project/spy2.0-api/public/templates/sql/advertise/ad_dashboard';
$_smarty_tpl->getSmarty()->getRuntime('TplFunction')->registerTplFunctions($_smarty_tpl, array (
  'dynamicAssign' => 
  array (
    'compiled_filepath' => '/mnt/e/project/spy2.0-api/public/templates_c/2777a8a4fe5a0fdec812a1998796c20fa9436701_0.file_creative_dash_alpha.tpl.php',
    'uid' => '2777a8a4fe5a0fdec812a1998796c20fa9436701',
    'call_name' => 'smarty_template_function_dynamicAssign_13936038576859051764e037_50355828',
  ),
));
$_smarty_tpl->assign('table', 'bigdata_dws.dws_ad_creative_daily_full', false, NULL);?>

<?php $_smarty_tpl->assign('col_guy', array(array('name'=>'show_cnt','derived'=>array('click_show_percent','qian_cost','show_convert_percent')),array('name'=>'click_cnt','derived'=>array('cpc','click_show_percent','convert_percent','activate_percent','download_start_percent')),array('name'=>'download_cnt','derived'=>array('download_finish_percent','install_finish_percent')),array('name'=>'activate_cnt','derived'=>array('activate_percent','activate_install_percent','register_percent','activate_cost')),array('name'=>'convert_cnt','derived'=>array('convert_cost','convert_percent','show_convert_percent')),array('name'=>'install_cnt','derived'=>array('install_finish_percent','activate_install_percent')),array('name'=>'lp_view','derived'=>array('lp_click_percent','install_finish_num')),array('name'=>'lp_download','derived'=>array('lp_click_percent','install_finish_num')),array('name'=>'download_start','derived'=>array('download_start_cost_percent','download_finish_percent','download_start_percent')),array('name'=>'register','derived'=>array('register_cost','register_percent')),array('name'=>'cost','derived'=>array('qian_cost','convert_cost','tt_game_in_app_roi_1days','tt_game_in_app_roi_4days','tt_game_in_app_roi_8days','tt_active_pay_intra_one_day_roi','gdt_mini_game_first_day_paying_roi','gdt_mini_game_pay_d3_roi','gdt_mini_game_pay_d7_roi','gdt_minigame_24h_pay_roi','gdt_roi_activated_d1','gdt_roi_activated_d3','gdt_roi_activated_d7')),array('name'=>'cost_discount','derived'=>array('new_user_cost','cpc','download_start_cost_percent','activate_cost','register_cost','create_role_cost','pay_frequency_7days_cost','new_user_payment_cost','roi_1','roi_2','roi_3','roi_4','roi_5','roi_6','roi_7','roi_8','roi_9','roi_10','roi_11','roi_12','roi_13','roi_14','roi_15','roi_16','roi_17','roi_18','roi_19','roi_20','roi_21','roi_22','roi_23','roi_24','roi_25','roi_26','roi_27','roi_28','roi_29','roi_30','roi_31','roi_32','roi_33','roi_34','roi_35','roi_36','roi_37','roi_38','roi_39','roi_40','roi_41','roi_42','roi_43','roi_44','roi_45','roi_46','roi_47','roi_48','roi_49','roi_50','roi_51','roi_52','roi_53','roi_54','roi_55','roi_56','roi_57','roi_58','roi_59','roi_60','back_paid_new_roi_1','back_paid_new_roi_2','back_paid_new_roi_3','back_paid_new_roi_4','back_paid_new_roi_5','back_paid_new_roi_6','back_paid_new_roi_7','back_paid_roi_within_24_hours','back_paid_cnt_cost_node_1','back_paid_cnt_cost_node_2','back_paid_cnt_cost_node_3','back_paid_cnt_cost_node_4','back_paid_cnt_cost_node_5','back_paid_cnt_cost_node_6','back_paid_cnt_cost_node_7','back_paid_cnt_cost_node_user')),array('name'=>'new_user','derived'=>array('new_user_cost','new_user_real_percent','pay_user_new_percent','arpu_new_user','pay_penetration','new_user_payment_cost','arppu_new_user','back_paid_percent','ltv_1','ltv_2','ltv_3','ltv_4','ltv_5','ltv_6','ltv_7','ltv_8','ltv_9','ltv_10','ltv_11','ltv_12','ltv_13','ltv_14','ltv_15','ltv_16','ltv_17','ltv_18','ltv_19','ltv_20','ltv_21','ltv_22','ltv_23','ltv_24','ltv_25','ltv_26','ltv_27','ltv_28','ltv_29','ltv_30','ltv_31','ltv_32','ltv_33','ltv_34','ltv_35','ltv_36','ltv_37','ltv_38','ltv_39','ltv_40','ltv_41','ltv_42','ltv_43','ltv_44','ltv_45','ltv_46','ltv_47','ltv_48','ltv_49','ltv_50','ltv_51','ltv_52','ltv_53','ltv_54','ltv_55','ltv_56','ltv_57','ltv_58','ltv_59','ltv_60','retain_1','retain_2','retain_3','retain_4','retain_5','retain_6','retain_7','retain_8','retain_9','retain_10','retain_11','retain_12','retain_13','retain_14','retain_15','retain_16','retain_17','retain_18','retain_19','retain_20','retain_21','retain_22','retain_23','retain_24','retain_25','retain_26','retain_27','retain_28','retain_29','ltv_amount_1','ltv_amount_2','ltv_amount_3','ltv_amount_4','ltv_amount_5','ltv_amount_6','ltv_amount_7')),array('name'=>'new_real_user','derived'=>array('new_user_real_percent')),'new_user_emulator','activate_device',array('name'=>'create_role_new','derived'=>array('create_role_percent','create_role_cost')),array('name'=>'pay_new_user_7days','derived'=>array('pay_frequency_avg_7days','paid_retain_7_1','paid_retain_7_2','paid_retain_7_3','paid_retain_7_4','paid_retain_7_5','paid_retain_7_6','paid_retain_7_7','paid_retain_7_8','paid_retain_7_9','paid_retain_7_10','paid_retain_7_11','paid_retain_7_12','paid_retain_7_13','paid_retain_7_14','paid_retain_7_15','paid_retain_7_16','paid_retain_7_17','paid_retain_7_18','paid_retain_7_19','paid_retain_7_20','paid_retain_7_21','paid_retain_7_22','paid_retain_7_23','paid_retain_7_24','paid_retain_7_25','paid_retain_7_26','paid_retain_7_27','paid_retain_7_28','paid_retain_7_29')),array('name'=>'pay_frequency_7days','derived'=>array('pay_frequency_avg_7days','pay_frequency_7days_cost')),array('name'=>'online_time','derived'=>array('first_online_time_avg','online_time_avg')),array('name'=>'first_online_time','derived'=>array('first_online_time_avg')),'active_user',array('name'=>'total_play','derived'=>array('play_duration_3s_percent','play_time_avg')),array('name'=>'play_time_per_play','derived'=>array('play_time_avg')),array('name'=>'play_duration_3s','derived'=>array('play_duration_3s_percent')),'pay_user','pay_money','pay_count',array('name'=>'pay_user_new','derived'=>array('pay_user_new_percent','new_user_payment_cost','arppu_new_user','paid_retain_1','paid_retain_2','paid_retain_3','paid_retain_4','paid_retain_5','paid_retain_6','paid_retain_7','paid_retain_8','paid_retain_9','paid_retain_10','paid_retain_11','paid_retain_12','paid_retain_13','paid_retain_14','paid_retain_15','paid_retain_16','paid_retain_17','paid_retain_18','paid_retain_19','paid_retain_20','paid_retain_21','paid_retain_22','paid_retain_23','paid_retain_24','paid_retain_25','paid_retain_26','paid_retain_27','paid_retain_28','paid_retain_29')),array('name'=>'pay_money_new','derived'=>array('arpu_new_user','pay_penetration','arppu_new_user')),'pay_count_new','pay_money_no_visual','pay_money_new_no_visual',"paid_permeation","active_user_week",array('name'=>'ltv_<n>','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(ltv_node, '\$.<n>')) AS DECIMAL), 0)","SUM(ltv_<n>)"),"format"=>'dynamicAssign','nodes'=>60,'derived'=>array('roi_1','roi_2','roi_3','roi_4','roi_5','roi_6','roi_7','roi_8','roi_9','roi_10','roi_11','roi_12','roi_13','roi_14','roi_15','roi_16','roi_17','roi_18','roi_19','roi_20','roi_21','roi_22','roi_23','roi_24','roi_25','roi_26','roi_27','roi_28','roi_29','roi_30','roi_31','roi_32','roi_33','roi_34','roi_35','roi_36','roi_37','roi_38','roi_39','roi_40','roi_41','roi_42','roi_43','roi_44','roi_45','roi_46','roi_47','roi_48','roi_49','roi_50','roi_51','roi_52','roi_53','roi_54','roi_55','roi_56','roi_57','roi_58','roi_59','roi_60','ltv_amount_1','ltv_amount_3','ltv_amount_7')),array('name'=>'retain_<n>','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(retain_node, '\$.<n>')) AS INT), 0)","SUM(retain_<n>)"),"format"=>'dynamicAssign','nodes'=>31),array('name'=>'paid_retain_<n>','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(paid_retain_node, '\$.<n>')) AS INT), 0)","SUM(paid_retain_<n>)"),"format"=>'dynamicAssign','nodes'=>31),array('name'=>'paid_retain_7_<n>','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(paid_retain_node_last_7, '\$.<n>')) AS INT), 0)","SUM(paid_retain_7_<n>)"),"format"=>'dynamicAssign','nodes'=>31),array('name'=>'role_pass_<n>_d','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(role_pass_node, '\$.role_pass_<n>_d')) AS INT), 0)","SUM(role_pass_<n>_d)"),"format"=>'dynamicAssign','nodes'=>4,'derived'=>array('pass_level_1','pass_level_2','pass_level_3','pass_level_4')),array('name'=>'role_pass_<n>_n','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(role_pass_node, '\$.role_pass_<n>_n')) AS INT), 0)","SUM(role_pass_<n>_n)"),"format"=>'dynamicAssign','nodes'=>4,'derived'=>array('pass_level_1','pass_level_2','pass_level_3','pass_level_4')),array('name'=>'paid_cnt_with_new_node_<n>','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(paid_cnt_with_new_node, '\$.<n>')) AS INT), 0)","SUM(paid_cnt_with_new_node_<n>)"),"format"=>'dynamicAssign','nodes'=>7,'derived'=>array('paid_cnt_avg_with_user_1','paid_cnt_avg_with_user_2','paid_cnt_avg_with_user_3','paid_cnt_avg_with_user_4','paid_cnt_cost_1','paid_cnt_cost_2','paid_cnt_cost_3')),array('name'=>'paid_user_with_new_node_<n>','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(paid_user_with_new_node, '\$.<n>')) AS INT), 0)","SUM(paid_user_with_new_node_<n>)"),"format"=>'dynamicAssign','nodes'=>7,'derived'=>array('paid_cnt_avg_with_user_1','paid_cnt_avg_with_user_2','paid_cnt_avg_with_user_3','paid_cnt_avg_with_user_4')),array('name'=>'back_paid_user_new_node_<n>','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(back_paid_user_new_node, '\$.<n>')) AS INT), 0)","SUM(back_paid_user_new_node_<n>)"),"format"=>'dynamicAssign','nodes'=>7,'derived'=>array('back_paid_cnt_avg_node_1','back_paid_cnt_avg_node_2','back_paid_cnt_avg_node_3','back_paid_cnt_avg_node_4','back_paid_cnt_avg_node_5','back_paid_cnt_avg_node_6','back_paid_cnt_avg_node_7')),array('name'=>'back_paid_cnt_new_node_<n>','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(back_paid_cnt_new_node, '\$.<n>')) AS INT), 0)","SUM(back_paid_cnt_new_node_<n>)"),"format"=>'dynamicAssign','nodes'=>7,'derived'=>array('back_paid_cnt_cost_node_1','back_paid_cnt_cost_node_2','back_paid_cnt_cost_node_3','back_paid_cnt_cost_node_4','back_paid_cnt_cost_node_5','back_paid_cnt_cost_node_6','back_paid_cnt_cost_node_7','back_paid_cnt_avg_node_1','back_paid_cnt_avg_node_2','back_paid_cnt_avg_node_3','back_paid_cnt_avg_node_4','back_paid_cnt_avg_node_5','back_paid_cnt_avg_node_6','back_paid_cnt_avg_node_7')),array('name'=>'back_paid_amount_new_node_<n>','displayed_formula'=>array("coalesce(CAST(json_unquote(json_extract(back_paid_amount_new_node, '\$.<n>')) AS DECIMAL), 0)","SUM(back_paid_amount_new_node_<n>)"),"format"=>'dynamicAssign','nodes'=>7,'derived'=>array('back_paid_new_roi_1','back_paid_new_roi_2','back_paid_new_roi_3','back_paid_new_roi_4','back_paid_new_roi_5','back_paid_new_roi_6','back_paid_new_roi_7')),"back_paid_user_new_within_24_hours",array('name'=>'back_paid_amount_new_within_24_hours','derived'=>array('back_paid_roi_within_24_hours')),"back_paid_cnt_new_within_24_hours",'tt_active_pay_intra_day_count',array('name'=>'tt_game_in_app_ltv_1day','derived'=>array('tt_game_in_app_roi_1day')),array('name'=>'tt_game_in_app_ltv_4days','derived'=>array('tt_game_in_app_roi_4days')),array('name'=>'tt_game_in_app_ltv_8days','derived'=>array('tt_game_in_app_roi_8days')),'tt_game_pay_7d_count','tt_active_pay_intra_one_day_count',array('name'=>'tt_active_pay_intra_one_day_amount','derived'=>array('tt_active_pay_intra_one_day_roi')),'gdt_mini_game_register_users','gdt_mini_game_paying_users_d1','gdt_minigame_1d_pay_count',array('name'=>'gdt_mini_game_paying_amount_d1','derived'=>array('gdt_mini_game_first_day_paying_roi')),'gdt_mini_game_pay_d3_uv','gdt_mini_game_d3_pay_count',array('name'=>'gdt_mini_game_paying_amount_d3','derived'=>array('gdt_mini_game_pay_d3_roi')),'gdt_mini_game_pay_d7_uv','gdt_mini_game_d7_pay_count',array('name'=>'gdt_mini_game_paying_amount_d7','derived'=>array('gdt_mini_game_pay_d7_roi')),'gdt_minigame_24h_pay_uv',array('name'=>'gdt_minigame_24h_pay_amount','derived'=>array('gdt_minigame_24h_pay_roi')),'gdt_first_day_first_pay_count','gdt_first_day_pay_count',array('name'=>'gdt_first_day_pay_amount','derived'=>array('gdt_roi_activated_d1')),'gdt_active_d3_pay_count',array('name'=>'gdt_payment_amount_activated_d3','derived'=>array('gdt_roi_activated_d3')),'active_d7_pay_count',array('name'=>'gdt_payment_amount_activated_d7','derived'=>array('gdt_roi_activated_d7')),'gdt_reg_dedup_pv'), false, NULL);?>

with creative_dash_alpha as (
    select
        `tday`, `main_channel_id`, `channel_id` , IF(`campaign_id`=0, '', `campaign_id`) as campaign_id ,IF(`plan_id`=0, '', `plan_id`) as plan_id,IF(`creative_id`=0, '', `creative_id`) as creative_id, `cp_game_id`, `game_id`,`package_id`,  `account_id`, `user_id`, `is_ad_data`, `is_appointment`, `marketing_goal`,
        <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('col_guy'), 'item');
$foreach0DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('item')->value) {
$foreach0DoElse = false;
?>
            <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                <?php if ((null !== ($_smarty_tpl->getValue('item')['format'] ?? null))) {?>
                    <?php $_smarty_tpl->getSmarty()->getRuntime('TplFunction')->callTemplateFunction($_smarty_tpl, $_smarty_tpl->getValue('item')['format'], array('fenir'=>$_smarty_tpl->getValue('item'),'level'=>0,'nodes'=>$_smarty_tpl->getValue('item')['nodes'],'cols'=>$_smarty_tpl->getValue('columns'),'hasCol'=>(null !== ($_smarty_tpl->getValue('columns') ?? null))), true);?>

                    <?php continue 1;?>
                <?php }?>

                <?php if ((null !== ($_smarty_tpl->getValue('item')['derived'] ?? null))) {?>
                    <?php if (!(null !== ($_smarty_tpl->getValue('columns') ?? null)) || ((null !== ($_smarty_tpl->getValue('columns') ?? null)) && ($_smarty_tpl->getSmarty()->getModifierCallback('count')($_smarty_tpl->getSmarty()->getModifierCallback('array_intersect')($_smarty_tpl->getValue('item')['derived'],$_smarty_tpl->getValue('columns'))) > 0 || $_smarty_tpl->getSmarty()->getModifierCallback('in_array')($_smarty_tpl->getValue('item')['name'],$_smarty_tpl->getValue('columns'))))) {?>
                        <?php echo $_smarty_tpl->getValue('item')['name'];?>
,
                    <?php }?>
                <?php }?>
            <?php } else { ?>
                <?php if (!(null !== ($_smarty_tpl->getValue('columns') ?? null)) || ((null !== ($_smarty_tpl->getValue('columns') ?? null)) && $_smarty_tpl->getSmarty()->getModifierCallback('in_array')($_smarty_tpl->getValue('item'),$_smarty_tpl->getValue('columns')))) {?> <?php echo $_smarty_tpl->getValue('item');?>
, <?php }?>
            <?php }?>
        <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
        update_time
    from <?php echo $_smarty_tpl->getValue('table');?>

    <?php if (!empty($_smarty_tpl->getValue('params'))) {?>
        <?php $_smarty_tpl->assign('mark_tag_1', 1, false, NULL);?>
        <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'fa', false, 'kk');
$foreach1DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('kk')->value => $_smarty_tpl->getVariable('fa')->value) {
$foreach1DoElse = false;
?>
            <?php if ($_smarty_tpl->getValue('kk') == 'range_date') {?>
                <?php if ($_smarty_tpl->getValue('mark_tag_1')) {?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php } else { ?> and <?php }?>
                tday between '<?php echo $_smarty_tpl->getValue('fa')[0];?>
' and '<?php echo $_smarty_tpl->getValue('fa')[1];?>
'
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'cp_game_id') {?>
                <?php if ($_smarty_tpl->getValue('mark_tag_1')) {?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php } else { ?> and <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('fa'))) {?>
                    cp_game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('fa'),',');?>
)
                <?php } else { ?>
                    cp_game_id = '<?php echo $_smarty_tpl->getValue('fa');?>
'
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'game_id') {?>
                <?php if ($_smarty_tpl->getValue('mark_tag_1')) {?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php } else { ?> and <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('fa'))) {?>
                    game_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('fa'),',');?>
)
                <?php } else { ?>
                    game_id = '<?php echo $_smarty_tpl->getValue('fa');?>
'
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'game_id_tags') {?>
                <?php if ($_smarty_tpl->getValue('mark_tag_1')) {?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php } else { ?> and <?php }?>
                game_id in (
                    select distinct data_id from base_conf_platform.biz_tags
                        where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('fa'),',');?>
) and table_name = 'games'
                )
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'package_id') {?>
                <?php if ($_smarty_tpl->getValue('mark_tag_1')) {?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php } else { ?> and <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('fa'))) {?>
                    package_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('fa'),',');?>
)
                <?php } else { ?>
                    package_id = '<?php echo $_smarty_tpl->getValue('fa');?>
'
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'package_id_tags') {?>
                <?php if ($_smarty_tpl->getValue('mark_tag_1')) {?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php } else { ?> and <?php }?>
                package_id in (
                    select distinct data_id from base_conf_platform.biz_tags
                        where tag_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('fa'),',');?>
) and table_name = 'packages'
                )
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'account_id') {?>
                <?php if ($_smarty_tpl->getValue('mark_tag_1')) {?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php } else { ?> and <?php }?>
                account_id like  '<?php echo (('%').($_smarty_tpl->getValue('fa'))).('%');?>
'
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'campaign_id') {?>
                <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('fa'))) {?>
                    campaign_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('fa'),',');?>
)
                <?php } else { ?>
                    campaign_id = '<?php echo $_smarty_tpl->getValue('fa');?>
'
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'campaign_name') {?>
                <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?>
                campaign_id in (select distinct campaign_id from adp_platform.tb_adp_campaign where campaign_name like '<?php echo (('%').($_smarty_tpl->getValue('fa'))).('%');?>
')
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'plan_id') {?>
                <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('fa'))) {?>
                    plan_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('fa'),',');?>
)
                <?php } else { ?>
                    plan_id = '<?php echo $_smarty_tpl->getValue('fa');?>
'
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'plan_name') {?>
                <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?>
                (
                plan_id in (select distinct plan_id from adp_platform.tb_adp_plan_base where plan_name like '<?php echo (('%').($_smarty_tpl->getValue('fa'))).('%');?>
')
                or plan_id in (select distinct id  from dataspy.tb_ad_svlink_conf where aid like '<?php echo (('%').($_smarty_tpl->getValue('fa'))).('%');?>
')
                )
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'creative_id') {?>
                <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?>
                <?php if (is_array($_smarty_tpl->getValue('fa'))) {?>
                    creative_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('fa'),',');?>
)
                <?php } else { ?>
                    creative_id = '<?php echo $_smarty_tpl->getValue('fa');?>
'
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'creative_name') {?>
                <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?>
                creative_id in (select distinct plan_id from adp_platform.tb_adp_creative_base where creative_name like '<?php echo (('%').($_smarty_tpl->getValue('fa'))).('%');?>
')
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'data_scope') {?>
                <?php if ($_smarty_tpl->getValue('fa') == 1) {?>
                    <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?> is_ad_data = 1
                <?php } elseif ($_smarty_tpl->getValue('fa') == 2) {?>
                    <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?> is_ad_data = 0
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'marketing_goal') {?>
                <?php if ($_smarty_tpl->getValue('fa') != array(1,2)) {?>
                    <?php if ($_smarty_tpl->getSmarty()->getModifierCallback('in_array')(1,$_smarty_tpl->getValue('fa'))) {?>
                        <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?> marketing_goal != 2
                    <?php }?>
                    <?php if ($_smarty_tpl->getSmarty()->getModifierCallback('in_array')(2,$_smarty_tpl->getValue('fa'))) {?>
                        <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?> marketing_goal = 2
                    <?php }?>
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'is_has_appointment') {?>
                <?php if (empty($_smarty_tpl->getValue('fa'))) {?> <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?> is_appointment != 1 <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('kk') == 'is_has_natural') {?>
                <?php if (empty($_smarty_tpl->getValue('fa'))) {?> <?php if (!$_smarty_tpl->getValue('mark_tag_1')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_1', 0, false, NULL);?> <?php }?> channel_id > 0 <?php }?>
                <?php continue 1;?>
            <?php }?>
        <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
    <?php }?>
),
dashboard_info as (
    select
        j1.tday,
        j1.cp_game_id,
        j1.game_id,
        j1.package_id,
        j1.campaign_id,
        any(coalesce(j2.campaign_name, '')) as campaign_name,
        j1.plan_id,
        j1.creative_id,
        j1.account_id,
        j1.ad_account,
        any(j1.platform_id) as platform_id,
        any(j1.account_name) as account_name,
        any(j1.is_ad_data) as is_ad_data,
        any(j1.is_appointment) as is_appointment,
        any(j1.marketing_goal) as marketing_goal,
        any(case when user_os = '["ANDROID"]' then 'ANDROID' when user_os = '["IOS"]' then 'IOS' else '混投' end)  as dim_user_os,
        any(j1.popularize_v2_id) as promotion_id,
        any(j1.app_show_id) as app_show_id,
        j1.promotion_channel_id,
        j1.dim_user_id,
        <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('col_guy'), 'item');
$foreach2DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('item')->value) {
$foreach2DoElse = false;
?>
            <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                <?php if ((null !== ($_smarty_tpl->getValue('item')['format'] ?? null))) {?>
                    <?php $_smarty_tpl->getSmarty()->getRuntime('TplFunction')->callTemplateFunction($_smarty_tpl, $_smarty_tpl->getValue('item')['format'], array('fenir'=>$_smarty_tpl->getValue('item'),'level'=>1,'nodes'=>$_smarty_tpl->getValue('item')['nodes'],'cols'=>$_smarty_tpl->getValue('columns'),'hasCol'=>(null !== ($_smarty_tpl->getValue('columns') ?? null))), true);?>

                    <?php continue 1;?>
                <?php }?>

                <?php if ((null !== ($_smarty_tpl->getValue('item')['derived'] ?? null))) {?>
                    <?php if (!(null !== ($_smarty_tpl->getValue('columns') ?? null)) || ((null !== ($_smarty_tpl->getValue('columns') ?? null)) && ($_smarty_tpl->getSmarty()->getModifierCallback('count')($_smarty_tpl->getSmarty()->getModifierCallback('array_intersect')($_smarty_tpl->getValue('item')['derived'],$_smarty_tpl->getValue('columns'))) > 0 || $_smarty_tpl->getSmarty()->getModifierCallback('in_array')($_smarty_tpl->getValue('item')['name'],$_smarty_tpl->getValue('columns'))))) {?>
                        SUM(<?php echo $_smarty_tpl->getValue('item')['name'];?>
) as <?php echo $_smarty_tpl->getValue('item')['name'];?>
,
                    <?php }?>
                <?php }?>
            <?php } else { ?>
                <?php if (!(null !== ($_smarty_tpl->getValue('columns') ?? null)) || ((null !== ($_smarty_tpl->getValue('columns') ?? null)) && $_smarty_tpl->getSmarty()->getModifierCallback('in_array')($_smarty_tpl->getValue('item'),$_smarty_tpl->getValue('columns')))) {?> SUM(<?php echo $_smarty_tpl->getValue('item');?>
) as <?php echo $_smarty_tpl->getValue('item');?>
, <?php }?>
            <?php }?>
        <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
        max(j1.update_time) as update_time
    from adp_platform.tb_adp_campaign j2 right join (
        select
            a1.*,
            <?php if ((null !== ($_smarty_tpl->getValue('ad_channels') ?? null))) {?>
                COALESCE( IF(a2.channel_id not in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('ad_channels'),',');?>
), a2.channel_id, IF(a1.channel_id != 0, IF(a1.channel_id =1013, 4, a1.channel_id), a2.channel_id) ) ,0) as promotion_channel_id,
                COALESCE( IF(a2.channel_id not in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('ad_channels'),',');?>
), a2.ad_user_id, IF(a1.user_id != 0, a1.user_id, a2.ad_user_id)),0 ) as dim_user_id,
            <?php } else { ?>
                a1.channel_id as promotion_channel_id,
                a1.user_id as dim_user_id,
            <?php }?>
            a3.ad_account, a4.advertiser_name as account_name, a2.platform_id,
            a2.popularize_v2_id, a2.app_show_id
        from creative_dash_alpha a1
        <?php if (!empty($_smarty_tpl->getValue('power_join_sql')) && $_smarty_tpl->getValue('power_join_sql') == 'base_conf_platform.tb_package_detail_conf') {?>
            join (<?php echo $_smarty_tpl->getValue('power_join_sql');?>
) a2 on a1.package_id = a2.package_id
        <?php } else { ?>
            join base_conf_platform.tb_package_detail_conf a2 on a1.package_id = a2.package_id
        <?php }?>
        left join base_conf_platform.tb_ad_account_conf a3 on a1.account_id = a3.account_id
        left join adp_platform.tb_adp_oauth a4 on a1.account_id = a4.advertiser_id and a1.main_channel_id = a4.channel_id
        <?php if (!empty($_smarty_tpl->getValue('params'))) {?>
            <?php $_smarty_tpl->assign('mark_tag_2', 1, false, NULL);?>
            <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'chill', false, 'ii');
$_smarty_tpl->getVariable('chill')->index = -1;
$foreach3DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('ii')->value => $_smarty_tpl->getVariable('chill')->value) {
$foreach3DoElse = false;
$_smarty_tpl->getVariable('chill')->index++;
$_smarty_tpl->getVariable('chill')->first = !$_smarty_tpl->getVariable('chill')->index;
$foreach3Backup = clone $_smarty_tpl->getVariable('chill');
?>
                <?php if ($_smarty_tpl->getValue('ii') == 'ad_account') {?>
                    <?php if (!$_smarty_tpl->getValue('mark_tag_2')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_2', 0, false, NULL);?> <?php }?>
                    a3.ad_account like '<?php echo (('%').($_smarty_tpl->getValue('chill'))).('%');?>
'
                    <?php continue 1;?>
                <?php }?>
                <?php if ($_smarty_tpl->getValue('ii') == 'app_show_id') {?>
                    <?php if (!$_smarty_tpl->getValue('mark_tag_2')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_2', 0, false, NULL);?> <?php }?>
                    <?php if (is_array($_smarty_tpl->getValue('chill'))) {?>
                        a2.app_show_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('chill'),',');?>
)
                    <?php } else { ?>
                        a2.app_show_id = '<?php echo $_smarty_tpl->getValue('chill');?>
'
                    <?php }?>
                    <?php continue 1;?>
                <?php }?>
                <?php if ($_smarty_tpl->getValue('ii') == 'platform_id') {?>
                    <?php if (!$_smarty_tpl->getValue('mark_tag_2')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_2', 0, false, NULL);?> <?php }?>
                    <?php if (is_array($_smarty_tpl->getValue('chill'))) {?>
                        a2.platform_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('chill'),',');?>
)
                    <?php } else { ?>
                        a2.platform_id = '<?php echo $_smarty_tpl->getValue('chill');?>
'
                    <?php }?>
                    <?php continue 1;?>
                <?php }?>
                <?php if ($_smarty_tpl->getValue('ii') == 'promotion_id') {?>
                    <?php if (!$_smarty_tpl->getValue('mark_tag_2')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_2', 0, false, NULL);?> <?php }?>
                    <?php if (is_array($_smarty_tpl->getValue('chill'))) {?>
                        a2.popularize_v2_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('chill'),',');?>
)
                    <?php } else { ?>
                        a2.popularize_v2_id = '<?php echo $_smarty_tpl->getValue('chill');?>
'
                    <?php }?>
                    <?php continue 1;?>
                <?php }?>
            <?php
$_smarty_tpl->setVariable('chill', $foreach3Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
        <?php }?>
    ) j1 on j1.main_channel_id = j2.channel_id and j1.campaign_id = j2.campaign_id
    <?php if (!empty($_smarty_tpl->getValue('params'))) {?>
        <?php $_smarty_tpl->assign('mark_tag_3', "1", false, NULL);?>
        <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'hi', false, 'ki');
$foreach4DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('ki')->value => $_smarty_tpl->getVariable('hi')->value) {
$foreach4DoElse = false;
?>
            <?php if ($_smarty_tpl->getValue('ki') == 'user_os') {?>
                <?php if (is_array($_smarty_tpl->getValue('hi'))) {?>
                    <?php if (!$_smarty_tpl->getValue('mark_tag_3')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_3', 0, false, NULL);?> <?php }?>
                    (
                    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('hi'), 'chill', false, 'ii');
$_smarty_tpl->getVariable('chill')->index = -1;
$foreach5DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('ii')->value => $_smarty_tpl->getVariable('chill')->value) {
$foreach5DoElse = false;
$_smarty_tpl->getVariable('chill')->index++;
$_smarty_tpl->getVariable('chill')->first = !$_smarty_tpl->getVariable('chill')->index;
$foreach5Backup = clone $_smarty_tpl->getVariable('chill');
?>
                        <?php if (!$_smarty_tpl->getVariable('chill')->first) {?> or <?php }?>
                        <?php if ($_smarty_tpl->getValue('chill') == 1) {?> j2.user_os =  '["IOS"]'<?php }?>
                        <?php if ($_smarty_tpl->getValue('chill') == 2) {?> j2.user_os =  '["ANDROID"]'<?php }?>
                        <?php if ($_smarty_tpl->getValue('chill') == 3) {?> ((j2.user_os != '["IOS"]' and j2.user_os != '["ANDROID"]') or j2.user_os is null)<?php }?>
                    <?php
$_smarty_tpl->setVariable('chill', $foreach5Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
                    )
                <?php }?>
                <?php continue 1;?>
            <?php }?>
            <?php if ($_smarty_tpl->getValue('ki') == 'campaign_name') {?>
                <?php if (!$_smarty_tpl->getValue('mark_tag_3')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('mark_tag_3', 0, false, NULL);?> <?php }?>
                j2.campaign_name like '<?php echo (('%').($_smarty_tpl->getValue('hi'))).('%');?>
'
            <?php }?>
        <?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
    <?php }?>
    group by j1.tday, j1.cp_game_id, j1.game_id, j1.package_id, j1.campaign_id, j1.plan_id, j1.creative_id, promotion_channel_id, j1.account_id, dim_user_id, j1.ad_account
)<?php }
/* smarty_template_function_dynamicAssign_13936038576859051764e037_50355828 */
if (!function_exists('smarty_template_function_dynamicAssign_13936038576859051764e037_50355828')) {
function smarty_template_function_dynamicAssign_13936038576859051764e037_50355828(\Smarty\Template $_smarty_tpl,$params) {
$_smarty_current_dir = '/mnt/e/project/spy2.0-api/public/templates/sql/advertise/ad_dashboard';
$params = array_merge(array('name'=>'dynamicAssign','fenir'=>array(),'nodes'=>1,'level'=>0,'cols'=>null,'hasCol'=>true), $params);
foreach ($params as $key => $value) {
$_smarty_tpl->assign($key, $value);
}
?>

    <?php if (!empty($_smarty_tpl->getValue('fenir'))) {?>
        <?php $_smarty_tpl->assign('cc', $_smarty_tpl->getValue('fenir')['name'], false, NULL);?>
        <?php if (!empty($_smarty_tpl->getValue('fenir')['displayed_formula'][$_smarty_tpl->getValue('level')])) {?>
            <?php $_smarty_tpl->assign('ffn', $_smarty_tpl->getValue('fenir')['displayed_formula'][$_smarty_tpl->getValue('level')], false, NULL);?>
            <?php
$_smarty_tpl->assign('i', null);
$_smarty_tpl->tpl_vars['i']->value = 1;
if ($_smarty_tpl->getValue('i') <= $_smarty_tpl->getValue('nodes')) {
for ($_foo=true;$_smarty_tpl->getValue('i') <= $_smarty_tpl->getValue('nodes'); $_smarty_tpl->tpl_vars['i']->value++) {
?>
                <?php $_smarty_tpl->assign('chillCol', $_smarty_tpl->getSmarty()->getModifierCallback('replace')($_smarty_tpl->getValue('cc'),"<n>",$_smarty_tpl->getValue('i')), false, NULL);?>
                <?php if (!empty($_smarty_tpl->getValue('fenir')['derived'])) {?>
                    <?php if (!$_smarty_tpl->getValue('hasCol') || ($_smarty_tpl->getValue('hasCol') && ($_smarty_tpl->getSmarty()->getModifierCallback('count')($_smarty_tpl->getSmarty()->getModifierCallback('array_intersect')($_smarty_tpl->getValue('fenir')['derived'],$_smarty_tpl->getValue('cols'))) > 0 || $_smarty_tpl->getSmarty()->getModifierCallback('in_array')($_smarty_tpl->getValue('chillCol'),$_smarty_tpl->getValue('cols'))))) {?>
                        <?php echo $_smarty_tpl->getSmarty()->getModifierCallback('replace')($_smarty_tpl->getValue('ffn'),"<n>",$_smarty_tpl->getValue('i'));?>
 as <?php echo $_smarty_tpl->getValue('chillCol');?>
,
                    <?php }?>
                <?php } else { ?>
                    <?php if ((!$_smarty_tpl->getValue('hasCol') || ($_smarty_tpl->getValue('hasCol') && $_smarty_tpl->getSmarty()->getModifierCallback('in_array')($_smarty_tpl->getValue('chillCol'),$_smarty_tpl->getValue('cols'))))) {?>
                        <?php echo $_smarty_tpl->getSmarty()->getModifierCallback('replace')($_smarty_tpl->getValue('ffn'),"<n>",$_smarty_tpl->getValue('i'));?>
 as <?php echo $_smarty_tpl->getValue('chillCol');?>
,
                    <?php }?>
                <?php }?>
            <?php }
}
?>
        <?php }?>
    <?php }
}}
/*/ smarty_template_function_dynamicAssign_13936038576859051764e037_50355828 */
}

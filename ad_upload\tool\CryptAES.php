<?php
// phpcs:disable

namespace app\ad_upload\tool;

class CryptAES
{
    private $method = 'AES-128-CBC';
    private $key;
    private $iv;

    public function setKey($key)
    {
        $this->key = $key;
    }

    public function setIv($iv)
    {
        $this->iv = $iv;
    }

    public function decrypt($data)
    {
        return openssl_decrypt($data, $this->method, $this->key, OPENSSL_RAW_DATA, $this->iv);
    }

    public function encrypt($data)
    {
        return openssl_encrypt($data, $this->method, $this->key, OPENSSL_RAW_DATA, $this->iv);
    }
}
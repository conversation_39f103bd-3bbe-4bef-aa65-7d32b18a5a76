<?php

namespace app\service\General;

use app\extension\FakeDB\FakeDB;

class AdpConfServ
{
    private function getConn()
    {
        return FakeDB::connection('adp_platform');
    }

    /**
     * 获取计划信息
     *
     * @param array $planIds
     * @return array
     */
    public function getPlanInfoByPlanIds(array $planIds): array
    {
        if (empty($planIds)) return [];

        $planIds      = array_unique(array_filter($planIds));
        $planIdString = implode(',', array_map(fn($item) => "'{$item}'", $planIds));

        $sql = "
        select 
            plan_id as plan_id,
            plan_name as plan_name
            from adp_platform.tb_adp_plan_base 
        where PLAN_ID IN ({$planIdString})";

        return $this->getConn()->query($sql)->fetchAll();
    }


}
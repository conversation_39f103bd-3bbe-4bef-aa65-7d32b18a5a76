<?php
/**
 * 虎牙数据上报
 * Created by PhpStorm.
 * User: maxie
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class <PERSON><PERSON> extends AdBaseInterface
{
    const CONVERT_ACTIVE   = 1;
    const CONVERT_REGISTER = 2;
    const CONVERT_PURCHASE = 3;


    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY');
    }


    /**
     * 公共上报
     * @param $info
     * @param $type
     * @return void
     */
    private function upload($info, $type)
    {
        switch ($type) {
            case 'ACTIVE':
                $callbackUrl = $info['CALLBACK_URL'] . '&event=' . self::CONVERT_ACTIVE;
                $typeName    = '激活';
                break;
            case 'REG':
                $callbackUrl = $info['CALLBACK_URL'] . '&event=' . self::CONVERT_REGISTER;
                $typeName    = '注册';
                break;
            case 'PAY':
                $callbackUrl = $info['CALLBACK_URL'] . '&event=' . self::CONVERT_PURCHASE;
                $typeName    = '付费';
                break;
        }

        $http = new Http($callbackUrl);
        $res  = $http->get();

        //记录上报结果
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'huya';
        $logInfo['request']      = json_encode(['url' => $callbackUrl,]);
        $logInfo['response']     = $res;
        $resContent              = json_decode($res, true);

        if ($resContent['code'] == 200) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        //写日志
        $this->log($info, $logInfo, $res, $callbackUrl);
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

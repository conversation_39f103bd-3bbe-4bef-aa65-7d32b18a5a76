<?php

namespace app\models\DdcPlatform;

use Plus\MVC\Model\ActiveRecord;

/**
 * @property int    id
 * @property string tday
 * @property int    cp_game_id
 * @property int    game_id
 * @property int    package_id
 * @property int    channel_id
 * @property int    sv_key
 * @property int    day_type
 * @property float  money
 * @property float  money_all
 * @property string  pay_date
 */
class DwsSvkeyAdLtvDaily extends ActiveRecord
{
    protected $_primaryKey = 'id';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->ddc_platform;
        parent::__construct($data);
    }
}
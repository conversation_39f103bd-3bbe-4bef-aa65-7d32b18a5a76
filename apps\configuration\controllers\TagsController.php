<?php

namespace app\apps\configuration\controllers;

use app\apps\internal\Traits\InternalRoutes;
use app\extension\Support\CommonCenter\ConfCenter\Request;
use app\extension\Support\CommonCenter\ConfCenter\Uri;
use app\service\General\GeneralOptionServ;
use Plus\MVC\Controller\JsonController;

/**
 * 标签管理
 *
 */
class TagsController extends JsonController
{
    use InternalRoutes;
    //缓存时间
    const cacheExpire = 1200;

    /**
     * @return array
     * @throws \Exception
     */
    public function listAction(): array
    {
        $request    = \Plus::$app->request;
        $optionType = $request->getValue('option_type');

        if (empty($optionType)) {
            return $this->error('missing required argument');
        }

        $key = "tags:$optionType";
        $tagsList = \Plus::$app->redis->get($key);
        if(!$tagsList) {
            $serv     = new GeneralOptionServ();
            $tagsList = $serv->listTags(['option_type' => $optionType]);
            \Plus::$app->redis->set($key,json_encode($tagsList),self::cacheExpire);
        }else{
            $tagsList = json_decode($tagsList,true);
        }


        $key = "tags:common-kv-config";
        $tagLevelOne = \Plus::$app->redis->get($key);
        if(!$tagLevelOne) {
            // 获取一级分类
            $bizRequest = new Request(Uri::getHost() . Uri::URI_COMMON_KV);
            $response   = $bizRequest->get();

            if (!$response) {
                return $this->error('配置获取错误,请稍后再试');
            }
            $response = \json_decode($response, true);
            if (empty($response['data']['items'])) {
                return $this->error('配置获取错误,请稍后再试');
            }
            $tagLevelOne = $response['data']['items'];

            uasort($tagLevelOne, fn($current, $next) => $current['id'] > $next['id']);

            \Plus::$app->redis->set($key,json_encode($tagLevelOne),self::cacheExpire);
        }else{
            $tagLevelOne = json_decode($tagLevelOne,true);
        }
 

        $listRe = [];

        foreach ($tagLevelOne as $item) {
            $itemId      = $item['id'] ?? '';
            $itemTitle   = $item['name'] ?? '';
            $code        = $item['code'] ?? '';
            $usePlatform = $item['use_platform'] ?: 2;

            if ($usePlatform != 1) {
                continue;
            }

            $children = [];

            foreach ($tagsList as $chill) {
                $chillType = $chill['type'];
                if ($chillType === $code) {
                    $children[] = [
                        'val' => $chill['name'],
                        'key' => $chill['id'],
                    ];
                }
            }

            if (!empty($children)) {
                $listRe[] = [
                    'val'      => $itemTitle,
                    'key'      => $itemId,
                    'children' => $children,
                ];
            }

        }

        return $this->success($listRe);
    }


}
<?php

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

/**
 * 抖音游戏渠道上报逻辑
 *
 */
class DouyinGame extends AdBaseInterface
{
    const REQ_URI = 'https://analytics.oceanengine.com/api/v2/conversion';

    public function uploadActive($info, $ext = [])
    {
        $this->uploadDouYinGame($info, 'active', $ext);
    }

    public function uploadLogin($info, $ext = [])
    {
        $this->uploadDouYinGame($info, 'login', $ext);
    }

    public function uploadRegister($info, $ext = [])
    {
        $this->uploadDouYinGame($info, 'active_pay', $ext);
    }

    /**
     * @param $info
     * @param $type
     * @param $ext
     * @param $logMark
     */
    protected function uploadDouYinGame($info, $type, $ext = [], $logMark = 'douyin_game')
    {
        $adExtra = [];
        $osId    = $info['OS'];
        $os      = 'others';

        if ($osId == 1) {
            $os = 'android';
        } elseif ($osId == 2) {
            $os = 'ios';
        }

        if ($type == 'active') {
            // 激活事件上报
            $adExtra = [
                'os'          => $os,
                'active_time' => strtotime($info['TIME_SERVER']),
            ];
        } elseif ($type == 'login') {
            // 登录事件上报
            $adExtra = [
                'os'           => $os,
                'game_user_id' => $info['CORE_ACCOUNT'],
            ];
        } elseif ($type == 'active_pay') {
            // 支付事件上报
            $adExtra = [
                'os'            => $os,
                'order_type'    => 1, // 1 - 支付成功 2 - 退款成功
                'game_user_id'  => $info['CORE_ACCOUNT'],
                'game_order_id' => $info['ORDER_ID'],
                'total_amount'  => $info['MONEY'] ?? 0,
                'pay_time'      => strtotime($info['PAY_TIME']),
            ];
        }// end if()

        $callbackParam = $info['CALLBACK_URL'];
        $postParam     = [
            'event_type' => $type,
            'context'    => [
                'ad' => ['callback' => $callbackParam],
            ],
            'extra'      => [
                'ad_extra_data' => $adExtra,
            ],
        ];

        $http      = new Http(self::REQ_URI);
        $res       = $http->post($postParam);
        $responseA = \json_decode($res, true);

        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = $logMark;
        $logInfo['request']      = json_encode(['url' => self::REQ_URI, 'data' => $postParam]);
        //记录上报结果
        $logInfo['response'] = $res;
        $resArr              = json_decode($res, true);
        if (isset($resArr['code']) && $resArr['code'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }

        $this->log($info, $logInfo, $res, self::REQ_URI);
    }


    function uploadPay($info, $ext = [])
    {
        // TODO: Implement uploadPay() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

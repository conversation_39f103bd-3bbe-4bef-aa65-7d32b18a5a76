<?php

namespace app\service\ReportMedia;

use app\service\ReportMedia\Contract\ReportMediaContract;
use app\service\ReportMedia\MediaCompany\KuaiShou;
use app\service\ReportMedia\MediaCompany\TxCommon;
use app\service\ReportMedia\MediaCompany\TxWeixin;

class ReportFactory
{
    const MEDIA_MAP = [
        '6'    => KuaiShou::class,
        '1013' => TxCommon::class,
        '1015' => TxCommon::class,
    ];

    /**
     * @param int $type
     *
     * @return ReportMediaContract|null
     */
    public static function getReportFactory(int $type = 1): ?ReportMediaContract
    {
        if (isset(static::MEDIA_MAP[$type])) {
            $c = static::MEDIA_MAP[$type];

            return new $c();
        }

        return null;
    }
}
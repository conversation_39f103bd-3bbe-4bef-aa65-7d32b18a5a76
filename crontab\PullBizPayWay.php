<?php
declare(ticks=1);

namespace app\crontab;

use app\models\baseConfPlatform\BizPayway;
use app\models\baseConfPlatform\BizTags;
use app\util\BizConfig;
use Plus\CLI\DaemonProcess;

/**
 *
 */
class PullBizPayWay extends DaemonProcess
{
    /**
     * @return void
     */
    public function run()
    {
        try {
            foreach ($this->getAllPayWay() as $chill) {
                $this->batchInsertOrUpdate($chill);
            }
        }
        catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            echo $e->getTraceAsString() . PHP_EOL;
        }
    }

    /**
     * @return \Generator
     */
    protected function getAllPayWay(): \Generator
    {
        $pageNo   = 1;
        $pageSize = 100;
        $req      = new BizConfig();
        $pageCnt  = 99;

        while ($pageCnt > 0) {
            try {
                $response     = $req->getPayWay(['pageNo' => $pageNo, 'pageSize' => $pageSize]);
                $responseData = $response['data'] ?? [];

                if ($pageNo == 1) $pageCnt = $responseData['pageCount'] ?? 1;

                if (empty($responseData['items'])) break;

                yield $responseData['items'];
            }
            catch (\Exception $e) {
                echo $e->getTraceAsString() . PHP_EOL;
                echo $e->getMessage() . PHP_EOL;
                break;
            }

            $pageNo++;
            $pageCnt--;
        }
    }

    /**
     * @param array $list
     * @return void
     */
    protected function batchInsertOrUpdate(array $list)
    {
        foreach ($list as $item) {
            if (empty($item['code']) || empty($item['name'])) continue;
            $item['order_offset'] = 0;
            $this->oneShot($item);
            $this->doubleShot($item);
        }

    }

    /**
     * @param $data
     * @return void
     */
    protected function oneShot($data)
    {
        $data = array_intersect_key($data, array_flip([
            'code', 'name', 'remarks', 'type', 'package_channel_main_id', 'operator',
            'payway_type', 'package_channel_main', 'add_time', 'update_time', 'order_offset',
        ]));

        $columns = \implode(', ', array_keys($data));
        $values  = "'" . implode("', '", array_values($data)) . "'";

        $sql = "INSERT INTO base_conf_platform.biz_payway ({$columns}) VALUES ({$values})";

        try {
            \Plus::$app->base_conf_platform_doris->query($sql)->execute();
        }
        catch (\Exception $e) {
            @\Plus::$app->log->error($e->getTraceAsString(), [], 'pull_biz_payway');
        }
    }


    /**
     * 双写
     * @param $data
     * @return void
     */
    protected function doubleShot($data)
    {
        $data = array_intersect_key($data, array_flip([
            'code', 'name', 'remarks', 'type', 'package_channel_main_id', 'operator',
            'payway_type', 'package_channel_main', 'add_time', 'update_time', 'order_offset',
        ]));

        $columns = \implode(', ', array_keys($data));
        $values  = "'" . implode("', '", array_values($data)) . "'";

        $sql = "INSERT INTO base_conf_platform.biz_payway ({$columns}) VALUES ({$values})";

        try {
            \Plus::$app->base_conf_platform_doris_new->query($sql)->execute();
        }
        catch (\Exception $e) {
            @\Plus::$app->log->error($e->getTraceAsString(), [], 'pull_biz_payway');
        }
    }
}
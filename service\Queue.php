<?php

namespace app\service;

use app\service\queue\FileQueue;
use framework\rabbit\RabbitQueue;
use Plus\Service\Children;

/**
 * 队列服务
 * @property FileQueue   $fileQueue
 * @property RabbitQueue $rabbitQueue
 * @property RabbitQueue $liveSyncQueue
 * <AUTHOR>
 */
class Queue extends Children
{
    const QUEUE_EXPORT_TASK_LOG = 'export_task_log';  // 导出处理队列
    const BASIC_USER_SPY        = 'basic_user_spy';   // 基础中台同步admin_user用户账号
    const LIVE_PACKAGE_SPY      = 'live_packages_spy'; // 直播包号配置
}

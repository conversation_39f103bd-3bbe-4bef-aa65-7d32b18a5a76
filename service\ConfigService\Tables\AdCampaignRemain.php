<?php

namespace app\service\ConfigService\Tables;

use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\Mather;
use app\service\ConfigService\Constants\FieldTag;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

/**
 * @AdCampaignRemain
 */
class AdCampaignRemain implements TableFieldAble
{
    use BaseHub, FieldTranslator;


    /**
     * @throws \Exception
     */
    public function getFields($options = null): Collection
    {
        $today  = new \DateTime();
        $NNodes = array_merge(
            range(1, 29), [44, 59], Mather::findNumInScope(59, 719, 30)
        );

        $collect = $this->getBaseFields(
            ['baseCollect', 'AdAccountBaseCollect', 'campaignBaseCollect',]
        );

        $collect = $collect->merge([
            'new_user'       => ['title' => '广告新增用户', 'sorter' => 'true'],
            'remain_days'    => ['title' => '留存统计天数'],
            'remain_current' => ['title' => '当前留存率'],
        ]);

        // $nDays = days_apart($today, $options['range_date_start'] ?? $today);
        $nDays = 720;
        $nDays -= 1;

        $remainList = $this->remainNCollect(Mather::findIn($nDays, $NNodes));
        $remainList = FieldTag::tagClassifyToNField($remainList, 'remain',
            [
                ['range' => [1, 29], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_1]],
                ['range' => [44, 179], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_2]],
                ['range' => [209, 359], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_3]],
                ['range' => [389, 719], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_4]],
            ]
        );

        $collect = $collect->merge($remainList);

        return $this->formatStandard($collect);
    }
}
<?php

namespace app\models\baseConfPlatform;

use Plus\MVC\Model\ActiveRecord;

/**
 * @table tb_base_package_conf
 * @property int    id
 * @property string package_id
 * @property int    game_id
 * @property int    channel_id
 * @property string begin_date
 * @property string end_date
 * @property int    popularize_v1_id
 * @property int    popularize_v2_id
 * @property int    company_id
 */
class TbBasePackageConf extends ActiveRecord
{
    public $_primaryKey = 'id';

    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->base_conf_platform;
    }
}
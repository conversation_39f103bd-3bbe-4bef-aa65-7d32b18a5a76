<?php
//phpcs:disable
namespace app\apps\operator\controllers;

use app\apps\internal\controllers\BaseController;
use app\apps\operator\Traits\OperationCalculators;
use app\apps\operator\Traits\OperatorRequest;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\TimeUtil;
use app\extension\Support\Helpers\Zakia;
use app\logic\operator\FirstLoginDashLogic;
use app\logic\advertise\AdIncomeDashLogic;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use app\service\OperationData\FirstLoginIndex;
use function GuzzleHttp\Promise\inspect;

class FirstLoginController extends BaseController
{
    use OperatorRequest, OperationCalculators;

    // 按小时维度
    const DIMENSION_HOUR = 1;
    // 按天维度
    const DIMENSION_DAY = 2;
    // 按周维度
    const DIMENSION_WEEK = 3;
    // 按月维度
    const DIMENSION_MONTH = 4;

    /**
     * 数据请求
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $dateDimension = (int)$params->get('range_date_dimension', static::DIMENSION_HOUR);

        if (static::DIMENSION_HOUR === $dateDimension) {
            $groups = ['tday', 'thour', 'package_id'];
        } else {
            $groups = ['tday', 'package_id'];
        }

        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];

        if ($params->has('groups')) {
            $groups = Arr::wrap($params->pull('groups'));
        }

        $fullGroups = ColumnManager::groupOperatorRelation($groups);

        if ($params->has('sort')) {
            $sort = $params->pull('sort');
            if (in_array($sort, ['tday', 'thour', 'cp_game_id', 'game_id', 'app_show_id', 'channel_main_id', 'platform_id', 'package_id', 'promotion_id', 'department_id', 'user_id'])
                && !in_array($sort, $fullGroups)
            ) {
                $sort = ['firstlogin_user desc'];
            } else {
                if ($params->has('order')) {
                    $sort .= ' ' . ($params->pull('order') == 'ascend' ? 'asc' : 'desc');
                }
            }
        } else {
            if (empty($groups)) {
                $sort = ['firstlogin_user desc'];
            } else {
                $sort = ['firstlogin_user desc'];
            }
        }// end if()
        $sort     = Arr::wrap($sort);
        $paginate = ['page' => $page, 'page_size' => $pageSize];

        $options        = $params->toArray();
        $rangeTimeType  = $params->get('range_date_dimension', 2);
        $rangeDateCycle = [];
        $serv           = new FirstLoginIndex();
        $options        = $params->toArray();
        $rangeTimeType  = $params->get('range_date_dimension', 2);
        $rangeDateCycle = [];

        if ($rangeTimeType == static::DIMENSION_WEEK) {
            [
                'begin' => $rangeDateStart,
                'end'   => $rangeDateEnd,
                'cycle' => $rangeDateCycle,
            ] = TimeUtil::divideWeekByRangeDate(
                Arr::pull($options, 'range_date_start'),
                Arr::pull($options, 'range_date_end')
            );

            $options['range_date_start'] = $rangeDateStart;
            $options['range_date_end']   = $rangeDateEnd;
        }


        $serv = new FirstLoginIndex();

        if ($dateDimension === static::DIMENSION_HOUR) {
            $result = $serv->listByHourly($options, $paginate, $groups, $sort);
        } else {
            $dashLogic = new FirstLoginDashLogic();
            $result    = $dashLogic->tableBase($options, $fullGroups, $paginate, $sort);
        }

        $list             = &$result['list'];
        $summaryRow       = &$result['summary'];
        $configBasic      = new BasicServ();
        $constConfCollect = $configBasic->getMultiOptions([
            'platform_id', 'promotion_id', 'department_id', 'user_id', 'promotion_channel_id',
            'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
        ])->put('channel_id', (new GeneralOptionServ())->listChannelOptions());

        $replaceFunc     = $this->replaceColumnDefine($constConfCollect);
        $resetFunc       = $this->resetUnusedColumn($groups);
        $generalCalcFunc = $this->firstLoginGeneralCalculators();
        $workLine        = [];

        if ($this->isAppendAdIncome($groups) && $rangeTimeType >= static::DIMENSION_DAY
        ) {
            $adIncomeLogic    = new AdIncomeDashLogic();
            $newOpt           = $options;
            $newOpt['groups'] = ['tday', 'cp_game_id', 'game_id'];

            if ($rangeTimeType == static::DIMENSION_MONTH) {
                $newOpt['range_date_start'] = date('Y-m-01', strtotime($newOpt['range_date_start']));
                $newOpt['range_date_end']   = date('Y-m-t', strtotime($newOpt['range_date_end']));
            }

            $adIncomeResult = $adIncomeLogic->getList($newOpt);
            $adIncomeList   = $adIncomeResult['list'] ?? [];
            $summaryIncome  = $adIncomeResult['summary'] ?? [];
            $adIncomeMap    = [];
            $formatFn       = fn($tDay) => $tDay;
            $groupIndex     = array_fill_keys($groups, 0);

//            $generalCalcFunc,
            if ($rangeTimeType == static::DIMENSION_WEEK) {
                $formatFn = function ($tDay) use ($rangeDateCycle) {
                    foreach ($rangeDateCycle as $range) {
                        if ($range['begin_date'] <= $tDay && $range['end_date'] >= $tDay) {
                            return $range['begin_date'] . '/' . $range['end_date'];
                        }
                    }
                    return $tDay;
                };
            } elseif ($rangeTimeType == static::DIMENSION_MONTH) {
                $formatFn = fn($tDay) => date('Y-m', strtotime($tDay));
            }

            foreach ($adIncomeList as $foo) {
                $incomeRMB   = round($foo['income_rmb'] ?? 0, 2);
                $foo['tday'] = $formatFn($foo['tday']);
                $kk          = implode('|', array_merge($groupIndex, array_intersect_key($foo, $groupIndex)));
                if (!isset($adIncomeMap[$kk])) {
                    $adIncomeMap[$kk] = $incomeRMB;
                } else {
                    $adIncomeMap[$kk] += $incomeRMB;
                }
            }

            $summaryRow['ad_income'] = round($summaryIncome['income_rmb'] ?? 0, 2);
            // 精度问题
            $adIncome                   = ($summaryIncome['income_rmb'] ?? 0);
            $payMoney                   = ($summaryRow['pay_money'] ?? 0);
            $summaryRow['total_income'] = bcadd((string)$adIncome, (string)$payMoney, 2);

            if (empty($summaryRow['total_income']) || empty($summaryRow['ad_income'])) {
                $summaryRow['ad_income_percent'] = '0%';
            } else {
                $summaryRow['ad_income_percent'] = round($summaryRow['ad_income'] / $summaryRow['total_income'] * 100, 2) . '%';
            }

            $workLine[] = function (&$target) use ($adIncomeMap, $groupIndex) {
                $kk                  = implode('|', array_merge($groupIndex, array_intersect_key($target, $groupIndex)));
                $target['ad_income'] = round($adIncomeMap[$kk] ?? 0, 2);

                // 精度问题
                $adIncome               = ($target['ad_income'] ?? 0);
                $payMoney               = ($target['pay_money'] ?? 0);
                $target['total_income'] = bcadd((string)$adIncome, (string)$payMoney, 2);

                if (empty($target['total_income']) || empty($target['ad_income'])) {
                    $target['ad_income_percent'] = '0%';
                } else {
                    $target['ad_income_percent'] = round($target['ad_income'] / $target['total_income'] * 100, 2) . '%';
                }
            };
        }// end if()

        if ($dateDimension == static::DIMENSION_HOUR) {
            $workLine = array_merge($workLine, [$generalCalcFunc]);
        }

        $workLine = array_merge($workLine, [$replaceFunc, $resetFunc]);

        $optionsServ = new GeneralOptionServ();
        if (in_array('package_id', $groups)) {
            $listPackages  = array_column($list, 'package_id');
            $packageTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'package_id', 'id' => $listPackages]) ?? []);

            if (!empty($packageTagMap)) {
                $packageTagAppendFn = function (&$target) use ($packageTagMap) {
                    if (!empty($target['package_id']) && $target['package_id'] != '-') {
                        $packageId              = $target['package_id'];
                        $target['package_tags'] = array_values($packageTagMap[$packageId] ?? []);
                    }
                };

                $workLine[] = $packageTagAppendFn;
            }
        }

        if (in_array('channel_id', $groups)
            || in_array('package_id', $groups)
        ) {
            $listChannel = array_column($list, 'channel_id');

            $channelTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'channel_id', 'id' => $listChannel]) ?? []);

            if (!empty($channelTagMap)) {
                $channelTagAppendFn = function (&$target) use ($channelTagMap) {
                    if (!empty($target['channel_id']) && $target['channel_id'] != '-') {
                        $channelId              = $target['channel_id'];
                        $target['channel_tags'] = array_values($channelTagMap[$channelId] ?? []);
                    }
                };

                array_unshift($workLine, $channelTagAppendFn);
            }
        }

        $this->processingLine($list, $groups, $workLine);

        if ($dateDimension == static::DIMENSION_HOUR) {
            @$generalCalcFunc($summaryRow);
        }

        return $result;
    }

    /**
     * 表头处理
     *
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $baseCollect = [
            'tday', 'thour', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id',
            'platform_id', 'department_id', 'user_id',
        ];

        $newUserCollect = [
            'activate_device', 'firstlogin_device',
            'firstlogin_user', 'create_role_new',
            'create_role_percent', 'create_role',
            'server_roll_percent', 'fistlogin_real_name',
            'device_conversion_rate',
        ];

        $costCollect = [
            'cost', 'cost_discount', 'new_user_cost',
        ];

        $payCollect = [
            'pay_new_user', 'pay_money_new', 'pay_money_all_with_hour', 'pay_user_new_percent',
            'pay_permeation_percent', 'arpu_new_user',
            'pay_user', 'pay_money', 'old_user_pay', 'old_user_pay_money',
            'old_user_pay_percent', 'old_user_arpu', 'old_user_arppu', 'arpu_new_user',
            'arppu_new_user', 'ad_income', 'total_income', 'ad_income_percent',
        ];

        $activeCollect = [
            'active_user', 'old_user', 'old_user_percent',
            'active_user_7days_ago', 'arpu_active', 'arppu_active',
            'active_pay_user', 'active_pay_percent', 'active_pay_user_percent',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'new_user_convert', 'label' => '新用户转化指标'],
                    ['value' => 'cost_index', 'label' => '成本指标'],
                    ['value' => 'pay_index', 'label' => '用户付费指标'],
                    ['value' => 'active_index', 'label' => '用户活跃指标'],
                    ['value' => 'tags', 'label' => '标签'],
                ],
            ],
        ];

        $options       = $params->toArray();
        $dateDimension = (int)$params->get('range_date_dimension', static::DIMENSION_HOUR);

        if (empty($options['groups'])) {
            if (static::DIMENSION_HOUR === $dateDimension) {
                $options['groups'] = ['tday', 'thour', 'package_id'];
            } else {
                $options['groups'] = ['tday', 'package_id'];
            }
        }

        $fields = $this->tableFields($options);

        if ($this->isAppendAdIncome($options['groups']) && $dateDimension >= static::DIMENSION_DAY
        ) {
            $fields[] = ['title' => '广告收入', 'dataIndex' => 'ad_income', 'align' => 'center'];
            $fields[] = ['title' => '总收入(含广告)', 'dataIndex' => 'total_income', 'align' => 'center'];
            $fields[] = ['title' => '广告收入占比', 'dataIndex' => 'ad_income_percent', 'align' => 'center'];
        }

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'] ?? '';

            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            } elseif (in_array($dIndex, $newUserCollect)) {
                $field['classify'] = ['attrs', 'new_user_convert'];
            } elseif (in_array($dIndex, $costCollect)) {
                $field['classify'] = ['attrs', 'cost_index'];
            } elseif (in_array($dIndex, $payCollect)) {
                $field['classify'] = ['attrs', 'pay_index'];
            } elseif (in_array($dIndex, $activeCollect)) {
                $field['classify'] = ['attrs', 'active_index'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @inerhitDoc
     *
     * @param $groups
     *
     * @return \Closure
     */
    protected function resetUnusedColumn($groups): \Closure
    {
        $resetCols = ColumnManager::groupFilterOperatorColumn($groups);

        return function (&$target) use ($resetCols) {
            $target = array_merge($target, $resetCols);
        };
    }

    /**
     * @param array $groups
     * @return bool
     */
    protected function isAppendAdIncome(array $groups): bool
    {
        return empty(array_diff($groups, ['tday', 'cp_game_id', 'game_id']));
    }
}

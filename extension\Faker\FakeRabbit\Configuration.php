<?php

namespace app\extension\Faker\FakeRabbit;

/**
 *
 */
class Configuration
{
    public string $host  = '';
    public string $vhost = '';

    public int $port = 0;

    public string $username = '';

    public string $password     = '';
    public string $login_method = 'AMQPLAIN';

    public bool $auto_ack = false;

    public bool $finish_init = false;

    public bool $confirm_init = false;

    public array $queue_declare_args = [];

    public bool $insist = false;

    public ?string $login_response = null;

    public string $locale = 'en_US';

    public int $connection_timeout = 30;

    public int $read_write_timeout = 10;

    /**
     * @var array|null|resource
     */
    public $context;
    /**
     * @var bool
     */
    public bool $keepalive = true;

    public int $heartbeat = 10;


    public function __construct(array $config = [])
    {
        foreach ($config as $key => $value) {
            if (is_numeric($key)) continue;

            if (isset($this->{$key})) {
                $this->{$key} = $value;
            }
        }
    }

}
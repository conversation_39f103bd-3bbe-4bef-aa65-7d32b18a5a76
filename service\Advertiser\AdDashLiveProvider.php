<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;
use app\service\General\BizTagsServ;
use Smarty\Exception;

class AdDashLiveProvider
{
    protected function dorisConn()
    {
        return FakeDB::connection('doris_entrance');
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $paginate
     * @param array $columns
     * @param bool  $isApiRule
     * @return array
     * @throws Exception
     */
    public function getData(
        array $params,
        array $groups = [],
        array $sort = [],
        array $paginate = [],
        array $columns = [],
        bool  $isApiRule = false
    ): array
    {
        $result     = [];
        $db         = $this->dorisConn();
        $adChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');

        if (!$isApiRule) $powerSQL = \Plus::$service->admin->powerSubSQL();
        $total    = 0;
        $totalTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_dashboard_live/creative_dash_live_total.tpl');
        $totalTpl
            ->assign('ad_channels', $adChannels)
            ->assign('params', $params)
            ->assign('groups', $groups);

        if (isset($powerSQL)) {
            $totalTpl->assign('power_join_sql', $powerSQL);
        }

        $total = $result['total'] = $db->query($totalTpl->fetch())->fetch()['total_row'] ?? 0;

        if ($total == 0) {
            return $result;
        }

        $infoTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_dashboard_live/creative_dash_live.tpl');
        $infoTpl
            ->assign('ad_channels', $adChannels)
            ->assign('params', $params)
            ->assign('groups', $groups);

        if (isset($powerSQL)) $infoTpl->assign('power_join_sql', $powerSQL);
        if (!empty($paginate)) $infoTpl->assign('paginate', $paginate);

        if (empty($sort)) {
            if (!empty($groups)) {
                $sort = array_fill_keys($groups, '');
            }
        }

        if (!empty($sort)) $infoTpl->assign('sorts', $sort);
        $result['list'] = $db->query($infoTpl->fetch())->fetchAll();

        $summaryTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_dashboard_live/creative_dash_live.tpl');
        $summaryTpl
            ->assign('ad_channels', $adChannels)
            ->assign('params', $params);

        if (isset($powerSQL)) {
            $summaryTpl->assign('power_join_sql', $powerSQL);
        }

        $summaryR          = $db->query($summaryTpl->fetch())->fetch();
        $result['summary'] = $summaryR;
        $result['time']    = $summaryR['last_update_time'] ?? '';

        return $result;
    }
}
<?php

namespace app\ad_upload\services;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\contract\AdUploadStrategyInterface;
use Plus\Util\StringUtil;

/**
 * 数据上报服务
 * <AUTHOR>
 */
class AdUploadService
{
    /**
     * 上报策略
     * @var AdUploadStrategyInterface
     */
    private AdUploadStrategyInterface $strategy;


    /**
     * 初始化
     * @param AdUploadStrategyInterface $strategy 上报策略
     * @return void
     */
    public function __construct(AdUploadStrategyInterface $strategy)
    {
        $this->strategy = $strategy;
    }


    /**
     * 上报处理
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @param string $action       上报动作
     * @param int    $channelId    渠道id
     * @return void
     */
    public function upload($uploadConfig, $packages, $action, $channelId): void
    {
        $beginTime  = microtime(true);
        \Plus::$uid = $action . uniqid();
        // 初始化上次上报点
        $this->strategy->initUploadLast();
        // 获取数据
        $data = $this->strategy->getData($uploadConfig, $packages);
        \Plus::$app->log->info("1.$action 获取数据条数：" . count($data), [], AdBaseInterface::LOG_DIR);
        $this->log('获取到数据', $data, $channelId, $action);
        // 获取数据为空，返回
        if (empty($data)) {
            return;
        }
        // 过滤数据
        $data = $this->strategy->filterData($data, $uploadConfig);
        \Plus::$app->log->info("2. $action 过滤数据后条数：" . count($data), [], AdBaseInterface::LOG_DIR);
        $this->log('过滤后数据', $data, $channelId, $action);
        // 过滤后数据为空，返回
        if (empty($data)) {
            //记录未匹配数据
            $this->strategy->setUnmatchedIds();
            return;
        }
        // 处理数据
        $processedData = $this->strategy->processData($data, $uploadConfig);
        \Plus::$app->log->info("3. $action 处理数据后条数：" . count($processedData), [], AdBaseInterface::LOG_DIR);
        $this->log('处理后数据', $processedData, $channelId, $action);
        // 上报数据
        $uploadMethod = 'upload' . StringUtil::convertToCamelHump($action, '_');
        foreach ($processedData as $item) {
            //逐条上报
            $this->strategy->uploadData($item, $uploadMethod);
            //设置上报点
            $this->strategy->setLastId($item);
        }
        //记录未匹配数据
        $this->strategy->setUnmatchedIds();
        // 记录上报耗时
        $endTime = microtime(true);
        \Plus::$app->log->info($endTime - $beginTime, [], 'ad_upload_time');
    }

    /**
     * 日志记录各个步骤处理后得到数据
     * @param string $title     title
     * @param array  $data      data
     * @param int    $channelId 渠道id
     * @param string $action    action
     * @return void
     */
    private function log($title, $data, $channelId, $action)
    {
//        $logData = array_column($data, 'ID');
//        $log     = sprintf("action:%s\nchannel:%s\n%s:\n%s", $action, $channelId, $title, json_encode($logData));
//        \Plus::$app->log->info($log, [], AdBaseInterface::LOG_DIR);
        foreach ($data as $v) {
            $log = sprintf("action:%s\nchannel:%s\n%s:\n%s", $action, $channelId, $title, json_encode($v));
            \Plus::$app->log->info($log, [], AdBaseInterface::LOG_DIR);
        }
    }
}

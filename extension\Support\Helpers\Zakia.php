<?php

namespace app\extension\Support\Helpers;

use app\models\baseConfPlatform\BizTags;
use app\models\baseConfPlatform\TbPackageDetailConf;

/**
 * 这是一个工具类
 *
 */
class Zakia
{
    /**
     * 检查必须要的参数
     *
     * @param array $data
     * @param array $fields
     *
     * @return bool
     */
    public static function arrayPropRequired(array $data, array $fields): bool
    {
        foreach ($fields as $field) {
            if (!isset($data[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查是否为IOS包号
     *
     * @param $package
     *
     * @return bool
     *
     */
    public static function checkPackageIsIOS($package): bool
    {
        return substr($package, -4) === '0099';
    }

    /**
     * 获取加密设备码
     *
     * @param $originDeviceId
     *
     * @return array
     * @throws \RedisException
     */
    public static function getDeviceId($originDeviceId): array
    {
        $originMd5DeviceId = \md5($originDeviceId);
        $deviceId          = \Plus::$service->dataEncryptor->encrypt($originDeviceId)[$originDeviceId] ?? '';
        $md5DeviceId       = \Plus::$service->dataEncryptor->encrypt($originMd5DeviceId)[$originMd5DeviceId] ?? '';

        return [$deviceId, $md5DeviceId];
    }

    /**
     * @param $channel
     * @param $package
     *
     * @return bool
     */
    public static function checkSmallGamePackage($channel, $package): bool
    {
        $packageArr = (new BizTags())->getWeiXinXcxPackageId();
        return in_array($channel, [2, 5]) && in_array($package, $packageArr);
    }

    /**
     * @param $url
     *
     * @return bool
     */
    public static function validUrl($url): bool
    {
        $pattern = "#(http|https)://(.*\.)?.*\..*#i";
        if (preg_match($pattern, $url)) {
            return true;
        }
        else {
            return false;
        }

    }

    /**
     * 根据标签表结构转换格式
     *
     * @param array $list
     * @param array $relation
     * @return array
     */
    public static function changeTagsStructMap(
        array $list,
        array $relation = [
            'tag_id'    => 'data_id',
            'tag'       => 'name',
            'parent'    => 'parent_name',
            'parent_id' => 'parent_id',
        ]
    ): array
    {
        $newList = [];

        [
            'tag_id'    => $tagIdIndex,
            'tag'       => $tagIndex,
            'parent'    => $parentIndex,
            'parent_id' => $parentIdIndex
        ] = $relation;

        foreach ($list as $item) {
            $tagId       = $item[$tagIdIndex];
            $tag         = $item[$tagIndex];
            $tagParent   = $item[$parentIndex];
            $tagParentId = $item[$parentIdIndex];

            if (!isset($newList[$tagId])) {
                $newList[$tagId] = [];
            }

            $chill = &$newList[$tagId];

            if (is_null($tagParentId)) {
                $chill[] = [
                    'name' => $tag
                ];
            }
            else {
                if (!isset($chill[$tagParentId])) {
                    $chill[$tagParentId] = [
                        'name'     => $tagParent,
                        'children' => [['name' => $tag]],
                    ];
                }
                else {
                    $chill[$tagParentId]['children'][] = ['name' => $tag];
                }
            }
        }

        return $newList;
    }

    //获取小程序包 安卓 ios包号
    public static function getXcxPackages($packageId)
    {
        $packageData = (new TbPackageDetailConf())->find(["PACKAGE_ID" => $packageId]);
        if (!$packageData) {
            return false;
        }
        if ($packageData["PLATFORM_ID"] == 101) {
            $data = [
                "android" => $packageId,
                "ios"     => $packageId + 1,
            ];
        }
        else {
            $data = [
                "android" => $packageId - 1,
                "ios"     => $packageId,
            ];
        }
        return $data;
    }

    /**
     * 中文字符转为英文字符
     *
     * @param $text
     * @return array|string|string[]
     */
    public static function convertChinesePunctuationToEnglish($text)
    {
        $chinesePunctuation = array('，', '。', '；', '：', '？', '！', '“', '”', '‘', '’', '（', '）', '【', '】', '【', '】', '《', '》');
        $englishPunctuation = array(',', '.', ';', ':', '?', '!', '"', '"', '\'', '\'', '(', ')', '[', ']', '[', ']', '<', '>');

        return str_replace($chinesePunctuation, $englishPunctuation, $text);
    }

    /**
     * 根据给定维度追加key
     *
     * @param $list
     * @param array $dimensions
     * @param string $separator
     * @return array
     */
    public static function appendKeysWithDimension($list, array $dimensions, string $separator = '|'): array
    {
        $result     = [];
        $dimensions = array_fill_keys($dimensions, 0);

        foreach ($list as $foo) {
            $dim        = array_merge($dimensions, array_intersect_key($foo, $dimensions));
            $k          = implode($separator, $dim);
            $result[$k] = $foo;
        }

        return $result;
    }

    /**
     * @param array $array
     * @return array
     */
    public static function removeTrailingEmptyValues(iterable $array): array
    {
        $count             = count($array);
        $lastNonEmptyIndex = null;

        for ($i = 0; $i < $count; $i++) {
            $value = $array[$i];

            if (empty($value)) {
                if ($lastNonEmptyIndex !== null) {
                    continue;
                }
            }
            else {
                $lastNonEmptyIndex = $i;
            }
        }

        if ($lastNonEmptyIndex !== null && $lastNonEmptyIndex < $count - 1) {
            // 使用array_splice移除尾部的空值
            array_splice($array, $lastNonEmptyIndex + 1);
        }

        return $array;
    }
}
<?php

namespace app\extension\FakeDB;

use Plus\SQL\Db;
use Spiral\Database\Config\DatabaseConfig;
use Spiral\Database\DatabaseManager;
use Spiral\Database\Driver\MySQL\MySQLDriver;
use Spiral\Database\Driver\Postgres\PostgresDriver;
use Spiral\Database\Driver\SQLite\SQLiteDriver;

/**
 * 扩展ORM
 *
 * @todo 接入ORM层解析
 * @see  DatabaseManager
 */
class FakeDB
{
    /**
     * @var DatabaseManager
     */
    protected static DatabaseManager $dbm;


    /**
     * 兼容原数据库初始配置
     *
     * @param array $config
     *
     * @return void
     */
    public static function initialize(array $config)
    {
        if (isset(static::$dbm)) {
            return;
        }
        $connMap    = [];
        $existHost  = [];
        $databases  = [];
        $components = $config['components'] ?? null;

        foreach ($components as $k => $item) {

            if (
                !isset($item['class'])
                || $item['class'] !== Db::class
            ) continue;

            foreach ($item as $i => $foo) {
                if (is_string($i) || !is_array($foo)) continue;

                $isReadOnly = $foo['readOnly'] ?? false;
                $db         = $foo['database_name'] ?? '';
                $mark       = $k;

                $dsn = static::getDsn(
                    ($foo['database_type'] ?? 'mysql'), $foo['server'],
                    ($foo['port'] ?? 3306), ($foo['database_name'] ?? '')
                );

                $newConn = [
                    'driver'  => static::chooseDatabaseType($foo['database_type'] ?? 'mysql'),
                    'options' => [
                        'connection' => $dsn,
                        'username'   => $foo['username'] ?? '',
                        'password'   => $foo['password'] ?? '',
                    ],
                ];

                $key = $k . (!$isReadOnly ? '' : '_read_only');

                isset($connMap[$key]) ?: $connMap[$key] = $newConn;

                if (!isset($databases[$mark])) {
                    $databases[$mark] = ['connection' => $key];
                    if ($isReadOnly) {
                        $databases[$mark] = ['readConnection' => $key];
                    }
                }
                else {
                    if (!$isReadOnly) {
                        $databases[$mark]['connection'] = $key;
                    }
                    else {
                        $databases[$mark]['readConnection'] = $key;
                    }
                }
            }
        }

        static::$dbm = new DatabaseManager(
            new DatabaseConfig(['index' => 'ddc_platform', 'databases' => $databases, 'connections' => $connMap])
        );

        unset($connMap, $databases);
    }

    /**
     * @param string $databaseType
     *
     * @return string
     */
    private static function chooseDatabaseType(string $databaseType): string
    {
        switch (strtolower($databaseType)) {
            case 'sqlite':
                return SQLiteDriver::class;
            case 'postgres':
                return PostgresDriver::class;
            default:
                return MySQLDriver::class;
        }

    }

    /**
     * @param string     $type
     * @param string     $host
     * @param string|int $port
     * @param string     $dbname
     *
     * @return string
     */
    private static function getDsn(string $type, string $host, $port = '3306', string $dbname = ''): string
    {
        return sprintf('%s:host=%s;port=%s;dbname=%s', $type, $host, $port, $dbname);
    }


    /**
     * @param string $db
     *
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    public static function connection(string $db)
    {
        return static::$dbm->database($db);
    }


    public static function __callStatic($method, $parameters)
    {
        return static::$dbm->database()->$method(...$parameters);
    }

}
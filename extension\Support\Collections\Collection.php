<?php

namespace app\extension\Support\Collections;

use app\extension\Support\Collections\Traits\EnumeratesValues;
use app\extension\Support\Contracts\Arrayable;
use app\extension\Support\Contracts\CanBeEscapedWhenCastToString;
use app\extension\Support\Contracts\Jsonable;
use app\extension\Support\Macroable\Traits\Macroable;
use Traversable;

/**
 * @template TKey of array-key
 * @template TValue
 *
 * @implements \ArrayAccess
 * @implements
 */
class Collection implements \ArrayAccess, Enumerable, CanBeEscapedWhenCastToString
{
    use EnumeratesValues, Macroable;

    /**
     * @var array<TKey, TValue>
     */
    protected array $items = [];


    public function __construct($items = [])
    {
        $this->items = $this->getArrayableItems($items);
    }

    /**
     * @param $from
     * @param $to
     * @return Collection|static
     */
    public static function range($from, $to): Collection
    {
        return new static(range($from, $to));
    }

    public function collapse()
    {
        return new static(Arr::collapse($this->items));
    }

    /**
     * @return array
     */
    public function all(): array
    {
        return $this->items;
    }

    /**
     * @template TGetDefault
     *
     * @param $key
     * @param $default
     * @return TValue|TGetDefault
     */
    public function get($key, $default = null)
    {
        if (array_key_exists($key, $this->items)) {
            return $this->items[$key];
        }

        return value($default);
    }

    /**
     * @param $key
     * @param $value
     * @return $this
     */
    public function put($key, $value): Collection
    {
        $this->offsetSet($key, $value);

        return $this;
    }

    /**
     * @param ...$values
     * @return $this
     */
    public function push(...$values): Collection
    {

        foreach ($values as $value) {
            $this->items[] = $value;
        }

        return $this;
    }

    /**
     * @param int $size
     * @return static<int, static>
     */
    public function chunk($size): Collection
    {
        if ($size <= 0) {
            return new static;
        }

        $chunks = [];

        foreach (array_chunk($this->items, $size, true) as $chunk) {
            $chunks[] = new static($chunk);
        }

        return new static($chunks);
    }

    /**
     * @param Arrayable<array-key, TValue>|iterable<array-key, TValue> $items
     * @return static
     */
    public function diff($items)
    {
        return new static(array_diff($this->items, $this->getArrayableItems($items)));
    }

    /**
     * @param $items
     * @return static|mixed
     */
    public function diffAssoc($items)
    {
        return new static(array_diff_assoc($this->items, $this->getArrayableItems($items)));
    }

    /**
     * @param          $items
     * @param callable $callback
     * @return static|mixed
     */
    public function diffAssocUsing($items, callable $callback): Collection
    {
        return new static(array_diff_uassoc($this->items, $this->getArrayableItems($items), $callback));
    }

    /**
     * @param $items
     * @return static|mixed
     */
    public function diffKeys($items): Collection
    {
        return new static(array_diff_key($this->items, $this->getArrayableItems($items)));
    }

    /**
     * @param          $items
     * @param callable $callback
     * @return static|mixed
     */
    public function diffKeysUsing($items, callable $callback): Collection
    {
        return new static(array_diff_ukey($this->items, $this->getArrayableItems($items), $callback));
    }

    /**
     * @param $items
     * @return array|mixed
     */
    protected function getArrayableItems($items)
    {
        if (is_array($items)) {
            return $items;
        }
        elseif ($items instanceof Enumerable) {
            return $items->all();
        }
        elseif ($items instanceof Arrayable) {
            return $items->toArray();
        }
        elseif ($items instanceof Jsonable) {
            return \json_decode($items->toJson(), true);
        }
        elseif ($items instanceof \JsonSerializable) {
            return (array)$items->jsonSerialize();
        }
        elseif ($items instanceof \Traversable) {
            return iterator_to_array($items);
        }

        return (array)$items;
    }

    /**
     * @param callable|null $callback
     * @return static
     */
    public function filter(callable $callback = null)
    {
        if ($callback) {
            return new static(Arr::where($this->items, $callback));
        }

        return new static(array_filter($this->items));
    }

    /**
     * @param $keys
     * @return $this|mixed
     */
    public function except($keys)
    {
        if ($keys instanceof Enumerable) {
            $keys = $keys->all();
        }
        elseif (!is_array($keys)) {
            $keys = func_get_args();
        }

        return new static(Arr::except($this->items, $keys));
    }

    /**
     * @template TMapValue
     *
     * @param callable $callback
     * @return static<TKey, TMapValue>
     */
    public function map(callable $callback): Collection
    {
        $keys  = array_keys($this->items);
        $items = array_map($callback, $this->items, $keys);

        return new static(array_combine($keys, $items));
    }

    /**
     * @param $items
     * @return static
     */
    public function merge($items): Collection
    {
        return new static(array_merge($this->items, $this->getArrayableItems($items)));
    }

    /**
     * @param Arrayable|iterable $items
     * @return static
     */
    public function replace($items): Collection
    {
        return new static(array_replace($this->items, $this->getArrayableItems($items)));
    }

    /**
     * @return static<int, TValue>
     */
    public function values(): Collection
    {
        return new static(array_values($this->items));
    }

    /**
     * @param float $depth
     * @return static<int, mixed>
     */
    public function flatten(float $depth = INF): Collection
    {
        return new static(Arr::flatten($this->items, $depth));
    }

    /**
     * @return static<TValue, TKey>
     */
    public function flip(): Collection
    {
        return new static(array_flip($this->items));
    }

    /**
     * @param $key
     * @return bool
     */
    public function has($key): bool
    {
        $keys = is_array($key) ? $key : func_get_args();

        foreach ($keys as $value) {
            if (!array_key_exists($value, $this->items)) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param $offset
     * @return bool
     */
    public function offsetExists($offset): bool
    {
        return isset($this->items[$offset]);
    }

    /**
     * @param $offset
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return $this->items[$offset];
    }

    /**
     * @param $offset
     * @param $value
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->items[] = $value;
        }
        else {
            $this->items[$offset] = $value;
        }
    }

    /**
     * @param $offset
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->items[$offset]);
    }

    /**
     * @return Traversable
     */
    public function getIterator(): Traversable
    {
        return new \ArrayIterator($this->items);
    }

    /**
     * @return int
     */
    public function count(): int
    {
        return \count($this->items);
    }

    /**
     * @param int $options
     * @return false|string
     */
    public function toJson(int $options = 0)
    {
        return json_encode($this->jsonSerialize(), $options);
    }

    /**
     * @param $callback
     * @return static
     */
    public function sort($callback = null): Collection
    {
        $items = $this->items;

        $callback && is_callable($callback)
            ? uasort($items, $callback)
            : asort($items, $callback ?? SORT_REGULAR);

        return new static($items);
    }

    /**
     * @param int $options
     * @return static
     */
    public function sortDesc(int $options = SORT_REGULAR): Collection
    {
        $items = $this->items;

        arsort($items, $options);

        return new static($items);
    }

    /**
     * @param      $callback
     * @param int  $options
     * @param bool $descending
     * @return static
     */
    public function sortBy($callback, int $options = SORT_REGULAR, bool $descending = false): Collection
    {
        if (is_array($callback) && !is_callable($callback)) {
            $this->sortMany($callback);
        }

        $results = [];

        $callback = $this->valueRetriever($callback);

        foreach ($this->items as $key => $value) {
            $results[$key] = $callback($value, $key);
        }

        $descending
            ? arsort($results, $options)
            : asort($results, $options);


        foreach (array_keys($results) as $key) {
            $results[$key] = $this->items[$key];
        }

        return new static($results);
    }

    /**
     * @param     $callback
     * @param int $options
     * @return static
     */
    public function sortByDesc($callback, int $options = SORT_REGULAR): Collection
    {
        return $this->sortBy($callback, $options, true);
    }

    /**
     * @param int  $options
     * @param bool $descending
     * @return static
     */
    public function sortKeys(int $options = SORT_REGULAR, bool $descending = false): Collection
    {
        $items = $this->items;

        $descending ? krsort($items, $options) : ksort($items, $options);

        return new static($items);
    }

    /**
     * @param int $options
     * @return static
     */
    public function sortKeysDesc(int $options = SORT_REGULAR): Collection
    {
        return $this->sortKeys($options, true);
    }

    /**
     * @param array $comparisons
     * @return static
     */
    protected function sortMany(array $comparisons = []): Collection
    {
        $items = $this->items;

        usort($items, function ($a, $b) use ($comparisons) {
            foreach ($comparisons as $comparison) {
                $comparison = Arr::wrap($comparison);

                $prop = $comparison[0];

                $ascending = Arr::get($comparison, 1, true) === true ||
                    Arr::get($comparison, 1, true) === 'asc';

                if (!is_string($prop) && is_callable($prop)) {
                    $result = $prop($a, $b);
                }
                else {
                    $values = [data_get($a, $prop), data_get($b, $prop)];

                    if (!$ascending) {
                        $values = array_reverse($values);
                    }

                    $result = $values[0] <=> $values[1];
                }

                if ($result === 0) {
                    continue;
                }

                return $result;
            }

            return true;
        });

        return new static($items);
    }

    /**
     * 删除并返回该元素
     *
     * @param string|int $key
     * @param mixed      $default
     * @return mixed
     */
    public function pull($key, $default = null)
    {
        return Arr::pull($this->items, $key, $default);
    }

}



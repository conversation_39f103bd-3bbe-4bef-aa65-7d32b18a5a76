<?php

namespace app\models\DdcPlatform;

use Plus\MVC\Model\ActiveRecord;
use Plus\SQL\Medoo;
use Plus\Util\Pagination;


/**
 * @DwsPlanAdBaseDaily
 *          短链维度-广告新增(拆分自然量)
 *
 */
class DwsPlanAdBaseDaily extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->ddc_platform;
        parent::__construct($data);
    }

    /**
     * 获取广告指标数据
     * @param $params
     * @return array|bool|mixed|\Plus\SQL\Db
     */
    public function list($params)
    {
        $fields = [];
        $join   = ["[>]dws_plan_ad_payment_daily (dws_plan_ad_payment_daily)" => ["TDAY", "PACKAGE_ID", "CHANNEL_ID", "PLAN_ID"]];
        $where  = [];


        //汇总
        $group    = explode(",", $params["groups"]);
        $groupArr = [];
        foreach ($group as $key) {
            $groupArr[] = $this->getKeyMap($key);
            //查询字段 - 需要显示字段
            if (!in_array($key, $fields)) {
                $fields[] = $this->getSearchField($key);
            }
        }

        //条件
        $fiterParamArr = ["cp_game_id", "game_id", "package_id", "promotion_channel_id", "main_channel_id", "plan_id"];
        foreach ($fiterParamArr as $key) {
            if (isset($params[$key])) {
                $where[$this->getKeyMap($key)] = $params[$key];
                //查询字段 - 需要显示字段
                if (!in_array($key, $fields)) {
                    $fields[] = $this->getSearchField($key);
                }
            }
        }
        $where["tday[<>]"] = [$params["range_date_start"], $params["range_date_end"]];


        //查询字段 - 需要统计的指标 例如新增用户  付费金额等
        $fieldsArr = explode(",", $params["fields"]);
        foreach ($fieldsArr as $key => $item) {
            $fields[$item] = Medoo::raw("SUM($item)");
        }

        if ($groupArr) {
            $where["GROUP"] = $groupArr;
        }

        $where['LIMIT'] = [($params["page"] - 1) * $params["page_size"], $params["page"] * $params["page_size"]];
        if (isset($params["order"]) && $params["order"]) {
            [$field, $order] = explode(" ", $params["order"]);
            $where["ORDER"] = [$field => strtoupper($order)];
        }

        return $this->_db->select($this->getTableName(), $join, $fields, $where);

    }

    /**
     * 获取映射关系 - 用于查询显示转换
     * @param $key
     * @return string
     */
    public function getSearchField($key)
    {
        return $this->getKeyMap($key) . " ($key)";
    }

    /**
     * 获取查询字段
     * @param $key
     * @return string
     */
    public function getKeyMap($key){
        $spceMap = [
            "cp_game_id" => $this->getTableName().".cp_game_id",
            "game_id" => $this->getTableName().".game_id",
            "package_id" => $this->getTableName().".package_id",
            "channel_id" => $this->getTableName().".channel_id",
            "sv_key" => $this->getTableName().".sv_key",
            "promotion_channel_id" => $this->getTableName().".channel_id",
        ];
        return isset($spceMap[$key])?$spceMap[$key]:$key;
    }
}
#!/usr/bin/python3
# coding=utf-8
import mysql.connector
from mysql.connector import errorcode

class Mysql:
    db = ''
    config = ''
    cursor = ''
    def __init__(self,Config):
        #数据库配置
        self.config = Config
        # 连接到MySQL数据库
        self.db = self.getMysqlConnect(Config['host'], Config['user'], Config['password'], Config['database'])
        # 创建游标对象
        self.cursor = self.db.cursor()

    #创建资源
    def getMysqlConnect(self,HOST,USER,PASSWORD,DATABASE):
        mydb = mysql.connector.connect(
            host=HOST,
            user=USER,
            password=PASSWORD,
            database=DATABASE,
            connect_timeout = 60
        )
        return mydb


    #批量插入(提交事务)
    def batchInsert(self,table,data):
        try:
            self.cursor.execute("START TRANSACTION")
            for v in data:
                print(v)
                # 插入的数据
                allowFields = v.keys()
                valuesStr = ''
                for k in allowFields:
                    valuesStr+="'"+str(v[k])+"',"
                valuesStr = valuesStr[0:-1];
                # sql语句
                sql = "INSERT IGNORE  INTO "+table+" ("+','.join(map(str, allowFields))+") \
                             VALUES ("+valuesStr+")"
                # 开始写入数据库
                self.cursor.execute(sql)
            self.cursor.execute("COMMIT")
        except mysql.connector.InternalError as e:
            print(e.msg)
            self.cursor.execute("ROLLBACK")
        return True

    #批量插入(提交事务)
    def batchUpdate(self,table,data):
        try:
            # 插入的数据
            allowFields = data[0].keys()
            updateWhere = ''
            for k in allowFields:
                updateWhere += k + '=values(' + k + '),'
            updateWhere = updateWhere[0:-1]

            #批量更新
            sql = "INSERT INTO " + table + " (" + ','.join(allowFields) + ")"
            valuesStr = ' VALUES '
            for v in data:
                valuesStr += "('" + "','".join('%s' %id for id in list(v.values())) + "'),"
            valuesStr = valuesStr[0:-1]
            sql += valuesStr + ' ON DUPLICATE KEY UPDATE ' + updateWhere
            # 开始写入数据库
            self.cursor.execute(sql)
            self.db.commit()
        except mysql.connector.errors.OperationalError as err:
            if err.errno == errorcode.CR_SERVER_LOST or err.errno == errorcode.CR_CONN_HOST_ERROR:
                print("Lost database connection. Reconnecting...")
            else:
                print("Error: ", err)
        return True

    def _del_(self):
        self.db.close()
        self.cursor.close()

<?php

namespace app\extension\Support\Collections;

class Arr
{

    /**
     * @param $value
     *
     * @return array
     */
    public static function wrap($value): array
    {
        if (is_null($value)) {
            return [];
        }

        return is_array($value) ? $value : [$value];
    }

    /**
     * @param $array
     *
     * @return array
     */
    public static function collapse($array): array
    {
        $result = [];

        foreach ($array as $values) {
            if ($values instanceof Collection) {
                $values = $values->all();
            }
            elseif (!is_array($values)) {
                continue;
            }

            $result[] = $values;
        }

        return array_merge([], ...$result);
    }

    /**
     * @param $value
     *
     * @return bool
     */
    public static function accessible($value): bool
    {
        return is_array($value) || $value instanceof \ArrayAccess;
    }

    /**
     * @param $array
     * @param $key
     *
     * @return bool
     */
    public static function exists($array, $key): bool
    {
        if ($array instanceof Enumerable) {
            return $array->has($key);
        }
        if ($array instanceof \ArrayAccess) {
            return $array->offsetExists($key);
        }

        return array_key_exists($key, $array);
    }

    /**
     * @param array        $array
     * @param array|string $keys
     *
     * @return void
     */
    public static function forget(array &$array, $keys)
    {
        $original = &$array;
        $keys     = (array)$keys;

        if (\count($keys) === 0) {
            return;
        }

        foreach ($keys as $key) {
            if (static::exists($array, $key)) {
                unset($array[$key]);

                continue;
            }

            $parts = \explode('.', $key);

            $array = &$original;

            while (\count($parts) > 1) {
                $part = array_shift($parts);

                if (isset($array[$part]) && is_array($array[$part])) {
                    $array = &$array[$part];
                }
                else {
                    continue 2;
                }
            }

            unset($array[array_shift($parts)]);
        }

    }

    /**
     * @param          $array
     * @param callable $callback
     *
     * @return array
     */
    public static function where($array, callable $callback): array
    {
        return array_filter($array, $callback, ARRAY_FILTER_USE_BOTH);
    }

    /**
     * @param $array
     *
     * @return array
     */
    public static function whereNotNull($array): array
    {
        return static::where($array, function ($value) {
            return !is_null($value);
        });
    }

    /**
     * @param array        $array
     * @param array|string $keys
     *
     * @return array
     */
    public static function except(array $array, $keys): array
    {
        static::forget($array, $keys);

        return $array;
    }


    /**
     * @param       $array
     * @param float $depth
     *
     * @return array
     */
    public static function flatten($array, float $depth = INF): array
    {
        $result = [];

        foreach ($array as $item) {
            $item = $item instanceof Collection ? $item->all() : $item;

            if (!is_array($item)) {
                $result[] = $item;
            }
            else {
                $values = $depth === 1
                    ? array_values($item)
                    : static::flatten($item, $depth - 1);

                foreach ($values as $value) {
                    $result[] = $value;
                }
            }
        }

        return $result;
    }

    /**
     * @param $array
     * @param $key
     * @param $default
     *
     * @return mixed
     */
    public static function get($array, $key, $default = null)
    {
        if (!static::accessible($array)) {
            return value($default);
        }

        if (is_null($key)) {
            return $array;
        }

        if (static::exists($array, $key)) {
            return $array[$key];
        }

        if (!str_contains($key, '.')) {
            return $array[$key] ?? value($default);
        }

        foreach (\explode('.', $key) as $segment) {
            if (static::accessible($array) && static::exists($array, $segment)) {
                $array = $array[$segment];
            }
            else {
                return value($default);
            }
        }


        return $array;
    }

    /**
     * 返回并在远对象中删除该元素
     *
     * @param array|Collection|\Iterator $array
     * @param string|int                 $key
     * @param mixed                      $default
     *
     * @return mixed
     */
    public static function pull(&$array, $key, $default = null)
    {
        $value = static::get($array, $key, $default);

        static::forget($array, $key);

        return $value;
    }

    /**
     * 数组头部插入
     *
     * @param array $array
     * @param mixed $value
     * @param mixed $key
     *
     * @return void
     */
    public static function prepend(array &$array, $value, $key = null)
    {
        if (func_num_args() === 2) {
            array_unshift($array, $value);
        }
        else {
            $array = [$key => $value] + $array;
        }
    }

    /**
     * @param $array
     * @param $key
     * @param $value
     *
     * @return array|mixed
     */
    public static function set(&$array, $key, $value)
    {
        if (is_null($key)) {
            return $array = $value;
        }

        $keys = explode('.', $key);

        foreach ($keys as $i => $key) {
            if (count($keys) === 1) break;

            unset($keys[$i]);

            if (!isset($array[$key]) || !is_array($array[$key])) {
                $array[$key] = [];
            }

            $array = &$array[$key];
        }
        $array[array_shift($keys)] = $value;

        return $array;
    }

    /**
     * @param array $array
     * @param array $map
     * @param bool  $isFlip
     * @param bool  $unsetOther
     *
     * @return array
     */
    public static function crossMap(array $array, array $map = [], bool $isFlip = false, bool $unsetOther = false): array
    {
        $result     = [];
        $map        = $isFlip ? array_flip($map) : $map;
        $keys       = array_fill_keys(array_keys($map), null);
        $targetKeys = array_values($map);

        foreach ($array as $item) {
            $kk = array_merge($keys, array_intersect_key($item, $keys));
            $c  = array_combine($targetKeys, $kk);

            if (!$unsetOther) {
                $other = array_diff_key($item, $kk);
                $c     = array_merge($c, $other);
            }

            $result[] = $c;
        }

        return $result;
    }

}
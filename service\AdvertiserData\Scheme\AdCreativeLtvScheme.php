<?php

namespace app\service\AdvertiserData\Scheme;

use app\extension\Support\Macroable\Traits\Macroable;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Traits\JoinBase;
use app\service\AdvertiserData\Traits\JoinFixAble;
use app\service\AdvertiserData\Traits\OperableQuery;
use app\service\AdvertiserData\Traits\Schemer;
use Aura\SqlQuery\Common\InsertInterface;
use Aura\SqlQuery\Common\SelectInterface;
use Aura\SqlQuery\Common\UpdateInterface;
use Aura\SqlQuery\QueryFactory;

/**
 * LTV
 */
class AdCreativeLtvScheme implements SchemeContract
{
    use Macroable, Schemer, OperableQuery, JoinFixAble, JoinBase;

    /**
     * @var QueryFactory
     */
    protected QueryFactory $queryFactory;
    /**
     * @var InsertInterface|SelectInterface|UpdateInterface
     */
    protected $query;

    /**
     * 主体表
     */
    public const MAIN_TABLE = [
        'alias' => 't_ltv',
        'table' => 'ddc_platform.dws_creative_ad_ltv_daily',
    ];

    /**
     * 关联表
     *
     * @var array|array[]
     */
    protected array $joinTables = [];

    public function __construct()
    {
        $this->queryFactory = new QueryFactory('mysql');
    }

    /**
     * 实例化对象
     *
     * @return AdCreativeLtvScheme
     */
    public static function NewOne(): AdCreativeLtvScheme
    {
        return new static();
    }

    public function __clone()
    {
        $this->queryFactory = clone $this->queryFactory;
        $this->query        = clone $this->query;
    }

    /**
     * @return array
     */
    public function fieldReflect($type = 'creative'): array
    {
        $mainTable = self::MAIN_TABLE['alias'];

        if ($type === 'plan') {
            $fields = [
                'tday'                 => $mainTable,
                'cp_game_id'           => $mainTable,
                'game_id'              => $mainTable,
                'package_id'           => $mainTable,
                'creative_id'          => $mainTable,
                'app_show_id'          => 'POWER',
                'channel_main_id'      => 'POWER',
                'channel_id'           => $mainTable,
                'platform_id'          => 'POWER',
                'plan_id'              => $mainTable,
                'campaign_id'          => $mainTable,
                'promotion_id'         => 'POWER.popularize_v2_id',
                'promotion_channel_id' => $mainTable . '.channel_id',
                'ad_account'           => 'base_account.ad_account',
                'ad_account_id'        => 'base_account.id',
                'account_id'           => 'base_account.account_id',
                'user_id'              => $mainTable.'.user_id',
                'department_id'        => 't_admin.department_id',
                'is_ad_data'           => 't_base'
            ];
        }
        else {
            $fields = [
                'tday'                 => $mainTable,
                'cp_game_id'           => $mainTable,
                'game_id'              => $mainTable,
                'package_id'           => $mainTable,
                'creative_id'          => $mainTable,
                'app_show_id'          => 'POWER',
                'channel_main_id'      => 'POWER',
                'channel_id'           => $mainTable,
                'platform_id'          => 'POWER',
                'plan_id'              => $mainTable,
                'campaign_id'          => $mainTable,
                'promotion_id'         => 'POWER.popularize_v2_id',
                'promotion_channel_id' => $mainTable . '.channel_id',
                'ad_account'           => 'base_account.ad_account',
                'ad_account_id'        => 'base_account.id',
                'account_id'           => 'base_account.account_id',
                'user_id'              => $mainTable.'.adv_user_id',
                'department_id'        => $mainTable.'.department_id',
                'is_ad_data'           => 't_base'
            ];
        }

        return $fields;
    }


    protected function fixedTables(): array
    {
        $mainTable  = self::MAIN_TABLE['alias'];
        $fixedTable = [];

        return $fixedTable;
    }

}
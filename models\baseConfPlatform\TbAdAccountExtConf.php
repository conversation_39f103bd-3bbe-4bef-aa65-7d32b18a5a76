<?php

namespace app\models\baseConfPlatform;

use Plus\MVC\Model\ActiveRecord;
/**
 * 推广账号配置表
 * @property int $ID 自增ID(CHANNEL_ID)
 * @property string $CHANNEL_ID 渠道
 * @property string $ADVERTISER_ID 账号id
 * @property string $START_DATE 开始时间
 * @property string $END_DATE 结束时间
 * @property string ADV_USER_ID 投放人
 * @property string UPDATE_TIME 更新时间
 * <AUTHOR>
 */
class TbAdAccountExtConf extends ActiveRecord
{
    public $_primaryKey = 'id';

    /**
     * 初始化，设置数据、数据库连接类
     *
     * @param array $data 批量配置数据
     *
     */
    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->base_conf_platform;
    }

    public function getUserIdByAdvertiserId($advertiserId){
        $result = $this->asArray()->find(["ADVERTISER_ID"=>$advertiserId,"ORDER"=>["UPDATE_TIME"=>"DESC"]]);
        return $result;
    }
}

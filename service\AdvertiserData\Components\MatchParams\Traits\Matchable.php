<?php

namespace app\service\AdvertiserData\Components\MatchParams\Traits;

trait Matchable
{
    /**
     * @var string[]
     */
    protected array $paramReflectMap = [];

    /**
     * @param $map
     * @return void
     */
    protected function setReflectMap($map)
    {
        $this->paramReflectMap = $map;
    }

    /**
     * @param $field
     * @return mixed|string
     */
    public function getField($field): string
    {
        if (isset($this->paramReflectMap[$field])) {
            $prefix = $this->paramReflectMap[$field];

            if (str_contains($prefix, '.')) {
                return $prefix;
            }

            if (is_array($prefix) && count($prefix) === 2) {
                return implode('.', $prefix);
            }

            return (string)$prefix . '.' . $field;
        }

        return $field;
    }

}
<?php

namespace app\service\ConfigService\Traits;

use app\service\ConfigService\Annotations\TableField;

trait CostFields
{
    /**
     * @TableField(
     *     title="返点前消耗金额",
     *     dataIndex="cost",
     *     options={"sort":true},
     *     classify={"attrs", "cost_info"}
     * )
     * @var string
     */
    public string $cost;

    /**
     * @TableField(
     *     title="返点后消耗金额",
     *     dataIndex="cost_discount",
     *     options={"sort":ture}
     *     classify={"attrs","cost_info"}
     * )
     * @var string
     */
    public string $costDiscount;

    /**
     * @TableField(
     *     title="新用户成本",
     *     dataIndex="new_user_cost",
     *     classify={"attrs","cost_info"}
     * )
     * @var string
     */
    public string $newUserCost;
}
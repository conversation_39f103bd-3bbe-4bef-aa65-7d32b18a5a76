{if !$mode_test}
    {include file="sql/advertise/ad_dashboard/creative_dash_info.tpl"}
{else}
    {include file="sql/advertise/ad_dashboard/creative_dash_alpha.tpl"}
{/if}
,
creative_deep_info as (
select
t1.tday,
t1.package_id,
t1.promotion_channel_id,
t1.campaign_id,
t1.plan_id,
t1.creative_id,
any(t1.campaign_name) as campaign_name,
any(game_id) as game_id,
any(cp_game_id) as cp_game_id,
any(dim_user_id) as dim_user_id,
any(t1.platform_id) as platform_id,
any(t1.app_show_id) as app_show_id,
any(channel_main_id) as channel_main_id,
any(department_id) as department_id,
any(t1.promotion_id) as promotion_id,
any(t1.account_id) as account_id,
any(t1.account_name) as account_name,
any(t1.ad_account) as ad_account,
any(t1.dim_user_os) as dim_user_os,
{if !empty($retain_type)}
    {if $retain_type eq 1}
        SUM(t1.pay_user_new) as retain_user
    {elseif $retain_type eq 2}
        SUM(t1.pay_new_user_7days) as retain_user
    {else}
        SUM(t1.pay_user_new) as retain_user
    {/if}
{/if}
from dashboard_info t1
left join base_conf_platform.tb_base_channel_conf t2 on t1.promotion_channel_id = t2.channel_id
left join dataspy.admin_user t3 on t1.dim_user_id = t3.id
{if !empty($params)}
    {assign var="first_mark" value=1}
    {foreach $params as $key => $item}
        {if $key eq 'channel_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($item)}
                t1.promotion_channel_id  in ({$item|join:','})
            {else}
                t1.promotion_channel_id  = '{$item}'
            {/if}
        {/if}
        {if $key eq 'channel_main_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($item)}
                channel_main_id in ({$item|join:','})
            {else}
                channel_main_id = {$item}
            {/if}
        {/if}
        {if $key eq 'user_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($item)}
                t1.dim_user_id in ({$item|join:','})
            {else}
                t1.dim_user_id = {$item}
            {/if}
        {/if}
        {if $key eq 'department_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($item)}
                t3.department_id in ({$item|join:','})
            {else}
                t3.department_id = {$item}
            {/if}
        {/if}
    {/foreach}
{/if}
group by t1.tday, t1.package_id, t1.promotion_channel_id, t1.campaign_id, t1.plan_id, t1.creative_id
)
select
{if !empty($max_retain_node)}
    {if !empty($show_type)}
        {for $f=1 to $max_retain_node}
            {if $show_type eq 1}
                SUM(IF(day_type={$f}, login_num, 0)) as remain_{$f},
            {elseif $show_type eq 2}
                CONCAT(SUM(IF(day_type={$f}, login_num, 0)),CONCAT('(' , ROUND( SUM(IF(day_type={$f}, login_num, 0)) / SUM(IF(day_type={$f}, retain_user, 0)) * 100 ,2) , '%',')') ) as remain_{$f},
            {else}
                COALESCE(CONCAT( ROUND( SUM(IF(day_type={$f}, login_num, 0)) / SUM(IF(day_type={$f}, retain_user, 0)) * 100 ,2) , '%'), '0.00%') as remain_{$f},
            {/if}
        {/for}
    {else}
        {for $f=1 to $max_retain_node}
            COALESCE(CONCAT( ROUND( SUM(IF(day_type={$f}, login_num, 0)) / SUM(IF(day_type={$f}, retain_user, 0)) * 100 ,2) , '%'), '0.00%') as remain_{$f},
        {/for}
    {/if}
{/if}
{if $show_type eq 1}
    SUM(IF(day_type={if $retain_type eq 2}1000{else}-1000{/if} and login_date=DATE_SUB(DATE(NOW()), INTERVAL 1 DAY), login_num, 0)) as remain_1000,
{elseif $show_type eq 2}
    CONCAT(SUM(IF(day_type={if $retain_type eq 2}1000{else}-1000{/if} and login_date=DATE_SUB(DATE(NOW()), INTERVAL 1 DAY) , login_num, 0)),CONCAT('(' , ROUND( SUM(IF(day_type={if $retain_type eq 2}1000{else}-1000{/if} and login_date=DATE_SUB(DATE(NOW()), INTERVAL 1 DAY), login_num, 0)) / SUM(IF(day_type={if $retain_type eq 2}1000{else}-1000{/if} and login_date=DATE_SUB(DATE(NOW()), INTERVAL 1 DAY), retain_user, 0)) * 100 ,2) , '%',')') ) as remain_1000,
{else}
    COALESCE(CONCAT( ROUND( SUM(IF(day_type={if $retain_type eq 2}1000{else}-1000{/if} and login_date=DATE_SUB(DATE(NOW()), INTERVAL 1 DAY), login_num, 0)) / SUM(IF(day_type={if $retain_type eq 2}1000{else}-1000{/if} and login_date=DATE_SUB(DATE(NOW()), INTERVAL 1 DAY), retain_user, 0)) * 100 ,2) , '%'), '0.00%') as remain_1000,
{/if}
SUM(all_retain_user) as paid_retain_user,
{if in_array('tday', $groups)}
    DATEDIFF(DATE(NOW()), tday) -1 as remain_days
{else}
    DATEDIFF(DATE(NOW()), '{$params['range_date'][0]}') -1 as remain_days
{/if}
{if !empty($groups)}
    ,
    {foreach $groups as $item}
        {if $item eq 'tday'} tday{if !$item@last}, {/if}
        {elseif $item eq 'cp_game_id'} cp_game_id{if !$item@last}, {/if}
        {elseif $item eq 'game_id'} game_id{if !$item@last}, {/if}
        {elseif $item eq 'app_show_id'} app_show_id{if !$item@last}, {/if}
        {elseif $item eq 'channel_main_id'} channel_main_id{if !$item@last}, {/if}
        {elseif $item eq 'promotion_channel_id'} promotion_channel_id{if !$item@last}, {/if}
        {elseif $item eq 'promotion_id'} promotion_id{if !$item@last}, {/if}
        {elseif $item eq 'user_id'} dim_user_id as user_id{if !$item@last}, {/if}
        {elseif $item eq 'department_id'} department_id{if !$item@last}, {/if}
        {elseif $item eq 'package_id'} package_id{if !$item@last}, {/if}
        {elseif $item eq 'platform_id'} platform_id{if !$item@last}, {/if}
        {elseif $item eq 'account_id'} account_id{if !$item@last}, {/if}
        {elseif $item eq 'account_name'} account_name{if !$item@last}, {/if}
        {elseif $item eq 'ad_account'} ad_account{if !$item@last}, {/if}
        {elseif $item eq 'campaign_id'} campaign_id,any(campaign_name) as campaign_name{if !$item@last}, {/if}
        {elseif $item eq 'plan_id'} plan_id{if !$item@last}, {/if}
        {elseif $item eq 'creative_id'} creative_id{if !$item@last}, {/if}
        {elseif $item eq 'dim_user_os'} dim_user_os{if !$item@last}, {/if}
        {/if}
    {/foreach}
{/if}
from (
select
r1.*,
IF(day_type =max(day_type) over (partition by tday, package_id, promotion_channel_id, campaign_id, plan_id, creative_id), retain_user, 0) as all_retain_user
from (
select a2.*, COALESCE(a1.day_type, 0) as day_type, a1.login_num, a1.login_date
from (
select
tday,
z1.package_id,
{if isset($ad_channels)}
    COALESCE(IF(z2.channel_id not in ({$ad_channels|join:','}),z2.channel_id, IF(z1.channel_id != 0,IF(z1.channel_id = 1013, 4, IF(z1.CAMPAIGN_ID!=0,z1.channel_id, z2.channel_id)),z2.channel_id)), 0) as p_channel_id,
{else}
    z2.channel_id as p_channel_id,
{/if}
IF(z1.campaign_id = '0' or null_or_empty(campaign_id) = 1, '', campaign_id) as campaign_id,
IF(z1.plan_id = 0 or null_or_empty(plan_id) = 1, '', plan_id)               as plan_id,
IF(z1.creative_id = 0 or null_or_empty(creative_id) =1, '', creative_id)    as creative_id,
day_type,
z1.cp_game_id,
z1.game_id,
main_channel_id,
sum(login_num) as login_num,
any(login_date) as login_date
from {if !empty($retain_type)}{if $retain_type eq 1}ddc_platform.dws_creative_ad_pay_once_remain_daily{elseif $retain_type eq 2} {if !$mode_test} ddc_platform.dws_creative_ad_pay_remain_daily_v2 {else} bigdata_dws.dws_creative_ad_pay_remain_daily_realtime {/if} {else}ddc_platform.dws_creative_ad_pay_once_remain_daily{/if}{else}ddc_platform.dws_creative_ad_pay_once_remain_daily{/if} z1
join base_conf_platform.tb_package_detail_conf z2 on z1.package_id = z2.package_id
{if !empty($params)}
    {assign var="mark_first_2" value=1}
    {foreach $params as $kk => $foo}
        {if $kk eq "range_date"}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            z1.tday between '{$foo[0]}' and '{$foo[1]}' {continue}
        {/if}
        {if $kk eq 'cp_game_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                z1.cp_game_id in ({$foo|join:','})
            {else}
                z1.cp_game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                z1.game_id in ({$foo|join:','})
            {else}
                z1.game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'package_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                z1.package_id in ({$foo|join:','})
            {else}
                z1.package_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'last_login_date'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            z1.login_date < '{$foo}'
            {continue}
        {/if}
    {/foreach}
{/if}
group by tday, z1.cp_game_id, z1.game_id, z1.package_id, campaign_id, plan_id,  creative_id, day_type, main_channel_id, p_channel_id
) a1
 join creative_deep_info a2 on a1.tday = a2.tday
and a1.package_id = a2.package_id
and a1.p_channel_id = a2.promotion_channel_id
and a1.campaign_id = a2.campaign_id
and a1.plan_id = a2.plan_id
and a1.creative_id = a2.creative_id
) r1
) rr1
{* 维度汇总 *}
{if !empty($groups)}
    group by
    {foreach $groups as $item}
        {if $item eq 'tday'} tday{if !$item@last}, {/if}
        {elseif $item eq 'cp_game_id'} cp_game_id{if !$item@last}, {/if}
        {elseif $item eq 'game_id'} game_id{if !$item@last}, {/if}
        {elseif $item eq 'app_show_id'} app_show_id{if !$item@last}, {/if}
        {elseif $item eq 'channel_main_id'} channel_main_id{if !$item@last}, {/if}
        {elseif $item eq 'promotion_channel_id'} promotion_channel_id{if !$item@last}, {/if}
        {elseif $item eq 'promotion_id'} promotion_id{if !$item@last}, {/if}
        {elseif $item eq 'user_id'} dim_user_id{if !$item@last}, {/if}
        {elseif $item eq 'department_id'} department_id{if !$item@last}, {/if}
        {elseif $item eq 'package_id'} package_id{if !$item@last}, {/if}
        {elseif $item eq 'platform_id'} platform_id{if !$item@last}, {/if}
        {elseif $item eq 'account_id'} account_id{if !$item@last}, {/if}
        {elseif $item eq 'account_name'} account_name{if !$item@last}, {/if}
        {elseif $item eq 'ad_account'} ad_account{if !$item@last}, {/if}
        {elseif $item eq 'campaign_id'} campaign_id{if !$item@last}, {/if}
        {elseif $item eq 'plan_id'} plan_id{if !$item@last}, {/if}
        {elseif $item eq 'creative_id'} creative_id{if !$item@last}, {/if}
        {elseif $item eq 'dim_user_os'} dim_user_os{if !$item@last}, {/if}
        {/if}
    {/foreach}
{/if}
{* 排序 *}
{if !empty($sorts)}
    order by
    {foreach $sorts as $ss => $oo}
        {$ss} {$oo}
        {if !$oo@last}, {/if}
    {/foreach}
{/if}
{* 分页 *}
{if !empty($paginate)}
    limit {$paginate.page_size} offset {$paginate.offset}
{/if}
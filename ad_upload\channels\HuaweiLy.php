<?php

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

/**
 * 华为联运 上报
 */
class HuaweiLy extends AdBaseInterface
{
    /**
     * 广告账号信息
     * @var array[]
     */
    protected $advertisersMap = [

        '900012194' => [
            'advertiser_id' => '900012194',
            'client_id'     => '1326788469892092992',
            'client_secret' => '3F1DB67124D9F61C2DE3989A73520ADA775F5E0545B347E5DDD8F6E9A3A6D0D8',
            'app_ids'       => ['108653841'],
        ],
    ];

    /**
     * 华为获取access_token的接口地址
     * @var string
     */
    private $oauthUri    = 'https://connect-api.cloud.huawei.com/api/oauth2/v1/token';
    private $acticateUri = 'https://connect-api.cloud.huawei.com/api/datasource/v1/track/activate';

    /**
     * 激活上报
     *
     * @param $info
     * @param $ext
     * @return void
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'active', $ext);
    }

    /**
     * 注册上报
     *
     * @param $info
     * @param $ext
     * @return void
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, 'register', $ext);
    }

    /**
     * 付费上报
     *
     * @param $info
     * @param $ext
     * @return void
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'pay', $ext);
    }

    /**
     * 华为渠道上报
     *
     * @link https://developer.huawei.com/consumer/cn/doc/promotion/bp-functions-ocpd-interface-return-0000001238484400
     *
     * @param $info
     * @param $type
     * @param $ext
     * @return void
     */
    private function uploadData($info, $type, $ext)
    {
        $log                     = \Plus::$app->log;
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'huawei_ly';
        $logInfo['log_type']     = 'reported_platform_log';

        $actionsMap = [
            'active'   => 1,
            'register' => 7,
            'pay'      => 4,
        ];

        $appId      = $info['EXT_CLICK']['app_id'];
        $advertiser = $this->getCliSecret($appId);

        if (empty($advertiser)) {
            $log->error('HUAWEILY_PARAM_' . $type . " 通过APPID获取CLIENT_SECRET失败,app_id:{$appId}", [], self::LOG_DIR_ERR);
            return;
        }

        $clientId = $advertiser['client_id'];
        $secret   = $advertiser['client_secret'];

        try {
            $token = $this->getAccessToken($clientId, $secret);
        } catch (\Exception $e) {
            $log->error('HUAWEILY_PARAM_' . $type . " 获取TOKEN失败,message:{$e->getMessage()}", [], self::LOG_DIR_ERR);
            return;
        }

        $header = [
            'client_id:' . $clientId,
            'Authorization:' . 'Bearer ' . $token,
            // 'charset'       => 'utf-8',
        ];

        $idType = $info['EXT_CLICK']['id_type'] ?? 1;

        if ($idType == 0) {
            $deviceType = 'IMEI_MD5';
            $deviceId   = $info['EXT_CLICK']['unique_id'] ?? '';
        } else {
            $deviceType = 'OAID';
            $deviceId   = $info['OAID'] ?? '';
        }

        $reportParams = [
            'appId'        => (int)$appId,
            'deviceIdType' => $deviceType,
            'deviceId'     => $deviceId,
            'actionTime'   => time() * 1000,
            'actionType'   => $actionsMap[$type] ?? '',
            'callBack'     => $info['CALLBACK_URL'] ?? '',
        ];

        if ($type == 'pay') {
            $orderId = $info['ORDER_ID'] ?? '';
            if (empty($info['MONEY'])) {
                \Plus::$app->log->error("HUAWEILY_PARAM_" . $type . "上报失败,message: 付费金额为0,order_id:{$orderId}", [], self::LOG_DIR_ERR);
                return;
            }

            $reportParams['actionParam'] = json_encode([['name' => '付费金额', 'value' => floatval($info['MONEY'])]], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        }

        $body = json_encode($reportParams, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        try {
            $logInfo['request'] = json_encode(['url' => $this->acticateUri, 'params' => $reportParams]);

            $http                = new Http($this->acticateUri);
            $res                 = $http->post($body, false, $header);
            $logInfo['response'] = $res;
            $response            = \json_decode($res, true);

            if (empty($response) || $response['code'] != 0) {
                $logInfo['reported_status'] = 1;
            } else {
                $logInfo['reported_status'] = -1;
            }
            $this->log($info, $logInfo, $res, $this->acticateUri);

            return;
        } catch (\Exception $e) {
            $log->error("HUAWEILY_PARAM_" . $type . " 上报失败,message:{$e->getMessage()}", [], self::LOG_DIR_ERR);
            return;
        }// end try()
    }


    /**
     * @param $appId
     * @return array
     */
    protected function getCliSecret($appId)
    {
        $r = [];
        foreach ($this->advertisersMap as $aId => $info) {
            $appIdMap = $info['app_ids'] ?? [];

            if (empty($appIdMap)) {
                continue;
            }

            if (in_array($appId, $appIdMap)) {
                $r['client_id']     = $info['client_id'] ?? '';
                $r['client_secret'] = $info['client_secret'] ?? '';
            }
        }

        return $r;
    }


    /**
     * 获取ACCESS_TOKEN
     *
     * @throws \Exception
     */
    protected function getAccessToken($clientId, $clientSecret)
    {
        $store    = \Plus::$app->redis82;
        $cacheKey = "cltj:hw_access_token_{$clientId}";
        $token    = $store->get($cacheKey);

        if (empty($token)) {
            $params   = [
                'grant_type'    => 'client_credentials',
                'client_id'     => $clientId,
                'client_secret' => $clientSecret,
            ];
            $http     = new Http($this->oauthUri);
            $response = $http->postJson($params);

            if (!$response) {
                throw new \Exception('获取access_token失败');
            }

            $response = \json_decode($response, true);

            if (isset($response['ret'])) {
                throw new \Exception("华为OAUTH2请求成功,但获取access_token失败,{$response['ret']}");
            } else {
                $token     = $response['access_token'];
                $expiresIn = $response['expires_in'];

                $store->setx($cacheKey, $token, ($expiresIn - 60));

                return $token;
            }
        } else {
            return $token;
        }// end if()
    }


    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

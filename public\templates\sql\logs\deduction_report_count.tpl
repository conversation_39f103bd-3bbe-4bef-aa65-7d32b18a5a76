select
    count(1) as total_rows
from ddc_platform.dwd_sdk_user_payment t_pay
right join bigdata_dwd.dwd_sdk_payment_intercept t1 on t1.order_id_actually = t_pay.order_id

{* 搜索条件 *}
{if isset($params) && !empty($params) }
    where true
    {* 上报时间 *}
    {if !empty($params['report_time'])}
        and t1.report_time between '{$params['report_time'][0]}' and '{$params['report_time'][1]}'
    {/if}
    {* 游戏原名 *}
    {if !empty($params['cp_game_id'])}
        and t1.cp_game_id IN ({$params['cp_game_id']})
    {/if}
    {* 游戏统计名 *}
    {if !empty($params['game_id'])}
        and t1.game_id IN ({$params['game_id']})
    {/if}
    {* 包号 *}
    {if !empty($params['package_id'])}
        and t1.package_id IN ({$params['package_id']})
    {/if}
    {* 核心账号 *}
    {if !empty($params['core_account'])}
        and t1.core_account like '%{$params['core_account']}%'
    {/if}
    {* 规则类型 *}
    {if !empty($params['report_type'])}
        and t1.report_type = {$params['report_type']}
    {/if}
    {* 规则名称 *}
    {if !empty($params['report_rule_name'])}
        and t1.report_rule_name like '%{$params['report_rule_name']}%'
    {/if}
    {if !empty($params['order_id'])}
        and t_pay.order_id IN ({$params['order_id']})
    {/if}
{/if}

{* 排序 *}
{if $sort|isset}
    order by
    {$is_fisrt=1}
    {foreach $sort as $k => $foo}
        {if $is_first eq 1}
            {$k} {$foo}
            {$is_first=0}
        {else}
            , {$k} {$foo}
        {/if}
    {/foreach}
{/if}

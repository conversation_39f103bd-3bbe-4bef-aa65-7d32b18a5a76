<?php
declare(ticks=1);

namespace app\daemon;

use app\extension\Exception\ParameterException;
use app\extension\FakeDB\FakeDB;
use app\extension\Support\CommonCenter\ConfCenter\Request;
use app\extension\Support\CommonCenter\ConfCenter\Uri;
use app\extension\Support\CommonCenter\WechatCenter\Request as WechatRequest;
use app\extension\Support\CommonCenter\WechatCenter\Uri as WechatApi;
use app\extension\Support\Helpers\TimeUtil;
use app\util\Common;
use Plus\CLI\DaemonProcess;
use Spiral\Database\Database;

/**
 *
 * 抓取广告收入数据
 *      支持平台: 微信
 *
 * @date 2024/07/25
 */
class AdIncomeCrawler extends DaemonProcess
{
    protected       $beginTime = null;
    protected       $endTime   = null;
    protected bool  $isCli     = false;
    protected array $appIds    = [];
    protected const MARK_STORE_PREFIX = 'spy2:ad_income_crawler:already_crawled:';

    /**
     * @throws \DateInvalidOperationException
     * @throws \Exception
     */
    public function run()
    {
        set_time_limit(0);
        $otherParams = Common::parseArgv($_SERVER['argv']);
        if (!empty($otherParams)) {
            // 当有额外参数传入时视为仅运行一次
            $this->beginTime = $otherParams['begin_time'] ?? date('Y-m-d');
            $this->endTime   = $otherParams['end_time'] ?? date('Y-m-d');
            $this->appIds    = $otherParams['appIds'] ?? [];
            $this->running   = false;
            $this->isCli     = true;
        }

        do {
            $now          = new \DateTime();
            $yesterday    = $now->sub(new \DateInterval('P1D'))->format('Y-m-d');
            $appIdList    = $this->getAppIdInfoList();
            $begin        = $this->beginTime ?? $yesterday;
            $end          = $this->endTime ?? $yesterday;
            $appIdGameMap = array_column($appIdList, 'game_id', 'appid');

            try {
                foreach (
                    TimeUtil::rangeDateGen($begin, $end) as $oneDate
                ) {
                    $dt   = $oneDate->format('Y-m-d');
                    $data = [];
                    echo $dt . PHP_EOL;

                    foreach ($this->incomeInfoWithOneDateFromApi($appIdList, $dt) as $appId => $list) {
                        // 获取分页请求的数据
                        // 格式化入库数据
                        $dd = $this->formatTencentPlatform($list, [
                            'platform_id' => 1,
                            'app_id'      => $appId,
                            'game_id'     => $appIdGameMap[$appId] ?? 0,
                        ]);

                        if (!empty($dd)) {
                            $data = array_merge($data, $dd);
                            if (!$this->isCli) {
                                \Plus::$app->redis->setex($this->getCacheKey($appId, $dt), $this->getSecondsUntilMidnight(), 1);
                            }
                        }
                    }

                    if (!empty($data)) {
                        $this->batchInsert($data);
                    }
                }
            }
            catch (\Exception $e) {
                echo $e->getMessage() . PHP_EOL;
            }

            if ($this->isCli) {
                break;
            }
            else {
                // 暂时一小时抓取一次
                sleep(3600);
            }
        }
        while ($this->running);
    }

    /**
     * appid 信息
     * @return array[]
     * @throws \Exception
     */
    protected function getAppIdInfoList(): array
    {
        $crawlerRe = [];
        $requestId = 'ad_income_' . uniqid();
        try {
            $req       = new Request(Uri::getHost() . Uri::URI_CRAWLER_AD_GAMES);
            $responser = $req->get();
            $response  = \json_decode($responser, true);

            if (empty($response['data'])) {
                \Plus::$app->log->info(json_encode(['request_id' => $requestId, 'message' => '此次获取广告收入抓取配置为空', 'response' => $responser]));
                return [];
            }
            else {
                \Plus::$app->log->info(json_encode(['request_id' => $requestId, 'response' => $responser]));
            }

            foreach ($response['data'] as $item) {
                if (is_object($item)) {
                    $item = (array)$item;
                }

                $adSwitch = $item['ad_switch'] ?? false;
                if (!$adSwitch) continue;
                if (empty($item['app_id']) || empty($item['game_id'])) continue;

                $crawlerRe[] = [
                    'appid'   => $item['app_id'],
                    'game_id' => $item['game_id'],
                ];
            }

            if (empty($crawlerRe)) {
                \Plus::$app->log->info(json_encode(['request_id' => $requestId, 'message' => '此次获取广告收入抓取配置为空']));
                return [];
            }
            else {
                return $crawlerRe;
            }

        }
        catch (\Exception $e) {
            \Plus::$app->log->error(json_encode(['request_id' => $requestId, 'message' => $e->getMessage()]));
            \Plus::$app->log->error(json_encode(['request_id' => $requestId, 'trace_message' => $e->getTraceAsString()]));
            return [];
        }
    }

    /**
     * @param $appIdList
     * @param $oneDate
     * @return \Generator
     */
    protected function incomeInfoWithOneDateFromApi($appIdList, $oneDate): \Generator
    {
        if (!empty($this->appIds)) {
            $onlyAppIds = $this->appIds;
            $filterFn   = function ($chill) use ($onlyAppIds) {
                $appId = $chill['appid'] ?? '';
                return in_array($appId, $onlyAppIds);
            };
        }
        else {
            $filterFn = function () {
                return true;
            };
        }

        foreach ($appIdList as $foo) {
            if (!$filterFn($foo)) continue;

            $app = $foo['appid'] ?? '';

            if (!$this->isCli) {
                $markKey = $this->getCacheKey($app, $oneDate);

                if (\Plus::$app->redis->exists($markKey)) {
                    continue;
                }
            }

            yield $app => $this->requestTencentApi($foo, [
                'start_date' => $oneDate,
                'end_date'   => $oneDate,
            ]);
        }
    }

    /**
     * @param       $appIdInfo
     * @param array $options
     * @return array
     */
    protected function requestTencentApi($appIdInfo, array $options = []): array
    {
        $retry     = 3;
        $page      = 1;
        $pageSize  = 100;
        $isRunning = true;
        $total     = -1;
        $result    = [];

        do {
            try {
                $params = array_merge($options, [
                    'action'    => 'publisher_adunit_general',
                    'page'      => $page,
                    'page_size' => $pageSize,
                    'appid'     => $appIdInfo['appid'],
                    'secret'    => $appIdInfo['secret'] ?? '',
                ]);

                $requestUri = WechatApi::HOST . WechatApi::URI_PUBLISHER_STAT;
                $request    = new WechatRequest($requestUri);
                $response   = $request->get($params);
                $response   = json_decode($response, true);

                if (JSON_ERROR_NONE !== json_last_error()) {
                    throw new \RuntimeException(json_last_error_msg());
                }

                if (empty($response['list'])) {
                    $isRunning = false;
                }
                else {
                    if ($total == -1) {
                        $total = (int)($response['total_num'] ?? 0);
                    }

                    $result = array_merge($result, $response['list']);

                    if (count($result) === $total) {
                        $isRunning = false;
                    }

                }
            }
            catch (\Exception $e) {
                \Plus::$app->log->error(json_encode(['message' => $e->getMessage()]));
                \Plus::$app->log->error(json_encode(['trace_message' => $e->getTraceAsString()]));
                $retry--;
            }

            $page++;
        }
        while ($isRunning && $retry > 0);

        return $result;
    }

    /**
     * @param       $list
     * @param array $fixed 追加的固定参数
     * @return array
     */
    protected function formatTencentPlatform($list, array $fixed = []): array
    {
        if (empty($list)) {
            return $list;
        }

        $data = [];
        foreach ($list as $item) {
            $statItem = $item['stat_item'] ?? [];

            if (empty($statItem)) continue;

            $d = [
                'dt'           => $statItem['date'] ?? '',
                'ad_id'        => $item['ad_unit_id'] ?? '',
                'ad_slot'      => $statItem['ad_slot'] ?? '',
                'ad_name'      => $item['ad_unit_name'] ?? '',
                'income_rmb'   => ($statItem['income'] ?? 0.00) * 0.01,
                'click_cnt'    => $statItem['click_count'] ?? 0,
                'exposure_cnt' => $statItem['exposure_count'] ?? 0,
                'pull_cnt'     => $statItem['req_succ_count'] ?? 0,
                'source_json'  => json_encode($item),
            ];

            $d      = array_merge($fixed, $d);
            $data[] = $d;
        }

        return $data;
    }

    /**
     * @param array $data
     * @return void
     */
    protected function batchInsert(array $data)
    {
        $db               = FakeDB::connection('origin_platform');
        $generateInsertFn = $this->insertUpdateSQLFn();

        try {
            $db->execute($generateInsertFn($data));
        }
        catch (\Throwable $e) {
            echo $e->getMessage() . PHP_EOL;
            echo $e->getTraceAsString() . PHP_EOL;
        }

    }

    /**
     * @return \Closure
     */
    protected function insertUpdateSQLFn(): \Closure
    {
        return function ($data) {
            $valueData    = [];
            $table        = 'origin_platform.tb_ad_income_crawler';
            $columns      = array_keys($data[0]);
            $insertSQL    = 'INSERT INTO ' . $table;
            $indexColumns = array_fill_keys($columns, 0);
            $updateKeys   = array_diff($columns, ['dt', 'platform_id', 'ad_id', 'ad_slot']);
            $onUpdates    = [];

            foreach ($data as $foo) {
                $d           = array_merge($indexColumns, array_intersect_key($foo, $indexColumns));
                $valueData[] = "('" . implode("', '", $d) . "')";
            }
            $insertSQL   .= ' (' . implode(',', $columns) . ') ';
            $valueString = implode(',', $valueData);
            $insertSQL   .= ' VALUES ' . $valueString;

            foreach ($updateKeys as $kk) {
                $onUpdates[] = "`$kk` = VALUES(`$kk`)";
            }

            if (!empty($onUpdates)) {
                $insertSQL .= ' on duplicate key update ' . implode(',', $onUpdates);
            }

            return $insertSQL;
        };
    }

    /**
     * @param $appId
     * @param $date
     * @return string
     */
    protected function getCacheKey($appId, $date): string
    {
        return self::MARK_STORE_PREFIX . $appId . '_' . $date;
    }

    /**
     * @return int
     */
    protected function getSecondsUntilMidnight(): int
    {
        $now      = new \DateTime();
        $midnight = (clone $now)->modify('tomorrow')->setTime(0, 0, 0);

        return $midnight->getTimestamp() - $now->getTimestamp();
    }
}
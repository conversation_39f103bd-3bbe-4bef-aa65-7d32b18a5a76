<?php

namespace app\logic\advertise\Traits;

trait RemainUtilTrait
{
    /**
     * 追加留存天数, 并去掉未到天数的字段
     * @param int $maxDayNode
     * @param bool $isHasTDay
     * @return \Closure
     */
    protected function appendRemainDays(int $maxDayNode, bool $isHasTDay = true, $prefix = 'remain_'): \Closure
    {
        $today = new \DateTime();

        if ($isHasTDay) {
            return function (&$target, &$context) use ($today, $prefix) {
                if (!isset($target['tday'])) return;

                $tDay                  = $target['tday'];
                $dayDiff               = max(days_apart($today, $tDay) - 1, 0);
                $target['remain_days'] = $dayDiff;

                if (isset($context['remain_key_map'])) {
                    $remainMap = array_keys($context['remain_key_map']);

                    foreach ($remainMap as $k) {
                        $nn = (int)str_replace($prefix, '', $k);
                        if ($nn == 1000) continue;
                        if ($nn > $dayDiff) {
                            unset($target[$k]);
                        }
                    }

                }
            };
        }
        else {
            return function (&$target, &$context) use ($maxDayNode) {
                $target['remain_days'] = $maxDayNode;
            };
        }


    }
}
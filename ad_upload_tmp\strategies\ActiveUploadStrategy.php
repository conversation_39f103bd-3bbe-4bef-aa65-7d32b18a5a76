<?php

namespace app\ad_upload_tmp\strategies;

use app\ad_upload_tmp\contract\AdBaseInterface;
use app\ad_upload_tmp\contract\AdUploadStrategyInterface;

/**
 * 激活数据上报 策略
 * <AUTHOR>
 */
class ActiveUploadStrategy extends AdUploadStrategyInterface
{
    /**
     * 上报类型
     * @var string
     */
    public $action = AdBaseInterface::ACTION_ACTIVE;

    /**
     * 最大id的数据
     * @var array
     */
    private $maxlastData = [];


    /**
     * 初始化上报点
     * @return void
     */
    public function initUploadLast()
    {
        parent::initUploadLast();
        if ($this->unmatchedIds == [0]) {
            $this->unmatchedIds = [];
        }
        //排除已过期的未归因匹配id
        // 数量大于100 才进行排除，减少查询次数
        if (count($this->unmatchedIds) < 100) {
            return;
        }
        $where  = $this->buildQueryKey();
        $before = date('Y-m-d H:i:s', strtotime($this->unmatchedTime));

        $sql                = "SELECT idfv,time FROM bigdata_tmp.dwd_sdk_activate_huge_match_tmp WHERE 
             ($where) AND time > '$before'";
        $data               = \Plus::$app->doris_entrance->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $this->unmatchedIds = $data;
    }

    /**
     * 构建查询唯一键条件
     * @return string
     */
    private function buildQueryKey()
    {
        $row = ["(idfv='' and time='1970-01-01 08:00:00')"];
        foreach ($this->unmatchedIds as $v) {
            $row[] = "(idfv = '{$v['idfv']}' and  time='{$v['time']}')";
        }
        return implode(' or ', $row);
    }

    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    public function getData($uploadConfig, $packages): array
    {
        if (empty($packages) || $this->lastId <= 0) {
            echo 'getData', $this->lastId, PHP_EOL;
            return [];
        }
        //获取补漏上报ID
        $where = $this->buildQueryKey();
        //lastId记录的是TIME时间戳
        $time = date('Y-m-d H:i:s', $this->lastId);
        //前3小时的
        $before = date('Y-m-d H:i:s', $this->lastId - 3600 * 3);
        //查询激活数据：可根据时间段或者最大ID查询
        $condition = "PACKAGE_ID IN ({$packages})";
        if (!empty($this->timeBegin) && !empty($this->timeEnd)) {
            $condition .= " AND TIME BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}' and new_channel_id={$this->channelId}";
        } else {
            $condition .= " AND (time > '$time' and new_channel_id in(0,$this->channelId) ) OR ";
            $condition .= "(time > '$before' and new_channel_id={$this->channelId} and ($where) )";
        }
        //$condition = " idfv ='0182DB07-F98E-448D-BD2E-0BF342F367B6'";
        $sql = "SELECT IDFV,`TIME` ,CALLBACK_PARAM_SOURCE,CALLBACK_PARAM,PACKAGE_ID,DEVICE_TYPE,DEVICE_KEY,
             SCREEN_WIDTH,SCREEN_HEIGHT,DEVICE_CODE,OAID, DEVICE_ID,MD5_DEVICE_ID,GAME_ID,OS,OS_VERSION,IP,
             USERAGENT,TIME_SERVER,ANDROID_ID, new_channel_id as CHANNEL_ID,SV_KEY,CLICK_ID,'{$this->action}' AS TYPE
                    FROM bigdata_tmp.dwd_sdk_activate_huge_match_tmp 
                    WHERE {$condition}
                    ";
        return \Plus::$app->doris_entrance->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 设置如果上一步getData 获取数据为空，设置最大上报id
     * 避免长期没有数据上报的渠道，查询过多数据
     * @return void
     */
    public function setMaxLastId(): void
    {
        $this->setLastId($this->maxlastData);
    }

    /**
     * 上报数据过滤
     * @param array $data         data
     * @param array $uploadConfig uploadConfig
     * @return array
     */
    public function filterData($data, $uploadConfig): array
    {
        $unmatchedIds = $this->unmatchedIds;
        $dataActive   = [];
        foreach ($data as $key => $val) {
            $uploadConfigOne = $uploadConfig[$val['PACKAGE_ID']] ?? [];
            if (empty($uploadConfigOne)) {
                \Plus::$app->log->error('not_match_config渠道配置不存在' . json_encode($val), [], AdBaseInterface::LOG_DIR);
            }
            foreach ($uploadConfigOne as $v) {
                //1为全量上报，2为匹配点击上报
                if ($v['UPLOAD_METHOD'] == 1 || ($v['UPLOAD_METHOD'] == 2)) {
                    $channelId = $v['UPLOAD_METHOD'] == 1 ? $v['CHANNEL_ID'] : $val['CHANNEL_ID'];
                    $clickId   = $v['UPLOAD_METHOD'] == 1 ? 0 : $val['CLICK_ID'];

                    //未归因数据缓存
                    $unmatchedIds = $this->cache->processUnmatchedIds2($unmatchedIds, $val);
                    if (in_array(['idfv' => $val['IDFV'], 'time' => $val['TIME']], $unmatchedIds)) {
                        \Plus::$app->log->info('unmatched_' . $this->action . $val['IDFV'], [], AdBaseInterface::LOG_DIR);
                        continue; //剔除未匹配的数据
                    }

                    // 没有force=1，剔除已上报的数据
                    if ($this->force == 0) {
                        $key   = 'ad_up_repeat_' . $this->action;
                        $value = $val['IDFV'] . '|' . $val['TIME'];
                        if (\Plus::$app->redis82->hget($key, $value)) {
                            \Plus::$app->log->warning('repeat_' . $this->action . $value, [], AdBaseInterface::LOG_DIR);
                            continue;
                        }
                        \Plus::$app->redis82->hset($key, $value, 1);
                    }

                    $dataActive[] = array_merge($val, [
                        'CHANNEL_ID'    => $channelId,
                        'UPLOAD_METHOD' => $v['UPLOAD_METHOD'],
                        'EXT_ID'        => $v['ID'],
                        'EXT'           => $v['EXT'],
                        'CLICK_ID'      => $clickId,
                    ]);
                }// end if()
            }// end foreach()
        }// end foreach()

        $this->unmatchedIds = $unmatchedIds;

        return $dataActive;
    }
}

<?php

namespace app\apps\internal\Traits;

use app\apps\internal\Helpers\IndicatorCalcHelpers;
use app\apps\internal\Helpers\IndicatorsHelpers;
use app\extension\Support\Collections\Arr;

/**
 * 部分指标处理复用部分
 */
trait AdIndexCalculators
{
    /**
     * ltv 批量处理
     *
     * @param array $ltvList
     * @param array $options
     * @param int   $maxDays
     *
     * @return \Closure
     */
    protected function ltvCalculators(array $ltvList = [], array $options = [], int $maxDays = 3): \Closure
    {
        $groups = $options['groups'];

        return function (&$target, $k) use ($ltvList, $groups, $maxDays) {

            $diff = $this->getDaysDiff(
                $target['tday'], $maxDays, in_array('tday', $groups)
            );

            $ltvInfo = $ltvList[$k]['day_type'] ?? [];

            if (!empty($ltvInfo)) {
                $this->ltvCalc($target, $ltvInfo);
            }

            $this->fillNodeNData($target, $diff, 0.00, 'ltv');

            if ($maxDays > $diff) {
                $this->fillNodeNData($target, $maxDays, '-', 'ltv');
            }
        };
    }

    /**
     * roi 批量处理
     *
     * @param array $ltvList
     * @param array $options
     * @param int   $maxDays
     *
     * @return \Closure
     */
    protected function roiCalculators(array $ltvList = [], array $options = [], int $maxDays = 3): \Closure
    {
        $groups = $options['groups'];

        return function (&$target, $k) use ($ltvList, $groups, $maxDays) {
            $diff = $this->getDaysDiff(
                $target['tday'], $maxDays,
                in_array('tday', $groups)
            );

            $ltvInfo = $ltvList[$k]['day_type'] ?? [];

            if (!empty($ltvInfo)) {
                $this->roiCalc($target, $ltvInfo);
            }

            $this->fillNodeNData($target, $diff, 0.00, 'roi');

            if ($maxDays > $diff) {
                $this->fillNodeNData($target, $maxDays, '-', 'roi');
            }
        };
    }

    /**
     * 留存 处理
     *
     * @param array $remainList
     * @param array $options
     * @param int   $maxDays
     *
     * @return \Closure
     */
    protected function remainCalculators(array $remainList = [], array $options = [], int $maxDays = 3): \Closure
    {
        $groups = $options['groups'];

        return function (&$target, $k) use ($remainList, $groups, $maxDays) {
            $diff = $this->getDaysDiff(
                $target['tday'], $maxDays,
                in_array('tday', $groups)
            );

            $remainInfo = $remainList[$k]['day_type'] ?? [];

            if (!empty($remainInfo)) {
                $this->remainCalc($target, $remainInfo);
            }

            $this->fillNodeNData($target, $diff, '0.00%', 'remain');

            if ($maxDays > $diff) {
                $this->fillNodeNData($target, $maxDays, '-', 'remain');
            }
        };
    }

    /**
     * 付费留存处理
     *
     * @param array $remainList
     * @param array $options
     * @param int   $maxDays
     *
     * @return \Closure
     */
    protected function payRemainCalculators(array $remainList = [], array $options = [], int $maxDays = 3): \Closure
    {
        $groups = $options['groups'];

        return function (&$target, $k) use ($remainList, $groups, $maxDays) {
            $diff = $this->getDaysDiff(
                $target['tday'], $maxDays,
                in_array('tday', $groups)
            );

            $remainInfo = $remainList[$k]['day_type'] ?? [];

            if (!empty($remainInfo)) {
                $this->remainCalc($target, $remainInfo, false, 'pay_remain');
            }

            $this->fillNodeNData($target, $diff, '0.00%', 'pay_remain');

            if ($maxDays > $diff) {
                $this->fillNodeNData($target, $maxDays, '-', 'pay_remain');
            }
        };
    }

    /**
     * @param array $passLevelList
     * @param array $options
     *
     * @return \Closure
     */
    protected function passLevelCalculators(array $passLevelList = [], array $options = []): \Closure
    {
        $groups = $options['groups'] ?? [];

        return function (&$target, $k) use ($passLevelList, $groups) {

            if (
                in_array('cp_game_id', $groups) ||
                in_array('game_id', $groups) ||
                in_array('package_id', $groups) ||
                in_array('plan_id', $groups) ||
                in_array('campaign_id', $groups) ||
                in_array('creative_id', $groups)
            ) {
                if (!in_array($target['cp_game_id'], [743, 749])) {
                    $this->fillNodeNData($target, 4, '-', 'pass_level');
                }
            }

            if (empty($passLevelList[$k])) {
                $this->fillNodeNData($target, 4, '0.00%', 'pass_level');
                return;
            }

            $passLevelInfo = $passLevelList[$k];

            $this->passLevelCalc($target, $passLevelInfo);
        };
    }

    /**
     * 付费渗透率列表处理
     *
     * @param array $payPermeationList
     * @param array $options
     *
     * @return \Closure
     */
    protected function payPermeationCalculators(array $payPermeationList = [], array $options = []): \Closure
    {
        $groups = $options['groups'] ?? [];

        return function (&$target, $k) use ($payPermeationList, $groups) {
            if (!isset($payPermeationList[$k])) {
                $target['pay_permeation_percent'] = '0.00%';
                return;
            }

            IndicatorCalcHelpers::payPermeation($target, $payPermeationList[$k]);
        };
    }

    /**
     * 广告通用综合指标（注意执行指标的依赖执行顺序）
     *
     * @return \Closure
     */
    protected function advertiserGeneralCalculators(): \Closure
    {
        return function (&$target) {
            IndicatorCalcHelpers::firstOnlineTime($target);
            IndicatorCalcHelpers::onlineTime($target);
            IndicatorCalcHelpers::newUserCost($target);
            IndicatorCalcHelpers::cpc($target);
            IndicatorCalcHelpers::clickShowPercent($target);
            IndicatorCalcHelpers::qianCost($target);
            IndicatorCalcHelpers::calcLpClickPercent($target);
            IndicatorCalcHelpers::convertCost($target);
            IndicatorCalcHelpers::convertPercent($target);
            IndicatorCalcHelpers::downloadStartCostPercent($target);
            IndicatorCalcHelpers::downloadFinishPercent($target);
            IndicatorCalcHelpers::installFinishNum($target);
            IndicatorCalcHelpers::installFinishPercent($target);
            IndicatorCalcHelpers::activateCost($target);
            IndicatorCalcHelpers::activatePercent($target);
            IndicatorCalcHelpers::activateInstallPercent($target);
            IndicatorCalcHelpers::registerCost($target);
            IndicatorCalcHelpers::registerPercent($target);
            IndicatorCalcHelpers::newUserRealPercent($target);
            IndicatorCalcHelpers::payUserPercent($target);
            IndicatorCalcHelpers::remain1Cost($target);
            IndicatorCalcHelpers::arpuNewUser($target);
            IndicatorCalcHelpers::payPenetration($target);
            IndicatorCalcHelpers::createRolePercent($target);
            IndicatorCalcHelpers::createRoleCost($target);
            IndicatorCalcHelpers::payFrequencyAvg7days($target);
            IndicatorCalcHelpers::payFrequency7daysCost($target);
            IndicatorCalcHelpers::newUserPaymentCost($target);
            IndicatorCalcHelpers::downloadStartPercent($target);
            IndicatorCalcHelpers::arppuNewUser($target);
            IndicatorCalcHelpers::playDuration3SPercent($target);
            IndicatorCalcHelpers::playTimeAvg($target);
            IndicatorCalcHelpers::showConvertPercent($target);
        };
    }

    //获取广告信息
    protected function getAdInfo($adInfo,$dimension = "plan_id"): \Closure
    {
        return function (&$target, $k) use ($adInfo,$dimension) {
               $fields = [];
               switch($dimension){
                   case "campaign_id":
                       $fields  = ["CAMPAIGN_NAME"];
                       break;
                   case "plan_id":
                       $fields  = ["CAMPAIGN_NAME","PLAN_NAME"];
                       break;
                   case "creative_id":
                       $fields  = ["CAMPAIGN_NAME","PLAN_NAME","PLAN_ID","CREATIVE_NAME"];
                       break;
               }
               foreach ($fields as $field){
                   $key = $target[$dimension];
                   if($dimension == "creative_id") {
                       $key = $target["plan_id"]."_".$target["creative_id"];
                   }
                   $target[strtolower($field)] = $adInfo[$key][$field]??null;
               }
        };
    }


    /**
     * 返回相隔最大的N节点
     *
     * @param      $tDate
     * @param      $maxDays
     * @param bool $isHasGroup
     *
     * @return int|mixed
     * @throws \Exception
     */
    protected function getDaysDiff($tDate, $maxDays, bool $isHasGroup = false)
    {
        if (!$isHasGroup) return (int)$maxDays;

        $today = new \DateTime();

        return min(days_apart($today, $tDate), $maxDays);
    }

    /**
     * ltv计算
     *
     * @param      $target
     * @param      $list
     * @param bool $hasNUser
     *
     * @return void
     */
    protected function ltvCalc(&$target, $list, bool $hasNUser = false)
    {
        if ($hasNUser) {
            $getUser = fn($data, $n) => $data['new_user_n'][$n] ?? 0;
        }
        else {
            $getUser = fn($data) => $data['new_user'] ?? 0;
        }

        foreach ($list as $i => $foo) {
            $target['ltv_' . $i] = IndicatorsHelpers::division($foo['money_all'], $getUser($target, $i));
        }
    }

    /**
     * 留存or付费留存计算
     *
     * @param        $target
     * @param        $list
     * @param bool   $hasNUser
     * @param string $keyPrefix
     *
     * @return void
     */
    protected function remainCalc(&$target, $list, bool $hasNUser = false, string $keyPrefix = 'remain')
    {
        if ($hasNUser) {
            $getUser = fn($data, $n) => ($keyPrefix == "pay_remain" ? $data["pay_new_user_7days_n"][$n] : $data['new_user_n'][$n]) ?? 0;
        }
        else {
            $getUser = fn($data) => ($keyPrefix == "pay_remain" ? $data["pay_new_user_7days"] : $data['new_user']) ?? 0;
        }

        foreach ($list as $i => $foo) {
            if ($i === 1) {
                $target['source_' . $keyPrefix . '_' . $i] = $foo['login_num'];
            }
            $target[$keyPrefix . '_' . $i] = IndicatorsHelpers::division(
                $foo['login_num'], $getUser($target, $i), 2, true
            );
        }
    }

    protected function payPermeationCalc()
    {

    }

    /**
     * roi 计算
     *
     * @param      $target
     * @param      $list
     * @param bool $hasN
     *
     * @return void
     */
    protected function roiCalc(&$target, $list, bool $hasN = false)
    {
        if ($hasN) {
            $getCost = fn($data, $n) => $data['cost_discount_n'][$n] ?? 0.00;
        }
        else {
            $getCost = fn($data) => $data['cost_discount'] ?? 0.00;
        }

        foreach ($list as $i => $foo) {
            $target['roi_' . $i] = IndicatorsHelpers::division(
                $foo['money_all'], $getCost($target, $i), 2, true
            );
        }
    }

    /**
     * @param $target
     * @param $info
     *
     * @return void
     */
    protected function passLevelCalc(&$target, $info)
    {
        for ($i = 1; $i <= 4; $i++) {
            if (empty($info['pass_level_' . $i]) || empty($info['pass_der_' . $i])) {
                $target['pass_level_' . $i] = '0.00%';
            }
            else {
                $target['pass_level_' . $i] = IndicatorsHelpers::division(
                    $info['pass_level_' . $i], $info['pass_der_' . $i], 2, true
                );
            }
        }
    }

    /**
     * @param        $target
     * @param int    $n
     * @param        $default
     * @param string $keyPrefix
     *
     * @return void
     */
    protected function fillNodeNData(&$target, int $n, $default, string $keyPrefix = '')
    {
        for ($i = 1; $i <= $n; $i++) {
            $key = $keyPrefix . '_' . $i;

            if (isset($target[$key])) continue;

            $target[$key] = $default;
        }
    }

    /**
     * 汇总总数
     *
     * @param          $data
     * @param string[] $columns
     *
     * @return array
     */
    protected function summaryRows($data, array $columns = []): array
    {
        $result = array_fill_keys($columns, 0);

        foreach ($data as $item) {
            foreach ($columns as $column) {
                $result[$column] += ($item[$column] ?? 0);
            }
        }

        return $result;
    }

    protected function summaryByDayType($info): array
    {
        $result = [];

        foreach ($info as $foo) {
            if (!isset($foo['nodes'])) continue;

            $dayType = $foo['nodes'];

            foreach ($dayType as $i => $item) {

                foreach ($item as $k => $chill) {
                    if (!isset($result[$i][$k])) {
                        Arr::set($result, implode('.', [$i, $k]), (int)$chill);
                    }
                    else {
                        $result[$i][$k] += (int)$chill;
                    }
                }
            }
        }

        return $result;
    }

    /**
     * @return \Closure
     */
    protected function mergePassLevel(): \Closure
    {
        return function (&$target, $source) {
            for ($i = 1; $i <= 4; $i++) {
                if (!isset($target['pass_level_' . $i])) {
                    $target['pass_level_' . $i] = 0;
                }

                if (!isset($target['pass_der_' . $i])) {
                    $target['pass_der_' . $i] = 0;
                }

                $target['pass_level_' . $i] += ($source['pass_level_' . $i] ?? 0);
                $target['pass_der_' . $i]   += ($source['pass_der_' . $i] ?? 0);
            }
        };
    }

    /**
     * @return \Closure
     */
    protected function mergePayPer(): \Closure
    {
        return function (&$target, $source) {
            if (!isset($target['pay_per_user'])) {
                $target['pay_per_user'] = $source['pay_per_user'] ?? 0;
            }
            else {
                $target['pay_per_user'] += ($source['pay_per_user'] ?? 0);
            }
        };
    }


    /**
     * 数据加工链
     *
     * @param       $target
     * @param array $groups
     * @param array $lines
     *
     * @return void
     */
    protected function processingLine(&$target, array $groups, array $lines = [])
    {
        $indexUnion = array_fill_keys($groups, 0);

        foreach ($target as &$item) {
            $unionKey = IndicatorsHelpers::flattenUnionKey($item, $indexUnion);

            foreach ($lines as $callback) {
                if ($callback instanceof \Closure) {
                    $callback($item, $unionKey);
                    continue;
                }

                if (is_callable($callback)) {
                    call_user_func_array($callback, [&$item, $unionKey]);
                }
            }
        }
    }

    /**
     * 固定必须有字段
     *
     * @return \Closure
     */
    protected function fillDefaultData(int $type): \Closure
    {
        switch ($type) {
            case 1:
                $default = $this->creativeDefaultEntity();
                break;
            case 2:
                $default = $this->planDefaultEntity();
                break;
            case 3:
                $default = $this->campaignDefaultEntity();
                break;
            default:
                $default = [];
        }

        return function (&$target, $k) use ($default) {
            $target = array_merge($default, $target);
        };
    }

    /**
     * @return array
     */
    protected function creativeDefaultEntity(): array
    {
        return [
            'tday'               => '-', 'cp_game_name' => '-',
            'cp_game_id'         => '-', 'game_name' => '-',
            'game_id'            => '-', 'app_show_name' => '-',
            'app_show_id'        => '-', 'channel_main' => '-',
            'channel_main_id'    => '-', 'channel' => '-',
            'channel_id'         => '-', 'platform_id' => '-',
            'package_id'         => '-', 'creative_id' => '-',
            'popularize_v1_id'   => '-', 'department_id' => '-',
            'plan_id'            => '-', 'plan_name' => '-',
            'user_id'            => '-', 'ad_account' => '-',
            'ad_account_id'      => '-', 'campaign_name' => '-',
            'campaign_id'        => '-', 'cost' => 0,
            'cost_discount'      => 0, 'new_user' => 0,
            'create_role_new'    => 0, 'pay_user_new' => 0,
            'pay_user'           => 0, 'pay_money' => 0,
            'pay_count'          => 0, 'pay_money_new' => 0,
            'pay_count_new'      => 0, 'show' => 0,
            'click'              => 0, 'download' => 0,
            'activate'           => 0, 'convert' => 0,
            'install'            => 0, 'lp_view' => 0,
            'lp_download'        => 0, 'download_start' => 0,
            'register'           => 0, 'new_real_user' => 0,
            'new_user_emulator'  => 0, 'activate_device' => 0,
            'pay_new_user_7days' => 0, 'pay_frequency_7days' => 0,
            'online_time'        => 0, 'first_online_time' => 0,
        ];
    }

    /**
     * @return array
     */
    protected function planDefaultEntity(): array
    {
        return [
            'tday'               => '-', 'cp_game_name' => '-',
            'cp_game_id'         => '-', 'game_name' => '-',
            'game_id'            => '-', 'app_show_name' => '-',
            'app_show_id'        => '-', 'channel_main' => '-',
            'channel_main_id'    => '-', 'channel' => '-',
            'channel_id'         => '-', 'platform_id' => '-',
            'package_id'         => '-',
            'popularize_v1_id'   => '-', 'department_id' => '-',
            'user_id'            => '-', 'ad_account' => '-',
            'ad_account_id'      => '-', 'campaign_name' => '-',
            'campaign_id'        => '-', 'cost' => 0,
            'cost_discount'      => 0, 'new_user' => 0,
            'create_role_new'    => 0, 'pay_user_new' => 0,
            'pay_user'           => 0, 'pay_money' => 0,
            'pay_count'          => 0, 'pay_money_new' => 0,
            'pay_count_new'      => 0, 'show' => 0,
            'click'              => 0, 'download' => 0,
            'activate'           => 0, 'convert' => 0,
            'install'            => 0, 'lp_view' => 0,
            'lp_download'        => 0, 'download_start' => 0,
            'register'           => 0, 'new_real_user' => 0,
            'new_user_emulator'  => 0, 'activate_device' => 0,
            'pay_new_user_7days' => 0, 'pay_frequency_7days' => 0,
            'online_time'        => 0, 'first_online_time' => 0,
        ];
    }

    /**
     * @return array
     */
    protected function campaignDefaultEntity(): array
    {
        return [
            'tday'               => '-', 'cp_game_name' => '-',
            'cp_game_id'         => '-', 'game_name' => '-',
            'game_id'            => '-', 'app_show_name' => '-',
            'app_show_id'        => '-', 'channel_main' => '-',
            'channel_main_id'    => '-', 'channel' => '-',
            'channel_id'         => '-', 'platform_id' => '-',
            'package_id'         => '-',
            'popularize_v1_id'   => '-', 'department_id' => '-',
            'user_id'            => '-', 'ad_account' => '-',
            'ad_account_id'      => '-', 'campaign_name' => '-',
            'campaign_id'        => '-', 'cost' => 0,
            'cost_discount'      => 0, 'new_user' => 0,
            'create_role_new'    => 0, 'pay_user_new' => 0,
            'pay_user'           => 0, 'pay_money' => 0,
            'pay_count'          => 0, 'pay_money_new' => 0,
            'pay_count_new'      => 0, 'show' => 0,
            'click'              => 0, 'download' => 0,
            'activate'           => 0, 'convert' => 0,
            'install'            => 0, 'lp_view' => 0,
            'lp_download'        => 0, 'download_start' => 0,
            'register'           => 0, 'new_real_user' => 0,
            'new_user_emulator'  => 0, 'activate_device' => 0,
            'pay_new_user_7days' => 0, 'pay_frequency_7days' => 0,
            'online_time'        => 0, 'first_online_time' => 0,
        ];
    }
}
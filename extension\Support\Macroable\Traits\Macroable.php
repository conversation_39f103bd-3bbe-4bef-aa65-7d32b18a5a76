<?php

namespace app\extension\Support\Macroable\Traits;

trait Macroable
{
    public static array $macros = [];

    public static function macro($name, $macro)
    {
        static::$macros[$name] = $macro;
    }

    /**
     * @param      $mixin
     * @param bool $replace
     * @return void
     * @throws \ReflectionException
     */
    public static function mixin($mixin, bool $replace = true)
    {
        $methods = (new \ReflectionClass($mixin))->getMethods(
            \ReflectionMethod::IS_PUBLIC | \ReflectionMethod::IS_PROTECTED
        );

        foreach ($methods as $method) {
            if ($replace || !static::hasMacro($method->name)) {
                $method->setAccessible(true);
                static::macro($method->name, $method->invoke($mixin));
            }
        }
    }

    /**
     * @param $name
     * @return bool
     */
    public static function hasMacro($name): bool
    {
        return isset(static::$macros[$name]);
    }

    /**
     *
     * @return void
     */
    public static function flushMacros()
    {
        static::$macros = [];
    }

    /**
     * @param $method
     * @param $parameters
     * @return mixed
     */
    public static function __callStatic($method, $parameters)
    {
        if (!static::hasMacro($method)) {
            throw new \BadMethodCallException(sprintf(
                'Method %s::%s does not exist.', static::class, $method
            ));
        }

        $marco = static::$macros[$method];

        if ($marco instanceof \Closure) {
            $marco = $marco->bindTo(null, static::class);
        }

        return $marco(...$parameters);
    }

    /**
     * @param $method
     * @param $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        if (!static::hasMacro($method)) {
            throw new \BadMethodCallException(sprintf(
                'Method %s::%s does not exist.', static::class, $method
            ));
        }

        $marco = static::$macros[$method];

        if ($marco instanceof \Closure) {
            $marco = $marco->bindTo(null, static::class);
        }

        return $marco(...$parameters);

    }

}
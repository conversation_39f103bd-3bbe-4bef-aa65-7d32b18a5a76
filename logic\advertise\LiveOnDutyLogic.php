<?php

namespace app\logic\advertise;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\Zakia;
use app\logic\advertise\Exceptions\EmptyDataException;
use app\service\AdvertiseLive\LiveAccountServ;
use app\service\AdvertiseLive\LiveAnchorServ;
use app\service\AdvertiseLive\LiveAssistantServ;
use app\service\AdvertiseLive\LiveOnDutyServ;
use app\service\AdvertiseLive\OperationalAccountServ;
use app\service\AdvertiserData\CostManagerServ;
use PhpOffice\PhpSpreadsheet\IOFactory;

class LiveOnDutyLogic
{
    /**
     * @param string $file
     *
     * @return mixed
     * @throws EmptyDataException
     * @throws \Throwable
     */
    public function importData(string $file)
    {
        $titleKeyMap = array_flip([
            'start_time'        => '开始时间',
            'end_time'          => '结束时间',
            'account'           => '账号ID',
            'anchor'            => '主播',
            'package_id'        => '包号',
            'assistant'         => '运营助手',
            'operation_account' => '运营账号',
        ]);

        $fixedTitle = ['开始时间', '结束时间', '账号ID', '主播', '包号', '运营助手', '运营账号', '主播成本', '小手柄成本', '福袋成本',];

        $againstFormats = [
            IOFactory::WRITER_CSV,
            IOFactory::WRITER_XLSX,
            IOFactory::WRITER_XLS,
        ];

        $reader    = IOFactory::load($file, 0, $againstFormats);
        $tableData = $reader->getActiveSheet()->toArray();

        if (empty($tableData)) {
            throw new EmptyDataException('未成功读取文件数据, 请稍后再试');
        }

        [
            'live_account'      => $liveAccountMap,
            'live_assistant'    => $assistantMap,
            'operation_account' => $operationAccountMap,
            'anchor'            => $anchorMap,
        ] = $this->getInfoMap();

        $assistantMap        = array_flip($assistantMap);
        $operationAccountMap = array_flip($operationAccountMap);
        $anchorMap           = array_flip($anchorMap);

        // 表头
        $tableTitle = array_map(fn($d) => \trim($d), Arr::pull($tableData, 0, []));
        $title      = array_reverse(array_filter(array_reverse($tableTitle)));

        if ($title != $fixedTitle) {
            throw new \InvalidArgumentException("表头需要规范");
        }

        $operator     = \Plus::$service->admin->getUserId();
        $dataOnDuty   = [];
        $dataLiveCost = [];
        $fieldLength  = count($tableTitle);

        foreach ($tableData as &$item) {
            $item = array_splice($item, 0, $fieldLength);
            $item = array_combine($tableTitle, array_map(fn($d) => \trim($d), $item));

            if (!$item) throw new \RuntimeException('表格数据解析错误');
        }
        unset($item);

        // 解析直播排班数据格式
        foreach ($tableData as $foo) {
            $chill = array_flip($foo);
            foreach ($chill as &$key) {
                if (isset($titleKeyMap[$key])) {
                    $key = $titleKeyMap[$key];
                }
                else {
                    $key = null;
                }
            }
            unset($key);
            $chill = array_filter($chill);
            $chill = array_flip($chill);

            if (
                !$this->checkRequire($chill, 'start_time', 'end_time', 'account', 'anchor', 'package_id')
            ) {
                throw new \RuntimeException('直播排班缺少参数, 请检查');
            }

            $chill['start_time'] = Zakia::convertChinesePunctuationToEnglish($chill['start_time']);
            $chill['end_time']   = Zakia::convertChinesePunctuationToEnglish($chill['end_time']);

            try {
                $startTime = new \DateTime($chill['start_time']);
                $endTime   = new \DateTime($chill['end_time']);
            }
            catch (\Exception $e) {
                throw new \RuntimeException("非标准时间格式");
            }

            if ($startTime > $endTime) {
                throw new \InvalidArgumentException("开始时间大于结束时间, 请检查");
            }

            $chill['start_time']  = $startTime->format('Y-m-d H:i:s');
            $chill['end_time']    = $endTime->format('Y-m-d H:i:s');
            $chill['operator_id'] = $operator;
            $liveAccount          = $chill['account'];
            $anchor               = $chill['anchor'];
            $assistant            = $chill['assistant'] ?? '';
            $operationAccount     = $chill['operation_account'] ?? '';

            // 判断是否存在该直播账号
            if (!isset($liveAccountMap[$liveAccount])) {
                throw new \InvalidArgumentException('该账号ID未创建:' . $liveAccount);
            }
            else {
                $chill['live_account_id'] = $liveAccount;
                unset($chill['account']);
            }
            // 判断该主播是否存在
            if (!isset($anchorMap[$anchor])) {
                throw new \InvalidArgumentException('该主播未创建:' . $anchor);
            }
            else {
                $chill['anchor_id'] = $anchorMap[$anchor];
                unset($chill['anchor']);
            }

            if (!empty($assistant)) {
                if (isset($assistantMap[$assistant])) {
                    $chill['assistant_id'] = $assistantMap[$assistant];
                }
            }
            unset($chill['assistant']);

            if (!empty($operationAccount)) {
                if (isset($operationAccountMap[$operationAccount])) {
                    $chill['operation_account_id'] = $operationAccountMap[$operationAccount];
                }
            }
            unset($chill['operation_account']);

            $dataOnDuty[] = $chill;
        }

        $liveCostServ  = new CostManagerServ();
        $liveCostIndex = array_flip(['开始时间', '结束时间', '账号ID', '包号', '主播成本', '小手柄成本', '福袋成本']);
        $costTypeMap   = $liveCostServ->getCostTypeMap();

        foreach ($tableData as $chill) {
            $chill['开始时间'] = Zakia::convertChinesePunctuationToEnglish($chill['开始时间'] ?? '');
            $chill['结束时间'] = Zakia::convertChinesePunctuationToEnglish($chill['结束时间'] ?? '');
            $chill             = array_intersect_key($chill, $liveCostIndex);
            $chill['账号']     = $liveAccountMap[$chill['账号ID'] ?? '']['account'] ?? '';

            foreach ($costTypeMap as $i => $costType) {
                $t = array_intersect_key($chill, ['开始时间' => '', '结束时间' => '', '账号' => '', '账号ID' => '', '包号' => '']);

                if (!empty($chill[$costType])) {
                    $t['成本类型']       = $costType;
                    $t['消费金额']       = $chill[$costType];
                    $t['返点后消耗金额'] = $chill[$costType];
                }

                $dataLiveCost[] = $t;
            }
        }

        // 直播排班写入
        $resultOnDuty     = (new LiveOnDutyServ())->multiInsert($dataOnDuty);
        $resultOnLiveCost = $liveCostServ->saveCostInfoForLive($dataLiveCost);


        return true;
    }

    /**
     * @param $data
     * @param ...$required
     *
     * @return bool
     */
    private function checkRequire($data, ...$required): bool
    {
        foreach ($required as $item) {
            if (!isset($data[$item])) {
                return false;
            }
        }

        return true;
    }

    /**
     * @return array
     */
    private function getInfoMap(): array
    {
        $liveAccountInfo     = (new LiveAccountServ())->getList([]);
        $liveAccountMap      = array_column($liveAccountInfo, null, 'account_id');
        $liveAssistant       = (new LiveAssistantServ())->getList([]);
        $assistantMap        = array_column($liveAssistant, 'assistant_name', 'id');
        $operationalAccount  = (new OperationalAccountServ())->getList([]);
        $operationAccountMap = array_column($operationalAccount, 'account_name', 'id');
        $anchorInfo          = (new LiveAnchorServ())->getList([]);
        $anchorMap           = array_column($anchorInfo, 'anchor_name', 'id');

        return [
            'live_account'      => $liveAccountMap,
            'live_assistant'    => $assistantMap,
            'operation_account' => $operationAccountMap,
            'anchor'            => $anchorMap,
        ];
    }

}
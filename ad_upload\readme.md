
## 数据上报调整

需要api 上报的渠道：

业务中台：配置中心/渠道媒体配置/出包子渠道配置

查看标签：里面有 `数据上报配置（379）` 这个标签

不需要再上报的渠道：
- 18 神马搜索
- 63 网易新闻消息流
- 67 头条搜索(旧)
- 69 广点通新
- 71 创业工场
- 1002 头条SDK
- 1013 广点通(旧)
- 1109 数因平台
- 1111 Sigmob

更多不再上报的渠道见：https://doc.weixin.qq.com/sheet/e3_AAoAQgY3ABMV02FU7kcTYu1N522tk?scode=AP0ADgeoAAkykj5hhQAAoAQgY3ABM&tab=BB08J2

## 目录结构说明

```shell
│  AdUpload.php # 代码入口
│  WatchProcess.php # 监控进程
├─channels # 具体的渠道上报代码
├─contract # 契约
│      AdBaseInterface.php # 渠道上报 契约
│      AdUploadStrategyInterface.php # 上报策略 契约
│      DeductionStrategyInterface.php # 扣量上报策略 契约
├─deduction # 扣量上报策略
├─migrate  # 上线前数据处理
├─services # 服务
│      AdUploadService.php # 上报媒体服务
│      DeductionService.php # 扣量上报过滤服务
├─strategies # 上报策略
│      ActiveUploadStrategy.php     # 激活
│      CreateRoleUploadStrategy.php # 创角
│      LoginUploadStrategy.php      # 登录
│      PayUploadStrategy.php        # 支付
│      PayVirtualUploadStrategy.php # 增量虚拟支付
│      RegisterUploadStrategy.php   # 注册
│      RemainUploadStrategy.php     # 留存
├─test # 本地测试
└─tool # 工具、算法函数

```

## 上报处理流程

术语定义：
- 上报点 `last_action_id`，各个事件上报记录的id, 程序每次运行，获取id 大于 上报点的数据来上报

流程图：
```mermaid
graph TD
    A[开始] --> B[初始化上次上报点]
    B --> C[根据上报点获取数据]
    C --> D{数据是否为空?}
    D -- 是 --> E[设置最大id为上报点]
    D -- 否 --> F[过滤数据]
    F --> G[处理数据]
    G --> H[上报数据]
    H --> I[设置上报点]
    I --> J[记录未匹配数据]
    J --> K[结束]
```
代码：
`\app\ad_upload\services\AdUploadService::upload`

```php
/**
     * 上报处理
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @param string $action       上报动作
     * @param int    $channelId    渠道id
     * @return void
     */
    public function upload($uploadConfig, $packages, $action, $channelId): void
    {
        $beginTime  = microtime(true);
        \Plus::$uid = $action . uniqid();
        // 初始化上次上报点
        $this->strategy->initUploadLast();
        // 获取数据
        $data = $this->strategy->getData($uploadConfig, $packages);
        echo "1.$action 获取数据条数：" . count($data) . "\n";
        $this->log('获取到数据', $data, $channelId, $action);
        // 获取数据为空，设置上报点
        if(empty($data)){
            // 无数据，设置上报点
            $this->strategy->setMaxLastId($data);
            return;
        }
        // 过滤数据
        $data = $this->strategy->filterData($data, $uploadConfig);
        echo "2. $action 过滤数据后条数：" . count($data) . "\n";
        $this->log('过滤后数据', $data, $channelId, $action);
        // 处理数据
        $processedData = $this->strategy->processData($data, $uploadConfig);
        echo "3. $action 处理数据后条数：" . count($processedData) . "\n";
        $this->log('处理后数据', $processedData, $channelId, $action);
        // 上报数据
        $uploadMethod = 'upload' . StringUtil::convertToCamelHump($action, '_');
        foreach ($processedData as $item) {
            //逐条上报
            $this->strategy->uploadData($item, $uploadMethod);
            //设置上报点
            $this->strategy->setLastId($item);
        }
        //记录未匹配数据
        $this->strategy->setUnmatchedIds();
        // 记录上报耗时
        $endTime = microtime(true);
        \Plus::$app->log->info($endTime - $beginTime, [], 'ad_upload_time');
    }

```


## 数据表
- 激活active：tb_sdk_active_log
- 注册register：tb_sdk_user_newlogin_package
- 登录login: tb_sdk_user_login
- 创角create_role: tb_sdk_user_login
- 充值pay：tb_sdk_user_payment
- 虚拟增量支付pay_virtual_upload：ddc_platform.dwd_user_payment_upload_virtual
- 留存remain：tb_sdk_user_login、tb_sdk_active_log

## 本地测试流程

代码上已经实现，本地运行，读取正式环境的业务数据，上报写死返回内容，不会真正请求。运行日志写入测试环境doris

1. 复制表数据 `dataspy.tb_ad_upload_log` 、`tb_ad_data_upload_conf` 到 `192.168.100.60`
2. 查看 `bigdata_dwd.dwd_reported_platform_log` 要测试媒体的 action_id， 根据时间筛选 
3. 修改 tb_ad_upload_log 对应渠道的 last_action_id。（action=pay 时， last_action_id=时间戳秒)

执行：
`php cli.php  -f  ad_upload/AdUpload.php -s debug --p 'channel_id=1113 actions=login'`
指定时间段
```shell
php cli.php -f ad_upload/AdUpload.php -s debug --p 'channel_id=1 actions=pay begin_time="2024-12-25 00:00:00" end_time="2024-12-25 23:59:59"'
```

## 本地自动测试
`ad_upload/test` 中有各个事件本地跑测试的脚步, 自动完成上面的操作

```shell
php cli.php -f ad_upload/test/PayTest.php -s debug --p 'channel_id=1113 days=1'
```


## 上线流程

1. 代码配置 `ad_upload/WatchProcess.php` 配置需要上报的渠道、类型
2. `dataspy.910app.com` 后台 停止相关进程
3. 服务器 159.75.114.2 手动kill 相关进程
4. ***pay*** 付费上报，要先运行数据调整  `php cli.php  -f ad_upload/migrate/PayLog.php -s debug --p 'channel_id={渠道id}'`
5. 发布代码
6. 服务器 159.75.114.2 增加定时任务，每3分钟运行一次 `ad_upload/WatchProcess.php`
7. 服务器 159.75.114.2 增加定时任务，每30分钟运行一次 `ad_upload/Monitor.php`, 检查上报数据量少了就告警，并自动重跑半小时内的数据
8. `ad_upload/Monitor.php`： 渠道切换半小时后才能在这里加监控，不然会造成重复上报
9. 未匹配订单号字段数据中, 移除其他渠道的订单号 `php cli.php -f ad_upload/migrate/UnmatchedOrderId.php -s debug --p 'channel_id={渠道id}'`

> channel_id、click_id 等归因字段可能会延迟更新，所以每3分钟运行一次

## 回滚旧上报流程

1. 要先运行数据调整 `php cli.php -f ad_upload/migrate/PayBack.php -s debug -p 'channel_id={渠道id}'`
2. `ad_upload/WatchProcess.php` 删除相关渠道
3. dataspy 后台启用相关进程

## 新渠道接入

1. `ad_upload\channels` 目录中增加上报代码
2. `dataspy.tb_ad_upload_log` 增加渠道配置
3. 代码配置 `ad_upload/WatchProcess.php` 配置需要上报的渠道、类型
4. 代码配置 `ad_upload/Monitor.php` 配置监控渠道

## 数据重跑

指定时间段

```shell
php cli.php -f ad_upload/AdUpload.php -s debug --p 'channel_id=1 actions=pay begin_time="2024-12-25 00:00:00" end_time="2024-12-25 23:59:59" force=0'
```

> 重要：`force` 参数默认是`0`，即使重跑，也会过滤已上报的数据，`force=1` 则强制忽略已上报数据
> 除非是网络异常、媒体接口异常，否则不建议使用 `force=1`

## 订单同步到日志
同步订单 到  支付上报日志

每分钟运行一次，同步前30分钟的数据
```bash
php cli.php -f ad_upload/SyncOrder.php -s debug
```
取代：doris 付费上报日志 http://cdh-slave04:12345/dolphinscheduler/ui/#/projects/definition/list/429
<?php

namespace app\apps\operator\Traits;

use app\apps\operator\Helpers\ConstFirstLogin;
use app\extension\Support\Collections\Arr;
use app\service\OperationData\FirstLoginIndex;

trait FirstLoginTrait
{
    /**
     * 追加按天查询每天的特定数据,
     * 用于时间段汇总时排除未到天数的数据
     *
     * @param $params
     * @param $groups
     *
     * @return \Closure
     */
    protected function addInfoInGroupDate($params, $groups): \Closure
    {
        $dateDimension = (int)Arr::get($params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        if (
            ConstFirstLogin::DIMENSION_WEEK === $dateDimension
            && in_array('tday', $groups)
        ) {
            $getRangeTime = function ($target) {
                $rangeDate = Arr::get($target, 'tday');
                if (empty($rangeDate)) return null;

                $rangeDate = \explode('/', $rangeDate);
                sort($rangeDate);

                return $rangeDate;
            };
        }

        if (
            ConstFirstLogin::DIMENSION_MONTH === $dateDimension
            && in_array('tday', $groups)
        ) {
            $getRangeTime = function ($target) {
                $rowMonth       = Arr::get($target, 'tday');
                $rangeDateStart = $rowMonth . '-01';
                $rangeDateEnd   =
                    (new \DateTime($rangeDateStart))
                        ->add(new \DateInterval('P1M'))
                        ->sub(new \DateInterval('P1D'))
                        ->format('Y-m-d');

                return [$rangeDateStart, $rangeDateEnd];
            };
        }

        if (!isset($getRangeTime)) {
            $getRangeTime = function () use ($params) {
                return [
                    Arr::get($params, 'range_date_start'),
                    Arr::get($params, 'range_date_end'),
                ];
            };
        }

        $baseParams  = $params;
        $sourceGroup = $groups;
        unset($baseParams['range_date_start'], $baseParams['range_date_end'], $groups['tday']);
        $groupIndex = array_fill_keys($groups, 0);

        return function (&$target, $k) use ($getRangeTime, $baseParams, $groupIndex, $sourceGroup) {
            $today = new \DateTime();

            $columns = [
                'tday'            => ['source' => 't_base'],
                'cost_discount'   => ['source' => 't_base', 'aggregate' => 'sum'],
                'cost'            => ['source' => 't_base', 'aggregate' => 'sum'],
                'firstlogin_user' => ['source' => 't_base', 'aggregate' => 'sum'],
                'app_show_id'     => ['source' => 'POWER'],
                'channel_id'      => ['source' => 'POWER'],
                'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
                'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
                'platform_id'     => ['source' => 'POWER'],
                'channel_main_id' => ['source' => 'POWER'],
                'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            ];

            [
                $rangeDateStart,
                $rangeDateEnd,
            ] = $getRangeTime($target);

            // 组合改行的特定的查询数据
            $rowParams = array_merge($groupIndex, array_intersect_key($target, $groupIndex));
            $options   = array_merge(
                $baseParams,
                $rowParams,
                ['range_date_start' => $rangeDateStart, 'range_date_end' => $rangeDateEnd,]
            );

            $result = (new FirstLoginIndex())->getInfoByFixedDate($options, $sourceGroup, $columns);

            $addInfo = ['cost_discount_n' => [], 'firstlogin_user_n' => []];

            foreach ($result as $foo) {
                $dayDiff = days_apart($foo['tday'], $today);
                foreach (['cost_discount', 'firstlogin_user'] as $k) {
                    $indexKey = $k . '_n';

                    if (!isset($addInfo[$indexKey])) {
                        $addInfo[$indexKey] = [];
                    }

                    $child = &$addInfo[$indexKey];

                    for ($i = 1; $i <= $dayDiff; $i++) {
                        if (!isset($child[$i])) {
                            Arr::set($child, $i, $foo[$k]);
                        }
                        else {
                            $addInfo[$indexKey][$i] += $foo[$k];
                        }

                    }
                }
            }

            $target = array_merge($target, $addInfo);
        };

    }

    /**
     * 追加每日汇总数据
     *
     * @param $params
     * @param $groups
     *
     * @return array[]
     * @throws \Exception
     */
    protected function addSummaryInfoGroupDate($params, $groups): array
    {
        $today         = new \DateTime();
        $dateDimension = (int)Arr::get($params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        $columns = [
            'tday'            => ['source' => 't_base'],
            'cost_discount'   => ['source' => 't_base', 'aggregate' => 'sum'],
            'cost'            => ['source' => 't_base', 'aggregate' => 'sum'],
            'firstlogin_user' => ['source' => 't_base', 'aggregate' => 'sum'],
            'app_show_id'     => ['source' => 'POWER'],
            'channel_id'      => ['source' => 'POWER'],
            'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
            'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
            'platform_id'     => ['source' => 'POWER'],
            'channel_main_id' => ['source' => 'POWER'],
            'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
        ];

        $result = (new FirstLoginIndex())->getSummaryInfoByFixedDate($params, $groups, $columns);

        $addInfo = ['cost_discount_n' => [], 'firstlogin_user_n' => []];

        foreach ($result as $foo) {
            $dayDiff = days_apart($foo['tday'], $today);
            foreach (['cost_discount', 'firstlogin_user'] as $k) {
                $indexKey = $k . '_n';

                if (!isset($addInfo[$indexKey])) {
                    $addInfo[$indexKey] = [];
                }

                $child = &$addInfo[$indexKey];

                for ($i = 1; $i <= $dayDiff; $i++) {
                    if (!isset($child[$i])) {
                        Arr::set($child, $i, $foo[$k]);
                    }
                    else {
                        $addInfo[$indexKey][$i] += $foo[$k];
                    }

                }
            }
        }

        return $addInfo;
    }

    /**
     * @param $params
     * @param $groups
     *
     * @return array
     * @throws \Exception
     */
    protected function addSummaryInfoGroupDateByRemain($params, $groups): array
    {
        $today         = new \DateTime();
        $dateDimension = (int)Arr::get($params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        $columns = [
            'tday'            => ['source' => 't_base'],
            'cost_discount'   => ['source' => 't_base', 'aggregate' => 'sum'],
            'cost'            => ['source' => 't_base', 'aggregate' => 'sum'],
            'firstlogin_user' => ['source' => 't_base', 'aggregate' => 'sum'],
            'app_show_id'     => ['source' => 'POWER'],
            'channel_id'      => ['source' => 'POWER'],
            'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
            'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
            'platform_id'     => ['source' => 'POWER'],
            'channel_main_id' => ['source' => 'POWER'],
            'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
        ];

        $result = (new FirstLoginIndex())->getSummaryInfoByFixedDate($params, $groups, $columns);

        $addInfo = ['firstlogin_user_n' => []];

        foreach ($result as $foo) {
            $dayDiff = days_apart($foo['tday'], $today);
            foreach (['firstlogin_user'] as $k) {
                $indexKey = $k . '_n';

                if (!isset($addInfo[$indexKey])) {
                    $addInfo[$indexKey] = [];
                }

                $child = &$addInfo[$indexKey];

                for ($i = 1; $i < $dayDiff; $i++) {
                    if (!isset($child[$i])) {
                        Arr::set($child, $i, $foo[$k]);
                    }
                    else {
                        $addInfo[$indexKey][$i] += $foo[$k];
                    }

                }
            }
        }

        return $addInfo;
    }


    protected function addInfoInGroupDateByRemain($params, $groups): \Closure
    {
        $dateDimension = (int)Arr::get($params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        if (
            ConstFirstLogin::DIMENSION_WEEK === $dateDimension
            && in_array('tday', $groups)
        ) {
            $getRangeTime = function ($target) {
                $rangeDate = Arr::get($target, 'tday');
                if (empty($rangeDate)) return null;

                $rangeDate = \explode('/', $rangeDate);
                sort($rangeDate);

                return $rangeDate;
            };
        }

        if (
            ConstFirstLogin::DIMENSION_MONTH === $dateDimension
            && in_array('tday', $groups)
        ) {
            $getRangeTime = function ($target) {
                $rowMonth       = Arr::get($target, 'tday');
                $rangeDateStart = $rowMonth . '-01';
                $rangeDateEnd   =
                    (new \DateTime($rangeDateStart))
                        ->add(new \DateInterval('P1M'))
                        ->sub(new \DateInterval('P1D'))
                        ->format('Y-m-d');

                return [$rangeDateStart, $rangeDateEnd];
            };
        }

        if (!isset($getRangeTime)) {
            $getRangeTime = function () use ($params) {
                return [
                    Arr::get($params, 'range_date_start'),
                    Arr::get($params, 'range_date_end'),
                ];
            };
        }

        $baseParams  = $params;
        $sourceGroup = $groups;
        unset($baseParams['range_date_start'], $baseParams['range_date_end'], $groups['tday']);
        $groupIndex = array_fill_keys($groups, 0);

        return function (&$target, $k) use ($getRangeTime, $baseParams, $groupIndex, $sourceGroup) {
            $today = new \DateTime();

            $columns = [
                'tday'            => ['source' => 't_base'],
                'cost_discount'   => ['source' => 't_base', 'aggregate' => 'sum'],
                'cost'            => ['source' => 't_base', 'aggregate' => 'sum'],
                'firstlogin_user' => ['source' => 't_base', 'aggregate' => 'sum'],
                'app_show_id'     => ['source' => 'POWER'],
                'channel_id'      => ['source' => 'POWER'],
                'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
                'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
                'platform_id'     => ['source' => 'POWER'],
                'channel_main_id' => ['source' => 'POWER'],
                'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            ];

            [
                $rangeDateStart,
                $rangeDateEnd,
            ] = $getRangeTime($target);

            // 组合改行的特定的查询数据
            $rowParams = array_merge($groupIndex, array_intersect_key($target, $groupIndex));
            $options   = array_merge(
                $baseParams,
                $rowParams,
                ['range_date_start' => $rangeDateStart, 'range_date_end' => $rangeDateEnd,]
            );

            $result = (new FirstLoginIndex())->getInfoByFixedDate($options, $sourceGroup, $columns);

            $addInfo = ['cost_discount_n' => [], 'firstlogin_user_n' => []];

            foreach ($result as $foo) {
                $dayDiff = days_apart($foo['tday'], $today);
                foreach (['cost_discount', 'firstlogin_user'] as $k) {
                    $indexKey = $k . '_n';

                    if (!isset($addInfo[$indexKey])) {
                        $addInfo[$indexKey] = [];
                    }

                    $child = &$addInfo[$indexKey];

                    for ($i = 1; $i < $dayDiff; $i++) {
                        if (!isset($child[$i])) {
                            Arr::set($child, $i, $foo[$k]);
                        }
                        else {
                            $addInfo[$indexKey][$i] += $foo[$k];
                        }

                    }
                }
            }

            $target = array_merge($target, $addInfo);
        };

    }


}
<?php

namespace app\service\AdvertiserData\Components\Helpers;

class JoinClause
{
    /**
     * @var string
     */
    protected string $method;

    /**
     * @var string
     */
    protected string $table;

    /**
     * @var string
     */
    protected string $alias;

    /**
     * @var array
     */
    protected array $wheres;

    /**
     * @var bool
     */
    protected bool $isSub = false;

    public function __construct($type, $table, $alias)
    {
        $this->method = $type;
        $this->alias  = $alias;

        $this->table = $this->queryNested($table);
    }

    /**
     * @param string $table
     * @return string
     */
    protected function queryNested(string $table): string
    {
        if (stristr($table,'select' )) {
            $this->isSub = true;
        };

        return $table;
    }

    /**
     * @param        $first
     * @param        $operator
     * @param        $second
     * @param string $boolean
     * @return $this
     */
    public function on($first, $operator = null, $second = null, string $boolean = 'and'): JoinClause
    {
        $this->wheres[] = compact('first', 'operator', 'second', 'boolean');
        return $this;
    }

    /**
     * @param $first
     * @param $operator
     * @param $second
     * @return $this|JoinClause
     */
    protected function orOn($first, $operator = null, $second = null): JoinClause
    {
        return $this->on($first, $operator, $second, 'or');
    }

    public function isSubQuery(): bool
    {
        return $this->isSub;
    }

    /**
     * @return array|string|string[]|null
     */
    public function getCond()
    {
        if (empty($this->wheres)) return '';

        $wheres = collect($this->wheres)->map(function ($item) {
            return ($item['boolean'] ?? 'and') . ' ' . $item['first'] . ' ' . $item['operator'] . ' ' . $item['second'];
        })->all();

        return $this->removeLeadingBoolean(implode(' ', $wheres));
    }

    /**
     * @param bool $hasAs
     * @return string
     */
    public function getTable(bool $hasAs = true): string
    {
        return $hasAs
            ? ($this->table . ' as ' . $this->alias)
            : $this->table;
    }

    /**
     * @return string
     */
    public function getAlias(): string
    {
        return $this->alias;
    }

    /**
     * @return string
     */
    public function getMethod(): string
    {
        return $this->method;
    }


    /**
     * 去除最前面的and | or字符
     * @param $value
     * @return array|string|string[]|null
     */
    protected function removeLeadingBoolean($value)
    {
        return preg_replace('/and |or /i', '', $value, 1);
    }
}
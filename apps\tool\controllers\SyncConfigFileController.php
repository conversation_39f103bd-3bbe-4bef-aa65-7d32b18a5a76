<?php

namespace app\apps\tool\controllers;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\Zakia;
use app\logic\tool\SyncConfig\BusinessDataDict;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Plus\MVC\Controller\JsonController;

/**
 *
 * @description 配置文件同步
 * @date 2024/05/23
 *
 */
class SyncConfigFileController extends JsonController
{
    protected const CONFIG_FUNC_MAP = [
        'dict_business' => BusinessDataDict::class,
    ];

    /**
     * 上传CSV或EXCEL文件
     *
     * @return array
     * @throws \Exception
     */
    public function uploadExcelAction(): array
    {
        ini_set('memory_limit', '1024M');

        $request    = \Plus::$app->request;
        $configType = $request->getValue('config_type');
        $fileKey    = $request->getValue('target', 'file');
        $input      = $request->getFileItem($fileKey);

        if (empty($input)) {
            throw new \Exception('missing file');
        }

        $fileInfo = $input->save(CACHE_DIR . $input->getFilename());
        $filePath = $fileInfo->getPathname();

        if (empty($filePath)) {
            return $this->error('获取路径失败');
        }

        $sheetData = $this->explainExcel($filePath);

        if (!isset(self::CONFIG_FUNC_MAP[$configType])) {
            return $this->error('missing function');
        }

        $fn = self::CONFIG_FUNC_MAP[$configType];

        try {
            (new $fn())($sheetData);
        }
        catch (\Exception $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

    /**
     * @description 解析Excel文件表格数据
     *
     *
     * @param $filePath
     * @return array
     */
    private function explainExcel($filePath): array
    {
        $againstFormats = [
            IOFactory::WRITER_CSV,
            IOFactory::WRITER_XLSX,
            IOFactory::WRITER_XLS,
        ];

        $reader             = IOFactory::load($filePath, 0, $againstFormats);
        $workSheet          = $reader->getActiveSheet()->toArray();
        $title              = Arr::pull($workSheet, 0);
        $placeholderCounter = 1;
        $sheetData          = [];
        $title              = Zakia::removeTrailingEmptyValues($title);

        foreach ($title as &$foo) {
            $foo = \trim($foo);
            if ($foo === '') {
                $foo = "Column{$placeholderCounter}";
                $placeholderCounter++;
            }
        }
        unset($foo);
        $titleLen = count($title);

        foreach ($workSheet as $row) {
            $sheetData[] = array_combine($title, array_slice($row, 0, $titleLen));
        }

        return $sheetData;
    }


}
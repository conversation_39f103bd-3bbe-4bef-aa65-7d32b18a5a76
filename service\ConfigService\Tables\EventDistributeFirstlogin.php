<?php

namespace app\service\ConfigService\Tables;

use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class EventDistributeFirstlogin implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    /**
     * @param $options
     *
     * @return \app\extension\Support\Collections\Collection
     */
    public function getFields($options = null): \app\extension\Support\Collections\Collection
    {
        return (new EventDistributeAd())->getFields($options);
    }
}
<?php

namespace app\models\baseConfPlatform;

use Plus\MVC\Model\ActiveRecord;

/**
 * Table "biz_payway"
 *
 * @property int    $id                自增ID(CHANNEL_ID)
 * @property string $code              支付方式标识
 * @property string $name              支付方式命名
 * @property string $remarks            备注
 * @property int    $type              类型
 * @property int    $package_channel_main_id
 * @property string $operator          操作人
 * @property string $payway_type       支付方式
 * @property string $package_channel_main
 * @property string $add_time
 * @property string $update_time
 */
class BizPayway extends ActiveRecord
{
    public $_primaryKey = 'id';

    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->base_conf_platform_doris;
    }
}
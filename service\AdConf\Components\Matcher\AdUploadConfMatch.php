<?php

namespace app\service\AdConf\Components\Matcher;

use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\AdvertiserData\Components\Matcher\BaseMatch;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;


class AdUploadConfMatch extends BaseMatch
{
    protected function processLine(): array
    {
        $process = [
            [$this, 'matchChannelId'],
            [$this, 'matchStatus'],
            [$this, 'matchReportStatus'],
        ];

        return array_merge(parent::processLine(), $process);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchChannelId(&$qb, array &$params)
    {
        if (empty($params['channel_id'])) return;

        $data  = $params['channel_id'];
        $field = $this->getReflectField('channel_id');
        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchStatus(&$qb, array &$params)
    {
        if (!isset($params['status'])) return;

        $data = $params['status'];

        if ($data == 0) {
            $qb->where(static function (SelectQuery $select) {
                $select
                    ->where('active_upload', 0)
                    ->where('reg_upload', 0)
                    ->where('login_upload', 0)
                    ->where('pay_upload', 0)
                    ->where('create_role_upload', 0);
            });
        }
        else {
            $qb->where(static function (SelectQuery $select) {
                $select
                    ->orWhere('active_upload', '!=', 0)
                    ->orWhere('reg_upload', '!=', 0)
                    ->orWhere('login_upload', '!=', 0)
                    ->orWhere('pay_upload', '!=', 0)
                    ->orWhere('create_role_upload', '!=', 0);
            });
        }
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchReportStatus(&$qb, array &$params)
    {
        if (!isset($params['report_result'])) return;

        $status = $params['report_result'];
        $field  = $this->getReflectField('notice');

        if (1 == $status) {
            $qb->where($field, 'like', '%成功%');
        }
        else {
            $qb->where($field, 'like', '%失败%');
        }
    }


}
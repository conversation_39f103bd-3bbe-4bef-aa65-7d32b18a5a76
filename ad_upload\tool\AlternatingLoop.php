<?php

namespace app\ad_upload\tool;

use Plus\Cache\RedisCache;

/**
 * 交替循环
 * 用于生成按指定交替循环，并使用Redis进行存储和管理
 * <AUTHOR>
 */
class AlternatingLoop
{
    /**
     * Redis 实例
     * @var RedisCache
     */
    private $redis;
    /**
     * Redis 键名
     * @var string
     */
    private $key;

    /**
     * 返回 true N 次
     * @var int
     */
    private $start;
    /**
     * 返回 false N 次
     * @var int
     */
    private $stop;

    /**
     * 构造函数
     * @param RedisCache $redis Redis 实例
     * @param string     $key   缓存key
     * @param int        $start 回 true N 次
     * @param int        $stop  返回 false N 次
     */
    public function __construct($redis, string $key, int $start, int $stop)
    {
        $this->redis = $redis;
        $this->key   = $key;
        $this->start = $start;
        $this->stop  = $stop;
    }

    /**
     * 初始化交替序列并存储到 Redis
     * @return void
     */
    private function initializeSequence(): void
    {
        $rA       = array_fill(0, $this->start, 1);
        $rB       = array_fill(0, $this->stop, 0);
        $sequence = array_merge($rA, $rB);
        //var_dump($sequence);

        // 将序列存储到 Redis 列表
        foreach ($sequence as $v) {
            $this->redis->rPush($this->key, $v);
        }
        $this->redis->expire($this->key, 365 * 24 * 60 * 60); //有效期一年
    }


    /**
     * 获取下一个值
     * @return bool
     */
    public function next(): bool
    {
        $nextValue = $this->redis->lPop($this->key);
        // 如果队列为空，重新生成序列
        if ($nextValue === false) {
            $this->initializeSequence();
            $nextValue = $this->redis->lPop($this->key);
        }
        //var_dump($nextValue, $nextValue == 1, "next");
        return $nextValue == 1;
    }
}

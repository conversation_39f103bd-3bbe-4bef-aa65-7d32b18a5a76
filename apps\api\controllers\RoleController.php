<?php

namespace app\apps\api\controllers;

use app\extension\Exception\ParameterException;
use app\models\baseConfPlatform\TbOathConf;
use app\models\DdcPlatform\DwsRole;
use app\service\Admin;
use Plus\MVC\Controller\JsonController;

/**
 * Class user
 * <AUTHOR>
 */
class RoleController extends JsonController
{

    /**
     * 角色数据
     * @param array $data 请求参数
     * @return array
     */
    public function dataAction()
    {
        //params
        $roleId = $this->getValue("role_id");
        $cpGameId = $this->getValue("cp_game_id");
        $fileds = $this->getValue("fields","cp_game_id,game_id,game_server,role_id,role_name,role_rank,role_vip,castle_rank");
        if (!$roleId) {
            throw new ParameterException("角色ID不能为空");
        }
        if (!$cpGameId) {
            throw new ParameterException("游戏原名不能为空");
        }
        if(!$fileds){
            $fileds = "*";
        }else{
            $fileds = explode(",",$fileds);
        }
        $data =  (new DwsRole())->asArray()->findAll(["CP_GAME_ID"=>$cpGameId,"ROLE_ID"=>explode(",",$roleId)],$fileds);
        return $this->success($data??[]);
    }

}


<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\General\BizTagsServ;
use app\service\General\Helpers\BaseConfPlatformTable;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

class CreativePayMatch extends PlanAdMatch
{
    public function processLine(): array
    {
        $newLine = [
            [$this, 'matchAccountId'],
            [$this, 'matchAccountName'],
            [$this, 'matchGameTags'],
            [$this, 'matchPackageTags'],
            [$this, 'matchChannelTags'],
            [$this, 'matchChannelMainTags'],
        ];

        return array_merge(parent::processLine(), $newLine);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return void
     * @throws \Exception
     */
    protected function matchTDay(&$qb, array $params)
    {
        if (
            empty($params['range_date_start']) || empty($params['range_date_end'])
        ) return;

        $field     = $this->getReflectField('tday');
        $rangeDate = array_filter([
            $params['range_date_start'],
            $params['range_date_end'],
        ]);

        sort($rangeDate);

        foreach ($rangeDate as &$foo) {
            $foo = (new \DateTime($foo))->format('Y-m-d');
        }

        [$timeStart, $timeEnd] = $rangeDate;
        $timeStart .= ' 00:00:00';
        $timeEnd   .= ' 23:59:59';

        $qb->where($field, 'between', $timeStart, $timeEnd);
    }

    protected function matchPlanId(&$qb, array &$params)
    {
        if (isset($params['plan_id'])) {
            $params['plan_id'] = is_numeric($params['plan_id']) ? strval($params['plan_id']) : $params['plan_id'];
        }

        parent::matchPlanId($qb, $params);
    }

    protected function matchPlatform(&$qb, array &$params)
    {
        if (!isset($params['platform_id'])) return;

        $field = $this->getReflectField('platform_id');
        $data  = $params['platform_id'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    protected function matchChannelId(&$qb, array &$params)
    {
        if (empty($params['channel_id'])) return;

        $field = $this->getReflectField('channel_id');
        $data  = $params['channel_id'];

        QueryBuilderHelper::priorityOrderBuild($qb, $field, $data);
    }

    protected function matchChannelMainId(&$qb, array &$params)
    {
        if (empty($params['channel_main_id'])) return;

        $field = $this->getReflectField('channel_main_id');
        $data  = $params['channel_main_id'];

        QueryBuilderHelper::priorityOrderBuild($qb, $field, $data);
    }

    protected function matchPackageTags(&$qb, array &$params)
    {
        if (empty($params['package_id_tags'])) return;

        $field = $this->getReflectField('package_id');
        $data  = $params['package_id_tags'];

        $subQb = $this->getConn()->select()
            ->from(BaseConfPlatformTable::BizTags)
            ->where('table_name', 'packages')
            ->where('tag_id', 'IN', new Parameter($data))
            ->columns(['data_id'])->distinct();

        $qb->where($field, 'IN', $subQb);
    }

    /**
     * @throws \RedisException
     */
    protected function matchChannelTags(&$qb, array &$params)
    {
        if (empty($params['channel_id_tags'])) return;

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        $field = $this->getReflectField('channel_id');
        $data  = $params['channel_id_tags'];

        $subQb = $this
            ->getConn()
            ->select()
            ->from(BaseConfPlatformTable::BizTags)
            ->where('table_name', 'app_channel')
            ->where('tag_id', 'IN', new Parameter($data))
            ->columns(['data_id'])->distinct();

        $powerChannelCol = $this->getReflectField('power.channel_id');
        $whereString     = sprintf(
            "IF(%s NOT IN (%s), %s, IF(%s != 0, IF(%s = 1013, 4, %s), %s)) IN (%s)",
            $powerChannelCol, $planChannelsString, $powerChannelCol, $field, $field, $field, $powerChannelCol, $subQb->__toString()
        );

        $qb->where(new Fragment($whereString));
    }

    protected function matchChannelMainTags(&$qb, array &$params)
    {
        if (empty($params['channel_main_id_tags'])) return;

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        $field = $this->getReflectField('channel_main_id');
        $data  = $params['channel_main_id_tags'];

        $subQb = $this
            ->getConn()
            ->select()
            ->from(BaseConfPlatformTable::BizTags)
            ->where('table_name', 'app_channel')
            ->where('tag_id', 'IN', new Parameter($data))
            ->columns(['data_id'])->distinct();

        $powerMainChannel = $this->getReflectField('power.channel_main_id');
        $powerChannelCol  = $this->getReflectField('power.channel_id');

        $whereString = sprintf(
            "IF(%s NOT IN (%s), %s, IF(%s != 0, IF(%s = 1013, 4, %s), %s)) IN (%s)",
            $powerChannelCol, $planChannelsString, $powerMainChannel, $field, $field, $field, $powerChannelCol, $subQb->__toString()
        );

        $qb->where(new Fragment($whereString));
    }

    protected function matchGameTags(&$qb, array &$params)
    {
        if (empty($params['game_id_tags'])) return;

        $field = $this->getReflectField('game_id');
        $data  = $params['game_id_tags'];

        $subQb = $this
            ->getConn()
            ->select()
            ->from(BaseConfPlatformTable::BizTags)
            ->where('table_name', 'games')
            ->where('tag_id', 'IN', new Parameter($data))
            ->columns(['data_id'])->distinct();

        $qb->where($field, 'IN', $subQb);
    }


    protected function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

}
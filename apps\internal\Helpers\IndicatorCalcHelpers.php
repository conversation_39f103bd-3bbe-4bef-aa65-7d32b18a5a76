<?php

namespace app\apps\internal\Helpers;

use MathParser\Parsing\Nodes\ExpressionNode;
use MathParser\StdMathParser;

class IndicatorCalcHelpers
{
    /**
     * 用户首次在线平均时长
     *
     * @param $data
     *
     * @return void
     */
    public static function firstOnlineTime(&$data)
    {
        $data['first_online_time_avg'] = IndicatorsHelpers::division(
            $data['first_online_time'], ($data['new_user'] * 60)
        );
    }

    /**
     * 平均在线时长
     *
     * @param $data
     *
     * @return void
     */
    public static function onlineTime(&$data)
    {
        $data['online_time_avg'] = IndicatorsHelpers::division(
            $data['online_time'], ($data['new_user'] * 60)
        );
    }

    /**
     * 新用户成本
     *
     * @param $data
     *
     * @return void
     */
    public static function newUserCost(&$data)
    {
        if (
            empty($data['cost_discount'])
            || empty($data['new_user'])
        ) {
            $data['new_user_cost'] = 0.00;
            return;
        }

        $data['new_user_cost'] = number_format(
            math_eval('x/y', ['x' => $data['cost_discount'], 'y' => $data['new_user']]),
            2
        );
    }

    public static function cpc(&$data)
    {
        $data['cpc'] = IndicatorsHelpers::division(
            $data['cost_discount'], $data['click']
        );
    }

    public static function clickShowPercent(&$data)
    {
        $data['click_show_percent'] = IndicatorsHelpers::division(
            $data['click'], $data['show'], 2, true
        );
    }

    /**
     * 千展成本
     *
     * @param $data
     *
     * @return void
     */
    public static function qianCost(&$data)
    {
        $data['qian_cost'] = IndicatorsHelpers::division(
            $data['cost'] * 1000, $data['show']
        );
    }


    public static function calcLpClickPercent(&$data)
    {
        $data['lp_click_percent'] = IndicatorsHelpers::division(
            $data['lp_download'], $data['lp_view'], 2, true
        );
    }

    public static function convertCost(&$data)
    {
        $data['convert_cost'] = IndicatorsHelpers::division(
            $data['cost'], $data['convert']
        );
    }

    public static function convertPercent(&$data)
    {
        $data['convert_percent'] = IndicatorsHelpers::division(
            $data['convert'], $data['click'], 2, true
        );
    }

    public static function downloadStartCostPercent(&$data)
    {
        $data['download_start_cost_percent'] = IndicatorsHelpers::division(
            $data['cost_discount'], $data['download_start']
        );
    }

    public static function downloadFinishPercent(&$data)
    {
        $data['download_finish_percent'] = IndicatorsHelpers::division(
            $data['download'], $data['download_start'], 2, true
        );
    }

    public static function installFinishNum(&$data)
    {
        $data['install_finish_num'] = IndicatorsHelpers::division(
            $data['lp_download'], $data['lp_view']
        );
    }

    public static function installFinishPercent(&$data)
    {
        $data['install_finish_percent'] = IndicatorsHelpers::division(
            $data['install'], $data['download'], 2, true
        );
    }

    public static function activateCost(&$data)
    {
        $data['activate_cost'] = IndicatorsHelpers::division(
            $data['cost_discount'], $data['activate']
        );
    }

    public static function activatePercent(&$data)
    {
        $data['activate_percent'] = IndicatorsHelpers::division(
            $data['activate'], $data['click'], 2, true
        );
    }

    public static function activateInstallPercent(&$data)
    {
        $data['activate_install_percent'] = IndicatorsHelpers::division(
            $data['activate'], $data['install'], 2, true
        );
    }

    public static function registerCost(&$data)
    {
        $data['register_cost'] = IndicatorsHelpers::division(
            $data['cost_discount'], $data['register']
        );
    }

    public static function registerPercent(&$data)
    {
        $data['register_percent'] = IndicatorsHelpers::division(
            $data['register'], $data['activate'], 2, true
        );
    }

    public static function newUserRealPercent(&$data)
    {
        $data['new_user_real_percent'] = IndicatorsHelpers::division(
            $data['new_real_user'], $data['new_user'], 2, true
        );
    }

    public static function newUserPaymentCost(&$data)
    {
        $data['new_user_payment_cost'] = IndicatorsHelpers::division(
            $data['cost_discount'], $data['pay_user_new']
        );
    }

    /**
     * 次留成本
     *
     * @param $data
     *
     * @return void
     */
    public static function remain1Cost(&$data)
    {
        $data['remain_1_cost'] = IndicatorsHelpers::division(
            $data['cost_discount'] ?? 0, $data['source_remain_1'] ?? 0
        );
    }

    public static function payUserPercent(&$data)
    {
        $data['pay_user_new_percent'] = IndicatorsHelpers::division(
            $data['pay_user_new'], $data['new_user'], 2, true
        );
    }

    public static function arpuNewUser(&$data)
    {
        $data['arpu_new_user'] = IndicatorsHelpers::division(
            $data['pay_money_new'], $data['new_user']
        );
    }

    public static function arppuNewUser(&$data)
    {
        $data['arppu_new_user'] = IndicatorsHelpers::division(
            $data['pay_money_new'], $data['pay_user_new']
        );
    }

    public static function payPenetration(&$data)
    {
        $data['pay_penetration'] = IndicatorsHelpers::division(
            $data['pay_money_new'], $data['new_user']
        );
    }

    public static function createRolePercent(&$data)
    {
        $data['create_role_percent'] = IndicatorsHelpers::division(
            $data['create_role_new'], $data['new_user'], 2, true
        );
    }

    public static function createRoleCost(&$data)
    {
        $data['create_role_cost'] = IndicatorsHelpers::division(
            $data['cost_discount'], $data['create_role_new']
        );
    }

    public static function payFrequencyAvg7days(&$data)
    {
        $data['pay_frequency_avg_7days'] = IndicatorsHelpers::division(
            $data['pay_frequency_7days'], $data['pay_new_user_7days']
        );
    }

    public static function payFrequency7daysCost(&$data)
    {
        $data['pay_frequency_7days_cost'] = IndicatorsHelpers::division(
            $data['cost_discount'], $data['pay_frequency_7days']
        );
    }

    /**
     * 通关率
     *
     * @param $target
     * @param $source
     *
     * @return void
     */
    public static function passLevel(&$target, $source)
    {
        $target['pass_level_1'] = IndicatorsHelpers::division($source['pass_level_1'] ?? 0, $source['pass_der_1'] ?? 0, 2, true);
        $target['pass_level_2'] = IndicatorsHelpers::division($source['pass_level_2'] ?? 0, $source['pass_der_2'] ?? 0, 2, true);
        $target['pass_level_3'] = IndicatorsHelpers::division($source['pass_level_3'] ?? 0, $source['pass_der_3'] ?? 0, 2, true);
        $target['pass_level_4'] = IndicatorsHelpers::division($source['pass_level_4'] ?? 0, $source['pass_der_4'] ?? 0, 2, true);
    }

    /**
     * 下载开始率
     *
     * @param $data
     *
     * @return void
     */
    public static function downloadStartPercent(&$data)
    {
        $data['download_start_percent'] = IndicatorsHelpers::division(
            $data['download_start'], $data['click'], 2, true
        );
    }

    /**
     * 付费渗透率计算
     *
     * @param $data
     * @param $source
     *
     * @return void
     */
    public static function payPermeation(&$data, $source)
    {
        $data['pay_permeation_percent'] = IndicatorsHelpers::division(
            $source['pay_per_user'], $data['new_user'], 2, true
        );
    }

    /**
     * @return ExpressionNode|mixed|null
     */
    public static function getSimplePercentParser()
    {
        static $parser;

        if (!is_null($parser)) {
            return $parser;
        }

        return $parser = static::simplePercentParser();
    }

    /**
     * @return ExpressionNode|mixed|null
     */
    public static function simplePercentParser()
    {
        return (new StdMathParser())->parse('x/y * 100');
    }

    /* 3秒播放率
     *
     * @param $data
     * @return void
     */
    public static function playDuration3SPercent(&$data)
    {
        $data['play_duration_3s_percent'] = IndicatorsHelpers::division(
            ($data['play_duration_3s'] ?? 0), ($data['total_play'] ?? 0), 2, true
        );
    }

    /**
     * 平均单次播放时长
     *
     * @param [type] $data
     * @return void
     */
    public static function playTimeAvg(&$data)
    {
        $data['play_time_avg'] = IndicatorsHelpers::division(
            ($data['play_time_per_play'] ?? 0), ($data['total_play'] ?? 0)
        );
    }

    /**
     * 曝光转化率
     *
     * @param $data
     * @return void
     */
    public static function showConvertPercent(&$data)
    {
        $data['show_convert_percent'] = IndicatorsHelpers::division(
            ($data['convert']??0), ($data['show'] ?? 0), 2, true
        );
    }
}
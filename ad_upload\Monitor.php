<?php
declare(ticks=1);

namespace app\ad_upload;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\CommonFunc;
use app\ad_upload\tool\CustomProcess;
use app\ad_upload\tool\ErrorHandler;
use app\util\Common;

/**
 * 媒体上报数据监控
 * 每0.5小时运行一次
 * <AUTHOR>
 */
class Monitor extends CustomProcess
{

    /**
     * 监控间隔
     * @var int
     */
    public $m = 36;
    /**
     * 开始时间
     * @var string
     */
    public $timeBegin = '';
    /**
     * 结束时间
     * @var string
     */
    public $timeEnd = '';

    /**
     * 上报配置
     * @var array
     */
    private $config = [];


    /**
     * 监控的渠道id
     * @var array
     */
    private $monitorChannelIds = [70, 8535, 1105, 1107, 1113, 2, 3, 4, 6, 8603, 8621, 8721];

    /**
     * 非小程序渠道，走包号归因
     * @var int[]
     */
    private $notXcxChannelIds = [2, 3, 4, 6];

    /**
     * 是否需要重跑进程
     * @var int
     */
    public $reRun = 1;


    /**
     * run
     * @return void
     */
    public function run()
    {
        if (empty($this->timeBegin)) {
            $this->timeBegin = date('Y-m-d H:i:s', strtotime('-' . $this->m . ' minutes'));
        }
        if (empty($this->timeEnd)) {
            $this->timeEnd = date('Y-m-d H:i:s', strtotime('-5 minutes'));
        }
        $this->config = $this->getConfig();

        $uploaded   = $this->getUploaded();
        $register   = $this->getRegister();
        $active     = $this->getActive();
        $createRole = $this->getCreateRole();
        $pay        = $this->getPay();


        $this->check($uploaded, $register, AdBaseInterface::ACTION_REGISTER);
        $this->check($uploaded, $active, AdBaseInterface::ACTION_ACTIVE);
        $this->check($uploaded, $createRole, AdBaseInterface::ACTION_CREATE_ROLE);
        $this->check($uploaded, $pay, AdBaseInterface::ACTION_PAY);
    }


    /**
     * 检查数据，并发送告警
     * @param array  $uploaded 已上报数据
     * @param array  $data     需要上报的数据
     * @param string $action   上报类型
     * @return false|void
     */
    private function check($uploaded, $data, $action)
    {
        $uploadedAction = $uploaded[$action] ?? [];
        if (empty($data)) {
            \Plus::$app->log->alert($action . '获取上报数据失败', [], AdBaseInterface::LOG_DIR);
            return false;
        }

        if (empty($uploadedAction)) {
            \Plus::$app->log->warning($action . '获取已上报数据失败', [], AdBaseInterface::LOG_DIR);
            return false;
        }

        foreach ($uploadedAction as $v1) {
            foreach ($data as $k2 => $v2) {
                if ($v1['channel_id'] == $v2['channel_id']) {
                    $data[$k2]['uploaded'] = $v1['c'];
                }
            }
        }

        foreach ($data as $v) {
            if ($v['channel_id'] == 0) {
                continue;
            }
            //不需要监控的跳过
            if (!in_array($v['channel_id'], $this->monitorChannelIds)) {
                continue;
            }
            //不需要上报的跳过
            if (!$this->needUpload($v['channel_id'], $action)) {
                continue;
            }

            $up        = $v['uploaded'] ?? 0;
            $real      = $v['c'];
            $diffRatio = $this->calculateDifferenceRatio($up, $real);
            $msg       = $action . '_channel_id：' . $v['channel_id'] . '，已上报数据：' . $up . '，需上报数据：' . $real . '，差异：' . $diffRatio . '%';
            \Plus::$app->log->info($msg, [], AdBaseInterface::LOG_DIR);
            // 1%以上差异，重跑；差异数据大于10个，发送告警; 付费事件只要不相等就重跑
            if (abs($diffRatio) > 1 || ($up != $real && $action == AdBaseInterface::ACTION_PAY)) {
                //重跑数据
                $this->reRun($action, $v['channel_id']);
                if (abs($up - $real) < 50) {
                    continue;
                }
                //发送企业微信消息
                if (APP_EVN != 'DEV') {
                    Common::qyWxBot(ErrorHandler::KEY, $msg);
                } else {
                    Common::qyWxBot(ErrorHandler::KEY_DEV, $msg);
                }
            }
        }// end foreach()
    }

    /**
     * 重跑数据
     * @param string $action    上报类型
     * @param int    $channelId 渠道id
     * @return void
     */
    private function reRun($action, $channelId)
    {
        if (!$this->reRun) {
            \Plus::$app->log->warning('跳过重跑', [], AdBaseInterface::LOG_DIR);
            return;
        }
        $fileInfo = explode('\\', AdUpload::class);
        unset($fileInfo[0]); //去掉 app
        $file = join('/', $fileInfo) . '.php';
        $opt  = [
            'f' => $file,
            'p' => "channel_id=$channelId actions=$action begin_time=\"{$this->timeBegin}\" end_time=\"{$this->timeEnd}\"",
        ];
        \Plus::$app->log->warning('重跑数据_' . $opt['p'], [], AdBaseInterface::LOG_DIR);
        $this->start($opt);
    }

    /**
     * 是否需要上报
     * @param int    $channelId 渠道id
     * @param string $action    上报类型
     * @return bool
     */
    private function needUpload($channelId, $action)
    {
        $rs = $this->config[$action][$channelId] ?? false;
        if (!empty($rs)) {
            return true;
        }
        return false;
    }

    /**
     * 计算差异比例
     * @param int $num1 num1
     * @param int $num2 num2
     * @return float|int
     */
    private function calculateDifferenceRatio($num1, $num2)
    {
        // 计算两个数的差异
        $difference = $num1 - $num2;
        $max        = max($num1, $num2);
        if ($max == 0) {
            return 0;
        }
        return number_format(($difference / $max) * 100, 2);
    }

    /**
     * 获取已上报数据
     * @return array|false
     */
    private function getUploaded()
    {
        $sql = "SELECT
                    channel_id,
                    `action`,
                    COUNT(1) as c
                FROM
                `bigdata_dwd`.`dwd_reported_platform_log`
                WHERE `time` BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
                GROUP BY channel_id,`action`";

        if (APP_EVN != 'DEV') {
            $db = \Plus::$app->doris_entrance;
        } else {
            $db = \Plus::$app->doris_entrance2;
        }
        $data = $db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $rs   = [];
        foreach ($data as $v) {
            $rs[$v['action']][] = $v;
        }
        return $rs;
    }


    /**
     * 获取注册数据
     * @return array|false
     */
    private function getRegister()
    {
        $regConfig = $this->config[AdBaseInterface::ACTION_REGISTER];
        $rs        = [];
        foreach ($regConfig as $channelId => $pkgs) {
            if (!in_array($channelId, $this->monitorChannelIds)) {
                continue;
            }
            $pkgStr = CommonFunc::arr2Str($pkgs);
            $sql    = "SELECT COUNT(*) AS c
                FROM (
                    SELECT 1
                    FROM tb_sdk_user_newlogin_package
                    WHERE CHANNEL_ID = {$channelId}
                      AND PACKAGE_ID IN ($pkgStr)
                      AND `time` BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
                    GROUP BY MD5_DEVICE_ID, PACKAGE_ID, ANDROID_ID, IP
                ) AS subquery";
            $c      = \Plus::$app->origin_platform->query($sql)->fetchColumn();
            $rs[]   = ['channel_id' => $channelId, 'c' => $c];
        }
        return $rs;
    }

    /**
     * 获取激活数据
     * @return array|false
     */
    private function getActive()
    {
        $regConfig = $this->config[AdBaseInterface::ACTION_ACTIVE];
        $rs        = [];
        foreach ($regConfig as $channelId => $pkgs) {
            if (!in_array($channelId, $this->monitorChannelIds)) {
                continue;
            }
            $pkgStr = CommonFunc::arr2Str($pkgs);
            $sql    = "SELECT COUNT(*) AS c
                FROM (
                    SELECT 1
                    FROM tb_sdk_active_log
                    WHERE CHANNEL_ID = {$channelId}
                      AND PACKAGE_ID IN ($pkgStr)
                      AND `time` BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
                    GROUP BY MD5_DEVICE_ID,PACKAGE_ID,ANDROID_ID,IP
                ) AS subquery";
            $c      = \Plus::$app->origin_platform->query($sql)->fetchColumn();
            $rs[]   = ['channel_id' => $channelId, 'c' => $c];
        }
        return $rs;
    }

    /**
     * 获取创建角色数据
     * @return array|false
     */
    private function getCreateRole()
    {
        $sql = "SELECT
              P.channel_id,
              COUNT(1) as c
            FROM
                tb_sdk_user_role_newlogin as R
                JOIN tb_sdk_user_newlogin_package AS P USING(PACKAGE_ID, GAME_ID, CORE_ACCOUNT)
            WHERE R.`time` BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
            GROUP BY P.channel_id";
        return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 获取支付数据
     * @return array|false
     */
    private function getPay()
    {
        $notXcxChannelIds = CommonFunc::arr2Str($this->notXcxChannelIds);
        $xcxChannelIds    = CommonFunc::arr2Str(array_diff($this->monitorChannelIds, $this->notXcxChannelIds));
        //小程序归因的
        $sql  = "SELECT
              t3.channel_id,
              COUNT(1) as c
            FROM
                   origin_platform.tb_sdk_user_payment t1
         left join ddc_platform.dwd_sdk_user_payment t2 on t1.order_id = t2.order_id
         left join ddc_platform.dwd_sdk_adsource_game t3 on t2.source_id = t3.source_id
            WHERE t1.`pay_time` BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}' AND t1.PAY_RESULT=1
              AND t3.channel_id in ($xcxChannelIds)
            GROUP BY t3.channel_id";
        $data = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        //包号新增的
        $sql3  = "
        SELECT 
               a.channel_id,
               COUNT(1) as c
        FROM origin_platform.tb_sdk_user_payment a
        LEFT JOIN origin_platform.tb_sdk_user_newlogin_package b USING (PACKAGE_ID, CORE_ACCOUNT)
        LEFT JOIN base_conf_platform.tb_package_detail_conf pc on a.PACKAGE_ID = pc.PACKAGE_ID
        LEFT JOIN ddc_platform.dwd_ad_click_match_log m ON a.CLICK_ID=m.id
        where a.channel_id in($notXcxChannelIds)  AND
              a.`pay_time` BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}' AND a.PAY_RESULT=1 AND   m.id is not null
        GROUP BY a.CHANNEL_ID
        ";
        $data2 = \Plus::$app->origin_platform->query($sql3)->fetchAll(\PDO::FETCH_ASSOC);

        $data = array_merge($data, $data2);
        //今日头条-微信小程序, 要排除激活时间超过30天的
        $day30        = date('Y-m-d H:i:s', strtotime('-30 day'));
        $sql2         = "SELECT
                   count(1)
                FROM
                  origin_platform.tb_sdk_user_payment t1
                  LEFT JOIN ddc_platform.dwd_sdk_user_payment t2
                    ON t1.order_id = t2.order_id
                  LEFT JOIN ddc_platform.dwd_sdk_adsource_game t3
                    ON t2.source_id = t3.source_id
                WHERE t1.`pay_time` BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}'
                  AND t1.PAY_RESULT = 1
                  AND t3.channel_id = 1107
                  AND t3.`ACTIVATE_TIME` < '$day30'";
        $expiredCount = \Plus::$app->origin_platform->query($sql2)->fetchColumn();
        if ($expiredCount <= 0) {
            return $data;
        }
        foreach ($data as $k => $v) {
            if ($v['channel_id'] == 1107) {
                $data[$k]['c'] -= $expiredCount;
            }
        }
        return $data;
    }

    /**
     * 获取上报配置
     * @return array
     */
    private function getConfig()
    {
        $sql        = "SELECT
                    duc.ID, 
                    duc.CHANNEL_ID, 
                    duc.PACKAGE_ID, 
                    duc.ACTIVE_UPLOAD, 
                    duc.CREATE_ROLE_UPLOAD,
                    duc.REG_UPLOAD, 
                    duc.LOGIN_UPLOAD, 
                    duc.PAY_UPLOAD, 
                    duc.REMAIN_UPLOAD, 
                    acc.UPLOAD_METHOD,
                    duc.PAY_VIRTUAL_UPLOAD
                FROM tb_ad_data_upload_conf duc
                LEFT JOIN base_conf_platform.tb_base_channel_conf acc ON duc.CHANNEL_ID = acc.CHANNEL_ID
                AND duc.STATUS = 1";
        $configList = \Plus::$app->dataspy->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $rs         = [];
        // 定义上传标志到动作常量的映射
        $uploadFlagsToActions = [
            'ACTIVE_UPLOAD'      => AdBaseInterface::ACTION_ACTIVE,
            'CREATE_ROLE_UPLOAD' => AdBaseInterface::ACTION_CREATE_ROLE,
            'REG_UPLOAD'         => AdBaseInterface::ACTION_REGISTER,
            'LOGIN_UPLOAD'       => AdBaseInterface::ACTION_LOGIN,
            'PAY_UPLOAD'         => AdBaseInterface::ACTION_PAY,
            'REMAIN_UPLOAD'      => AdBaseInterface::ACTION_REMAIN,
            'PAY_VIRTUAL_UPLOAD' => AdBaseInterface::ACTION_PAY_VIRTUAL,
        ];
        foreach ($configList as $val) {
            // 遍历映射数组
            foreach ($uploadFlagsToActions as $flag => $action) {
                if ($val[$flag]) {
                    $rs[$action][$val['CHANNEL_ID']][] = $val['PACKAGE_ID'];
                }
            }
        }
        return $rs;
    }
}

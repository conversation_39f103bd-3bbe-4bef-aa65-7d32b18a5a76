{* 扣量上报日志记录查询 *}
select
    t_main.* ,
    t_source.plan_id,
    t_source.channel_id
from ddc_platform.dwd_sdk_adsource_game t_source
right join (

select
    t1.cp_game_id,
    t1.game_id,
    t1.package_id,
    t1.order_id_actually as order_id,
    t1.report_type,
    t1.money,
    IF(t1.report_type=1, '-' , t1.money_report) as money_report,
    t1.report_time,
    IF(t1.report_type=1, '不上报', '已上报') as report_result,
    t_pay.source_id,
    t1.core_account,
    t1.report_rule_name,
    t1.time
from ddc_platform.dwd_sdk_user_payment t_pay
right join bigdata_dwd.dwd_sdk_payment_intercept t1 on t1.order_id_actually = t_pay.order_id

{* 搜索条件 *}
{if isset($params) && !empty($params) }
    where true
    {* 上报时间 *}
    {if !empty($params['report_time'])}
        and t1.report_time between '{$params['report_time'][0]}' and '{$params['report_time'][1]}'
    {/if}
    {* 游戏原名 *}
    {if !empty($params['cp_game_id'])}
        and t1.cp_game_id IN ({$params['cp_game_id']})
    {/if}
    {* 游戏统计名 *}
    {if !empty($params['game_id'])}
        and t1.game_id IN ({$params['game_id']})
    {/if}
    {* 包号 *}
    {if !empty($params['package_id'])}
        and t1.package_id IN ({$params['package_id']})
    {/if}
    {* 核心账号 *}
    {if !empty($params['core_account'])}
        and t1.core_account like '%{$params['core_account']}%'
    {/if}
    {* 规则类型 *}
    {if !empty($params['report_type'])}
        and t1.report_type = {$params['report_type']}
    {/if}
    {* 规则名称 *}
    {if !empty($params['report_rule_name'])}
        and t1.report_rule_name like '%{$params['report_rule_name']}%'
    {/if}
    {if !empty($params['order_id'])}
        and t_pay.order_id IN ({$params['order_id']})
    {/if}
{/if}

{* 排序 *}
{if $sort|isset}
    order by
    {$is_first=1}
    {foreach $sort as $k => $foo}
        {if $is_first eq 1}
            {$k} {$foo}
            {$is_first=0}
        {else}
            , {$k} {$foo}
        {/if}
    {/foreach}
{/if}

{* 分页 *}
{*{if !empty($paginate)}*}
{*    {assign var=page_size value=$paginate.page_size}*}
{*    {assign var=page value=$paginate.page}*}
{*    limit {$page_size} offset {($page-1) * $page_size}*}
{*{/if}*}

) t_main
on t_source.source_id = t_main.source_id

{* 排序 *}
{if $wrap_sort|isset}
    order by
    {$is_first=1}
    {foreach $wrap_sort as $k => $foo}
        {if $is_first eq 1}
            {$k} {$foo}
            {$is_first=0}
        {else}
            , {$k} {$foo}
        {/if}
    {/foreach}
{/if}

{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}

SELECT
    {if (isset($is_hour) && $is_hour eq 1)}
        DATE_FORMAT(t_pass.tday, '%Y-%m-%d %H:00:00') as tday,
    {else}
        DATE_FORMAT(t_pass.tday, '%Y-%m-%d') as tday,
    {/if}
    t_pass.cp_game_id as cp_game_id,
    t_pass.game_id as game_id,
    t_pass.package_id as package_id,
    IF(t_pass.channel_id = 0, power.channel_id, t_pass.channel_id) as channel_id,
    t_pass.campaign_id as campaign_id,
    t_pass.plan_id as plan_id,
    t_pass.creative_id as creative_id,
    t_pass.account_id as account_id,
    t_pass.ad_account as ad_account,
    t_pass.user_id as user_id,
    au.department_id as department_id,
    base_channel.channel_main_id as channel_main_id ,
    power.app_show_id as app_show_id,
    power.platform_id as platform_id,
    adp_oauth.advertiser_name as account_name,
    case when USER_OS = JSON_ARRAY('ANDROID') then 'ANDROID' when USER_OS = JSON_ARRAY('IOS') then 'IOS' when JSON_CONTAINS(USER_OS, '["ANDROID","IOS"]', '$') = 1 then '混投' else '混投' end as dim_user_os,
    {if $indicators_column|isset}
        {$indicators_column}
    {/if}
FROM ddc_platform.dws_creative_ad_pass_hourly t_pass
JOIN {$powerSql}
on t_pass.package_id = power.package_id
LEFT JOIN adp_platform.tb_adp_campaign t_campaign on t_pass.CAMPAIGN_ID = t_campaign.CAMPAIGN_ID
LEFT JOIN adp_platform.tb_adp_plan_base t_plan on t_pass.channel_id = t_plan.channel_id and t_pass.plan_id = t_plan.plan_id
LEFT JOIN base_conf_platform.tb_base_channel_conf as base_channel
on t_pass.channel_id = base_channel.channel_id
LEFT JOIN dataspy.admin_user au on t_pass.user_id = au.id
LEFT JOIN adp_platform.tb_adp_oauth adp_oauth on t_pass.account_id = adp_oauth.advertiser_id

{include file="sql/advertise/ad_pass_rule/base_match.tpl"}

{* 分组汇总 *}
{if $group_by|isset}
    group by {$group_by}
{/if}

{* 排序 *}
{if $sort|isset}
    order by
    {$is_first=1}
    {foreach $sort as $k => $foo}
        {if $is_first eq 1}
            {$k} {$foo}
            {$is_first=0}
        {else}
            , {$k} {$foo}
        {/if}
    {/foreach}
{/if}


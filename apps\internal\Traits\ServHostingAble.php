<?php

namespace app\apps\internal\Traits;

trait ServHostingAble
{
    /**
     * @var object[]
     */
    protected array $servStorage = [];

    /**
     * @param $serv
     * @param $key
     *
     * @return void
     */
    protected function cacheServForStorage(&$serv, $key)
    {
        $this->servStorage[$key] = $serv;
    }

    /**
     * @param string $key
     *
     * @return object|null
     */
    protected function getServFormStorage(string $key): ?object
    {
        if (!isset($this->servStorage[$key])) {
            return null;
        }

        if (is_object($this->servStorage[$key])) {
            return clone $this->servStorage[$key];
        }
        else {
            return $this->servStorage[$key];
        }
    }


}
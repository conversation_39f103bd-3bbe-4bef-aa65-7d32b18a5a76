<?php

namespace app\service\AdvertiserData\Scheme;

use app\extension\Support\Macroable\Traits\Macroable;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Traits\JoinBase;
use app\service\AdvertiserData\Traits\JoinFixAble;
use app\service\AdvertiserData\Traits\OperableQuery;
use app\service\AdvertiserData\Traits\Schemer;
use Aura\SqlQuery\Common\InsertInterface;
use Aura\SqlQuery\Common\SelectInterface;
use Aura\SqlQuery\Common\UpdateInterface;
use Aura\SqlQuery\QueryFactory;

class AdCreativePayRemainScheme implements SchemeContract
{
    use Macroable, Schemer, OperableQuery, JoinFixAble, JoinBase;

    protected QueryFactory $queryFactory;
    /**
     * @var InsertInterface|SelectInterface|UpdateInterface
     */
    protected $query;

    public const MAIN_TABLE = [
        'alias' => 't_remain',
        'table' => 'ddc_platform.dws_creative_ad_pay_remain_daily',
    ];

    /**
     * 关联表
     *
     * @var array|array[]
     */
    protected array $joinTables = [];

    public function __construct()
    {
        $this->queryFactory = new QueryFactory('mysql');
    }

    public static function NewOne(): AdCreativePayRemainScheme
    {
        return new static();
    }

    public function __clone()
    {
        $this->queryFactory = clone $this->queryFactory;
        $this->query        = clone $this->query;
    }


    public function fieldReflect(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];

        return [
            'tday'                 => $mainTable,
            'cp_game_id'           => $mainTable,
            'game_id'              => $mainTable,
            'package_id'           => $mainTable,
            'app_show_id'          => 'POWER',
            'channel_main_id'      => 'POWER',
            'channel_id'           => $mainTable,
            'platform_id'          => 'POWER',
            'plan_id'              => $mainTable,
            'campaign_id'          => $mainTable,
            'creative_id'          => $mainTable,
            'promotion_id'         => 'POWER.popularize_v2_id',
            'promotion_channel_id' => $mainTable . '.channel_id',
            'plan_name'            => 'base_plan',
            'campaign_name'        => 'base_campaign',
            'creative_name'        => 'base_creative',
            'ad_account'           => 'base_account.ad_account',
            'ad_account_id'        => 'base_account.id',
            'account_id'           => 'base_account.account_id',
            'user_id'              => 'base_plan.user_id',
            'department_id'        => 'base_plan.department_id',
            'is_ad_data'           => 't_base'
        ];
    }

    protected function fixedTables(): array
    {
        $mainTable  = self::MAIN_TABLE['alias'];
        $fixedTable = [];

        return $fixedTable;
    }
}
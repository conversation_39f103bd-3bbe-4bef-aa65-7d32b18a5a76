<?php

namespace app\ad_upload_tmp\tool;

use Plus\CLI\DaemonProcess;
use Plus\CLI\Process;

/**
 * 进程主类
 * <AUTHOR>
 *         phpcs:disable
 */
abstract class CustomProcess extends DaemonProcess
{
    public function __construct()
    {
        $this->initializeParams();
    }

    /**
     * 初始化传入参数 (支持json格式，支持参数格式)
     * 传入格式 --p 'start_date=2022-05-07 end_date=2022-06-07 channel_id=1,2,3,4'
     * 传入格式 --p '{"process_date":"2022-06-09 17:45:32"}'
     * 调用为 $this->start_date  需要先定义类 属性
     */
    public function initializeParams()
    {
        $opts   = getopt('f:s:', ['help', 'p:']);
        $opt    = $opts['p'] ?? '';
        $params = json_decode($opt, true);
        if ($params) {
            foreach ($params as $key => $value) {
                if (property_exists($this, $key)) {
                    $this->$key = $value;
                }
            }
        } else {
            // 正则匹配键值对
            preg_match_all('/(\w+)=(".*?"|\S+)/', $opt, $matches, PREG_SET_ORDER);
            foreach ($matches as $match) {
                $key   = $match[1];
                $value = $match[2];
                // 去掉值两边的引号
                $value = trim($value, '"');
                if (property_exists($this, $key)) {
                    $this->$key = $value;
                }
            }
        }
    }
}

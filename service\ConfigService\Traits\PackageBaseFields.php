<?php

namespace app\service\ConfigService\Traits;

use app\service\ConfigService\Annotations\TableField;

trait PackageBaseFields
{
    /**
     * @TableField(title="游戏原名", dataIndex="cp_game_id"，classify={"attrs", "base_info"})
     * @var string
     */
    public string $cpGameId;

    /**
     * @TableField(title="游戏统计名", dataIndex="game_id"，classify={"attrs", "base_info"})
     * @var string
     */
    public string $gameId;

    /**
     * @TableField(title="游戏前端名", dataIndex="app_show_id"，classify={"attrs", "base_info"})
     * @var string
     */
    public string $appShowId;

    /**
     * @TableField(title="包号", dataIndex="package_id"，classify={"attrs", "base_info"})
     * @var string
     */
    public string $packageId;

    /**
     * @TableField(title="主渠道",dataIndex="channel_main_id",classify={"attrs", "base_info"})
     * @var string
     */
    public string $channelMainId;

    /**
     * @TableField(title="子渠道", dataIndex="channel_id",classify={"attrs", "base_info"})
     * @var string
     */
    public string $channelId;

    /**
     * @TableField(title="投放部门",dataIndex="department_id", classify={"attrs", "base_info"})
     * @var string
     */
    public string $departmentId;

    /**
     * @var string
     * @TableField(title="投放人",dataIndex="user_id",classify={"attrs", "base_info"})
     */
    public string $userId;
}
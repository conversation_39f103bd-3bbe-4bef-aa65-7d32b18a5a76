<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Matcher\AdPlanMatch;
use app\service\General\BizTagsServ;
use app\service\General\Helpers\AdpPlatformTable;
use app\service\General\Helpers\BaseConfPlatformTable;
use app\service\General\Helpers\DataSpyTable;
use app\service\General\Helpers\DdcPlatformTable;
use app\util\Common;
use PhpOffice\PhpSpreadsheet\Calculation\Statistical\Distributions\F;
use PhpParser\Node\Stmt\Expression;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Query\SelectQuery;

/**
 * 计划类基础指标查询逻辑
 *
 */
class AdPlanBaseServ
{
    const MODE_ALL = 31;
    const MODE_DETAIL = 1;
    const MODE_TOTAL = 2;
    const MODE_SUMMARY = 4;
    const MODE_DETAIL_QB = 8;
    const MODE_SUMMARY_QB = 16;

    const QB_MODE_ALL = 63;
    const QB_MODE_POWER = 1;
    const QB_MODE_PAYMENT = 2;
    const QB_MODE_AD_CHANNEL = 4;
    const QB_MODE_ADP_OAUTH = 8;
    const QB_MODE_AD_USER = 16;
    const QB_MODE_AD_ACCOUNT = 32;

    /**
     * 汇总维度字段的临时对应关系映射
     * @var array|string[]
     */
    private array $groupReflectMap = [
        'cp_game_id'           => 't_base.cp_game_id',
        'tday'                 => 't_base.tday',
        'game_id'              => 't_base.game_id',
        'app_show_id'          => 'power.app_show_id',
        'channel_main_id'      => 'tztz_channel_main_id',
        'promotion_channel_id' => 'tztz_promotion_channel_id',
        'user_id'              => 'tztz_user_id',
        'department_id'        => 'tztz_department_id',
        'package_id'           => 't_base.package_id',
        'platform_id'          => 't_base.platform_id',
        'account_id'           => 't_base.account_id',
        'account_name'         => 't_base.account_name',
        'plan_id'              => 't_base.plan_id',
    ];

    /**
     * 获取统计的基础指标数据
     *
     * @param array $params 搜索条件
     * @param array $groups 汇总维度
     * @param array $paginate 分页
     * @param array $sort 排序
     * @param int $mode 获取查询的维度
     * @return void
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], array $cols = [], int $mode = -1
    )
    {
        // todo: code in here
    }

    /**
     * 离线看板特供
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $cols
     * @param int $mode
     * @return array
     * @throws \RedisException
     * @throws \Exception
     */
    public function getListForBigData(
        array $params, array $groups = [], array $paginate = [], array $sort = [], array $cols = [], int $mode = -1
    ): array
    {
        $groupsMap = [
            'tday'                 => 't_base.tday',
            'cp_game_id'           => 't_base.cp_game_id',
            'game_id'              => 't_base.game_id',
            'app_show_id'          => 'power.app_show_id',
            'channel_main_id'      => 'tztz_channel_main_id',
            'channel_id'           => 'tztz_promotion_channel_id',
            'promotion_channel_id' => 'tztz_promotion_channel_id',
            'package_id'           => 't_base.package_id',
            'platform_id'          => 'power.platform_id',
            'account_id'           => 't_base.account_id',
            'user_id'              => 'tztz_user_id',
            'department_id'        => 'tztz_department_id',
        ];

        $result = [];
        $qb     = $this->queryBuilder(self::QB_MODE_ALL ^ self::QB_MODE_PAYMENT, $params, $groups);

        // todo: 搜索条件拼接
        $matcher = new AdPlanMatch([
            'package_id'          => 't_base.package_id',
            'cp_game_id'          => 't_base.cp_game_id',
            'game_id'             => 't_base.game_id',
            'platform_id'         => 'power.platform_id',
            'app_show_id'         => 'power.app_show_id',
            'channel_main_id'     => 'ad_channel.channel_main_id',
            'channel_id'          => 't_base.channel_id',
            'promotion_id'        => 'power.popularize_v2_id',
            'power.department_id' => 'power.ad_department_id',
            'department_id'       => 'ad_user.department_id',
            'user_id'             => 't_base.user_id',
            'power.user_id'       => 'power.ad_user_id',
            'ad_account'          => 'base_account.ad_account',
            'account_id'          => 'base_account.account_id',
            'campaign_id'         => 't_base.campaign_id',
            'plan_id'             => 't_base.plan_id',
            'is_appointment'      => 't_base.is_appointment',
            'marketing_goal'      => 't_base.marketing_goal',
            'tday'                => 't_base.tday',
        ]);

        $matcher($qb, $params);

        $infoQb   = clone $qb;
        $infoCols = $this->searchCols(self::QB_MODE_ALL, false, $params, $groups, $cols);
        $infoQb->columns($infoCols);

        if (!empty($groups)) {
            foreach ($groups as $g) {
                if (isset($groupsMap[$g])) {
                    $infoQb->groupBy($groupsMap[$g]);
                }
                else {
                    $infoQb->groupBy($g);
                }
            }
        }

        $notPageInfoQb = clone $infoQb;

        if (!empty($sort)) {
            $infoQb->orderBy($sort);
        }

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $pageLen] = $paginate;
            $infoQb->limit($pageLen)->offset(($page - 1) * $pageLen);
        }

        if ($mode & self::MODE_DETAIL_QB) {
            // 返回查询构造对象
            $result['detail_qb'] = clone $infoQb;
        }

        if ($mode & self::MODE_DETAIL) {
            if (
                !empty($groups)
                && !in_array('tday', $groups)
            ) {
                $newCols = array_merge($infoCols, $this->getNodeCols($params));
                $infoQb->columns($newCols);
            }

            @Common::dumpSql($infoQb->__toString());

            $result['list'] = $infoQb->fetchAll();
        }

        if ($mode & self::MODE_TOTAL) {
            $totalQb         = clone $notPageInfoQb;
            $result['total'] = $this->getConn()->select()->from(new Fragment('(' . $totalQb->__toString() . ') as total'))->count();
        }

        $summaryQb   = clone $qb;
        $summaryCols = $this->searchCols(self::QB_MODE_ALL, true, $params, $groups, $cols);

        if ($mode & self::MODE_SUMMARY_QB) {
            $result['summary_qb'] = $summaryQb;
        }

        if ($mode & self::MODE_SUMMARY) {
            $summaryCols = array_merge($summaryCols, $this->getNodeCols($params));
            $summaryQb->columns($summaryCols);
            @Common::dumpSql($summaryQb->__toString());

            $result['summary'] = ($summaryQb->fetchAll())[0] ?? [];
        }

        return $result;
    }


    /**
     * @param int $qbMode
     * @param array $params
     * @param array $groups
     * @return SelectQuery
     */
    protected function queryBuilder(int $qbMode = -1, array $params = [], array $groups = []): SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from(DdcPlatformTable::DwsPlanAdBaseDaily . ' as t_base');

        $qb
            ->leftJoin('adp_platform.tb_adp_campaign', 'base_campaign')
            ->on([
                't_base.campaign_id'     => 'base_campaign.campaign_id',
                't_base.main_channel_id' => 'base_campaign.channel_id',
            ]);

        if ($qbMode & self::QB_MODE_POWER) {
            $qb
                ->innerJoin(new Fragment(str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql())), 'power')
                ->on([
                    't_base.package_id' => 'power.package_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_PAYMENT) {
            $qb
                ->leftJoin(DdcPlatformTable::DwsPlanAdPaymentDaily, 't_payment')
                ->on([
                    't_base.tday'       => 't_payment.tday',
                    't_base.package_id' => 't_payment.package_id',
                    't_base.channel_id' => 't_payment.channel_id',
                    't_base.plan_id'    => 't_payment.plan_id'
                ]);
        }

        if ($qbMode & self::QB_MODE_AD_CHANNEL) {
            $qb
                ->leftJoin(BaseConfPlatformTable::TbBaseChannelConf, 'ad_channel')
                ->on([
                    't_base.channel_id' => 'ad_channel.channel_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_ADP_OAUTH) {
            $qb
                ->leftJoin(AdpPlatformTable::TbAdpOauth, 'adp_oauth')
                ->on([
                    'adp_oauth.advertiser_id' => 't_base.account_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_AD_USER) {
            $qb
                ->leftJoin(DataSpyTable::AdminUser, 'ad_user')
                ->on([
                    'ad_user.id' => 't_base.user_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_AD_ACCOUNT) {
            $qb
                ->leftJoin(BaseConfPlatformTable::TbAdAccountConf, 'base_account')
                ->on([
                    'base_account.account_id' => 't_base.account_id',
                    'base_account.status'     => new Fragment('1')
                ]);
        }


        return $qb;
    }

    /**
     * 生成带特定未到天数节点数据列(暂时只生成7天内付费用户)
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    protected function getNodeCols(array $params = []): array
    {
        $tDays = $params['tday'] ?? [];
        if (empty($tDays)) {
            throw new \Exception('缺少参数: tday');
        }

        $baseTime   = min($tDays);
        $targetTime = date('Y-m-d');
        $cols       = [];
        $dayDiff    = days_apart($baseTime, $targetTime);

        for ($i = 1; $i <= $dayDiff; $i++) {
            $cols[] = new Fragment("SUM(IF(DATEDIFF(NOW(), t_base.tday) > {$i}, t_base.pay_new_user_7days, 0)) as pay_new_user_7days_{$i}");
        }

        return $cols;
    }

    /**
     * 获取查询的列
     *
     * @param int $qbMode
     * @param bool $isSummary
     * @param array $params
     * @param array $groups
     * @param array $eqCols
     * @return Fragment[]
     * @throws \RedisException
     */
    protected function searchCols(
        int $qbMode = -1, bool $isSummary = false, array $params = [], array $groups = [], array $eqCols = []
    ): array
    {
        static $planChannelsString = [];

        if ($planChannelsString === []) {
            $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
            $planChannelsString = implode(',', $planChannels);
        }

        $channelString = sprintf(
            "IF(power.channel_id NOT IN (%s), power.channel_id, IF(t_base.channel_id != 0, IF(t_base.channel_id=1013, 4, t_base.channel_id), power.channel_id)) AS tztz_promotion_channel_id",
            $planChannelsString
        );

        $channelMainString = sprintf(
            "COALESCE(IF(power.channel_id IN (%s), IF(ad_channel.channel_main_id != 0, ad_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS tztz_channel_main_id",
            $planChannelsString
        );

        $departmentString = sprintf(
            "COALESCE(IF(`power`.`channel_id` IN (%s), `ad_user`.`department_id`, `power`.`ad_department_id`), 0)   AS `tztz_department_id`",
            $planChannelsString
        );

        $adUserString = sprintf(
            "COALESCE(IF(`power`.`channel_id` IN (%s), `t_base`.`USER_ID`, `POWER`.`AD_USER_ID`),0) AS  `tztz_user_id`",
            $planChannelsString
        );

        $infoColsMap = [
            'link_mark'                 => new Fragment('1 as link_mark'), // 关联标识
            'tday'                      => 't_base.tday as tday',
            'cp_game_id'                => 't_base.cp_game_id',
            'game_id'                   => 't_base.game_id as game_id',
            'app_show_id'               => 'power.app_show_id as app_show_id',
            'platform_id'               => 'power.platform_id as platform_id',
            'tztz_promotion_channel_id' => new Fragment($channelString),
            'tztz_channel_main_id'      => new Fragment($channelMainString),
            'package_id'                => 't_base.package_id as package_id',
            'promotion_id'              => 'power.popularize_v2_id as promotion_id',
            'campaign_id'               => 't_base.campaign_id as campaign_id',
            'plan_id'                   => 't_base.plan_id as plan_id',
            'tztz_department_id'        => new Fragment($departmentString),
            'tztz_user_id'              => new Fragment($adUserString),
            'account_id'                => 't_base.account_id',
            'account_name'              => 'adp_oauth.advertiser_name as account_name',
            'ad_account'                => 'base_account.ad_account',
            'marketing_goal'            => 't_base.marketing_goal',
            'is_appointment'            => 't_base.is_appointment',
            'is_ad_data'                => 't_base.is_ad_data',
            'dim_user_os'               => new Fragment("case when USER_OS = JSON_ARRAY('ANDROID') then 'ANDROID' when USER_OS = JSON_ARRAY('IOS') then 'IOS' when JSON_CONTAINS(USER_OS, '[\"ANDROID\",\"IOS\"]', '$') = 1 then '混投' else '混投' end as dim_user_os"),
        ];
        $colsFilter  = null;
        if (!empty($eqCols)) {
            $infoColsMap = array_intersect_key($infoColsMap, array_flip($eqCols));
            $colsFilter  = fn($item) => in_array($item, $eqCols);
        }

        $calcColsMap = [
            't_base'    => [
                'cost', 'cost_discount', 'new_user',
                'create_role_new', 'show', 'click',
                'activate', 'convert', 'install',
                'lp_view', 'lp_download', 'download_start',
                'register', 'new_real_user', 'new_user_emulator',
                'activate_device', 'pay_new_user_7days', 'pay_frequency_7days',
                'online_time', 'first_online_time', 'active_user',
                'total_play', 'play_time_per_play', 'play_duration_3s', 'active_pay_intra_day_count',
                'attribution_billing_game_in_app_ltv_1day'
            ],
            't_payment' => [
                'pay_user', 'pay_money', 'pay_count', 'pay_money_new', 'pay_count_new'
            ],
        ];

        if (!($qbMode & self::QB_MODE_PAYMENT)) {
            unset($calcColsMap['t_payment']);
        }

        $cols = [];

        if (!$isSummary) {
            $cols = array_merge($cols, array_values($infoColsMap));
        }
        else {
            $cols = [new Fragment('t_base.update_time as last_update_time')];
        }

        $calcMap = Arr::flatten($calcColsMap);

        if ($colsFilter != null) {
            $calcMap = array_filter($calcMap, $colsFilter);
        }

        // 拼装需要统计的指标列
        if ($isSummary || !empty($groups)) {
            foreach ($calcMap as $item) {
                $cols[] = new Fragment("IFNULL(SUM({$item}), 0) as `{$item}`");
            }
        }
        else {
            foreach ($calcMap as $item) {
                $cols[] = "{$item} as {$item}";
            }
        }

        return $cols;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

}
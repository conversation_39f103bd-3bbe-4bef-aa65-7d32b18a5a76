<?php

namespace app\service;

use PHP_CodeSniffer\Tokenizers\PHP;
use Plus\Net\Http;

/**
 * Class Auth Service
 * <AUTHOR>
 */
class Auth
{
    //config
    private $host = "https://test-basic.910admin.com";
    private $pid = 73;
    private $apiSecret = "wYCvjuZNsSKNjtVIZCBvxSnvdmDfUbXn";
    private $pageNum = 1;
    private $pageSize = 10;

    /**
     * api list
     * @var string[]
     */
    private $apiList = [
        //common
        "getAccessToken"        => ["path" => "/basicapi/v1/getAccessToken", "method" => "GET"],         //get token
        "getUserAuthorizeList"  => ["path" => "/basicapi/v1/authority/authorizeList", "method" => "POST_JSON"], //user auth list
        "getAllDepartmentsList" => ["path" => "/basicapi/v1/department/getAllDepartmentsList", "method" => "GET"], //department list

        //authority
        "getAuthorityList"      => ["path" => "/basicapi/v1/authority/list", "method" => "GET"], //list
        "createAuthority"       => ["path" => "/basicapi/v1/authority/create", "method" => "POST_JSON"],   //create
        "updateAuthority"       => ["path" => "/basicapi/v1/authority/update", "method" => "POST_JSON"],   //update
        "deleteAuthority"       => ["path" => "/basicapi/v1/authority/delete", "method" => "POST_JSON"],   //delete
        "authorizeAuthority"    => ["path" => "/basicapi/v1/authority/authorize", "method" => "POST_JSON"],   //authorize
        "revokeAuthority"       => ["path" => "/basicapi/v1/authority/revoke", "method" => "POST_JSON"],      //revoke
        "isRole"                => ["path" => "/basicapi/v1/role/userIs", "method" => "POST_JSON"],           //role
        "otherAuthorizeList"    => ["path" => "/basicapi/v1/authority/otherAuthorizeList", "method" => "POST_JSON"],

        //role
        "getRoleList"           => ["path" => "/basicapi/v1/role/list", "method" => "GET"], //list
        "createRole"            => ["path" => "/basicapi/v1/role/create", "method" => "POST_JSON"],   //create
        "updateRole"            => ["path" => "/basicapi/v1/role/update", "method" => "POST_JSON"],   //update
        "deleteRole"            => ["path" => "/basicapi/v1/role/delete", "method" => "POST_JSON"],   //delete

        //user
        "getAllUsersList"       => ["path" => "/basicapi/v1/user/getAllUsersList", "method" => "GET"],    //get user list
        "createUser"            => ["path" => "/basicapi/v1/role/addUser", "method" => "POST_JSON"],           //create
        "deleteUser"            => ["path" => "/basicapi/v1/role/delete", "method" => "POST_JSON"],            //delete

        //get user token
        "getUserToken"          => ["path" => "/basicapi/v1/user/username2token", "method" => "GET"],
    ];

    function __call($name, $arguments)
    {
        if (in_array($name, array_keys($this->apiList))) {
            return $this->query($this->apiList[$name]["path"], $arguments[0], $this->apiList[$name]["method"]);
        } else {
            throw new \Exception("方法{$name}不存在");
        }
    }

    /**
     * query
     * @param $url
     * @param $params
     * @param $method
     * @return mixed
     * @throws \Exception
     */
    public function query($url, $params, $method = "GET")
    {
        if (APP_EVN == "PRO") {
            $this->host = "https://basic.910admin.com";
        } else if (APP_EVN == "GRAY") {
            $this->host = "https://basictest.910admin.com";
        }
        $token = $this->getAccessToken();
        if (!isset($params["pageNum"])) {
            $params["pageNum"] = $this->pageNum;
        }
        if (!isset($params["pageSize"])) {
            $params["pageSize"] = $this->pageSize;
        }
        return $this->request($this->host . $url, $params, ["Access-Token:{$token}"], $method);
    }

    /**
     * get token
     * @return mixed
     * @throws \Exception
     */
    public function getAccessToken($flush=false)
    {

        $key = APP_EVN . ":auth_access_token";
        $accessToken = \Plus::$app->redis->get($key);
        if ($accessToken && !$flush) {
            return $accessToken;
        }

        $url    = $this->host . $this->apiList["getAccessToken"]["path"];
        $time   = time() + rand(1, 200);
        $params = [
            "pid"  => $this->pid,
            "time" => $time,
            "sign" => md5(sha1($this->pid . $this->apiSecret . $time)),
        ];
        $data   = $this->request($url, $params, [], $this->apiList["getAccessToken"]["method"]);
        \Plus::$app->redis->set($key, $data["data"], 3600 * 22);
        return $data["data"];
    }

    /**
     * requerst
     * @param $url
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public function request($url, $params = [], $headers = [], $method = "GET")
    {
        $http = new Http($url);
        if ($method == "GET") {
            $result = $http->get($params, $headers);
        } elseif ($method == "POST_JSON") {
            $headers[] = 'Content-Type: application/json';
            $result    = $http->post($params, false, $headers);
        } elseif ($method == "POST") {
            $result = $http->post($params, false, $headers);
        }
        $data = json_decode($result, true);
        if (!$data || $data["code"] != 0) {
            \Plus::$app->log->info(["url" => $url , "params" =>  $params,"header"=>$headers], [], 'oa');
            throw new \Exception($data["message"], 400);
        }
        return $data;
    }

    /**
     * 获取菜单字段权限列表
     * @return array[]
     */
    public function getFields()
    {
        $username = \Plus::$service->admin->getUsername();
        //获取用户所有权限
        $params       = ["userList" => [$username], "type" => ["API", "COLUMN"]];
        $userAuthList = $this->getUserAuthorizeList($params);
        $userAuthList = $userAuthList["data"][$username];

        $columnList = [];
        $apiList    = [];
        foreach ($userAuthList as $item) {
            switch ($item["type"]) {
                case "COLUMN":
                    $columnList[$item["parentId"]][] = $item["data"];
                    break;
                case "API":
                    $apiList[$item["data"]] = ["authorityId" => $item["authorityId"]];
                    break;
            }
        }
        //查找api接口权限字段
        foreach ($apiList as &$item) {
            $item["columns"] = [];
            if (isset($columnList[$item["authorityId"]])) {
                $item["columns"] = $columnList[$item["authorityId"]];
            }
        }
        \Plus::$app->redis->set($username . ":menu_fields", json_encode($apiList));
        return $apiList;
    }
}
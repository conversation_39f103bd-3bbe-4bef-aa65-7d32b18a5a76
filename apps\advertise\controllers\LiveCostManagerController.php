<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\service\AdvertiserData\CostManagerServ;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use http\Exception\RuntimeException;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * 直播消耗金额管理
 */
class LiveCostManagerController extends BaseTableController
{
    use ColumnsInteract;

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = ['tday' => 'ASC'];
        }
        $options = $params->toArray();
        $serv    = new CostManagerServ();
        $infoRe  = $serv->fetchCostInfoForLive(
            $options, [], ['page' => $page, 'page_size' => $pageSize], $sort, $serv::MODE_ALL ^ $serv::MODE_SUMMARY
        );

        $list             = &$infoRe['list'];
        $generalServ      = new GeneralOptionServ();
        $constConfCollect = (new BasicServ())->getMultiOptions(['game_id'])
            ->put('channel_id', $generalServ->listChannelOptions())
            ->put('cost_type', collect($generalServ->listLiveCostType()));

        $replaceFn = $this->replaceColumnDefine($constConfCollect);
        $process   = new ProcessLine();
        $process->addProcess($replaceFn);
        $process->run($list);

        return $infoRe;
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'cost_index', 'label' => '成本数据'],
                ],
            ],
        ];

        $fields = [
            ['title' => '日期', 'dataIndex' => 'tday', 'classify' => ['attrs', 'base']],
            ['title' => '直播时间段', 'dataIndex' => 'time_range', 'classify' => ['attrs', 'base']],
            ['title' => '账号', 'dataIndex' => 'ad_account_name', 'classify' => ['attrs', 'base']],
            ['title' => '账号ID', 'dataIndex' => 'ad_account_id', 'classify' => ['attrs', 'base']],
            ['title' => '包号', 'dataIndex' => 'package_id', 'classify' => ['attrs', 'base']],
            ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'classify' => ['attrs', 'base']],
            ['title' => '推广子渠道', 'dataIndex' => 'channel_id', 'classify' => ['attrs', 'base']],
            ['title' => '成本类型', 'dataIndex' => 'cost_type', 'classify' => ['attrs', 'base']],
            ['title' => '返点前消耗金额', 'dataIndex' => 'cost', 'classify' => ['attrs', 'cost_index']],
            ['title' => '返点后消耗金额', 'dataIndex' => 'cost_discount', 'classify' => ['attrs', 'cost_index']],
            ['title' => '导入人', 'dataIndex' => 'operator', 'classify' => ['attrs', 'base']],
            ['title' => '导入时间', 'dataIndex' => 'create_time', 'classify' => ['attrs', 'base']],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }


    /**
     * @return array
     */
    public function delAction(): array
    {
        $requestBody = \json_decode(\Plus::$app->request->getRawBody(), true);
        $ids         = $requestBody['id'] ?? null;

        if (empty($ids)) {
            throw new RuntimeException("missing id");
        }

        try {
            (new CostManagerServ())->delForLiveByIds(Arr::wrap($ids));
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }


    /**
     * 通过csv/xls/xlsx
     *
     *
     * @return array
     */
    public function inputByFileAction(): array
    {
        $input = \Plus::$app->request->getFileItem('file');

        if (empty($input)) {
            return $this->error('missing file');
        }

        $fileInfo = $input->save(CACHE_DIR . $input->getFilename());
        $filePath = $fileInfo->getPathname();

        if (empty($filePath)) {
            return $this->error('发生未知错误, 请稍后再试');
        }

        $againstFormats = [
            IOFactory::WRITER_CSV,
            IOFactory::WRITER_XLSX,
            IOFactory::WRITER_XLS,
        ];

        $reader    = IOFactory::load($filePath, 0, $againstFormats);
        $tableData = $reader->getActiveSheet()->toArray();

        if (empty($tableData)) {
            return $this->error("未成功读取文件数据, 请稍后再试");
        }

        try {
            $title = Arr::pull($tableData, 0);
            $title = array_reverse(array_filter(array_reverse($title), fn($value) => $value !== null));

            if ($title != ['日期', '直播时间段', '账号', '账号ID', '包号', '成本类型', '消费金额', '返点后消耗金额']) {
                throw new \InvalidArgumentException('表头需要规范');
            }

            foreach ($tableData as &$item) {
                $item = array_filter($item, fn($value) => $value !== null);

                if (count($item) == 0) {
                    $item = null;
                    continue;
                }

                if (count($item) !== count($title)) {
                    throw new \RuntimeException("表格格式不规范, 请修改后再导入");
                }
                $item = array_combine($title, $item);
            }

            unset($item);
            $tableData = array_filter($tableData);
            (new CostManagerServ())->saveCostInfoForLive($tableData);
            $result = $this->success([]);
        }
        catch (\RuntimeException|\Exception $e) {
            $result = $this->error($e->getMessage(), ['trace' => $e->getTraceAsString()]);
        }
        catch (\Throwable $th) {
            $result = $this->error('未知错误', ['trace' => $th->getTraceAsString()]);
        }
        finally {
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        return $result;
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $indexDay = date('Y-m-d', strtotime('-1day'));

        return collect([
            ['field' => 'range_date_start', 'default' => $indexDay], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $indexDay], // 统计日期结束时间
            ['field' => 'ad_account'], // 主渠道
            ['field' => 'package_id'], // 包号
            ['field' => 'cost_type'], // 成本类型

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }
}
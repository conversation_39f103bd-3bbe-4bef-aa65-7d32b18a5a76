<?php


namespace app\service;

/**
 * Class FileIO
 * @package app\service
 * <AUTHOR>
 */
class FileIO
{
    /**
     * arrayToCsv
     * @param array $dataList 数据集
     * @return void
     */
    public function arrayToCsv(array $dataList)
    {
        header('Content-Type: text/csv');
        $title = $dataList['title'];
        if (isset($dataList['title_extend'])) {
            $title .= "_" . $dataList['title_extend'];
        }
        $filename = $title . '.csv';
        header('Content-Disposition: attachment;filename=' . $filename);
        header('Cache-Control: must-revalidate');
        ob_start();
        $fp     = fopen('php://output', 'a');
        $head   = [];
        $index  = [];
        $fields = $dataList['fields'];
        foreach ($fields as $k => $v) {
            $head[]  = $v['name'];
            $index[] = $k;
        }
        foreach ($head as $k => $v) {
            // $head[$k] = iconv('utf-8', 'gbk', trim($v));
            $head[$k] = trim($v);
        }
        fputcsv($fp, $head);
        $limit   = 1000;
        $count   = 0;
        $content = $dataList['content'];
        foreach ($content as $k => $row) {
            $tmpArr = [];
            foreach ($index as $li) {
                // $tmpArr[] = iconv('utf-8', 'gbk', trim($row[$li]));
                $tmpArr[] = trim($row[$li]);
            }
            $count++;
            fputcsv($fp, $tmpArr);
            unset($content[$k]);
            if ($limit == $count) {
                ob_flush();
                flush();
                $count = 0;
            }
        }
    }
}

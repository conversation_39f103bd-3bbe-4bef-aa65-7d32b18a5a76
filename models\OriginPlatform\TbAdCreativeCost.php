<?php

namespace app\models\OriginPlatform;

use app\models\baseConfPlatform\TbAdAccountExtConf;
use Plus\MVC\Model\ActiveRecord;

/**
 * 广告创意消耗表
 *
 * @property int    id
 * @property string time
 * @property string time_grab
 * @property string update_time
 * @property int    channel_id
 * @property string ad_account_id
 * @property string ad_account
 * @property string ad_login_account
 * @property string plan_id
 * @property int    game_id
 * @property int    cp_game_id
 * @property int    package_id
 */
class TbAdCreativeCost extends ActiveRecord
{
    protected $_primaryKey = 'id';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->origin_platform;
        parent::__construct($data);
    }

    //获取计划信息
    public function getPlanData($plan_id){
        $data = $this->asArray()->find(["PLAN_ID"=>$plan_id,"ORDER"=>["ID"=>"DESC"]],["ad_account_id(advertiser_id)","plan_name","ad_login_account(ad_account)"]);
        if(!$data) return [];
        $accountExtData = (new TbAdAccountExtConf())->asArray()->find(["ADVERTISER_ID"=>$data["advertiser_id"],"ORDER"=>["UPDATE_TIME"=>"DESC"]]);
        $data["user_id"] = $accountExtData["ADV_USER_ID"]??0;
        return $data;
    }
}
<?php


namespace app\extension\Exception;


use Exception;
use Throwable;

class ParameterException extends Exception
{
    /**
     * ParameterException constructor.
     *
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct( string $message = "", int $code = 0, Throwable $previous = null ) {

        $message = $message ?: "参数有误";

        $code    = $code ?: 500;

        parent::__construct($message, $code, $previous);

    }

    /**
     * @param string $message
     * @return $this
     */
    public function message(string $message): ParameterException
    {
        $this->message = $message;

        return $this;
    }
}
<?php

namespace app\service\General\Helpers;

class DdcPlatformTable
{
    const DwdSdkUserPayment           = 'ddc_platform.dwd_sdk_user_payment';
    const DwdSdkDevice                = 'ddc_platform.dwd_sdk_device';
    const DwsPlanAdBaseDaily          = 'ddc_platform.dws_plan_ad_base_daily';
    const DwsPlanAdPaymentDaily       = 'ddc_platform.dws_plan_ad_payment_daily';
    const DwsCreativeAdPayRemainDaily = 'ddc_platform.dws_creative_ad_pay_remain_daily';
    const DwsCreativeAdBaseDaily      = 'ddc_platform.dws_creative_ad_base_daily';
    const DwsCreativeAdPaymentDaily   = 'ddc_platform.dws_creative_ad_payment_daily';
    const DwsPackageBaseDaily         = 'ddc_platform.dws_package_base_daily';
    const DwsPackageBaseHourly        = 'ddc_platform.dws_package_base_hourly';
    const DwsPackageBaseMonth         = 'ddc_platform.dws_package_base_month';
    const DwsPackagePaymentDaily      = 'ddc_platform.dws_package_payment_daily';
    const DwsPackagePaymentHourly     = 'ddc_platform.dws_package_payment_hourly';
    const DwsPackagePaymentMonth      = 'ddc_platform.dws_package_payment_month';
    const DwsFirstloginPayRemainDaily = 'ddc_platform.dws_firstlogin_pay_remain_daily';
    const DwsFirstloginLtvDaily       = 'ddc_platform.dws_firstlogin_ltv_daily';
    const DwsPackageLtvDaily          = 'ddc_platform.dws_package_ltv_daily';
    const DwdSdkRoleFirstlogin        = 'ddc_platform.dwd_sdk_role_firstlogin';
    const DwdUserPaymentUploadVirtual = 'ddc_platform.dwd_user_payment_upload_virtual';
}
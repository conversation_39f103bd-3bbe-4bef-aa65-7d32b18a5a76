<?php

namespace app\service\AdvertiserData\Scheme;

use app\extension\Support\Macroable\Traits\Macroable;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Traits\JoinBase;
use app\service\AdvertiserData\Traits\JoinFixAble;
use app\service\AdvertiserData\Traits\OperableQuery;
use app\service\AdvertiserData\Traits\Schemer;
use Aura\SqlQuery\Common\InsertInterface;
use Aura\SqlQuery\Common\SelectInterface;
use Aura\SqlQuery\Common\UpdateInterface;
use Aura\SqlQuery\QueryFactory;


class BaseScheme implements SchemeContract
{
    use Schemer, OperableQuery, JoinFixAble, JoinBase;

    /**
     * @var BaseScheme|mixed
     */
    protected $mainTable = null;

    /**
     * @var SelectInterface|InsertInterface|UpdateInterface
     */
    protected $query;

    protected $joinTables = [];

    public function __construct()
    {
        $this->query = (new QueryFactory('mysql'))->newSelect();
    }

    /**
     * @param $table
     * @param $alias
     *
     * @return $this
     */
    public function from($table, $alias = null): BaseScheme
    {
        if ($this->isSubTable($table)) {
            return $this->fromSub($table, $alias);
        }

        empty($alias) ?: $table = $table . ' as ' . $alias;

        $this->query->from($table);

        return $this;
    }

    public function fromSub($table, $alias = null): BaseScheme
    {
        ($table instanceof SchemeContract)
            ? $this->query->fromSubSelect($table->toSql(), $alias)
            : $this->query->fromSubSelect($table, $alias);

        return $this;
    }

    /**
     * @param $table
     *
     * @return bool
     */
    private function isSubTable($table): bool
    {
        return ($table instanceof SchemeContract)
            || (is_string($table) && stripos($table, 'select'));
    }

    /**
     * @return void
     */
    public function __clone()
    {
        $this->query = clone $this->query;
    }
}
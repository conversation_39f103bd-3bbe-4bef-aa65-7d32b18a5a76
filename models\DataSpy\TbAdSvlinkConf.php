<?php

namespace app\models\DataSpy;


use Plus\MVC\Model\ActiveRecord;


/**
 * @TbAdSvlinkConf
 *                广告监控链接表
 *
 *
 * @property int ID
 * @property int CP_GAME_ID
 * @property int GAME_ID
 * @property int PACKAGE_ID
 * @property string PLAN_ID
 * @property string AID
 * @property string AD_ACCOUNT
 * @property int OS
 * @property int SV_LINK
 * @property int DOWNLOAD_LINK
 * @property string EXT
 * @property int STATUS
 * @property int IS_STATUS
 * @property int IS_REYUN
 * @property int USER_ID
 * @property int ADD_TIME
 */
class TbAdSvlinkConf extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->dataspy;
        parent::__construct($data);
    }

}
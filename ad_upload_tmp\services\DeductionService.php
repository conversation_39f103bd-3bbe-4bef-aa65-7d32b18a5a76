<?php

namespace app\ad_upload_tmp\services;

use app\ad_upload_tmp\contract\AdBaseInterface;
use app\ad_upload_tmp\contract\DeductionStrategyInterface;
use app\util\BizConfig;

/**
 * 扣量上报 服务
 * <AUTHOR>
 */
class DeductionService
{

    /**
     * 上报配置 缓存
     * @var array
     */
    private $configs = [];
    /**
     * 上报策略
     * @var DeductionStrategyInterface
     */
    private DeductionStrategyInterface $strategy;

    /**
     * 设置策略
     * @param DeductionStrategyInterface $strategy 策略
     * @return void
     */
    public function setStrategy(DeductionStrategyInterface $strategy): void
    {
        $this->strategy = $strategy;
    }

    /**
     * 策略处理
     * @param array $data        data
     * @param int   $report_type 上报类型:上报类型1:sdk上报，2:api上报
     * @return bool
     */
    public function applyDeduction($data, $report_type = 2): bool
    {
        //获取配置, 多条
        $configs = $this->getDeductionConfigs($data, $report_type);
        \Plus::$app->log->info($data["ORDER_ID"] . '|rule:' . json_encode($configs), [], AdBaseInterface::LOG_DIR);
        if (empty($configs)) {
            $this->strategy->init([], $data);
            return false;
        }
        foreach ($configs as $config) {
            //初始化策略传参
            $this->strategy->init($config, $data);
            //判断订单时间是否在策略时间范围内
            if (!$this->strategy->timeCheck()) {
                continue;
            }
            //获取策略处理结果
            if ($this->strategy->isPass()) {
                //上报大数据
                if ($report_type == 2) {
                    $this->strategy->reportBigData();
                }
                return true;
            }
        }
        return false;
    }


    /**
     * 获取扣量上报配置
     * @param array $data        单条数据
     * @param int   $report_type 上报类型:上报类型1:sdk上报，2:api上报
     * @return array|null  多条配置
     */
    private function getDeductionConfigs($data, $report_type = 2)
    {
        $package     = $data['PACKAGE_ID'] ?? '';
        $cpGameId    = $data['CP_GAME_ID'] ?? '';
        $gameId      = $data['GAME_ID'] ?? '';
        $channelId   = $data['CHANNEL_ID'] ?? 0;
        $queryParams = [
            'package_id'  => $package,
            'cp_game_id'  => $cpGameId,
            'game_id'     => $gameId,
            'media_id'    => $channelId,
            'pageNo'      => 1,
            'pageSize'    => 1000,
            'report_type' => $report_type,
            'kind'        => 3,
        ];
        //sdk上报不用 传媒体参数
        if ($report_type == 1) {
            unset($queryParams['media_id']);
        }
        $key       = md5(http_build_query($queryParams));
        $cacheData = \Plus::$app->redis->get($key);
        if ($cacheData) {
            return json_decode($cacheData, true);
        }
        $biz      = new BizConfig();
        $response = $biz->getReportDeduction($queryParams);
        $items    = $response['data']['items'] ?? [];
        $rs       = [];
        foreach ($items as $foo) {
            if ($foo['status'] == 2 && $foo['kind'] == 1 && ($foo['package_id'] == $package || $foo['game_id'] == $gameId)) {
                if ($foo["media_id"] == $channelId) {
                    $rs[] = $foo;
                }
            }
            if ($foo['status'] == 2 && $foo['kind'] == 2 && ($foo['package_id'] == $package || $foo['game_id'] == $gameId)) {
                if ($foo["media_id"] == $channelId) {
                    $rs[] = $foo;
                }
            }
            //自定义上报v2
            if ($foo['status'] == 2 && $foo['kind'] == 3 && ($foo['game_id'] == $gameId)) {
                $rs[] = $foo;
            }
        }
        if (!empty($rs)) {
            foreach ($rs as &$v) {
                $v['config_data'] = json_decode($v['config_data'], true);
            }
        }
        \Plus::$app->redis->set($key, json_encode($rs), 60*10); //缓存10分钟
        return $rs;
    }
}

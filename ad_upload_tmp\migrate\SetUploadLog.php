<?php

namespace app\ad_upload_tmp\migrate;

use app\ad_upload_tmp\tool\CustomProcess;

/**
 * 初始化设置上报点
 * <AUTHOR>
 */
class SetUploadLog extends CustomProcess
{

    /**
     * 数据库
     * @var \Plus\SQL\Db
     */
    private $db;

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->db = \Plus::$app->dataspy;
        //测试环境 tb_ad_upload_log 读写测试环境库
        if (APP_EVN == 'DEV') {
            $this->db = \Plus::$app->dataspy2;
        }
        parent::__construct();
    }

    /**
     * run
     * @return void
     */
    public function run()
    {
        $this->db->insert('tb_ad_upload_log', [
            'channel_id'     => '70560099',
            'action'         => 'active',
            'last_action_id' => strtotime('2025-06-23 00:00:00'),
            'update_time'    => date('Y-m-d H:i:s'),
            'unmatched_ids'  => null,
        ]);

        $this->db->insert('tb_ad_upload_log', [
            'channel_id'     => '70560099',
            'action'         => 'register',
            'last_action_id' => strtotime('2025-06-23 00:00:00'),
            'update_time'    => date('Y-m-d H:i:s'),
            'unmatched_ids'  => json_encode([0]),
        ]);

        $this->db->insert('tb_ad_upload_log', [
            'channel_id'     => '70560099',
            'action'         => 'pay',
            'last_action_id' => strtotime('2025-06-23 00:00:00'),
            'update_time'    => date('Y-m-d H:i:s'),
            'unmatched_ids'  => json_encode([0]),
        ]);
    }
}

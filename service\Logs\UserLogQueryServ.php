<?php

namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\General\Helpers\BaseConfPlatformTable;
use app\service\General\Helpers\BigDataDwdTable;
use app\util\Common;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Query\SelectQuery;


class UserLogQueryServ
{
    /**
     * 获取用户日志总览列表(都以包号维度汇总)
     *
     * @param array $params
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \Exception
     */
    public function getOverviewInfo(
        array $params, array $paginate = [], array $sort = []
    ): array
    {
        $eventMaps = [
            'sdk_activate'      => SdkActivateLogServ::class,
            'user_register'     => SdkUserRegisterLogServ::class,
            'user_login'        => SdkUserLoginLogServ::class,
            'role_login'        => SdkRoleLoginServ::class,
            'user_payment'      => SdkUserPaymentServ::class,
            'sdk_crash'         => SdkCrashServ::class,
            'role_rank'         => SdkRoleRankServ::class,
            'castle_rank'       => SdkCastleRank::class,
            'create_role_first' => SdkRoleFirstLoginServ::class
        ];
        /**
         * @var $unionMaps SelectQuery[]
         */
        $unionMaps  = [];
        $eventTypes = Arr::pull($params, 'event_type', array_keys($eventMaps));

        if (empty($sort)) {
            $sort = ['last_event_time' => 'DESC'];
        }

        foreach ($eventTypes as $ett) {
            if (isset($eventMaps[$ett])) {
                $serv = new $eventMaps[$ett];

                if ($ett == 'user_payment') {
                    $qb = call_user_func_array(
                        [$serv, 'getInfoQueryBuilder'],
                        [
                            $params, [], ['a1.package_id', 'key'],
                            $sort,
                            [
                                'a1.package_id',
                                new Fragment("'user_payment' as `key`"),
                                new Fragment("'user_payment' as `n_key`"),
                                new Fragment('COUNT(1) as log_times'),
                                new Fragment('MAX(pay_time) as last_event_time')
                            ]
                        ]);
                }
                else {
                    $chillCols = [
                        'a1.package_id',
                        new Fragment("'{$ett}' as `key`"),
                        new Fragment("'{$ett}' as `n_key`"),
                        new Fragment('COUNT(1) as log_times'),
                        new Fragment('MAX(time) as last_event_time')
                    ];

                    $qb = call_user_func_array(
                        [$serv, 'getInfoQueryBuilder'], [$params, [], ['package_id', 'n_key'], $sort, $chillCols]
                    );
                }

                $unionMaps[] = $qb;
            }
        }

        $qb = null;

        foreach ($unionMaps as $foo) {
            if ($qb == null) {
                $qb = $foo;
            }
            else {
                $qb->union($foo);
            }
        }

        $overviewQb =
            $this
                ->getConn()
                ->select()
                ->from(new Fragment('(' . $qb->__toString() . ') as ov'))
                ->leftJoin(BaseConfPlatformTable::TbPackageDetailConf, 'bp')
                ->on(['ov.package_id' => 'bp.package_id'])
                ->columns(['ov.*', 'bp.cp_game_id', 'bp.game_id']);

        $totalSubQb = clone $overviewQb;

        if (!empty($paginate)) {
            $page     = $paginate['page'] ?? 1;
            $pageSize = $paginate['page_size'] ?? 100;
            $overviewQb->limit($pageSize)->offset(($page - 1) * $pageSize);
        }

        if (!empty($sort)) {
            $overviewQb->orderBy($sort);
        }

        $total = $this->getConn()->select()->from(new Fragment('(' . $totalSubQb->__toString() . ') as tt'))->count();

        return [
            'list'  => $overviewQb->fetchAll(),
            'total' => $total
        ];
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     * @throws \Exception
     */
    protected function getConn()
    {
        if ($dorisIndex = Common::pingDorisIndex()) {
            return FakeDB::connection($dorisIndex);
        }
        else {
            throw new \RuntimeException('与数据库连接断开');
        }
    }
}
<?php

namespace app\models\OriginPlatform;

use app\models\baseConfPlatform\TbAdAccountConf;
use app\models\baseConfPlatform\TbAdAccountExtConf;
use Plus\MVC\Model\ActiveRecord;

/**
 * 广告消耗表
 *
 * @property int    id
 * @property string time
 * @property string time_grab
 * @property string update_time
 * @property int    channel_id
 * @property string ad_account_id
 * @property string ad_account
 * @property string ad_type
 * @property string aid
 * @property string plan_id
 * @property int    game_id
 * @property int    cp_game_id
 * @property int    package_id
 * @property int    show_cnt
 * @property int    click_cnt
 * @property int    download_cnt
 * @property float  cost
 * @property float  cost_discount
 * @property string ext
 * @property string relate_file
 * @property int    user_i
 * @property int    status
 */
class TbAdCost extends ActiveRecord
{
    protected $_primaryKey = 'id';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->origin_platform;
        parent::__construct($data);
    }

    //获取计划信息
    public function getPlanData($plan_id){
        $data = $this->asArray()->find(["PLAN_ID"=>$plan_id,"ORDER"=>["ID"=>"DESC"]],["ad_account_id(advertiser_id)","aid(plan_name)","ad_account"]);
        if(!$data) return [];
        $accountExtData = (new TbAdAccountExtConf())->asArray()->find(["ADVERTISER_ID"=>$data["advertiser_id"],"ORDER"=>["UPDATE_TIME"=>"DESC"]]);
        $accountData = (new TbAdAccountConf())->findById($accountExtData["AD_ACCOUNT_ID"]);
        $data["user_id"] = $accountExtData["ADV_USER_ID"]??0;
        $data["ad_account"] = $accountData["AD_ACCOUNT"]??'';
        return $data;
    }
}
#!/bin/bash

if [ $# -ne 2 ];then
  echo "用法: $0 <启动脚本> <最小进程数>"
  exit 1
fi

process_name="$1"
min_processes="$2"

check_process() {
  # shellcheck disable=SC2009
  process_num=$(ps aux | grep -v grep | grep -c "$process_name")
  # shellcheck disable=SC2004
  process_count=$(($process_num - 2))
  return "$process_count"
}

start_processes() {
  # shellcheck disable=SC2100
  need_processes=min_processes-running_processes
  # shellcheck disable=SC2039
  for ((i = 1; i <= need_processes; i++)); do
      echo "启动进程 $i..."
#      /usr/local/webserver/php74/bin/php cli.php -f daemon/Universal/"$process_name" -s start
     # shellcheck disable=SC2164
     cd /www/spy.910admin.com/
     /usr/local/webserver/php74/bin/php cli.php -f daemon/Universal/"$process_name"
    done
}

check_process
running_processes="$?"
echo "发现 $running_processes 个进程 $process_name 正在运行。"

if [ "$running_processes" -lt "$min_processes" ]; then
  # 如果运行的进程数小于最小进程数，启动新的进程
  echo "运行的进程数低于最小进程数，启动新进程..."
  start_processes
fi







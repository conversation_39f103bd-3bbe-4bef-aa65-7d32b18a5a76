<?php

namespace app\service\AdvertiserData\Components\Matcher\Traits;

use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

trait AdCampaignAble
{
    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchCampaignId(&$qb, array &$params)
    {
        if (empty($params['campaign_id'])) return;

        $field = $this->getReflectField('campaign_id');
        $data  = $params['campaign_id'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchCampaignName(&$qb, array &$params)
    {
        if (empty($params['campaign_name'])) return;

        $field = $this->getReflectField('campaign_name');
        $data  = $params['campaign_name'];

        $qb->where($field, 'like', "%{$data}%");
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchPlanId(&$qb, array &$params)
    {
        if (!isset($params['plan_id'])) return;

        $field = $this->getReflectField('plan_id');
        $data  = $params['plan_id'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchPlanName(&$qb, array &$params)
    {
        if (empty($params['plan_name'])) return;

        $field = $this->getReflectField('plan_name');
        $data  = $params['plan_name'];

        $qb->where($field, 'like', "%{$data}%");
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchCreativeId(&$qb, array &$params)
    {
        if (empty($params['creative_id'])) return;

        $field = $this->getReflectField('creative_id');
        $data  = $params['creative_id'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchCreativeName(&$qb, array &$params)
    {
        if (empty($params['creative_name'])) return;

        $field = $this->getReflectField('creative_name');
        $data  = $params['creative_name'];

        $qb->where($field, 'like', "%{$data}%");
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchIsHasNatural(&$qb, array &$params)
    {
        if (empty($params['is_has_natural'])) {
            $field = $this->getReflectField('plan_id');
            $qb->where($field, '>', 0);
        }
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchDataScope(&$qb, array &$params)
    {
        if (empty($params['data_scope'])) return;

        $field = $this->getReflectField('is_ad_data');
        $data  = (int)$params['data_scope'];

        if (1 === $data) {
            QueryBuilderHelper::baseBuild($qb, $field, 1);
        }
        elseif (2 === $data) {
            QueryBuilderHelper::baseBuild($qb, $field, 0);
        }
    }


}
<?php

namespace app\logic\operator;

use app\service\OperationData\ConvertConfServ;

class ConvertConfManagerLogic
{
    /**
     * @param array $data
     * @return true
     * @throws \Throwable
     */
    public function save(array $data): bool
    {
        foreach (['ios_packages', 'android_packages'] as $kk) {
            if (!empty($data[$kk])) {
                if (is_array($data[$kk])) {
                    $data[$kk] = json_encode($data[$kk]);
                }
                elseif (is_string($data[$kk]) && str_contains($data[$kk], ',')) {
                    $data[$kk] = json_encode(explode(',', $data[$kk]));
                }
            }
        }

        (new ConvertConfServ())->insertOrUpdate($data);
        $this->getConvertConfList(true);

        return true;
    }

    /**
     * @param bool $reset
     * @return array|mixed|mixed[]|\Redis
     * @throws \RedisException
     */
    public function getConvertConfList(bool $reset = false)
    {
        $key = 'spy:conf:convert_conf_list';

        if ($reset) {
            $data = [];
        }
        else {
            $data = \Plus::$app->redis->hGetAll($key);

            if (!empty($data)) {
                foreach ($data as $kk => $item) {
                    $data[$kk] = json_decode($item, true);
                }
            }

        }

        if (!empty($data)) {
            return $data;
        }

        $result = (new ConvertConfServ())->fetchAll();
        $data   = $result['list'] ?? [];

        if (!empty($data)) {
            $data  = array_column($data, null, 'id');
            $older = \Plus::$app->redis->hGetAll($key);

            if (!empty($older)) {
                foreach ($older as $kk => $item) {
                    \Plus::$app->redis->hDel($key, $kk);
                }
            }
            $cacheData = $data;
            foreach ($cacheData as $kk => $item) {
                $cacheData[$kk] = json_encode($item, JSON_UNESCAPED_UNICODE);
            }

            \Plus::$app->redis->hMSet($key, $cacheData);
        }

        return $data;
    }
}
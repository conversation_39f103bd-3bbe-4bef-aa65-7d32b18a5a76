{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "815b626f10adb9935755c09be347e8d4", "packages": [{"name": "aura/sqlquery", "version": "4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/aura/sqlquery/4.x-dev/aura-sqlquery-4.x-dev.zip", "reference": "f9702f0e8c0f16fdea602d0b1c8dce76a7658f35", "shasum": ""}, "require": {"php": "^7.2 || ^7.3 || ^7.4 || ^8.0 || ^8.1"}, "require-dev": {"phpunit/phpunit": "~8.5"}, "suggest": {"aura/sql": "Provides an extension to the native PDO along with a profiler and connection locator."}, "type": "library", "autoload": {"psr-4": {"Aura\\SqlQuery\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Aura.SqlQuery Contributors", "homepage": "https://github.com/auraphp/Aura.SqlQuery/contributors"}], "description": "Object-oriented query builders for MySQL, Postgres, SQLite, and SQLServer; can be used with any database connection library.", "homepage": "https://github.com/auraphp/Aura.SqlQuery", "keywords": ["database", "db", "delete", "dml", "insert", "mysql", "pdo", "pgsql", "postgres", "postgresql", "query", "select", "sql", "sql server", "sqlite", "sqlserver", "update"], "time": "2021-12-20T01:29:01+00:00"}, {"name": "composer/installers", "version": "1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/composer/installers/1.x-dev/composer-installers-1.x-dev.zip", "reference": "894a0b5c5d34c88b69b097f2aae1439730fa6836", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.3"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}, "plugin-modifies-install-path": true}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "tastyigniter", "typo3", "wordpress", "yawik", "zend", "zikula"], "time": "2022-03-15T21:23:54+00:00"}, {"name": "cycle/annotated", "version": "2.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/cycle/annotated/2.x-dev/cycle-annotated-2.x-dev.zip", "reference": "57be2acd6e962fc11e8a5a2bffe74b523a2a96c6", "shasum": ""}, "require": {"cycle/schema-builder": "^1.2", "doctrine/annotations": "^1.7", "php": ">=7.2", "spiral/attributes": "^2.7", "spiral/tokenizer": "^2.7"}, "require-dev": {"mockery/mockery": "^1.1", "phpunit/phpunit": "~8.0", "spiral/code-style": "^1.0", "spiral/debug": "^2.7"}, "type": "library", "autoload": {"psr-4": {"Cycle\\Annotated\\": "src/"}}, "license": ["MIT"], "description": "Cycle ORM Annotated Entities generator", "time": "2021-11-30T08:33:37+00:00"}, {"name": "cycle/orm", "version": "1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/cycle/orm/1.x-dev/cycle-orm-1.x-dev.zip", "reference": "4f90d7cda5d8a1ab8a245e94c24394edf2e9a8d8", "shasum": ""}, "require": {"doctrine/collections": "^1.5", "doctrine/inflector": "^1.4|^2.0", "doctrine/instantiator": "^1.2", "ext-pdo": "*", "laminas/laminas-hydrator": "^2.4.2|^3.0|^4.0", "php": ">=7.2", "spiral/database": "^2.8.4"}, "require-dev": {"mockery/mockery": "^1.1", "phpunit/phpunit": "~8.0", "ramsey/uuid": "^3.8", "spiral/code-style": "^1.0.6", "spiral/dumper": "^2.7", "spiral/tokenizer": "^2.7"}, "type": "library", "autoload": {"psr-4": {"Cycle\\ORM\\": "src/"}}, "license": ["MIT"], "description": "PHP DataMapper ORM and Data Modelling Engine", "time": "2022-01-17T13:50:40+00:00"}, {"name": "cycle/schema-builder", "version": "1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/cycle/schema-builder/1.x-dev/cycle-schema-builder-1.x-dev.zip", "reference": "4aa96348d021348b32d8fc156e8f8303b9bd2d10", "shasum": ""}, "require": {"cycle/orm": "^1.4", "php": ">=7.2", "yiisoft/friendly-exception": "^1.0"}, "require-dev": {"mockery/mockery": "^1.1", "phpunit/phpunit": "~8.0", "spiral/code-style": "^1.0", "spiral/debug": "^2.7", "spiral/tokenizer": "^2.7"}, "type": "library", "autoload": {"psr-4": {"Cycle\\Schema\\": "src/"}}, "license": ["MIT"], "description": "Cycle ORM Schema Builder", "time": "2022-01-26T17:00:35+00:00"}, {"name": "doctrine/annotations", "version": "1.14.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/doctrine/annotations/1.14.x-dev/doctrine-annotations-1.14.x-dev.zip", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2023-02-01T09:20:38+00:00"}, {"name": "doctrine/collections", "version": "1.8.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/collections/1.8.x-dev/doctrine-collections-1.8.x-dev.zip", "reference": "ca783dc3ff8afe806a16f118b9e4d42cb71b28aa", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "time": "2022-09-29T14:08:59+00:00"}, {"name": "doctrine/deprecations", "version": "v1.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/deprecations/v1.0.0/doctrine-deprecations-v1.0.0.zip", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5|^8.5|^9.5", "psr/log": "^1|^2|^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "time": "2022-05-02T15:47:09+00:00"}, {"name": "doctrine/inflector", "version": "2.1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/doctrine/inflector/2.1.x-dev/doctrine-inflector-2.1.x-dev.zip", "reference": "bf265da9f831e46e646c7a01b59ee8a33f8c0365", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "time": "2023-01-15T14:40:19+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/instantiator/1.5.x-dev/doctrine-instantiator-1.5.x-dev.zip", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2022-12-30T00:15:36+00:00"}, {"name": "doctrine/lexer", "version": "2.1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/lexer/2.1.x-dev/doctrine-lexer-2.1.x-dev.zip", "reference": "e74756f7517d72c238b9163fcd1ed54eb1f92bd0", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2022-12-29T09:22:42+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.16.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/ezyang/htmlpurifier/v4.16.0/ezyang-htmlpurifier-v4.16.0.zip", "reference": "523407fb06eb9e5f3d59889b3978d5bfe94299c8", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2022-09-18T07:06:19+00:00"}, {"name": "firebase/php-jwt", "version": "v6.4.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/firebase/php-jwt/v6.4.0/firebase-php-jwt-v6.4.0.zip", "reference": "4dd1e007f22a927ac77da5a3fbb067b42d3bc224", "shasum": ""}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.5||^7.4", "phpspec/prophecy-phpunit": "^1.1", "phpunit/phpunit": "^7.5||^9.5", "psr/cache": "^1.0||^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "time": "2023-02-09T21:01:23+00:00"}, {"name": "framework/php_crontab", "version": "dev-master", "source": {"type": "git", "url": "https://newgitlab.910app.com/framework/php_crontab.git", "reference": "dc0e2f04bd52c43df524e41605d7ac7b6f78e203"}, "require": {"framework/plus": "3.*", "php": "^7.4", "poliander/cron": "^2.3"}, "type": "library", "autoload": {"psr-4": {"Crontab\\": "src/"}}, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": "2021-10-27T09:17:01+00:00"}, {"name": "framework/plus", "version": "3.0.21", "source": {"type": "git", "url": "https://newgitlab.910app.com/framework/plus.git", "reference": "4bd0e62fdbef75e85c08e03e92c2ddee41c6fb26"}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pdo": "*", "php": "^7.4.3"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.5", "phpunit/phpunit": "^9.5", "symfony/process": "^5.4"}, "suggest": {"ext-gd": "图片处理扩展", "ext-inotify": "文件监控扩展", "ext-pcntl": "进程控制扩展", "ext-posix": "不太常用的库放这里，减少安装依赖", "ext-zlib": "数据压缩扩展"}, "type": "library", "autoload": {"psr-4": {"Plus\\": "src/"}}, "autoload-dev": {"psr-4": {"Plus\\tests\\": "tests/"}}, "scripts": {"test": ["vendor/bin/phpunit"]}, "description": "plus - 手盟php框架", "time": "2024-12-13T06:42:49+00:00"}, {"name": "framework/plus_rabbit", "version": "1.0.6", "source": {"type": "git", "url": "http://newgitlab.910app.com:81/framework/plus3.0/plus_rabbit.git", "reference": "56e48a647566e430049525615fc5c8633fe5dbe1"}, "require": {"ext-pcntl": "*", "ext-sockets": "*", "framework/plus": "3.*", "php": "7.4.*", "php-amqplib/php-amqplib": "^3.3"}, "type": "library", "autoload": {"psr-4": {"framework\\rabbit\\": "src/"}}, "description": "rabbitMQ php client", "time": "2023-01-29T07:29:03+00:00"}, {"name": "guzzlehttp/guzzle", "version": "dev-master", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/guzzlehttp/guzzle/dev-master/guzzlehttp-guzzle-dev-master.zip", "reference": "b964ca597e86b752cd994f27293e9fa6b6a95ed9", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.29 || ^9.5.23", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "time": "2023-04-17T16:30:08+00:00"}, {"name": "guzzlehttp/promises", "version": "dev-master", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/guzzlehttp/promises/dev-master/guzzlehttp-promises-dev-master.zip", "reference": "b94b2807d85443f9719887892882d0329d1e2598", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2022-08-28T14:55:35+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.6.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/guzzlehttp/psr7/2.6.x-dev/guzzlehttp-psr7-2.6.x-dev.zip", "reference": "b635f279edd83fc275f822a1188157ffea568ff6", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2023-04-17T16:11:26+00:00"}, {"name": "ifsnop/mysqldump-php", "version": "v2.12", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/ifsnop/mysqldump-php/v2.12/ifsnop-mysqldump-php-v2.12.zip", "reference": "2d3a43fc0c49f23bf7dee392b0dd1f8c799f89d3", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.8.36", "squizlabs/php_codesniffer": "1.*"}, "type": "library", "autoload": {"psr-4": {"Ifsnop\\": "src/Ifsnop/"}}, "license": ["GPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/ifsnop", "role": "Developer"}], "description": "PHP version of mysqldump cli that comes with MySQL", "homepage": "https://github.com/ifsnop/mysqldump-php", "keywords": ["PHP7", "database", "hhvm", "ma<PERSON>b", "mysql", "mysql-backup", "mysqldump", "pdo", "php", "php5", "sql"], "time": "2023-04-12T07:43:14+00:00"}, {"name": "laminas/laminas-hydrator", "version": "4.5.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/laminas/laminas-hydrator/4.5.x-dev/laminas-laminas-hydrator-4.5.x-dev.zip", "reference": "bc849d46fb44e96dd93f7479e21266185ad3def3", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^3.3", "php": "^7.4 || ~8.0.0 || ~8.1.0", "webmozart/assert": "^1.10"}, "conflict": {"laminas/laminas-servicemanager": "<3.14.0", "zendframework/zend-hydrator": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.3.0", "laminas/laminas-eventmanager": "^3.5.0", "laminas/laminas-modulemanager": "^2.11.0", "laminas/laminas-serializer": "^2.13.0", "laminas/laminas-servicemanager": "^3.14.0", "phpbench/phpbench": "^1.2.5", "phpunit/phpunit": "~9.5.21", "psalm/plugin-phpunit": "^0.17.0", "psr/cache": "1.0.1", "vimeo/psalm": "^4.24.0"}, "suggest": {"laminas/laminas-eventmanager": "^3.2, to support aggregate hydrator usage", "laminas/laminas-serializer": "^2.9, to use the SerializableStrategy", "laminas/laminas-servicemanager": "^3.14, to support hydrator plugin manager usage"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Hydrator", "config-provider": "Laminas\\Hydrator\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Hydrator\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "Serialize objects to arrays, and vice versa", "homepage": "https://laminas.dev", "keywords": ["hydrator", "laminas"], "time": "2022-07-13T13:58:31+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.13.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/laminas/laminas-stdlib/3.13.x-dev/laminas-laminas-stdlib-3.13.x-dev.zip", "reference": "66a6d03c381f6c9f1dd988bf8244f9afb9380d76", "shasum": ""}, "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.3.0", "phpbench/phpbench": "^1.2.6", "phpstan/phpdoc-parser": "^0.5.4", "phpunit/phpunit": "^9.5.23", "psalm/plugin-phpunit": "^0.17.0", "vimeo/psalm": "^4.26"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "time": "2022-08-24T13:56:50+00:00"}, {"name": "langleyfoxall/math_eval", "version": "v2.0.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/langleyfoxall/math_eval/v2.0.0/langleyfoxall-math_eval-v2.0.0.zip", "reference": "f650532d34834dc0af12535f77f194683249bc42", "shasum": ""}, "require": {"mossadal/math-parser": "^1.3", "php": ">=5.6"}, "require-dev": {"fzaninotto/faker": "^1.6", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^5.7"}, "type": "library", "autoload": {"files": ["src/math_eval.php"], "psr-4": {"LangleyFoxall\\MathEval\\": "src/"}}, "license": ["LGPL-3.0-only"], "authors": [{"name": "Jordan Hall", "email": "<EMAIL>"}], "description": "✖️➕➖➗ `math_eval` safely evaluates mathematical expressions", "time": "2019-09-20T08:13:49+00:00"}, {"name": "league/climate", "version": "3.8.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/league/climate/3.8.2/league-climate-3.8.2.zip", "reference": "a785a3ac8f584eed4abd45e4e16fe64c46659a28", "shasum": ""}, "require": {"php": "^7.3 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "seld/cli-prompt": "^1.0"}, "require-dev": {"mikey179/vfsstream": "^1.6.10", "mockery/mockery": "^1.4.2", "phpunit/phpunit": "^9.5.10"}, "suggest": {"ext-mbstring": "If ext-mbstring is not available you MUST install symfony/polyfill-mbstring"}, "type": "library", "autoload": {"psr-4": {"League\\CLImate\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://joe.codes/", "role": "Developer"}, {"name": "<PERSON>", "email": "*****************", "homepage": "https://github.com/duncan3dc", "role": "Developer"}], "description": "PHP's best friend for the terminal. CLImate allows you to easily output colored text, special formats, and more.", "keywords": ["cli", "colors", "command", "php", "terminal"], "time": "2022-06-18T14:42:08+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.2.6", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/maennchen/zipstream-php/2.2.6/maennchen-zipstream-php-2.2.6.zip", "reference": "30ad6f93cf3efe4192bc7a4c9cad11ff8f4f237f", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": "^7.4 || ^8.0", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.9", "guzzlehttp/guzzle": "^6.5.3 || ^7.2.0", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.4", "phpunit/phpunit": "^8.5.8 || ^9.4.2", "vimeo/psalm": "^4.1"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "time": "2022-11-25T18:57:19+00:00"}, {"name": "markbaker/complex", "version": "3.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/markbaker/complex/3.0.x-dev/markbaker-complex-3.0.x-dev.zip", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/markbaker/matrix/3.0.x-dev/markbaker-matrix-3.0.x-dev.zip", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2022-12-02T22:17:43+00:00"}, {"name": "mossadal/math-parser", "version": "v1.3.16", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/mossadal/math-parser/v1.3.16/mossadal-math-parser-v1.3.16.zip", "reference": "981b03ca603fd281049e092d75245ac029e13dec", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpdocumentor/phpdocumentor": "2.*", "phpunit/php-code-coverage": "6.0.*", "phpunit/phpunit": "7.3.*"}, "type": "library", "autoload": {"psr-4": {"MathParser\\": "src/MathParser"}}, "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP parser for mathematical expressions, including elementary functions, variables and implicit multiplication. Also supports symbolic differentiation.", "homepage": "https://github.com/mossadal/math-parser", "keywords": ["mathematics", "parser"], "time": "2018-09-15T22:20:34+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/myclabs/php-enum/1.8.4/myclabs-php-enum-1.8.4.zip", "reference": "a867478eae49c9f59ece437ae7f9506bfaa27483", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "time": "2022-08-04T09:53:51+00:00"}, {"name": "nikic/php-parser", "version": "4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/nikic/php-parser/4.x-dev/nikic-php-parser-4.x-dev.zip", "reference": "0ffddce52d816f72d0efc4d9b02e276d3309ef01", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2023-03-06T22:12:36+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.6.3", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/paragonie/constant_time_encoding/v2.6.3/paragonie-constant_time_encoding-v2.6.3.zip", "reference": "58c3f47f650c94ec05a151692652a868995d2938", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "time": "2022-06-14T06:56:20+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/paragonie/random_compat/v9.99.100/paragonie-random_compat-v9.99.100.zip", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2020-10-15T08:29:30+00:00"}, {"name": "php-amqplib/php-amqplib", "version": "v3.5.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/php-amqplib/php-amqplib/v3.5.3/php-amqplib-php-amqplib-v3.5.3.zip", "reference": "bccaaf8ef8bcf18b4ab41e645e92537752b887bd", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-sockets": "*", "php": "^7.1||^8.0", "phpseclib/phpseclib": "^2.0|^3.0"}, "conflict": {"php": "7.4.0 - 7.4.1"}, "replace": {"videlalvaro/php-amqplib": "self.version"}, "require-dev": {"ext-curl": "*", "nategood/httpful": "^0.2.20", "phpunit/phpunit": "^7.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"PhpAmqpLib\\": "PhpAmqpLib/"}}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "Original Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Formerly videlalvaro/php-amqplib.  This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/php-amqplib/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "time": "2023-04-03T18:25:49+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.28.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/phpoffice/phpspreadsheet/1.28.0/phpoffice-phpspreadsheet-1.28.0.zip", "reference": "6e81cf39bbd93ebc3a4e8150444c41e8aa9b769a", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.15", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^1.0 || ^2.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.2.4", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2023-02-25T12:24:49+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/phpseclib/phpseclib/3.0.x-dev/phpseclib-phpseclib-3.0.x-dev.zip", "reference": "8b67d0ac328c47c6573ca327ab628042e882380c", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "time": "2023-04-13T01:56:23+00:00"}, {"name": "poliander/cron", "version": "2.4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/poliander/cron/2.4.x-dev/poliander-cron-2.4.x-dev.zip", "reference": "e87f4598f250264e969d7a4e3995ecb0766769aa", "shasum": ""}, "require": {"php": "7.4.* || 8.0.* || 8.1.* || 8.2.*"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "license": ["GPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "Standard (V7) compliant crontab expression parser/validator with support for time zones", "homepage": "https://github.com/poliander/cron", "time": "2023-01-20T11:16:32+00:00"}, {"name": "psr/cache", "version": "1.0.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/psr/cache/1.0.1/psr-cache-1.0.1.zip", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "dev-master", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/psr/container/dev-master/psr-container-dev-master.zip", "reference": "90db7b9ac2a2c5b849fcb69dde58f3ae182c68f5", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2022-07-19T17:36:59+00:00"}, {"name": "psr/http-client", "version": "dev-master", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-client/dev-master/psr-http-client-dev-master.zip", "reference": "0955afe48220520692d2d09f7ab7e0f93ffd6a31", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "time": "2023-04-10T20:12:12+00:00"}, {"name": "psr/http-factory", "version": "dev-master", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/psr/http-factory/dev-master/psr-http-factory-dev-master.zip", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "1.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-message/1.1/psr-http-message-1.1.zip", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "1.1.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/log/1.1.4/psr-log-1.1.4.zip", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/psr/simple-cache/1.0.1/psr-simple-cache-1.0.1.zip", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/ralouphie/getallheaders/3.0.3/ralouphie-getallheaders-3.0.3.zip", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "seld/cli-prompt", "version": "1.0.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/seld/cli-prompt/1.0.4/seld-cli-prompt-1.0.4.zip", "reference": "b8dfcf02094b8c03b40322c229493bb2884423c5", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpstan/phpstan": "^0.12.63"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\CliPrompt\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Allows you to prompt for user input on the command line, and optionally hide the characters they type", "keywords": ["cli", "console", "hidden", "input", "prompt"], "time": "2020-12-15T21:32:01+00:00"}, {"name": "smarty/smarty", "version": "dev-master", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/smarty/smarty/dev-master/smarty-smarty-dev-master.zip", "reference": "1da30e76e835b2b8d0c8367fc3df0c5c5163688b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-mbstring": "^1.27"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^7.5", "smarty/smarty-lexer": "^4.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Smarty\\": "src/"}}, "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://www.iwink.nl/"}], "description": "Smarty - the compiling PHP template engine", "homepage": "https://smarty-php.github.io/smarty/", "keywords": ["templating"], "time": "2024-03-25T12:54:02+00:00"}, {"name": "spiral/attributes", "version": "2.14.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/spiral/attributes/2.14.1/spiral-attributes-2.14.1.zip", "reference": "e5470703d6479935c54a78acd1687f9bd09f4c61", "shasum": ""}, "require": {"nikic/php-parser": "^4.1", "php": ">=7.4", "psr/cache": ">=1.0", "psr/simple-cache": "1 - 3"}, "require-dev": {"doctrine/annotations": "^1.12", "jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^8.5|^9.5", "symfony/var-dumper": "^5.2|^6.0"}, "suggest": {"doctrine/annotations": "^1.0 for Doctrine metadata driver support"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.15.x-dev"}}, "autoload": {"files": ["src/polyfill.php"], "psr-4": {"Spiral\\Attributes\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON> (SerafimArts)", "email": "<EMAIL>"}], "description": "PHP attributes reader", "homepage": "https://spiral.dev", "time": "2022-09-01T21:11:15+00:00"}, {"name": "spiral/core", "version": "2.14.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/spiral/core/2.14.1/spiral-core-2.14.1.zip", "reference": "bbc0a4a1d3c4f14d8d7354d94a05577e60129416", "shasum": ""}, "require": {"php": ">=7.4", "psr/container": "^1.1|^2.0"}, "require-dev": {"mockery/mockery": "^1.5", "phpunit/phpunit": "^8.5|^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.15.x-dev"}}, "autoload": {"psr-4": {"Spiral\\Core\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}], "description": "IoC container, IoC scopes, factory, memory, configuration interfaces", "homepage": "https://spiral.dev", "time": "2022-09-01T21:11:51+00:00"}, {"name": "spiral/database", "version": "v2.9.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/spiral/database/v2.9.4/spiral-database-v2.9.4.zip", "reference": "fb1c5a9b57085d10495629ab45d86cb499f68411", "shasum": ""}, "require": {"ext-pdo": "*", "php": ">=7.2", "spiral/core": "^2.7", "spiral/logger": "^2.7", "spiral/pagination": "^2.7"}, "require-dev": {"mockery/mockery": "^1.1", "phpunit/phpunit": "~8.0", "spiral/code-style": "^1.0", "spiral/dumper": "^2.7", "spiral/tokenizer": "^2.7"}, "type": "library", "autoload": {"psr-4": {"Spiral\\Database\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}], "description": "DBAL, schema introspection, migration and pagination", "time": "2022-07-05T08:20:53+00:00"}, {"name": "spiral/logger", "version": "2.14.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/spiral/logger/2.14.1/spiral-logger-2.14.1.zip", "reference": "14ea05f8a24a5d4ecb3b8811e34948d3a735576e", "shasum": ""}, "require": {"php": ">=7.4", "psr/log": "1 - 3", "spiral/core": "^2.14.1"}, "require-dev": {"mockery/mockery": "^1.5", "phpunit/phpunit": "^8.5|^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.15.x-dev"}}, "autoload": {"psr-4": {"Spiral\\Logger\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}], "description": "LogFactory and global log listeners", "homepage": "https://spiral.dev", "time": "2022-09-12T15:13:31+00:00"}, {"name": "spiral/pagination", "version": "2.14.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/spiral/pagination/2.14.1/spiral-pagination-2.14.1.zip", "reference": "bf4d8b1fbd21544eb9d511bbba57775de5ba3c5f", "shasum": ""}, "require": {"php": ">=7.4"}, "require-dev": {"mockery/mockery": "^1.5", "phpunit/phpunit": "^8.5|^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.15.x-dev"}}, "autoload": {"psr-4": {"Spiral\\Pagination\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}], "description": "Common pagination interfaces", "homepage": "https://spiral.dev", "time": "2022-09-01T21:12:15+00:00"}, {"name": "spiral/tokenizer", "version": "2.14.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/spiral/tokenizer/2.14.1/spiral-tokenizer-2.14.1.zip", "reference": "45f704219f115fb2aa3b384aa83ed8ec34cff9ca", "shasum": ""}, "require": {"php": ">=7.4", "spiral/core": "^2.14.1", "spiral/logger": "^2.14.1", "symfony/finder": "^5.1|^6.0"}, "require-dev": {"phpunit/phpunit": "^8.5|^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.15.x-dev"}}, "autoload": {"psr-4": {"Spiral\\Tokenizer\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}], "description": "Static Analysis: Class and Invocation locators", "homepage": "https://spiral.dev", "time": "2022-09-12T15:14:10+00:00"}, {"name": "symfony/deprecation-contracts", "version": "2.5.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/deprecation-contracts/2.5.x-dev/symfony-deprecation-contracts-2.5.x-dev.zip", "reference": "80d075412b557d41002320b96a096ca65aa2c98d", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "time": "2023-01-24T14:02:46+00:00"}, {"name": "symfony/finder", "version": "5.4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/finder/5.4.x-dev/symfony-finder-5.4.x-dev.zip", "reference": "078e9a5e1871fcfe6a5ce421b539344c21afef19", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "time": "2023-02-16T09:33:00+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "dev-main", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/polyfill-mbstring/dev-main/symfony-polyfill-mbstring-dev-main.zip", "reference": "f9c7affe77a00ae32ca127ca6833d034e6d33f25", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2023-01-30T17:25:47+00:00"}, {"name": "symfony/polyfill-php80", "version": "dev-main", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/polyfill-php80/dev-main/symfony-polyfill-php80-dev-main.zip", "reference": "6caa57379c4aec19c0a12a38b59b26487dcfe4b5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2023-01-26T09:26:14+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/webmozart/assert/1.11.0/webmozart-assert-1.11.0.zip", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2022-06-03T18:03:27+00:00"}, {"name": "yiisoft/friendly-exception", "version": "1.1.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/yiisoft/friendly-exception/1.1.0/yiisoft-friendly-exception-1.1.0.zip", "reference": "4b4a19edff251791e3c92d4d83435d2716351ff4", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.4", "roave/infection-static-analysis-plugin": "^1.5", "spatie/phpunit-watcher": "^1.23", "vimeo/psalm": "^4.3"}, "type": "library", "autoload": {"psr-4": {"Yiisoft\\FriendlyException\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "An interface for friendlier exception", "homepage": "http://www.yiiframework.com/", "keywords": ["error handling", "exception", "exceptions", "friendly"], "time": "2021-10-26T21:43:25+00:00"}], "packages-dev": [{"name": "behat/gherkin", "version": "v4.10.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/behat/gherkin/v4.10.0/behat-gherkin-v4.10.0.zip", "reference": "cbb83c4c435dd8d05a161f2a5ae322e61b2f4db6", "shasum": ""}, "require": {"php": "~7.2|~8.0"}, "require-dev": {"cucumber/cucumber": "dev-gherkin-24.1.0", "phpunit/phpunit": "~8|~9", "symfony/yaml": "~3|~4|~5|~6|~7"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-0": {"Behat\\Gherkin": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Gherkin DSL parser for PHP", "homepage": "http://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "support": {"issues": "https://github.com/Behat/Gher<PERSON>/issues", "source": "https://github.com/Behat/Gherkin/tree/v4.10.0"}, "time": "2024-10-19T14:46:06+00:00"}, {"name": "codeception/codeception", "version": "4.2.3-beta4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/codeception/codeception/4.2.3-beta4/codeception-codeception-4.2.3-beta4.zip", "reference": "93bb57fea2a13d15ff40b402db8a1fd619cdf49a", "shasum": ""}, "require": {"behat/gherkin": "^4.4.0", "codeception/lib-asserts": "^1.0 | 2.0.*@dev", "codeception/phpunit-wrapper": ">6.0.15 <6.1.0 | ^6.6.1 | ^7.7.1 | ^8.1.1 | ^9.0", "codeception/stub": "^2.0 | ^3.0 | ^4.0", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/psr7": "^1.4 | ^2.0", "php": ">=5.6.0 <9.0", "symfony/console": ">=2.7 <6.0", "symfony/css-selector": ">=2.7 <6.0", "symfony/event-dispatcher": ">=2.7 <6.0", "symfony/finder": ">=2.7 <6.0", "symfony/yaml": ">=2.7 <6.0"}, "require-dev": {"codeception/module-asserts": "^1.0 | 2.0.*@dev", "codeception/module-cli": "^1.0 | 2.0.*@dev", "codeception/module-db": "^1.0 | 2.0.*@dev", "codeception/module-filesystem": "^1.0 | 2.0.*@dev", "codeception/module-phpbrowser": "^1.0 | 2.0.*@dev", "codeception/specify": "~0.3", "codeception/util-universalframework": "*@dev", "monolog/monolog": "~1.8", "squizlabs/php_codesniffer": "~2.0", "symfony/process": ">=2.7 <6.0", "vlucas/phpdotenv": "^2.0 | ^3.0 | ^4.0 | ^5.0"}, "suggest": {"codeception/specify": "BDD-style code blocks", "codeception/verify": "BDD-style assertions", "hoa/console": "For interactive console functionality", "stecman/symfony-console-completion": "For BASH autocompletion", "symfony/phpunit-bridge": "For phpunit-bridge support"}, "bin": ["codecept"], "type": "library", "extra": {"branch-alias": []}, "autoload": {"files": ["functions.php"], "psr-4": {"Codeception\\": "src/Codeception", "Codeception\\Extension\\": "ext"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://codegyre.com"}], "description": "BDD-style testing framework", "homepage": "https://codeception.com/", "keywords": ["BDD", "TDD", "acceptance testing", "functional testing", "unit testing"], "support": {"issues": "https://github.com/Codeception/Codeception/issues", "source": "https://github.com/Codeception/Codeception/tree/4.2.3-beta4"}, "time": "2022-09-03T14:30:12+00:00"}, {"name": "codeception/lib-asserts", "version": "2.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/codeception/lib-asserts/2.0.x-dev/codeception-lib-asserts-2.0.x-dev.zip", "reference": "78c55044611437988b54e1daecf13f247a742bf8", "shasum": ""}, "require": {"codeception/phpunit-wrapper": "^7.7.1 | ^8.0.3 | ^9.0", "ext-dom": "*", "php": "^7.4 | ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "description": "Assertion methods used by Codeception core and Asserts module", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-asserts/issues", "source": "https://github.com/Codeception/lib-asserts/tree/2.0"}, "time": "2022-09-27T06:17:39+00:00"}, {"name": "codeception/lib-innerbrowser", "version": "2.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/codeception/lib-innerbrowser/2.0.x-dev/codeception-lib-innerbrowser-2.0.x-dev.zip", "reference": "108679cd01a297df6f9e3e6e4467a8b06f708b34", "shasum": ""}, "require": {"codeception/codeception": "^4.1 | 4.*@dev", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "php": "^7.4 | ^8.0", "symfony/browser-kit": "^4.4 | ^5.4 | ^6.0", "symfony/dom-crawler": "^4.4 | ^5.4 | ^6.0"}, "conflict": {"codeception/codeception": "<4.1"}, "require-dev": {"codeception/util-universalframework": "dev-master"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Parent library for all Codeception framework modules and PhpBrowser", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-innerbrowser/issues", "source": "https://github.com/Codeception/lib-innerbrowser/tree/2.0.2"}, "time": "2022-01-27T15:55:51+00:00"}, {"name": "codeception/module-asserts", "version": "2.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/codeception/module-asserts/2.0.x-dev/codeception-module-asserts-2.0.x-dev.zip", "reference": "02d8bd327bedceaa7dda5e3c7a80e728fd8679a5", "shasum": ""}, "require": {"codeception/codeception": "^4.1 | *@dev", "codeception/lib-asserts": "^2.0", "php": "^7.4 | ^8.0"}, "conflict": {"codeception/codeception": "<4.1"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "description": "Codeception module containing various assertions", "homepage": "https://codeception.com/", "keywords": ["assertions", "asserts", "codeception"], "support": {"issues": "https://github.com/Codeception/module-asserts/issues", "source": "https://github.com/Codeception/module-asserts/tree/master"}, "time": "2022-01-14T17:44:51+00:00"}, {"name": "codeception/module-phpbrowser", "version": "2.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/codeception/module-phpbrowser/2.0.x-dev/codeception-module-phpbrowser-2.0.x-dev.zip", "reference": "21338dc28ac82a63344224cc765a8ff2964b4a84", "shasum": ""}, "require": {"codeception/codeception": "^4.1", "codeception/lib-innerbrowser": "^2.0", "ext-json": "*", "guzzlehttp/guzzle": "^7.4", "php": "^7.4 | ^8.0"}, "conflict": {"codeception/codeception": "<4.1"}, "require-dev": {"aws/aws-sdk-php": "^3.199", "codeception/module-rest": "^2.0", "ext-curl": "*"}, "suggest": {"codeception/phpbuiltinserver": "Start and stop PHP built-in web server for your tests"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Codeception module for testing web application over HTTP", "homepage": "https://codeception.com/", "keywords": ["codeception", "functional-testing", "http"], "support": {"issues": "https://github.com/Codeception/module-phpbrowser/issues", "source": "https://github.com/Codeception/module-phpbrowser/tree/2.0"}, "time": "2022-05-21T13:48:15+00:00"}, {"name": "codeception/phpunit-wrapper", "version": "9.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/codeception/phpunit-wrapper/9.0.x-dev/codeception-phpunit-wrapper-9.0.x-dev.zip", "reference": "7439a53ae367986e9c22b2ac00f9d7376bb2f8cf", "shasum": ""}, "require": {"php": ">=7.2", "phpunit/phpunit": "^9.0"}, "require-dev": {"codeception/specify": "*", "consolidation/robo": "^3.0.0-alpha3", "vlucas/phpdotenv": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\PHPUnit\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "PHPUnit classes used by Codeception", "support": {"issues": "https://github.com/Codeception/phpunit-wrapper/issues", "source": "https://github.com/Codeception/phpunit-wrapper/tree/9.0.9"}, "time": "2022-05-23T06:24:11+00:00"}, {"name": "codeception/stub", "version": "4.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/codeception/stub/4.0.2/codeception-stub-4.0.2.zip", "reference": "18a148dacd293fc7b044042f5aa63a82b08bff5d", "shasum": ""}, "require": {"php": "^7.4 | ^8.0", "phpunit/phpunit": "^8.4 | ^9.0 | ^10.0 | 10.0.x-dev"}, "require-dev": {"consolidation/robo": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\": "src/"}}, "license": ["MIT"], "description": "Flexible Stub wrapper for PHPUnit's Mock Builder", "support": {"issues": "https://github.com/Codeception/Stub/issues", "source": "https://github.com/Codeception/Stub/tree/4.0.2"}, "time": "2022-01-31T19:25:15+00:00"}, {"name": "framework/phplint", "version": "1.0.8", "source": {"type": "git", "url": "https://newgitlab.910app.com/framework/phplint.git", "reference": "79d6578873d54ffb676f9f6ee8f47532bc6bb9f3"}, "bin": ["bin/phpcs.phar", "bin/phpcbf.phar"], "type": "library", "autoload": {"psr-4": {"lint\\": ""}}, "scripts": {"post-install-cmd": ["lint\\Hook::install"]}, "description": "php代码检查", "time": "2023-03-23T10:55:36+00:00"}, {"name": "myclabs/deep-copy", "version": "1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/myclabs/deep-copy/1.x-dev/myclabs-deep-copy-1.x-dev.zip", "reference": "4764e040f8743e92b86c36f488f32d0265dd1dae", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2024-11-26T13:04:49+00:00"}, {"name": "phar-io/manifest", "version": "dev-master", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phar-io/manifest/dev-master/phar-io-manifest-dev-master.zip", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phar-io/version/3.2.1/phar-io-version-3.2.1.zip", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/phpunit/php-code-coverage/9.2.x-dev/phpunit-php-code-coverage-9.2.x-dev.zip", "reference": "0448d60087a382392a1b2a1abe434466e03dcc87", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-text-template": "^2.0.4", "sebastian/code-unit-reverse-lookup": "^2.0.3", "sebastian/complexity": "^2.0.3", "sebastian/environment": "^5.1.5", "sebastian/lines-of-code": "^1.0.4", "sebastian/version": "^3.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2024-10-31T05:58:25+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/php-file-iterator/3.0.x-dev/phpunit-php-file-iterator-3.0.x-dev.zip", "reference": "38b24367e1b340aa78b96d7cab042942d917bb84", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2022-02-11T16:23:04+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/phpunit/php-invoker/3.1.1/phpunit-php-invoker-3.1.1.zip", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/php-text-template/2.0.4/phpunit-php-text-template-2.0.4.zip", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/php-timer/5.0.3/phpunit-php-timer-5.0.3.zip", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.6.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpunit/phpunit/9.6.x-dev/phpunit-phpunit-9.6.x-dev.zip", "reference": "8afd8554c471373b47b5a59138e8feea69e60bfe", "shasum": ""}, "require": {"doctrine/instantiator": "^1.5.0 || ^2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.12.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.32", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.4", "phpunit/php-timer": "^5.0.3", "sebastian/cli-parser": "^1.0.2", "sebastian/code-unit": "^1.0.8", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.6", "sebastian/environment": "^5.1.5", "sebastian/exporter": "^4.0.6", "sebastian/global-state": "^5.0.7", "sebastian/object-enumerator": "^4.0.4", "sebastian/resource-operations": "^3.0.4", "sebastian/type": "^3.2.1", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.6-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2024-12-20T07:00:31+00:00"}, {"name": "psr/event-dispatcher", "version": "dev-master", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/psr/event-dispatcher/dev-master/psr-event-dispatcher-dev-master.zip", "reference": "bbd9eacc080d33861e5b5c75b3b8c4d7e6d01874", "shasum": ""}, "require": {"php": ">=7.2.0"}, "suggest": {"fig/event-dispatcher-util": "Provides some useful PSR-14 utilities"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"source": "https://github.com/php-fig/event-dispatcher"}, "time": "2024-03-17T21:29:03+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/cli-parser/1.0.x-dev/sebastian-cli-parser-1.0.x-dev.zip", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "time": "2024-03-02T06:27:43+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/code-unit/1.0.8/sebastian-code-unit-1.0.8.zip", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/sebastian/code-unit-reverse-lookup/2.0.3/sebastian-code-unit-reverse-lookup-2.0.3.zip", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/comparator/4.0.x-dev/sebastian-comparator-4.0.x-dev.zip", "reference": "b247957a1c8dc81a671770f74b479c0a78a818f1", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2022-09-14T12:46:14+00:00"}, {"name": "sebastian/complexity", "version": "2.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/complexity/2.0.x-dev/sebastian-complexity-2.0.x-dev.zip", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "time": "2023-12-22T06:19:30+00:00"}, {"name": "sebastian/diff", "version": "4.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/diff/4.0.x-dev/sebastian-diff-4.0.x-dev.zip", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "time": "2024-03-02T06:30:58+00:00"}, {"name": "sebastian/environment", "version": "5.1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/environment/5.1.x-dev/sebastian-environment-5.1.x-dev.zip", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2023-02-03T06:03:51+00:00"}, {"name": "sebastian/exporter", "version": "4.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/exporter/4.0.x-dev/sebastian-exporter-4.0.x-dev.zip", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2024-03-02T06:33:00+00:00"}, {"name": "sebastian/global-state", "version": "5.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/global-state/5.0.x-dev/sebastian-global-state-5.0.x-dev.zip", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2024-03-02T06:35:11+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/lines-of-code/1.0.x-dev/sebastian-lines-of-code-1.0.x-dev.zip", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "time": "2023-12-22T06:20:34+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/object-enumerator/4.0.4/sebastian-object-enumerator-4.0.4.zip", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/object-reflector/2.0.4/sebastian-object-reflector-2.0.4.zip", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/recursion-context/4.0.x-dev/sebastian-recursion-context-4.0.x-dev.zip", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "time": "2023-02-03T06:07:39+00:00"}, {"name": "sebastian/resource-operations", "version": "dev-main", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/resource-operations/dev-main/sebastian-resource-operations-dev-main.zip", "reference": "ff553e7482dcee39fa4acc2b175d6ddeb0f7bc25", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2024-03-14T18:47:08+00:00"}, {"name": "sebastian/type", "version": "3.2.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/type/3.2.x-dev/sebastian-type-3.2.x-dev.zip", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "time": "2023-02-03T06:13:03+00:00"}, {"name": "sebastian/version", "version": "3.0.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/sebastian/version/3.0.x-dev/sebastian-version-3.0.x-dev.zip", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2020-09-28T06:39:44+00:00"}, {"name": "symfony/browser-kit", "version": "5.4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/browser-kit/5.4.x-dev/symfony-browser-kit-5.4.x-dev.zip", "reference": "03cce39764429e07fbab9b989a1182a24578341d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/dom-crawler": "^4.4|^5.0|^6.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/css-selector": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Simulates the behavior of a web browser, allowing you to make requests, click on links and submit forms programmatically", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/5.4"}, "time": "2024-10-22T13:05:35+00:00"}, {"name": "symfony/console", "version": "5.4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/console/5.4.x-dev/symfony-console-5.4.x-dev.zip", "reference": "c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/5.4"}, "time": "2024-11-06T11:30:55+00:00"}, {"name": "symfony/css-selector", "version": "5.4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/css-selector/5.4.x-dev/symfony-css-selector-5.4.x-dev.zip", "reference": "4f7f3c35fba88146b56d0025d20ace3f3901f097", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/5.4"}, "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/dom-crawler", "version": "5.4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/dom-crawler/5.4.x-dev/symfony-dom-crawler-5.4.x-dev.zip", "reference": "b57df76f4757a9a8dfbb57ba48d7780cc20776c6", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/5.4"}, "time": "2024-11-13T14:36:38+00:00"}, {"name": "symfony/event-dispatcher", "version": "5.4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/event-dispatcher/5.4.x-dev/symfony-event-dispatcher-5.4.x-dev.zip", "reference": "72982eb416f61003e9bb6e91f8b3213600dcf9e9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/5.4"}, "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "2.5.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/event-dispatcher-contracts/2.5.x-dev/symfony-event-dispatcher-contracts-2.5.x-dev.zip", "reference": "e0fe3d79b516eb75126ac6fa4cbf19b79b08c99f", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/2.5"}, "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/polyfill-ctype", "version": "1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/polyfill-ctype/1.x-dev/symfony-polyfill-ctype-1.x-dev.zip", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-intl-grapheme/1.x-dev/symfony-polyfill-intl-grapheme-1.x-dev.zip", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-intl-normalizer/1.x-dev/symfony-polyfill-intl-normalizer-1.x-dev.zip", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php73", "version": "1.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-php73/1.x-dev/symfony-polyfill-php73-1.x-dev.zip", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.32.0"}, "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/service-contracts", "version": "v1.1.2", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/service-contracts/v1.1.2/symfony-service-contracts-v1.1.2.zip", "reference": "191afdcb5804db960d26d8566b7e9a2843cab3a0", "shasum": ""}, "require": {"php": "^7.1.3"}, "suggest": {"psr/container": "", "symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v1.1.2"}, "time": "2019-05-28T07:50:59+00:00"}, {"name": "symfony/string", "version": "5.4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/string/5.4.x-dev/symfony-string-5.4.x-dev.zip", "reference": "136ca7d72f72b599f2631aca474a4f8e26719799", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/5.4"}, "time": "2024-11-10T20:33:58+00:00"}, {"name": "symfony/yaml", "version": "5.4.x-dev", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/yaml/5.4.x-dev/symfony-yaml-5.4.x-dev.zip", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/5.4"}, "time": "2024-09-25T14:11:13+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/theseer/tokenizer/1.2.3/theseer-tokenizer-1.2.3.zip", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"aura/sqlquery": 20, "cycle/orm": 20, "cycle/annotated": 20, "guzzlehttp/guzzle": 20, "framework/php_crontab": 20, "smarty/smarty": 20, "phpunit/phpunit": 20, "codeception/codeception": 10, "codeception/module-phpbrowser": 20, "codeception/module-asserts": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.4.3", "ext-json": "*", "ext-pdo": "*", "ext-mbstring": "*", "ext-pcntl": "*", "ext-posix": "*", "ext-redis": "*"}, "platform-dev": [], "plugin-api-version": "2.1.0"}
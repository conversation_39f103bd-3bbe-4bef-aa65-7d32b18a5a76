<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;
use app\service\General\BizTagsServ;
use app\util\Common;
use Smarty\Exception;

class AdPayPermeationProvider
{
    const RESULT_INFO    = 1;
    const RESULT_SUMMARY = 2;
    const RESULT_ALL     = 3;

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $columns
     * @param int   $resultMode
     * @param bool  $isApiRule
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function dashList(
        array $params, array $groups = [], array $paginate = [],
        array $sort = [], array $columns = [], int $resultMode = self::RESULT_ALL, bool $isApiRule = false
    ): array
    {
        $result   = [];
        $powerSQL = null;
        $db       = $this->dorisConn();
        $tplMap   = [
            'info' => 'sql/advertise/ad_dashboard/creative_dash_pay_permeation.tpl',
        ];

        $adChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');

        if (!$isApiRule) {
            $powerSQL = \Plus::$service->admin->powerSubSQL();
        }

        if ($resultMode & self::RESULT_INFO) {
            $infoTPL = \Plus::$app->sqlTemplates->createTemplate($tplMap['info']);
            $infoTPL
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params)
                ->assign('groups', $groups);

            if (!empty($powerSQL)) {
                $infoTPL->assign('power_join_sql', $powerSQL);
            }

            if (!empty($columns)) {
                $infoTPL->assign('columns', $columns);
            }

            $infoSQL = $infoTPL->fetch();
            @Common::dumpSql($infoSQL);
            $result['list'] = $db->query($infoSQL)->fetchAll();
        }

        if ($resultMode & self::RESULT_SUMMARY) {
            $summaryTPL = \Plus::$app->sqlTemplates->createTemplate($tplMap['info']);
            $summaryTPL
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params);

            if (!empty($powerSQL)) {
                $summaryTPL->assign('power_join_sql', $powerSQL);
            }
            if (!empty($columns)) {
                $summaryTPL->assign('columns', $columns);
            }

            $summarySQL = $summaryTPL->fetch();
            @Common::dumpSql($summarySQL);

            $summaryResult     = $db->query($summarySQL)->fetch();
            $result['summary'] = $summaryResult;
        }

        return $result;
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    protected function dorisConn()
    {
        return FakeDB::connection('doris_entrance');
    }
}
<?php

//判断当前环境：灰度生产环境：PRO(默认) ，测试环境：TEST，本地环境：DEV
$env = getenv('RUNTIME_ENV') ?: 'PRO';

$appDebug   = $sqlDebug = true;
if (isset($_SERVER['HTTP_HOST'])) {
    if ($_SERVER['HTTP_HOST'] == 'spy.910admin.com') {
        $env = 'PRO';
        $appDebug = $sqlDebug = false;
    } elseif ((isset($_SERVER['SERVER_ADDR']) && $_SERVER['SERVER_ADDR'] == '127.0.0.1') ||
        $_SERVER['HTTP_HOST'] == 'test-spy.910admin.com'
    ) {
        $env = 'TEST';
    }elseif ($_SERVER['HTTP_HOST'] == 'gray-spy.910admin.com'){
        $env = 'GRAY';
    }
}

define('APP_EVN',$env);
define('APP_DEBUG',$appDebug);

// 环境变量
$env = getenv('RUNTIME_ENV');
defined('APP_DEBUG') or define('APP_DEBUG', false);  // 是否调试
defined('APP_EVN') or define('APP_EVN',  !$env ? 'DEV' : $env);  // PRO灰度生产环境,DEV开发,TEST测试


return [
    'APP_DEBUG'  => APP_DEBUG,
    'APP_EVN'    => APP_EVN
];

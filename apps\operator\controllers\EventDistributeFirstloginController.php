<?php

namespace app\apps\operator\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\operator\Helpers\EventDistributeExt;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use app\service\OperationData\EventDistributionServ;

/**
 * 用户事件分布(首登维度)
 *
 */
class EventDistributeFirstloginController extends BaseTableController
{
    use ColumnsInteract;

    /**
     * 数据主体
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];
        $groups    = $this->groupAdRelation(Arr::wrap($params->pull('groups')));
        $eventType = $params->get('event_type');
        $groupType = (int)$params->get('group_type');

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            if ($eventType > 990) {
                $sort = in_array('tday', $groups)
                    ? ['tday' => 'ASC', 'y_event_sum' => 'DESC']
                    : ['y_event_sum' => 'DESC', 'x_event_sum' => 'DESC'];
            }
            else {
                $sort = in_array('tday', $groups)
                    ? ['tday' => 'ASC', 'event_sum' => 'DESC']
                    : ['event_sum' => 'DESC'];
            }
        }

        $options = $params->toArray();
        $serv    = new EventDistributionServ();
        $infoRe  = $serv->getList($serv::DIMENSION_FIRST_LOG, $options, $groups, ['page' => $page, 'page_size' => $pageSize], $sort);

        $list        = &$infoRe['list'];
        $summaryRow  = &$infoRe['summary'];
        $wrapSummary = [&$summaryRow];

        $constConfCollect = (new BasicServ())
            ->getMultiOptions(['platform_id', 'promotion_id', 'cp_game_id:all', 'game_id', 'channel_main_id'])
            ->put('channel_id', (new GeneralOptionServ())->listChannelOptions());

        $resetGroupFn = $this->resetGroupsCols($groups, $groups,
            array_diff(ConstHub::OPERATOR_FIXED_INFO_COLS, ['thour', 'app_show_id', 'department', 'user_id'])
        );
        $replaceFn    = $this->replaceColumnDefine($constConfCollect);

        $process = new ProcessLine();
        if ($eventType > 990) {
            $process->addProcess(EventDistributeExt::calcIndexXY($groupType));
        }
        else {
            // 根据展示类别显示指标对应数值(event_type[1,2,3])
            $process->addProcess(EventDistributeExt::calcIndex($params->get('display_type', 0), $groupType));
        }

        $summaryProcess = clone $process;

        $process
            ->addProcess(static fn(&$target) => $target['channel_id'] = $target['t_channel_id'] ?? '')
            ->addProcess(static fn(&$target) => $target['channel_main_id'] = $target['t_channel_main_id'] ?? '')
            ->addProcess($replaceFn)
            ->addProcess(EventDistributeExt::formatTDay((int)$params->get('range_date_dimension', 2)))
            ->addProcess($resetGroupFn);

        $process->run($list);
        $summaryProcess->run($wrapSummary);

        $infoRe['time'] = $summaryRow['max_update_time'] ?? '';

        return $infoRe;
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'event_index', 'label' => '事件指标'],
                    ['value' => 'time_range_index', 'label' => '事件区间'],
                ],
            ],
        ];

        $fields = $this->tableFields($params->toArray());

        return ['fields' => $fields, 'classify' => $classify];
    }


    protected function registerParams(): Collection
    {
        $indexDay = date('Y-m-d', strtotime('-1day'));

        return collect([
            ['field' => 'range_date_start', 'default' => $indexDay], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $indexDay], // 统计日期结束时间
            ['field' => 'range_date_dimension', 'default' => 2], // 统计日期结束时间
            ['field' => 'cp_game_id'], // 游戏原名
            ['field' => 'game_id'], // 游戏统计名
            ['field' => 'channel_main_id'], // 主渠道
            ['field' => 'channel_id'], // 主渠道
            ['field' => 'package_id'], // 包号
            ['field' => 'platform_id'], // 客户端ID
            ['field' => 'promotion_id'], // 推广分类
            ['field' => 'event_type', 'default' => 1], // 事件类型
            ['field' => 'group_type', 'default' => 1], // 自然日
            ['field' => 'display_type', 'default' => 0], // 展示节点
            ['field' => 'channel_id_tags'],
            ['field' => 'package_id_tags'],
            ['field' => 'channel_main_id_tags'],
            ['field' => 'game_id_tags'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups', 'default' =>
                ['tday', 'cp_game_id', 'game_id', 'package_id', 'platform_id', 'promotion_id', 'channel_main_id', 'channel_id'],
            ],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

    /**
     * @param array $groups
     *
     * @return array
     */
    private function groupAdRelation(array $groups): array
    {
        $relationTree = [
            'package_id'      => ['game_id', 'cp_game_id', 'platform_id', 'channel_main_id', 'channel_id',],
            'channel_id'      => ['channel_main_id'],
            'channel_main_id' => [],
            'game_id'         => ['cp_game_id'],
            'platform_id'     => [],
            'promotion_id'    => [],
            'cp_game_id'      => [],
            'tday'            => [],
        ];

        return ColumnManager::matchRelationByGroups($relationTree, $groups);
    }


}
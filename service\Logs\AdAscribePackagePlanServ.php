<?php

namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use phpseclib3\Math\BigInteger\Engines\PHP;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

class AdAscribePackagePlanServ
{
    /**
     * 简单的获取部分基础指标数据
     *
     *
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function simpleBaseInfo(
        array $params = [],
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): array
    {
        $data = [];
        return $data;
    }

    /**
     *
     *
     *
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function listInfo(
        array $params = []
    ): array
    {
        // $tday = $params['login_time'] ?? '';

        if ((!($params['package_id'] ?? '') || !($params['login_time'] ?? ''))
            && (!($params['newlogin_package_id'] ?? '') || !($params['newlogin_time'] ?? ''))
        ) {
            return  ['list' => [], 'summary' => [], 'total' => 0];
        }

        $where = $this->where($params);

        $sql = "SELECT a.*,min(a.diff_days) from (select * from (SELECT
                    d.id source_active_id,
                    a.id active_id,
                    a.time login_time,
                    a.id,
                    d.sv_key newlogin_sv_key,
                    a.sv_key,
                    d.activate_time,
                    a.core_account,
                    a.login_account,
                    d.game_id,
                    a.package_id,
                    d.package_id newlogin_package_id,
                    DATEDIFF(a.TIME,c.TIME) diff_days,
                    d.main_account,
                    d.newlogin_time,
                    d.device_key,
                    e.ad_name,
                    d.cp_game_id,
                    md5(d.ip),
                    h.aid newlogin_aid,
                    h.aid plan,
                    j.aid,
                    IF(((g.OAID = LEFT(f.OAID, 64) and f.OAID != '') or (md5(g.OAID) = LEFT(f.OAID, 64) and f.OAID != '')), 'OAID', IF(g.ANDROID_ID = LEFT(f.ANDROID_ID, 64) and f.ANDROID_ID != '', 'ANDROID_ID',
                                                                    IF(g.MD5_DEVICE_ID = LEFT(f.MD5_DEVICE_ID, 255) and f.MD5_DEVICE_ID != '',
                                                                        'MD5_DEVICE_ID', IF(md5(d.ip) = F.IP, 'IP', '')))) match_type,
                    IF(((g.OAID = LEFT(i.OAID, 64) and i.OAID != '') or (md5(g.OAID) = LEFT(i.OAID, 64) and i.OAID != '')), 'OAID', IF(g.ANDROID_ID = LEFT(i.ANDROID_ID, 64) and i.ANDROID_ID != '', 'ANDROID_ID',
                                                                    IF(g.MD5_DEVICE_ID = LEFT(i.MD5_DEVICE_ID, 255) and i.MD5_DEVICE_ID != '',
                                                                        'MD5_DEVICE_ID',
                                                                        IF(md5(d.ip) = i.IP, 'IP', ''))))                  match_type_2
                FROM origin_platform.tb_sdk_user_newlogin_package a
                        LEFT JOIN base_conf_platform.tb_base_game_conf b USING (game_id)
                        LEFT JOIN ddc_platform.dwd_sdk_user_newlogin_seven c
                                on b.CP_GAME_ID = c.CP_GAME_ID and a.CORE_ACCOUNT = c.CORE_ACCOUNT and a.TIME>=c.TIME
                        LEFT JOIN ddc_platform.dwd_sdk_adsource_game d on c.SOURCE_ID = d.SOURCE_ID
                        LEFT JOIN base_conf_platform.tb_ad_channel_conf e on d.CHANNEL_ID = e.ID
                        LEFT JOIN ddc_platform.dwd_ad_click_match_log f on d.CLICK_ID = f.ID
                        left join origin_platform.tb_ad_click_match_log_2022 i on d.CLICK_ID = i.ID
                        LEFT JOIN origin_platform.tb_sdk_active_log g on d.ACTIVATE_TIME = g.TIME and LEFT(d.DEVICE_KEY, 32)=g.DEVICE_KEY
                        LEFT JOIN dataspy.tb_ad_svlink_conf h on d.SV_KEY = h.ID
                        LEFT JOIN dataspy.tb_ad_svlink_conf j on a.SV_KEY = j.ID
                        left join adp_platform.tb_adp_plan_base tapb on d.PLAN_ID = tapb.PLAN_ID
                WHERE {$where} order by d.NEWLOGIN_TIME desc  limit 100000
                )a GROUP BY ID)a group by ID order by NEWLOGIN_TIME desc
        ";
// echo $sql;die;
        $list = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $list = $this->format($list);

        return  ['list' => $list, 'summary' => [], 'total' => count($list)];
    }

    /**
     * where条件
     *
     * @param array $params
     * @return void
     */
    public function where($params = [])
    {
        $where = " 1=1 ";

        if (!($params['login_time'] ?? '') && !($params['newlogin_time'] ?? '')) {
            $params['login_time'] = date('Y-m-d');
        }

        // 登陆时间
        if ($params['login_time'] ?? '') {
            $valueTime = explode(' - ', $params['login_time']);
            $startTime = $valueTime[0] ?? date('Y-m-d');
            $endTime = $valueTime[1] ?? $startTime;
            $where .= " AND a.time between '$startTime 00:00:00' AND '$endTime 23:59:59' ";
        }

        // 广告新增时间
        if ($params['newlogin_time'] ?? '') {
            $valueTime = explode(' - ', $params['newlogin_time']);
            $startTime = $valueTime[0] ?? date('Y-m-d');
            $endTime = $valueTime[1] ?? $startTime;
            $where .= " AND d.newlogin_time between '$startTime 00:00:00' AND '$endTime 23:59:59' ";
        }

        // $diffMessage = "其中有%s条记录异常，分别归因到其他短链<span style='color: red'>%s</span>条,其他短链归因到查询短链<span style='color: blue'>%s</span>条";
        // 归因计划或短链名称
        if ($params['plan'] ?? '') {
            if (is_numeric($params['plan'])) {
                $where .= " AND (h.id = '{$params['plan']}')";
            } else {
                $where .= " AND (h.aid = '{$params['plan']}' or tapb.plan_name = '{$params['plan']}')";
            }
        }

        // 登录包号
        if ($params['package_id'][0] ?? '') {
            $where .= " AND a.package_id = '{$params['package_id'][0]}'";
        }


        // 归因包号
        if ($params['newlogin_package_id'] ?? '') {
            $where .= " AND d.package_id = '{$params['newlogin_package_id']}'";
        }

        // 是否广告新增 // 登陆包号 = 归因包号 且 不是子账号 为 广告新增
        if (isset($params['is_ad_new'])) {
            if ($params['is_ad_new'] == 1) {
                $where .= " AND (a.package_id = d.package_id AND d.main_account = a.core_account)";
            } elseif ($params['is_ad_new'] == 0) {
                $where .= " AND (a.package_id != d.package_id or d.main_account != a.core_account)";
            }
        }

        // 是否子账号
        if (isset($params['is_sub_account'])) {
            if ($params['is_sub_account'] == 1) {
                $where .= " AND d.main_account != a.core_account";
            } elseif ($params['is_sub_account'] == 0) {
                $where .= " AND d.main_account = a.core_account";
            }
        }

        return $where;
    }

    /**
     * 格式化
     *
     * @param array $list
     * @return void|array
     */
    public function format($list = [])
    {
        foreach ($list as $key => $value) {
            $list[$key]['is_sub_account'] = $value['main_account'] != $value['core_account'] ? '是' : '否';
            $list[$key]['is_ad_new'] = $value['package_id'] == $value['newlogin_package_id'] && $value['main_account'] == $value['core_account'] ? '是' : '否';
        }

        return $list;
    }
}
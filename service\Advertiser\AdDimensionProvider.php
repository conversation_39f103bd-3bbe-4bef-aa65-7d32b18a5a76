<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;

class AdDimensionProvider
{
    const AD_CAMPAIGN = 1;
    const AD_PLAN = 2;
    const AD_CREATIVE = 3;


    /**
     * @param array $ids
     * @param int $dimension
     * @return array
     */
    public function loadDimensionName(array $ids, int $dimension = self::AD_PLAN): array
    {
        $ids = "'" . implode("','", $ids) . "'";
        $db = $this->getConn();
        if ($dimension == static::AD_CAMPAIGN) {
            $sql = "
            select distinct channel_id, campaign_id, campaign_name from adp_platform.tb_adp_campaign where campaign_id in ({$ids});
            ";
        }
        elseif ($dimension == static::AD_PLAN) {
            $sql = "
            select a1.channel_id, a2.campaign_id, a2.campaign_name, a1.plan_id, a1.plan_name
            from adp_platform.tb_adp_plan_base a1
                     left join adp_platform.tb_adp_campaign a2 on a1.channel_id = a2.channel_id and a1.campaign_id = a2.campaign_id
            where a1.plan_id IN ({$ids});
            ";
            $svSQL = "
            select a1.id as plan_id, a1.channel_id, a1.aid as plan_name
            from dataspy.tb_ad_svlink_conf a1
            where a1.id IN ({$ids});
            ";
            $data = $db->query($sql)->fetchAll();
            $svData = $db->query($svSQL)->fetchAll();
            if (!empty($svData)) {
                $data = array_merge($data, $svData);
            }
            return $data;
        }
        elseif ($dimension == static::AD_CREATIVE) {
            $sql = "
            select distinct channel_id,plan_id, creative_id, creative_name from adp_platform.tb_adp_creative_base where creative_id in ({$ids})
            ";
        }

        if (isset($sql)) {
            return $db->query($sql)->fetchAll();
        }
        else {
            return [];
        }
    }


    private function getConn()
    {
        return FakeDB::connection('doris_entrance');
    }
}
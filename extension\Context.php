<?php

namespace app\extension;

use ArrayObject;
use JsonSerializable;

/**
 * 保存上下文信息
 * @package app\service
 * <AUTHOR>
 * @property bool|int lastInsertId            新增操作-自增id
 * @property bool|int rowCount                更新操作-影响行数
 * @property string   action                  list|create|update|delete
 * @property mixed    dataBeforeUpdate        更新前数据
 * @property  mixed   jsonResponse            json 序列化结果（响应结果）
 */
class Context implements JsonSerializable
{
    /**
     * 数据源
     * @var array
     */
    private $data = [];

    /**
     * 数据写入日志
     * @var array
     */
    private $log = [];

    /**
     * 是否调试
     * @var bool
     */
    public $debug = false;

    /**
     * set
     * @param string $name  key
     * @param mixed  $value value
     * @return mixed
     */
    public function __set($name, $value)
    {
        $this->saveLog(__METHOD__, $name, $value);
        return $this->set($name, $value);
    }

    /**
     * get
     * @param string $name key
     * @return mixed
     */
    public function __get($name)
    {
        $value = $this->get($name);
        $this->saveLog(__METHOD__, $name, $value);
        return $value;
    }

    /**
     * 写入调试日志
     * @param string $action set|get
     * @param string $key    key
     * @param mixed  $value  value
     * @return void
     */
    private function saveLog($action, $key, $value)
    {
        if ($this->debug) {
            $debug       = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
            $debug       = end($debug);
            $this->log[] = [
                'key'    => $key,
                'value'  => $value,
                'action' => $action,
                'trace'  => $debug['file'] . ':' . $debug['line'],
            ];
        }
    }

    /**
     * 获取调试日志
     * @return array
     */
    public function getLog()
    {
        return $this->log;
    }

    /**
     * 设置key
     * @param string $key   key
     * @param mixed  $value val
     * @return mixed
     */
    public function set($key, $value)
    {
        return $this->data[$key] = $value;
    }

    /**
     * 获取key
     * @param string $key key
     * @return mixed|null
     */
    public function get($key)
    {
        return isset($this->data[$key]) ? $this->data[$key] : null;
    }

    /**
     * key是否存在
     * @param string $key key
     * @return bool
     */
    public function has($key)
    {
        return isset($this->data[$key]);
    }

    /**
     * 移除key
     * @param string $key key
     * @return bool
     */
    public function remove($key)
    {
        unset($this->data[$key]);
        return true;
    }

    /**
     * 清空
     * @return bool
     */
    public function clear()
    {
        $this->data = [];
        return true;
    }

    /**
     * json 序列化结果
     * @return bool
     */
    public function jsonSerialize()
    {
        $res = $this->get('jsonResponse');
        $res = $res ? $res : new ArrayObject();
        return $res;
    }
}

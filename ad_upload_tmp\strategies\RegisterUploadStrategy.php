<?php

namespace app\ad_upload_tmp\strategies;

use app\ad_upload_tmp\contract\AdBaseInterface;
use app\ad_upload_tmp\contract\AdUploadStrategyInterface;
use app\ad_upload_tmp\tool\CommonFunc;

/**
 * 注册数据上报 策略
 * <AUTHOR>
 */
class RegisterUploadStrategy extends AdUploadStrategyInterface
{
    /**
     * 上报类型
     * @var string
     */
    public $action = AdBaseInterface::ACTION_REGISTER;

    /**
     * 最大id的数据
     * @var array
     */
    private $maxlastData = [];

    /**
     * 初始化上报点
     * @return void
     */
    public function initUploadLast()
    {
        parent::initUploadLast();
        //排除已过期的未归因匹配id
        // 数量大于100 才进行排除，减少查询次数
        if (count($this->unmatchedIds) < 100) {
            return;
        }
        $before             = date('Y-m-d H:i:s', strtotime($this->unmatchedTime));
        $unmatchedIdString  = CommonFunc::arr2Str($this->unmatchedIds);
        $sql                = "SELECT CORE_ACCOUNT FROM bigdata_tmp.dwd_sdk_reg_huge_match_tmp WHERE 
             CORE_ACCOUNT IN ($unmatchedIdString) AND time > '$before'";
        $data               = \Plus::$app->doris_entrance->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $data               = array_column($data, 'CORE_ACCOUNT');
        $this->unmatchedIds = $data;
        if (empty($data)) {
            $this->unmatchedIds = [0];
        }
    }

    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    public function getData($uploadConfig, $packages): array
    {
        //获取补漏上报ID
        $unmatchedIdString = implode(',', $this->unmatchedIds);
        if (empty($packages) || $this->lastId <= 0) {
            return [];
        }

        //获取补漏上报ID
        $unmatchedIdString = CommonFunc::arr2Str($this->unmatchedIds);
        //lastId记录的是TIME时间戳
        $time = date('Y-m-d H:i:s', $this->lastId);
        //前3小时的
        $before = date('Y-m-d H:i:s', $this->lastId - 3600 * 3);
        //查询激活数据：可根据时间段或者最大ID查询
        $condition = "PACKAGE_ID IN ({$packages})";
        if (!empty($this->timeBegin) && !empty($this->timeEnd)) {
            $condition .= " AND TIME BETWEEN '{$this->timeBegin}' AND '{$this->timeEnd}' and new_channel_id={$this->channelId}";
        } else {
            $condition .= " AND (time > '$time' and new_channel_id in(0,$this->channelId) ) OR ";
            $condition .=  "(time > '$before' and new_channel_id={$this->channelId} and  CORE_ACCOUNT in($unmatchedIdString))";
        }
        //$condition .= ' and core_account="9_2506231735186307"';

        $sql = "SELECT 
                    IDFV,
                    `TIME`,
                    CALLBACK_PARAM,
                    PACKAGE_ID,
                    DEVICE_KEY,
                    DEVICE_TYPE,
                    DEVICE_CODE,
                    USERAGENT,
                    OAID,
                    DEVICE_ID,
                    MD5_DEVICE_ID,
                    GAME_ID,
                    OS,
                    CORE_ACCOUNT,
                    IP,
                    ANDROID_ID,
                    new_channel_id as CHANNEL_ID,
                    SV_KEY,
                    CLICK_ID,
                    '{$this->action}' AS TYPE
                FROM  bigdata_tmp.dwd_sdk_reg_huge_match_tmp
                WHERE {$condition}
                ";
        return \Plus::$app->doris_entrance->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 设置如果上一步getData 获取数据为空，设置最大上报id
     * 避免长期没有数据上报的渠道，查询过多数据
     * @return void
     */
    public function setMaxLastId():void
    {
        $this->setLastId($this->maxlastData);
    }

    /**
     * 上报数据过滤
     * @param array $data         data
     * @param array $uploadConfig uploadConfig
     * @return array
     */
    public function filterData($data, $uploadConfig): array
    {
        $unmatchedIds = $this->unmatchedIds;
        $dataActive   = [];
        foreach ($data as $key => $val) {
            $uploadConfigOne = $uploadConfig[$val['PACKAGE_ID']] ?? [];
            if (empty($uploadConfigOne)) {
                \Plus::$app->log->error('not_match_config渠道配置不存在' . json_encode($val), [], AdBaseInterface::LOG_DIR);
            }
            foreach ($uploadConfigOne as $v) {
                //1为全量上报，2为匹配点击上报
                if ($v['UPLOAD_METHOD'] == 1 || ($v['UPLOAD_METHOD'] == 2 )) {
                    $channelId = $v['UPLOAD_METHOD'] == 1 ? $v['CHANNEL_ID'] : $val['CHANNEL_ID'];
                    $clickId   = $v['UPLOAD_METHOD'] == 1 ? 0 : $val['CLICK_ID'];

                    //未归因数据缓存
                    $unmatchedIds = $this->cache->processUnmatchedIds($unmatchedIds, $val, 'CORE_ACCOUNT', 'CHANNEL_ID');
                    if (in_array($val["CORE_ACCOUNT"], $unmatchedIds)) {
                        \Plus::$app->log->info('unmatched_' . $this->action . $val['CORE_ACCOUNT'], [], AdBaseInterface::LOG_DIR);
                        continue; //剔除未匹配的数据
                    }
                    // 没有force=1，剔除已上报的数据
                    if ($this->force == 0) {
                        $key = 'ad_up_repeat_' . $this->action;
                        if (\Plus::$app->redis82->hget($key, $val['CORE_ACCOUNT'])) {
                            \Plus::$app->log->warning('repeat_' . $this->action . $val['CORE_ACCOUNT'], [], AdBaseInterface::LOG_DIR);
                            continue;
                        }
                        \Plus::$app->redis82->hset($key, $val['CORE_ACCOUNT'], 1);
                    }

                    $dataActive[] = array_merge($val, [
                        'CHANNEL_ID'    => $channelId,
                        'UPLOAD_METHOD' => $v['UPLOAD_METHOD'],
                        'EXT_ID'        => $v['ID'],
                        'EXT'           => $v['EXT'],
                        'CLICK_ID'      => $clickId,
                    ]);
                }// end if()
            }// end foreach()
        }// end foreach()

        $this->unmatchedIds = $unmatchedIds;

        return $dataActive;
    }
}

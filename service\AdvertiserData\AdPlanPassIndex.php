<?php

namespace app\service\AdvertiserData;

use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Components\MatchParams\AdPlanMatcher;
use app\service\AdvertiserData\Scheme\AdPlanPassScheme;
use app\service\AdvertiserData\Traits\AdServiceable;
use app\service\AdvertiserData\Traits\BasicOperator;
use app\service\AdvertiserData\Traits\Converter;
use app\service\General\BizTagsServ;
use app\util\Common;

class AdPlanPassIndex
{
    use Converter, BasicOperator, AdServiceable;

    /**
     * @param array $params
     * @param array $groups
     *
     * @return array|false
     */
    public function fetchPassRate(array $params = [], array $groups = [])
    {
        $powerSql = \Plus::$service->admin->getAdminPowerSql();

        $scheme    = AdPlanPassScheme::NewOne()->select();
        $mainTable = $scheme::MAIN_TABLE['alias'];

        $scheme
            ->joinPowerSql($powerSql)
            ->joinPlanBase('left')
            ->join($this->joinAccount('t_base'))
            ->joinAdpOauth('left', 't_base')
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id' , '=','base_channel.channel_id'))
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params)));

        $matcher = new AdPlanMatcher($scheme->fieldReflect());
        $matcher->setParams($params);
        $matcher->execute($scheme);
        Common::dumpSql((clone $scheme)->toSql());
        return $this->fetchAll($scheme->toSql());
    }

    /**
     * @param array $params
     * @param bool  $isTop
     * @param bool  $isTotal
     *
     * @return array
     */
    protected function getColumns(array $params = [], bool $isTop = false, bool $isTotal = false): array
    {
        $mainTable = AdPlanPassScheme::MAIN_TABLE['alias'];

        $planChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString     = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0, IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) AS promotion_channel_id",
            $planChannelsString
        );
        $channelMainString = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );


        $fixInfoIndex    = [
            'tday'                 => ['source' => $mainTable],
            'cp_game_id'           => ['source' => $mainTable],
            'game_id'              => ['source' => $mainTable],
            'app_show_id'          => ['source' => 'POWER'],
//            'channel_main_id'      => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (6568, 6822, 5329), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id'],
            'channel_main_id'      => ['info', 'raw' => $channelMainString],
//            'promotion_channel_id' => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id NOT IN (6568, 6822, 5329), POWER.channel_id,IF(t_base.channel_id != 0, t_base.channel_id,POWER.channel_id)), 0) AS promotion_channel_id'],
            'promotion_channel_id' => ['info', 'raw' => $channelString],
            'platform_id'          => ['source' => 'POWER'],
            'package_id'           => ['source' => $mainTable],
            'campaign_id'          => ['source' => $mainTable],
            'plan_id'              => ['source' => $mainTable],
            'promotion_id'         => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            'department_id'        => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN ('.$planChannelsString.'),t_admin.department_id, POWER.ad_department_id),0) as department_id'],
            'user_id'              => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN ('.$planChannelsString.'),t_base.USER_ID, POWER.AD_USER_ID), 0) AS user_id'],
            'ad_account'           => ['source' => 'base_account'],
            'account_id'           => ['source' => 'base_account'],
            'account_name'         => ['source' => 'adp_oauth', 'source_field' => 'ADVERTISER_NAME'],
        ];
        $calculatedIndex = [
            'pass_level_1' => ['raw' => 'SUM(IF(ROLE_TYPE = 1, NUMERATOR, 0)) as pass_level_1'],
            'pass_level_2' => ['raw' => 'SUM(IF(ROLE_TYPE = 2, NUMERATOR, 0)) as pass_level_2'],
            'pass_level_3' => ['raw' => 'SUM(IF(ROLE_TYPE = 3, NUMERATOR, 0)) as pass_level_3'],
            'pass_level_4' => ['raw' => 'SUM(IF(ROLE_TYPE = 4, NUMERATOR, 0)) as pass_level_4'],
            'pass_der_1'   => ['raw' => 'SUM(IF(ROLE_TYPE = 1, DENOMINATOR, 0)) as pass_der_1'],
            'pass_der_2'   => ['raw' => 'SUM(IF(ROLE_TYPE = 2, DENOMINATOR, 0)) as pass_der_2'],
            'pass_der_3'   => ['raw' => 'SUM(IF(ROLE_TYPE = 3, DENOMINATOR, 0)) as pass_der_3'],
            'pass_der_4'   => ['raw' => 'SUM(IF(ROLE_TYPE = 4, DENOMINATOR, 0)) as pass_der_4'],
        ];

        $result  = [];
        $collect = collect();

        if (!$isTotal) {
            $collect = $collect->merge($fixInfoIndex);
        }
        $collect = $collect->merge($calculatedIndex);

        $collect->each(function (&$item, $key) use (&$result, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];
                $format    = "{$aggregate}(%s)";
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }

            $field .= $item['source_field'] ?? $key;

            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }

    /**
     * @return string[]
     */
    protected function groupsReflect(): array
    {
        $mainAlias = AdPlanPassScheme::MAIN_TABLE['alias'];

        return [
            'tday'            => $mainAlias . '.tday',
            'cp_game_id'      => $mainAlias . '.cp_game_id',
            'game_id'         => $mainAlias . '.game_id',
            'package_id'      => $mainAlias . '.package_id',
            'channel_id'      => $mainAlias . '.channel_id',
//            'channel_main_id' => 'POWER.channel_main_id',
            'creative_id'     => $mainAlias . '.creative_id',
            'day_type'        => $mainAlias . '.day_type',
            'ad_account_id'   => 'base_account.id',
        ];
    }

    private function fetchAll(string $sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

}
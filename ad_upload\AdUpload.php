<?php
declare(ticks=1);

namespace app\ad_upload;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\services\AdUploadService;
use app\ad_upload\strategies\ActiveUploadStrategy;
use app\ad_upload\strategies\CreateRoleUploadStrategy;
use app\ad_upload\strategies\LoginUploadStrategy;
use app\ad_upload\strategies\PayUploadStrategy;
use app\ad_upload\strategies\PayVirtualUploadStrategy;
use app\ad_upload\strategies\RegisterUploadStrategy;
use app\ad_upload\strategies\RemainUploadStrategy;
use app\ad_upload\tool\CommonFunc;
use app\ad_upload\tool\CustomProcess;

/**
 * 媒体数据 上报
 * <AUTHOR>
 */
class AdUpload extends CustomProcess
{
    /**
     * 渠道id
     * @var int
     */
    public $channel_id = 0;

    /**
     * 包号
     * @var string
     */
    public $package_ids = "";

    /**
     * 上报动作
     * @var string
     */
    public $actions = '';

    /**
     * 开始时间
     * @var string
     */
    public $begin_time = '';

    /**
     * 结束时间
     * @var string
     */
    public $end_time = '';

    /**
     * 数据id、订单号, 只有支付需要
     * @var string
     */
    public $ids = '';

    /**
     * 是否强制上报，跳过重复检查
     * @var int
     */
    public $force = 0;

    /**
     * 启动入口
     * @return void
     */
    public function run()
    {
        ini_set('memory_limit', '4048M');

        if (empty($this->channel_id) || empty($this->actions)) {
            echo "渠道ID和上报操作不能为空\n";
            return;
        }
        if (!in_array($this->channel_id, $this->getUploadChannel())) {
            \Plus::$app->log->alert("渠道ID:{$this->channel_id} 不支持的上报", [], AdBaseInterface::LOG_DIR);
            return;
        }

        $isRerun = false;
        if (!empty($this->begin_time) || !empty($this->end_time) || !empty($this->package_ids) || !empty($this->ids)) {
            $isRerun = true;
        }
        //获取上报配置
        $packagesConfig = $this->getDataUploadConfig();
        $actions        = explode(',', $this->actions);
        $params         = [$this->channel_id, $this->begin_time, $this->end_time, $isRerun, $this->ids, $this->force];
        //获取上报策略类
        $strategies = [
            AdBaseInterface::ACTION_ACTIVE      => new ActiveUploadStrategy(...$params),
            AdBaseInterface::ACTION_REGISTER    => new RegisterUploadStrategy(...$params),
            AdBaseInterface::ACTION_LOGIN       => new LoginUploadStrategy(...$params),
            AdBaseInterface::ACTION_PAY         => new PayUploadStrategy(...$params),
            AdBaseInterface::ACTION_CREATE_ROLE => new CreateRoleUploadStrategy(...$params),
            //AdBaseInterface::ACTION_REMAIN      => new RemainUploadStrategy(...$params), //已经没有使用
            AdBaseInterface::ACTION_PAY_VIRTUAL => new PayVirtualUploadStrategy(...$params),
        ];
        //循环上报
        foreach ($actions as $action) {
            $action = trim($action);
            if (!isset($strategies[$action])) {
                \Plus::$app->log->alert("渠道ID:{$this->channel_id} 不支持的操作类型{$action}", [], AdBaseInterface::LOG_DIR);
                continue;
            }
            $strategy      = $strategies[$action];
            $uploadService = new AdUploadService($strategy);
            //如果没有指定包号，默认使用配置的
            $packages = $this->package_ids;
            $pkgConf  = $packagesConfig[$action] ?? [];
            if (empty($pkgConf)) {
                \Plus::$app->log->notice("渠道ID:{$this->channel_id} 没有配置{$action}上报", [], AdBaseInterface::LOG_DIR);
                continue;
            }
            if (empty($packages)) {
                $packages = CommonFunc::arr2Str(array_keys($pkgConf));
            }
            $uploadService->upload($pkgConf, $packages, $action, $this->channel_id);
        }
    }

    /**
     * 获取数据上报配置
     * @return array
     */
    private function getDataUploadConfig()
    {

        $packagesConfig = [];
        $sql            = "SELECT
                    duc.ID, 
                    duc.CHANNEL_ID, 
                    duc.PACKAGE_ID, 
                    duc.EXT, 
                    duc.ACTIVE_UPLOAD, 
                    duc.CREATE_ROLE_UPLOAD,
                    duc.REG_UPLOAD, 
                    duc.LOGIN_UPLOAD, 
                    duc.PAY_UPLOAD, 
                    duc.REMAIN_UPLOAD, 
                    acc.UPLOAD_METHOD,
                    duc.PAY_VIRTUAL_UPLOAD
                FROM tb_ad_data_upload_conf duc
                LEFT JOIN base_conf_platform.tb_base_channel_conf acc ON duc.CHANNEL_ID = acc.CHANNEL_ID
                WHERE duc.CHANNEL_ID = {$this->channel_id}
                AND duc.STATUS = 1";

        $configList    = \Plus::$app->dataspy->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $uploadActions = [
            AdBaseInterface::ACTION_ACTIVE      => 'ACTIVE_UPLOAD',
            AdBaseInterface::ACTION_REGISTER    => 'REG_UPLOAD',
            AdBaseInterface::ACTION_LOGIN       => 'LOGIN_UPLOAD',
            AdBaseInterface::ACTION_PAY         => 'PAY_UPLOAD',
            AdBaseInterface::ACTION_CREATE_ROLE => 'CREATE_ROLE_UPLOAD',
            AdBaseInterface::ACTION_REMAIN      => 'REMAIN_UPLOAD',
            AdBaseInterface::ACTION_PAY_VIRTUAL => 'PAY_VIRTUAL_UPLOAD',
        ];
        foreach ($configList as $key => $val) {
            $val['EXT'] = json_decode($val['EXT'], true);
            //根据包号+渠道ID记录，针对同个包号可以上报多个渠道的问题
            foreach ($uploadActions as $action => $field) {
                if ($val[$field]) {
                    $packagesConfig[$action][$val['PACKAGE_ID']][$val['CHANNEL_ID']] = $val;
                }
            }
        }
        return $packagesConfig;
    }


    /**
     * 获取上报渠道，
     * 在业务中台->出包子渠道配置-> 标签 -> 数据上报配置（379）
     * @return mixed
     */
    private function getUploadChannel()
    {
        $sql = "SELECT distinct DATA_ID from base_conf_platform.biz_tags where TAG_ID = 379 and TABLE_NAME = 'app_channel'";
        $rs  = \Plus::$app->base_conf_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        return array_column($rs, 'DATA_ID');
    }
}

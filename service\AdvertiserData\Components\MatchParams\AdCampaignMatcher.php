<?php

namespace app\service\AdvertiserData\Components\MatchParams;

use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\Convert;
use app\service\AdvertiserData\Components\MatchParams\Contracts\MatcherContract;
use app\service\AdvertiserData\Components\MatchParams\Traits\CampaignMatch;
use app\service\AdvertiserData\Components\MatchParams\Traits\GeneralMatch;
use app\service\AdvertiserData\Components\MatchParams\Traits\GroupConvertable;
use app\service\AdvertiserData\Components\MatchParams\Traits\Matchable;
use app\service\AdvertiserData\Contracts\GroupConvertContract;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\General\BizTagsServ;
use app\service\General\Matcher\Traits\TagsMatcher;
use Aura\SqlQuery\Common\SelectInterface;

class AdCampaignMatcher extends Matcher implements MatcherContract, GroupConvertContract
{
    use Matchable, GeneralMatch, CampaignMatch, GroupConvertable, TagsMatcher;

    public function __construct($reflectMap = [])
    {
        if (!empty($reflectMap)) {
            $this->setReflectMap($reflectMap);
        }
    }

    public function setParams($params): AdCampaignMatcher
    {
        $this->processLine($params);

        return $this;
    }

    /**
     * @param SchemeContract $scheme
     * @return void
     */
    public function execute(SchemeContract &$scheme)
    {
        $wheres = array_unique(array_filter($this->wheres));

        $scheme->scope(function (&$query) use ($wheres) {
            if (!$query instanceof SelectInterface) return;

            foreach ($wheres as $where) {
                $query->where($where);
            }
        });
    }

    protected function processLine($params)
    {
        $line = [
            [$this, 'matchTDay'],
            [$this, 'matchCpGames'],
            [$this, 'matchGames'],
            [$this, 'matchAppShowIds'],
            [$this, 'matchChannelMainId'],
            [$this, 'matchChannelId'],
            [$this, 'matchPackageTags'],
            [$this, 'matchChannelTags'],
            [$this, 'matchGameIdTags'],
            [$this, 'matchChannelMainTags'],
            [$this, 'matchPlatformId'],
            [$this, 'matchPackageId'],
            [$this, 'matchPromotionId'],
            [$this, 'matchAdAccountId'],
            [$this, 'matchPromotionChannel'],
            [$this, 'matchAdAccount'],
            [$this, 'matchCampaignId'],
            [$this, 'matchCampaignName'],
            [$this, 'matchAccountId'],
        ];

        foreach ($line as $callback) {
            if (is_callable($callback)) {
                $this->wheres[] = call_user_func_array($callback, [$params]);
            }
        }
    }

    /**
     * @param $params
     *
     * @return string
     * @throws \RedisException
     */
    protected function matchChannelMainId($params): string
    {
        $data = Convert::convertInString(Arr::get($params, 'channel_main_id'));

        if (empty($data)) return '';

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        return "COALESCE(IF(`POWER`.`channel_id` IN ({$planChannelsString}), IF(`base_channel`.`channel_main_id` != 0,`base_channel`.`channel_main_id`,`power`.`channel_main_id`),`power`.`channel_main_id`),0) IN ({$data})";
    }
}
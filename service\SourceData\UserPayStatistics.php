<?php

namespace app\service\SourceData;


use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

/**
 * @\app\service\SourceData\UserPayStatistics 用户付费统计查询
 *
 */
class UserPayStatistics
{
    /**
     * @param array $params
     * @param array $groups
     * @param       $sort
     * @param array $columns
     *
     * @return array
     */
    public function getPayInfoByHourly(array $params, array $groups = [], $sort = null, array $columns = []): array
    {
        $today = date('Y-m-d');
        $table = 'ddc_platform.dwd_sdk_user_payment';
        $db    = $this->getConn();
        $qb    = $db->select()->from($table . ' as t_pay');

        $dateStart = $params['range_date_start'] ?? $today;
        $dateEnd   = $params['range_date_end'] ?? $today;

        $qb->where('pay_time', 'between', ($dateStart . ' 00:00:00'), ($dateEnd . ' 23:59:59'));

        if (!empty($params['pay_result'])) {
            $qb->where('pay_result' , $params['pay_result']);
        }

        if (!empty($params['cp_game_id'])) {
            $qb->where('cp_game_id' , new Parameter(Arr::wrap($params['cp_game_id'])));
        }

        if (empty($columns)) {
            $columns = [
                new Fragment('DATE(pay_time) as tday'),
                new Fragment('HOUR(pay_time) as thour'),
                'cp_game_id',
                new Fragment('ROUND(SUM(money), 2) as pay_money'),
            ];
        }

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        $qb->columns($columns);

        return ['list' => $qb->fetchAll()];
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
<?php

namespace app\service\AdConf;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\CommonCenter\ConfCenter\Request;
use app\extension\Support\CommonCenter\ConfCenter\Uri;
use app\extension\Support\Helpers\Zakia;
use app\models\baseConfPlatform\BizTags;
use app\models\baseConfPlatform\TbPackageDetailConf;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\General\GeneralOptionServ;
use app\service\General\Helpers\DataSpyTable;
use Spiral\Database\Database;
use Spiral\Database\Query\SelectQuery;

class SvlinkServ
{

    const QB_ALL         = 1;
    const QB_UPLOAD_CONF = 1;


    /**
     * 获取短链配置列表
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int   $mode
     *
     * @return array
     */
    public function getInfo(
        array $params = [], array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $qb = $this->getQueryBuilder(self::QB_ALL ^ self::QB_UPLOAD_CONF);
        $this->whereMatch($qb, $params);

        $cloneQb = clone $qb;

        $qb->columns($this->getColumns(self::QB_ALL ^ self::QB_UPLOAD_CONF));

        if (!empty($paginate)) {
            [
                'page'      => $page,
                'page_size' => $pageSize,
            ] = $paginate;

            $qb->limit($pageSize)->offset(($page - 1) * $pageSize);
        }

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        $total = $cloneQb->count();

        return [
            'list'  => $qb->fetchAll(),
            'total' => $total,
        ];
    }

    /**
     * @param int $qbMode
     *
     * @return \Spiral\Database\Query\SelectQuery
     */
    private function getQueryBuilder(int $qbMode = -1): \Spiral\Database\Query\SelectQuery
    {
        $db = $this->getConn();

        $qb = $db->select()->from(DataSpyTable::SV_LINK_CONF . ' as svlink');

        if ($qbMode & self::QB_UPLOAD_CONF) {
            $qb
                ->leftJoin(DataSpyTable::DATA_UPLOAD_CONF, 'upload_conf')
                ->on([
                    'svlink.package_id' => 'upload_conf.package_id',
                    'svlink.channel_id' => 'upload_conf.channel_id',
                ]);
        }

        return $qb;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('dataspy');
    }

    /**
     * @param int $qbMode
     *
     * @return string[]
     */
    private function getColumns(int $qbMode = -1): array
    {
        $columns = [
            'svlink.id as id',
            'svlink.channel_id as channel_id',
            'svlink.cp_game_id as cp_game_id',
            'svlink.game_id as game_id',
            'svlink.package_id as package_id',
            'svlink.aid as aid',
            'svlink.ext as ext',
            'svlink.sv_link as sv_link',
            'svlink.status as status',
            'svlink.is_status as is_status',
            'svlink.user_id as user_id',
            'svlink.add_time as add_time',
            'download_link as download_link',
        ];

        if ($qbMode & self::QB_UPLOAD_CONF) {
            $columns[] = 'upload_conf.os';
        }

        return $columns;
    }

    /**
     * @param SelectQuery $qb
     * @param array       $params
     *
     * @return void
     */
    private function whereMatch(SelectQuery &$qb, array $params)
    {
        if (!empty($params['channel_id'])) {
            $channel = $params['channel_id'];
            QueryBuilderHelper::baseBuild($qb, 'channel_id', $channel);
        }

        if (!empty($params['package_id'])) {
            $package = $params['package_id'];
            QueryBuilderHelper::baseBuild($qb, 'svlink.package_id', $package);
        }

        if (!empty($params['game_id'])) {
            $gameId = $params['game_id'];
            QueryBuilderHelper::baseBuild($qb, 'svlink.game_id', $gameId);
        }

        if (!empty($params['aid'])) {
            $aid = $params['aid'];
            $qb->where('aid', 'like', "%{$aid}%");
        }

        if (!empty($params['user_id'])) {
            $userId = $params['user_id'];
            QueryBuilderHelper::baseBuild($qb, 'svlink.user_id', $userId);
        }
    }

    /**
     * @param               $channel
     * @param               $gameId
     * @param               $packageId
     * @param               $aid
     * @param Database|null $db
     *
     * @return bool
     */
    public function checkDuplicate($channel, $gameId, $packageId, $aid, Database $db = null): bool
    {
        if (is_null($db)) $db = $this->getConn();

        $qb = $db->select()->from(DataSpyTable::SV_LINK_CONF);

        $qb->where([
            'channel_id' => $channel,
            'game_id'    => $gameId,
            'package_id' => $packageId,
            'aid'        => $aid,
        ]);

        return $qb->count() > 0;
    }


    /**
     * @param $list
     *
     * @return mixed
     * @throws \Throwable
     */
    public function multiInsert($list)
    {
        $db          = $this->getConn();
        $now         = date('Y-m-d H:i:s');
        $operator    = \Plus::$service->admin->getUserId() ?? '0';
        $generalServ = new GeneralOptionServ();
        $channelMap  = array_column($generalServ->listChannelInfo() ?? [], null, 'channel_id');
        $packageArr  = (new BizTags())->getWeiXinXcxPackageId();

        return $db->transaction(function (Database $ndb) use ($list, $operator, $generalServ, $now, $channelMap, $packageArr) {
            $result = [];
            foreach ($list as $foo) {
                if (!Zakia::arrayPropRequired(
                    $foo, ['channel_id', 'package_id', 'aid', 'num']
                )) {
                    throw new \Exception('缺失必要参数');
                }

                [
                    'channel_id' => $channel,
                    'package_id' => $package,
                    'aid'        => $aid,
                    'num'        => $num,
                ] = $foo;

                $suffixRangeLeft  = $foo['suffix_range_left'] ?? 1;
                $suffixRangeRight = $foo['suffix_range_right'] ?? 1;

                if (!empty($foo['user_id'])) {
                    $userId = $foo['user_id'];
                }
                else {
                    $userId = $operator;
                }

                if ($channel == 8475) {
                    $foo['channel_code'] = $channelMap[$channel]['channel_code'] ?? '';
                    $this->createCompanyInsideLink($ndb, $foo);
                    continue;
                }

                //抖音小程序
                $iosPackageStr = '';
                if (in_array($package, $packageArr)) {
                    if (substr($package, -4) != "0300" && substr($package, -4) != "0301") {
                        $result = Zakia::getXcxPackages($package);
                        if (!$result) {
                            throw new \Exception('填写包号未配置');
                        }
                        $package       = $result["android"];
                        $iosPackageStr = "&ios_package_id=" . $result["ios"];
                    }
                }

                $package      = \trim($package);
                $ext          = $foo['ext'] ?? '';
                $downloadLink = $foo['download_link'] ?? '';

                if (Zakia::checkSmallGamePackage($channel, $package)) {
                    throw new \Exception('小游戏投放不需要创建短链');
                }

                if (69 == $channel) {
                    throw new \Exception('不能选择广点通新渠道创建监控短链');
                }

                if (!isset($channelMap[$channel])) {
                    throw  new \Exception('未找到对应渠道信息');
                }

                $packageInfo = $generalServ->getPackageInfo($package);

                if (empty($packageInfo)) {
                    throw new \Exception('非法的包号');
                }

                $packageInfo = array_change_key_case($packageInfo);
                $gameId      = $packageInfo['game_id'];
                $cpGameId    = $packageInfo['cp_game_id'];

                $d = [
                    'channel_id' => $channel,
                    'cp_game_id' => $cpGameId,
                    'game_id'    => $gameId,
                    'package_id' => $package,
                    'ext'        => $ext ?? '',
                    'add_time'   => $now,
                    'user_id'    => $userId,
                ];

                if ($channel == 132) {
                    if (empty($downloadLink)) {
                        throw new \Exception('请填写下载链接!');
                    }

                    $downloadLink = $foo['download_link'];

                    if (!Zakia::validUrl($downloadLink)) {
                        throw new \Exception('下载地址格式不正确,请以http或https开头!');
                    }

                    $d['download_link'] = $downloadLink;
                }

                $channelInfo = $channelMap[$channel];

                if (Zakia::checkPackageIsIOS($package)) {
                    $backParam = $channelInfo['back_param_ios'] ?? '';
                }
                else {
                    $backParam = $channelInfo['back_param_android'] ?? '';
                }

                //小程序和信息流同个渠道 带xcx
                if (in_array($package, $packageArr) && strpos($channelInfo["channel_code"], "xcx") === false) {
                    $backParam = $channelInfo['back_param_ios'] . '&xcx=1';
                }

                //抖音小程序
                if ($iosPackageStr) {
                    $backParam .= $iosPackageStr;
                }

                if ($num == 1) {
                    $suffixRangeLeft = $suffixRangeRight = 1;
                }

                $length = max(strlen($suffixRangeRight), 2);

                for ($i = $suffixRangeLeft; $i <= $suffixRangeRight; $i++) {
                    $tmp     = $d;
                    $aidN    = $aid;
                    $iosKeyN = null;

                    if ($num != 1) {
                        $aidN .= '_' . str_pad($i, $length, '0', STR_PAD_LEFT);
                    }

                    $tmp['aid'] = $aidN;

                    $isDuplicate = $ndb
                        ->table(DataSpyTable::SV_LINK_CONF)
                        ->select()
                        ->where([
                            'channel_id' => $channel,
                            'game_id'    => $gameId,
                            'package_id' => $package,
                            'aid'        => $aid,
                        ])->count();

                    if ($isDuplicate) {
                        throw new \Exception('请勿重复提交');
                    }

                    if (in_array($channel, [8405, 8407, 8409])) {
                        if (substr($package, -4, 4) === '0099') {
                            throw new \Exception("包号选项只能填写安卓包号");
                        }
                    }

                    if (
                        in_array($channel, [8405, 8407, 8409])
                        && !empty($ext)
                    ) {
                        if (count(explode(',', $ext)) > 1) {
                            throw new \Exception("分包选项只能填写一个包号");
                        }

                        if (substr($ext, -4) !== '0099') {
                            throw new \Exception('分包选项只能填写IOS包号');
                        }

                        $iosTmp               = $tmp;
                        $iosTmp['package_id'] = $ext;

                        $chillId = $ndb->table(DataSpyTable::SV_LINK_CONF)->insertOne($iosTmp);

                        if (!$chillId) {
                            throw new \RuntimeException("IOS分包短链创建失败");
                        }
                        else {
                            $iosKeyN = $chillId;
                        }
                    }

                    $id = $ndb->table(DataSpyTable::SV_LINK_CONF)->insertOne($tmp);

                    if (!$id) {
                        throw new \Exception('新增记录错误');
                    }

                    if ($channel == 23) {
                        $serviceApi = "https://data.910app.com:8006/ad/ad_click?package_id={$package}&channel=";
                    }
                    elseif ($channel == 1105) {
                        $serviceApi = "https://data.910app.com/ad/ad_click?package_id={$package}&channel=";
                    }
                    elseif ($channel == 32) {
                        // 华为信息流
                        $serviceApi = "https://data.910app.com/ad/ad_click?package_id={$package}&sm_channel=";
                    }
                    else {
                        $serviceApi = "https://data.910app.com/ad/ad_click?package_id={$package}&channel=";
                    }

                    $channelCode = $channelInfo['channel_code'];
                    $svLink      = $serviceApi . $channelCode . '&key=' . $id . $backParam;

                    if (in_array($channel, [8405, 8407])) {
                        // 星图(直播投流）|| 星图(直播自然流)
                        // 星图平台-非转化
                        $serviceApi      = "https://data.910app.com/ad/ad_click?package_id={$package}&channel=";
                        $xingtuBackParam = "&ip=__IP__&time=__TS__&os=__OS__&callback_url=__CALLBACK_URL__&ipv6=__IPV6__&mode=__MODEL__&demand_id=__DEMAND_ID__&item_id=__ITEM_ID__&callback_param=__CALLBACK_PARAM__";
                        if ($ext) {
                            $xingtuBackParam .= "&ext_package_id=" . $ext;

                            if ($iosKeyN) {
                                $xingtuBackParam .= '&ios_key=' . $iosKeyN;
                            }
                        }

                        $svLink = $serviceApi . $channelCode . '&key=' . $id . $xingtuBackParam;
                    }
                    elseif ($channel == 8409) {
                        // 星图(短视频)
                        // 原星图平台-转化
                        $serviceApi      = "https://data.910app.com/ad/ad_click?package_id={$package}&channel=";
                        $xingtuBackParam = "&ip=__IP__&time=__TS__&os=__OS__&callback_url=__CALLBACK_URL__&ipv6=__IPV6__&mode=__MODEL__&demand_id=__DEMAND_ID__&item_id=__ITEM_ID__&callback_param=__CALLBACK_PARAM__&md5_idfa=__IDFA_MD5__&md5_oaid=__OAID_MD5__&md5_android_id=__ANDROIDID_MD5__&md5_imei=__IMEI_MD5__";
                        if (!empty($ext)) {
                            $xingtuBackParam .= "&ext_package_id=" . $ext;

                            if ($iosKeyN) {
                                $xingtuBackParam .= '&ios_key=' . $iosKeyN;
                            }
                        }

                        $svLink = $serviceApi . $channelCode . '&key=' . $id . $xingtuBackParam;
                    }

                    // H5
                    if ($channel == 132) {
                        $svLink = 'https://siyu.wangyoubuluo.com/ad_plaform_v1/h5?traceId=' . \Plus::$service->dataEncryptor->encryptionString((string)$id);
                        $planId = $this->getSelfRestraintPlanId($i);

                        $this->insertPlanInfo([
                            'plan_id'    => $planId,
                            'plan_name'  => $aidN,
                            'channel_id' => $channel,
                            'cp_game_id' => $cpGameId,
                            'game_id'    => $gameId,
                            'package_id' => $package,
                            'user_id'    => $userId,
                        ]);

                    }

                    /**
                     * @date 2024/01/23
                     * @desc 广点通3.0兼容(过渡)
                     * 选择为3.0版本的时, 短链格式先不读中台配置写死在代码中
                     */
                    if ($channel == 4) {
                        if (!empty($foo['ad_version'])) {
                            if ($foo['ad_version'] == '3.0') {
                                $serviceApi = "https://data.910app.com/ad/ad_click?package_id={$package}&channel={$channelCode}&key={$id}";

                                if (Zakia::checkPackageIsIOS($package)) {
                                    $gdtParamsV3 = "&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&click_id=__CLICK_ID__&time=__CLICK_TIME__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&md5_idfa=__MUID__&os=__DEVICE_OS_TYPE__&ip=__IP__&advertiser_id=__ACCOUNT_ID__&ad_version=3.0&sm_format_version=2.0";
                                }
                                else {
                                    $gdtParamsV3 = "&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&click_id=__CLICK_ID__&time=__CLICK_TIME__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&md5_android_id=__HASH_ANDROID_ID__&md5_oaid=__HASH_OAID__&md5_imei=__MUID__&os=__DEVICE_OS_TYPE__&ip=__IP__&advertiser_id=__ACCOUNT_ID__&ad_version=3.0&sm_format_version=2.0&creative_components_info=__CREATIVE_COMPONENTS_INFO__&element_info=__ELEMENT_INFO__";
//                                    $gdtParamsV3 = "&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&click_id=__CLICK_ID__&time=__CLICK_TIME__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&md5_android_id=__HASH_ANDROID_ID__&md5_oaid=__HASH_OAID__&md5_imei=__MUID__&os=__DEVICE_OS_TYPE__&ip=__IP__&advertiser_id=__ACCOUNT_ID__&ad_version=3.0&sm_format_version=2.0";
                                }

                                $svLink = $serviceApi . $gdtParamsV3;

                                if (!empty($iosPackageStr)) {
                                    $svLink .= $iosPackageStr;
                                }
                            }
                        }
                    }

                    /**
                     * @date 2024/01/24
                     * @desc 广点通-微信小游戏渠道同上
                     */
                    if ($channel == 1105) {
                        if (!empty($foo['ad_version'])) {

                            if ($foo['ad_version'] == '3.0') {
                                $serviceApi = "https://data.910app.com/ad/ad_click?package_id={$package}&channel={$channelCode}&key={$id}";
                                $backParam  = '&advertiser_id=__ACCOUNT_ID__&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&oaid=__WECHAT_OPEN_ID__&report_col_impression_id=__IMPRESSION_ID__&os=__DEVICE_OS_TYPE__&report_col_click_id=__CLICK_ID__&time=__CLICK_TIME__&report_col_request_id=__REQUEST_ID__&ad_version=3.0&sm_format_version=2.0&creative_components_info=__CREATIVE_COMPONENTS_INFO__&element_info=__ELEMENT_INFO__';
//                                $backParam  = '&advertiser_id=__ACCOUNT_ID__&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&oaid=__WECHAT_OPEN_ID__&report_col_impression_id=__IMPRESSION_ID__&os=__DEVICE_OS_TYPE__&report_col_click_id=__CLICK_ID__&time=__CLICK_TIME__&report_col_request_id=__REQUEST_ID__&ad_version=3.0&sm_format_version=2.0';
                                $svLink = $serviceApi . $backParam;
                                if (!empty($iosPackageStr)) {
                                    $svLink .= $iosPackageStr;
                                }
                            }

                        }

                    }

                    if ($channel == 132 && isset($planId)) {
                        $insertData = [
                            'sv_link' => $svLink,
                            'plan_id' => $planId,
                        ];
                    }
                    else {
                        $insertData = [
                            'sv_link' => $svLink,
                        ];
                    }

                    $ndb
                        ->table(DataSpyTable::SV_LINK_CONF)
                        ->update($insertData)
                        ->where('id', $id)
                        ->run();

                    // 分包扩展
                    $ndb->table(DataSpyTable::TB_AD_SV_LINK_EXT)->delete(['sv_key' => $id])->run();
                    if (!empty($ext)) {
                        $packages = explode(',', $ext);
                        foreach ($packages as $pack) {
                            $ndb
                                ->table(DataSpyTable::TB_AD_SV_LINK_EXT)
                                ->insertOne([
                                    'sv_key'     => $id,
                                    'package_id' => $pack,
                                    'channel_id' => $channel,
                                    'add_time'   => $now,
                                ]);
                        }
                    }

                    $result[] = [
                        'id'        => $id,
                        'sv_link'   => $svLink,
                        'plan_name' => $aidN ?? '',
                    ];

                }
            }

            return $result;
        });
    }


    /**
     * @param               $data
     * @param Database|null $db
     *
     * @return int|string|null
     */
    public function insertOne($data, Database $db = null)
    {
        if (is_null($db)) {
            $db = $this->getConn();
        }

        return $db
            ->table(DataSpyTable::SV_LINK_CONF)
            ->insertOne($data);
    }

    /**
     * 分包扩展包
     *
     * @param string|int    $svKey
     * @param array         $data
     * @param Database|null $db
     *
     * @return false|void
     */
    public function extPackage($svKey, $data, Database $db = null)
    {
        if (is_null($db)) {
            $db = $this->getConn();
        }
        $now       = date('Y-m-d H:i:s');
        $packages  = explode(',', $data['ext']);
        $channelId = $data["channel_id"];

        $qb = $db->table(DataSpyTable::TB_AD_SV_LINK_EXT);
        $qb
            ->delete(['sv_key' => $svKey])
            ->run();

        if (!$data["ext"]) return false;

        foreach ($packages as $package) {
            $db
                ->table(DataSpyTable::TB_AD_SV_LINK_EXT)
                ->insertOne([
                    'sv_key'     => $svKey,
                    'package_id' => $package,
                    'channel_id' => $channelId,
                    'add_time'   => $now,
                ]);
        }

    }

    /**
     * @param $id
     *
     * @return mixed
     * @throws \Throwable
     */
    public function deleteById($id)
    {
        $db = $this->getConn();

        return $db->transaction(static function (Database $ndb) use ($id) {
            $ndb
                ->table(DataSpyTable::SV_LINK_CONF)
                ->delete(['id' => $id])
                ->run();

            $ndb
                ->table(DataSpyTable::TB_AD_SV_LINK_EXT)
                ->delete(['sv_key' => $id])
                ->run();
        });
    }

    /**
     * @param Database $db
     * @param          $params
     * @return void
     * @throws \app\extension\Exception\ParameterException
     * @throws \Exception
     */
    protected function createCompanyInsideLink(Database &$db, $params)
    {
        $smMark = '910910';

        [
            'channel_id'   => $channel,
            'channel_code' => $channelCode,
            'package_id'   => $package,
            'aid'          => $aid,
            'num'          => $num,
        ] = $params;

        $bizHost     = Uri::getHost();
        $url         = $bizHost . Uri::URI_WX_LINK;
        $userId      = \Plus::$service->admin->getUserId();
        $packageInfo = (new GeneralOptionServ())->getPackageInfo($package);

        if (!empty($packageInfo)) {
            $packageInfo = array_change_key_case($packageInfo);
        }

        for ($i = 0; $i < $num; $i++) {
            $newAid       = ($i > 0) ? $aid . '_' . $i : $aid;
            $milliseconds = round(microtime(true) * 1000) . $i;
            $planId       = $smMark . $milliseconds;
            $req          = new Request($url);

            $adpPlanInfo = [
                'cp_game_id' => $packageInfo['cp_game_id'] ?? 0,
                'game_id'    => $packageInfo['game_id'] ?? 0,
                'package_id' => $packageInfo['package_id'] ?? 0,
                'channel_id' => $channel,
                'plan_id'    => $planId,
                'plan_name'  => $newAid,
                'user_id'    => $userId,
            ];

            // 新增计划信息
            $this->insertPlanInfo($adpPlanInfo);
            $response = $req->post(json_encode([
                [
                    'channel_id'      => $channel,
                    'channel'         => $channelCode,
                    'package_id'      => $package,
                    'plan_id'         => $planId,
                    'source_sign_top' => 'sm_inline',
                    'is_sm_domain'    => 1,
                ],
            ]));

            $response = \json_decode($response, true);

            if (JSON_ERROR_NONE != json_last_error()) {
                throw new \Exception('json解析错误');
            }

            $responseCode = $response['code'] ?? -1;
            if ($responseCode != 200) {
                throw new \Exception('请求生成锻炼时发生错误, 请排查');
            }

            $urls = $response['data'] ?? [];

            array_walk($urls, fn(&$item) => $item = 'https://' . $item);

            // 插入SV_LINK
            $db->table('dataspy.tb_ad_svlink_conf')->insertOne([
                'CHANNEL_ID' => $channel,
                'CP_GAME_ID' => $packageInfo['cp_game_id'] ?? 0,
                'GAME_ID'    => $packageInfo['GAME_ID'] ?? 0,
                'PACKAGE_ID' => $package,
                'PLAN_ID'    => $planId,
                'AID'        => $newAid,
                'SV_LINK'    => implode(',', $urls),
                'USER_ID'    => $userId,
            ]);
        }

    }

    /**
     * @param $suffix
     * @return string
     */
    protected function getSelfRestraintPlanId($suffix = 0)
    {
        $smMark       = '910910';
        $milliseconds = round(microtime(true) * 1000);
        return $smMark . $milliseconds . $suffix;
    }


    /**
     * @param Database $db
     * @param array    $data
     * @return void
     */
    protected function insertPlanInfo(array $data)
    {
        $insertData = [
            'CHANNEL_ID'  => $data['channel_id'],
            'CP_GAME_ID'  => $data['cp_game_id'],
            'GAME_ID'     => $data['game_id'],
            'PACKAGE_ID'  => $data['package_id'],
            'CAMPAIGN_ID' => 0,
            'PLAN_ID'     => $data['plan_id'],
            'PLAN_NAME'   => $data['plan_name'],
            'USER_ID'     => $data['user_id'],
        ];

        FakeDB::connection('adp_platform')
            ->table('adp_platform.tb_adp_plan')
            ->insertOne($insertData);

        FakeDB::connection('adp_platform')
            ->table('adp_platform.tb_adp_plan_base')
            ->insertOne($insertData);
    }


}
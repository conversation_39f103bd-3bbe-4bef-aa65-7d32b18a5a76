<?php

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

/**
 * 绅凯数据上报
 *
 */
class ShenKai extends AdBaseInterface
{
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }

    private function upload($info, $type)
    {
        $callbackUrl = \urldecode($info['CALLBACK_URL']);
        $http        = new Http($callbackUrl);
        $res         = $http->get();

        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'shen_kai';
        $logInfo['request']      = json_encode(['url' => $callbackUrl]);
        //记录上报结果
        $logInfo['response'] = $res;
        $resArr              = json_decode($res, true);
        if (isset($resArr['code']) && $resArr['code'] == 200) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        $this->log($info, $logInfo, $res, $callbackUrl);
    }


    public function uploadRegister($info, $ext = [])
    {
        // TODO: Implement uploadRegister() method.
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadPay($info, $ext = [])
    {
        // TODO: Implement uploadPay() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

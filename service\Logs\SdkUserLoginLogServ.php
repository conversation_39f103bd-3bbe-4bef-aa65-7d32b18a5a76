<?php

namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;
use app\service\General\Helpers\BigDataDwdTable;
use app\service\Logs\Components\Matcher\UserLoginMatch;
use app\util\Common;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Query\SelectQuery;

/**
 * 账号登录日志查询
 */
class SdkUserLoginLogServ
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     * @return SelectQuery
     */
    public function getInfoQueryBuilder(
        array $params = [],
        array $paginate = [],
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from(BigDataDwdTable::DwdSdkUserLogin . ' as a1');

        $adminSql = \Plus::$service->admin->powerSubSQL();

        $qb->innerJoin(new Fragment($adminSql), 'power')->on([
            'a1.package_id' => 'power.package_id'
        ]);

        if (!empty($params['range_date_start']) || !empty($params['range_date_end'])) {
            $params['dt'] = array_filter([
                $params['range_date_start'] ?? null,
                $params['range_date_end'] ?? null
            ]);

            unset($params['range_date_start'], $params['range_date_end']);
        }

        $matcher = new UserLoginMatch([
            'device_code' => ['device_code', 'idfa', 'oaid', 'device_key'],
            'package_id'  => 'a1.package_id',
            'game_id'     => 'a1.game_id',
            'cp_game_id'  => 'a1.cp_game_id',
        ]);
        $matcher($qb, $params);

        $qb->columns($columns);

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $len] = $paginate;
            $qb->limit($len)->offset(($page - 1) * $len);
        }

        return $qb;
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     * @return array
     * @throws \Exception
     */
    public function getList(array $params = [], array $paginate = [], array $groups = [], array $sort = [], array $columns = []): array
    {
        $qb    = $this->getInfoQueryBuilder($params, [], $groups, $sort, $columns);
        $count = (clone $qb)->count();

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $len] = $paginate;
            $qb->limit($len)->offset(($page - 1) * $len);
        }

        return [
            'total' => $count,
            'list'  => $qb->fetchAll()
        ];
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     * @throws \Exception
     */
    protected function getConn()
    {
        if ($dorisIndex = Common::pingDorisIndex()) {
            return FakeDB::connection($dorisIndex);
        }
        else {
            throw new \RuntimeException('与数据库连接断开');
        }
    }
}
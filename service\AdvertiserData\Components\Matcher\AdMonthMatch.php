<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\service\AdvertiserData\Components\Matcher\Traits\AdChannelAble;
use app\service\AdvertiserData\Components\Matcher\Traits\DepartmentMatchAble;
use app\service\General\Matcher\Traits\TagsMatcher;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

/**
 * where 条件匹配
 *
 *
 */
class AdMonthMatch extends BaseMatch
{
    use AdChannelAble, DepartmentMatchAble, TagsMatcher;

    /**
     * @return array
     */
    public function processLine(): array
    {
        $newLine = [
            [$this, 'matchTDay'],
            [$this, 'matchChannelMainId'],
            [$this, 'matchChannelId'],
            [$this, 'matchDepartment'],
            [$this, 'matchUserId'],
            [$this, 'matchGameTagsQb'],
            [$this, 'matchPackageTagsQb'],
            [$this, 'matchChannelTagsQb'],
            [$this, 'matchChannelMainTagsQb'],
        ];

        return array_merge(parent::processLine(), $newLine);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return void
     * @throws \Exception
     */
    protected function matchTDay(&$qb, array $params)
    {
        if (
            empty($params['range_date_start']) || empty($params['range_date_end'])
        ) return;

        $field     = $this->getReflectField('month');
        $rangeDate = array_filter([
            $params['range_date_start'],
            $params['range_date_end'],
        ]);
        sort($rangeDate);
        $rangeDate = array_unique($rangeDate);

        foreach ($rangeDate as &$foo) {
            $foo = (new \DateTime($foo))->format('Ym');
        }

        if (count($rangeDate) === 1) {
            $qb->where($field, $rangeDate[0]);
        }
        else {
            $qb->where($field, 'between', $rangeDate[0], $rangeDate[1]);
        }
    }
}
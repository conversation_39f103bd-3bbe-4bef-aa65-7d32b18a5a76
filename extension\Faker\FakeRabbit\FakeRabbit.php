<?php

namespace app\extension\Faker\FakeRabbit;

use framework\rabbit\BaseQueue;
use PhpAmqpLib\Channel\AbstractChannel;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;

/**
 *
 */
class FakeRabbit extends BaseQueue
{
    /**
     * rabbit 配置
     *
     * @var Configuration
     */
    protected Configuration $config;

    protected AMQPStreamConnection $amqp;

    /**
     * 只做初始化配置
     *
     * @return void
     */
    public function init()
    {
        $this->config = new Configuration(get_object_vars($this));
    }

    /**
     * @throws \Exception
     */
    public function getConnection(): AMQPStreamConnection
    {
        return $this->connect();
    }

    /**
     * @throws \Exception
     */
    public function initConnection()
    {
        $this->connect();
    }

    /**
     * @throws \Exception
     */
    protected function connect(): AMQPStreamConnection
    {
        if (empty($this->amqp)) {
            $config = $this->config;

            $this->amqp = new AMQPStreamConnection(
                $config->host,
                $config->port,
                $config->username,
                $config->password,
                $config->vhost,
                $config->insist,
                $config->login_method,
                $config->login_response,
                $config->locale,
                $config->connection_timeout,
                $config->read_write_timeout,
                $config->context,
                $config->keepalive,
                $config->heartbeat
            );
        }

        return $this->amqp;
    }

    /**
     *
     * @param $key
     * @param $value
     *
     * @return void
     */
    public function setConfig($key, $value)
    {
        if (method_exists($this->config, $key)) {
            $this->config->{$key} = $value;
        }
    }

    /**
     * 单次推送单挑消息
     *
     * @param string $topicName
     * @param mixed  $msg
     * @param bool   $durable
     *
     * @return void
     */
    function put(string $topicName, $msg, bool $durable = true)
    {
//        parent::checkTopic($topicName);

        if (!is_scalar($msg)) {
            $msg = \json_encode($msg);
        }

        try {
            $producer = $this->getConnection();

            $args    = new AMQPTable($this->config->queue_declare_args);
            $channel = $producer->channel();
            $channel->queue_declare($topicName, false, $durable, false, false, false, $args);

            $mode    = $durable ? AMQPMessage::DELIVERY_MODE_PERSISTENT : AMQPMessage::DELIVERY_MODE_NON_PERSISTENT;
            $message = new AMQPMessage($msg, ['delivery_mode' => $mode]);

            $this->confirmBasicPublish($channel, $message, $topicName);
            $channel->close();
        }
        catch (\Exception $e) {
            \Plus::$app->log->alert($topicName . ':' . $e->getMessage(), [], 'rabbitmq');
            \Plus::$app->log->alert($msg, [], 'rabbitmq_' . $topicName);
        }
    }

    function consume(callable $listener, string $topicName)
    {
        // TODO: Implement consume() method.
    }

    /**
     * @throws \Exception
     */
    public function __destruct()
    {
        if (!empty($this->amqp)) {
            $this->amqp->close();
        }
    }

    /**
     * @param             $channel
     * @param AMQPMessage $message
     * @param string      $topicName
     * @param string      $exchange
     *
     * @return void
     */
    protected function confirmBasicPublish(&$channel, AMQPMessage $message, string $topicName, string $exchange = '')
    {
        $channel->confirm_select();
        // nack,rabbitMQ内部错误时触发
        $channel->set_nack_handler(static function (AMQPMessage $msg) {
            \Plus::$app->log->alert('提交失败:{exchange}|{msg}',
                ['exchange' => $msg->getExchange(), 'msg' => $msg->getBody()], 'rabbitmq');
        });
        //  消息到达交换机,但是没有进入合适的队列,消息回退
        $channel->set_return_listener(static function ($replyCode, $replyText, $exchange, $routingKey, AMQPMessage $msg) {
            \Plus::$app->log->alert('消息回退:{replyText}|{exchange}|{msg}', [
                'replyText' => $replyText,
                'exchange'  => $exchange,
                'msg'       => $msg->getBody(),
            ], 'rabbitmq');
        });
        // 等待接收服务器的 ack 和 nacks
        $channel->basic_publish($message, $exchange, $topicName, true);

        $channel->wait_for_pending_acks_returns(10);
    }
}
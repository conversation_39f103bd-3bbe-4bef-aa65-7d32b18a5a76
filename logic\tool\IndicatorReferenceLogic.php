<?php

namespace app\logic\tool;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\StringAKA;
use app\service\Tool\IndicatorReferenceServ;

/**
 * 逻辑层
 * <AUTHOR>
 * @desc LTV&ROI参考标准处理逻辑
 */
class IndicatorReferenceLogic
{
    /**
     *
     * @desc 保存指标参考标准
     * @param array $data
     * @return mixed
     * @throws \Throwable
     */
    public function save(array $data)
    {
        // 暂时写死
        $data['tags']     = json_encode(['LTV']);
        $data['operator'] = \Plus::$service->admin->getUserId();

        if (!empty($data['children'])) {
            $data['children'] = $this->changeFormat($data['children']);
        }

        if (empty($data['id'])) {
            // 没有带id视作新增
            if (empty($data['children']) && empty($data['name'])) {
                throw new \Exception('新增参数缺失');
            }
        }

        return (new IndicatorReferenceServ())->insertOrUpdateOne($data);
    }

    /**
     * @param array $chillList
     * @return void
     */
    private function appendChildDataIndex(array &$chillList)
    {
        foreach ($chillList as &$d) {
            $title = $d['indicator_title'];
            // 暂时写死
            if ($title == '新用户成本') {
                $d['indicator_index'] = json_encode(['new_user_cost']);
            }

            if (str_contains($title, 'LTV')) {
                $i                    = str_replace('LTV', '', $title);
                $d['indicator_index'] = json_encode(['ltv_' . $i]);
            }
        }
    }

    /**
     * @param array $chillList
     * @return array
     */
    private function appendRoiData(array $chillList): array
    {
        // find 新用户成本
        $newUserCost = 0.00;

        foreach ($chillList as $d) {
            $title = $d['indicator_title'];
            if ($title == '新用户成本') {
                $newUserCost = $d['standard'] ?? 0.00;
                break;
            }
        }

        foreach ($chillList as $d) {
            $title = $d['indicator_title'];
            if (str_contains($title, 'LTV')) {
                $i              = str_replace('LTV', '', $title);
                $indicatorIndex = json_encode(['roi_' . $i]);
                $standard       = round(($d['standard'] / $newUserCost) ?? 0.00, 4);
                $chillList[]    = [
                    'indicator_title' => 'ROI' . $i,
                    'standard'        => $standard,
                    'indicator_index' => $indicatorIndex,
                ];
            }
        }

        return $chillList;
    }


    /**
     * @param bool $reset
     * @return array|mixed|mixed[]|\Redis
     * @throws \RedisException
     */
    public function getFirstGradeList(bool $reset = false)
    {
        $key = 'spy:conf:indicator_reference_first_grade_list';

        if ($reset) {
            $data = [];
        } else {
            $data = \Plus::$app->redis->hGetAll($key);
        }

        if (!empty($data)) {
            return $data;
        }

        $result = (new IndicatorReferenceServ())->getAllIndicator();
        $data   = $result['list'] ?? [];

        if (!empty($data)) {
            $data  = array_column($data, 'name', 'id');
            $older = \Plus::$app->redis->hGetAll($key);

            if (!empty($older)) {
                foreach ($older as $kk => $item) {
                    \Plus::$app->redis->hDel($key, $kk);
                }
            }

            \Plus::$app->redis->hMSet($key, $data);
        }

        return $data;
    }

    /**
     *
     * @param array $data
     * @return array
     */
    private function changeFormat(array $data): array
    {
        $r = [];

        foreach ($data as $kk => $chill) {
            $kIndex = null;
            if (StringAKA::hasChinese($kk)) {
                if ($kk == '新用户成本') {
                    $kIndex = json_encode(['new_user_cost']);
                }
            } else {
                $kIndex = json_encode(Arr::wrap(StringAKA::toUnderscore($kk)));
            }

            $r[] = [
                'indicator_title' => $kk,
                'standard'        => $chill,
                'indicator_index' => $kIndex,
            ];
        }

        return $r;
    }
}

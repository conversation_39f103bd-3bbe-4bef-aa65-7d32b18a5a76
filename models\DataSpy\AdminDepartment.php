<?php

namespace app\models\DataSpy;


use Plus\MVC\Model\ActiveRecord;


/**
 * @AdminDepartment
 *                 spy 部门表
 *
 *
 * @property int    id
 * @property int    pid
 * @property string name
 * @property string ext
 */
class AdminDepartment extends ActiveRecord
{
    public $_primaryKey = 'id';

    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->dataspy;
    }
}
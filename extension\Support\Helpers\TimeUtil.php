<?php

namespace app\extension\Support\Helpers;

class TimeUtil
{
    /**
     * 根据时间段划周
     *
     * @param $begin
     * @param $end
     *
     * @return array
     * @throws \Exception
     */
    public static function divideWeekByRangeDate($begin, $end): array
    {
        $result  = [];
        $dayDiff = (int)days_apart($begin, $end);

        $cycle = floor($dayDiff / 7);

        for ($i = 0; $i <= $cycle; $i++) {
            $next     = $i + 1;
            $result[] = [
                'begin_date' => date('Y-m-d', strtotime("{$begin} +{$i}week")),
                'end_date'   => date('Y-m-d', strtotime("{$begin} +{$next}week -1day")),
            ];
        }

        $cycle += 1;
        $end   = date('Y-m-d', strtotime("{$begin} +{$cycle}week -1day"));

        return [
            'begin' => $begin,
            'end'   => $end,
            'cycle' => $result,
        ];
    }

    /**
     * 获取相隔月份的月份日期
     *
     * @param string $start
     * @param string $end YY-mm-dd || YY-mm
     *
     * @return array
     * @throws \Exception
     */
    public static function getMonthRange(string $start, string $end): array
    {
        $start = new \DateTime($start);
        $end   = new \DateTime($end);
        $start->modify('first day of this month');
        $end->modify('first day of next month');

        $step   = \DateInterval::createFromDateString('1 month');
        $period = new \DatePeriod($start, $step, $end);
        $months = [];

        foreach ($period as $date) {
            $months[] = $date->modify('first day of this month')->format('Y-m-d');
        }

        return $months;
    }

    /**
     * @param ...$date
     *
     * @return array
     */
    public static function changeDateTime(...$date): array
    {
        foreach ($date as &$item) {
            try {
                if (!$item instanceof \DateTime) {
                    $item = new \DateTime($item);
                }
            }
            catch (\Exception $e) {
                $item = null;
            }
        }

        return $date;
    }

    /**
     * 获取毫秒时间戳
     *
     * @return false|string
     */
    public static function getMillisecond()
    {
        [$mirco, $sec] = \explode(' ', \microtime());
        $mirco = (float)sprintf('%.0f', (floatval($mirco) + floatval($sec)) * 1000);

        return substr($mirco, 0, 13);
    }

    /**
     * @param $start
     * @param $end
     * @param int $step
     * @return \Generator
     * @throws \Exception
     */
    public static function rangeDateGen($start, $end, int $step = 1): \Generator
    {
        $n       = days_apart($start, $end);
        $oneDate = new \DateTime($start);
        $step    = new \DateInterval('P' . $step . 'D');

        for ($i = 0; $i <= $n; $i++) {
            yield clone $oneDate;
            $oneDate->add($step);
        }
    }

    /**
     * 获取当前时间到第二天的秒数
     * @return float|int
     */
    public static function getSecondsUntilTomorrow()
    {
        $tomorrowMidnight = (new \DateTime())->modify('+1 day')->setTime(0, 0);
        $interval         = (new \DateTime())->diff($tomorrowMidnight);

        return $interval->s + ($interval->i * 60) + ($interval->h * 3600);
    }
}
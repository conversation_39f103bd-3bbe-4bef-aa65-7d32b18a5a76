<?php

namespace app\service\Media\Components\Matcher;

use app\extension\Support\Helpers\DBHelper\MatcherAbstract;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\SelectQuery;

/**
 *
 */
class MediaVideoMatcher extends TaskMatcher
{

    /**
     *
     *
     * @return array|\Closure[]
     */
    protected function matchFnList(): array
    {
        $new = [
            'account_id'        => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'video_fitid'       => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'media_video_title' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::likeBuild($qb, $key, $value);
            },
            'media_video_topic' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::likeBuild($qb, $key, $value);
            },
            'task_id'           => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
        ];

        return array_merge(parent::matchFnList(), $new);
    }
}
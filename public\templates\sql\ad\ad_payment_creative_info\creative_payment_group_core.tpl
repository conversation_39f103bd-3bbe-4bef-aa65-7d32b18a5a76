{include file="sql/ad/ad_payment_creative_info/creative_payment_common.tpl"}
select
ch1.main_account,
ch1.core_account,
any(pay_count)               as pay_count,
any(pay_money)               as pay_money,
any(newlogin_time)           as newlogin_time,
any(last_paid_time)          as last_paid_time,
any(last_paid_relative_time) as last_paid_relative_time,
sum(ch2.money)               as total_paid_money
from (select * from bigdata_dws.dws_ad_account_money_daily where pay_date = DATE(NOW())) ch2
right join (
select
q2.main_account,
q1.core_account,
{if $paid_mode == 'pay_money'}
    sum(IF(q1.pay_time between '{$params['range_date'][0]} 00:00:00' and '{$params['range_date'][1]} 23:59:59', 1, 0))     as pay_count,
    sum(IF(q1.pay_time between '{$params['range_date'][0]} 00:00:00' and '{$params['range_date'][1]} 23:59:59', money, 0)) as pay_money,
{else}
    sum(IF(date(q1.pay_time) = date(q2.newlogin_time), 1, 0))     as pay_count,
    sum(IF(date(q1.pay_time) = date(q2.newlogin_time), money, 0)) as pay_money,
{/if}
SUM(money)                                                                                      as total_paid_money,
any(q2.newlogin_time)                                                                           as newlogin_time,
max(pay_time)                                                                                   as last_paid_time,
max(IF(q1.pay_time between '{$params['range_date'][0]} 00:00:00' and '{$params['range_date'][1]} 23:59:59', q1.pay_time, null)) as last_paid_relative_time
from ddc_platform.dwd_sdk_adsource_game q2
join (
select lt.*
from ddc_platform.dwd_sdk_user_payment lt
join (select distinct core_source from source_id_list) rt on lt.SOURCE_ID = rt.core_source
where pay_result = 1
) q1 on q1.source_id = q2.source_id
{if !empty($params)}
    {assign var="mf" value=1}
    {foreach $params as $ff => $fi}
        {if $ff eq 'range_date'}
            {if !$mf} and {else} where {$mf=0} {/if}
            DATE(pay_time) between '{$fi[0]}' and '{$fi[1]}'
            {continue}
        {/if}
        {if $ff eq 'main_account'}
            {if !$mf} and {else} where {$mf=0} {/if}
            {if is_array($foo)}
                q2.main_account in ({$fi|join:','})
            {else}
                q2.main_account = '{$fi}'
            {/if}
            {continue}
        {/if}
    {/foreach}
{/if}
group by q1.core_account, q2.main_account
{* 排序 *}
{if empty($order_by)}
    order by pay_money desc
{else}
    order by {$order_by}
{/if}
{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}
) ch1 on ch1.main_account = ch2.main_account and ch1.core_account = ch2.core_account
group by ch1.main_account, ch1.core_account
{* 排序 *}
{if empty($order_by)}
    order by pay_money desc
{else}
    order by {$order_by}
{/if}
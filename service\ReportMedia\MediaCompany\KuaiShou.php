<?php

namespace app\service\ReportMedia\MediaCompany;

use app\extension\Support\Helpers\TimeUtil;
use app\service\ReportMedia\Contract\ReportMediaContract;
use http\Client\Request;
use Plus\Net\Http;

/**
 * 快手上报
 *
 */
class <PERSON><PERSON>Shou implements ReportMediaContract
{
    private const EVENT_MAP = [
        'active'   => 1, // 激活
        'register' => 2, // 注册
        'pay'      => 3, // 付费
    ];


    /**
     * @return array|false
     * @throws \Exception
     */
    public function report()
    {
        $options   = [];
        $mircoTime = TimeUtil::getMillisecond();

        if (func_num_args() === 4) {
            [$callbackUri, $item, $event, $num] = func_get_args();

            if ($event != 'pay') {
                return false;
            }
            $options['event_type']      = static::EVENT_MAP['pay'];
            $options['purchase_amount'] = $num;
        }
        else {
            [$callbackUri, $item, $event] = func_get_args();
            $options['event_type'] = static::EVENT_MAP[$event];
        }

        $options['event_time'] = $mircoTime;

        $optionsString = http_build_query($options);
        $callbackUri   .= '&' . $optionsString;

        $request = new Http($callbackUri);

        return [
            'request_uri' => $callbackUri,
            'response'    => $request->get(),
        ];
    }
}
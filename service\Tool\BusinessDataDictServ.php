<?php

namespace app\service\Tool;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Helpers\Zakia;
use Spiral\Database\Database;

class BusinessDataDictServ
{
    protected const CACHE_KEY = 'spy:spy_dict:business_data_dict';

    /**
     * @return void
     * @throws \RedisException
     * @throws \Throwable
     */
    public function updateOrInsert(array $list)
    {
        $store = \Plus::$app->redis;
        $db    = FakeDB::connection('dataspy');

        $db->transaction(function (Database $db) use ($list, $store) {
            foreach ($list as $foo) {
                $indicatorName     = $foo['指标名称'] ?? '';
                $formulaDefinition = $foo['定义或公式'] ?? '';

                if (empty($indicatorName) || empty($formulaDefinition)) {
                    continue;
                }

                $indicatorName = Zakia::convertChinesePunctuationToEnglish($indicatorName);
                $store->hSet(self::CACHE_KEY, $indicatorName, $formulaDefinition);

                $table   = $db->table('dict_business_data');
                $isExist = $table->select()->where('indicator_name', $indicatorName)->count() > 0;

                if ($isExist) {
                    $table
                        ->update([
                            'formula_definition' => $formulaDefinition
                        ])
                        ->where('indicator_name', $indicatorName)
                        ->run();
                }
                else {
                    $table
                        ->insertOne([
                            'indicator_name'     => $indicatorName,
                            'formula_definition' => $formulaDefinition
                        ]);
                }
            }
        });
    }


    /**
     * @throws \RedisException
     */
    public function getAll()
    {
        $store = \Plus::$app->redis;
        $data  = $store->hGetAll(self::CACHE_KEY);

        if (empty($data)) {

            $list = FakeDB::connection('dataspy')
                ->table('dataspy.dict_business_data')
                ->select()
                ->columns(['indicator_name', 'formula_definition'])
                ->fetchAll();

            $data = array_column($list, 'formula_definition', 'indicator_name');
            $store->hMSet(self::CACHE_KEY, $data);
        }

        return $data;
    }

}
<?php

namespace app\util;

/**
 * 配置中心接口
 * @copyright 武汉手盟网络科技有限公司 Copyright (c) 2012-2014 (http://www.910app.com)
 * <AUTHOR>
 * Date: 2016-01-07
 * Time: 11:38
 * phpcs:disable
 */
class BizConfig
{

    public $url          = "http://biz-api.sm910.com";
    public $code         = "cltj";
    public $key          = "xdjfdnne";
    public $pageNo       = 1;
    public $pageSize     = 10000;
    public $interfaceArr = [
        "token"                   => "/api/auth/get-access-token",
        "cp_games"                => "/game/cp-games/list",
        "games"                   => "/game/games/list",
        "app_channel"             => "/channel/app-channel/list",
        "package_channel_main"    => "/channel/package-channel-main/list",
        "packages"                => "/game/packages/list",
        "popularize_media"        => "/channel/popularize-media/list",
        "company"                 => "/company/company/list",
        "sdk_serial_version"      => "/substrate/sdk-serial-version/list",
        "report_condition_config" => "/operation/report-condition-config/list",
        "media_params"            => "/tool/media/find",
        "payway"                  => '/pay/payway/list',
    ];

    /**
     * 获取媒体参数
     * @param $packages
     * @return array|bool
     */
    public function getMediaParams($packages)
    {
        return $this->getConfig($this->interfaceArr['media_params'], ['package_ids' => $packages], true);
    }

    /**
     * 获取扣量上报配置
     *
     * @param $params
     * @return bool|array
     */
    public function getReportDeduction($params = [])
    {
        return $this->getConfig($this->interfaceArr['report_condition_config'], $params);
    }

    /**
     * 获取游戏原名列表
     * @param $params 参数
     * @return bool|string
     */
    public function getCpGames($params = [])
    {
        return $this->getConfig($this->interfaceArr["cp_games"], $params);
    }

    /**
     * 获取游戏统计列表
     * @param $params 参数
     * @return bool|string
     */
    public function getGames($params = [])
    {
        return $this->getConfig($this->interfaceArr["games"], $params);
    }

    /**
     * 获取出包渠道（出包渠道 由game_platform.dd_app_channel 迁移）
     * @param $params 参数
     * @return bool|string
     */
    public function getAppChannel($params = [])
    {
        return $this->getConfig($this->interfaceArr["app_channel"], $params);
    }

    /**
     * 主渠道表 由 game_platform.dd_app_channel_ext_main 迁移
     * @param $params 参数
     * @return bool|string
     */
    public function getPackageChannelMain($params = [])
    {
        return $this->getConfig($this->interfaceArr["package_channel_main"], $params);
    }

    /**
     * 游戏包号配置(20210414)由game_platform.packages 迁移
     * @param $params 参数
     * @return bool|string
     */
    public function getPackages($params = [])
    {
        return $this->getConfig($this->interfaceArr["packages"], $params);
    }

    /**
     * 推广媒体 由 base_conf_platform.tb_ad_channel_conf 迁移
     * @param $params 参数
     * @return bool|string
     */
    public function getPopularizeMedia($params = [])
    {
        return $this->getConfig($this->interfaceArr["popularize_media"], $params);
    }

    /**
     * 公司主体 由 game_platform.sdk_subject + finance_platform.subject_info+oa的数据手动导入
     * @param $params 参数
     * @return bool|string
     */
    public function getCompany($params = [])
    {
        return $this->getConfig($this->interfaceArr["company"], $params);
    }

    /**
     * SDK列表 由 game_platform.sdk_serial_version迁移
     * @param $params 参数
     * @return bool|string
     */
    public function getSdkSerialVersion($params = [])
    {
        return $this->getConfig($this->interfaceArr["sdk_serial_version"], $params);
    }

    private function getConfig($path, $params = [], $post = false)
    {
        $resp  = $this->getAccessToken();
        $token = $resp["data"]["access_token"];
        if (!isset($params["pageNo"])) {
            $params["pageNo"] = $this->pageNo;
        }
        if (!isset($params["pageSize"])) {
            $params["pageSize"] = $this->pageSize;
        }

        return $this->query($this->url . $path, $params, ["Access-Token:{$token}"], $post);
    }

    private function getAccessToken()
    {
        $time = time();
        $sign = md5(sha1($this->code . $time . $this->key));

        return $this->query(
            $this->url . $this->interfaceArr["token"],
            ["code" => "cltj", "time" => $time, "sign" => $sign]
        );
    }

    /**
     * 请求配置接口
     * @param       $url
     * @param array $params
     * @return bool|array
     */
    private function query($url, $params = [], $header = [], $post = false)
    {
        if (!empty($params) && !$post) {
            $url .= "?" . http_build_query($params);
        }
        //初始化
        $curl = curl_init();
        //设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);
        if (!empty($header)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_HEADER, 0);//返回response头部信息
        }

        if ($post) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params, JSON_UNESCAPED_UNICODE));
        }

        //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        //执行命令
        $data = curl_exec($curl);
        //关闭URL请求
        curl_close($curl);

        //显示获得的数据
        return json_decode($data, true);
    }

    /**
     * @param array $params
     * @return array|bool
     */
    public function getPayWay(array $params = [])
    {
        return $this->getConfig($this->interfaceArr["payway"], $params);
    }

}


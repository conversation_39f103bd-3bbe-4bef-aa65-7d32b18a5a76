<?php

namespace app\middware;

use app\service\Admin;
use app\service\user\Core;
use app\util\Jwt;
use Plus;
use Plus\Internal\Config;
use Plus\MVC\Request\Request;
use Plus\MVC\Response\Response;
use Plus\Util\StringUtil;
use Exception;

/**
 * 权限校验中间件
 * <AUTHOR>
 */
class Auth
{
    const SUPPER_LEVEL = 9;

    /**
     * 权限校验
     * @param Request  $request  请求对象
     * @param Response $response 响应对象
     * @return \Generator
     */
    public static function auth(Request $request, Response $response)
    {
        header("Access-Control-Allow-Origin: *");
        header('Access-Control-Allow-Credentials: true');
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header("Access-Control-Allow-Headers: Authorization,content-type,Access-Token,x-requested-with");
            header('Access-Control-Allow-Methods: GET, POST');
            die;
        }

        $permit = new Config('permit');

        // 非登录权限
        $whiteList = $permit->whiteList ?? [];
        // 非控制权限
        $allowList = $permit->allowList ?? [];

        $router = Plus::$app->router;
        $trust  = true;

        $app        = StringUtil::unCamelize($router->getApp(), '-');
        $controller = StringUtil::unCamelize($router->getController(), '-');
        $action     = StringUtil::unCamelize($router->getAction(), '-');
        $path       = "/".implode('/', [$app, $controller, $action]);
        if ($app === $permit->apiModule) {
            $allowList[] = $path;
        }

        try {
            if (!in_array($path, $whiteList)) {
                if (!isset($_SERVER['HTTP_ACCESS_TOKEN'])) {
                    throw new Exception('请重新登录', 401);
                }
                try {
                    $tokenData = self::setUserData($_SERVER['HTTP_ACCESS_TOKEN']);
                } catch (Exception $e) {
                    throw new Exception('token校验失效：' . $e->getMessage(), 401);
                }

                $userType = $tokenData->type??"";
                if($userType=="api"){
                    // API SPY报表 非控制权限
                    $apiAllowList = $permit->apiAllowList ?? [];
                    $allowList = array_merge($allowList,$apiAllowList);
                }

                if (!in_array($path, $allowList)) {
                    //check user api auth
                    \Plus::$service->admin->checkAuth($path);
                }

                if ($app !== $permit->apiModule) {
                    //游戏、渠道、包 特殊权限处理
                    \Plus::$service->admin->specAuthByUser($request,$userType);
                }
                
            }
        } catch (Exception $e) {
            $trust = false;
            $response->setBody(
                json_encode(
                    [
                        'code'    => $e->getCode(),
                        'message' => $e->getMessage(),
                    ]
                )
            );
        }// end try()

        yield $trust;
    }

    /**
     * 解析token 存储用户名
     * @param $token
     * @return mixed
     */
    public static function setUserData($token)
    {
        $data = \Plus::$service->admin->parseAccessToken($token);
        \Plus::$service->admin->setUserData($data->data);
        return $data->data;
    }
}

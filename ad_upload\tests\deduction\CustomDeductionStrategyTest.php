<?php

namespace app\ad_upload\tests\deduction;

use PHPUnit\Framework\TestCase;
use app\ad_upload\deduction\CustomDeductionStrategy;

/**
 * 自定义上报 测试、
 * <AUTHOR>
 * phpcs:disable
 */
class CustomDeductionStrategyTest extends TestCase
{
    private $mockConfig;
    private $mockData;


    protected function setUp(): void
    {
        $this->mockConfig = [
            'id'          => '152',
            'cp_game_id'  => '803',
            'game_id'     => '6791',
            'package_id'  => '0',
            'status'      => '2',
            'add_time'    => '2025-02-14 11:26:16',
            'update_time' => '2025-02-14 11:26:16',
            'operator'    => '许振钧',
            'remark'      => '',
            'config_data' =>
                [
                    'rule' =>
                        [
                            0 =>
                                [
                                    'ratio'         =>
                                        [
                                            'stop'      => '',
                                            'type'      => 1,
                                            'start'     => '',
                                            'amount'    => '2,3,6',
                                            'ratioType' => 2,
                                        ],
                                    'rules'         =>
                                        [
                                            0 =>
                                                [
                                                    'field'    => 'role_level',
                                                    'value'    => 2,
                                                    'fieldArr' =>
                                                        [
                                                            0 => 0,
                                                            1 => 'role_level',
                                                        ],
                                                    'operator' => '>',
                                                ],
                                            1 =>
                                                [
                                                    'field'    => 'payment_count',
                                                    'value'    => 100,
                                                    'fieldArr' =>
                                                        [
                                                            0 => 0,
                                                            1 => 'payment_count',
                                                        ],
                                                    'operator' => '<',
                                                ],
                                            2 =>
                                                [
                                                    'field'    => 'total_payment',
                                                    'value'    => 88,
                                                    'fieldArr' =>
                                                        [
                                                            0 => 0,
                                                            1 => 'total_payment',
                                                        ],
                                                    'operator' => '>=',
                                                ],
                                            3 =>
                                                [
                                                    'field'    => 'core_account',
                                                    'value'    => '123456',
                                                    'fieldArr' =>
                                                        [
                                                            0 => 0,
                                                            1 => 'core_account',
                                                        ],
                                                    'operator' => 'not_in',
                                                ],
                                            4 =>
                                                [
                                                    'field'    => 'ad_new_days',
                                                    'value'    => 2,
                                                    'fieldArr' =>
                                                        [
                                                            0 => 0,
                                                            1 => 'ad_new_days',
                                                        ],
                                                    'operator' => '<',
                                                ],
                                            5 =>
                                                [
                                                    'field'    => 'ad_active_days',
                                                    'value'    => 1,
                                                    'fieldArr' =>
                                                        [
                                                            0 => 0,
                                                            1 => 'ad_active_days',
                                                        ],
                                                    'operator' => '=',
                                                ],
                                        ],
                                    'enabled'       => true,
                                    'conditionName' => '',
                                ],
                        ],
                ],
            'config_type' => '2',
            'name'        => '狂暴幻想-032-狂暴幻想（H5）',
            'kind'        => '3',
            'report_type' => '2',
            'media_id'    => '0',
        ];
        $this->mockData   = [
            'ID'               => '*********',
            'DEVICE_KEY'       => '0b1c71ad79d1249477987a7fa9c9fd8c',
            'PACKAGE_ID'       => '********',
            'ORDER_ID'         => '*****************',
            'DEVICE_CODE'      => 'ooOgd7e21aK9reCqd8RXnOn8TnAM',
            'OAID'             => 'wx0mkr3gqumbwovw00',
            'DEVICE_ID'        => '',
            'MD5_DEVICE_ID'    => 'aes_tzqDZ5J0JYlLaCosoB_v1qr2O5qLmJbCDutkYlUdaqw',
            'CORE_ACCOUNT'     => 'minigame_6791_72490e4056533ec369bc7a3a3c94cebd',
            'PAYWAY'           => 'minigame',
            'MONEY'            => '98.000000',
            'GAME_ID'          => '6791',
            'OS'               => '',
            'IP'               => '**************',
            'ANDROID_ID'       => '',
            'CHANNEL_ID'       => '1105',
            'SV_KEY'           => '********',
            'CLICK_ID'         => '*********',
            'TYPE'             => 'pay',
            'NEWLOGIN_TIME'    => '2024-08-30 13:22:41',
            'PAY_TIME'         => '2025-02-14 07:41:35',
            'CAMPAIGN_ID'      => '***********',
            'PLAN_ID'          => '**********',
            'ROLE_NAME'        => '天下有雪',
            'ROLE_ID'          => '********20100000680',
            'ROLE_RANK'        => '103',
            'ROLE_VIP'         => '5',
            'GAME_SERVER_ID'   => '********',
            'CP_GAME_ID'       => '803',
            'PAY_GAME_ID'      => '6791',
            'SOURCE_GAME_ID'   => '6791',
            'PAY_PACKAGE_ID'   => '********',
            'SOURCE_ID'        => '665|6791_0b1c71ad79d1249477987a7fa9c9fd8c_1',
            'PAY_RESULT'       => '1',
            'ACTUALLY_MONEY'   => '98.000000',
            'SOURCE_DIMENSION' => '2',
            'ACTIVATE_TIME'    => '2024-08-30 13:22:21',
            'UPLOAD_METHOD'    => '2',
            'EXT_ID'           => '4267',
            'EXT'              =>
                [
                    'app_id' => 'wx109a5bb4cb3d8376',
                ],
        ];

        $this->strategy = $this->getMockBuilder(CustomDeductionStrategy::class)
            ->onlyMethods([
                'getPaidTimesByCpAccount',
                'getTotalPaidAmount',
                'getUserTime',
                'getAdAccountId',
                'getPlanRoiCost',
            ])
            ->getMock();

        $this->mockData['paid_report_log'] = $this->initPaidReportLog($this->mockData);

        $this->strategy->init($this->mockConfig, $this->mockData);
    }

    public function testTimeCheck()
    {
        $this->assertTrue($this->strategy->timeCheck());
    }

    protected function initPaidReportLog($v)
    {
        return  [
            'order_id'         => $v['ORDER_ID'],
            'pay_result'       => $v['PAY_RESULT'] ?? 0,
            'actually_money'   => $v['ACTUALLY_MONEY'] ?? 0,
            'cp_game_id'       => $v['CP_GAME_ID'] ?? 0,
            'game_id'          => $v['GAME_ID'] ?? 0,
            'package_id'       => $v['PACKAGE_ID'] ?? 0,
            'channel_id'       => $v['CHANNEL_ID'] ?? 0,
            'oaid'             => $v['OAID'] ?? '',
            'device_key'       => $v['DEVICE_KEY'] ?? '',
            'pay_time'         => $v['PAY_TIME'] ?? '',
            'core_account'     => $v['CORE_ACCOUNT'] ?? '',
            'source_id'        => $v['SOURCE_ID'] ?? '',
            'source_dimension' => $v['SOURCE_DIMENSION'] ?? '',
        ];
    }

    public function testIsPass()
    {
        $rs = $this->strategy->isPass();
        $this->assertTrue($rs);
        $data = $this->strategy->getData();
        $this->assertEquals($data['paid_report_log']['no_reported_origin'], '不上报原因：匹配上报规则失败');
    }

    public function testRoleLevelCheck()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'roleLevel');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 103,], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '>=', 'value' => 5,], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '<', 'value' => 100,], $this->mockData);
        $this->assertFalse($result['result']);
    }

    public function testPaymentCountCheck()
    {
        $this->strategy->method('getPaidTimesByCpAccount')->willReturn(['PAY_TIMES' => 5]);

        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'paymentCount');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 5], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '>', 'value' => 2], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '<', 'value' => 3], $this->mockData);
        $this->assertFalse($result['result']);
    }

    public function testTotalPaymentCheck()
    {
        $this->strategy->method('getTotalPaidAmount')->willReturn(['TOTAL_AMOUNT' => 500]);

        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'totalPayment');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 500], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '>', 'value' => 100], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '>', 'value' => 1000], $this->mockData);
        $this->assertFalse($result['result']);
    }

    public function testCoreAccountCheck()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'coreAccount');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => 'in', 'value' => 'test_123'], $this->mockData);
        $this->assertFalse($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => 'in', 'value' => 'minigame_6791_724,minigame_6791_72490e4056533ec369bc7a3a3c94cebd'], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => 'not_in', 'value' => 'minigame_6791_'], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 'minigame_6791_72490e4056533ec369bc7a3a3c94cebd'], $this->mockData);
        $this->assertTrue($result['result']);
    }

    public function testAdNewDaysCheck()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'adNewDays');
        $method->setAccessible(true);
        //'NEWLOGIN_TIME'    => '2024-08-30 13:22:41',
        $result = $method->invoke($this->strategy, ['operator' => '>=', 'value' => 169], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '>=', 'value' => 3], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '<', 'value' => 3], $this->mockData);
        $this->assertFalse($result['result']);
    }

    public function testadActiveDaysCheck()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'adActiveDays');
        $method->setAccessible(true);
        //'ACTIVATE_TIME'    => '2024-08-30 13:22:21',
        $result = $method->invoke($this->strategy, ['operator' => '>=', 'value' => 169], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '>=', 'value' => 3], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '<', 'value' => 3], $this->mockData);
        $this->assertFalse($result['result']);
    }

    public function testSourceChannelCheck()
    {
        //1105
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'sourceChannel');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => '1105'], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '!=', 'value' => '123'], $this->mockData);
        $this->assertTrue($result['result']);
    }

    public function testSourceAccountIdCheck()
    {
        $this->strategy->method('getAdAccountId')->willReturn("hello");

        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'sourceAccountId');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 'hello'], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '!=', 'value' => 'test'], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => 'in', 'value' => 'test'], $this->mockData);
        $this->assertFalse($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => 'not_in', 'value' => 'test'], $this->mockData);
        $this->assertTrue($result['result']);
    }


    public function testSourceProjectIdCheck()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'sourceProjectId');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 'test'], $this->mockData);
        $this->assertFalse($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => '***********'], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => 'in', 'value' => 'xx***********'], $this->mockData);
        $this->assertTrue($result['result']);
    }

    public function testSourcePlanIdCheck()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'sourcePlanId');
        $method->setAccessible(true);
//'PLAN_ID'          => '**********',
        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 'test'], $this->mockData);
        $this->assertFalse($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => '**********'], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => 'in', 'value' => 'hello,**********'], $this->mockData);
        $this->assertTrue($result['result']);
    }

    public function testSourcePackageCheck()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'sourcePackage');
        $method->setAccessible(true);
//     'PACKAGE_ID'       => '********',
        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 'test'], $this->mockData);
        $this->assertFalse($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => '********'], $this->mockData);
        $this->assertTrue($result['result']);
    }

    public function testPaymentAmount()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'paymentAmount');
        $method->setAccessible(true);
//    'MONEY'            => '98.000000',
        $result = $method->invoke($this->strategy, ['operator' => '>', 'value' => 100], $this->mockData);
        $this->assertFalse($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '<', 'value' => 100], $this->mockData);
        $this->assertTrue($result['result']);
    }

    public function testPaymentMethodCheck()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'paymentMethod');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 'alipay'], $this->mockData);
        $this->assertFalse($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '==', 'value' => 'minigame'], $this->mockData);
        $this->assertTrue($result['result']);
    }

    public function testPaymentTimeCheck()
    {
        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'paymentTime');
        $method->setAccessible(true);

        // 'PAY_TIME'         => '2025-02-14 07:41:35',
        $result = $method->invoke($this->strategy, ['operator' => '>', 'value' => '2025-02-01 07:41:35'], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '<', 'value' => '2025-02-15 07:41:35'], $this->mockData);
        $this->assertTrue($result['result']);
    }


    public function testPlanRoiCheck()
    {
        $this->strategy->method('getPlanRoiCost')
            ->willReturn(['roi' => 2.5]);

        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'planRoi1');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => '>=', 'value' => 2.0], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '<', 'value' => 2.0], $this->mockData);
        $this->assertFalse($result['result']);
    }

    public function testPlanPaymentCountCost1()
    {
        $this->strategy->method('getPlanRoiCost')
            ->willReturn(['cost' => 2.5]);

        $method = new \ReflectionMethod(CustomDeductionStrategy::class, 'planPaymentCountCost1');
        $method->setAccessible(true);

        $result = $method->invoke($this->strategy, ['operator' => '>=', 'value' => 2.0], $this->mockData);
        $this->assertTrue($result['result']);

        $result = $method->invoke($this->strategy, ['operator' => '<', 'value' => 2.0], $this->mockData);
        $this->assertFalse($result['result']);
    }
}

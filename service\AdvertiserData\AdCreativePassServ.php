<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use Smarty\Exception;

class AdCreativePassServ
{
    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public function getInfoList(array $params = [], array $groups = [], array $columns = []): array
    {
        $tpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_pass_rule/base_info.tpl');

        if (isset($params['is_hour'])) {
            $tpl->assign('is_hour', 1);
        }

        if (!empty($columns)) {
            $indicators = $columns;
        }
        else {
            $indicators = [
                'SUM(NUMERATOR) as numerator',
                'SUM(DENOMINATOR) as denominator',
            ];
        }

        if (is_array($indicators)) {
            $indicators = implode(', ', $indicators);
        }

        if (is_array($groups)) {
            $groups = implode(',', $groups);
        }

        $tpl
            ->assign('params', $params)
            ->assign('group_by', $groups)
            ->assign('powerSql', str_replace('POWER', 'power', \Plus::$service->admin->getAdminPowerSql()))
            ->assign('indicators_column', $indicators);

        $infoSql = $tpl->fetch();
        $db      = $this->getConn();

        return [
            'list' => $db->query($infoSql)->fetchAll()
        ];
    }


}
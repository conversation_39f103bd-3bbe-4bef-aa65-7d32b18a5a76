<?php

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

/**
 *  优酷上报
 */
class Youku extends AdBaseInterface
{

    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, "active");
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, "register");
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, "pay");
    }

    /**
     * 上报数据
     * @param $packageId
     * @param $info
     * @param $uploadConfId
     */
    private function uploadData($info, $event)
    {
        $token       = $info["EXT"]["token"]?$info["EXT"]["token"]:"EGIozTliFEvyFtBvVeIf93qlWkZRRnaA";
        $callbackUrl = $info['CALLBACK_URL'];
        $time        = sprintf('%.0f', microtime(true) * 1000);
        $callbackUrl = str_replace("__EVENTTIME__", $time, $callbackUrl);


        $deepconv = 1;
        switch ($event) {
            case "active":
                $title    = "激活";
                $deepconv =0;
                break;
            case "register":
                $title    = "注册";
                $deepconv =0;
                break;
            case "pay":
                $title       = "付费";
                $callbackUrl = str_replace("__CONVAMOUNT__", ($info["MONEY"] * 100), $callbackUrl);
                break;
        }

        $callbackUrl = str_replace("__DEEPCONV__", $deepconv, $callbackUrl);
        $callbackUrl = str_replace("__EVENTTYPE__", $event, $callbackUrl);
        $callbackUrl = str_replace("__CONVCOUNT__", 1, $callbackUrl);
        //验签
        $objArr       = $this->getObjectParams($callbackUrl);
        $content      = $this->toStr($objArr);
        $signature    = $this->sign($objArr["app_key"], $token, $content);
        $signature    = strtoupper($signature);
        $callbackUrl .="&sign={$signature}";

        $http = new Http($callbackUrl);
        $res  = $http->get();
        //记录上报结果
        $resArr = json_decode($res, true);

        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'youku';
        $logInfo['request']      = json_encode(['url' => $callbackUrl]);
        //记录上报结果
        $logInfo['response'] = $res;
        $resArr              = json_decode($res, true);
        if (isset($resArr['msg']) && $resArr['msg'] == 'success') {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        $this->log($info, $logInfo, $res, $callbackUrl);
    }

    public function getObjectParams($url)
    {
        $netUrl = null;

        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return null;
        } else {
            $m = [];
            $q = parse_url($url, PHP_URL_QUERY);
            if (empty($q)) {
                return null;
            } else {
                $list = explode("&", $q);
                if (empty($list)) {
                    return null;
                } else {
                    foreach ($list as $pair) {
                        if (strpos($pair, "=") !== false) {
                            $keyValue = explode("=", $pair);
                            if (!empty($keyValue)) {
                                $m[array_shift($keyValue)] = array_shift($keyValue);
                            }
                        }
                    }
                    return $m;
                }
            }
        }// end if()
    }

    public function toStr(array $params)
    {
        if (empty($params)) {
            return null;
        }
        $paramNameList = array_keys($params);
        sort($paramNameList);
        $builder = "";
        $first   = true;
        foreach ($paramNameList as $key) {
            if (!$first) {
                $builder .= "&";
            } else {
                $first = false;
            }
            $builder    .= $key . "=";
            $value       = $params[$key];
            $valueString = "";
            if (!is_null($value)) {
                $valueString = strval($value);
            }
            $builder .= $valueString;
        }
        return $builder;
    }


    //签名
    function sign($appKey, $appSecret, $content)
    {
        $keyBuilder = $appKey . $content;
        return strtoupper(md5(hash_hmac("sha256", $keyBuilder, $appSecret)));
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

<?php

namespace app\ad_upload\channels;

/**
 * 星广联投-微信小程序
 * @date 2024/10/16
 */
class XgltXcx extends XingtuXcx
{

    /**
     * 激活上报
     * @param array $info
     * @param array $ext
     * @return void
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'ACTIVE', 'xglt_xcx');
    }

    /**
     * 注册上报
     * @param array $info
     * @param array $ext
     * @return void
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, 'REG', 'xglt_xcx');
    }

    /**
     * 付费上报
     *
     * @param array $info
     * @param array $ext
     * @return void
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'PAY', 'xglt_xcx');
    }
}

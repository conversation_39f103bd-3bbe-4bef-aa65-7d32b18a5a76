<?php
/**
 * 今日头条数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 * phpcs:disable
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;
use Plus\Util\StringUtil;

class Toutiao2 extends AdBaseInterface
{
    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->toutiaoUpload($info, 'ACTIVE');
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->toutiaoUpload($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->toutiaoUpload($info, 'PAY');
    }


    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }

    /**
     * 头条上报
     * @param $info
     * @param $type
     * @param $logLabel
     * @return bool
     */
    protected function toutiaoUpload($info, $type)
    {
        if($info['PACKAGE_ID'] == '70560099'){
            return true;
        }

        $className   = get_called_class();
        $parts       = explode('\\', $className);
        $channelCode = end($parts);

        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = StringUtil::unCamelize($channelCode);
        $logInfo['log_type']     = 'reported_platform_log';

        if (substr($info['PACKAGE_ID'], -2, 2) == '99') {
            $deviceIdKey = 'idfa';
            $deviceId    = $info['DEVICE_ID'];
        } else {
            $deviceIdKey = 'imei';
            $deviceId    = md5($info['DEVICE_ID']);
        }

        $callbackUrl = '';
        $eventType   = '';
        switch ($type) {
            case 'ACTIVE':
                $callbackUrl = $info['CALLBACK_URL'] . "&{$deviceIdKey}={$deviceId}" . '&event_type=0';
                $eventType   = "active";
                break;
            case 'REG':
                $callbackUrl = $info['CALLBACK_URL'] . "&{$deviceIdKey}={$deviceId}" . '&event_type=1';
                $eventType   = "active_register";
                break;
            case 'PAY':
                $eventType   = "active_pay";
                $payMoney    = $info["MONEY"]; // 默认金额
                $payMoney    = $payMoney * 100;
                $callbackUrl = $info['CALLBACK_URL'] . "&{$deviceIdKey}={$deviceId}" . '&event_type=2&props={"pay_amount":' . $payMoney . '}';
                break;
        }
        //暂时还是用旧版的上报
        if (true) {
            $http = new Http($callbackUrl);
            for ($i = 1; $i <= 3; $i++) {
                $res = $http->get();
                if (!empty($res)) {
                    break;
                }
            }
            $logInfo['request']  = \json_encode(['url' => $callbackUrl]);
        } else {
            $reqUri = 'https://analytics.oceanengine.com/api/v2/conversion';
            $http = new Http($reqUri);

            //https://ad.oceanengine.com/track/activate/
            //https://event-manager.oceanengine.com/docs/8650/all_events
            //https://bytedance.larkoffice.com/docx/ZanOdTN6EobL07xbQZccyI3Fn5c
            //$urlInfo  = parse_url($info['CALLBACK_URL']);
            //$needPost = $urlInfo['path'] != "/track/activate/";  //新版头条上报，post


            $sql        = "select idfv,android_id  from bigdata_dwd.dwd_sdk_activate_log 
            where 
                  package_id='{$info['PACKAGE_ID']}'  and device_key='{$info['DEVICE_KEY']}'
            order by  time desc limit 1";
            $deviceInfo = \Plus::$app->doris_entrance->query($sql)->fetch(\PDO::FETCH_ASSOC);
            $idfv       = $deviceInfo['idfv'] ?? '';
            $androidId  = $deviceInfo['android_id'] ?? '';
            $urlInfo = parse_url($info['CALLBACK_URL']);
            parse_str($urlInfo['query'], $callBackUrl);
            $postData   = [
                'event_type' => $eventType,
                'context'    => [
                    'ad' => [
                        'callback' => $callBackUrl['callback'],
                    ],
                ],
                'timestamp'  => time(),
            ];
            if ($idfv) {
                $postData['context']['device'] = ['idfv' => $idfv, 'platform' => 'ios'];
            }
            if ($androidId) {
                $postData['context']['device'] = ['android_id' => $androidId, 'platform' => 'android'];
            }
            if ($eventType == "active_pay") {
                $postData['props'] = ['pay_amount' => $payMoney];
            }
            $res = $http->post($postData, null, array("Content-Type: application/json"));
            $logInfo['request']  = \json_encode(['url' => $reqUri, 'data' => $postData]);
        }

        //写日志
        $logInfo['response'] = $res;

        //记录上报结果
        $resArr = json_decode($res, true);
        $resCode = $resArr['code'] ?? $resArr['status'];
        if ($resCode == 200 || $res == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
            if (!empty($info['paid_report_log'])) {
                $info['paid_report_log']['no_reported_origin'] = '接口返回编码异常';
            }
        }

        $this->log($info, $logInfo, $res, $callbackUrl);
    }


    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }
}

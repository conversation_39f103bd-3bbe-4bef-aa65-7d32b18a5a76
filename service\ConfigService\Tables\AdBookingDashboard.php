<?php

namespace app\service\ConfigService\Tables;

use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;


class AdBookingDashboard implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    public function getFields($options = null)
    {
        $fields = collect([
            'tday'            => ['title' => '日期', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'cp_game_id'      => ['title' => '游戏原名', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'game_id'         => ['title' => '游戏统计名', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'channel_main_id' => ['title' => '主渠道', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'channel_id'      => ['title' => '推广子渠道', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'package_id'      => ['title' => '包号', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'platform_id'     => ['title' => '客户端', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'promotion_id'    => ['title' => '推广分类', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'user_id'         => ['title' => '投放人', 'sorter' => true, 'classify' => ['attrs', 'base']],
        ])->merge($this->getBaseFields(['campaignBaseCollect', 'planBaseCollect', 'AdAccountBaseCollect']));

        $fields = $fields->merge([
            'cost'                     => ['title' => '返点前消耗金额', 'sorter' => true, 'classify' => ['attrs', 'ad_cost']],
            'cost_discount'            => ['title' => '返点后消耗金额', 'sorter' => true, 'classify' => ['attrs', 'ad_cost']],
            'show'                     => ['title' => '展示数', 'classify' => ['attrs', 'media_index']],
            'form'                     => ['title' => '表单提交(头条)', 'classify' => ['attrs', 'media_index']],
            'button'                   => ['title' => '按钮button(头条)', 'classify' => ['attrs', 'media_index']],
            'page_reservation_count'   => ['title' => '表单预约量(广点通)', 'classify' => ['attrs', 'media_index']],
            'reservation_uv'           => ['title' => '表单预约人数(广点通)', 'classify' => ['attrs', 'media_index']],
            'event_appoint_form'       => ['title' => '预约表单数(快手)', 'classify' => ['attrs', 'media_index']],
            'event_appoint_form_ratio' => ['title' => '预约表单点击率(快手)', 'classify' => ['attrs', 'media_index']],
            'submit_1_d_cnt'           => ['title' => '当日累计表单提交(快手)', 'classify' => ['attrs', 'media_index']],
            'submit_7_d_cnt'           => ['title' => '7天内累计表单提交(快手)', 'classify' => ['attrs', 'media_index']],
            'submit_unit_price_cost'   => ['title' => '累计表单提交单价(快手)', 'classify' => ['attrs', 'media_index']],
            'direct_submit_1_d_cnt'    => ['title' => '表单提交数(计费时间)(快手)', 'classify' => ['attrs', 'media_index']],
        ]);

        return $this->formatStandard($fields);
    }
}
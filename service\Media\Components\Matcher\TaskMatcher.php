<?php

namespace app\service\Media\Components\Matcher;

use app\extension\Support\Helpers\DBHelper\MatcherAbstract;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\SelectQuery;


class TaskMatcher extends MatcherAbstract
{
    /**
     * @return \Closure[]
     */
    protected function matchFnList(): array
    {
        return [
            'video_topic'       => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::likeBuild($qb, $key, $value);
            },
            'video_title'       => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::likeBuild($qb, $key, $value);
            },
            'mc_id'             => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'mc_video_id'       => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'video_duration'    => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'media_platform_id' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'task_script_name'  => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::likeBuild($qb, $key, $value);
            },
            'task_script_id'    => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'data_label'        => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::likeBuild($qb, $key, $value);
            },
            'job_kind'          => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
        ];
    }

}
<?php

namespace app\logic\log;

use app\extension\Support\Collections\Arr;
use app\service\ConfigService\BasicServ;
use app\service\Logs\PaymentVirtualServ;
use Smarty\Exception;

class PaymentVirtualLogic
{
    /**
     * @param array $param
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public function getList(array $param): array
    {
        $page     = Arr::pull($param, 'page', 1);
        $pageSize = Arr::pull($param, 'page_size', 100);
        $sort     = Arr::pull($param, 'sort', null);

        if (!empty($sort)) {
            $order = Arr::pull($param, 'order');
            if ($order == 'ascend') {
                $order = 'ASC';
            }
            else {
                $order = 'DESC';
            }
            $orderBy = [$sort => $order];
        }
        else {
            $orderBy = ['id' => 'DESC'];
        }

        if (!empty($param['range_date_start']) && !empty($param['range_date_end'])) {
            $param['upload_time'] = [
                $param['range_date_start'], $param['range_date_end']
            ];
            sort($param['upload_time']);
            unset($param['range_date_start'], $param['range_date_end']);
        }

        foreach ($param as $key => &$value) {
            if (in_array($key, ['package_id', 'rule_id', 'upload_status', 'upload_type',])) {
                $value = implode(', ', Arr::wrap($value));
            }
        }

        $constMap   = (new BasicServ())->getMultiOptions(['payment_virtual:rule_id', 'channel_id']);
        $ruleMap    = array_column($constMap->get('payment_virtual:rule_id')->toArray(), 'val', 'key');
        $channelMap = array_column($constMap->get('channel_id')->toArray(), 'val', 'key');
        $result     = (new PaymentVirtualServ())->list($param, [], ['page' => $page, 'page_size' => $pageSize], $orderBy);

        $list = &$result['list'];

        foreach ($list as &$item) {
            $item               = array_change_key_case($item);
            $item['channel_id'] = $channelMap[$item['channel_id']] ?? '-';

            if (empty($item['rule_id'])) {
                $item['rule_name'] = '手动指定账号';
            }
            else {
                $item['rule_name'] = $ruleMap[$item['rule_id']] ?? '-';
            }

            if ($item['upload_status'] == 1) {
                $item['upload_status'] = '已上报';
            }
            elseif ($item['upload_status'] == -1) {
                $item['upload_status'] = '不上报';
            }
            else {
                $item['upload_status'] = '未上报';
            }

            if ($item['log_report_time'] == '1000-01-01 00:00:00') {
                $item['log_report_time'] = '-';
            }

            if ($item['upload_type'] == 1) {
                $item['upload_type'] = 'SDK';
            }
            else {
                $item['upload_type'] = 'API';
            }

        }

        return $result;
    }
}
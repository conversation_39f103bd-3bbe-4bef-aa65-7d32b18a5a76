<?php

if (!function_exists('days_apart')) {

    /**
     * Get the number of days between two dates
     *
     * @param DateTime|string $base
     * @param DateTime|string $target
     * @param bool            $isAbsolute
     *
     * @return int
     * @throws Exception
     */
    function days_apart($base, $target, bool $isAbsolute = true): int
    {
        foreach ([&$target, &$base] as &$item) {
            if (!$item instanceof DateTime) {
                $item = new DateTime($item);
            }
        }

        $format = !$isAbsolute ? '%R%a' : '%a';

        return (int)date_diff($base, $target)->format($format);
    }
}


if (!function_exists('array_order')) {
    /**
     * 数组多维排序
     *
     * @return mixed|null
     * @example array_order($data, 'key1' , SORT_DESC , 'key2' , SORT_ASC)
     */
    function array_order()
    {
        $args = func_get_args();
        $data = array_shift($args);

        foreach ($args as $n => $field) {
            if (is_string($field)) {
                $tmp = [];
                foreach ($data as $key => $row) {
                    $tmp[$key] = $row[$field];
                }
                $args[$n] = $tmp;
            }
        }
        $args[] = &$data;
        call_user_func_array('array_multisort', $args);

        return array_pop($args);
    }
}
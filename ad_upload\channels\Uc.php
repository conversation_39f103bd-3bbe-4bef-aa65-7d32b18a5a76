<?php
/**
 * Uc数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class Uc extends AdBaseInterface
{
    const CONVERT_ACTIVE   = 1;
    const CONVERT_REGISTER = 27;
    const CONVERT_REMAIN   = 28;
    const CREATE_ROLE      = 75;
    const CONVERT_PURCHASE = 1000;


    /**
     * 测试联调
     * @param $info
     * @return boolean
     */
    public function testUpload($info)
    {
        return $this->upload($info, 'ACTIVE');
    }


    /**
     * 上报激活
     * @param array $info
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }


    /**
     * 上报注册
     * @param array $info
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY');
    }


    /**
     * 上报留存
     * @param array $info
     */
    public function uploadRemain($info, $ext = [])
    {
        $this->upload($info, 'REMAIN');
    }

    /**
     * 创角
     *
     * @param array $info
     * @param array $ext
     * @desc UC创角上报实际暂时调整为同时上报激活和注册
     * @date 2024/06/28
     * @return void
     */
    public function uploadCreateRole($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
        $this->upload($info, 'REG');
        $this->upload($info, 'CREATE_ROLE');
    }


    /**
     * 公共上报
     * @param        $info
     * @param        $type
     * @param string $logPrefix
     */
    protected function upload($info, $type, $logPrefix = 'UC')
    {
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = strtolower($logPrefix);
        $logInfo['log_type']     = 'reported_platform_log';
        $callbackUrl             = '';
        switch ($type) {
            case 'ACTIVE':
                $callbackUrl = $info['CALLBACK_URL'] . '&type=' . self::CONVERT_ACTIVE;
                $typeName    = '激活';
                break;
            case 'REG':
                $callbackUrl = $info['CALLBACK_URL'] . '&type=' . self::CONVERT_REGISTER;
                $typeName    = '注册';
                break;
            case 'PAY':
                $callbackUrl = $info['CALLBACK_URL'] . '&type=' . self::CONVERT_PURCHASE . "&money=" . $info["MONEY"];
                $typeName    = '付费';
                break;
            case 'CREATE_ROLE':
                $callbackUrl = $info['CALLBACK_URL'] . '&type=' . self::CREATE_ROLE;
                $typeName    = '创角';
                break;
        }
        $logInfo['request'] = json_encode(['url' => $callbackUrl]);
        $http               = new Http($callbackUrl);
        $rs                 = $http->get();

        //记录上报结果
        $resArr              = json_decode($rs, true);
        $logInfo['response'] = $rs;

        if ($resArr['status'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        $this->log($info, $logInfo, $rs, $callbackUrl);
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }
}

<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\advertise\AdIncomeDashLogic;
use app\service\AdvertiserData\AdRoiMonthServ;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use Spiral\Database\Query\SelectQuery;

/**
 *
 */
class AdMonthController extends BaseTableController
{
    use ColumnsInteract;

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = ['t_month' => 'ASC', 'new_user_num' => 'DESC'];
        }

        $groups  = Arr::wrap($params->pull('groups'));
        $options = $params->toArray();

        // 外部账号仅能显示自己账号归属的数据
        if (\Plus::$service->admin->isOutsiders()) {
            $options['user_id'] = \Plus::$service->admin->getUserId();
        }

        $serv   = new AdRoiMonthServ();
        $result = $serv->getList($options, $groups, ['page' => $page, 'page_size' => $pageSize], $sort);


        $list        = &$result['list'];
        $summaryRow  = &$result['summary'];
        $ltvRoiSumFn = $this->ltvOrRoiSum();

        $constConfCollect = (new BasicServ())
            ->getMultiOptions([
                'platform_id', 'promotion_id', 'department_id', 'user_id',
                'cp_game_id:all', 'game_id', 'channel_main_id',
            ])->put('channel_id', (new GeneralOptionServ())->listChannelOptions());

        $replaceFn             = $this->replaceColumnDefine($constConfCollect);
        $resetGroupFn          = $this->resetGroupsCols(
            $this->groupAdRelation($groups),
            $groups,
            ConstHub::AD_MONTH_FIXED_INFO_COLS
        );
        $monthSumFn            = $this->monthIndexSum();
        $appendAdIncomeFn      = fn(&$target) => true;
        $summaryAppendIncomeFn = fn(&$target) => true;

        if (
            $this->isAppendAdIncome($groups)
        ) {
            $newOpt                     = $options;
            $newOpt['range_date_start'] = $newOpt['range_date_start'] . '-01';
            $newOpt['range_date_end']   = date('Y-m-t', strtotime($newOpt['range_date_end'] . '-01'));
            $newOpt['groups']           = ['tday', 'cp_game_id', 'game_id'];
            $adIncomeLogic              = new AdIncomeDashLogic();
            $adIncomeResult             = $adIncomeLogic->getList($newOpt);
            $adIncomeList               = $adIncomeResult['list'] ?? [];
            $summaryIncome              = $adIncomeResult['summary'] ?? [];
            $adIncomeMap                = [];
            $groupIndex                 = array_fill_keys($groups, 0);
            $dayFormatFn                = fn($item) => true;
            if (in_array('t_month', $groups)) {
                $dayFormatFn = function (&$item) {
                    $item['t_month'] = date('Y-m', strtotime($item['tday']));
                };
            }

            foreach ($adIncomeList as $foo) {
                $dayFormatFn($foo);
                $incomeRMB = $foo['income_rmb'] ?? 0;
                $kk        = implode('|', array_merge($groupIndex, array_intersect_key($foo, $groupIndex)));
                if (!isset($adIncomeMap[$kk])) {
                    $adIncomeMap[$kk] = $incomeRMB;
                }
                else {
                    $adIncomeMap[$kk] += $incomeRMB;
                }
            }

            $appendAdIncomeFn = function (&$target) use ($adIncomeMap, $groupIndex) {
                $kk = implode('|', array_merge($groupIndex, array_intersect_key($target, $groupIndex)));

                $target['ad_income']    = round($adIncomeMap[$kk] ?? 0, 2);
                $target['total_income'] = round(($adIncomeMap[$kk] ?? 0) + ($target['total_ltv_money_sum'] ?? 0), 2);
                if (empty(floatval($target['total_income'])) || empty(floatval($target['cost_discount_sum']))) {
                    $target['roi_ad_income_percent'] = '0.00%';
                }
                else {
                    $target['roi_ad_income_percent'] = round($target['total_income'] / $target['cost_discount_sum'] * 100, 2) . '%';
                }
            };

            $summaryAppendIncomeFn = function (&$target) use ($summaryIncome) {
                $target['ad_income']    = round($summaryIncome['income_rmb'] ?? 0, 2);
                $target['total_income'] = round(($target['ad_income'] ?? 0) + ($target['total_ltv_money_sum'] ?? 0), 2);
                if (empty(floatval($target['total_income'])) || empty(floatval($target['cost_discount_sum']))) {
                    $target['roi_ad_income_percent'] = '0.00%';
                }
                else {
                    $target['roi_ad_income_percent'] = round($target['total_income'] / $target['cost_discount_sum'] * 100, 2) . '%';
                }
            };
        }

        // 计算指标
        foreach ($list as &$item) {
            $item['cost_discount_sum'] = round($item['cost_discount_sum'] ?? 0.00, 2);
            $item['channel_main_id']   = $item['channel_main'];
            $item['channel_id']        = $item['t_channel'];

            $appendAdIncomeFn($item);
            // ltv & roi 计算
            $ltvRoiSumFn($item);
            // 其他指标计算
            $monthSumFn($item);
            // 统计新增用户付费
            $this->sumNewUserPay($item);
            // 替换对应的字段
            $replaceFn($item);
            // 汇总去除没意义的字段
            $resetGroupFn($item);
            // 清理部分字段
            $this->unsetFields($item);
        }

        $summaryAppendIncomeFn($summaryRow);
        $ltvRoiSumFn($summaryRow);
        $this->sumNewUserPay($summaryRow);
        $monthSumFn($summaryRow);
        $resetGroupFn($summaryRow);
        $this->unsetFields($summaryRow);

        $result['time'] = $summaryRow['max_update_time'] ?? '';

        return $result;
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],
                    ['value' => 'ltv_group_1', 'label' => 'LTV1-30'],
                    ['value' => 'ltv_group_2', 'label' => 'LTV60-360'],
                    ['value' => 'roi_group_1', 'label' => 'ROI1-30'],
                    ['value' => 'roi_group_2', 'label' => 'ROI60-360'],
                ],
            ],
        ];

        $fields = $this->tableFields($params->toArray());
        $groups = $params->get('groups');

        if ($this->isAppendAdIncome($groups)) {
            $fields[] = [
                'title'     => '累计付费金额',
                'dataIndex' => 'total_ltv_money_sum',
                'classify'  => ['attrs', 'new_user_base']
            ];
            $fields[] = [
                'title'     => '广告收入',
                'dataIndex' => 'ad_income',
                'classify'  => ['attrs', 'new_user_base']
            ];
            $fields[] = [
                'title'     => '累计总收入(含广告)',
                'dataIndex' => 'total_income',
                'classify'  => ['attrs', 'new_user_base']
            ];
            $fields[] = [
                'title'     => '累计ROI(含广告)',
                'dataIndex' => 'roi_ad_income_percent',
                'classify'  => ['attrs', 'new_user_base']
            ];
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $nowMonth = date('Y-m');

        return collect([
            ['field' => 'range_date_start', 'default' => $nowMonth], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $nowMonth], // 统计日期结束时间
            ['field' => 'cp_game_id'], // 游戏原名
            ['field' => 'game_id'], // 游戏统计名
            ['field' => 'channel_main_id'], // 主渠道
            ['field' => 'channel_id'], // 主渠道
            ['field' => 'package_id'], // 包号
            ['field' => 'platform_id'], // 客户端ID
            ['field' => 'promotion_id'], // 推广分类
            ['field' => 'department_id'], // 投放部门
            ['field' => 'user_id'], // 投放人
            ['field' => 'game_id_tags'],
            ['field' => 'channel_id_tags'],
            ['field' => 'channel_main_id_tags'],
            ['field' => 'channel_main_id_tags'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups', 'default' => ['t_month', 'package_id', 'channel_id', 'user_id']],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

    /**
     * @param array $groups
     *
     * @return array
     */
    private function groupAdRelation(array $groups): array
    {
        $relationTree = [
            'user_id'         => ['department_id', 'package_id'],
            'package_id'      => ['game_id', 'cp_game_id', 'platform_id', 'channel_main_id', 'channel_id', 'user_id', 'promotion_id'],
            'channel_id'      => ['channel_main_id'],
            'channel_main_id' => [],
            'game_id'         => ['cp_game_id'],
            'platform_id'     => [],
            'promotion_id'    => [],
            'cp_game_id'      => [],
            't_month'         => [],
        ];

        return ColumnManager::matchRelationByGroups($relationTree, $groups);
    }

    /**
     * @return \Closure
     */
    private function ltvOrRoiSum(): \Closure
    {
        return function (&$data) {
            // 匹配LTV&ROI节点
            preg_match_all('/ltv_money_sum\d+/', \implode(',', array_keys($data)), $nodes);
            $nodes = $nodes[0];

            if (empty($nodes)) return;

            $costDiscount = (float)($data['cost_discount_sum'] ?? 0);
            $newUser      = (int)$data['new_user_num'] ?? 0;

            foreach ($nodes as $node) {
                $i     = (int)str_replace('ltv_money_sum', '', $node);
                $money = (float)$data[$node] ?? 0;

                // ltv
                if (empty($money) || empty($newUser)) {
                    $data['ltv_' . $i] = '0.00';
                }
                else {
                    $data['ltv_' . $i] = number_format(round(
                        math_eval('x/y', ['x' => $money, 'y' => $newUser]), 2
                    ), 2);
                }

                // roi
                if (empty($money) || empty($costDiscount)) {
                    $data['roi_' . $i] = '0.00%';
                }
                else {
                    $data['roi_' . $i] = number_format(round(
                            math_eval('x/y * 100', ['x' => $money, 'y' => $costDiscount]), 2
                        ), 2) . '%';
                }
            }
        };
    }

    /**
     * 月度其余指标统计
     *
     * @return \Closure
     */
    private function monthIndexSum(): \Closure
    {
        return function (&$data) {
            $costDiscount  = (float)$data['cost_discount_sum'] ?? 0;
            $newUser       = (int)$data['new_user_num'] ?? 0;
            $totalLtvMoney = (float)$data['total_ltv_money_sum'] ?? 0;

            if (empty($totalLtvMoney) || empty($newUser)) {
                $data['total_ltv'] = 0.00;
            }
            else {
                $data['total_ltv'] = number_format(
                    math_eval('x/y', ['x' => $totalLtvMoney, 'y' => $newUser]), 2
                );
            }

            if (empty($totalLtvMoney) || empty($costDiscount)) {
                $data['total_roi'] = '0.00%';
            }
            else {
                $data['total_roi'] = number_format(
                        math_eval('x/y * 100', ['x' => $totalLtvMoney, 'y' => $costDiscount]), 2
                    ) . '%';
            }

            // 新用户成本
            if (empty($newUser) || empty($costDiscount)) {
                $data['new_user_cost'] = 0.00;
            }
            else {
                $data['new_user_cost'] = round(math_eval('x/y', ['x' => $costDiscount, 'y' => $newUser]), 2);
            }
        };
    }

    /**
     * @param $data
     *
     * @return void
     */
    private function unsetFields(&$data)
    {
        preg_match_all('/ltv_money_sum\d+/', \implode(',', array_keys($data)), $nodes);
        $nodes = $nodes[0] ?? [];

        if (!empty($nodes)) {
            $data = array_diff_key($data, array_flip($nodes));
        };

        preg_match_all('/new_uer_pay_sum\d+/', \implode(',', array_keys($data)), $payNodes);
        $payNodes = $payNodes[0] ?? [];

        if (!empty($payNodes)) {
            $data = array_diff_key($data, array_flip($payNodes));
        };
    }

    /**
     * 统计新增用户付费
     *
     * @param $data
     *
     * @return void
     */
    private function sumNewUserPay(&$data)
    {
        $data['new_user_pay'] = $data['new_uer_pay_sum1'] ?? 0;
//        $data['new_user_pay'] = 0;
//        preg_match_all('/new_uer_pay_sum\d+/', \implode(',', array_keys($data)), $nodes);
//
//        $nodes = $nodes[0] ?? [];
//
//        if (empty($nodes)) return;
//
//        foreach ($nodes as $node) {
//            $data['new_user_pay'] += $data[$node];
//        }
    }

    /**
     * @param array $groups
     * @return bool
     */
    protected function isAppendAdIncome(array $groups): bool
    {
        return empty(array_diff($groups, ['t_month', 'cp_game_id', 'game_id']));
    }


}
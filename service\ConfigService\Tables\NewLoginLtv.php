<?php

namespace app\service\ConfigService\Tables;

use app\extension\Support\Helpers\Mather;
use app\service\ConfigService\Constants\FieldTag;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class NewLoginLtv implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    /**
     * @inerhitDoc
     *
     * @param $options
     *
     * @return \app\extension\Support\Collections\Collection|mixed
     * @throws \Exception
     */
    public function getFields($options = null)
    {
        $rangeDateDimension = (int)($options['range_date_dimension'] ?? 2);

        $today  = new \DateTime();
        $NNodes = array_merge(
            range(1, 30), [45, 60], Mather::findNumInScope(60, 720, 30)
        );

        $collect = $this->getBaseFields(['baseOperationCollect']);
        $collect = $collect->merge([
            'new_user'           => ['title' => '新增用户', 'sorter' => 'true'],
            'cost_discount'      => ['title' => '返点后消耗金额', 'sorter' => true, 'width' => 150],
            'new_user_cost'      => ['title' => '新用户成本'],
            'new_user_total_pay' => ['title' => '新用户累计付费'],
            'total_ltv'          => ['title' => '累计LTV'],
            'total_roi'          => ['title' => '累计ROI'],
        ]);

        $groups = $options['groups'];

        if (in_array('package_id', $groups)) {
            $collect = $collect->merge([
                'package_tags' => ['title' => '包号标签', 'classify' => ['attrs', 'tags']]
            ]);
        }

        if (
            in_array('package_id', $groups)
            || in_array('channel_id', $groups)
        ) {
            $collect = $collect->merge([
                'channel_tags' => ['title' => '推广子渠道标签', 'classify' => ['attrs', 'tags']]
            ]);
        }

        // $nDays   = days_apart($today, $options['range_date_start'] ?? $today);
        $nDays   = 720;

        $ltvFields = $this->ltvNCollect(Mather::findIn($nDays, $NNodes));
        $ltvFields = FieldTag::tagClassifyToNField($ltvFields, 'ltv',
            [
                ['range' => [1, 30], 'classify' => ['attrs', FieldTag::LTV_GROUP_1]],
                ['range' => [45, 180], 'classify' => ['attrs', FieldTag::LTV_GROUP_2]],
                ['range' => [210, 360], 'classify' => ['attrs', FieldTag::LTV_GROUP_3]],
                ['range' => [390, 720], 'classify' => ['attrs', FieldTag::LTV_GROUP_4]],
            ]);

        $roiFields = $this->roiNCollect(Mather::findIn($nDays, $NNodes));
        $roiFields = FieldTag::tagClassifyToNField($roiFields, 'roi', [
            ['range' => [1, 30], 'classify' => ['attrs', FieldTag::ROI_GROUP_1]],
            ['range' => [45, 180], 'classify' => ['attrs', FieldTag::ROI_GROUP_2]],
            ['range' => [210, 360], 'classify' => ['attrs', FieldTag::ROI_GROUP_3]],
            ['range' => [390, 720], 'classify' => ['attrs', FieldTag::ROI_GROUP_4]],
        ]);

        $collect = $collect->merge($ltvFields)->merge($roiFields);

        return $this->formatStandard($collect);
    }

}
<?php

namespace app\ad_upload_tmp\strategies;

use app\ad_upload_tmp\contract\AdBaseInterface;
use app\ad_upload_tmp\contract\AdUploadStrategyInterface;
use app\ad_upload_tmp\contract\DeductionStrategyInterface;
use app\ad_upload_tmp\contract\LogDbTrait;
use app\ad_upload_tmp\contract\PayUploadStrategyTrait;
use app\ad_upload_tmp\deduction\AmountDeductionStrategy;
use app\ad_upload_tmp\deduction\CustomDeductionStrategy;
use app\ad_upload_tmp\deduction\TimesDeductionStrategy;
use app\ad_upload_tmp\services\DeductionService;
use app\ad_upload_tmp\tool\ChannelFactory;
use app\ad_upload_tmp\tool\CommonFunc;

/**
 * 付费 上报 策略
 * <AUTHOR>
 */
class PayUploadStrategy extends AdUploadStrategyInterface
{
    use PayUploadStrategyTrait;
    use LogDbTrait;

    /**
     * 上报类型
     * @var string
     */
    public $action = AdBaseInterface::ACTION_PAY;

    /**
     * 最大id的数据
     * @var array
     */
    private $maxlastData = [];

    /**
     * 初始化上报点
     * @return void
     */
    public function initUploadLast()
    {
        parent::initUploadLast();
        $sql               = 'select max(id) as ID from tb_sdk_user_payment';
        $this->maxlastData = \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    public function getData($uploadConfig, $packages): array
    {
        if (empty($packages) || $this->lastId <= 0) {
            return [];
        }
        //先获取已支付订单
        $orderInfo = $this->getPaidOrder($packages);

        $allOrderIds = array_column($orderInfo, 'ORDER_ID');
        //手动指定订单id，不需要合并未匹配归因订单
        if (empty($this->ids)) {
            $allOrderIds = array_merge($allOrderIds, $this->unmatchedIds); //合并未匹配归因订单
        }

        if (empty($allOrderIds)) {
            return [];
        }
        $packageIds = explode(',', $packages);
        $rs         = $this->getPaidWithPackageNew($allOrderIds, $packageIds);
        //支付时间正序
        usort($rs, function ($a, $b) {
            return (strtotime($a['PAY_TIME']) - strtotime($b['PAY_TIME'])) > 0;
        });
        if (!empty($rs)) {
            //补充订单 投放信息 补充归因信息
            foreach ($orderInfo as $v) {
                foreach ($rs as $k2 => $v2) {
                    if ($v['ORDER_ID'] == $v2['ORDER_ID']) {
                        $rs[$k2] = array_merge($v2, $v);
                    }
                }
            }
        }// end if()

        return $rs;
    }



    /**
     * 设置如果上一步getData 获取数据为空，设置最大上报id
     * 避免长期没有数据上报的渠道，查询过多数据
     * @return void
     */
    public function setMaxLastId(): void
    {
        $data             = $this->maxlastData;
        $data['PAY_TIME'] = date('Y-m-d H:i:s');
        $this->setLastId($data);
    }

    /**
     * 获取已支付的订单id
     * @param string $packages 包号
     * @return array|false
     */
    private function getPaidOrder($packages)
    {
        //获取补漏上报ID
        $unmatchedIdString = CommonFunc::arr2Str($this->unmatchedIds);

        //lastId记录的是PAY_TIME时间戳
        $payTime = date('Y-m-d H:i:s', $this->lastId);
        $wheres  = [
            'a1.PAY_RESULT=1',
        ];
        if (!empty($this->timeBegin) && !empty($this->timeEnd)) {
            $wheres[] = "a1.PAY_TIME between '{$this->timeBegin}' and '{$this->timeEnd}'";
        } else if (!empty($this->ids)) {
            $idsArr   = explode(',', $this->ids);
            $ids      = CommonFunc::arr2Str($idsArr);
            $wheres[] = "a1.ORDER_ID in ({$ids})";
        } else {
            $wheres[] = "(a1.PAY_TIME > '{$payTime}' or a1.order_id in({$unmatchedIdString}))";
        }
        //指定订单id，不需要查包号
        if (empty($this->ids)) {
            $wheres[] = "a1.PACKAGE_ID IN ({$packages})";
        }

        $whereString = ' WHERE ' . implode(' AND ', $wheres);
        $sql         = "SELECT 
            a1.ID, a1.ORDER_ID, a2.CAMPAIGN_ID, a2.PLAN_ID,
            a2.IDFV,
            a2.`TIME`,
            a2.CALLBACK_PARAM,
            a2.PACKAGE_ID,
            a2.DEVICE_KEY,
            a2.DEVICE_TYPE,
            a2.DEVICE_CODE,
            a2.USERAGENT,
            a2.OAID,
            a2.DEVICE_ID,
            a2.MD5_DEVICE_ID,
            a2.GAME_ID,
            a2.OS,
            a2.CORE_ACCOUNT,
            a2.IP,
            a2.ANDROID_ID,
            a2.new_channel_id as CHANNEL_ID,
            a2.SV_KEY,
            a2.CLICK_ID
         FROM origin_platform.tb_sdk_user_payment a1
         left join bigdata_tmp.dwd_sdk_reg_huge_match_tmp a2 on(a1.core_account=a2.core_account and a1.PACKAGE_ID=a2.PACKAGE_ID)
         {$whereString}
         ";
        return \Plus::$app->doris_entrance->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 走包号新增归因上报的数据
     *
     * @param array $orderIds   订单id
     * @param array $packageIds 包号
     * @return array
     */
    private function getPaidWithPackageNew($orderIds, $packageIds)
    {
        if (empty($orderIds)) {
            return [];
        }

        $orderIdString = CommonFunc::arr2Str($orderIds);
        $wheres        = [
            "a.order_id in ({$orderIdString})",
        ];

        if (!empty($packageIds)) {
            $packageIdString = CommonFunc::arr2Str($packageIds);
            $wheres[]        = "b.package_id IN ({$packageIdString})";
        }

        $whereString = ' WHERE ' . implode(' and ', $wheres);

        $sql = "
            SELECT 
               a.ID,
               a.PACKAGE_ID,
               a.ORDER_ID,
               a.DEVICE_KEY,
               a.DEVICE_CODE,
               a.OAID,
               IF(b.DEVICE_ID = '', a.DEVICE_ID, b.DEVICE_ID)                                                            AS DEVICE_ID,
               IF(b.MD5_DEVICE_ID = 'aes_tzqDZ5J0JYlLaCosoB_v1qr2O5qLmJbCDutkYlUdaqw', a.MD5_DEVICE_ID,
                  b.MD5_DEVICE_ID)                                                                                       AS MD5_DEVICE_ID,
               a.CORE_ACCOUNT,
               a.PAYWAY,
               a.MONEY,
               a.GAME_ID,
               b.USERAGENT,
               b.OS,
               a.IP,
               a.ANDROID_ID,
               a.CHANNEL_ID,
               a.SV_KEY,
               a.CLICK_ID,
               '{$this->action}'                                                                                         AS TYPE,
               IF(DATE(a.PAY_TIME) = DATE(b.TIME), 1, 0)                                                                 AS IS_NEW_USER,
               b.TIME                                                                                                    as NEWLOGIN_TIME,
               a.PAY_TIME,
               a.ROLE_RANK,
               a.ROLE_ID,
               a.ROLE_NAME,
               a.GAME_SERVER_ID,
               a.ROLE_VIP,
               pc.CP_GAME_ID,
               a.GAME_ID                                                                                                 as PAY_GAME_ID,
               b.GAME_ID                                                                                                 as SOURCE_GAME_ID,
               b.DEVICE_KEY,
               a.PAY_RESULT,
               a.MONEY as ACTUALLY_MONEY,
               c.SOURCE_ID,
               '3' as SOURCE_DIMENSION
        FROM origin_platform.tb_sdk_user_payment a
        left join ddc_platform.dwd_sdk_user_payment c on a.order_id = c.order_id
        LEFT JOIN origin_platform.tb_sdk_user_newlogin_package b on a.PACKAGE_ID = b.PACKAGE_ID  AND a.CORE_ACCOUNT = b.CORE_ACCOUNT
        LEFT JOIN base_conf_platform.tb_package_detail_conf pc on a.PACKAGE_ID = pc.PACKAGE_ID
        {$whereString}
        ORDER BY a.ID";
        return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 重写过滤数据
     * @param array $data         data
     * @param array $uploadConfig config
     * @return array
     */
    public function filterData($data, $uploadConfig): array
    {
        $unmatchedIds = $this->unmatchedIds;
        $dataPay      = [];
        //根据包号补充渠道
        foreach ($data as $key => $val) {
            //echo json_encode($val), PHP_EOL;
            $uploadConfigOne = $uploadConfig[$val['PACKAGE_ID']] ?? [];
            /**
             * 移除未匹配的订单:
             * 1.不存在渠道配置的包
             * 2.是渠道不匹配
             */
            if (empty($uploadConfigOne) || $val['CHANNEL_ID'] != $this->channelId) {
                if (in_array($val["ORDER_ID"], $unmatchedIds)) {
                    $unmatchedIds = array_values(array_diff($unmatchedIds, [$val['ORDER_ID']]));
                }
                \Plus::$app->log->error('not_match_config渠道配置不存在' . json_encode($val), [], AdBaseInterface::LOG_DIR);
            }

            foreach ($uploadConfigOne as $v) {
                //1为全量上报，2为匹配点击上报
                if ($v['UPLOAD_METHOD'] == 1 || ($v['UPLOAD_METHOD'] == 2)) {
                    $channelId = $v['UPLOAD_METHOD'] == 1 ? $v['CHANNEL_ID'] : $val['CHANNEL_ID'];
                    $clickId   = $v['UPLOAD_METHOD'] == 1 ? 0 : $val['CLICK_ID'];

                    //过滤已上报的订单
                    if (!empty($val['CLICK_ID']) && $this->force == 0 && CommonFunc::checkPayUploaded($val['ORDER_ID'])) {
                        \Plus::$app->log->error('过滤已上报的订单' . $val['ID'], [], AdBaseInterface::LOG_DIR_ERR);
                        continue;
                    }

                    //未归因，大于6小时的数据记录日志
                    if (empty($val['CLICK_ID']) && strtotime($val['PAY_TIME']) < strtotime($this->unmatchedTime)) {
                        $val['paid_report_log']                       = $this->initPaidReportLog($val);
                        $val['paid_report_log']['reported_money']     = 0;
                        $val['paid_report_log']['reported_behavior']  = -1;
                        $val['paid_report_log']['no_reported_origin'] = CommonFunc::getClientType($val['PACKAGE_ID']) . '归因自然量，不上报';
                        $this->logPaidToDoris($val, ['channel_code' => ChannelFactory::getChannelCode($channelId)], '');
                        //从未归因数据缓存中删除
                        if (in_array($val['ORDER_ID'], $unmatchedIds)) {
                            $unmatchedIds = array_values(array_diff($unmatchedIds, [$val['ORDER_ID']]));
                        }
                        \Plus::$app->log->info('未归因数据缓存:' . $val['ID'], [], AdBaseInterface::LOG_DIR);
                        continue;
                    }
                    //未归因数据缓存
                    $unmatchedIds = $this->processUnmatchedIds($unmatchedIds, $val);
                    if (in_array($val["ORDER_ID"], $unmatchedIds)) {
                        \Plus::$app->log->info('unmatched_' . $this->action . $val['ID'], [], AdBaseInterface::LOG_DIR);
                        continue; //剔除未匹配的数据
                    }

                    $dataPay[] = array_merge($val, [
                        'CHANNEL_ID'    => $channelId,
                        'UPLOAD_METHOD' => $v['UPLOAD_METHOD'],

                        'EXT_ID'   => $v['ID'],
                        'EXT'      => $v['EXT'],
                        'CLICK_ID' => $clickId,
                    ]);
                }// end if()
            }// end foreach()
        }// end foreach()
        $this->unmatchedIds = $unmatchedIds;
        return $dataPay;
    }

    /**
     * 未归因数据缓存处理
     * @param array $unmatchedIds 订单id
     * @param array $val          data
     * @return array|mixed
     */
    private function processUnmatchedIds($unmatchedIds, $val)
    {
        $id     = $val['ORDER_ID'] ?? 0;
        $money1 = $val['MONEY'] ?? 0;
        $money2 = $val['ACTUALLY_MONEY'] ?? 0;
        // 代金券支付的，money 字段可能是0，会延后更新
        if (!empty($val['CHANNEL_ID']) && $money1 > 0 && $money2 > 0) {
            // 如果 ID 在 unmatchedIds 中，移除它
            if (in_array($id, $unmatchedIds)) {
                $unmatchedIds = array_values(array_diff($unmatchedIds, [$id]));
            }
        } else if (!in_array($id, $unmatchedIds)) {
            \Plus::$app->log->info('未归因数据缓存:' . json_encode($val), [], AdBaseInterface::LOG_DIR);
            // 如果 CLICK_ID 为空，将 ID 添加到 unmatchedIds
            $unmatchedIds[] = $id;
        }
        return $unmatchedIds;
    }

    /**
     * 上报数据 处理
     * @param array $data         data
     * @param array $uploadConfig 配置
     * @return array
     */
    public function processData(array $data, $uploadConfig): array
    {
        //上报数据 处理
        $rs = $this->processDataPay($data, $uploadConfig);
        //扣量上报
        $deductionService = new DeductionService();
        //扣量策略
        $strategies = [
            //new AmountDeductionStrategy(),
            //new TimesDeductionStrategy(),
            new CustomDeductionStrategy(),
        ];
        foreach ($rs as $k => $v) {
            /**
             * 策略
             * @var DeductionStrategyInterface $s
             */
            foreach ($strategies as $s) {
                $deductionService->setStrategy($s);
                $apply = $deductionService->applyDeduction($v);
                if ($apply) {
                    $rs[$k] = $s->getData(); //获取处理后的数据
                    break; //满足一个策略就退出循环
                }
            }
        }// end foreach()
        return $rs;
    }
}

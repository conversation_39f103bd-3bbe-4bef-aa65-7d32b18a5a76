<?php
/**
 * 百度数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 * phpcs:disable
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class <PERSON><PERSON> extends AdBaseInterface
{

    private $uploadFilter        = ['44512490' => 0, '24822640' => 0];
    private $uploadFilterPackage = ['44512490', '24822640'];
    private $akeyArr             = [];

    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        if (in_array($info['PACKAGE_ID'], $this->uploadFilterPackage)) { //每上报六个就要过滤1个
            $this->uploadFilter[$info['PACKAGE_ID']]++;
            if ($this->uploadFilter[$info['PACKAGE_ID']] % 6 != 0) {
                $this->upload($info, 'ACTIVE');
            } else { //存入表中
                $insertData = ['PACKAGE_ID' => $info['PACKAGE_ID'], 'MD5_DEVICE_ID' => $info['MD5_DEVICE_ID'], 'CLICK_ID' => $info['CLICK_ID'], 'ADD_TIME' => date("Y-m-d H:i:s")];
                \Plus::$app->origin_platform->insert('tb_baidu_upload_filter', $insertData);
            }
        } else {
            $this->upload($info, 'ACTIVE');
        }
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY');
    }

    /**
     * 公共上报方法
     * @param $info
     * @param $type
     * @return void
     */
    private function upload($info, $type): void
    {
        if (in_array($info['PACKAGE_ID'], $this->uploadFilterPackage)) { //过滤要上报的数据
            $sql     = "SELECT ID FROM tb_baidu_upload_filter WHERE CLICK_ID=" . $info['CLICK_ID'];
            $dataTmp = \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
            if (!empty($dataTmp)) {
                return;
            }
        }

        switch ($type) {
            case 'ACTIVE':
                $aType    = 'activate';
                $aValue   = 0;
                $typeName = '激活';
                break;
            case 'REG':
                $aType    = 'register';
                $aValue   = 0;
                $typeName = '注册';
                break;
            case 'PAY':
                $aType    = 'orders';
                $aValue   = $info['MONEY'] * 100;
                $typeName = '付费';
                break;
        }
        $callbackUrl = str_replace(['{{ATYPE}}', '{{AVALUE}}'], [$aType, $aValue], $info['CALLBACK_URL']);
        // 补充join_type参数
        $this->fillData($info, $type);
        $joinType = $info['active_info']['match_col'] ?? '';
        if ($joinType) {
            if ($joinType == "md5_oaid") {
                $joinType = "oaid";
            }
            if ($joinType == "md5_android_id") {
                $joinType = 'android_id';
            }
            if ($joinType == "md5_device_id") {
                $joinType = 'oaid';
            }
            if ($joinType == "device_code") {
                $joinType = 'oaid';
            }
            $callbackUrl .= '&join_type=' . $joinType;
        }
        $akey        = '';
        if (empty($this->akeyArr[$info['SV_KEY']])) {
            $sql         = "SELECT REMARK FROM tb_ad_account_conf WHERE ACCOUNT_ID = '" . $info['EXT_CLICK']['user_id'] . "'";
            $accountConf = \Plus::$app->base_conf_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
            $akey        = !empty($accountConf) ? $accountConf['REMARK'] : '';
            if (empty($akey)) {
                $akey = $info['EXT']['akey'];
            }
        } else {
            $akey = $this->akeyArr[$info['SV_KEY']];
        }

        if (!empty($akey)) {
            $logInfo                        = $info['log_info'] ?? [];
            $logInfo['channel_code']        = 'baidu';
            $this->akeyArr[$info['SV_KEY']] = $akey;
            $sign                           = md5($callbackUrl . $akey);
            $callbackUrl                   .= '&sign=' . $sign;
            $logInfo['request']             = json_encode(['url' => $callbackUrl]);
            $http                           = new Http($callbackUrl);
            $res                            = $http->get();

            //记录上报结果
            $logInfo['response'] = $res;
            $resArr              = json_decode($res, true);
            if (isset($resArr['error_code']) && $resArr['error_code'] == 0) {
                $logInfo['reported_status'] = 1;
            } else {
                $logInfo['reported_status'] = -1;
            }

            $this->log($info, $logInfo, $res, $callbackUrl);
        }// end if()
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }

    /**
     * 补全激活信息，获取归因方式
     * @param $info
     * @param $action
     * @return void
     */
    private function fillData(&$info, $action)
    {
        $activateTime = $info['TIME_SERVER'] ?? '';
        $deviceKey    = $info['DEVICE_KEY'] ?? '';

        if ($action != 'ACTIVE') {
            // 注册、付费额外查询激活时间
            $sql          = "select ACTIVATE_TIME from `ddc_platform`.`dwd_sdk_adsource_game` where 
                        game_id='{$info['GAME_ID']}' and 
                        main_account='{$info['CORE_ACCOUNT']}' and 
                        device_key='{$deviceKey}'";
            $activateTime = \Plus::$app->origin_platform->query($sql)->fetchColumn();
        }

        if (empty($activateTime) || empty($deviceKey)) {
            \Plus::$app->log->alert(json_encode(['t'=>$activateTime, 'k'=>$deviceKey]) . $info['ID'], [], AdBaseInterface::LOG_DIR);
            return $info;
        }
        $activateDate = date('Y-m-d', strtotime($activateTime));

        $sql = "select android_id,time,replace(json_extract(ext, '$.match_col'), '\"','') AS match_col 
                       from bigdata_dwd.dwd_sdk_activate_log_sv_game 
                where dt='{$activateDate}' and device_key='{$deviceKey}' and time='{$activateTime}'";
        $res = \Plus::$app->doris_entrance->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if (!empty($res)) {
            $info['active_info'] = $res;
        } else {
            \Plus::$app->log->alert('fillUpClick: empty res' . $info['ID'], [], AdBaseInterface::LOG_DIR);
        }
    }
}

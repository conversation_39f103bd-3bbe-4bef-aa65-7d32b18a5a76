<?php
declare(ticks=1);

namespace app\ad_upload;

use Plus\CLI\ProcessManage;

/**
 * 定时任务，每2分钟运行一次
 * 运行上报媒体
 * <AUTHOR>
 */
class WatchProcess2 extends ProcessManage
{

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->config = [
            $this->conf(1107, 'register,active'), //今日头条-微信小程序
        ];
    }

    /**
     * 单条配置
     * @param int    $channelId 渠道id
     * @param string $actions   动作
     * @return array
     */
    private function conf(int $channelId, string $actions = 'active,pay,register,create_role')
    {
        return [
            'file' => AdUpload::class, 'num' => 1, 'param' => "channel_id=$channelId actions=$actions",
        ];
    }
}

<?php

namespace app\service\AdvertiserData;

use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Components\MatchParams\AdCreativeMatcher;
use app\service\AdvertiserData\Scheme\AdCreativePassScheme;
use app\service\AdvertiserData\Scheme\CreativePayPermeationDailyScheme;
use app\service\AdvertiserData\Traits\AdServiceable;
use app\service\AdvertiserData\Traits\BasicOperator;
use app\service\AdvertiserData\Traits\Converter;
use app\service\General\BizTagsServ;
use app\util\Common;
use Aura\SqlQuery\QueryFactory;

class CreativePayPerDailyIndex
{
    use Converter, AdServiceable, BasicOperator;

    /**
     * @param array $options
     *
     * @return array|false
     */
    public function fetchPerRate(array $options = [])
    {
        $minGroups = ['tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id', 'creative_id'];
        $powerSql  = \Plus::$service->admin->getAdminPowerSql();

        $sub = CreativePayPermeationDailyScheme::NewOne()->select();

        $sub
            ->joinPowerSql($powerSql)
            ->joinCreativeBase('left')
            ->join($this->joinAccount('t_base'))
            ->joinAdpOauth('left','t_base')
            ->join((new JoinClause('left', 'dataspy.admin_user', 't_admin'))
                ->on('t_base.USER_ID', '=', 't_admin.ID'))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id' , '=','base_channel.channel_id'))
            ->join((new JoinClause('left', 'adp_platform.tb_adp_campaign', 'base_campaign'))
                ->on('base_campaign.channel_id', '=', 't_base.main_channel_id')
                ->on('base_campaign.campaign_id', '=', 't_base.campaign_id'))
            ->scope($this->buildGroup(['tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id', 'creative_id']))
            ->scope($this->buildColumn($this->getSubColumns()));

        $matcher = new AdCreativeMatcher($sub->fieldReflect());
        $matcher->setParams($options);
        $matcher->execute($sub);

        $linkRelate = [
            'main_body.tday = slave_body.tday',
            'main_body.package_id = slave_body.package_id',
            'main_body.channel_id = slave_body.channel_id',
            'main_body.campaign_id = slave_body.campaign_id',
            'main_body.plan_id = slave_body.plan_id',
            'main_body.creative_id = slave_body.creative_id',
            'main_body.day_type = slave_body.day_type',
        ];

        $mainScheme = (new QueryFactory('mysql'))->newSelect();
        $sql        = " ddc_platform.dws_creative_ad_pay_permeation_daily main_body ";
        $mainScheme
            ->fromRaw($sql)
            ->joinSubSelect('inner', $sub->toSql(), 'slave_body', implode(' and ', $linkRelate))
            ->cols($this->getTopCols())
            ->groupBy([
                'main_body.tday',
                'main_body.package_id',
                'promotion_channel_id',
                'main_body.campaign_id',
                'main_body.plan_id',
                'main_body.creative_id',
            ]);
        Common::dumpSql($mainScheme->__toString());
        return $this->fetchAll($mainScheme->__toString());
    }

    /**
     * @return array[]
     */
    protected function getTopCols(): array
    {
        return [
            'main_body.tday',
            'main_body.package_id',
            'main_body.campaign_id',
            'main_body.plan_id',
            'main_body.creative_id',
            'main_body.cp_game_id',
            'main_body.game_id',
            'slave_body.promotion_channel_id',
            'slave_body.promotion_id as ',
            'slave_body.channel_main_id',
            'slave_body.power_channel_id as channel_id',
            'slave_body.platform_id',
            'slave_body.department_id',
            'slave_body.user_id  ',
            'slave_body.ad_account',
            'slave_body.ad_account_id',
            'main_body.day_type',
            'sum(pay_user_num) as pay_per_user',
        ];
    }

    /**
     * @return string[]
     */
    protected function getSubColumns(): array
    {
        $mainTable    = CreativePayPermeationDailyScheme::MAIN_TABLE['alias'];

        $planChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString     = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0,IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) AS promotion_channel_id",
            $planChannelsString
        );
        $channelMainString = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );



        $fixInfoIndex = [
            $mainTable.'.channel_id',
            $mainTable . '.tday',
            $mainTable . '.package_id',
            'POWER.channel_id as power_channel_id',
            $mainTable.'.campaign_id',
            $mainTable.'.plan_id',
            $mainTable . '.cp_game_id',
            $mainTable . '.game_id',
            $mainTable . '.creative_id',
            'POWER.popularize_v2_id as promotion_id',
            $channelMainString,
            $channelString,
            'POWER.platform_id as platform_id',
            'COALESCE(IF(POWER.channel_id IN ('.$planChannelsString.'),t_admin.department_id, POWER.ad_department_id),0) as department_id',
            'COALESCE(IF(POWER.channel_id IN ('.$planChannelsString.'),t_base.USER_ID, POWER.AD_USER_ID), 0) AS user_id',
            'adp_oauth.ADVERTISER_NAME as account_name',
            'base_account.id as ad_account_id',
            'base_account.ad_account as ad_account',
            'max(day_type) as day_type',
            "case when USER_OS = JSON_ARRAY('ANDROID') then 'ANDROID' when USER_OS = JSON_ARRAY('IOS') then 'IOS' when JSON_CONTAINS(USER_OS, '[\"ANDROID\",\"IOS\"]', '$') = 1 then '混投' else '无' end as dim_user_os"
        ];
        return $fixInfoIndex;
    }

    /**
     * @return string[]
     */
    protected function groupsReflect(): array
    {
        $mainTable = CreativePayPermeationDailyScheme::MAIN_TABLE['alias'];

        return [
            'tday'            => $mainTable . '.tday',
            'cp_game_id'      => $mainTable . '.cp_game_id',
            'game_id'         => $mainTable . '.game_id',
            'package_id'      => $mainTable . '.package_id',
            'channel_id'      => $mainTable . '.channel_id',
//            'channel_main_id' => 'POWER.channel_main_id',
            'creative_id'     => $mainTable . '.creative_id',
            'day_type'        => $mainTable . '.day_type',
            'ad_account_id'   => 'base_account.id',
        ];
    }

    private function fetchAll(string $sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }
}
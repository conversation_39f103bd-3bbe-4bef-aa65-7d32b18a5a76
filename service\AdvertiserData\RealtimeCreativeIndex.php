<?php

namespace app\service\AdvertiserData;

use app\extension\Support\Collections\Arr;
use app\extension\TableAssistant\Helpers\TableBaseHelp;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Components\Helpers\WrapQuery;
use app\service\AdvertiserData\Components\MatchParams\AdCreativeMatcher;
use app\service\AdvertiserData\Scheme\RealtimeCreativeScheme;
use app\service\AdvertiserData\Traits\AdServiceable;
use app\service\AdvertiserData\Traits\Converter;
use app\service\General\BizTagsServ;
use app\util\Common;
use Aura\SqlQuery\Common\SelectInterface;
use Plus\Util\Pagination;

/**
 * @RealtimeCreativeIndex 创意实时统计指标查询服务
 */
class RealtimeCreativeIndex
{
    use Converter, AdServiceable;

    /**
     * 查询基础数据
     *
     * @param array $where
     * @param array $page
     * @param array $group
     * @param null $sort
     * @param string[] $column
     * @param null $options
     * @param bool $returnTotal
     *
     * @return array
     */
    public function listBase(
        array $where = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = ['*'],
              $options = null,
        bool  $returnTotal = true
    ): array
    {
        $today    = date('Y-m-d');
        $result   = collect();
        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = RealtimeCreativeScheme::NewOne()->select();

        $scheme
            ->joinPowerSql($powerSql)
            ->join($this->joinAccount($scheme::MAIN_TABLE['alias']))
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->join((new JoinClause('left', 'adp_platform.tb_adp_campaign', 'base_campaign'))
                ->on('base_campaign.channel_id', '=', 't_base.main_channel_id')
                ->on('base_campaign.campaign_id', '=', 't_base.campaign_id'));


        // 搜索条件拼接
        $matcher = new AdCreativeMatcher($scheme->fieldReflect());
        $matcher->setParams($where);
        $matcher->execute($scheme);

        $backupScheme = clone $scheme;
        //字段
        $scheme->scope(TableBaseHelp::setColumn($this->getColumns($where)));
        if ($returnTotal) {

            // 获取总行数
            $rowCountScheme = clone $backupScheme;
            $rowCountScheme->scope(TableBaseHelp::setColumn($this->getColumns($where, false, false, ["update_time"])));
            $rowCountScheme->scope(TableBaseHelp::setGroup(TableBaseHelp::changeGroups($group, $this->groupsReflect())));
            Common::dumpSql((clone $rowCountScheme)->toSql());
            $countSql = WrapQuery::countRowQuery($rowCountScheme->toSql());

            // 汇总行
            $totalScheme = clone $backupScheme;
            $totalScheme->scope(TableBaseHelp::setColumn($this->getColumns($where, false, true)));
            $totalSql       = $totalScheme->toSql();
            $summaryRow     = $this->fetchOne($totalSql);
            $countRow       = $this->fetchOne($countSql);
            $lastUpdateTime = Arr::pull($summaryRow, 'update_time');

            $result
                ->put('summary', $summaryRow)
                ->put('total', $countRow['total'] ?? 0)
                ->put('time', $lastUpdateTime);
        }

        //分组
        $scheme->scope(TableBaseHelp::setGroup(TableBaseHelp::changeGroups($group, $this->groupsReflect())));
        // 排序处理
        if (!empty($sort)) {
            $scheme->scope(TableBaseHelp::setOrder(Arr::wrap($sort)));
        }
        // 分页
        if (!empty($page)) {
            $scheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }

        Common::dumpSql((clone $scheme)->toSql());
        $result->put('list', $this->fetchAll($scheme->toSql()));

        return $result->toArray();
    }

    /**
     * 组合
     *
     * @param $groups
     *
     * @return \Closure
     */
    protected function buildGroup($groups): \Closure
    {
        $groups = $this->changeGroups($groups);

        return function (&$query) use ($groups) {
            if (!$query instanceof SelectInterface) return;

            $query->groupBy($groups);
        };
    }

    /**
     * 排序处理
     *
     * @param $order
     *
     * @return \Closure
     */
    protected function buildOrder($order): \Closure
    {
        return function (&$query) use ($order) {
            if (!$query instanceof SelectInterface) return;

            $query->orderBy($order);
        };
    }

    /**
     * @param array $columns
     *
     * @return \Closure
     */
    protected function buildColumn(array $columns = ['*']): \Closure
    {
        return function (&$query) use ($columns) {
            if (!$query instanceof SelectInterface) return;
            $query->cols($columns);
        };
    }

    /**
     * @param array $pagination
     *
     * @return \Closure
     */
    protected function buildPage(array $pagination): \Closure
    {
        [
            'page'      => $page,
            'page_size' => $pageSize,
        ] = $pagination;

        $offset = ($page - 1) * $pageSize;

        return function (&$query) use ($pageSize, $offset) {
            if (!$query instanceof SelectInterface) return;
            $query
                ->limit($pageSize)
                ->offset($offset);
        };

    }

    /**
     * 获取SQL查询字段
     *
     * @param       $options
     * @param bool $isTop
     * @param bool $isTotal
     * @param array $filterCol 过滤字段
     * @param array $onlyCol 只保留字段
     *
     * @return array
     * @todo 待优化
     */
    private function getColumns($options, bool $isTop = false, bool $isTotal = false, $filterCol = [], $onlyCol = []): array
    {
        $mainAlias = RealtimeCreativeScheme::MAIN_TABLE['alias'];

        $planChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString     = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0, IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) AS promotion_channel_id",
            $planChannelsString
        );
        $channelMainString = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );


        /**
         * 固定详情数据
         */
        $fixInfoIndex = [
            'tday'                 => ['source' => $mainAlias],
            'cp_game_id'           => ['source' => $mainAlias],
            'game_id'              => ['source' => $mainAlias],
            'app_show_id'          => ['source' => 'POWER'],
            //            'channel_main_id'      => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (6568, 6822, 5329), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id'],
            'channel_main_id'      => ['info', 'raw' => $channelMainString],
            //            'promotion_channel_id' => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id NOT IN (6568, 6822, 5329), POWER.channel_id,IF(t_base.channel_id != 0, t_base.channel_id,POWER.channel_id)), 0) AS promotion_channel_id'],
            'promotion_channel_id' => ['info', 'raw' => $channelString],
            'platform_id'          => ['source' => 'POWER'],
            'package_id'           => ['source' => $mainAlias],
            'campaign_id'          => ['source' => $mainAlias],
            'plan_id'              => ['source' => $mainAlias],
            'creative_id'          => ['source' => $mainAlias],
            'promotion_id'         => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            'department_id'        => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN ('.$planChannelsString.'),t_admin.department_id, POWER.ad_department_id),0) as new_department_id'],
            'user_id'              => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN ('.$planChannelsString.'),t_base.USER_ID, POWER.AD_USER_ID), 0) AS user_id'],
            'ad_account'           => ['source' => 'base_account'],
            'account_name'         => ['source' => 'adp_oauth', 'source_field' => 'ADVERTISER_NAME'],
            'account_id'           => ['source' => $mainAlias],
            'marketing_goal'       => ['source' => $mainAlias],
        ];

        /**
         * 需要计算的指标
         */
        $calculatedIndex = [
            'cost'                => ['source' => $mainAlias, 'aggregate' => 'sum'], // 返点前消耗金额
            'cost_discount'       => ['source' => $mainAlias, 'aggregate' => 'sum'], // 返点后消耗金额
            'new_user'            => ['source' => $mainAlias, 'aggregate' => 'sum'], // 广告新增用户
            'create_role_new'     => ['source' => $mainAlias, 'aggregate' => 'sum'], // 创角新增
            'pay_user_new'        => ['source' => 't_payment', 'aggregate' => 'sum'], // 新增用户付费金额
            'pay_user'            => ['source' => 't_payment', 'aggregate' => 'sum'], // 付费用户数
            'pay_money'           => ['source' => 't_payment', 'aggregate' => 'sum'], // 付费金额
            'pay_count'           => ['source' => 't_payment', 'aggregate' => 'sum'], // 付费次数
            'pay_money_new'       => ['source' => 't_payment', 'aggregate' => 'sum'], // 新用户付费金额
            'pay_count_new'       => ['source' => 't_payment', 'aggregate' => 'sum'], //新用户付费次数
            'show'                => ['source' => $mainAlias, 'aggregate' => 'sum'], // 展示数
            'click'               => ['source' => $mainAlias, 'aggregate' => 'sum'], // 点击数
            'download'            => ['source' => $mainAlias, 'aggregate' => 'sum'], //开始下载数
            'activate'            => ['source' => $mainAlias, 'aggregate' => 'sum'], // 激活数
            'convert'             => ['source' => $mainAlias, 'aggregate' => 'sum'], // 转化数
            'install'             => ['source' => $mainAlias, 'aggregate' => 'sum'], // 安装数
            'lp_view'             => ['source' => $mainAlias, 'aggregate' => 'sum'], // 落地页展示数
            'lp_download'         => ['source' => $mainAlias, 'aggregate' => 'sum'], // 落地页点击数
            'download_start'      => ['source' => $mainAlias, 'aggregate' => 'sum'], // 开始下载数
            'register'            => ['source' => $mainAlias, 'aggregate' => 'sum'], // 注册数
            'new_real_user'       => ['source' => $mainAlias, 'aggregate' => 'sum'], // 广告新增实名用户
            'new_user_emulator'   => ['source' => $mainAlias, 'aggregate' => 'sum'], // 广告新增用户(模拟器)
            'activate_device'     => ['source' => $mainAlias, 'aggregate' => 'sum'], // 激活设备
            'pay_new_user_7days'  => ['source' => $mainAlias, 'aggregate' => 'sum'], // 付费新用户(7天内)
            'pay_frequency_7days' => ['source' => $mainAlias, 'aggregate' => 'sum'], // 新用户付费次数(7天内)
            'online_time'         => ['source' => $mainAlias, 'aggregate' => 'sum'], // 广告新增用户今日总在线时长(秒)
            'first_online_time'   => ['source' => $mainAlias, 'aggregate' => 'sum'], //广告新增用户首次在线时长(秒)
            'update_time'         => ['source' => $mainAlias, 'aggregate' => 'max'],
        ];

        $result  = [];
        $collect = collect();

        if (!$isTotal) {
            $collect = $collect->merge($fixInfoIndex);
        }
        $collect = $collect->merge($calculatedIndex);

        $collect->each(function (&$item, $key) use (&$result, $isTop, $filterCol, $onlyCol) {

            //过滤字段
            if (in_array($key, $filterCol) && $filterCol) {
                return;
            }

            //只保留字段
            if (!in_array($key, $onlyCol) && $onlyCol) {
                return;
            }

            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];
                $format    = "{$aggregate}(%s)";
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }


            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }

            $field    .= $item['source_field'] ?? $key;
            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }

    /**
     * 分组对照SQL的关系
     *
     * @return string[]
     */
    private function groupsReflect(): array
    {
        $mainAlias = RealtimeCreativeScheme::MAIN_TABLE['alias'];

        return [
            'tday'          => $mainAlias . '.tday',
            'cp_game_id'    => $mainAlias . '.cp_game_id',
            'game_id'       => $mainAlias . '.game_id',
            'package_id'    => $mainAlias . '.package_id',
            'channel_id'    => $mainAlias . '.channel_id',
            'creative_id'   => $mainAlias . '.creative_id',
            'campaign_id'   => $mainAlias . '.campaign_id',
            'plan_id'       => $mainAlias . '.plan_id',
            'ad_account_id' => 'base_account.id',
            'department_id' => 'new_department_id',
        ];
    }

    /**
     * 查询一条数据
     *
     * @param $sql
     *
     * @return mixed
     */
    private function fetchOne($sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * 执行查询数据
     *
     * @param $sql
     *
     * @return array|false
     */
    private function fetchAll($sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

}
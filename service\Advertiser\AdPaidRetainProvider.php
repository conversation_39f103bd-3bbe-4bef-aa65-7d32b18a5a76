<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\General\BizTagsServ;
use app\util\Common;
use Smarty\Template;

/**
 * Class AdPaidRetainProvider
 * 广告付费留存查询
 * @date create by 2025/03/25
 *
 */
class AdPaidRetainProvider
{
    const RESULT_INFO    = 1;
    const RESULT_SUMMARY = 2;
    const RESULT_ALL     = 3;


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    protected function connDoris()
    {
        return FakeDB::connection('doris_entrance');
    }

    /**
     * 付费留存查询
     *
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $paginate
     * @param array $columns
     * @param bool  $isApiRule
     * @return void
     * @throws \Exception
     */
    public function list(
        array $params,
        array $groups = [],
        array $paginate = [],
        array $sort = [],
        array $columns = ['*'],
        bool  $isApiRule = false,
        int   $resultMode = self::RESULT_ALL
    ): array
    {
        $tplMap = [
            'total' => 'sql/advertise/ad_paid_retain/paid_retain_total.tpl',
            'info'  => 'sql/advertise/ad_paid_retain/paid_retain.tpl',
        ];

        if (empty($params['range_date'])) return [];
        $modeTest   = $params['mode_test'] ?? 0;
        $showType   = Arr::pull($params, 'show_type', 1);
        $powerSQL   = null;
        $result     = ['total' => 0, 'list' => []];
        $db         = $this->connDoris();
        $adChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $isRealtime = $params['is_realtime'] ?? 0;
        $minDate    = min($params['range_date']);
        $retainType = Arr::pull($params, 'retain_type', 1);

        if (!$isRealtime)
            $maxRetainNode = days_apart(date('Y-m-d'), $minDate) - 1;
        else
            $maxRetainNode = days_apart(date('Y-m-d'), $minDate);

        if (!$isApiRule) $powerSQL = \Plus::$service->admin->powerSubSQL();

        $commonFn = function (Template &$tpl, $isSummary = false) use (
            $params, $groups, $adChannels, $powerSQL, $maxRetainNode, $retainType, $showType, $modeTest
        ) {
            $tpl
                ->assign('ad_channels', $adChannels)
                ->assign('params', $params)
                ->assign('max_retain_node', $maxRetainNode)
                ->assign('retain_type', $retainType)
                ->assign('show_type', $showType)
                ->assign('mode_test', $modeTest)
                ->assign('columns', ['pay_new_user_7days', 'pay_user_new']);

            if (!is_null($powerSQL)) {
                $tpl->assign('power_join_sql', $powerSQL);
            }

            if (!$isSummary) {
                $tpl->assign('groups', $groups);
            }
        };

        $totalTpl = \Plus::$app->sqlTemplates->createTemplate($tplMap['total']);
        $commonFn($totalTpl);
        $total           = $db->query($totalTpl->fetch())->fetch()['total_count'] ?? 0;
        $result['total'] = $total;

        if ($total == 0) {
            return $result;
        }

        if ($resultMode & self::RESULT_INFO) {
            $infoTpl = \Plus::$app->sqlTemplates->createTemplate($tplMap['info']);
            $commonFn($infoTpl);

            $infoTpl
                ->assign('max_retain_node', $maxRetainNode)
                ->assign('paginate', $paginate);

            if (!empty($sort)) $infoTpl->assign('sorts', $sort);
            $result['list'] = $db->query($infoSQL = $infoTpl->fetch())->fetchAll();
            @Common::dumpSql($infoSQL);
        }

        if ($resultMode & self::RESULT_SUMMARY) {
            $summaryTpl = \Plus::$app->sqlTemplates->createTemplate($tplMap['info']);
            $commonFn($summaryTpl, true);
            $summaryTpl
                ->assign('max_retain_node', $maxRetainNode);

            $result['summary'] = $db->query($summarySQL = $summaryTpl->fetch())->fetch();
            @Common::dumpSql($summarySQL);
        }

        return $result;
    }


}
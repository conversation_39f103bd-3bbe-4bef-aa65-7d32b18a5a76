<?php

namespace app\ad_upload_tmp\deduction;

/**
 * 自定义扣量策略 数据查询
 * <AUTHOR>
 */
trait DeductionStrategyTrait
{
    /**
     * 文本比较
     * @param array  $rule 规则
     * @param string $txt  字符串
     * @return array
     */
    protected function textCompare($rule, $txt)
    {
        if (!empty($txt)) {
            $condition = "return '{$txt}' {$rule['operator']} '{$rule['value']}';";
            $rs        = stripos($rule['value'], $txt);
            if ($rule['operator'] == 'in') {
                return ['result' => $rs !== false, 'condition' => $condition];
            } else if ($rule['operator'] == 'not_in') {
                return ['result' => $rs === false, 'condition' => $condition];
            } else {
                return ['result' => eval($condition), 'condition' => $condition];
            }
        }
        return ['result' => false, 'condition' => 'empty txt'];
    }

    /**
     * 检查 运算条件 是否合法
     * @param array $rule 规则
     * @return bool
     */
    protected function conditionCheck($rule)
    {
        $array = [
            "=",
            "!=",
            "<=",
            "<",
            ">=",
            ">",
            "in",
            "not_in",
        ];
        if (in_array($rule['operator'], $array)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取随机金额及对应的上报行为
     * @param string|null $amountStr 逗号分隔的金额字符串
     * @return array [$reportMoney, $reported_behavior]
     */
    protected function getRandomAmount($amountStr): array
    {
        if (!empty($amountStr)) {
            $amountOptions = explode(',', $amountStr);
            return [$amountOptions[array_rand($amountOptions)], 2]; // 2 代表金额扣减
        }
        return [$this->data['MONEY'], 0]; // 0 代表正常上报
    }

    /**
     * 获取总付费金额
     * @param int    $cpGameId    游戏原名id
     * @param string $coreAccount 核心账号
     * @param string $payTime     付费时间
     * @return mixed
     */
    protected function getTotalPaidAmount($cpGameId, $coreAccount, $payTime)
    {
        $sql = "
        SELECT SUM(MONEY) as TOTAL_AMOUNT
        FROM origin_platform.tb_sdk_user_payment t1
                 left join base_conf_platform.tb_package_detail_conf t2 on t1.PACKAGE_ID = t2.PACKAGE_ID
        where t1.CORE_ACCOUNT = '{$coreAccount}' and PAY_RESULT=1 and PAY_TIME <= '{$payTime}'
          and CP_GAME_ID = '{$cpGameId}'
        ";

        return \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * 获取付费次数
     * @param int    $cpGameId    游戏原名id
     * @param string $coreAccount 核心账号
     * @param string $payTime     支付时间
     * @return array
     */
    protected function getPaidTimesByCpAccount($cpGameId, $coreAccount, $payTime)
    {
        $sql = "
        SELECT count(*) as PAY_TIMES
        FROM origin_platform.tb_sdk_user_payment t1
                 left join base_conf_platform.tb_package_detail_conf t2 on t1.PACKAGE_ID = t2.PACKAGE_ID
        where t1.CORE_ACCOUNT = '{$coreAccount}' and PAY_RESULT=1 and PAY_TIME <= '{$payTime}'
          and CP_GAME_ID = '{$cpGameId}'";

        return \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * 获取在线时长
     * @param int    $cpGameId    游戏原名id
     * @param string $coreAccount 核心账号
     * @param string $payTime     支付时间
     * @return mixed
     */
    protected function getOnlineTime($cpGameId, $coreAccount, $payTime)
    {
        $sql = "
        select SUM(ONLINE_TIME) as ONLINE_TIME
        from ddc_platform.dwd_sdk_user_active_daily
        where
           TDAY between '2023-01-01' and '$payTime'
          and CORE_ACCOUNT = '{$coreAccount}'
          and CP_GAME_ID = '{$cpGameId}'
        ";
        return \Plus::$app->doris_entrance->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }


    /**
     * 用户时间缓存
     * @var array
     */
    private $userTime = [];

    /**
     * 包号新增维度的数据：
     * 获取 用户 激活时间、新增时间  包号归因
     * @param int    $packageId   包号
     * @param string $coreAccount 核心账号
     * @return array
     * @throws \Exception
     */
    protected function getUserTime($packageId, $coreAccount)
    {
        $key = $packageId . $coreAccount;
        if (isset($this->userTime[$key])) {
            return $this->userTime[$key];
        }
        $sql  = "SELECT
                    TIME,
                    DEVICE_CODE
                  FROM
                    `origin_platform`.`tb_sdk_user_newlogin_package`
                  WHERE PACKAGE_ID ='$packageId'
                    AND core_account = '$coreAccount'";
        $data = \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
        $rs   = [
            'activeTime'   => '', // 广告激活时间
            'newLoginTime' => '', //广告新增时间
        ];
        if ($data) {
            $rs['newLoginTime'] = $data['TIME'];
            $sql2               = "SELECT
                      TIME 
                    FROM
                      tb_sdk_active_log
                    WHERE DEVICE_CODE = '{$data['DEVICE_CODE']}' and PACKAGE_ID='$packageId'";
            $data2              = \Plus::$app->origin_platform->query($sql2)->fetch(\PDO::FETCH_ASSOC);
            if ($data2) {
                $rs['activeTime'] = $data2['TIME'];
            }
        }
        $this->userTime[$key] = $rs;
        return $rs;
    }

    /**
     * 获取广告主id
     * @var array $adAccountIds
     */
    protected $adAccountIds = [];

    /**
     * 获取广告主id
     * @param int    $channelId 渠道id
     * @param string $planId    计划id
     * @return mixed|string
     */
    protected function getAdAccountId($channelId, $planId)
    {
        $key = $channelId . $planId;
        if (isset($this->adAccountIds[$key])) {
            return $this->adAccountIds[$key];
        }
        $sql  = "select ADVERTISER_ID from adp_platform.tb_adp_plan_base 
                     where PLAN_ID = '{$planId}'";
        $data = \Plus::$app->adp_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if ($data) {
            $this->adAccountIds[$key] = $data['ADVERTISER_ID'];
            return $data['ADVERTISER_ID'];
        } else {
            $this->adAccountIds[$key] = '';
            return "";
        }
    }

    /**
     * roi、成本 计算缓存
     * @var array $planRoiCost
     */
    protected $planRoiCost = [];

    /**
     * roi、成本 计算
     * @param int   $dayType N 天
     * @param array $data    订单数据
     * @return string|float[]|int[]
     * @throws \Exception
     */
    protected function getPlanRoiCost($dayType, $data)
    {
        $newLoginTime = $data['NEWLOGIN_TIME'] ?? '';
        if (empty($newLoginTime)) {
            $userTime     = $this->getUserTime($data['PACKAGE_ID'], $data['CORE_ACCOUNT']);
            $newLoginTime = $userTime['newLoginTime'];
        }
        if (empty($newLoginTime)) {
            return 'empty_newLoginTime';
        }
        $activeDay     = date('Y-m-d', strtotime($newLoginTime));
        $now           = new \DateTime(date('Y-m-d', strtotime($data['PAY_TIME'])));
        $activeTimeObj = new \DateTime($activeDay);
        $days          = $now->diff($activeTimeObj)->days + 1;

        if ($days != $dayType) {
            return $newLoginTime . '|' . $days . '!=dayType:' . $dayType;
        }
        $sql = "SELECT
              SUM (t1.reported_money) AS pay_money,
              COUNT (t1.order_id) AS pay_count
            FROM
              bigdata_dwd.dwd_reported_paid_platform_log AS t1
              LEFT JOIN ddc_platform.dwd_sdk_adsource_game AS t2 USING (SOURCE_ID, CP_GAME_ID)
            WHERE t1.reported_status = 1 -- 已成功上报的状态
               AND t2.CHANNEL_ID = {$data['CHANNEL_ID']} -- 使用CHANNEL_ID
               AND t2.PLAN_ID = '{$data['PLAN_ID']}' -- 使用PLAN_ID值
               AND (
                DATEDIFF (t1.PAY_TIME, t2.NEWLOGIN_TIME) + 1
              ) <= {$dayType} -- 使用day_type值：roin 与付费成本n的 累计额
               AND LEFT (t2.NEWLOGIN_TIME, 10) = '{$activeDay}' -- 使用add_date值
               AND t1.pay_time < '{$data['PAY_TIME']}'
            GROUP BY LEFT (t2.NEWLOGIN_TIME, 10)";
        //收入
        $payInfo = \Plus::$app->doris_entrance->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if (!$payInfo) {
            return 'empty_payInfo';
        }
        //支出
        $sql      = "SELECT
                  sum(cost) as cost
                FROM
                  `bigdata_dws`.`dws_ad_creative_daily`
                WHERE 
                   tday = '{$activeDay}'
                  AND channel_id='{$data['CHANNEL_ID']}'
                  AND plan_id = '{$data['PLAN_ID']}'";
        $costInfo = \Plus::$app->doris_entrance->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if (!$costInfo) {
            return 'empty_costInfo';
        }
        $cal = []; //计算过程
        //roi
        $roi = 0;
        if ($costInfo['cost'] > 0) {
            $roi        = ($payInfo['pay_money'] / $costInfo['cost']) * 100;
            $cal['roi'] = "({$payInfo['pay_money']} / {$costInfo['cost']}) * 100";
        }
        //归属计划n日付费次数成本
        $cost = 9999999999;
        if ($payInfo['pay_count'] > 0 && $costInfo['cost'] > 0) {
            $cost        = $costInfo['cost'] / $payInfo['pay_count'];
            $cal['cost'] = "({$costInfo['cost']} / {$payInfo['pay_count']})";
        }

        $rs = [
            'roi'  => $roi,
            'cost' => $cost,
            'cal'  => $cal,
        ];
        return $rs;
    }
}

<?php

namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;
use app\service\General\Helpers\TableConst;
use app\service\Media\Helper\MediaTableConst;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;


class RechargeSummary
{

    const MODE_SUMMARY = 2;
    const MODE_LIST = 1;


    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        if(!$groups){
            $groups = ["tday","package_id","payway"];
        }
        if(!in_array("payway",$groups)){
            $groups[] = "payway";
        }
        $qb = $this->getQueryBuilder();
        $this->whereMatch(
            $qb,
            $params,
            [
                "cp_game_id"=>"es.cp_game_id",
                "package_id"=>"es.package_id"
            ]
        );
        $result = [];

        if ($mode & static::MODE_LIST) {
            $qb->columns([
                'es.tday',
                'es.cp_game_id',
                'es.game_id',
                'es.package_id',
                'payway_conf.NAME payway',
                'package_detail.platform_id',
                new Fragment('SUM(DECUCT_COIN) as decuct_coin'),
                new Fragment('SUM(COUPON_MONEY) as coupon_money'),
                new Fragment('SUM(MONEY) as money'),
                new Fragment('SUM(PAY_USER) as pay_user'),
                new Fragment('SUM(MONEY)/sum(PACKAGE_MONEY) as money_proportion'),
                new Fragment('SUM(PAY_USER)/sum(PACKAGE_PAY_USER) as pay_user_proportion'),
            ]);

            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    $infoQb->groupBy($g);
                }
            }
            $noPageQb = clone $infoQb;

            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;
                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }

            if (!empty($sort)) {
                $infoQb->orderBy($sort);
            }

            $result['list']  = $infoQb->fetchAll();
            $result['total'] = $this
                ->getConn()
                ->select()
                ->from(new Fragment('(' . (clone $noPageQb)->__toString() . ') as total_body'))
                ->count();
        }

        if ($mode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            $summaryQb->columns([
                new Fragment('SUM(DECUCT_COIN) as decuct_coin'),
                new Fragment('SUM(COUPON_MONEY) as coupon_money'),
                new Fragment('SUM(MONEY) as money'),
                new Fragment('SUM(PAY_USER) as pay_user'),
                new Fragment('SUM(MONEY)/sum(PACKAGE_MONEY) as money_proportion'),
                new Fragment('SUM(PAY_USER)/sum(PACKAGE_PAY_USER) as pay_user_proportion'),
            ]);
            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * @param SelectQuery $qb
     * @param array $params
     * @param array $reflectMap
     *
     * @return void
     */
    protected function whereMatch(SelectQuery &$qb, array $params = [], array $reflectMap = [])
    {
        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            $rangeDate = [
                $params['range_date_start'],
                $params['range_date_end'],
            ];

            sort($rangeDate);
            $rangeDate = array_unique($rangeDate);

            if (count($rangeDate) === 1) {
                $qb->where('tday', $rangeDate[0]);
            }
            else {
                $qb->where('tday', 'between', $rangeDate[0], $rangeDate[1]);
            }
        }

        if (isset($params['cp_game_id'])) {
            $d     = $params['cp_game_id'];
            $field = $reflectMap['cp_game_id'] ?? 'cp_game_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['package_id'])) {
            $d = $params['package_id'];
            $field = $reflectMap['package_id'] ?? 'package_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['payway'])) {
            $d = $params['payway'];
            $field = $reflectMap['payway'] ?? 'payway';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['platform_id'])) {
            $d = $params['platform_id'];
            $field = $reflectMap['platform_id'] ?? 'platform_id';
            $qb->where($field, new Parameter($d));
        }
    }


    /**
     * @param int $qbMode
     *
     * @return \Spiral\Database\Query\SelectQuery
     */
    private function getQueryBuilder(int $qbMode = -1): \Spiral\Database\Query\SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from(MediaTableConst::DWS_ECHARGE_SUMMARY_DAILY . ' as es');
        $qb
            ->leftJoin(TableConst::BASE_PAYWAY_CONF, 'payway_conf')
            ->on([
                'es.payway' => 'payway_conf.CODE',
            ])->leftJoin(TableConst::CONF_PACKAGE_DETAIL, 'package_detail')
            ->on([
                'es.PACKAGE_ID' => 'package_detail.PACKAGE_ID',
            ]);


        return $qb;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $mode
     *
     * @return array
     */
    public function getDimensionWithAccount(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $result = [];
        $db     = $this->getConn();
        $qb     = $db
            ->select()
            ->from('ddc_platform.dwd_media_account_snapshot_audience_analyze as account_audience');

        if ($mode & static::MODE_LIST) {
            $infoQb = clone $qb;

            $infoQb->columns([
                'tday',
                'data_type as data_type',
                'dimension as dimension',
                'num as num',
            ]);

            $result['list'] = $infoQb->fetchAll();
        }

        return $result;
    }

    /**
     * @param array $groupTA
     *
     * @return array|array[]
     */
    public function getDimensionGroupTA(array $groupTA): array
    {
        if (empty($groupTA)) return [];

        $result = ['list' => []];
        $db     = $this->getConn();
        $qb     = null;

        foreach ($groupTA as $foo) {
            [
                'tday'       => $tDay,
                'account_id' => $accountId,
            ] = $foo;

            if (empty($tDay) || empty($accountId)) {
                continue;
            }

            $newQb = $db
                ->select()
                ->from('ddc_platform.dwd_media_account_snapshot_audience_analyze')
                ->where('tday', $tDay)
                ->where('account_id', $accountId)
                ->columns([
                    'tday as tday',
                    'account_id as account_id',
                    'data_type as data_type',
                    'dimension as dimension',
                    'num as num',
                ]);

            if (is_null($qb)) {
                $qb = $newQb;
            }
            else {
                $qb->unionAll($newQb);
            }
        }

        if (!is_null($qb)) {
            $result['list'] = $qb->fetchAll();
        }

        return $result;
    }


}
<?php

namespace app\apps\tool\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\service\Tool\ClickInfoServ;
/**
 * 日志 -> 点击明细
 *
 * @route /tool/click-info/*
 *
 */
class DebugController extends BaseTableController
{

    protected function data(Collection $params): array
    {
        $options = $params->toArray();
        $ssdb =\Plus::$app->redis;
        $username = \Plus::$service->admin->getUsername();
        $key =  $username.":debug:content";
        $data = $ssdb->get($key)?json_decode($ssdb->get($key)):[];
        $ssdb->set($key,json_encode([]));
        return ['list' => $data, 'summary' => [], 'total' => count($data)];

    }

    /**
     * 设置切换状态
     * @param Collection $params
     * @return array
     * @throws \RedisException
     */
    public function switchStatusAction($data): array
    {
        $status = $data["status"]??0;
        $ssdb =\Plus::$app->redis;
        $username = \Plus::$service->admin->getUsername();
        $key =  $username.":debug:switch";
        $ssdb->set($key,$status);
        return $this->success([]);

    }

    /**
     * 获取切换状态
     * @param Collection $params
     * @return array
     * @throws \RedisException
     */
    public function getStatusAction($data): array
    { 
        $ssdb =\Plus::$app->redis;
        $username = \Plus::$service->admin->getUsername();
        $key =  $username.":debug:switch";
        $status = $ssdb->get($key)?1:0;
        return $this->success(["username"=>$username,"status"=>$status]);
    }

    protected function fields(Collection $params): array
    {
        $fields = [

        ];

        $classify = [

        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $start = date('Y-m-d');
        $end   = date('Y-m-d');
        /*

        */
        return collect([
            ['field' => 'method', 'default' => ['data', 'fields']],
            ['field' => 'status', 'default' => [1]],
        ]);
    }
}
SELECT
    SUM(pay_money)                       as pay_money,
    COUNT(1)                             as pay_count,
    GROUP_CONCAT(distinct newlogin_time) as newlogin_time,
    IF(SUM(is_exception) > 0, 1, 0)      as is_exception,
    `main_account`,
    `core_account`
FROM (
SELECT
     `cp_game_id`,
     `game_id`,
     `package_id`,
     `campaign_id`,
     `creative_id`,
     COALESCE(if(COALESCE(plan_id, 0) != 0, plan_id, if(COALESCE(creative_id, 0) != 0, plan_id, source_svkey)), 0) as plan_id,
     `main_account`,
     COALESCE(if(power_channel_id NOT IN (6568, 6822, 5329), power_channel_id, if(channel_id !=0, channel_id, power_channel_id)),0) as channel_id,
     COALESCE(if(power_channel_id NOT IN (6568, 6822, 5329), power_channel_main_id, channel_main_id)) as channel_main_id,
     `platform_id`,
     `app_show_id`,
     `promotion_id`,
    `pay_money`,
    `newlogin_time`,
    COALESCE(if(power_channel_id NOT IN (6568, 6822, 5329), power_user_id, if(COALESCE(ad_user_id, 0) !=0, ad_user_id,IF(COALESCE(svlink_user, 0) != 0,svlink_user, power_user_id))),0) as user_id,
    COALESCE(if(power_channel_id NOT IN (6568, 6822, 5329), power_department_id, if(COALESCE(ad_department_id, 0) != 0, ad_department_id,IF(COALESCE(svlink_user, 0) != 0, svlink_department, power_department_id))),0) as department_id,
    `creative_name`,
    COALESCE(IF(COALESCE(plan_id, 0) != 0, plan_name, IF(COALESCE(creative_id, 0) != 0, plan_name, svlink_name)),'') AS plan_name,
    `campaign_name`,
    `is_exception`,
    `account_id`,
    `account_name`,
    `core_account`
FROM (
    SELECT
    `source_game`.`cp_game_id`                                         AS `cp_game_id`,
    `source_game`.`game_id`                                            AS `game_id`,
    `source_game`.`package_id`                                         AS `package_id`,
    `source_game`.`campaign_id`                                        AS `campaign_id`,
    `source_game`.`creative_id`                                        AS `creative_id`,
    IF(source_channel.IS_USE_SV_KEY = 1, source_game.sv_key, source_game.plan_id) as plan_id, `source_game`.`main_account` AS `main_account`,
    IF(POWER.channel_id NOT IN (6568, 5327, 5329, 6447, 6822), POWER.channel_id,IF(source_game.channel_id != 0, IF(source_game.channel_id = 1013, 4, source_game.channel_id), POWER.channel_id)) AS channel_id,
    COALESCE(IF(POWER.channel_id IN (6568, 5327, 5329, 6447, 6822),
    IF(source_channel.channel_main_id != 0, source_channel.channel_main_id,power.channel_main_id), power.channel_main_id), 0)  AS channel_main_id,
    `power`.`platform_id`                                              AS `platform_id`,
    `power`.`app_show_id`                                              AS `app_show_id`,
    `power`.`popularize_v2_id`                                         AS
    `promotion_id`,
    `user_payment`.`money`                                             AS `pay_money`,
    `source_game`.`newlogin_time`                                      AS `newlogin_time`,
    `power`.`channel_id`                                               AS `power_channel_id`,
    `power`.`channel_main_id`                                          AS
    `power_channel_main_id`,
    `source_game`.`sv_key`                                             AS `source_svkey`,
    `power`.`ad_user_id`                                               AS `power_user_id`,
    `power`.`ad_department_id`                                         AS `power_department_id`,
    `ad_admin`.`id`                                                    AS
    `ad_user_id`,
    `ad_admin`.`department_id`                                         AS `ad_department_id`,
    `campaign_name`,
    `creative_name`,
    `plan_name`,
    `svlink`.`aid`                                                     AS `svlink_name`,
    `base_account`.`account_id`                                        AS
    `account_id`,
    `adp_oauth`.`advertiser_name`                                      AS `account_name`,
    IF(svlink.user_id = 0 or svlink.user_id is null, svlink_plan.user_id, svlink.user_id)   as svlink_user,IF(svlink_admin.department_id = 0 or svlink_admin.department_id is null,svlink_plan_admin.department_id,svlink_admin.department_id) as svlink_department,
    COALESCE(IF(source_profile.BUY_GIFT_PACK_IN_NEW_2DAYS = 1 or source_profile.CREATE_ROLE_PAY_IN_5_MIN = 1, 1, 0), 0) as is_exception,
    `core_account`
FROM `ddc_platform`.`dwd_sdk_user_payment` AS `user_payment`
    LEFT JOIN `ddc_platform`.`dwd_sdk_adsource_game` AS `source_game`
        ON `user_payment`.`source_id` = `source_game`.`source_id`
    LEFT JOIN `ddc_platform`.`dwd_sdk_ad_source_game_user_profile` AS `source_profile`
        ON `source_profile`.`source_id` = `user_payment`.`source_id`
    INNER JOIN {$power_sql}
        ON `source_game`.`package_id` = `power`.`package_id`
    LEFT JOIN `base_conf_platform`.`tb_base_channel_conf` AS `source_channel`
        ON `source_channel`.`channel_id` = `source_game`.`channel_id`
    LEFT JOIN `adp_platform`.`tb_adp_campaign` AS `campaign_base`
        ON `source_game`.`campaign_id` = `campaign_base`.`campaign_id`
LEFT JOIN `adp_platform`.`tb_adp_plan_base` AS `plan_base`
    ON `source_game`.`plan_id` = `plan_base`.`plan_id`
LEFT JOIN `dataspy`.`admin_user` AS `ad_admin`
    ON `plan_base`.`user_id` = `ad_admin`.`id`
LEFT JOIN `adp_platform`.`tb_adp_creative_base` AS `creative_base`
    ON `source_game`.`channel_id` = `creative_base`.`channel_id` AND `source_game`.`plan_id` = `creative_base`.`plan_id` AND `source_game`.`creative_id` = `creative_base`.`creative_id`
LEFT JOIN `dataspy`.`tb_ad_svlink_conf` AS `svlink`
    ON `source_game`.`sv_key` = `svlink`.`id`
LEFT JOIN `dataspy`.`admin_user` AS `svlink_admin`
    ON `svlink`.`user_id` = `svlink_admin`.`id`
LEFT JOIN `dataspy`.`tb_ad_svlink_conf` AS `svlink_plan`
    ON `source_game`.`plan_id` = `svlink_plan`.`id`
LEFT JOIN `dataspy`.`admin_user` AS `svlink_plan_admin`
    ON `svlink_plan`.`user_id` = `svlink_plan_admin`.`id`
LEFT JOIN `base_conf_platform`.`tb_ad_account_conf` AS `base_account`
    ON (`base_account`.`channel_id` = `campaign_base`.`channel_id` AND `base_account`.`account_id` =`campaign_base`.`advertiser_id`)
LEFT JOIN `adp_platform`.`tb_adp_oauth` AS `adp_oauth`
    ON (`adp_oauth`.`channel_id` = `campaign_base`.`channel_id` AND `adp_oauth`.`advertiser_id` = `campaign_base`.`advertiser_id`)

{if count($params) gt 0}
    where pay_result = 1
    {* 付费时间 *}
    {if !empty($params['pay_range'])}
        and `pay_time` between '{$params['pay_range'][0]} 00:00:00' and '{$params['pay_range'][1]} 23:59:59'
    {/if}
    {* 包号 *}
    {if !empty($params['package_id'])}
        and source_game.`package_id` IN ({$params['package_id']})
    {/if}
    {* 游戏原名 *}
    {if !empty($params['cp_game_id'])}
        and power.cp_game_id IN ({$params['cp_game_id']})
    {/if}
    {* 游戏统计名 *}
    {if !empty($params['game_id'])}
        and power.game_id IN ({$parms['game_id']})
    {/if}
    {* 包号标签 *}
    {if !empty($params['package_id_tags'])}
        and source.package_id IN (
            select distinct data_id from base_conf_platform.biz_tags where table_name = 'packages' and tag_id IN ({$params['package_id_tags']})
        )
    {/if}
    {* 创意id *}
    {if !empty($params['creative_id'])}
        and source_game.creative_id IN ({$params['creative_id']})
    {/if}
    {* 广告组ID *}
    {if !empty($params['campaign_id'])}
        and source_game.campaign_id IN ({$params['campaign_id']})
    {/if}
    {* 广告组名称 *}
    {if !empty($params['campaign_name'])}
        and source_game.campaign_id IN (
            select distinct campaign_id from adp_platform.tb_adp_campaign where campaign_name like '%{$params['campaign_name']}%'
        )
    {/if}
    {* 客户端ID *}
    {if !empty($params['platform_id'])}
        and power.platform_id IN ({$params['platform_id']})
    {/if}
    {* 是否含自然量 *}
    {if isset($params['is_has_natural']) && $params['is_has_natural'] eq 0}
        and source.plan_id > 0
    {/if}
    {* 推广分类 *}
    {if !empty($params['promotion_id'])}
        and power.popularize_v2_id IN ({$params['promotion_id']})
    {/if}
    {* 是否限定为新增付费 *}
    {if !empty($params['target'])}
        {if $params['target'] eq 'pay_money_new'}
            (DATE(user_payment.pay_time) = DATE(source_game.newlogin_time))
        {/if}
    {/if}

    {if !empty($params['main_account'])}
        and source_game.main_account = '{$params['main_account']}'
    {/if}

    {* data_scope *}
    {* marketing_goal *}
{/if}
) as wrap) as main_body

{if count($params) gt 0}
where true
    {* 推广子渠道搜索 *}
    {if !empty($params['channel_id'])}
        and channel_id IN ({$params['channel_id']})
    {/if}
    {* 推广子渠道标签筛选 *}
    {if !empty($params['channel_id_tags'])}
        and channel_id IN (
            select distinct data_id from base_conf_platform.biz_tags where table_name = 'app_channel' and tag_id IN ({$params['channel_id_tags']})
        )
    {/if}
    {* 主渠道 *}
    {if !empty($params['channel_main_id'])}
        and channel_main_id IN ({$params['channel_main_id']})
    {/if}
    {* 主渠道标签 *}
    {if !empty($params['channel_main_id_tags'])}
        and channel_main_id IN (
            select distinct data_id from base_conf_platform.biz_tags where table_name = 'package_channel_main' and tag_id IN ({$params['channel_main_id_tags']})
        )
    {/if}
    {* 计划ID搜索 *}
    {if !empty($params['plan_id'])}
        and plan_id IN ({$params['plan_id']})
    {/if}
    {* 计划名称搜索 *}
    {if !empty($params['plan_name'])}
        {* 计划名搜索根据搜索时间名前后为一年时间缩小范围以提升速度 *}
        and plan_id IN (
            select plan_id from adp_platform.tb_adp_plan_base where plan_name like '%{$params['plan_name']}%' and AD_CREATE_TIME between '{$ad_range_start} 00:00:00' and '{$ad_range_end} 23:59:59'
        union
            select id from dataspy.tb_ad_svlink_conf where aid like '%{$params['plan_name']}%' and ADD_TIME between '{$ad_range_start} 00:00:00' and '{$ad_range_end} 23:59:59'
        )
    {/if}
    {*  *}
    {if !empty($params['account_id'])}
        and account_id IN ({$params['account_id']})
    {/if}

    {if !empty($params['user_id'])}
        and user_id IN ({$params['user_id']})
    {/if}

    {if !empty($params['department_id'])}
        and department_id IN ({$params['department_id']})
    {/if}

{/if}

{if $group_by|isset}
    GROUP BY {$group_by}
{/if}

ORDER BY `pay_money` DESC

{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}
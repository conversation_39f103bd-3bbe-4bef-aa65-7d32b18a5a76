<?php
declare(ticks=1);

namespace app\ad_upload\migrate;

use app\ad_upload\tool\CustomProcess;

/**
 * 支付日志上报点迁移 回滚
 * <AUTHOR>
 */
class PayBack extends CustomProcess
{
    /**
     * 渠道id
     * @var int
     */
    public $channel_id = 0;

    /**
     * 将id 改成时间戳
     * @return void
     */
    public function run()
    {
        if (empty($this->channel_id)) {
            echo "渠道ID不能为空\n";
            return;
        }
        $data = \Plus::$app->dataspy->find(
            'tb_ad_upload_log',
            ['channel_id' => $this->channel_id, 'action' => 'pay']
        );
        if (empty($data)) {
            echo "没有数据\n";
            return;
        }
        $id = \Plus::$app->origin_platform->get(
            'tb_sdk_user_payment',
            'id',
            ['pay_time' => date('Y-m-d H:i:s', $data['last_action_id'])]
        );
        if (empty($id)) {
            echo "没有id\n";
            return;
        }
        echo "id: $id\n";

        \Plus::$app->dataspy->update(
            'tb_ad_upload_log',
            ['last_action_id' => $id],
            ['id' => $data['id']]
        );
        echo "修改成功\n";
    }
}

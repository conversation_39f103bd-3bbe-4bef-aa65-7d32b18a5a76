<?php

namespace app\extension\TableAssistant\Helpers;

class ColumChanger
{
    /**
     * @param array $fixedCols
     * @param array $calcCols
     * @param bool  $isSummaryRow
     * @param bool  $notNeedAggregate
     *
     * @return void
     */
    public static function changeArrayForCols(
        array $fixedCols = [], array $calcCols = [], bool $isSummaryRow = false, bool $notNeedAggregate = false
    ): array
    {
        $result  = [];
        $collect = collect();

        $isSummaryRow ?: $collect = $collect->merge($fixedCols);
        $collect = $collect->merge($calcCols);

        $collect->each(function (&$item, $key) use (&$result, $notNeedAggregate) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];

                static::isCanAddIfNullAggregate($aggregate)
                    ? $format = "IFNULL({$aggregate}(%s), 0)"
                    : $format = "{$aggregate}(%s)";
            } elseif (isset($item['custom'])) {
                $format = "{$item['custom']}";
            } else {
                $format = '%s';
            }

            if ($notNeedAggregate) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            !isset($item['source']) ?: $field .= $item['source'] . '.';

            $field    .= $item['source_field'] ?? $key;
            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;

    }

    /**
     * 需添加if null的聚合方法
     *
     * @param $aggregate
     *
     * @return bool
     */
    public static function isCanAddIfNullAggregate($aggregate): bool
    {
        return in_array($aggregate, ['sum', 'avg']);
    }

    /**
     * 长线ltv,roi，留存等，查询月表减去没到时间的数据
     *
     * @param array $calcIndex 数据字段
     * @param int $days 减去多余日期 一般ltv 1天，留存2天
     * @param string $endDate 查询月份结束日期
     * @return void|array
     */
    public static function ltvRoiLoginMonth($calcIndex, $days = 1, $endDate = '')
    {
        // 后续能用 $scheme->joinMonthDataFilterDailyData() 判断追加left join对应的日报表
        // 格式化查询结束日期
        $endDate = $endDate ? : date('Y-m-d');
        if (is_numeric($endDate) && strlen($endDate) == 6) { // 6位202301格式凭借日期方便转化判断月份
            $endDate .= '01';
        }

        // 判断查询月份时候包含最新的一个月, 包号则需要连表减去最新$days的数据 ltv 1天、留存 2天
        if (date('m') == date('m', strtotime($endDate))) {
            // 日报表和月报表字段差
            /* $dailyFieldMap = [
                'firstlogin_active_user_month' => 'firstlogin_active_user',
                'active_user_month'            => 'active_user',
                'pay_user_month'               => 'pay_user',
            ]; */
            foreach ($calcIndex as $key => $value) {
                // 跳过不用计算的字段
                if (strtolower(($value['aggregate']) != 'sum')) continue;

                // 获取表字段 $fields月报表  $fields1 日报表
                $fields = $fields1 = $value['source_field'] ?? $key;
                // $fields1 = $dailyFieldMap[$fields1] ?? $fields1;
                // 日报表字段 默认替换所有日报表月字段
                $fields1 = str_replace('_month', '', $fields1);

                // 自定义字段计算
                $cumtom = '';
                $sum = "IFNULL(SUM({$value['source']}.`{$fields}`), 0)";
                $sum1 = "IFNULL(SUM({$value['source']}1.`{$fields1}`), 0)";
                $sum2 = "IFNULL(SUM({$value['source']}2.`{$fields1}`), 0)";
                // 根据过滤最新1天或2天计算相减, {source}1 为最新1天的连表 {source}2 为倒数第二天的连表
                if ($days == 1 ) {
                    $cumtom = "IFNULL({$sum} - {$sum1} , 0)";
                } elseif ($days == 2) {
                    $cumtom = "IFNULL({$sum} - {$sum1} - {$sum2} , 0)";
                }

                if ($cumtom) {
                    $calcIndex[$key] = ['custom' => $cumtom];
                }
            }
        }
        return $calcIndex;
    }

}
<?php

namespace app\apps\logs\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\log\PaymentVirtualLogic;
use app\service\General\GeneralOptionServ;
use app\service\Logs\PaymentVirtualServ;

class PaymentVirtualUploadController extends BaseTableController
{
    /**
     * @param Collection $params
     * @return array
     */
    protected function data(Collection $params): array
    {
        return (new PaymentVirtualLogic())->getList($params->toArray());
    }

    /**
     * @param Collection $params
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '规则ID', 'dataIndex' => 'rule_id', 'classify' => ['attrs', 'base']],
            ['title' => '规则名称', 'dataIndex' => 'rule_name', 'classify' => ['attrs', 'base']],
            ['title' => '推广子渠道', 'dataIndex' => 'channel_id', 'classify' => ['attrs', 'base']],
            ['title' => '上报方式', 'dataIndex' => 'upload_type', 'classify' => ['attrs', 'base']],
            ['title' => '核心账号', 'dataIndex' => 'core_account', 'classify' => ['attrs', 'base'], 'sorter' => true],
            ['title' => '包号', 'dataIndex' => 'package_id', 'classify' => ['attrs', 'base'], 'sorter' => true],
            ['title' => '上报金额', 'dataIndex' => 'money', 'classify' => ['attrs', 'base'], 'sorter' => true],
            ['title' => '上报结果', 'dataIndex' => 'upload_status', 'classify' => ['attrs', 'base'], 'sorter' => true],
            ['title' => '插入时间', 'dataIndex' => 'create_time', 'classify' => ['attrs', 'base'], 'sorter' => true],
            ['title' => '实际上报时间', 'dataIndex' => 'log_report_time', 'classify' => ['attrs', 'base'], 'sorter' => true],
            ['title' => '上报时间段(开始)', 'dataIndex' => 'upload_time', 'classify' => ['attrs', 'base'], 'sorter' => true],
            ['title' => '上报时间段(截止)', 'dataIndex' => 'upload_expire_time', 'classify' => ['attrs', 'base'], 'sorter' => true],
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '基础数据',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                ],
            ],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * 新增虚拟上报订单
     * @param $request
     * @return array
     * @throws \Throwable
     */
    public function saveAction($request): array
    {
        if (empty($request)) {
            return $this->error('参数格式错误');
        }

        $packages    = array_column($request, 'package_id');
        $packageList = (new GeneralOptionServ())->listPackageInfo($packages);
        $list        = $request;

        foreach ($list as $i => &$item) {
            if (!isset($item['package_id'])) {
                unset($list[$i]);
                continue;
            }

            $package = $item['package_id'];

            if (!isset($item['channel_id'])) {
                $item['channel_id'] = 0;
            }

            if (isset($packageList[$package])) {
                $chill              = $packageList[$package];
                $item['cp_game_id'] = $chill['cp_game_id'] ?? 0;
                $item['game_id']    = $chill['game_id'] ?? 0;
            }

            $uploadType = $item['upload_type'] ?? 0;
            if ($uploadType == 2) {
                $rangeBegin    = strtotime($item['upload_time']);
                $rangeEnd      = strtotime($item['upload_expire_time']);
                $randTimeStamp = rand($rangeBegin, $rangeEnd);
                // 开始时间在时间范围内随机一个数
                $item['upload_time'] = date('Y-m-d H:i:s', $randTimeStamp);
            }
        }

        try {
            $result = (new PaymentVirtualServ())->batchInsert($list);

            return $this->success(['result' => $result]);
        }
        catch (\Exception $e) {
            return $this->error('保存失败', ['code' => $e->getMessage()]);
        }
    }

}
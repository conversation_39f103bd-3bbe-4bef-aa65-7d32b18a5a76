with creative_ocean_table as (
select
    `tday`,
    `channel_id`,
    `package_id`,
    IF(null_or_empty(campaign_id) = 1 or campaign_id = '0', '', campaign_id) as campaign_id,
    IF(null_or_empty(plan_id) = 1 or plan_id = '0', '', plan_id) as plan_id,
    IF(null_or_empty(creative_id) = 1 or creative_id = '0', '', creative_id) as creative_id,
    `cp_game_id`,
    `game_id`,
    `main_channel_id`,
    `account_id`,
    `show`,
    `click`,
    `download`,
    `activate`,
    `convert`,
    `install`,
    `lp_view`,
    `lp_download`,
    `download_start`,
    `register`,
    `cost`,
    `cost_discount`,
    new_real_user,
    new_user,
    new_user_emulator,
    activate_device,
    create_role_new,
    pay_new_user_7days,
    pay_frequency_7days,
    online_time,
    first_online_time,
    active_user,
    active_user_week,
    total_play,
    play_time_per_play,
    play_duration_3s,
    `user_id`,
    is_ad_data,
    is_appointment,
    marketing_goal,
    pay_user,
    pay_user_list,
    pay_money,
    pay_count,
    pay_user_new,
    pay_user_new_list,
    pay_money_new,
    pay_count_new,
    pay_money_no_visual,
    pay_money_new_no_visual,
    `update_time`
from
    bigdata_dws.dws_ad_creative_daily
{if !empty($params)}
    {assign var="first_mark" value=1}
    {foreach $params as $kk => $foo}
        {if $kk eq "range_date"}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            tday between '{$foo[0]}' and '{$foo[1]}' {continue}
        {/if}
        {if $kk eq 'cp_game_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                cp_game_id in ({$foo|join:','})
            {else}
                cp_game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                game_id in ({$foo|join:','})
            {else}
                game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id_tags'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            game_id in (
                select distinct data_id from base_conf_platform.biz_tags
                    where tag_id in ({$foo|join:','}) and table_name = 'games'
            )
            {continue}
        {/if}
        {if $kk eq 'package_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                package_id in ({$foo|join:','})
            {else}
                package_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'package_id_tags'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            package_id in (
                select distinct data_id from base_conf_platform.biz_tags
                    where tag_id in ({$foo|join:','}) and table_name = 'packages'
            )
            {continue}
        {/if}
        {if $kk eq 'account_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            account_id like  '{'%'|cat:$foo|cat:'%'}'
            {continue}
        {/if}
        {if $kk eq 'campaign_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                campaign_id in ({$foo|join:','})
            {else}
                campaign_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'campaign_name'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            campaign_id in (select distinct campaign_id from adp_platform.tb_adp_campaign where campaign_name like '{'%'|cat:$foo|cat:'%'}')
            {continue}
        {/if}
        {if $kk eq 'plan_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                plan_id in ({$foo|join:','})
            {else}
                plan_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'plan_name'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            (
            plan_id in (select distinct plan_id from adp_platform.tb_adp_plan_base where plan_name like '{'%'|cat:$foo|cat:'%'}')
            or plan_id in (select distinct id  from dataspy.tb_ad_svlink_conf where aid like '{'%'|cat:$foo|cat:'%'}')
            )
            {continue}
        {/if}
        {if $kk eq 'creative_id'}
            {if !$first_mark} and {else} where {$first_mark=0} {/if}
            {if is_array($foo)}
                creative_id in ({$foo|join:','})
            {else}
                creative_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'data_scope'}
            {if $foo eq 1}
                {if !$first_mark} and {else} where {$first_mark=0} {/if} is_ad_data = 1
            {elseif $foo eq 2}
                {if !$first_mark} and {else} where {$first_mark=0} {/if} is_ad_data = 0
            {/if}
            {continue}
        {/if}
        {if $kk eq 'marketing_goal'}
            {if $foo != [1,2] || $foo != [2,1]}
                {if in_array(1, $foo)}
                    {if !$first_mark} and {else} where {$first_mark=0} {/if} marketing_goal != 2
                {/if}
                {if in_array(2, $foo)}
                    {if !$first_mark} and {else} where {$first_mark=0} {/if} marketing_goal = 2
                {/if}
            {/if}
            {continue}
        {/if}
        {if $kk eq 'is_has_appointment'}
            {if empty($foo)} {if !$first_mark} and {else} where {$first_mark=0} {/if} is_appointment != 1 {/if}
            {continue}
        {/if}
        {if $kk eq 'is_has_natural'}
            {if empty($foo)} {if !$first_mark} and {else} where {$first_mark=0} {/if} channel_id > 0 {/if}
            {continue}
        {/if}
    {/foreach}
{/if}
),
dashboard_info as (

    select
        b1.tday,
        DATE_FORMAT(b1.tday,'%Y-%m') as tmonth,
        b1.cp_game_id,
        b1.game_id,
        b1.package_id,
        b1.campaign_id,
        any(coalesce(b2.campaign_name, '')) as campaign_name,
        b1.plan_id,
        b1.creative_id,
        b1.main_channel_id,
        b1.account_id,
        b1.ad_account,
        any(b1.platform_id) as platform_id,
        any(b1.account_name) as account_name,
        any(b1.is_ad_data) as is_ad_data,
        any(b1.is_appointment) as is_appointment,
        any(b1.marketing_goal) as marketing_goal,
        any(case when user_os = '["ANDROID"]' then 'ANDROID' when user_os = '["IOS"]' then 'IOS' else '混投' end)  as dim_user_os,
        any(b1.popularize_v2_id) as promotion_id,
        any(b1.app_show_id) as app_show_id,
        b1.promotion_channel_id,
        b1.dim_user_id
        {if empty($base_only_dimension)}
            ,sum(`show`)  as show_cnt,
            sum(click)   as click_cnt,
            sum(download) as download_cnt,
            sum(activate) as activate_cnt,
            sum(`convert`) as convert_cnt,
            sum(`install`) as install_cnt,
            sum(lp_view) as lp_view,
            sum(lp_download) as lp_download,
            sum(download_start) as download_start,
            sum(register) as register,
            sum(cost) as cost,
            sum(cost_discount) as cost_discount,
            sum(new_real_user) as new_real_user,
            sum(new_user) as new_user,
            sum(new_user_emulator) as new_user_emulator,
            sum(activate_device) as activate_device,
            sum(create_role_new) as create_role_new,
            sum(pay_new_user_7days) as pay_new_user_7days,
            sum(pay_frequency_7days) as pay_frequency_7days,
            sum(online_time) as online_time,
            sum(first_online_time) as first_online_time,
            sum(active_user) as active_user,
            sum(active_user_week) as active_user_week,
            sum(total_play) as total_play,
            sum(play_time_per_play) as play_time_per_play,
            sum(play_duration_3s) as play_duration_3s,
            sum(pay_user) as pay_user,
            sum(pay_money) as pay_money,
            sum(pay_count)  as pay_count,
            sum(pay_user_new) as pay_user_new,
            sum(pay_money_new) as pay_money_new,
            sum(pay_count_new) as pay_count_new,
            sum(pay_money_no_visual) as pay_money_no_visual,
            sum(pay_money_new_no_visual) as pay_money_new_no_visual,
            max(b1.update_time) as update_time
        {/if}
    from
        adp_platform.tb_adp_campaign b2
    right join (
    select
        {if isset($ad_channels)}
            COALESCE( IF(power.channel_id not in ({$ad_channels|join:','}), power.channel_id, IF(a1.channel_id != 0, IF(a1.channel_id =1013, 4, a1.channel_id), power.channel_id) ) ,0) as promotion_channel_id,
            COALESCE( IF(power.channel_id not in ({$ad_channels|join:','}), power.ad_user_id, IF(a1.user_id != 0, a1.user_id, power.ad_user_id)),0 ) as dim_user_id,
        {else}
            a1.channel_id as promotion_channel_id,
            a1.user_id as dim_user_id,
        {/if}
        a1.*, a2.ad_account,
        a3.advertiser_name as account_name,
        power.platform_id,
        power.popularize_v2_id,
        power.app_show_id
        from creative_ocean_table a1
        {if !empty($power_join_sql) && $power_join_sql == 'base_conf_platform.tb_package_detail_conf'}
            join ({$power_join_sql}) power on a1.package_id = power.package_id
        {else}
            join base_conf_platform.tb_package_detail_conf power on a1.package_id = power.package_id
        {/if}
        left join base_conf_platform.tb_ad_account_conf a2 on a1.account_id = a2.account_id
        left join adp_platform.tb_adp_oauth a3 on a1.account_id = a3.advertiser_id and a1.main_channel_id = a3.channel_id
    {if !empty($params)}
        {assign var="first_mark_1" value=1}
        {foreach $params as $kk => $foo}
            {if $kk eq 'ad_account'}
                {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
                a2.ad_account like '{'%'|cat:$foo|cat:'%'}'
                {continue}
            {/if}
            {if $kk eq 'app_show_id'}
                {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
                {if is_array($foo)}
                    power.app_show_id in ({$foo|join:','})
                {else}
                    power.app_show_id = '{$foo}'
                {/if}
                {continue}
            {/if}
            {if $kk eq 'platform_id'}
                {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
                {if is_array($foo)}
                    power.platform_id in ({$foo|join:','})
                {else}
                    power.platform_id = '{$foo}'
                {/if}
                {continue}
            {/if}
            {if $kk eq 'promotion_id'}
                {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
                {if is_array($foo)}
                    power.popularize_v2_id in ({$foo|join:','})
                {else}
                    power.popularize_v2_id = '{$foo}'
                {/if}
                {continue}
            {/if}
        {/foreach}
    {/if}
    ) b1 on b1.campaign_id = b2.campaign_id and b1.main_channel_id = b2.channel_id
    {if !empty($params)}
        {assign var="first_mark_2" value=1}
        {foreach $params as $key => $foo}
            {if $kk eq 'user_os'}
                {if is_array($foo)}
                    {if !$first_mark_2} and {else} where {$first_mark_2=0} {/if}
                    (
                    {foreach $foo as $ii => $chill}
                        {if !$chill@first} or {/if}
                        {if $chill eq 1} b2.user_os =  '["IOS"]'{/if}
                        {if $chill eq 2} b2.user_os =  '["ANDROID"]'{/if}
                        {if $chill eq 3} (b2.user_os != '["IOS"]' and b2.user_os != '["ANDROID"]'){/if}
                    {/foreach}
                    )
                {/if}
                {continue}
            {/if}
            {if $kk eq 'campaign_name'}
                {if !$first_mark_2} and {else} where {$first_mark_2=0} {/if}
                b2.campaign_name like '{'%'|cat:$foo|cat:'%'}'
            {/if}
        {/foreach}
    {/if}
    group by tday, b1.cp_game_id, b1.game_id, b1.package_id, promotion_channel_id, b1.campaign_id, b1.plan_id,
    b1.creative_id, b1.main_channel_id, b1.account_id, dim_user_id, b1.ad_account
)

{include file="sql/advertise/ad_targets/ad_payment_detail.tpl"}
select
count(1) as total_row
from ddc_platform.dwd_sdk_adsource_game b2
right join payment_detail b1 on b1.source_id = b2.source_id
{if !empty($params)}
    {assign var="tag_first_where" value=1}
    {foreach $params as $key => $foo}
        {if $key eq 'plan_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}

            {if is_array($foo)}
                b2.plan_id in ('{$foo|join:"', '"}')
            {else}
                b2.plan_id = '{$foo}'
            {/if}
        {/if}
        {if $key eq 'package_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                b2.package_id in ('{$foo|join:"', '"}')
            {else}
                b2.package_id = '{$foo}'
            {/if}
        {/if}
    {/foreach}
{/if}
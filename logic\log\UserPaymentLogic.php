<?php

namespace app\logic\log;

use app\extension\Support\Collections\Collection;
use app\service\BusinessPlatform\OptionServ;
use app\service\ConfigService\BasicServ;
use app\service\SourceData\PaymentServ;
use Spiral\Database\Injection\Parameter;

class UserPaymentLogic
{

    /**
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    public function listInfo(Collection $params): array
    {
        $page     = $params->pull('page');
        $pageSize = $params->pull('page_size');
        $sort     = $params->pull('sort', null);

        if (!empty($sort)) {
            $order = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort  = [$sort => $order];
        }
        else {
            $sort = ['t_payment.pay_time' => 'DESC'];
        }

        try {
            $serv   = new PaymentServ();
            $result = $serv->getList($params->toArray(), [], ['page' => $page, 'page_size' => $pageSize], $sort);
        }
        catch (\UnexpectedValueException $ue) {
            return ['list' => [], 'summary' => [], 'total' => 0];
        }

        $list = &$result['list'];

        if (!empty($list)) {
            $constMap        = (new BasicServ())->getMultiOptions(['order_type', 'payway', 'user:all', 'department:all', 'tag_mode', 'order_type']);
            $departmentMap   = array_column($constMap->offsetGet('department:all')->toArray() ?? [], 'val', 'key');
            $userMap         = array_column($constMap->offsetGet('user:all')->toArray() ?? [], 'val', 'key');
            $tagModeMap      = array_column($constMap->offsetGet('tag_mode')->toArray() ?? [], 'val', 'key');
            $orderTypeMap    = array_column($constMap->offsetGet('order_type')->toArray() ?? [], 'val', 'key');
            $coreAccountList = array_column($list, 'core_account');
            // 批量查询登录账号
            $loginAccountList = (new OptionServ())
                ->getAccountInfo([
                    'core_user' => new Parameter($coreAccountList),
                    'status'    => 1,
                ], [
                    'login_account', 'core_user',
                ]);
            $loginAccountMap  = [];

            foreach ($loginAccountList as $foo) {
                $coreUser = $foo['core_user'] ?? null;
                $loginA   = $foo['login_account'] ?? null;

                if (empty($coreUser) || empty($loginA)) continue;
                $coreUser = strtolower($coreUser);

                if (!isset($loginAccountMap[$coreUser])) {
                    $loginAccountMap[$coreUser] = [$loginA];
                }
                else {
                    $loginAccountMap[$coreUser][] = $loginA;
                }
            }

            foreach ($list as &$item) {
                $orderType      = $item['order_type'] ?? null;
                $tagMode        = $item['tag_mode'] ?? null;
                $testDepartment = $item['test_department'] ?? null;
                $tagOperator    = $item['tag_operator_id'] ?? null;

                if (in_array($orderType, ['339', '596', '342', '343', '340', '344', '345', '346', '347'])) {
                    $orderType = 1;
                }

                $item['tagging_operator'] = $userMap[$tagOperator] ?? '';
                $item['test_department']  = $departmentMap[$testDepartment] ?? '';
                $item['tag_mode']         = $tagModeMap[$tagMode] ?? '';
                $item['order_type']       = $orderTypeMap[$orderType] ?? '';
                $item['order_status']     = $item['order_status'] == 0 ? '无效订单' : '有效订单';
                $item['game_back_status'] = $item['game_back_status'] == 0 ? '回调失败' : '回调成功';
                $coreUser                 = $item['core_account'];
                $loginAccount             = implode(',', $loginAccountMap[$coreUser] ?? []);
                $item['login_account']    = $loginAccount;
            }
            unset($item);
        }

        return $result;
    }
}
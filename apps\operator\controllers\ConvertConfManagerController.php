<?php

namespace app\apps\operator\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Collection;
use app\logic\operator\ConvertConfManagerLogic;
use app\service\ConfigService\BasicServ;

/**
 * @description  转端配置管理
 *
 */
class ConvertConfManagerController extends BaseTableController
{
    /**
     * @param Collection $params
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $page     = $params->get('page', 1);
        $pageSize = $params->get('page_size', 100);
        $db       = FakeDB::connection('base_conf_platform');
        $qb       = $db
            ->table('base_conf_platform.tb_convert_platform_conf')
            ->select()
            ->columns([
                'id as id',
                'cp_game_id as cp_game_id',
                'game_id as game_id',
                'android_packages as android_packages',
                'ios_packages as ios_packages',
            ])
            ->orderBy('id', 'DESC');

        $notPageQb = clone $qb;

        $total     = $notPageQb->count();
        $qb        = $qb->limit($pageSize)->offset(($page - 1) * $pageSize);
        $data      = $qb->fetchAll();
        $constMap  = (new BasicServ())->getMultiOptions(['cp_game_id:all', 'game_id']);
        $cpGameMap = array_column($constMap->get('cp_game_id')->toArray(), 'val', 'key');
        $gameMap   = array_column($constMap->get('game_id')->toArray(), 'val', 'key');

        foreach ($data as &$item) {
            $item['cp_game_name']       = $cpGameMap[$item['cp_game_id']] ?? '';
            $item['game_name']          = $gameMap[$item['game_id']] ?? '';
            $item['android_packages'] = json_decode($item['android_packages'], true);
            $item['ios_packages']     = json_decode($item['ios_packages'], true);
        }

        return [
            'list'  => $data,
            'total' => $total
        ];
    }

    protected function fields(Collection $params): array
    {
        return [
            'fields' => [
                ['title' => '游戏原名', 'dataIndex' => 'cp_game_name'],
                ['title' => '游戏统计名', 'dataIndex' => 'game_name'],
                ['title' => '安卓转端包', 'dataIndex' => 'android_packages'],
                ['title' => 'IOS转端包', 'dataIndex' => 'ios_packages'],
            ]
        ];
    }

    /**
     * @return array|void
     */
    public function saveAction()
    {
        $body    = \Plus::$app->request->getRawBody();
        $request = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $logic = new ConvertConfManagerLogic();
        try {
            $logic->save($request);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

}
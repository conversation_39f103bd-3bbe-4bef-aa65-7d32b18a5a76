<?php

namespace app\service\Logs\Components\Matcher;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\General\Helpers\BigDataDwdTable;
use app\util\Common;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class UserPaymentMatch extends BaseLogMatch
{
    /**
     * @return array
     * @throws \Exception
     */
    protected function matchFnList(): array
    {
        $new = [
            'game_server' => $this->matchGameServer(),
            'role_id'     => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'role_name'   => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::likeBuild($qb, $key, $value);
            },
        ];

        return array_merge(parent::matchFnList(), $new);
    }

    protected function matchDt(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value) {
            if (is_array($value) && count($value) > 1) {
                [$start, $end] = $value;

                $start .= ' 00:00:00';
                $end   .= ' 23:59:59';

                $qb->where($key, 'between', $start, $end);
            }
            else {
                if (is_array($value)) {
                    $value = $value[0];
                }

                $qb->where($key, $value);
            }
        };
    }

    /**
     * @throws \Exception
     */
    protected function matchGameServer(): \Closure
    {
        $db = $this->getConn();
        return function (SelectQuery &$qb, $key, $value) use ($db) {
            $subQb = $db->select()
                ->from(BigDataDwdTable::DwdSdkRoleLogin)
                ->where('game_server', new Parameter(Arr::wrap($value)))
                ->columns(['core_account'])->distinct();

            $qb->where('core_account', 'IN', $subQb);
        };
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     * @throws \Exception
     */
    protected function getConn()
    {
        if ($dorisIndex = Common::pingDorisIndex()) {
            return FakeDB::connection($dorisIndex);
        }
        else {
            throw new \RuntimeException('与数据库连接断开');
        }
    }
}
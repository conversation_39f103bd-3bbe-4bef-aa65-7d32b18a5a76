with dash_detail as (
    {* 整合时间维度详情数据 *}
    select
        {if $time_range_type == 3}
            case
            {foreach $range_days as $dd}
                when a1.tday between '{$dd['begin_date']}' and '{$dd['end_date']}' then '{$dd['begin_date']|cat:'/'|cat:$dd['end_date']}'
            {/foreach}
            end as tday,
        {else}
            tday,
        {/if}
        power.cp_game_id,
        power.game_id,
        power.app_show_id,
        power.channel_main_id,
        power.channel_id,
        power.platform_id,
        a1.package_id,
        power.popularize_v2_id as promotion_id,
        power.ad_department_id as department_id,
        power.ad_user_id as user_id,
        a1.is_multiple,
        a1.activate_device,
        a1.cost,
        a1.cost_discount,
        a1.firstlogin_user,
        a1.firstlogin_device,
        {if $time_range_type == 3}
            case
                {foreach $range_days as $dd}
                    when a1.tday = '{$dd['begin_date']}' then a1.firstlogin_active_user_week
                {/foreach}
            else 0
            end as firstlogin_active_user,
            case
            {foreach $range_days as $dd}
                when a1.tday = '{$dd['begin_date']}' then a1.firstlogin_pay_user_week
            {/foreach}
            else 0
            end as firstlogin_pay_user,
            case
            {foreach $range_days as $dd}
                when a1.tday = '{$dd['begin_date']}' then a1.pay_user_week
            {/foreach}
            else 0
            end as pay_user,
        {else}
            a1.firstlogin_active_user,
            a1.firstlogin_pay_user,
            a1.pay_user,
        {/if}
        a1.firstlogin_pay_user_new,
        a1.firstlogin_role,
        a1.firstlogin_role2,
        a1.firstlogin_real_user,
        a1.active_user_week,
        a1.firstlogin_active_user_7_days_ago,
        a1.firstlogin_active_user_pay,
        a1.active_user_7_days_ago,
        a1.new_user,
        a1.new_device,
        a1.new_real_user,
        a1.new_real_user_der,
        a1.reg_user,
        a1.reg_device,
        a1.new_user_imulator,
        a1.active_user_imulator,
        a1.active_user_imulator_bind,
        a1.create_role,
        a1.pay_user_newlogin,
        a1.pay_money_newlogin,
        a1.pay_money_reg,
        a1.update_time,
        a1.pay_money,
        a1.firstlogin_pay_money_new,
        a1.order_count,
        a1.order_success
from bigdata_dws.dws_package_all_daily a1
    {if !empty($power_join_sql) && $power_join_sql == 'base_conf_platform.tb_package_detail_conf'}
        join ({$power_join_sql}) power on a1.package_id = power.package_id
    {else}
        join base_conf_platform.tb_package_detail_conf power on a1.package_id = power.package_id
    {/if}
{if !empty($params)}
    {assign var="tag_first_where" value=1}
    {foreach $params as $kk => $foo}
        {if $kk eq 'range_date'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            a1.tday between '{$foo[0]}' and '{$foo[1]}'
            {continue}
        {/if}
        {if $kk eq 'cp_game_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.cp_game_id in ({$foo|join:','})
            {else}
                power.cp_game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.game_id in ({$foo|join:','})
            {else}
                power.game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'app_show_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.app_show_id in ({$foo|join:','})
            {else}
                power.app_show_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'package_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                a1.package_id in ({$foo|join:','})
            {else}
                a1.package_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'channel_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.channel_id in ({$foo|join:','})
            {else}
                power.channel_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'channel_main_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.channel_main_id in ({$foo|join:','})
            {else}
                power.channel_main_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'channel_id_tags'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            power.channel_id in (
                select distinct data_id from base_conf_platform.biz_tags
                where tag_id in ({$foo|join:','}) and table_name = 'app_channel'
            )
            {continue}
        {/if}
        {if $kk eq 'package_id_tags'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            a1.package_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in ({$foo|join:','}) and table_name = 'packages'
            )
            {continue}
        {/if}
        {if $kk eq 'channel_main_id_tags'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            power.channel_main_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in ({$foo|join:','}) and table_name = 'package_channel_main'
            )
            {continue}
        {/if}
        {if $kk eq 'game_id_tags'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            power.game_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in ({$foo|join:','}) and table_name = 'games'
            )
            {continue}
        {/if}
        {if $kk eq 'platform_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.platform_id in ({$foo|join:','})
            {else}
                power.platform_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'promotion_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.popularize_v2_id in ({$foo|join:','})
            {else}
                power.popularize_v2_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'department_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.ad_department_id in ({$foo|join:','})
            {else}
                power.ad_department_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'user_id'}
            {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.ad_user_id in ({$foo|join:','})
            {else}
                power.ad_user_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'has_no_cost'}
            {if $foo eq 0}
                {if !$tag_first_where} and {else} where {$tag_first_where=0} {/if}
                a1.cost > 0
            {/if}
        {/if}
    {/foreach}
{/if}
),paid_per_info as (
select tday, package_id, SUM(pay_user_num) AS `pay_user_per`
from (
SELECT tday,
b1.package_id,
row_number() over (partition by tday, b1.package_id order by day_type desc ) as rowz,
pay_user_num
FROM `ddc_platform`.`dws_fristlogin_pay_permeation_daily` b1
left join base_conf_platform.tb_package_detail_conf b2 on b1.package_id = b2.package_id
{if !empty($params)}
    {assign var="tag_first_per_where" value=1}

    {foreach $params as $kk => $foo}
        {if $kk eq 'range_date'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            b1.tday between '{$foo[0]}' and '{$foo[1]}'
            {continue}
        {/if}
        {if $kk eq 'cp_game_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b2.cp_game_id in ({$foo|join:','})
            {else}
                b2.cp_game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b2.game_id in ({$foo|join:','})
            {else}
                b2.game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'app_show_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b2.app_show_id in ({$foo|join:','})
            {else}
                b2.app_show_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'package_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b1.package_id in ({$foo|join:','})
            {else}
                b1.package_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'channel_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b2.channel_id in ({$foo|join:','})
            {else}
                b2.channel_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'channel_main_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b2.channel_main_id in ({$foo|join:','})
            {else}
                b2.channel_main_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'channel_id_tags'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            b2.channel_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in ({$foo|join:','}) and table_name = 'app_channel'
            )
            {continue}
        {/if}
        {if $kk eq 'package_id_tags'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            b2.package_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in ({$foo|join:','}) and table_name = 'packages'
            )
            {continue}
        {/if}
        {if $kk eq 'channel_main_id_tags'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            b2.channel_main_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in ({$foo|join:','}) and table_name = 'package_channel_main'
            )
            {continue}
        {/if}
        {if $kk eq 'game_id_tags'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            b2.game_id in (
            select distinct data_id from base_conf_platform.biz_tags
            where tag_id in ({$foo|join:','}) and table_name = 'games'
            )
            {continue}
        {/if}
        {if $kk eq 'platform_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b2.platform_id in ({$foo|join:','})
            {else}
                b2.platform_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'promotion_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b2.popularize_v2_id in ({$foo|join:','})
            {else}
                b2.popularize_v2_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'department_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b2.ad_department_id in ({$foo|join:','})
            {else}
                b2.ad_department_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'user_id'}
            {if !$tag_first_per_where} and {else} where {$tag_first_per_where=0} {/if}
            {if is_array($foo)}
                b2.ad_user_id in ({$foo|join:','})
            {else}
                b2.ad_user_id = '{$foo}'
            {/if}
            {continue}
        {/if}
    {/foreach}

{/if}
) c1
where rowz = 1
group by tday, package_id
)
select
    {if !empty($groups)}
        {foreach $groups as $item}
            {if $item eq 'tday'} dash_detail.tday,
            {elseif $item eq 'cp_game_id'} cp_game_id,
            {elseif $item eq 'game_id'} game_id,
            {elseif $item eq 'app_show_id'} app_show_id,
            {elseif $item eq 'channel_main_id'} channel_main_id,
            {elseif $item eq 'channel_id'} channel_id,
            {elseif $item eq 'promotion_id'} promotion_id,
            {elseif $item eq 'user_id'} user_id,
            {elseif $item eq 'department_id'} department_id,
            {elseif $item eq 'package_id'} dash_detail.package_id,
            {elseif $item eq 'platform_id'} platform_id,
            {/if}
        {/foreach}
    {/if}
    IFNULL(sum(activate_device), 0)                   AS activate_device,
    IFNULL(sum(cost), 0)                              AS cost,
    IFNULL(sum(cost_discount), 0)                     AS cost_discount,
    IFNULL(sum(firstlogin_user), 0)                   AS firstlogin_user,
    IFNULL(sum(firstlogin_device), 0)                 AS firstlogin_device,
    IFNULL(sum(firstlogin_active_user), 0)            AS firstlogin_active_user,
    IFNULL(sum(firstlogin_pay_user_new), 0)           AS firstlogin_pay_user_new,
    IFNULL(sum(firstlogin_pay_user), 0)               AS firstlogin_pay_user,
    IFNULL(sum(firstlogin_role), 0)                   AS create_role_new,
    IFNULL(sum(firstlogin_role2), 0)                  AS firstlogin_role2,
    IFNULL(sum(firstlogin_real_user), 0)              AS firstlogin_real_user,
    IFNULL(sum(active_user_week), 0)                  AS active_user_week,
    IFNULL(sum(firstlogin_active_user_7_days_ago), 0) AS active_user_7days_ago,
    IFNULL(sum(firstlogin_active_user_pay), 0)        AS active_pay_user,
    IFNULL(sum(active_user_7_days_ago), 0)            AS active_user_7_days_ago,
    IFNULL(sum(new_user), 0)                          AS new_user,
    IFNULL(sum(new_device), 0)                        AS new_device,
    IFNULL(sum(new_real_user), 0)                     AS new_real_user,
    IFNULL(sum(new_real_user_der), 0)                 AS new_real_user_der,
    IFNULL(sum(reg_user), 0)                          AS reg_user,
    IFNULL(sum(reg_device), 0)                        AS reg_device,
    IFNULL(sum(firstlogin_active_user), 0)            AS active_user,
    IFNULL(sum(new_user_imulator), 0)                 AS new_user_imulator,
    IFNULL(sum(active_user_imulator), 0)              AS active_user_imulator,
    IFNULL(sum(active_user_imulator_bind), 0)         AS active_user_imulator_bind,
    IFNULL(sum(create_role), 0)                       AS create_role,
    IFNULL(sum(pay_user_newlogin), 0)                 AS pay_user_newlogin,
    IFNULL(sum(pay_money_newlogin), 0)                AS pay_money_newlogin,
    IFNULL(sum(pay_money_reg), 0)                     AS pay_money_reg,
    max(update_time)                                  AS update_time,
    IFNULL(sum(pay_money), 0)                         AS pay_money,
    IFNULL(sum(pay_user), 0)                          AS pay_user,
    IFNULL(sum(firstlogin_pay_user_new), 0)           AS pay_new_user,
    IFNULL(sum(firstlogin_pay_money_new), 0)          AS pay_money_new,
    IFNULL(sum(order_count), 0)                       AS order_count,
    IFNULL(sum(order_success), 0)                     AS order_success,
    coalesce(round(sum(firstlogin_user)/sum(firstlogin_device) *100, 2),0) as device_conversion_rate,
    coalesce(round(sum(firstlogin_role)/sum(firstlogin_user) *100, 2),0) as create_role_percent,
    coalesce(round(sum(firstlogin_real_user)/sum(firstlogin_user) *100, 2),0) as fistlogin_real_name,
    coalesce(round(sum(firstlogin_pay_user_new)/sum(firstlogin_user) *100, 2),0) as pay_user_new_percent,
    coalesce(round(sum(firstlogin_pay_money_new)/sum(firstlogin_user), 2),0) as arpu_new_user,
    coalesce(round(sum(firstlogin_pay_money_new)/sum(firstlogin_pay_user_new), 2),0) as arppu_new_user,
    coalesce(round(sum(cost_discount)/sum(firstlogin_user), 2),0) as new_user_cost,
    coalesce(round(sum(pay_user)/sum(firstlogin_active_user) * 100, 2),0) as active_pay_percent,
    coalesce(round(sum(pay_money)/sum(firstlogin_active_user), 2),0) as arpu_active,
    coalesce(round(sum(pay_money)/sum(pay_user), 2),0) as arppu_active,
    coalesce(sum(firstlogin_active_user) - sum(firstlogin_user),0) as old_user,
    coalesce(sum(pay_user) - sum(firstlogin_pay_user_new),0) as old_user_pay,
    ROUND(coalesce(sum(pay_money) - sum(firstlogin_pay_money_new),0), 2) as old_user_pay_money,
    coalesce( round((sum(pay_user) - sum(firstlogin_pay_user_new)) / (sum(firstlogin_active_user) - sum(firstlogin_user)) * 100, 2), 0 ) as old_user_pay_percent,
    coalesce( round((sum(pay_money) - sum(firstlogin_pay_money_new)) / (sum(firstlogin_active_user) - sum(firstlogin_user)),2), 0 ) as old_user_arpu,
    coalesce( round( (sum(pay_money) - sum(firstlogin_pay_money_new)) / (sum(pay_user) - sum(firstlogin_pay_user_new)), 2 ), 0 ) as old_user_arppu,
    coalesce( round( (sum(firstlogin_active_user) - sum(firstlogin_user)) / sum(firstlogin_active_user) * 100, 2 ), 0 ) as old_user_percent,
    coalesce( round(sum(firstlogin_active_user_pay) / sum(firstlogin_active_user) * 100,2), 0) as active_pay_user_percent,
    coalesce( round((sum(create_role) - sum(firstlogin_role2)) / sum(create_role) * 100, 2 ), 0) as server_roll_percent,
    IFNULL(sum(pay_user_per), 0) AS pay_user_per,
    coalesce(round(sum(pay_user_per)/ sum(firstlogin_user) * 100, 2), 0) as pay_permeation_percent
from dash_detail left join paid_per_info on dash_detail.tday = paid_per_info.tday and dash_detail.package_id = paid_per_info.package_id
{* section:维度汇总 *}
{if !empty($groups)}
    group by
    {foreach $groups as $item}
        {if $item eq 'tday'} dash_detail.tday{if !$item@last}, {/if}
        {elseif $item eq 'cp_game_id'} cp_game_id{if !$item@last}, {/if}
        {elseif $item eq 'game_id'} game_id{if !$item@last}, {/if}
        {elseif $item eq 'app_show_id'} app_show_id{if !$item@last}, {/if}
        {elseif $item eq 'channel_main_id'} channel_main_id{if !$item@last}, {/if}
        {elseif $item eq 'channel_id'} channel_id{if !$item@last}, {/if}
        {elseif $item eq 'promotion_id'} promotion_id{if !$item@last}, {/if}
        {elseif $item eq 'user_id'} user_id{if !$item@last}, {/if}
        {elseif $item eq 'department_id'} department_id{if !$item@last}, {/if}
        {elseif $item eq 'package_id'} dash_detail.package_id{if !$item@last}, {/if}
        {elseif $item eq 'platform_id'} platform_id{if !$item@last}, {/if}
        {/if}
    {/foreach}
{/if}
{* 排序 *}
{if !empty($sorts)}
    order by
    {foreach $sorts as $ss => $oo}
        {$oo}
        {if !$oo@last}, {/if}
    {/foreach}
{/if}
{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}





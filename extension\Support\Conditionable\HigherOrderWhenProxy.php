<?php

namespace app\extension\Support\Conditionable;

class HigherOrderWhenProxy
{
    /**
     * @var mixed
     */
    protected $target;

    /**
     * @var bool
     */
    protected bool $condition;

    /**
     * new proxy instance
     *
     * @param $target
     * @param $condition
     */
    public function __construct($target, $condition)
    {
        $this->target    = $target;
        $this->condition = $condition;
    }

    /**
     * Proxy accessing an attribute onto the target.
     *
     * @param $key
     * @return mixed
     */
    public function __get($key)
    {
        return $this->condition ? $this->target->{$key} : $this->target;
    }

    /**
     * proxy a method call on target
     *
     * @param $method
     * @param $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        return $this->condition
            ? $this->target->{$method}(...$parameters)
            : $this->target;
    }


}
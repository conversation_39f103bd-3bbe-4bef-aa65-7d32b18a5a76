<?php

namespace app\logic\advertise;

use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\ArrayZoom;
use app\extension\Support\Helpers\ProcessLine;
use app\service\Advertiser\AdLtvProvider;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;

class AdCreativeLtvLogic
{
    use ColumnsInteract;

    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $paginate
     * @return array
     * @throws \Smarty\Exception
     * @throws \Exception
     */
    public function ltvCreativeData(array $params = [], array $groups = [], array $sort = [], array $paginate = []): array
    {
        $isNotReplaceChinese = Arr::pull($params, 'is_not_replace_chinese', false);

        if (
            isset($params['range_date_start'])
            && isset($params['range_date_end'])
            && !isset($params['range_date'])
        ) {
            $params['range_date'] = [
                $params['range_date_start'],
                $params['range_date_end'],
            ];
            sort($params['range_date']);
            unset($params['range_date_start'], $params['range_date_end']);
        }

        $provider  = new AdLtvProvider();
        $ltvResult = $provider->getData($params, $groups, $sort, $paginate, true);

        $formatFn = function (&$chill) {
            $collect = ArrayZoom::filterByPrefixKey($chill, 'ltv_|roi');

            if (!empty($collect)) {
                foreach ($collect as $key => $value) {
                    if (str_contains($key, 'ltv_')) {
                        $chill[$key] = floatval($value);
                    }
                }
            }
        };

        if (!empty($ltvResult['list'])) {
            $list    = &$ltvResult['list'];
            $process = new ProcessLine();
            $process->addProcess($formatFn);

            if (!$isNotReplaceChinese) {
                $replaceFn = $this->replaceColumnDefine((new BasicServ())->getMultiOptions([
                    'platform_id', 'promotion_id', 'department_id', 'user_id', 'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
                ])->put('promotion_channel_id', (new GeneralOptionServ())->listChannelOptions()));

                $process->addProcess($replaceFn);

                [$adInfo, $dimension] = (new RealtimeIndex())->getAdInfoByGroups($list, $groups);

                if ($dimension) {
                    $process->addProcess(function (&$target, $k) use ($adInfo, $dimension) {
                        $fields = [];
                        switch ($dimension) {
                            case "campaign_id":
                                $fields = ["CAMPAIGN_NAME"];
                                break;
                            case "plan_id":
                                $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME"];
                                break;
                            case "creative_id":
                                $fields = ["CAMPAIGN_NAME", "CAMPAIGN_ID", "PLAN_NAME", "PLAN_ID", "CREATIVE_NAME"];
                                break;
                        }
                        foreach ($fields as $field) {
                            $key = $target[$dimension];
                            if ($dimension == "creative_id") {
                                $key = $target["plan_id"] . "_" . $target["creative_id"];
                            }
                            $target[strtolower($field)] = $adInfo[$key][$field] ?? null;
                        }
                    });
                }
            }

            $process->run($list);
        }

        if (!empty($ltvResult['summary'])) {
            $summaryRow = &$ltvResult['summary'];
            $formatFn($summaryRow);
        }

        return $ltvResult;
    }

}
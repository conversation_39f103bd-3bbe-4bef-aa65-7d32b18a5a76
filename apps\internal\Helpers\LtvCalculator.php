<?php

namespace app\apps\internal\Helpers;

use app\extension\Support\Collections\Arr;

/**
 * @LtvCalculator  LTV数据计算逻辑
 */
class LtvCalculator
{
    /**
     * @param \Closure $getUserFunc
     * @param array $options
     * @param string $field
     *
     * @return \Closure
     */
    public static function calcEachRow(
        \Closure $getUserFunc, array $options = [], string $field = 'money_all'
    ): \Closure
    {
        $ltvType = $options['ltv_type'] ?? 0;
        $maxDays = $options['max_days'] ?? 0;

        return function (&$target) use (
            $getUserFunc, $ltvType, $field, $maxDays
        ) {
            $ltvInfo = $target['ltv_info'] ?? [];

            if (!empty($ltvInfo)) {
                static::calcLtv($target, $ltvInfo, $getUserFunc, $ltvType, $field);
            }

            IndexCalculators::fillNodes($target, $maxDays, 0.00, 'ltv');
        };
    }

    /**
     * 单行LTV数据计算过程
     *
     * @param          $target
     * @param array $ltvInfo
     * @param \Closure $getUserFunc
     * @param int $ltvType
     * @param string $field
     * @param bool $totalGetLast
     * @return void
     */
    public static function calcLtv(
        &$target,
        array $ltvInfo,
        \Closure $getUserFunc,
        int $ltvType = 0,
        string $field = 'money_all',
        bool $totalGetLast = false
    )
    {
        $ltvInfo = array_column($ltvInfo, null, 'day_type');
        ksort($ltvInfo);

        $ltvGetFunc = IndexCalculators::ltvValueGet($ltvType);

        if ($totalGetLast) {
            $keys                         = array_diff(array_keys($ltvInfo), [1000]);
            $target['new_user_total_pay'] = $ltvInfo[max($keys)][$field] ?? 0.00;
        }
        else {
            $target['new_user_total_pay'] = $ltvInfo[1000][$field] ?? 0.00;
        }

        foreach ($ltvInfo as $i => $foo) {
            $target['ltv_' . $i] = $ltvGetFunc($foo[$field], $getUserFunc($target, $i));
        }
    }

    /**
     * @param string $field
     *
     * @return \Closure
     */
    public static function cumulativeLtvEachRow(string $field = 'firstlogin_user'): \Closure
    {
        return function (&$target) use ($field) {
            static::cumulativeLtv($target, $field);
        };
    }

    /**
     * @param        $target
     * @param string $field
     *
     * @return void
     */
    public static function cumulativeLtv(&$target, string $field = 'firstlogin_user')
    {
        if (
            empty($target['new_user_total_pay'])
            || empty((float)$target[$field])
        ) {
            $target['total_ltv'] = 0.00;
            return;
        }

        $target['total_ltv'] = number_format(
            math_eval('x/y', ['x' => $target['new_user_total_pay'], 'y' => $target[$field]]),
            2
        );
    }

    /**
     * LTV增量计算
     *
     * @return \Closure
     */
    public static function ltvIncrementalEachRow(): \Closure
    {
        return function (&$target) {
            static::ltvIncremental($target);
        };
    }

    /**
     * LTV增量计算
     *
     * @param $target
     *
     * @return void
     */
    public static function ltvIncremental(&$target)
    {
        $keys = static::catchLtvKeys($target);

        if (empty($keys)) return;

        rsort($keys);

        foreach ($keys as $kk) {
            $ii          = (int)str_replace('ltv_', '', $kk);
            $target[$kk] = round($target[$kk] - ($target['ltv_' . ($ii - 1)] ?? 0.00), 2);
        }
    }

    /**
     * LTV倍率计算过程
     *
     * @return \Closure
     */
    public static function ltvMultipleEachRow(): \Closure
    {
        return function (&$target) {
            static::ltvMultiple($target);
        };
    }

    /**
     * LTV倍率计算
     *
     * @param $target
     *
     * @return void
     */
    public static function ltvMultiple(&$target)
    {
        $keys = static::catchLtvKeys($target);

        if (empty($keys)) return;

        $ltv1 = $target['ltv_1'] ?? 0.00;

        foreach ($keys as $kk) {
            $target[$kk] = IndicatorsHelpers::division($target[$kk] ?? 0, $ltv1);
        }
    }


    /**
     * 获取含"ltv_"的字段
     *
     * @param $data
     *
     * @return mixed
     */
    public static function catchLtvKeys($data)
    {
        preg_match_all('/\bltv_\d+\b/', implode(',', array_keys($data)), $keyMatches);
        return Arr::pull($keyMatches, 0, []);
    }
}
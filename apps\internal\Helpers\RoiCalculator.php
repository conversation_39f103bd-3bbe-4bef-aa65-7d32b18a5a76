<?php

namespace app\apps\internal\Helpers;

class RoiCalculator
{
    /**
     * @param \Closure $getCostFunc
     * @param array    $options
     * @param string   $field
     *
     * @return \Closure
     */
    public static function calcEachRow(
        \Closure $getCostFunc, array $options = [], string $field = 'money_all'
    ): \Closure
    {

        $maxDays = $options['max_days'] ?? 0;

        return function (&$target) use (
            $getCostFunc, $field, $maxDays
        ) {
            $ltvInfo = $target['ltv_info'] ?? [];

            if (!empty($ltvInfo)) {
                $ltvInfo = array_column($ltvInfo, null, 'day_type');
                static::calcRoi($target, $ltvInfo, $getCostFunc, $field);
            }

            IndexCalculators::fillNodes($target, $maxDays, '0.00%', 'roi');
        };
    }

    /**
     * @param          $target
     * @param array    $ltvInfo
     * @param \Closure $getCostFunc
     * @param string   $field
     *
     * @return void
     */
    public static function calcRoi(
        &$target, array $ltvInfo, \Closure $getCostFunc, string $field = 'money_all'
    )
    {
        foreach ($ltvInfo as $i => $foo) {
            $target['roi_' . $i] = IndicatorsHelpers::division($foo[$field], $getCostFunc($target, $i), 2, true);
        }
    }

    public static function cumulativeRoiEachRow(string $field = 'cost_discount'): \Closure
    {
        return function (&$target) use ($field) {
            static::cumulativeRoi($target, $field);
        };
    }

    public static function cumulativeRoi(&$target, string $field = 'cost_discount')
    {
        if (
            empty($target['new_user_total_pay'])
            || empty((float)$target[$field])
        ) {
            $target['total_roi'] = 0.00;
            return;
        }

        $target['total_roi'] = number_format(
                math_eval('x/y * 100', ['x' => $target['new_user_total_pay'], 'y' => $target[$field]]),
                2
            ) . '%';
    }
}
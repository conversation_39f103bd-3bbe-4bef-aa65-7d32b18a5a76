<?php /** @noinspection PhpMissingReturnTypeInspection */

namespace app\service\AdvertiserData\Traits;

trait OperableQuery
{
    /**
     * @return static
     */
    public function select($sql = "")
    {
        $this->query = $this->queryFactory->newSelect();

        $table = empty(self::MAIN_TABLE['alias'])
            ? self::MAIN_TABLE['table']
            : (self::MAIN_TABLE['table'] . ' as ' . self::MAIN_TABLE['alias']);

        if($sql){
            $this->query->fromRaw($sql);
        } else{
            $this->query->from($table);
        }

        return $this;
    }

    /**
     * @param \Closure $callback
     * @return static
     */
    public function scope(\Closure $callback)
    {
        $callback($this->query);

        return $this;
    }
}
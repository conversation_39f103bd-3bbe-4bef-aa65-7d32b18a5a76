<?php

namespace app\apps\api\controllers;

use app\extension\Exception\ParameterException;
use app\models\baseConfPlatform\TbOathConf;
use app\models\DataSpy\TbCpsSettlement;
use app\models\DdcPlatform\DwsSvkeyAdBaseDaily;
use app\service\Admin;
use app\util\Common;
use Plus\MVC\Controller\JsonController;
use Plus\Util\Pagination;

/**
 * Class CpsSettlement
 * <AUTHOR>
 */
class CpsSettlementController extends JsonController
{

    /**
     * create data
     * @param array $data 请求参数
     * @return array
     */
    public function createAction($data)
    {
        if (!$data || !is_array($data)) {
            throw new ParameterException("数据不能为空！");
        }
        $time = date("Y-m-d H:i:s");
        //统计日期新增 注册
        $dateArr                   = array_unique(array_column($data, "date"));
        $dateStr                   = implode("','", $dateArr);
        $sql                       = "SELECT 
                A.TDAY,
                <PERSON><PERSON>_ID,
                B.CP_GAME_ID,
                B.GAME_ID,
                B.CHANNEL_ID,
                B.CHANNEL_ID,
                SUM(ACTIVATE_DEVICE) ACTIVATE_DEVICE,
                SUM(NEW_USER) NEW_USER,
                CONCAT(A.TDAY,'-',A.PACKAGE_ID) UNIQUE_KEY
                FROM dws_svkey_ad_base_daily A 
                JOIN base_conf_platform.tb_package_detail_conf B USING(PACKAGE_ID) WHERE A.TDAY IN('" . $dateStr . "')  AND (ACTIVATE_DEVICE>0 OR NEW_USER>0)
                GROUP BY A.TDAY,A.PACKAGE_ID";
        $newUserActiveDviceNumData = (new DwsSvkeyAdBaseDaily())->_db->query($sql)->fetchAll(2);
        $newUserActiveDviceNumData = array_column($newUserActiveDviceNumData, null, "UNIQUE_KEY");

        $insertData = [];
        foreach ($data as $item) {
            $cpsData = [ 
                "TIME"             => $item["date"],
                "CHANNEL_ID"       => $item["channel_id"],
                "CP_GAME_ID"       => $item["cp_game_id"],
                "GAME_ID"          => $item["game_id"],
                "PACKAGE_ID"       => $item["package_id"],
                "PROMOTION_WAY_ID" => $item["promotion_id"],
                "COMPANY"          => $item["subject"],
                "OS"               => $item["os"],
                "PAY_PEOPLE_NUM"   => $item["pay_count"],
                "PAY_MONEY"        => $item["pay_money"],
                "SETTLEMENT_MONEY" => $item["settlement_money"],
                "BALANCE"          => $item["balance"],
                "CORP"             => $item["company"],
                "UPDATE_TIME" => $time,
            ];
            $k       = $item["date"] . "-" . $item["package_id"];
            if (isset($newUserActiveDviceNumData[$k])) {
                $cpsData["ACTIVE_DEVICE_NUM"] = $newUserActiveDviceNumData[$k]["ACTIVATE_DEVICE"];
                $cpsData["REG_DEVICE_NUM"]    = $newUserActiveDviceNumData[$k]["NEW_USER"];
                unset($newUserActiveDviceNumData[$k]);
            }
            $insertData[] = $cpsData;
        }

        //无充值的数据填充
        foreach ($newUserActiveDviceNumData as $item) {
            $insertData[] = [
                "TIME"              => $item["TDAY"],
                "CHANNEL_ID"        => $item["CHANNEL_ID"],
                "CP_GAME_ID"        => $item["CP_GAME_ID"],
                "GAME_ID"           => $item["GAME_ID"],
                "PACKAGE_ID"        => $item["PACKAGE_ID"],
                "PROMOTION_WAY_ID"  => 704,
                "COMPANY"           => "",
                "CORP"              => "",
                "OS"                => substr($item["PACKAGE_ID"], -3) == '099'?2:1,
                "ACTIVE_DEVICE_NUM" => $item["ACTIVATE_DEVICE"],
                "REG_DEVICE_NUM"    => $item["NEW_USER"],
                "UPDATE_TIME" => $time,
            ];
        }

        $defaultValue = [
            "TIME"              => "",
            "PACKAGE_ID"        => "",
            "COMPANY"           => "",
            "CORP"              => "",
            "CHANNEL_ID"        => "",
            "CP_GAME_ID"        => "",
            "GAME_ID"           => "",
            "PROMOTION_WAY_ID"  => "",
            "OS"                => 1,
            "ACTIVE_DEVICE_NUM" => 0,
            "REG_DEVICE_NUM"    => 0,
            "PAY_PEOPLE_NUM"    => 0,
            "PAY_MONEY"         => 0,
            "SETTLEMENT_MONEY"  => 0,
            "BALANCE"           => 0,
            "UPDATE_TIME" => $time
        ];
        $sql          = Common::getBatchUpdateSql("tb_cps_settlement", $insertData, $defaultValue);
        \Plus::$app->dataspy->exec($sql);

        //清理无效数据
        $sql = "DELETE  FROM tb_cps_settlement WHERE `TIME` IN('" . $dateStr . "') AND UPDATE_TIME <'{$time}' ";
        \Plus::$app->dataspy->exec($sql);
        return $this->success([]);
    }

    /**
     * 结算数据删除
     * @param $data
     * @return array
     * @throws ParameterException
     */
    public function deleteAction($data)
    {
        if (!$data) {
            throw new ParameterException("数据不能为空！");
        }
        if (!isset($data["date"])) {
            throw new ParameterException("日期不能为空！");
        }
        $res = (new TbCpsSettlement())->delete(["TIME" => $data["date"]]);
        if ($res) {
            return $this->success([]);
        } else {
            return $this->error("删除失败");
        }
    }

}

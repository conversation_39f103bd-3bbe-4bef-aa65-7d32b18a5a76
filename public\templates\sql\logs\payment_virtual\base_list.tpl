SELECT
*
FROM ddc_platform.dwd_user_payment_upload_virtual
{include file="sql/logs/payment_virtual/base_match.tpl"}
{* 排序 *}
{if $sort|isset}
    order by
    {$is_first=1}
    {foreach $sort as $k => $foo}
        {if $is_first eq 1}
            {$k} {$foo}
            {$is_first=0}
        {else}
            , {$k} {$foo}
        {/if}
    {/foreach}
{/if}

{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}
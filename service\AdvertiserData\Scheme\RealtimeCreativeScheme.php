<?php

namespace app\service\AdvertiserData\Scheme;


use app\extension\Support\Macroable\Traits\Macroable;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Traits\JoinBase;
use app\service\AdvertiserData\Traits\JoinFixAble;
use app\service\AdvertiserData\Traits\OperableQuery;
use app\service\AdvertiserData\Traits\Schemer;
use app\service\AdvertiserData\Traits\TotalAble;
use Aura\SqlQuery\Common\InsertInterface;
use Aura\SqlQuery\Common\SelectInterface;
use Aura\SqlQuery\Common\UpdateInterface;
use Aura\SqlQuery\QueryFactory;

/**
 * @RealtimeCreativeScheme
 *
 */
class RealtimeCreativeScheme implements SchemeContract
{
    use Macroable, Schemer, JoinFixAble, OperableQuery, JoinBase;

    public const MAIN_TABLE = [
        'alias' => 't_base',
        'table' => 'ddc_platform.dws_creative_ad_base_daily',
    ];

    /**
     * 基础关联的表
     *
     */
    protected array $joinTables = [];

    /**
     * @var QueryFactory
     */
    protected QueryFactory $queryFactory;
    /**
     * @var InsertInterface|SelectInterface|UpdateInterface
     */
    protected $query;

    /**
     * 返回新创建实例
     *
     * @return RealtimeCreativeScheme
     */
    public static function NewOne(): RealtimeCreativeScheme
    {
        return new RealtimeCreativeScheme();
    }

    public function __construct()
    {
        $this->queryFactory = new QueryFactory('mysql');
    }

    public function __clone()
    {
        $this->queryFactory = clone $this->queryFactory;
        $this->query        = clone $this->query;
    }

    public function fieldReflect(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];
        return [
            'tday'                 => $mainTable,
            'cp_game_id'           => $mainTable,
            'game_id'              => $mainTable,
            'package_id'           => $mainTable,
            'app_show_id'          => 'POWER',
            'channel_main_id'      => 'POWER',
            'channel_id'           => $mainTable,
            'platform_id'          => 'POWER',
            'plan_id'              => $mainTable,
            'creative_id'          => $mainTable,
            'campaign_id'          => $mainTable,
            'promotion_id'         => 'POWER.popularize_v2_id',
            'promotion_channel_id' => $mainTable . '.channel_id',
            'ad_account'           => 'base_account.ad_account',
            'ad_account_id'        => 'base_account.id',
            'account_id'           => 'base_account.account_id',
            'user_id'              => $mainTable.'.user_id',
            'department_id'        => 'base_account.department_id',
            'is_ad_data'           => $mainTable,
            'is_appointment'       => $mainTable
        ];
    }

    /**
     * 固定关联的表
     *
     * @return array
     */
    protected function fixedTables(): array
    {
        $mainTable  = self::MAIN_TABLE['alias'];
        $fixedTable = [];

        $fixedTable[] =
            (new JoinClause('left', 'ddc_platform.dws_creative_ad_payment_daily', 't_payment'))
                ->on('t_payment.tday', '=', $mainTable . '.tday')
                ->on('t_payment.package_id', '=', $mainTable . '.package_id')
                ->on('t_payment.channel_id', '=', $mainTable . '.channel_id')
                ->on('t_payment.campaign_id', '=', $mainTable . '.campaign_id')
                ->on('t_payment.plan_id', '=', $mainTable . '.plan_id')
                ->on('t_payment.creative_id', '=', $mainTable . '.creative_id');

        $fixedTable[] =
            (new JoinClause('left', 'adp_platform.tb_adp_oauth', 'adp_oauth'))
                ->on('adp_oauth.ADVERTISER_ID', '=', $mainTable . '.ACCOUNT_ID');

        $fixedTable[] =
            (new JoinClause('left', 'dataspy.admin_user', 't_admin'))
                ->on('t_base.USER_ID', '=', 't_admin.ID');



        return $fixedTable;
    }
}
{assign var="table" value='bigdata_dws.dws_ad_creative_daily_full'}
{function dynamicAssign fenir=[] nodes=1 level=0 cols=null hasCol=true}
    {if !empty($fenir)}
        {assign var="cc" value=$fenir.name}
        {if !empty($fenir.displayed_formula[$level])}
            {assign var="ffn" value=$fenir.displayed_formula[$level]}
            {for $i=1; $i<=$nodes; $i++}
                {assign var="chillCol" value=$cc|replace:"<n>":$i}
                {if !empty($fenir['derived'])}
                    {if !$hasCol or ($hasCol and (count($fenir['derived']|array_intersect:$cols)>0 or in_array($chillCol, $cols))) }
                        {$ffn|replace:"<n>":$i} as {$chillCol},
                    {/if}
                {else}
                    {if (!$hasCol or ($hasCol and in_array($chillCol, $cols)))}
                        {$ffn|replace:"<n>":$i} as {$chillCol},
                    {/if}
                {/if}
            {/for}
        {/if}
    {/if}
{/function}
{assign var="col_guy" value=[
    ['name'=>'show_cnt','derived'=>['click_show_percent','qian_cost','show_convert_percent']],
    ['name'=>'click_cnt','derived'=>['cpc','click_show_percent','convert_percent','activate_percent','download_start_percent']],
    ['name'=>'download_cnt', 'derived'=>['download_finish_percent','install_finish_percent']],
    ['name'=>'activate_cnt', 'derived'=>['activate_percent','activate_install_percent','register_percent','activate_cost']],
    ['name'=>'convert_cnt', 'derived'=>['convert_cost','convert_percent','show_convert_percent']],
    ['name'=>'install_cnt', 'derived'=>['install_finish_percent','activate_install_percent']],
    ['name'=>'lp_view', 'derived'=>['lp_click_percent','install_finish_num']],
    ['name'=>'lp_download', 'derived'=>['lp_click_percent','install_finish_num']],
    ['name'=>'download_start', 'derived'=>['download_start_cost_percent','download_finish_percent','download_start_percent']],
    ['name'=>'register', 'derived'=>['register_cost','register_percent']],
    ['name'=>'cost', 'derived'=>[
        'qian_cost','convert_cost','tt_game_in_app_roi_1day','tt_game_in_app_roi_4days','tt_game_in_app_roi_8days','tt_active_pay_intra_one_day_roi',
        'gdt_mini_game_first_day_paying_roi','gdt_mini_game_pay_d3_roi','gdt_mini_game_pay_d7_roi','gdt_minigame_24h_pay_roi','gdt_roi_activated_d1',
        'gdt_roi_activated_d3','gdt_roi_activated_d7'
    ]],
    ['name'=>'cost_discount', 'derived'=>[
    'new_user_cost','cpc','download_start_cost_percent','activate_cost','register_cost','create_role_cost','pay_frequency_7days_cost','new_user_payment_cost',
    'roi_1','roi_2','roi_3','roi_4','roi_5','roi_6','roi_7','roi_8','roi_9','roi_10','roi_11','roi_12','roi_13','roi_14','roi_15','roi_16','roi_17','roi_18','roi_19','roi_20',
    'roi_21','roi_22','roi_23','roi_24','roi_25','roi_26','roi_27','roi_28','roi_29','roi_30','roi_31','roi_32','roi_33','roi_34','roi_35','roi_36','roi_37','roi_38','roi_39','roi_40',
    'roi_41','roi_42','roi_43','roi_44','roi_45','roi_46','roi_47','roi_48','roi_49','roi_50','roi_51','roi_52','roi_53','roi_54','roi_55','roi_56','roi_57','roi_58','roi_59','roi_60',
    'back_paid_new_roi_1','back_paid_new_roi_2','back_paid_new_roi_3','back_paid_new_roi_4','back_paid_new_roi_5','back_paid_new_roi_6','back_paid_new_roi_7','back_paid_roi_within_24_hours',
    'back_paid_cnt_cost_node_1','back_paid_cnt_cost_node_2','back_paid_cnt_cost_node_3','back_paid_cnt_cost_node_4','back_paid_cnt_cost_node_5','back_paid_cnt_cost_node_6','back_paid_cnt_cost_node_7',
    'back_paid_cnt_cost_node_user','paid_cnt_cost_1', 'paid_cnt_cost_2','paid_cnt_cost_3', 'paid_cnt_cost_4','paid_cnt_cost_5','paid_cnt_cost_6','paid_cnt_cost_7','paid_cnt_avg_with_user_1','paid_cnt_avg_with_user_2','paid_cnt_avg_with_user_3','paid_cnt_avg_with_user_4','paid_cnt_avg_with_user_5','paid_cnt_avg_with_user_6','paid_cnt_avg_with_user_7'
    ]],
    ['name'=>'new_user', 'derived'=>['new_user_cost','new_user_real_percent','pay_user_new_percent','arpu_new_user','pay_penetration','new_user_payment_cost','arppu_new_user','back_paid_percent',
    'ltv_1', 'ltv_2', 'ltv_3', 'ltv_4', 'ltv_5', 'ltv_6', 'ltv_7', 'ltv_8', 'ltv_9', 'ltv_10', 'ltv_11', 'ltv_12', 'ltv_13', 'ltv_14', 'ltv_15', 'ltv_16', 'ltv_17', 'ltv_18', 'ltv_19', 'ltv_20',
    'ltv_21', 'ltv_22', 'ltv_23', 'ltv_24', 'ltv_25', 'ltv_26', 'ltv_27', 'ltv_28', 'ltv_29', 'ltv_30', 'ltv_31', 'ltv_32', 'ltv_33', 'ltv_34', 'ltv_35', 'ltv_36', 'ltv_37', 'ltv_38', 'ltv_39', 'ltv_40',
    'ltv_41', 'ltv_42', 'ltv_43', 'ltv_44', 'ltv_45', 'ltv_46', 'ltv_47', 'ltv_48', 'ltv_49', 'ltv_50', 'ltv_51', 'ltv_52', 'ltv_53', 'ltv_54', 'ltv_55', 'ltv_56', 'ltv_57', 'ltv_58', 'ltv_59', 'ltv_60',
    'retain_1', 'retain_2', 'retain_3', 'retain_4', 'retain_5', 'retain_6', 'retain_7', 'retain_8', 'retain_9', 'retain_10', 'retain_11', 'retain_12', 'retain_13', 'retain_14', 'retain_15', 'retain_16', 'retain_17',
    'retain_18', 'retain_19', 'retain_20', 'retain_21', 'retain_22', 'retain_23', 'retain_24', 'retain_25', 'retain_26', 'retain_27', 'retain_28', 'retain_29','ltv_amount_1','ltv_amount_2','ltv_amount_3','ltv_amount_4',
    'ltv_amount_5','ltv_amount_6','ltv_amount_7'
    ]],
    ['name'=>'new_real_user','derived'=>['new_user_real_percent']],
    'new_user_emulator',
    'activate_device',
    ['name'=>'create_role_new','derived'=>['create_role_percent','create_role_cost']],
    ['name'=>'pay_new_user_7days','derived'=>['pay_frequency_avg_7days','paid_retain_7_1',
    'paid_retain_7_2', 'paid_retain_7_3', 'paid_retain_7_4', 'paid_retain_7_5', 'paid_retain_7_6', 'paid_retain_7_7',
    'paid_retain_7_8', 'paid_retain_7_9', 'paid_retain_7_10', 'paid_retain_7_11', 'paid_retain_7_12', 'paid_retain_7_13',
    'paid_retain_7_14', 'paid_retain_7_15', 'paid_retain_7_16', 'paid_retain_7_17', 'paid_retain_7_18', 'paid_retain_7_19',
    'paid_retain_7_20', 'paid_retain_7_21', 'paid_retain_7_22', 'paid_retain_7_23', 'paid_retain_7_24', 'paid_retain_7_25', 'paid_retain_7_26',
    'paid_retain_7_27', 'paid_retain_7_28', 'paid_retain_7_29']],
    ['name'=>'pay_frequency_7days','derived'=>['pay_frequency_avg_7days','pay_frequency_7days_cost']],
    ['name'=>'online_time','derived'=>['first_online_time_avg','online_time_avg']],
    ['name'=>'first_online_time','derived'=>['first_online_time_avg']],
    'active_user',
    ['name'=>'total_play', 'derived'=>['play_duration_3s_percent','play_time_avg']],
    ['name'=>'play_time_per_play','derived'=>['play_time_avg']],
    ['name'=>'play_duration_3s','derived'=>['play_duration_3s_percent']],
    'pay_user',
    'pay_money',
    'pay_count',
    ['name'=>'pay_user_new','derived'=>['pay_user_new_percent','new_user_payment_cost','arppu_new_user','paid_retain_1',
    'paid_retain_2', 'paid_retain_3', 'paid_retain_4', 'paid_retain_5', 'paid_retain_6', 'paid_retain_7', 'paid_retain_8',
    'paid_retain_9', 'paid_retain_10', 'paid_retain_11', 'paid_retain_12', 'paid_retain_13', 'paid_retain_14', 'paid_retain_15',
    'paid_retain_16', 'paid_retain_17', 'paid_retain_18', 'paid_retain_19', 'paid_retain_20', 'paid_retain_21', 'paid_retain_22',
    'paid_retain_23', 'paid_retain_24', 'paid_retain_25', 'paid_retain_26', 'paid_retain_27', 'paid_retain_28', 'paid_retain_29']],
    ['name'=>'pay_money_new','derived'=>['arpu_new_user','pay_penetration','arppu_new_user']],
    'pay_count_new',
    'pay_money_no_visual' ,
    'pay_money_new_no_visual',
    "paid_permeation",
    "active_user_week",
    ['name'=> 'ltv_<n>' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(ltv_node, '\$.<n>')) AS DECIMAL), 0)", "SUM(ltv_<n>)"], "format" => 'dynamicAssign', 'nodes' => 60,'derived'=>['roi_1','roi_2','roi_3','roi_4','roi_5','roi_6','roi_7','roi_8','roi_9','roi_10','roi_11','roi_12','roi_13','roi_14','roi_15','roi_16','roi_17','roi_18','roi_19','roi_20',
    'roi_21','roi_22','roi_23','roi_24','roi_25','roi_26','roi_27','roi_28','roi_29','roi_30','roi_31','roi_32','roi_33','roi_34','roi_35','roi_36','roi_37','roi_38','roi_39','roi_40',
    'roi_41','roi_42','roi_43','roi_44','roi_45','roi_46','roi_47','roi_48','roi_49','roi_50','roi_51','roi_52','roi_53','roi_54','roi_55','roi_56','roi_57','roi_58','roi_59','roi_60','ltv_amount_1','ltv_amount_3','ltv_amount_7']],
    ['name'=> 'retain_<n>' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(retain_node, '\$.<n>')) AS INT), 0)", "SUM(retain_<n>)"], "format" => 'dynamicAssign', 'nodes' => 31],
    ['name'=> 'paid_retain_<n>' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(paid_retain_node, '\$.<n>')) AS INT), 0)","SUM(paid_retain_<n>)"], "format" => 'dynamicAssign' , 'nodes' => 31],
    ['name'=> 'paid_retain_7_<n>' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(paid_retain_node_last_7, '\$.<n>')) AS INT), 0)", "SUM(paid_retain_7_<n>)"], "format" => 'dynamicAssign', 'nodes' => 31],
    ['name'=> 'role_pass_<n>_d' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(role_pass_node, '\$.role_pass_<n>_d')) AS INT), 0)", "SUM(role_pass_<n>_d)"], "format" => 'dynamicAssign', 'nodes' => 4,'derived' => ['pass_level_1' , 'pass_level_2' , 'pass_level_3' , 'pass_level_4']],
    ['name'=> 'role_pass_<n>_n' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(role_pass_node, '\$.role_pass_<n>_n')) AS INT), 0)", "SUM(role_pass_<n>_n)"], "format" => 'dynamicAssign', 'nodes' => 4,'derived' => ['pass_level_1' , 'pass_level_2' , 'pass_level_3' , 'pass_level_4']],
    ['name'=> 'paid_cnt_with_new_node_<n>' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(paid_cnt_with_new_node, '\$.<n>')) AS INT), 0)", "SUM(paid_cnt_with_new_node_<n>)"], "format" => 'dynamicAssign', 'nodes' => 7, 'derived'=>['paid_cnt_avg_with_user_1','paid_cnt_avg_with_user_2','paid_cnt_avg_with_user_3','paid_cnt_avg_with_user_4','paid_cnt_cost_1','paid_cnt_cost_2','paid_cnt_cost_3','paid_cnt_cost_1', 'paid_cnt_cost_2','paid_cnt_cost_3', 'paid_cnt_cost_4','paid_cnt_cost_5','paid_cnt_cost_6','paid_cnt_cost_7','paid_cnt_avg_with_user_1','paid_cnt_avg_with_user_2','paid_cnt_avg_with_user_3','paid_cnt_avg_with_user_4','paid_cnt_avg_with_user_5','paid_cnt_avg_with_user_6','paid_cnt_avg_with_user_7']],
    ['name'=> 'paid_user_with_new_node_<n>' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(paid_user_with_new_node, '\$.<n>')) AS INT), 0)", "SUM(paid_user_with_new_node_<n>)"], "format" => 'dynamicAssign', 'nodes' => 7, 'derived'=>['paid_cnt_avg_with_user_1','paid_cnt_avg_with_user_2','paid_cnt_avg_with_user_3','paid_cnt_avg_with_user_4']],
    ['name'=> 'back_paid_user_new_node_<n>' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(back_paid_user_new_node, '\$.<n>')) AS INT), 0)", "SUM(back_paid_user_new_node_<n>)"], "format" => 'dynamicAssign', 'nodes' => 7,
    'derived'=>['back_paid_cnt_avg_node_1','back_paid_cnt_avg_node_2','back_paid_cnt_avg_node_3','back_paid_cnt_avg_node_4','back_paid_cnt_avg_node_5','back_paid_cnt_avg_node_6','back_paid_cnt_avg_node_7']
    ],
    ['name'=> 'back_paid_cnt_new_node_<n>' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(back_paid_cnt_new_node, '\$.<n>')) AS INT), 0)","SUM(back_paid_cnt_new_node_<n>)"], "format" => 'dynamicAssign', 'nodes' => 7,
    'derived'=>['back_paid_cnt_cost_node_1','back_paid_cnt_cost_node_2','back_paid_cnt_cost_node_3','back_paid_cnt_cost_node_4','back_paid_cnt_cost_node_5','back_paid_cnt_cost_node_6','back_paid_cnt_cost_node_7',
    'back_paid_cnt_avg_node_1','back_paid_cnt_avg_node_2','back_paid_cnt_avg_node_3','back_paid_cnt_avg_node_4','back_paid_cnt_avg_node_5','back_paid_cnt_avg_node_6','back_paid_cnt_avg_node_7'
    ]
    ],
    ['name'=> 'back_paid_amount_new_node_<n>' , 'displayed_formula' => ["coalesce(CAST(json_unquote(json_extract(back_paid_amount_new_node, '\$.<n>')) AS DECIMAL), 0)","SUM(back_paid_amount_new_node_<n>)"],
    "format" => 'dynamicAssign', 'nodes' => 7,'derived'=>['back_paid_new_roi_1','back_paid_new_roi_2','back_paid_new_roi_3','back_paid_new_roi_4','back_paid_new_roi_5','back_paid_new_roi_6','back_paid_new_roi_7']
    ],
    "back_paid_user_new_within_24_hours",
    ['name'=>'back_paid_amount_new_within_24_hours','derived'=>['back_paid_roi_within_24_hours']],
    "back_paid_cnt_new_within_24_hours",
    'tt_active_pay_intra_day_count',
    ['name' => 'tt_game_in_app_ltv_1day','derived' => ['tt_game_in_app_roi_1day']],
    ['name' => 'tt_game_in_app_ltv_4days','derived' => ['tt_game_in_app_roi_4days']],
    ['name' => 'tt_game_in_app_ltv_8days','derived' => ['tt_game_in_app_roi_8days']],
    'tt_game_pay_7d_count', 'tt_active_pay_intra_one_day_count',
    ['name' => 'tt_active_pay_intra_one_day_amount', 'derived' => ['tt_active_pay_intra_one_day_roi']],
    'gdt_mini_game_register_users',
    'gdt_mini_game_paying_users_d1',
    'gdt_minigame_1d_pay_count',
    ['name' => 'gdt_mini_game_paying_amount_d1', 'derived' => ['gdt_mini_game_first_day_paying_roi']],
    'gdt_mini_game_pay_d3_uv',
    'gdt_mini_game_d3_pay_count',
    ['name'=>'gdt_mini_game_paying_amount_d3', 'derived'=>['gdt_mini_game_pay_d3_roi']],
    'gdt_mini_game_pay_d7_uv',
    'gdt_mini_game_d7_pay_count',
    ['name'=>'gdt_mini_game_paying_amount_d7', 'derived'=>['gdt_mini_game_pay_d7_roi']],
    'gdt_minigame_24h_pay_uv',
    ['name' => 'gdt_minigame_24h_pay_amount' , 'derived'=>['gdt_minigame_24h_pay_roi']],
    'gdt_first_day_first_pay_count',
    'gdt_first_day_pay_count',
    ['name' => 'gdt_first_day_pay_amount', 'derived'=>['gdt_roi_activated_d1']],
    'gdt_active_d3_pay_count',
    ['name'=>'gdt_payment_amount_activated_d3', 'derived'=>['gdt_roi_activated_d3']],
    'gdt_active_d7_pay_count',
    ['name'=>'gdt_payment_amount_activated_d7', 'derived'=>['gdt_roi_activated_d7']],
    'gdt_reg_dedup_pv'
]}

with creative_dash_alpha as (
    select
        `tday`, `main_channel_id`, `channel_id` , IF(`campaign_id`=0, '', `campaign_id`) as campaign_id ,IF(`plan_id`=0, '', `plan_id`) as plan_id,IF(`creative_id`=0, '', `creative_id`) as creative_id, `cp_game_id`, `game_id`,`package_id`,  `account_id`, `user_id`, `is_ad_data`, `is_appointment`, `marketing_goal`,
        {foreach $col_guy as $item}
            {if is_array($item)}
                {if $item['format']|isset}
                    {call $item['format'] fenir=$item level=0 nodes=$item['nodes'] cols=$columns hasCol=$columns|isset}
                    {continue}
                {/if}

                {if $item['derived']|isset}
                    {if !isset($columns) || ( isset($columns) and (count($item['derived']|array_intersect:$columns) >0 or in_array($item['name'], $columns))) }
                        {$item['name']},
                    {/if}
                {/if}
            {else}
                {if !isset($columns) || (isset($columns) and in_array($item, $columns)) } {$item}, {/if}
            {/if}
        {/foreach}
        update_time
    from {$table}
    {if !empty($params)}
        {assign var="mark_tag_1" value=1}
        {foreach $params as $kk => $fa}
            {if $kk eq 'range_date'}
                {if $mark_tag_1} where {$mark_tag_1=0} {else} and {/if}
                tday between '{$fa[0]}' and '{$fa[1]}'
                {continue}
            {/if}
            {if $kk eq 'cp_game_id'}
                {if $mark_tag_1} where {$mark_tag_1=0} {else} and {/if}
                {if is_array($fa)}
                    cp_game_id in ({$fa|join:','})
                {else}
                    cp_game_id = '{$fa}'
                {/if}
                {continue}
            {/if}
            {if $kk eq 'game_id'}
                {if $mark_tag_1} where {$mark_tag_1=0} {else} and {/if}
                {if is_array($fa)}
                    game_id in ({$fa|join:','})
                {else}
                    game_id = '{$fa}'
                {/if}
                {continue}
            {/if}
            {if $kk eq 'game_id_tags'}
                {if $mark_tag_1} where {$mark_tag_1=0} {else} and {/if}
                game_id in (
                    select distinct data_id from base_conf_platform.biz_tags
                        where tag_id in ({$fa|join:','}) and table_name = 'games'
                )
                {continue}
            {/if}
            {if $kk eq 'package_id'}
                {if $mark_tag_1} where {$mark_tag_1=0} {else} and {/if}
                {if is_array($fa)}
                    package_id in ({$fa|join:','})
                {else}
                    package_id = '{$fa}'
                {/if}
                {continue}
            {/if}
            {if $kk eq 'package_id_tags'}
                {if $mark_tag_1} where {$mark_tag_1=0} {else} and {/if}
                package_id in (
                    select distinct data_id from base_conf_platform.biz_tags
                        where tag_id in ({$fa|join:','}) and table_name = 'packages'
                )
                {continue}
            {/if}
            {if $kk eq 'account_id'}
                {if $mark_tag_1} where {$mark_tag_1=0} {else} and {/if}
                account_id like  '{'%'|cat:$fa|cat:'%'}'
                {continue}
            {/if}
            {if $kk eq 'campaign_id'}
                {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if}
                {if is_array($fa)}
                    campaign_id in ({$fa|join:','})
                {else}
                    campaign_id = '{$fa}'
                {/if}
                {continue}
            {/if}
            {if $kk eq 'campaign_name'}
                {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if}
                campaign_id in (select distinct campaign_id from adp_platform.tb_adp_campaign where campaign_name like '{'%'|cat:$fa|cat:'%'}')
                {continue}
            {/if}
            {if $kk eq 'plan_id'}
                {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if}
                {if is_array($fa)}
                    plan_id in ({$fa|join:','})
                {else}
                    plan_id = '{$fa}'
                {/if}
                {continue}
            {/if}
            {if $kk eq 'plan_name'}
                {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if}
                (
                plan_id in (select distinct plan_id from adp_platform.tb_adp_plan_base where plan_name like '{'%'|cat:$fa|cat:'%'}')
                or plan_id in (select distinct id  from dataspy.tb_ad_svlink_conf where aid like '{'%'|cat:$fa|cat:'%'}')
                )
                {continue}
            {/if}
            {if $kk eq 'creative_id'}
                {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if}
                {if is_array($fa)}
                    creative_id in ({$fa|join:','})
                {else}
                    creative_id = '{$fa}'
                {/if}
                {continue}
            {/if}
            {if $kk eq 'creative_name'}
                {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if}
                creative_id in (select distinct plan_id from adp_platform.tb_adp_creative_base where creative_name like '{'%'|cat:$fa|cat:'%'}')
                {continue}
            {/if}
            {if $kk eq 'data_scope'}
                {if $fa eq 1}
                    {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if} is_ad_data = 1
                {elseif $fa eq 2}
                    {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if} is_ad_data = 0
                {/if}
                {continue}
            {/if}
            {if $kk eq 'marketing_goal'}
                {if $fa != [1,2]}
                    {if in_array(1, $fa)}
                        {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if} marketing_goal != 2
                    {/if}
                    {if in_array(2, $fa)}
                        {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if} marketing_goal = 2
                    {/if}
                {/if}
                {continue}
            {/if}
            {if $kk eq 'is_has_appointment'}
                {if empty($fa)} {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if} is_appointment != 1 {/if}
                {continue}
            {/if}
            {if $kk eq 'is_has_natural'}
                {if empty($fa)} {if !$mark_tag_1} and {else} where {$mark_tag_1=0} {/if} channel_id > 0 and campaign_id != '' {/if}
                {continue}
            {/if}
        {/foreach}
    {/if}
),
dashboard_info as (
    select
        j1.tday,
        j1.cp_game_id,
        j1.game_id,
        j1.package_id,
        j1.campaign_id,
        any(coalesce(j2.campaign_name, '')) as campaign_name,
        j1.plan_id,
        j1.creative_id,
        j1.account_id,
        j1.ad_account,
        any(j1.platform_id) as platform_id,
        any(j1.account_name) as account_name,
        any(j1.is_ad_data) as is_ad_data,
        any(j1.is_appointment) as is_appointment,
        any(j1.marketing_goal) as marketing_goal,
        any(case when user_os = '["ANDROID"]' then 'ANDROID' when user_os = '["IOS"]' then 'IOS' else '混投' end)  as dim_user_os,
        any(j1.popularize_v2_id) as promotion_id,
        any(j1.app_show_id) as app_show_id,
        j1.promotion_channel_id,
        j1.dim_user_id,
        {foreach $col_guy as $item}
            {if is_array($item)}
                {if $item['format']|isset}
                    {call $item['format'] fenir=$item level=1 nodes=$item['nodes'] cols=$columns hasCol=$columns|isset}
                    {continue}
                {/if}

                {if $item['derived']|isset}
                    {if !isset($columns) || ( isset($columns) and (count($item['derived']|array_intersect:$columns) >0 or in_array($item['name'], $columns))) }
                        SUM({$item['name']}) as {$item['name']},
                    {/if}
                {/if}
            {else}
                {if !isset($columns) || (isset($columns) and in_array($item, $columns)) } SUM({$item}) as {$item}, {/if}
            {/if}
        {/foreach}
        max(j1.update_time) as update_time
    from adp_platform.tb_adp_campaign j2 right join (
        select
            a1.*,
            {if isset($ad_channels)}
                COALESCE( IF(a2.channel_id not in ({$ad_channels|join:','}), a2.channel_id, IF(a1.channel_id != 0, IF(a1.channel_id =1013, 4, a1.channel_id), a2.channel_id) ) ,0) as promotion_channel_id,
                COALESCE( IF(a2.channel_id not in ({$ad_channels|join:','}), a2.ad_user_id, IF(a1.user_id != 0, a1.user_id, a2.ad_user_id)),0 ) as dim_user_id,
            {else}
                a1.channel_id as promotion_channel_id,
                a1.user_id as dim_user_id,
            {/if}
            a3.ad_account, a4.advertiser_name as account_name, a2.platform_id,
            a2.popularize_v2_id, a2.app_show_id
        from creative_dash_alpha a1
        {if !empty($power_join_sql) && $power_join_sql == 'base_conf_platform.tb_package_detail_conf'}
            join ({$power_join_sql}) a2 on a1.package_id = a2.package_id
        {else}
            join base_conf_platform.tb_package_detail_conf a2 on a1.package_id = a2.package_id
        {/if}
        left join base_conf_platform.tb_ad_account_conf a3 on a1.account_id = a3.account_id
        left join adp_platform.tb_adp_oauth a4 on a1.account_id = a4.advertiser_id and a1.main_channel_id = a4.channel_id
        {if !empty($params)}
            {assign var="mark_tag_2" value=1}
            {foreach $params as $ii => $chill}
                {if $ii eq 'ad_account'}
                    {if !$mark_tag_2} and {else} where {$mark_tag_2=0} {/if}
                    a3.ad_account like '{'%'|cat:$chill|cat:'%'}'
                    {continue}
                {/if}
                {if $ii eq 'app_show_id' }
                    {if !$mark_tag_2} and {else} where {$mark_tag_2=0} {/if}
                    {if is_array($chill)}
                        a2.app_show_id in ({$chill|join:','})
                    {else}
                        a2.app_show_id = '{$chill}'
                    {/if}
                    {continue}
                {/if}
                {if $ii eq 'platform_id'}
                    {if !$mark_tag_2} and {else} where {$mark_tag_2=0} {/if}
                    {if is_array($chill)}
                        a2.platform_id in ({$chill|join:','})
                    {else}
                        a2.platform_id = '{$chill}'
                    {/if}
                    {continue}
                {/if}
                {if $ii eq 'promotion_id'}
                    {if !$mark_tag_2} and {else} where {$mark_tag_2=0} {/if}
                    {if is_array($chill)}
                        a2.popularize_v2_id in ({$chill|join:','})
                    {else}
                        a2.popularize_v2_id = '{$chill}'
                    {/if}
                    {continue}
                {/if}
            {/foreach}
        {/if}
    ) j1 on j1.main_channel_id = j2.channel_id and j1.campaign_id = j2.campaign_id
    {if !empty($params)}
        {assign var="mark_tag_3" value="1"}
        {foreach $params as $ki => $hi}
            {if $ki eq 'user_os'}
                {if is_array($hi)}
                    {if !$mark_tag_3} and {else} where {$mark_tag_3=0} {/if}
                    (
                    {foreach $hi as $ii => $chill}
                        {if !$chill@first} or {/if}
                        {if $chill eq 1} j2.user_os =  '["IOS"]'{/if}
                        {if $chill eq 2} j2.user_os =  '["ANDROID"]'{/if}
                        {if $chill eq 3} ((j2.user_os != '["IOS"]' and j2.user_os != '["ANDROID"]') or j2.user_os is null){/if}
                    {/foreach}
                    )
                {/if}
                {continue}
            {/if}
            {if $ki eq 'campaign_name'}
                {if !$mark_tag_3} and {else} where {$mark_tag_3=0} {/if}
                j2.campaign_name like '{'%'|cat:$hi|cat:'%'}'
            {/if}
        {/foreach}
    {/if}
    group by j1.tday, j1.cp_game_id, j1.game_id, j1.package_id, j1.campaign_id, j1.plan_id, j1.creative_id, promotion_channel_id, j1.account_id, dim_user_id, j1.ad_account
)
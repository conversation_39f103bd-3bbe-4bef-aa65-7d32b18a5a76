<?php

namespace app\service\AdvertiserData\Components\Matcher\Traits;

use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

trait CommonAble
{
    /**
     * 游戏原名下拉匹配
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchCpGameId(&$qb, array &$params)
    {
        if (!isset($params['cp_game_id'])) return;
        $field = $this->getReflectField('cp_game_id');
        $data  = $params['cp_game_id'];

        if ($data != 0) {
            QueryBuilderHelper::baseBuild($qb, $field, $data);
        }
        else {
            $qb->where(function (SelectQuery $select) use ($field) {
                $select->where($field, 0)->orWhere($field, null);
            });
        }
    }

    /**
     * 游戏统计名匹配
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchGameId(&$qb, array &$params)
    {
        if (empty($params['game_id'])) return;
        $field = $this->getReflectField('game_id');
        $data  = $params['game_id'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }
}
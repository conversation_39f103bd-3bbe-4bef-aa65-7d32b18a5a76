<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\Traits\InternalRoutes;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiseLive\LiveAccountServ;
use Plus\MVC\Controller\JsonController;

/**
 * 直播账号管理
 *
 * @route /advertise/live-account/*
 */
class LiveAccountController extends JsonController
{
    use InternalRoutes;

    /**
     * @return array
     */
    public function listAction(): array
    {
        $params = $this->wrapParams(\Plus::$app->request);
        $serv   = new LiveAccountServ();
        $list   = $serv->getList(['state' => 1], [], [], ['sorted' => 'DESC']);

        $list = Arr::crossMap($list, ['account_id' => 'key', 'account' => 'val']);

        return $this->success([
            'list' => $list,
        ]);
    }

    /**
     * @return array
     */
    public function updateSortedAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);
        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $orderList  = $params['list'];
        $serv       = new LiveAccountServ();
        $updateList = [];
        $count      = \count($orderList);

        foreach ($orderList as $id) {
            $t            = [
                'id'     => $id,
                'sorted' => $count--,
            ];
            $updateList[] = $t;
        }

        try {
            $serv->updateByAccountId($updateList);
        }
        catch (\Throwable $e) {
            return $this->error('更新失败');
        }

        return $this->success([]);

    }

    /**
     * @return array
     */
    public function saveAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $serv = new LiveAccountServ();

        try {
            $data = $params;
            $serv->insert($data);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

    /**
     * @return array
     */
    public function updateAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $serv = new LiveAccountServ();

        try {
            $data = $params;
            $serv->updateByAccountId([$data]);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

    /**
     * 软删除
     *
     * @return array
     */
    public function delAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON格式错误');
        }

        $id = $params['id'] ?? null;
        if (!is_numeric($id)) {
            return $this->error('参数缺失');
        }
        $serv = new LiveAccountServ();

        try {
            $serv->removeByAccountIds(Arr::wrap($id));
        }
        catch (\Throwable $e) {
            return $this->error('删除失败');
        }

        return $this->success([]);
    }
}
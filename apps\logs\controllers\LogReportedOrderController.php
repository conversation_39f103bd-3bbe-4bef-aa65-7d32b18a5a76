<?php

namespace app\apps\logs\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\log\LogReportedOrderLogic;
use Smarty\Exception;

/**
 * Class LogReportedOrderController
 * phpcs:disable
 * @desc 付费上报查询看板接口
 */
class LogReportedOrderController extends BaseTableController
{
    /**
     * @inerhitDoc
     * @throws Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page', 1),
            $params->pull('page_size', 100),
        ];

        if ($params->has('sort')) {
            $sortC = $params->pull('sort');
            $sort  = [$sortC => ($params->pull('order') == 'ascend' ? 'asc' : 'desc')];
        } else {
            $sort = ['reported_time' => 'desc'];
        }
        $today               = date('Y-m-d');
        $param               = $params->toArray();
        $param['order_date'] = [
            Arr::pull($param, 'range_date_start') ?? $today,
            Arr::pull($param, 'range_date_end') ?? $today,
        ];
        sort($param['order_date']);

        if (!empty($param['reported_rule_id'])) {
            $param['reported_rule_id'] = \explode(',', implode(',', $param['reported_rule_id']));
        }

        return (new LogReportedOrderLogic())->dashboardData($param, ['page' => $page, 'page_size' => $pageSize], $sort);
    }

    /**
     * @inerhitDoc
     */
    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '订单id', 'dataIndex' => 'order_id'],
            ['title' => '订单金额', 'dataIndex' => 'order_money'],
            ['title' => '上报金额', 'dataIndex' => 'reported_money'],
            ['title' => '游戏原名', 'dataIndex' => 'cp_game_name'],
            ['title' => '包号(充值)', 'dataIndex' => 'paid_package_id'],
            ['title' => '包号(归因)', 'dataIndex' => 'source_package_id'],
            ['title' => '归因渠道', 'dataIndex' => 'source_channel_name'],
            ['title' => '核心账号(充值)', 'dataIndex' => 'paid_core_account'],
            ['title' => '核心账号(归因)', 'dataIndex' => 'source_core_account'],
            ['title' => '归因计划', 'dataIndex' => 'plan_name'],
            ['title' => '付费时间', 'dataIndex' => 'pay_time'],
            ['title' => '上报时间', 'dataIndex' => 'reported_time'],
            ['title' => '自定义上报规则', 'dataIndex' => 'reported_rule_id'],
            ['title' => '自定义上报条件', 'dataIndex' => 'condition_name'],
            ['title' => '上报行为类别', 'dataIndex' => 'reported_behavior'],
            ['title' => '上报失败信息', 'dataIndex' => 'no_reported_origin'],
            ['title' => '上报状态', 'dataIndex' => 'reported_status'],
        ];

        return ['fields' => $fields];
    }
}

<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\logic\advertise\Exceptions\EmptyDataException;
use app\logic\advertise\LiveOnDutyLogic;
use app\service\AdvertiseLive\LiveAssistantServ;
use app\service\AdvertiseLive\LiveBaseServ;
use app\service\AdvertiseLive\LiveOnDutyServ;
use app\service\AdvertiseLive\OperationalAccountServ;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;

/**
 * 直播排班管理
 *
 * @route /advertise/live-on-duty/*
 *
 */
class LiveOnDutyController extends BaseTableController
{
    use ColumnsInteract;

    /**
     * 表格列表数据
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page', 1),
            $params->pull('page_size', 100),
        ];

        $options                = $params->toArray();
        $serv                   = new LiveOnDutyServ();
        $infoRe                 = $serv->getList($options, [], ['page' => $page, 'page_size' => $pageSize]);
        $liveBaseServ           = new LiveBaseServ();
        $dutyQb                 = $infoRe['duty_qb'];
        $dutyCost               = $liveBaseServ->getCostByLiveDutyQb($dutyQb, $options, ['tday', 'range_time', 'package_id', 'account_id'], ['page' => $page, 'page_size' => $pageSize]);
        $liveAssistant          = (new LiveAssistantServ())->getList([]);
        $liveAssistant          = array_column($liveAssistant, 'assistant_name', 'id');
        $liveOperationalAccount = (new OperationalAccountServ())->getList([]);
        $liveOperationalAccount = array_column($liveOperationalAccount, 'account_name', 'id');
        $constConfCollect       = (new BasicServ())->getMultiOptions(['cp_game_id:all', 'game_id', 'live_platform', 'live_team']);
        $replaceFn              = $this->replaceColumnDefine($constConfCollect);

        $process = new ProcessLine();
        $process->addProcess($replaceFn);
        $process->addProcess(fn(&$target) => $target['assistant'] = ($liveAssistant[$target['assistant_id']] ?? ''));
        $process->addProcess(fn(&$target) => $target['operational_account'] = ($liveOperationalAccount[$target['operation_account_id']] ?? ''));

        $list         = &$infoRe['list'];
        $costIndexMap = ['tday' => '', 'range_time' => '', 'package_id' => '', 'account_id' => ''];
        $costList     = $dutyCost['list'] ?? [];
        $costMap      = [];

        foreach ($costList as $chill) {
            $uniqueKey = array_merge($costIndexMap, array_intersect_key($chill, $costIndexMap));
            $uniqueKey = implode('|', $uniqueKey);

            if (!isset($costMap[$uniqueKey])) {
                $costMap[$uniqueKey] = [];
            }

            $item = &$costMap[$uniqueKey];

            isset($item['anchor_cost'])
                ? $item['anchor_cost'] += ($chill['cost_discount_1'] ?? 0)
                : $item['anchor_cost'] = ($chill['cost_discount_1'] ?? 0);

            isset($item['lucky_cost'])
                ? $item['lucky_cost'] += ($chill['cost_discount_2'] ?? 0)
                : $item['lucky_cost'] = ($chill['cost_discount_2'] ?? 0);

            isset($item['knob_cost'])
                ? $item['knob_cost'] += ($chill['cost_discount_3'] ?? 0)
                : $item['knob_cost'] = ($chill['cost_discount_3'] ?? 0);
        }
        unset($item);

        foreach ($list as &$item) {
            $uniqueKey = array_merge($costIndexMap, array_intersect_key($item, $costIndexMap));
            $uniqueKey = implode('|', $uniqueKey);

            if (isset($costMap[$uniqueKey])) {
                $item = array_merge($item, $costMap[$uniqueKey]);
            }
            else {
                $item = array_merge($item, ['anchor_cost' => 0, 'lucky_cost' => 0, 'knob_cost' => 0]);
            }
        }

        $process->run($list);

        return $infoRe;
    }

    /**
     * 表格表头
     *
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'package_info', 'label' => '关联包号信息'],
                    ['value' => 'config_info', 'label' => '配置信息'],
                ],
            ],
        ];

        $fields = [
//            ['title' => '直播日期', 'dataIndex' => 'live_date', 'classify' => ['attrs', 'base']],
//            ['title' => '直播时间段', 'dataIndex' => 'time_range', 'classify' => ['attrs', 'base']],
            ['title' => '开始时间', 'dataIndex' => 'start_time', 'classify' => ['attrs', 'base']],
            ['title' => '结束时间', 'dataIndex' => 'end_time', 'classify' => ['attrs', 'base']],
            ['title' => '直播平台', 'dataIndex' => 'live_platform', 'classify' => ['attrs', 'base']],
            ['title' => '直播账号', 'dataIndex' => 'live_account_name', 'classify' => ['attrs', 'base']],
            ['title' => '直播账号ID', 'dataIndex' => 'live_account', 'classify' => ['attrs', 'base']],
            ['title' => '合作码', 'dataIndex' => 'collaborative_code', 'classify' => ['attrs', 'base']],
            ['title' => '主播', 'dataIndex' => 'anchor_name', 'classify' => ['attrs', 'base']],
            ['title' => '主播团队', 'dataIndex' => 'live_team', 'classify' => ['attrs', 'base']],
            ['title' => '主播对接人', 'dataIndex' => 'docking_partner', 'classify' => ['attrs', 'base']],
            ['title' => '运营助手', 'dataIndex' => 'assistant', 'classify' => ['attrs', 'base']],
            ['title' => '运营账号', 'dataIndex' => 'operational_account', 'classify' => ['attrs', 'base']],
            ['title' => '游戏原名', 'dataIndex' => 'cp_game_id', 'classify' => ['attrs', 'package_info']],
            ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'classify' => ['attrs', 'package_info']],
            ['title' => '包号', 'dataIndex' => 'package_id', 'classify' => ['attrs', 'package_info']],
            ['title' => '配置人', 'dataIndex' => 'operator', 'classify' => ['attrs', 'config_info']],
            ['title' => '配置时间', 'dataIndex' => 'create_time', 'classify' => ['attrs', 'config_info']],
            ['title' => '主播成本', 'dataIndex' => 'anchor_cost', 'classify' => ['attrs', 'config_info']],
            ['title' => '福袋成本', 'dataIndex' => 'lucky_cost', 'classify' => ['attrs', 'config_info']],
            ['title' => '小手柄成本', 'dataIndex' => 'knob_cost', 'classify' => ['attrs', 'config_info']],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * 新增值班安排
     *
     * anchor_cost 主播成本
     * lucky_cost 福袋成本
     * knob_cost 小手柄成本
     *
     * @return array
     */
    public function saveAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON解析错误');
        }

        $serv = new LiveOnDutyServ();

        try {
            $data                = $params;
            $data['operator_id'] = \Plus::$service->admin->getUserId();
            $serv->insert($data);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

    /**
     * @return array
     */
    public function updateAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON解析错误');
        }

        try {
            $params['operator_id'] = \Plus::$service->admin->getUserId();
            (new LiveOnDutyServ())->updateById($params);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

    /**
     * @return array
     */
    public function delAction(): array
    {
        $params = \json_decode(\Plus::$app->request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON解析错误');
        }

        $id = $params['id'] ?? null;
        if (empty($id)) {
            return $this->error('参数缺失');
        }

        $serv = new LiveOnDutyServ();

        try {
            $serv->removeByIds(Arr::wrap($id));
        }
        catch (\Throwable $e) {
            return $this->error('删除失败');
        }

        return $this->success([]);
    }

    public function operatorListAction(): array
    {
        $serv       = new LiveOnDutyServ();
        $operatorRe = $serv->getOperatorList();
        $ids        = array_column($operatorRe, 'operator_id');
        $list       = (new GeneralOptionServ())->getUserListByIds($ids);

        return $this->success(['list' => Arr::crossMap($list, ['id' => 'key', 'real_name' => 'val'])]);
    }

    /**
     * 排班导入
     *
     * @return array
     */
    public function importByFileAction(): array
    {
        $input = \Plus::$app->request->getFileItem('file');
        if (empty($input)) {
            return $this->error('missing file');
        }

        $fileInfo = $input->save(CACHE_DIR . $input->getFilename());
        $filePath = $fileInfo->getPathname();

        if (empty($filePath)) {
            return $this->error('获取路径失败');
        }

        try {
            $logic = new LiveOnDutyLogic();
            $logic->importData($filePath);
        }
        catch (EmptyDataException $e) {
            return $this->error($e->getMessage());
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }
}
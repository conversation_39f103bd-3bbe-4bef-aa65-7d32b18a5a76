<?php

namespace app\service\Revenue;

use app\extension\FakeDB\FakeDB;
use Cycle\ORM\Select\QueryBuilder;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class IncomeServ
{
    /**
     * MODE_ALL(返回汇总行+详情)
     * MODE_SUMMARY(仅返回汇总行)
     * MODE_LIST(仅返回详情)
     *
     * MODE_ALL ^ MODE_SUMMARY(仅返回详情) = MODE_LIST
     */
    const MODE_ALL     = 3;
    const MODE_SUMMARY = 2;
    const MODE_LIST    = 1;


    private const COL_MODE_LIST = 1;
    private const COL_MODE_INFO = 2;

    private const TB_INCOME_PACKAGE = 'dataspy.tb_income_stat_package_daily';

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return array
     * @throws \Exception
     */
    public function fetchIncomeDaily(array $params = [], array $groups = ['tday'], array $columns = []): array
    {
        $db       = $this->getConn();
        $powerSql = \Plus::$service->admin->getAdminPowerSql();

        $qb = $db
            ->select()
            ->from(static::TB_INCOME_PACKAGE . ' as t_income')
            ->innerJoin(new Fragment($powerSql))
            ->on('t_income.package_id', 'POWER.package_id');

        $this->matchIncomeWhere($qb, $params);

        $cols = [
            'tday'     => new Fragment('DATE(tday) as tday'),
            'amount'   => new Fragment('SUM(IF(type=2,sum_amount,0)) as amount'),
            'recharge' => new Fragment('SUM(IF(type=1,sum_amount,0)) as recharge'),
        ];

        if (!empty($columns)) {
            $cols = array_intersect_key($cols, array_flip($columns));
        }

        $qb->columns($cols);

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        return $qb->fetchAll();
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return array
     * @throws \Exception
     */
    public function fetchRechargeInfo(array $params = [], array $groups = ['tday'], array $columns = []): array
    {
        $db       = $this->getConn();
        $powerSql = \Plus::$service->admin->getAdminPowerSql();

        $qb = $db
            ->select()
            ->from(static::TB_INCOME_PACKAGE . ' as t_income')
            ->innerJoin(new Fragment($powerSql))
            ->on('t_income.package_id', 'POWER.package_id');

        $this->matchIncomeWhere($qb, $params);

        $cols = [
            'tday'                         => new Fragment('DATE(tday) as tday'),
            'sum_amount'                   => new Fragment('COALESCE(SUM(sum_amount),0) as sum'),
            'sm_amount'                    => new Fragment('COALESCE(SUM(sm_amount),0) as shoumeng'),
            'sm_yyb_amount'                => new Fragment('COALESCE(SUM(sm_yyb_amount),0) as yyb_shoumeng'),
            'sm_ios_amount'                => new Fragment('COALESCE(SUM(sm_ios_amount),0) as ios_shoumeng'),
            'yyb_amount_normal'            => new Fragment('COALESCE(SUM(yyb_amount_normal),0) as yyb_normal'),
            'yyb_amount_special_first'     => new Fragment('COALESCE(SUM(yyb_amount_special_first),0) as yyb_special_first'),
            'yyb_amount_special_second'    => new Fragment('COALESCE(SUM(yyb_amount_special_second),0) as yyb_special_second'),
            'ios_amount'                   => new Fragment('COALESCE(SUM(ios_amount),0) as ios'),
            'other_channel_amount_normal'  => new Fragment('COALESCE(SUM(other_channel_amount_normal), 0) as other_channel_normal'),
            'other_channel_amount_special' => new Fragment('COALESCE(SUM(other_channel_amount_special), 0) as other_channel_special'),
            'wfly_amount'                  => new Fragment('COALESCE(SUM(wfly_amount),0) as wfly'),
            'coin_amount'                  => new Fragment('COALESCE(SUM(coin_amount),0) as coin'),
            'other_amount'                 => new Fragment('COALESCE(SUM(other_amount),0) as other'),
            'gdt_weixin_amount'            => new Fragment('COALESCE(SUM(gdt_weixin_amount), 0) as gdt_weixin'),
            'coin_deduct'                  => new Fragment('COALESCE(SUM(coin_deduct) , 0) as coin_deduct'), // 九玩币抵扣
            'coupon_deduct'                => new Fragment('COALESCE(SUM(coupon_deduct) , 0) as coupon_deduct'), // 代金券抵扣
        ];

        $qb->columns($cols);

        $summaryQb = clone $qb;

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        return [
            'list'    => $qb->fetchAll(),
            'summary' => ($summaryQb->fetchAll())[0] ?? [],
        ];
    }

    /**
     * 兼容fetchRechargeInfo方法
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int   $mode
     * @param array $columns
     *
     * @return array
     * @throws \Exception
     */
    public function rechargeInfo(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1, array $columns = []
    ): array
    {
        $result   = [];
        $db       = $this->getConn();
        $powerSql = str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql());

        $qb = $db
            ->select()
            ->from(static::TB_INCOME_PACKAGE . ' as t_income')
            ->innerJoin(new Fragment($powerSql), 'power')
            ->on([
                't_income.package_id' => 'power.package_id',
            ]);

        $allMode   = static::MODE_ALL | static::MODE_SUMMARY | static::MODE_LIST;
        $paramMode = $mode & $allMode;

        $this->matchIncomeWhere($qb, $params);
        $qb->columns($this->getIncomeFields($params, $groups, $columns));

        if ($paramMode & static::MODE_LIST) {
            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    $infoQb->groupBy($g);
                }
            }

            $notHavePageQb = clone $infoQb;

            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;
                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }

            if (!empty($sort)) {
                $infoQb->orderBy($sort);
            }

            $result['list'] = $infoQb->fetchAll();
//            $wrapTotalQb     = (clone $notHavePageQb)->columns([new Fragment('1 as row')]);
//            $result['total'] = $this
//                ->getConn()
//                ->select()
//                ->from(new Fragment('(' . $wrapTotalQb->__toString() . ') as total_body'))
//                ->count();
        }

        if ($paramMode & static::MODE_SUMMARY) {
            $summaryQb    = clone $qb;
            $summaryField = $this->getIncomeFields($params, $groups, $columns);
            $summaryField = array_diff_key($summaryField, array_flip($groups));
            $summaryQb->columns($summaryField);
            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('dataspy');
    }

    /**
     * @param QueryBuilder|SelectQuery $qb
     * @param array                    $params
     *
     * @return void
     * @throws \Exception
     */
    private function matchIncomeWhere(&$qb, array $params = [])
    {
        $today = date('Y-m-d');

        if (
            !empty($params['range_date_start'])
            || !empty($params['range_date_end'])
        ) {
            $rangeDate = [
                (new \DateTime(($params['range_date_start'] ?? $today)))->format('Ymd'),
                (new \DateTime(($params['range_date_end'] ?? $today)))->format('Ymd'),
            ];

            sort($rangeDate);

            $qb->where('t_income.tday', 'between', ...$rangeDate);
        }

        if (isset($params['is_remove'])) {
            $qb->where('is_remove', $params['is_remove']);
        }

        if (isset($params['type'])) {
            $tmp = $params['type'];

            if (is_array($tmp)) {
                $tmp = new Parameter($params['type']);
            }

            $qb->where('t_income.type', $tmp);
        }

        if (!empty($params['cp_game_id'])) {
            $cpGames = $params['cp_game_id'];

            if (is_string($cpGames) && str_contains($cpGames, ',')) {
                $cpGames = \explode(',', $cpGames);
            }

            if (is_array($cpGames)) {
                $cpGames = new Parameter($cpGames);
            }

            $qb->where('cp_game_id', $cpGames);
        }

        if (!empty($params['only_coupon'])) {
            $rangeDate = [
                (new \DateTime(($params['range_date_start'] ?? $today)))->format('Y-m-d'),
                (new \DateTime(($params['range_date_end'] ?? $today)))->format('Y-m-d'),
            ];

            sort($rangeDate);

            // sub query
            $sub   = $this->getConn();
            $subQb = $sub
                ->select()
                ->from('ddc_platform.dwd_sdk_user_payment_virtual_ext as t1')
                ->innerJoin('ddc_platform.dwd_sdk_user_payment', 't2')
                ->on(['t1.order_id' => 't2.order_id'])
                ->where('t1.pay_time', 'between', $rangeDate[0] . ' 00:00:00', $rangeDate[1] . ' 23:59:59')
                ->where('t2.pay_result', 1)
                ->where('coupon_money', '>', 0)->groupBy('t1.cp_game_id')->columns('t1.cp_game_id as cp_game_id');

            $cpGames = array_column($subQb->fetchAll(), 'cp_game_id');

            $qb->where('cp_game_id', new Parameter($cpGames));
        }

    }

    /**
     * 返回表头字段
     *
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return array
     */
    protected function getIncomeFields(array $params, array $groups = [], array $columns = []): array
    {
        $infoCols = [
            'tday'       => new Fragment('DATE(tday) as tday'),
            'cp_game_id' => 'power.cp_game_id',
        ];

        $calcCols = [
            'sum_amount'                   => new Fragment('COALESCE(SUM(sum_amount),0) as sum'),
            'sm_amount'                    => new Fragment('COALESCE(SUM(sm_amount),0) as shoumeng'),
            'sm_yyb_amount'                => new Fragment('COALESCE(SUM(sm_yyb_amount),0) as yyb_shoumeng'),
            'sm_ios_amount'                => new Fragment('COALESCE(SUM(sm_ios_amount),0) as ios_shoumeng'),
            'yyb_amount_normal'            => new Fragment('COALESCE(SUM(yyb_amount_normal),0) as yyb_normal'),
            'yyb_amount_special_first'     => new Fragment('COALESCE(SUM(yyb_amount_special_first),0) as yyb_special_first'),
            'yyb_amount_special_second'    => new Fragment('COALESCE(SUM(yyb_amount_special_second),0) as yyb_special_second'),
            'ios_amount'                   => new Fragment('COALESCE(SUM(ios_amount),0) as ios'),
            'other_channel_amount_normal'  => new Fragment('COALESCE(SUM(other_channel_amount_normal), 0) as other_channel_normal'),
            'other_channel_amount_special' => new Fragment('COALESCE(SUM(other_channel_amount_special), 0) as other_channel_special'),
            'wfly_amount'                  => new Fragment('COALESCE(SUM(wfly_amount),0) as wfly'),
            'coin_amount'                  => new Fragment('COALESCE(SUM(coin_amount),0) as coin'),
            'other_amount'                 => new Fragment('COALESCE(SUM(other_amount),0) as other'),
            'gdt_weixin_amount'            => new Fragment('COALESCE(SUM(gdt_weixin_amount), 0) as gdt_weixin'),
            'coin_deduct'                  => new Fragment('COALESCE(SUM(coin_deduct) , 0) as coin_deduct'), // 九玩币抵扣
            'coupon_deduct'                => new Fragment('COALESCE(SUM(coupon_deduct) , 0) as coupon_deduct'), // 代金券抵扣
        ];

        $infoCols = array_intersect_key($infoCols, array_flip($groups));
        $cols     = array_merge($infoCols, $calcCols);

        if (!empty($columns)) {
            $cols = array_intersect_key($cols, array_flip($columns));
        }

        return $cols;
    }
}
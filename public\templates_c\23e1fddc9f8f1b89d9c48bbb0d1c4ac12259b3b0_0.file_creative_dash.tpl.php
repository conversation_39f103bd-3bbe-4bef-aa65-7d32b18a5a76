<?php
/* Smarty version 5.0.0-rc3, created on 2025-06-23 15:27:57
  from 'file:sql/advertise/ad_dashboard/creative_dash.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.0.0-rc3',
  'unifunc' => 'content_685901fd670ad7_93435284',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '23e1fddc9f8f1b89d9c48bbb0d1c4ac12259b3b0' => 
    array (
      0 => 'sql/advertise/ad_dashboard/creative_dash.tpl',
      1 => 1750323986,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:sql/advertise/ad_dashboard/creative_dash_alpha.tpl' => 1,
  ),
))) {
function content_685901fd670ad7_93435284 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = '/mnt/e/project/spy2.0-api/public/templates/sql/advertise/ad_dashboard';
$_smarty_tpl->renderSubTemplate("file:sql/advertise/ad_dashboard/creative_dash_alpha.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), (int) 0, $_smarty_current_dir);
?>

<?php $_smarty_tpl->assign('dash_col', array('show_cnt','click_cnt','download_cnt','activate_cnt','convert_cnt','install_cnt','lp_view','lp_download','download_start','register','cost','cost_discount','new_real_user','new_user','new_user_emulator','activate_device','create_role_new','pay_new_user_7days','pay_frequency_7days','online_time','first_online_time','active_user','active_user_week','total_play','play_time_per_play','play_duration_3s','pay_user','pay_money','pay_count','pay_user_new','pay_money_new','pay_count_new','pay_money_no_visual','pay_money_new_no_visual',array('name'=>'first_online_time_avg','displayed_formula'=>"coalesce(round((sum(first_online_time) / sum(new_user) / 60), 2),0.00)"),array('name'=>'online_time_avg','displayed_formula'=>"coalesce(round((sum(online_time) / sum(new_user) / 60), 2), 0)"),array('name'=>'new_user_cost','displayed_formula'=>"coalesce(round(sum(cost_discount) / sum(new_user), 2), 0)"),array('name'=>'cpc','displayed_formula'=>"coalesce(round(sum(cost_discount) / sum(`click_cnt`), 2), 0)"),array('name'=>'click_show_percent','displayed_formula'=>"coalesce(round(sum(`click_cnt`) / sum(`show_cnt`) * 100, 2), 0)"),array('name'=>'qian_cost','displayed_formula'=>"coalesce(round(sum(`cost`) * 1000 / sum(`show_cnt`), 2), 0)"),array('name'=>'lp_click_percent','displayed_formula'=>"coalesce(round(sum(lp_download) / sum(lp_view) * 100, 2), 0)"),array('name'=>'convert_cost','displayed_formula'=>"coalesce(round(sum(cost) / sum(convert_cnt), 2), 0.00)"),array('name'=>'convert_percent','displayed_formula'=>"coalesce(round(sum(`convert_cnt`) / sum(`click_cnt`) * 100, 2), 0)"),array('name'=>'download_start_cost_percent','displayed_formula'=>"coalesce(round(sum(`cost_discount`) / sum(download_start), 2), 0)"),array('name'=>'download_finish_percent','displayed_formula'=>"coalesce(round(sum(`download_cnt`) / sum(`download_start`) * 100, 2), 0)"),array('name'=>'install_finish_num','displayed_formula'=>"coalesce(round(sum(`lp_download`) / sum(`lp_view`), 2), 0)"),array('name'=>'install_finish_percent','displayed_formula'=>"coalesce(round(sum(`install_cnt`) / sum(`download_cnt`), 2), 0)"),array('name'=>'activate_cost','displayed_formula'=>"coalesce(round(sum(`cost_discount`) / sum(`activate_cnt`), 2), 0)"),array('name'=>'activate_percent','displayed_formula'=>"coalesce(round(sum(`activate_cnt`) / sum(`click_cnt`) * 100, 2), 0)"),array('name'=>'activate_install_percent','displayed_formula'=>"coalesce(round(sum(`activate_cnt`) / sum(`install_cnt`) * 100, 2), 0)"),array('name'=>'register_cost','displayed_formula'=>"coalesce(round(sum(`cost_discount`) / sum(`register`), 2), 0)"),array('name'=>'register_percent','displayed_formula'=>"coalesce(round(sum(`register`) / sum(`activate_cnt`), 2), 0)"),array('name'=>'new_user_real_percent','displayed_formula'=>"coalesce(round(sum(`new_real_user`) / sum(`new_user`) * 100, 2), 0)"),array('name'=>'pay_user_new_percent','displayed_formula'=>"coalesce(round(sum(`pay_user_new`) / sum(`new_user`) * 100, 2), 0)"),array('name'=>'arpu_new_user','displayed_formula'=>"coalesce(round(sum(`pay_money_new`) / sum(`new_user`), 2), 0)"),array('name'=>'pay_penetration','displayed_formula'=>"coalesce(round(sum(`pay_money_new`) / sum(`new_user`), 2), 0)"),array('name'=>'create_role_percent','displayed_formula'=>"coalesce(round(sum(`create_role_new`) / sum(`new_user`) * 100, 2), 0)"),array('name'=>'create_role_cost','displayed_formula'=>"coalesce(round(sum(`cost_discount`) / sum(`create_role_new`) * 100, 2), 0)"),array('name'=>'pay_frequency_avg_7days','displayed_formula'=>"coalesce(round(sum(`pay_frequency_7days`) / sum(`pay_new_user_7days`), 2), 0)"),array('name'=>'pay_frequency_7days_cost','displayed_formula'=>"coalesce(round(sum(`cost_discount`) / sum(`pay_frequency_7days`), 2), 0)"),array('name'=>'new_user_payment_cost','displayed_formula'=>"coalesce(round(sum(`cost_discount`) / sum(`pay_user_new`), 2), 0)"),array('name'=>'download_start_percent','displayed_formula'=>"coalesce(round(sum(`download_start`) / sum(`click_cnt`) * 100, 2), 0)"),array('name'=>'arppu_new_user','displayed_formula'=>"coalesce(round(sum(`pay_money_new`) / sum(`pay_user_new`), 2), 0)"),array('name'=>'play_duration_3s_percent','displayed_formula'=>"coalesce(round(sum(`play_duration_3s`) / sum(`total_play`) * 100, 2), 0)"),array('name'=>'play_time_avg','displayed_formula'=>"coalesce(round(sum(`play_time_per_play`) / sum(`total_play`), 2), 0)"),array('name'=>'show_convert_percent','displayed_formula'=>"coalesce(round(sum(`convert_cnt`) / sum(`show_cnt`) * 100, 2), 0)"),array('name'=>'ltv_<n>',"displayed_formula"=>array("ROUND(SUM(ltv_<n>) / SUM(IF(DATEDIFF(DATE(NOW()), tday) +1 >= <n>, new_user, 0)), 2)"),"format"=>'dynamicAssign','nodes'=>60),array('name'=>'ltv_amount_<n>',"displayed_formula"=>array("SUM(ltv_<n>)"),"format"=>'dynamicAssign','nodes'=>7),array('name'=>'roi_<n>',"displayed_formula"=>array("ROUND(SUM(ltv_<n>) / SUM(IF(DATEDIFF(DATE(NOW()), tday) +1 >= <n>, cost_discount, 0)) * 100, 2)"),"format"=>'dynamicAssign','nodes'=>60),array('name'=>'retain_<n>','displayed_formula'=>array('ROUND(SUM(retain_<n>) / SUM(new_user) * 100, 2)'),'format'=>'dynamicAssign','nodes'=>31),array('name'=>'paid_retain_<n>','displayed_formula'=>array('ROUND(SUM(paid_retain_<n>)/SUM(pay_user_new) * 100, 2)'),"format"=>'dynamicAssign','nodes'=>31),array('name'=>'paid_retain_7_<n>','displayed_formula'=>array('ROUND(SUM(paid_retain_7_<n>) / SUM(pay_new_user_7days) * 100, 2)'),"format"=>'dynamicAssign','nodes'=>31),array('name'=>'paid_cnt_with_new_node_<n>','displayed_formula'=>array("SUM(paid_cnt_with_new_node_<n>)"),'nodes'=>7,"format"=>'dynamicAssign'),array('name'=>'paid_user_with_new_node_<n>','displayed_formula'=>array("SUM(paid_user_with_new_node_<n>)"),'nodes'=>7,"format"=>'dynamicAssign'),array('name'=>'paid_cnt_cost_<n>','displayed_formula'=>array("coalesce(round(SUM(cost_discount) / SUM(paid_cnt_with_new_node_<n>),2), 0)"),'nodes'=>7,"format"=>'dynamicAssign'),array('name'=>'paid_cnt_avg_with_user_<n>','displayed_formula'=>array("coalesce(round( SUM(paid_cnt_with_new_node_<n>) / SUM(paid_user_with_new_node_<n>), 2))"),'nodes'=>7,"format"=>'dynamicAssign'),array('name'=>'back_paid_user_new_node_<n>','displayed_formula'=>array("SUM(back_paid_user_new_node_<n>)"),'nodes'=>7,"format"=>'dynamicAssign','derived'=>array('back_paid_cnt_cost_node_user')),array('name'=>'back_paid_amount_new_node_<n>','displayed_formula'=>array("SUM(back_paid_amount_new_node_<n>)"),'nodes'=>7,"format"=>'dynamicAssign'),array('name'=>'back_paid_new_roi_<n>','displayed_formula'=>array("coalesce(round(SUM(back_paid_amount_new_node_<n>)/SUM(cost_discount) * 100, 2), 0.00)"),'nodes'=>7,"format"=>'dynamicAssign'),array('name'=>'back_paid_cnt_new_node_<n>','displayed_formula'=>array("sum(back_paid_cnt_new_node_<n>)"),'nodes'=>7,"format"=>'dynamicAssign'),array('name'=>'back_paid_cnt_cost_node_<n>','displayed_formula'=>array("coalesce(round(SUM(cost_discount) / SUM(back_paid_cnt_new_node_<n>), 2), 0.00)"),'nodes'=>7,"format"=>"dynamicAssign"),array('name'=>'back_paid_cnt_cost_node_user','displayed_formula'=>array("coalesce(round(SUM(cost_discount)/SUM(back_paid_user_new_node_1), 2), 0.00)"),'nodes'=>7,"format"=>"dynamicAssign"),array('name'=>'back_paid_cnt_avg_node_<n>','displayed_formula'=>array("coalesce(round(SUM(back_paid_cnt_new_node_<n>)/SUM(back_paid_user_new_node_<n>), 2), 0.00)"),'nodes'=>7,"format"=>"dynamicAssign"),array('name'=>'pass_level_<n>','displayed_formula'=>array("coalesce(round(SUM(role_pass_<n>_n)/SUM(role_pass_<n>_d) * 100, 2), 0.00)"),'nodes'=>4,"format"=>"dynamicAssign"),array('name'=>'back_paid_percent','displayed_formula'=>"coalesce(round(sum(back_paid_user_new_node_1) /sum(new_user)*100, 2), 0.00)"),'back_paid_user_new_within_24_hours','back_paid_amount_new_within_24_hours','back_paid_cnt_new_within_24_hours',array('name'=>'back_paid_roi_within_24_hours','displayed_formula'=>"coalesce(round(sum(back_paid_amount_new_within_24_hours) /sum(cost_discount)*100, 2), 0.00)"),'tt_active_pay_intra_day_count','tt_game_in_app_ltv_1day','tt_game_in_app_ltv_4days','tt_game_in_app_ltv_8days',array('name'=>'tt_game_in_app_roi_1day','displayed_formula'=>"coalesce(round(sum(tt_game_in_app_ltv_1day) /sum(cost)*100, 2), 0.00)"),array('name'=>'tt_game_in_app_roi_4days','displayed_formula'=>"coalesce(round(sum(tt_game_in_app_ltv_4days) /sum(cost)*100, 2), 0.00)"),array('name'=>'tt_game_in_app_roi_8days','displayed_formula'=>"coalesce(round(sum(tt_game_in_app_ltv_8days) /sum(cost)*100, 2), 0.00)"),'tt_game_pay_7d_count','tt_active_pay_intra_one_day_count','tt_active_pay_intra_one_day_amount',array('name'=>'tt_active_pay_intra_one_day_roi','displayed_formula'=>"coalesce(round(sum(tt_active_pay_intra_one_day_amount) /sum(cost)*100, 2), 0.00)"),'gdt_mini_game_register_users','gdt_mini_game_paying_users_d1','gdt_minigame_1d_pay_count','gdt_mini_game_paying_amount_d1',array('name'=>'gdt_mini_game_first_day_paying_roi','displayed_formula'=>"coalesce(round(sum(gdt_mini_game_paying_amount_d1) /sum(cost)*100, 2), 0.00)"),'gdt_mini_game_pay_d3_uv','gdt_mini_game_d3_pay_count','gdt_mini_game_paying_amount_d3',array('name'=>'gdt_mini_game_pay_d3_roi','displayed_formula'=>"coalesce(round(sum(gdt_mini_game_paying_amount_d3) /sum(cost)*100, 2), 0.00)"),'gdt_mini_game_pay_d7_uv','gdt_mini_game_d7_pay_count','gdt_mini_game_paying_amount_d7',array('name'=>'gdt_mini_game_pay_d7_roi','displayed_formula'=>"coalesce(round(sum(gdt_mini_game_paying_amount_d7) /sum(cost)*100, 2), 0.00)"),'gdt_minigame_24h_pay_uv','gdt_minigame_24h_pay_amount',array('name'=>'gdt_minigame_24h_pay_roi','displayed_formula'=>"coalesce(round(sum(gdt_minigame_24h_pay_amount) /sum(cost)*100, 2), 0.00)"),'gdt_first_day_first_pay_count','gdt_first_day_pay_count','gdt_first_day_pay_amount',array('name'=>'gdt_roi_activated_d1','displayed_formula'=>"coalesce(round(sum(gdt_first_day_pay_amount) /sum(cost)*100, 2), 0.00)"),'gdt_active_d3_pay_count','gdt_payment_amount_activated_d3',array('name'=>'gdt_roi_activated_d3','displayed_formula'=>"coalesce(round(sum(gdt_payment_amount_activated_d3) /sum(cost)*100, 2), 0.00)"),'active_d7_pay_count','gdt_payment_amount_activated_d7','gdt_reg_dedup_pv',array('name'=>'gdt_roi_activated_d7','displayed_formula'=>"coalesce(round(sum(gdt_payment_amount_activated_d7) /sum(cost)*100, 2), 0.00)")), false, NULL);?>

select
<?php if (!empty($_smarty_tpl->getValue('groups'))) {?>
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('groups'), 'item', true);
$_smarty_tpl->getVariable('item')->iteration = 0;
$foreach0DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('item')->value) {
$foreach0DoElse = false;
$_smarty_tpl->getVariable('item')->iteration++;
$_smarty_tpl->getVariable('item')->last = $_smarty_tpl->getVariable('item')->iteration === $_smarty_tpl->getVariable('item')->total;
$foreach0Backup = clone $_smarty_tpl->getVariable('item');
?>
        <?php if ($_smarty_tpl->getValue('item') == 'tday') {?> tday,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'cp_game_id') {?> cp_game_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'game_id') {?> game_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'app_show_id') {?> app_show_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'channel_main_id') {?> channel_main_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_channel_id') {?> promotion_channel_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_id') {?> promotion_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'user_id') {?> dim_user_id as user_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'department_id') {?> department_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'package_id') {?> package_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'platform_id') {?> t1.platform_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'account_id') {?> account_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'account_name') {?> account_name,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'ad_account') {?> ad_account,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'campaign_id') {?> campaign_id,any(campaign_name) as campaign_name,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'plan_id') {?> plan_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'creative_id') {?> creative_id,<?php continue 1;?>
        <?php } elseif ($_smarty_tpl->getValue('item') == 'dim_user_os') {?> dim_user_os,<?php continue 1;?>
        <?php }?>
    <?php
$_smarty_tpl->setVariable('item', $foreach0Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('dash_col'), 'chill');
$foreach1DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('chill')->value) {
$foreach1DoElse = false;
?>
    <?php if (is_array($_smarty_tpl->getValue('chill'))) {?>
        <?php if ((null !== ($_smarty_tpl->getValue('chill')['format'] ?? null))) {?>
            <?php $_smarty_tpl->getSmarty()->getRuntime('TplFunction')->callTemplateFunction($_smarty_tpl, $_smarty_tpl->getValue('chill')['format'], array('fenir'=>$_smarty_tpl->getValue('chill'),'level'=>0,'nodes'=>$_smarty_tpl->getValue('chill')['nodes'],'cols'=>$_smarty_tpl->getValue('columns'),'hasCol'=>(null !== ($_smarty_tpl->getValue('columns') ?? null))), true);?>

        <?php } else { ?>
            <?php if (!(null !== ($_smarty_tpl->getValue('columns') ?? null)) || ((null !== ($_smarty_tpl->getValue('columns') ?? null)) && $_smarty_tpl->getSmarty()->getModifierCallback('in_array')($_smarty_tpl->getValue('chill')['name'],$_smarty_tpl->getValue('columns')))) {?>
                <?php echo $_smarty_tpl->getValue('chill')['displayed_formula'];?>
  as <?php echo $_smarty_tpl->getValue('chill')['name'];?>
,
            <?php }?>
        <?php }?>
    <?php } else { ?>
        <?php if (!(null !== ($_smarty_tpl->getValue('columns') ?? null)) || ((null !== ($_smarty_tpl->getValue('columns') ?? null)) && $_smarty_tpl->getSmarty()->getModifierCallback('in_array')($_smarty_tpl->getValue('chill'),$_smarty_tpl->getValue('columns')))) {?> sum(<?php echo $_smarty_tpl->getValue('chill');?>
) as <?php echo $_smarty_tpl->getValue('chill');?>
, <?php }?>
    <?php }
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
max(update_time) as last_update_time
from dashboard_info t1
left join base_conf_platform.tb_base_channel_conf t2 on t1.promotion_channel_id = t2.channel_id
left join dataspy.admin_user t3 on t1.dim_user_id=t3.id
<?php if (!empty($_smarty_tpl->getValue('params'))) {?>
    <?php $_smarty_tpl->assign('tag_first_where', 1, false, NULL);?>
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('params'), 'item', true, 'key');
$_smarty_tpl->getVariable('item')->iteration = 0;
$foreach2DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('key')->value => $_smarty_tpl->getVariable('item')->value) {
$foreach2DoElse = false;
$_smarty_tpl->getVariable('item')->iteration++;
$_smarty_tpl->getVariable('item')->last = $_smarty_tpl->getVariable('item')->iteration === $_smarty_tpl->getVariable('item')->total;
$foreach2Backup = clone $_smarty_tpl->getVariable('item');
?>
        <?php if ($_smarty_tpl->getValue('key') == 'channel_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                t1.promotion_channel_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
)
            <?php } else { ?>
                t1.promotion_channel_id = <?php echo $_smarty_tpl->getValue('item');?>

            <?php }?>
        <?php }?>

        <?php if ($_smarty_tpl->getValue('key') == 'channel_main_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                channel_main_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
)
            <?php } else { ?>
                channel_main_id = <?php echo $_smarty_tpl->getValue('item');?>

            <?php }?>
        <?php }?>

        <?php if ($_smarty_tpl->getValue('key') == 'user_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                t1.dim_user_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
)
            <?php } else { ?>
                t1.dim_user_id = <?php echo $_smarty_tpl->getValue('item');?>

            <?php }?>
        <?php }?>

        <?php if ($_smarty_tpl->getValue('key') == 'department_id') {?>
            <?php if (!$_smarty_tpl->getValue('tag_first_where')) {?> and <?php } else { ?> where <?php $_smarty_tpl->assign('tag_first_where', 0, false, NULL);?> <?php }?>
            <?php if (is_array($_smarty_tpl->getValue('item'))) {?>
                t3.department_id in (<?php echo $_smarty_tpl->getSmarty()->getModifierCallback('join')($_smarty_tpl->getValue('item'),',');?>
)
            <?php } else { ?>
                t3.department_id = <?php echo $_smarty_tpl->getValue('item');?>

            <?php }?>
        <?php }?>

    <?php
$_smarty_tpl->setVariable('item', $foreach2Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}
if (!empty($_smarty_tpl->getValue('groups'))) {?>
    group by
    <?php if (!empty($_smarty_tpl->getValue('groups'))) {?>
        <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('groups'), 'item', true);
$_smarty_tpl->getVariable('item')->iteration = 0;
$foreach3DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('item')->value) {
$foreach3DoElse = false;
$_smarty_tpl->getVariable('item')->iteration++;
$_smarty_tpl->getVariable('item')->last = $_smarty_tpl->getVariable('item')->iteration === $_smarty_tpl->getVariable('item')->total;
$foreach3Backup = clone $_smarty_tpl->getVariable('item');
?>
            <?php if ($_smarty_tpl->getValue('item') == 'tday') {?> tday <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'cp_game_id') {?> cp_game_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'game_id') {?> game_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'app_show_id') {?> app_show_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'channel_main_id') {?> channel_main_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_channel_id') {?> promotion_channel_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'promotion_id') {?> promotion_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'user_id') {?> user_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'department_id') {?> department_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'package_id') {?> package_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'platform_id') {?> t1.platform_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'account_id') {?> account_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'account_name') {?> account_name <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'ad_account') {?> ad_account <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'campaign_id') {?> campaign_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'plan_id') {?> plan_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'creative_id') {?> creative_id <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php } elseif ($_smarty_tpl->getValue('item') == 'dim_user_os') {?> dim_user_os <?php if (!$_smarty_tpl->getVariable('item')->last) {?>, <?php }?>
            <?php }?>
        <?php
$_smarty_tpl->setVariable('item', $foreach3Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
    <?php }
}
if (!empty($_smarty_tpl->getValue('sorts'))) {?>
    order by
    <?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('sorts'), 'oo', true, 'ss');
$_smarty_tpl->getVariable('oo')->iteration = 0;
$foreach4DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('ss')->value => $_smarty_tpl->getVariable('oo')->value) {
$foreach4DoElse = false;
$_smarty_tpl->getVariable('oo')->iteration++;
$_smarty_tpl->getVariable('oo')->last = $_smarty_tpl->getVariable('oo')->iteration === $_smarty_tpl->getVariable('oo')->total;
$foreach4Backup = clone $_smarty_tpl->getVariable('oo');
?>
        <?php echo $_smarty_tpl->getValue('ss');?>
 <?php echo $_smarty_tpl->getValue('oo');?>

        <?php if (!$_smarty_tpl->getVariable('oo')->last) {?>, <?php }?>
    <?php
$_smarty_tpl->setVariable('oo', $foreach4Backup);
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);
}
if (!empty($_smarty_tpl->getValue('paginate'))) {?>
    <?php $_smarty_tpl->assign('page_size', $_smarty_tpl->getValue('paginate')['page_size'], false, NULL);?>
    <?php $_smarty_tpl->assign('page', $_smarty_tpl->getValue('paginate')['page'], false, NULL);?>
    limit <?php echo $_smarty_tpl->getValue('page_size');?>
 offset <?php echo ($_smarty_tpl->getValue('page')-1)*$_smarty_tpl->getValue('page_size');?>

<?php }
}
}

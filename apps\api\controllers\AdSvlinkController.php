<?php

namespace app\apps\api\controllers;

use app\extension\Exception\ParameterException;
use app\extension\Support\Helpers\Zakia;
use app\logic\adconf\SvlinkManagerLogic;
use app\models\baseConfPlatform\BizTags;
use app\models\baseConfPlatform\TbBaseChannelConf;
use app\models\DataSpy\TbAdSvlinkConf;
use app\models\DdcPlatform\DwsPlanAdBaseDaily;
use app\models\DdcPlatform\DwsSvkeyAdBaseDaily;
use app\models\DdcPlatform\DwsSvkeyAdPaymentDaily;
use app\service\AdConf\SvlinkServ;
use app\service\AdvertiserData\SplitNature;
use app\service\ConfigService\BasicServ;
use app\util\BizConfig;
use Plus\MVC\Controller\JsonController;

class AdSvlinkController extends JsonController
{

    /**
     * 创建短链
     * @return array
     * @throws ParameterException
     */
    public function createAction($data): array
    {
        [
            $channelId,
            $packageId,
            $userId,
            $adVersion,
        ] = [
            $data['channel_id'] ?? 0,
            $data['package_id'] ?? 0,
            $data['user_id'] ?? 0,
            $data['ad_version'] ?? 0,
        ];

        if (empty($channelId) || empty($packageId)) {
            throw new ParameterException("参数不能为空");
        }

        $bizConfig   = new BizConfig();
        $packageInfo = $bizConfig->getPackages(["package_id" => $packageId]);

        $packageInfo = $packageInfo["data"]["items"][0] ?? "";
        if (empty($packageInfo)) {
            throw new ParameterException("包号不存在！请前往业务中台配置");
        }

        $model = new TbBaseChannelConf();
        $data  = $model->find(["CHANNEL_ID" => $channelId], ["CHANNEL_CODE", "BACK_PARAM_ANDROID", "BACK_PARAM_IOS"]);
        if (!$data) {
            throw new ParameterException("渠道不存在");
        }

        //通过不同操作系统设置不同的回调参数
        if (substr($packageId, -4) === '0099') {
            $backParam = $data['BACK_PARAM_IOS'];
        }
        else {
            $backParam = $data['BACK_PARAM_ANDROID'];
        }

        $packageArr = (new BizTags())->getWeiXinXcxPackageId();
        if (in_array($packageId, $packageArr)) {
            if (substr($packageId, -4) != "0300" && substr($packageId, -4) != "0301") {
                $result = Zakia::getXcxPackages($packageId);
                if (!$result) {
                    throw new \Exception('填写包号未配置');
                }
                $packageId = $result["android"];
                $backParam .= "&ios_package_id=" . $result["ios"];
            }
        }
        if (in_array($packageId, $packageArr) && strpos($data["CHANNEL_CODE"], "xcx") === false) {
            $backParam = $data['BACK_PARAM_IOS'] . "&xcx=1";
        }
        $serviceApi = 'https://data.910app.com/ad/ad_click?package_id=' . $packageId . '&channel=';
        $svLink     = $serviceApi . $data['CHANNEL_CODE'] . $backParam;

        if ($channelId == 4 && !empty($adVersion)) {
            if ($adVersion == '3.0') {
                $svLink = $this->createGdtV3x($packageId, $data['CHANNEL_CODE']);
            }
        }

        if ($channelId == 1105 && !empty($adVersion)) {
            if ($adVersion == '3.0') {
                $svLink = $this->createGdXcxV3x($packageId, $data['CHANNEL_CODE']);
            }
        }

        if ($userId) {
            $svLink .= "&user_id=" . $userId;
        }

        return $this->success(["svlink" => $svLink]);
    }


    /**
     * 广点通 3.x
     * 暂不读中台配置持续
     * 兼容到广点通旧版接口功能关闭
     *
     * @param $packageId
     * @param $channelCode
     * @param $userId
     * @return string
     */
    protected function createGdtV3x($packageId, $channelCode): string
    {
        $serviceApi = "https://data.910app.com/ad/ad_click?package_id={$packageId}&channel={$channelCode}";

        if (Zakia::checkPackageIsIOS($packageId)) {
            $backParam = "&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&click_id=__CLICK_ID__&time=__CLICK_TIME__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&md5_idfa=__MUID__&os=__DEVICE_OS_TYPE__&ip=__IP__&advertiser_id=__ACCOUNT_ID__&ad_version=3.0&sm_format_version=2.0&creative_components_info=__CREATIVE_COMPONENTS_INFO__&element_info=__ELEMENT_INFO__";
//            $backParam = "&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&click_id=__CLICK_ID__&time=__CLICK_TIME__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&md5_idfa=__MUID__&os=__DEVICE_OS_TYPE__&ip=__IP__&advertiser_id=__ACCOUNT_ID__&ad_version=3.0&sm_format_version=2.0";
        }
        else {
            $backParam = "&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&click_id=__CLICK_ID__&time=__CLICK_TIME__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&md5_android_id=__HASH_ANDROID_ID__&md5_oaid=__HASH_OAID__&md5_imei=__MUID__&os=__DEVICE_OS_TYPE__&ip=__IP__&advertiser_id=__ACCOUNT_ID__&ad_version=3.0&sm_format_version=2.0&creative_components_info=__CREATIVE_COMPONENTS_INFO__&element_info=__ELEMENT_INFO__";
//            $backParam = "&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&click_id=__CLICK_ID__&time=__CLICK_TIME__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&md5_android_id=__HASH_ANDROID_ID__&md5_oaid=__HASH_OAID__&md5_imei=__MUID__&os=__DEVICE_OS_TYPE__&ip=__IP__&advertiser_id=__ACCOUNT_ID__&ad_version=3.0&sm_format_version=2.0";
        }

        $packageArr = (new BizTags())->getWeiXinXcxPackageId();
        if (in_array($packageId, $packageArr)) {
            if (substr($packageId, -4) != "0300" && substr($packageId, -4) != "0301") {
                $result = Zakia::getXcxPackages($packageId);
                if (!$result) {
                    throw new \Exception('填写包号未配置');
                }
                $packageId = $result["android"];
                $backParam .= "&ios_package_id=" . $result["ios"];
            }
        }

        return $serviceApi . $backParam;
    }

    /**
     * @param $packageId
     * @param $channelCode
     * @return string
     * @throws \Exception
     */
    protected function createGdXcxV3x($packageId, $channelCode): string
    {
        $serviceApi = "https://data.910app.com/ad/ad_click?package_id={$packageId}&channel={$channelCode}";
//        $backParam  = '&advertiser_id=__ACCOUNT_ID__&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&oaid=__WECHAT_OPEN_ID__&report_col_impression_id=__IMPRESSION_ID__&os=__DEVICE_OS_TYPE__&report_col_click_id=__CLICK_ID__&time=__CLICK_TIME__&report_col_request_id=__REQUEST_ID__&ad_version=3.0&sm_format_version=2.0&creative_components_info=__CREATIVE_COMPONENTS_INFO__&element_info=__ELEMENT_INFO__';
        $backParam  = '&advertiser_id=__ACCOUNT_ID__&report_col_promoted_object_id=__PROMOTED_OBJECT_ID__&campaign_id=__ADGROUP_ID__&campaign_name=__ADGROUP_NAME__&plan_id=__DYNAMIC_CREATIVE_ID__&creative_id=__DYNAMIC_CREATIVE_ID__&callback_url=__CALLBACK__&oaid=__WECHAT_OPEN_ID__&report_col_impression_id=__IMPRESSION_ID__&os=__DEVICE_OS_TYPE__&report_col_click_id=__CLICK_ID__&time=__CLICK_TIME__&report_col_request_id=__REQUEST_ID__&ad_version=3.0&sm_format_version=2.0';
        $packageArr = (new BizTags())->getWeiXinXcxPackageId();

        if (in_array($packageId, $packageArr)) {
            if (substr($packageId, -4) != "0300" && substr($packageId, -4) != "0301") {
                $result = Zakia::getXcxPackages($packageId);
                if (!$result) {
                    throw new \Exception('填写包号未配置');
                }
                $packageId = $result["android"];
                $backParam .= "&ios_package_id=" . $result["ios"];
            }
        }

        return $serviceApi . $backParam;
    }

    /**
     * @return array
     */
    public function createSvLinkAction(): array
    {
        $request     = \Plus::$app->request;
        $requestBody = \json_decode($request->getRawBody(), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON解析错误');
        }

        if (empty($requestBody['num'])) $requestBody['num'] = 1;

        try {
            $result = (new SvlinkManagerLogic())->multiInsert([$requestBody]);
        }
        catch (\Throwable $e) {
            return $this->error($e->getMessage());
        }

        return $this->success($result, 'success');
    }

}









<?php

namespace app\service\AdvertiserData\Traits;

use app\apps\internal\Helpers\ConstHub;
use app\service\AdvertiserData\Scheme\RealtimePlanScheme;
use Aura\SqlQuery\Common\SelectInterface;

trait BasicOperator
{
    protected function buildGroup($groups): \Closure
    {
        $groups = $this->changeGroups($groups);

        return function (&$query) use ($groups) {
            if (!$query instanceof SelectInterface) return;

            $query->groupBy($groups);
        };
    }

    protected function buildColumn(array $columns = ['*']): \Closure
    {
        return function (&$query) use ($columns) {
            if (!$query instanceof SelectInterface) return;
            $query->cols($columns);
        };
    }

    /**
     * @param $order
     *
     * @return \Closure
     */
    protected function buildOrder($order): \Closure
    {
        return function (&$query) use ($order) {
            if (!$query instanceof SelectInterface) return;

            $query->orderBy($order);
        };
    }

    /**
     * @param array $pagination
     *
     * @return \Closure
     */
    protected function buildPage(array $pagination): \Closure
    {
        [
            'page'      => $page,
            'page_size' => $pageSize,
        ] = $pagination;

        $offset = ($page - 1) * $pageSize;

        return function (&$query) use ($pageSize, $offset) {
            if (!$query instanceof SelectInterface) return;
            $query
                ->limit($pageSize)
                ->offset($offset);
        };
    }


    /**
     * 获取不同时间维度的时间查询方式
     *
     * @param int        $timeDimension
     * @param string     $table
     * @param string[][] $rangeDate [[{range_start}, {range_end}]]
     * @param string     $alias
     *
     * @return string
     */
    protected function getFieldForTDay(
        int $timeDimension, string $table, array $rangeDate = [], string $alias = 'tday'
    ): string
    {
        if ($timeDimension === ConstHub::DIMENSION_WEEK) {
            $dayFields = [];
            if (count($rangeDate) == count($rangeDate, 1)) {
                $rangeDate = [$rangeDate];
            }

            foreach ($rangeDate as $dayCycle) {
                sort($dayCycle);
                [$beginDate, $endDate] = $dayCycle;

                $dayFields[] = sprintf(
                    "when {$table}.tday between '%s' and '%s' then '%s'",
                    $beginDate, $endDate, $beginDate . '/' . $endDate
                );
            }

            return sprintf("CASE %s END AS {$alias}", implode(' ', $dayFields));
        }
        elseif ($timeDimension === ConstHub::DIMENSION_MONTH) {
            return "DATE_FORMAT({$table}.tday, '%Y-%m') AS {$alias}";
        }
        else {
            return "{$table}.tday AS {$alias}";
        }
    }
}
<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\AdvertiserData\Components\Matcher\CreativePayMatch;
use app\service\General\BizTagsServ;
use app\service\General\Helpers\TableConst;
use Smarty\Exception;
use Spiral\Database\Injection\Fragment;

/**
 * 付费查询
 */
class AdPaymentInfoServ
{
    /**
     * @var int QB_MODE_ALL  全关联(默认/-1)
     * @var int QB_MODE_POWER 关联权限控制的powerSql
     */
    const QB_MODE_ALL = 1023;
    const QB_MODE_CAMPAIGN = 8;
    const QB_MODE_SV_KEY = 4;
    const QB_MODE_PLAN = 2;
    const QB_MODE_CREATIVE = 1;
    const QB_MODE_POWER = 16;
    const QB_MODE_SOURCE = 64;
    const QB_MODE_SOURCE_CHANNEL = 32;
    const QB_MODE_ACCOUNT = 128;
    const QB_MODE_ADP_OAUTH = 256;
    const QB_MODE_SOURCE_PROFILE = 512;


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * @param int $mode
     *
     * @return \Spiral\Database\Query\SelectQuery
     */
    private function getPayInfoBuilder(int $mode = -1): \Spiral\Database\Query\SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from(TableConst::DWD_SDK_USER_PAYMENT . ' as user_payment');

        if ($mode & static::QB_MODE_SOURCE) {
            $qb
                ->leftJoin(TableCollect::DWD_AD_SOURCE_GAME, 'source_game')
                ->on('user_payment.source_id', 'source_game.source_id');
        }

        if ($mode & static::QB_MODE_SOURCE_PROFILE) {
            $qb
                ->leftJoin(TableCollect::DWD_SDK_AD_SOURCE_GAME_USER_PROFILE, 'source_profile')
                ->on('source_profile.source_id', 'user_payment.source_id');
        }


        if ($mode & static::QB_MODE_POWER) {
            $powerSql = str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql());
            $qb
                ->leftJoin(new Fragment($powerSql), 'power')
                ->on('source_game.package_id', 'power.package_id');
        }

        if ($mode & static::QB_MODE_SOURCE_CHANNEL) {
            $qb
                ->leftJoin(TableCollect::BASE_CHANNEL_CONF, 'source_channel')
                ->on(['source_channel.channel_id' => 'source_game.channel_id']);
        }

        if ($mode & static::QB_MODE_CAMPAIGN) {
            $qb
                ->leftJoin(TableCollect::ADP_CAMPAIGN_BASE, 'campaign_base')
                ->on([
                    'source_game.campaign_id' => 'campaign_base.campaign_id',
                    //                    'source_game.channel_id'  => 'campaign_base.channel_id',
                ]);

            if (!($mode & static::QB_MODE_PLAN)) {
                $qb
                    ->leftJoin(TableCollect::SPY_ADMIN, 'ad_admin')
                    ->on([
                        'campaign_base.user_id' => 'ad_admin.id',
                    ]);
            }
        }

        if ($mode & static::QB_MODE_PLAN) {
            $qb
                ->leftJoin(TableCollect::ADP_PLAN_BASE, 'plan_base')
                ->on([
//                    'source_game.channel_id' => 'plan_base.channel_id',
'source_game.plan_id' => 'plan_base.plan_id',
                ])
                ->leftJoin(TableCollect::SPY_ADMIN, 'ad_admin')
                ->on([
                    'plan_base.user_id' => 'ad_admin.id',
                ]);
        }

        if ($mode & static::QB_MODE_CREATIVE) {
            $qb
                ->leftJoin(TableCollect::ADP_CREATIVE_BASE, 'creative_base')
                ->on([
                    'source_game.channel_id'  => 'creative_base.channel_id',
                    'source_game.plan_id'     => 'creative_base.plan_id',
                    'source_game.creative_id' => 'creative_base.creative_id',
                ]);
        }

        if ($mode & static::QB_MODE_SV_KEY) {
            $qb
                ->leftJoin(TableCollect::SPY_SV_LINK, 'svlink')
                ->on(['source_game.sv_key' => 'svlink.id'])
                ->leftJoin(TableCollect::SPY_ADMIN, 'svlink_admin')
                ->on([
                    'svlink.user_id' => 'svlink_admin.id',
                ]);

            $qb
                ->leftJoin(TableCollect::SPY_SV_LINK, 'svlink_plan')
                ->on(['source_game.plan_id' => 'svlink_plan.id'])
                ->leftJoin(TableCollect::SPY_ADMIN, 'svlink_plan_admin')
                ->on([
                    'svlink_plan.user_id' => 'svlink_plan_admin.id',
                ]);
        }

        if ($mode & static::QB_MODE_ACCOUNT) {
            $qb
                ->leftJoin(TableCollect::BASE_ACCOUNT_CONF, 'base_account')
                ->on([
                    'base_account.channel_id' => 'campaign_base.channel_id',
                    'base_account.account_id' => 'campaign_base.advertiser_id',
                ]);
        }

        if ($mode & static::QB_MODE_ADP_OAUTH) {
            $qb
                ->leftJoin(TableCollect::ADP_OAUTH, 'adp_oauth')
                ->on([
                    'adp_oauth.channel_id'    => 'campaign_base.channel_id',
                    'adp_oauth.advertiser_id' => 'campaign_base.advertiser_id',
                ]);
        }

        return $qb;
    }

    /**
     * 获取付费详情
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $mode
     *
     * @return array
     * @throws \RedisException
     */
    public function getPaymentInfo(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $dimension = Arr::pull($params, 'dimension', 'plan');
        $target    = Arr::pull($params, 'target', 'pay_money_new');

        if (isset($params['promotion_channel_id'])) {
            $params['channel_id'] = $params['promotion_channel_id'];
        }

        if ($dimension == 'campaign') {
            $qbMode = self::QB_MODE_ALL ^ self::QB_MODE_CREATIVE ^ self::QB_MODE_PLAN ^ self::QB_MODE_SV_KEY;
        }
        else {
            $qbMode = self::QB_MODE_ALL;
        }

        $subQb = $this->getPayInfoBuilder($qbMode);

        $matcher = new CreativePayMatch([
            'cp_game_id'  => 'source_game.cp_game_id',
            'game_id'     => 'power.game_id',
            'tday'        => 'pay_time',
            'package_id'  => 'source_game.package_id',
            'plan_id'     => 'source_game.plan_id',
            'creative_id' => 'source_game.creative_id',
            'campaign_id' => 'source_game.campaign_id',
            'platform_id' => 'power.platform_id',
            'promotion_id' => 'power.popularize_v2_id',
        ]);
        // 提取部分搜索条件
        $infoParams = array_intersect_key($params, array_flip([
            'cp_game_id', 'game_id', 'package_id',
            'range_date_start', 'range_date_end', 'package_id_tags',
            'creative_id', 'campaign_id', 'package_id',
            'platform_id', 'promotion_id', 'platform_id',
            'is_has_natural', 'data_scope', 'marketing_goal'
        ]));

        $matcher->exec($subQb, $infoParams);

        if ($target === 'pay_money_new') {
            // 新增付费的条件 (付费日期和新增日期为同一天)
            $subQb->where(new Fragment('(DATE(user_payment.pay_time) = DATE(source_game.newlogin_time))'));
        }
        $subQb->where('pay_result', 1);
        $subQb->columns($this->getPaySelectFields($qbMode));

        $otherParam = array_diff_key($params, array_diff_key($infoParams, ['is_has_natural' => $params['is_has_natural']] ?? 1));
        $db         = $this->getConn();
        $wrapQb     = $db->select()->from(new Fragment('(' . $subQb->__toString() . ') as wrap'));

        $wrapQb->columns($this->getPayWrapFields($qbMode));
        $mainQb = $this->getConn()->select()->from(new Fragment('(' . $wrapQb->__toString() . ') as main_body'));

        foreach ($otherParam as $k => &$item) {
            if ($k == 'account_id' && $item == '') {
                $item = null;
            }
            if ($k == 'account_name' && $item === 0) {
                $item = null;
            }
        }

        (new CreativePayMatch())->exec($mainQb, $otherParam);

        if (!empty($groups)) {
            $wrapCols = [
                new Fragment('SUM(pay_money) as pay_money'),
                new Fragment('COUNT(1) as pay_count'),
                new Fragment('GROUP_CONCAT(distinct newlogin_time) as newlogin_time'),
                new Fragment('IF(SUM(is_exception) > 0, 1, 0) as is_exception'),
                new Fragment('IF(COUNT(distinct IF(core_account != main_account, core_account, null)) > 0 , 1, 0) as has_children')
            ];

            $wrapCols = array_merge($wrapCols, $groups);

            foreach ($groups as $g) {
                $mainQb->groupBy($g);
            }

            $mainQb->columns($wrapCols);
        }

        $total = $this->getConn()->select()->from(new Fragment('(' . $mainQb->__toString() . ') as total_body'))->count();


        if (!empty($sort)) {
            $mainQb->orderBy($sort);
        }

        return [
            'list'  => $mainQb->fetchAll(),
            'total' => $total,
        ];
    }

    /**
     * @param int $qbMode
     *
     * @return array
     * @throws \RedisException
     */
    private function getPaySelectFields(int $qbMode): array
    {

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);

        $channelString     = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(source_game.channel_id != 0, IF(source_game.channel_id=1013,4,source_game.channel_id), POWER.channel_id)) AS channel_id",
            $planChannelsString
        );
        $channelMainString = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(source_channel.channel_main_id != 0, source_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );


        $columns = [
            'cp_game_id'            => 'source_game.cp_game_id as cp_game_id',
            'game_id'               => 'source_game.game_id as game_id',
            'package_id'            => 'source_game.package_id as package_id',
            'campaign_id'           => 'source_game.campaign_id as campaign_id',
            'creative_id'           => 'source_game.creative_id as creative_id',
            'plan_id'               => new Fragment('IF(source_channel.IS_USE_SV_KEY=1, source_game.sv_key,source_game.plan_id) as plan_id'),
            'main_account'          => 'source_game.main_account as main_account',
            //            'channel_id'            => new Fragment('source_game.channel_id as channel_id'),
            'channel_id'            => new Fragment($channelString),
            //            'plan_id'         => new Fragment('COALESCE(IF(COALESCE(source_game.plan_id, 0) != 0, source_game.plan_id, source_game.sv_key) ,0) AS plan_id'),
            //            'channel_id'      => new Fragment('COALESCE(IF(power.channel_id NOT IN (6568, 6822, 5329), power.channel_id, IF(source_game.channel_id != 0, source_game.channel_id, power.channel_id)), 0) as channel_id'),
            //            'channel_main_id' => new Fragment('COALESCE(IF(power.channel_id NOT IN (6568, 6822, 5329), power.channel_main_id, IF(source_channel.channel_main_id != 0, source_channel.channel_main_id, power.channel_main_id)), 0) as channel_main_id'),
            //            'channel_main_id'       => new Fragment('source_channel.channel_main_id as channel_main_id'),
            'channel_main_id'       => new Fragment($channelMainString),
            'platform_id'           => 'power.platform_id as platform_id',
            'app_show_id'           => 'power.app_show_id as app_show_id',
            'promotion_id'          => 'power.popularize_v2_id as promotion_id',
            'pay_money'             => 'user_payment.money as pay_money',
            'newlogin_time'         => 'source_game.newlogin_time as newlogin_time',
            'power_channel_id'      => 'power.channel_id as power_channel_id',
            'power_channel_main_id' => 'power.channel_main_id as power_channel_main_id',
            'source_svkey'          => 'source_game.sv_key as source_svkey',
            'power_user_id'         => 'power.ad_user_id as power_user_id',
            'power_department_id'   => 'power.ad_department_id as power_department_id',
            'ad_user_id'            => 'ad_admin.id as ad_user_id',
            'ad_department_id'      => 'ad_admin.department_id as ad_department_id',
            'campaign_name'         => 'campaign_name',
            'creative_name'         => 'creative_name',
            'plan_name'             => 'plan_name',
            'svlink_name'           => 'svlink.aid as svlink_name',

            //            'svlink_account_id'   => 'svlink_account.account_id as svlink_account_id',
            //            'svlink_account_name' => 'svlink_adp_oauth.advertiser_name as svlink_account_name',
            'account_id'            => 'base_account.account_id as account_id',
            'account_name'          => 'adp_oauth.advertiser_name as account_name',
            'svlink_user'           => new Fragment('IF(svlink.user_id = 0 or svlink.user_id is null, svlink_plan.user_id,  svlink.user_id) as svlink_user'),
            'svlink_department'     => new Fragment('IF(svlink_admin.department_id = 0 or svlink_admin.department_id is null, svlink_plan_admin.department_id, svlink_admin.department_id) as svlink_department'),
            'is_exception'          => new Fragment('COALESCE(IF(source_profile.BUY_GIFT_PACK_IN_NEW_2DAYS = 1 or source_profile.CREATE_ROLE_PAY_IN_5_MIN = 1, 1, 0), 0) as is_exception'),
            'core_account'          => 'user_payment.core_account as core_account'
        ];

        if (!($qbMode & static::QB_MODE_PLAN)) {
            unset($columns['plan_id']);
            unset($columns['plan_name']);
            unset($columns['svlink_name']);
            unset($columns['svlink_user']);
            unset($columns['svlink_department']);
        }

        if (!($qbMode & static::QB_MODE_CREATIVE)) {
            unset($columns['creative_name']);
            unset($columns['creative_id']);
        }

        return array_values($columns);
    }


    /**
     * @param int $qbMode
     *
     * @return array
     */
    private function getPayWrapFields(int $qbMode): array
    {
        $columns = [
            'cp_game_id'      => 'cp_game_id',
            'game_id'         => 'game_id',
            'package_id'      => 'package_id',
            'campaign_id'     => 'campaign_id',
            'creative_id'     => 'creative_id',
            'plan_id'         => new Fragment('COALESCE(if (COALESCE(plan_id, 0) != 0, plan_id, if (COALESCE(creative_id, 0) != 0, plan_id, source_svkey)) ,0) as plan_id'),
            'main_account'    => 'main_account',
            'channel_id'      => new Fragment('COALESCE(if (power_channel_id NOT IN(6568, 6822, 5329), power_channel_id, if (channel_id != 0, channel_id, power_channel_id)), 0) as channel_id'),
            'channel_main_id' => new Fragment('COALESCE(if (power_channel_id NOT IN(6568, 6822, 5329), power_channel_main_id, channel_main_id ) ) as channel_main_id'),
            'platform_id'     => 'platform_id',
            'app_show_id'     => 'app_show_id',
            'promotion_id'    => 'promotion_id',
            'pay_money'       => 'pay_money',
            'newlogin_time'   => 'newlogin_time',
            'user_id'         => new Fragment('COALESCE(if (power_channel_id NOT IN(6568, 6822, 5329), power_user_id, if (COALESCE(ad_user_id, 0) != 0, ad_user_id, IF(COALESCE(svlink_user, 0) != 0, svlink_user, power_user_id))), 0) as user_id'),
            'department_id'   => new Fragment('COALESCE(if (power_channel_id NOT IN(6568, 6822, 5329), power_department_id, if (COALESCE(ad_department_id, 0) != 0, ad_department_id, IF(COALESCE(svlink_user, 0) != 0, svlink_department, power_department_id) )), 0) as department_id'),
            'creative_name'   => 'creative_name',
            'plan_name'       => new Fragment("COALESCE(IF(COALESCE(plan_id, 0) != 0, plan_name, IF(COALESCE(creative_id,0) != 0, plan_name, svlink_name)) , '') AS plan_name"),
            'campaign_name'   => 'campaign_name',
            'is_exception'    => 'is_exception',
            'core_account'    => 'core_account',
        ];

        if (!($qbMode & static::QB_MODE_CREATIVE)) {
            unset($columns['creative_id']);
            unset($columns['creative_name']);
        }

        if (!($qbMode & static::QB_MODE_PLAN)) {
            unset($columns['plan_id']);
            unset($columns['plan_name']);
        }

//        if ($qbMode & static::QB_MODE_SV_KEY) {
//            $columns['account_id']   = new Fragment("COALESCE(IF(COALESCE(plan_id, 0) != 0, account_id, IF(COALESCE(creative_id,0) != 0, account_id, svlink_account_id)) , '') AS account_id");
//            $columns['account_name'] = new Fragment("COALESCE(IF(COALESCE(plan_id, 0) != 0, account_name, IF(COALESCE(creative_id,0) != 0, account_name, svlink_account_name)) , '') AS account_name");
//        }
//        else {
//            $columns['account_id']   = 'account_id';
//            $columns['account_name'] = 'account_name';
//        }


        $columns['account_id']   = 'account_id';
        $columns['account_name'] = 'account_name';

        return array_values($columns);
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws Exception
     */
    public function paymentInfoGroupCoreAccount(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $db       = $this->getConn();
        $tpl      = \Plus::$app->sqlTemplates->createTemplate('sql/ad/ad_payment/ad_payment_group_core.tpl');
        $countTpl = \Plus::$app->sqlTemplates->createTemplate('sql/ad/ad_payment/count_ad_payment_group_core.tpl');

        $tpl
            ->assign('power_sql', str_replace('POWER', 'power', \Plus::$service->admin->getAdminPowerSql()))
            ->assign('params', $params)
            ->assign('group_by', implode(',', $groups))
            ->assign('paginate', $paginate);

        $countTpl
            ->assign('power_sql', str_replace('POWER', 'power', \Plus::$service->admin->getAdminPowerSql()))
            ->assign('params', $params)
            ->assign('group_by', implode(',', $groups));

        $list     = $db->query($tpl->fetch())->fetchAll();
        $countRow = $db->query($countTpl->fetch())->fetch();

        return [
            'list'  => $list,
            'total' => $countRow['count_rows'] ?? 0
        ];
    }


}
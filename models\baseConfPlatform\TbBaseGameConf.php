<?php

namespace app\models\baseConfPlatform;

use app\util\Common;
use Plus\MVC\Model\ActiveRecord;

/**
 * games
 *
 * @property int    $id             自增ID(CHANNEL_ID)
 * @property string $GAME_ID        游戏id
 * @property string $SM_GAME_NAME   游戏统计名
 * @property string $CP_GAME_ID     游戏原名ID
 * <AUTHOR>
 */
class TbBaseGameConf extends ActiveRecord
{
    public $_primaryKey = 'id';

    /**
     * 初始化，设置数据、数据库连接类
     *
     * @param array $data 批量配置数据
     *
     */
    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->base_conf_platform;
    }

    /**
     * 获取游戏ids
     *
     * @param $where
     *
     * @return TbOathConf
     */
    public function getAllGameIds($where)
    {
        $list = $this->asArray()->findAll($where);
        return array_column($list, 'GAME_ID');
    }

    /**
     * @param array $gameNames
     *
     * @return array
     */
    public function getGameIdByNames(array $gameNames): array
    {
        $sql = "
        SELECT game_name, game_id
        FROM base_conf_platform.tb_base_game_conf
        WHERE is_show = 1;
        ";

        if ($_db = Common::pingDoris()) {
            $this->_db = $_db;
        }

        $gameList = $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $gameList = array_column($gameList, 'game_id', 'game_name');

        return array_intersect_key($gameList, array_flip($gameNames));
    }
}

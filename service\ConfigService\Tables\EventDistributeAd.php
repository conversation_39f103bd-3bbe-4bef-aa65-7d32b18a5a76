<?php

namespace app\service\ConfigService\Tables;

use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;


class EventDistributeAd implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    /**
     * @param $options
     *
     * @return \app\extension\Support\Collections\Collection
     */
    public function getFields($options = null): \app\extension\Support\Collections\Collection
    {
        $fields = collect([
            'tday'            => ['title' => '日期', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'cp_game_id'      => ['title' => '游戏原名', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'game_id'         => ['title' => '游戏统计名', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'channel_main_id' => ['title' => '主渠道', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'channel_id'      => ['title' => '推广子渠道', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'package_id'      => ['title' => '包号', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'platform_id'     => ['title' => '客户端', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'promotion_id'    => ['title' => '推广分类', 'sorter' => true, 'classify' => ['attrs', 'base']],
        ]);

        $eventType = (int)($options['event_type'] ?? 1);
        $groupType = (int)($options['group_type'] ?? 1);

        if (1 === $eventType) {
            $fields = $fields->put('event_sum', ['title' => '活跃用户', 'sorter' => true, 'classify' => ['attrs', 'event_index']]);
        }
        elseif (2 === $eventType) {
            $fields = $fields->put('event_sum', ['title' => '付费用户', 'sorter' => true, 'classify' => ['attrs', 'event_index']]);
        }
        elseif (3 === $eventType) {
            $fields = $fields->put('event_sum', ['title' => '付费金额', 'sorter' => true, 'classify' => ['attrs', 'event_index']]);
        }
        elseif (991 === $eventType) {
            $fields = $fields->put('event_sum', ['title' => '活跃付费率', 'sorter' => true, 'classify' => ['attrs', 'event_index']]);
        }
        elseif (992 === $eventType) {
            $fields = $fields->put('event_sum', ['title' => '活跃ARPU', 'sorter' => true, 'classify' => ['attrs', 'event_index']]);
        }

        $rangeNode = [1, 60];
        $suffix    = '天';

        $timeDimension = (int)($options['range_date_dimension'] ?? 2);

        if ($timeDimension === 3 || $timeDimension === 4) {
            // 固定按周或按月只能展示'自然月'
            $groupType = 2;
        }

        if (2 === $groupType) {
            // 自然月
            $rangeNode = [1, 24];
            $suffix    = '月';
        }

        $fields = $fields->put('node_0', ['title' => '当' . $suffix, 'sorter' => true, 'classify' => ['attrs', 'time_range_index']]);

        for ($i = $rangeNode[0]; $i <= $rangeNode[1]; $i++) {
            $fields = $fields->put('node_'.$i, ['title' => "{$i}{$suffix}前", 'classify' => ['attrs', 'time_range_index']]);
        }
        $fields = $fields->put('node_1000', ['title' => "{$rangeNode[1]}+{$suffix}前", 'classify' => ['attrs', 'time_range_index']]);

        return $this->formatStandard($fields);
    }
}
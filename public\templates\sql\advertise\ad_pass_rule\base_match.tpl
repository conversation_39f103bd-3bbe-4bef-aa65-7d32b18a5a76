{if isset($params) && !empty($params)}
    where true
    {if !empty($params['range_time'])}
        and t_pass.tday between '{$params['range_time'][0]} 00:00:00' and '{$params['range_time'][1]} 23:59:59'
    {/if}
    {* 游戏原名 *}
    {if !empty($params['cp_game_id'])}
        and t_pass.cp_game_id in ({$params['cp_game_id']})
    {/if}
    {* 游戏统计名 *}
    {if !empty($params['game_id'])}
        and t_pass.game_id in ({$params['game_id']})
    {/if}
    {* 包号 *}
    {if !empty($params['package_id'])}
        and t_pass.package_id in ({$params['package_id']})
    {/if}
    {* 主渠道搜索 *}
    {if !empty($params['channel_main_id'])}
        and base_channel.channel_main_id in ({$params['channel_main_id']})
    {/if}
    {* 推广子渠道搜索 *}
    {if !empty($params['channel_id'])}
        and IF(t_pass.channel_id = 0, power.channel_id, t_pass.channel_id) in ({$params['channel_id']})
    {/if}
    {* 客户端 *}
    {if !empty($params['platform_id'])}
        and power.platform_id in ({$params['platform_id']})
    {/if}
    {* 游戏前端名 *}
    {if !empty($params['app_show_id'])}
        and power.app_show_id in ({$params['app_show_id']})
    {/if}
    {* 推广分类 *}
    {if !empty($params['promotion_id'])}
        and power.popularize_v2_id in ({$params['promotion_id']})
    {/if}
    {* 投放部门 *}
    {if !empty($params['department_id'])}
        and t_campaign.user_id IN (SELECT id from dataspy.admin_user where department_id IN ({$params['department_id']}) )
    {/if}
    {* 投放人 *}
    {if !empty($params['user_id'])}
        and t_campaign.user_id in ({$params['user_id']})
    {/if}
    {* 投放账号 *}
    {if !empty($params['ad_account'])}
        and t_pass.ad_account like '%{$params['ad_account']}%'
    {/if}
    {* 账号ID *}
    {if !empty($params['account_id'])}
        and t_pass.account_id like '%{$params['account_id']}%'
    {/if}
    {* 广告组名称 *}
    {if !empty($params['campaign_name'])}
        and campaign_name like '%{$params['campaign_name']}%'
    {/if}
    {* 广告组ID *}
    {if !empty($params['campaign_id'])}
        {if $params['campaign_id'] eq ''}
            and t_pass.campaign_id IN ('', 0)
        {else}
            and t_pass.campaign_id in ({$params['campaign_id']})
        {/if}
    {/if}
    {* 计划名称 *}
    {if !empty($params['plan_name'])}
        and plan_name like '%{$params['plan_name']}%'
    {/if}
    {* 计划id  *}
    {if isset($params['plan_id'])}
        {if $params['plan_id'] eq ''}
            and t_pass.plan_id IN ('', 0)
        {else}
            and t_pass.plan_id in ({$params['plan_id']})
        {/if}
    {/if}

    {if isset($params['role_type'])}
        and t_pass.role_type in ({$params['role_type']})
    {/if}

    {if isset($params['account_name'])}
        and adp_oauth.advertiser_name like '%{$params['account_name']}%'
    {/if}


{/if}
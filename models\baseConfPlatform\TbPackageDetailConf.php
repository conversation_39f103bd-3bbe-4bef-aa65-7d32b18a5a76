<?php


namespace app\models\baseConfPlatform;

use Plus\MVC\Model\ActiveRecord;

/**
 * @TbPackageDetailConf
 *                     包号表详细信息表（整合包号信息、渠道信息、投放人信息）
 *
 * @property int    ID
 * @property int    CP_GAME_ID
 * @property int    GAME_ID
 * @property int    PACKAGE_ID
 * @property int    CHANNEL_MAIN_ID
 * @property int    CHANNEL_ID
 * @property int    POPULARIZE_V1_ID
 * @property int    PLATFORM_ID
 * @property int    AD_USER_ID
 * @property int    AD_DEPARTMENT_ID
 * @property int    IS_MULTIPLE
 * @property string ADD_TIME
 * @property string UPDATE_TIME
 * @property int    APP_SHOW_ID
 */
class TbPackageDetailConf extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->base_conf_platform;
        parent::__construct($data);
    }


    /**
     * 根据包号获取游戏原名、统计名、渠道
     *
     * @param $packages
     *
     * @return array[]
     * @throws \Exception
     */
    public function getGameChannelForPackages($packages)
    {
        $cpGameIds      = [];
        $gameIds        = [];
        $channelMainIds = [];
        if (!is_array($packages)) {
            throw new \Exception("packages must be array.");
        }
        $data = $this->asArray()->findAll(["PACKAGE_ID" => $packages], ["CP_GAME_ID", "GAME_ID", "CHANNEL_MAIN_ID"]);
        if ($data) {
            $cpGameIds      = array_unique(array_column($data, "CP_GAME_ID"));
            $gameIds        = array_unique(array_column($data, "GAME_ID"));
            $channelMainIds = array_unique(array_column($data, "CHANNEL_MAIN_ID"));
        }

        return [
            "cp_game_ids"      => $cpGameIds,
            "game_ids"         => $gameIds,
            "channel_main_ids" => $channelMainIds,
        ];
    }


    /**
     * @param $packageId
     *
     * @return array
     */
    public function getInfoByPackageId($packageId): array
    {
        return $this
            ->asArray()
            ->findAll(
                [
                    'package_id'    => $packageId,
                    'package_id[>]' => 9,
                ],
                [
                    'package_id',
                    'tb_package_detail_conf.game_id',
                    'game_name',
                    'tb_package_detail_conf.channel_id',
                    'channel_name',
                    'tb_package_detail_conf.channel_main_id',
                    'channel_main_name',
                    'tb_package_detail_conf.platform_id(os)',
                    'tb_package_detail_conf.cp_game_id',
                ],
                [
                    '[>]tb_base_channel_conf' => ['tb_package_detail_conf.channel_id' => 'channel_id'],
                    '[>]tb_base_game_conf'    => ['tb_package_detail_conf.game_id' => 'game_id'],
                ]
            );
    }

}

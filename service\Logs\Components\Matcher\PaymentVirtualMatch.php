<?php

namespace app\service\Logs\Components\Matcher;

use app\extension\Support\Helpers\DBHelper\MatcherAbstract;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\SelectQuery;

class PaymentVirtualMatch extends MatcherAbstract
{

    protected function matchFnList(): array
    {
        return [
            'login_account' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'core_account'  => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'package_id'    => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'upload_status' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'upload_time'   => function (SelectQuery &$qb, $key, $value) {
                sort($value);
                [$start, $end] = $value;
                $start .= ' 00:00:00';
                $end   .= ' 23:59:59';

                $qb->where($key, 'between', $start, $end);
            }
        ];
    }
}
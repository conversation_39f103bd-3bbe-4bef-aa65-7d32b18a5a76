<?php

namespace app\apps\internal\Helpers;

use MathParser\Interpreting\Evaluator;
use MathParser\StdMathParser;

class RemainCalculator
{
    /**
     * 留存默认展示方式
     */
    public const REMAIN_VAL_DEFAULT = 0;
    /**
     * 留存展示方式(只展示登录数)
     */
    public const REMAIN_VAL_NUM = 1;
    /**
     * 都展示
     */
    public const REMAIN_VAL_BOTH = 2;


    /**
     * 留存每行的计算处理过程
     *
     * @param \Closure $getUserFunc
     * @param array    $options
     * @param string   $field
     *
     * @return \Closure
     */
    public static function calcEachRow(
        \Closure $getUserFunc, array $options = [], int $remainType = self::REMAIN_VAL_DEFAULT, string $field = 'login_num'
    ): \Closure
    {
        $maxDays = $options['max_days'] ?? 0;

        return function (&$target) use ($getUserFunc, $field, $maxDays, $remainType) {
            $remainInfo = $target['remain_info'] ?? [];

            if (!empty($remainInfo)) {
                static::calcRemain($target, $remainInfo, $getUserFunc, $remainType, $field);
            }
        };
    }

    /**
     * 留存具体计算过程
     *
     * @param          $target
     * @param array    $remainInfo
     * @param \Closure $getUserFunc
     * @param string   $field
     *
     * @return void
     */
    public static function calcRemain(
        &$target, array $remainInfo, \Closure $getUserFunc, int $remainType = self::REMAIN_VAL_DEFAULT, string $field = 'login_num'
    )
    {
        $remainInfo = array_column($remainInfo, null, 'day_type');
        ksort($remainInfo);

        $remainValueFn = static::remainValueGet($remainType);

        foreach ($remainInfo as $i => $foo) {
            $x = $foo[$field] ?? 0;
            $y = $getUserFunc($target, $i);

            if ($i == 1000) {
                $target['remain_current'] = $foo[$field];
            }

            $target['remain_' . $i] = $remainValueFn($x, $y);
        }
    }

    /**
     * 不同的留存展示方式
     *
     * @param int $remainType
     *
     * @return \Closure
     */
    protected static function remainValueGet(int $remainType): \Closure
    {
        if (static::REMAIN_VAL_NUM === $remainType) {
            return fn($loginNum) => $loginNum;
        }
        elseif (static::REMAIN_VAL_BOTH === $remainType) {
            return function ($loginNum, $userNew) {
                if (empty($loginNum) || empty($userNew)) {
                    $percentN = '0.00%';
                }
                else {
                    $percentN = number_format(math_eval('x/y * 100', ['x' => $loginNum, 'y' => $userNew]), 2) . '%';
                }

                return $loginNum . "({$percentN})";
            };
        }
        else {
            return function ($loginNum, $userNew) {
                if (empty($loginNum) || empty($userNew)) return '0.00%';
                return number_format(math_eval('x/y*100', ['x' => $loginNum, 'y' => $userNew]), 2) . '%';
            };
        }
    }
}
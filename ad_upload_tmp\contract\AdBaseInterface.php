<?php

namespace app\ad_upload_tmp\contract;

use app\ad_upload_tmp\tool\ChannelFactory;
use app\ad_upload_tmp\tool\Http;

/**
 * 上报接口
 * <AUTHOR>
 */
abstract class AdBaseInterface
{
    use LogDbTrait;
    //上报类型
    const ACTION_ACTIVE      = 'active';
    const ACTION_REGISTER    = 'register';
    const ACTION_LOGIN       = 'login';
    const ACTION_PAY         = 'pay';
    const ACTION_REMAIN      = 'remain';
    const ACTION_CREATE_ROLE = 'create_role';
    const ACTION_PAY_VIRTUAL = 'pay_virtual_upload';
    /**
     * 日志存储目录
     */
    const LOG_DIR     = 'ad_upload';
    const LOG_DIR_ERR = 'ad_upload_err';

    /**
     * 上报激活
     * @param array $info 数据
     * @param array $ext  扩展
     * @return void
     */
    abstract public function uploadActive($info, $ext = []);


    /**
     * 上报注册
     * @param array $info 数据
     * @param array $ext  扩展
     * @return void
     */
    abstract public function uploadRegister($info, $ext = []);

    /**
     * 上报登录
     * @param array $info 数据
     * @param array $ext  扩展
     * @return void
     */
    abstract public function uploadLogin($info, $ext = []);

    /**
     * 上报充值
     * @param array $info 数据
     * @param array $ext  扩展
     * @return void
     */
    abstract public function uploadPay($info, $ext = []);

    /**
     * 上报创角
     * @param array $info 数据
     * @param array $ext  扩展
     * @return void
     */
    abstract public function uploadCreateRole($info, $ext = []);


    /**
     * 上报留存
     * @param array $info 数据
     * @param array $ext  扩展
     * @return void
     */
    abstract public function uploadRemain($info, $ext = []);

    /**
     * 前置处理
     * @var \app\ad_upload\contract\AdUploadStrategyInterface::uploadData 中调用
     *
     * @param array  $info         data
     * @param string $uploadMethod 上传方法
     * @return bool|array
     */
    public function uploadBefore($info, $uploadMethod)
    {
        if ($uploadMethod == 'uploadPay') {
            $behavior = $info['paid_report_log']['reported_behavior'] ?? 0;
            //上报行为类别(0-正常上报, 1-次数扣减, 2-金额扣减,3-虚拟订单上报)
            if ($behavior == 1) {
                \Plus::$app->log->info(json_encode($info).'扣量上报', [], self::LOG_DIR);
                //扣量上报，记录日志就返回
                $this->logPaidToDoris($info, ['channel_code'=>ChannelFactory::getChannelCode($info['CHANNEL_ID'])], '');
                return false;
            }
        }
        return $info;
    }

    /**
     * 保存日志到数据库
     * @param array  $info        上报数据
     * @param array  $logInfo     `bigdata_dwd`.`dwd_reported_platform_log` 日志
     * @param string $res         回调结果
     * @param string $callbackUrl 回调地址
     * @return void
     */
    public function log($info, $logInfo, $res, $callbackUrl)
    {
        // 文本日志
        $logData = [
            'action_id' => $logInfo['action_id'] ?? ($info['ID'] ?? 0),
            'prefix'    => $logInfo['channel_code'] . '_' . $logInfo['action'],
            'result'    => $info['PACKAGE_ID'] . "包 上报结果：" . $res . "，上报参数：" . $callbackUrl,
        ];
        \Plus::$app->log->info($logData, [], self::LOG_DIR, 3);

        if ($logInfo['reported_status'] == 1) {
            $message = '上报成功';
        } else {
            $message = '上报失败';
        }
        $this->recordUploadResult($info['EXT_ID'], $message, $res);
        $this->logDoris($logInfo);
        //$this->logPaidToDoris($info, $logInfo, $callbackUrl);
        $this->updatePaymentUploadVirtual($info, $callbackUrl, $logInfo['reported_status'] == 1);
    }

    /**
     * 记录上报结果
     * @param int    $id      id
     * @param string $message 消息
     * @param string $result  结果
     * @return mixed
     */
    private function recordUploadResult($id, $message, $result)
    {
        $notice = $message == '上报成功' ? "{$message}：" : "{$message}：{$result} ";

        return \Plus::$app->dataspy->update('tb_ad_data_upload_conf', [
            'NOTICE' => $notice . date("Y-m-d H:i:s"),
        ], ['id' => $id]);
    }

    /**
     * 更新虚拟增量上报结果
     * @param array  $info        data
     * @param string $callbackUrl callback
     * @param bool   $ok          是否成功
     * @return void
     */
    private function updatePaymentUploadVirtual($info, $callbackUrl, $ok)
    {
        if (isset($info['UPLOAD_VIRTUAL']) && $info['UPLOAD_VIRTUAL'] == 1) {
            $rowId = $info['ID'] ?? '';
            $now   = date('Y-m-d H:i:s');

            if (!empty($rowId)) {
                if ($ok) {
                    $ext = json_encode([
                        'report_result' => '上报成功',
                        'report_param'  => [],
                        'report_url'    => $callbackUrl,
                    ], JSON_UNESCAPED_UNICODE);
                    $sql = "UPDATE ddc_platform.dwd_user_payment_upload_virtual set UPLOAD_STATUS =1, EXT = '{$ext}', LOG_REPORT_TIME='{$now}' where ID = {$rowId}";
                } else {
                    $ext = json_encode([
                        'report_result' => '上报失败',
                        'report_param'  => [],
                        'report_url'    => $callbackUrl,
                    ], JSON_UNESCAPED_UNICODE);

                    $sql = "UPDATE ddc_platform.dwd_user_payment_upload_virtual set UPLOAD_STATUS =999, EXT = '{$ext}', LOG_REPORT_TIME='{$now}' where ID = {$rowId}";
                }
                \Plus::$app->ddc_platform->exec($sql);
            }
        }// end if()
    }

    /**
     * 广告主token 缓存
     * @var array $advertiserIds
     */
    private $advertiserIds = [];

    /**
     * 获取广告主token
     * @param string $advertiserId 广告主id
     * @return mixed
     */
    private function getAuthToken($advertiserId)
    {
        if (empty($advertiserId)) {
            return '';
        }
        if (!empty($this->advertiserIds)) {
            return $this->advertiserIds[$advertiserId];
        }
        $sql                                = "SELECT
                  b.access_token
                FROM
                  adp_platform.tb_adp_oauth AS a
                  LEFT JOIN adp_platform.tb_adp_oauth AS b
                    ON a.PARENT_ADVERTISER = b.ADVERTISER_ID
                WHERE a.ADVERTISER_ID = '$advertiserId'";
        $token                              = \Plus::$app->adp_platform->query($sql)->fetchColumn();
        $this->advertiserIds[$advertiserId] = $token;
        return $token;
    }

    /**
     * 广点通post请求， 增加鉴权
     * @param string $url        url
     * @param string $account_id 广告主id
     * @param array  $data       上报数据
     * @return bool|string
     * @throws \Exception
     */
    protected function gdtPost($url, $account_id, $data)
    {
        $urlInfo  = parse_url($url);
        $needAuth = $urlInfo['host'] != "tracking.e.qq.com";
        $headers  = [
            "Content-type: application/json;charset='utf-8'",
            "Accept: application/json",
        ];
        if ($needAuth) {
            $token = $this->getAuthToken($account_id);
            if (empty($token)) {
                \Plus::$app->log->alert("广告主token获取失败:".$account_id, [], self::LOG_DIR);
                return "广告主token获取失败:".$account_id;
            }
            $headers[] = "access-token: $token";
            $headers[] = "timestamp: ".time();
            $headers[] = "nonce: ".uniqid();
        }
        $http = new Http($url);
        return $http->post($data, false, $headers);
    }
}

<?php

namespace app\service\AdvertiserData\Components\Helpers;

use app\extension\Support\Collections\Arr;

/**
 * @Convert 转换部分查询条件的工具
 */
class Convert
{
    /**
     * 将数据转换为where in ($data) 数值
     *
     * @param        $data
     * @param string $separator
     *
     * @return string
     */
    public static function convertInString($data, string $separator = ','): string
    {
        if (!static::isValueAble($data)) return '';

        if (is_string($data)) {
            if (str_contains($data, ',')) {
                $data = \explode(',', $data);
            }
            else {
                $data = Arr::wrap($data);
            }
        }

        if (is_numeric($data)) {
            $data = Arr::wrap($data);
        }

        array_walk($data, fn(&$value) => $value = "'{$value}'");

        return implode($separator, $data);
    }

    /**
     * @param        $data
     * @param string $separator
     *
     * @return array
     */
    public static function splitArray($data, string $separator = ','): array
    {
        if (is_array($data)) {
            return $data;
        }

        if (is_string($data)) {
            if (str_contains($data, $separator)) {
                $data = \explode($separator, $data);
            }
        }

        return Arr::wrap($data);
    }


    /**
     * 判断数据类型
     *
     * @param $data
     *
     * @return bool
     */
    public static function isValueAble($data): bool
    {
        return is_string($data)
            || is_numeric($data)
            || is_array($data);
    }

}
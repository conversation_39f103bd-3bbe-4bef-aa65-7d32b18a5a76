with log_order_list as (
select
order_id,
reported_money,
actually_money,
reported_status,
`time` as reported_time,
source_dimension,
reported_behavior,
reported_rule_id,
reported_behavior_rule,
no_reported_origin,
callback_url,
`content`,
response_content,
pay_time
from bigdata_dwd.dwd_reported_paid_platform_log
{* 筛选条件 *}
{if !empty($params)}
    {assign var="first_mark_1" value=1}
    {foreach $params as $kk => $foo}
        {* 付费时间 *}
        {if $kk eq "order_date"}
            {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
            DATE(`pay_time`) between '{$foo[0]}' and '{$foo[1]}'
            {continue}
        {/if}
        {* 充值订单 *}
        {if $kk eq "order_id"}
            {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
            {if is_array($foo)}
                order_id in ('{$foo|join:'\' ,\''}')
            {else}
                order_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {* 上报状态 *}
        {if $kk eq "reported_status"}
            {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
            {if is_array($foo)}
                reported_status in ('{$foo|join:'\' ,\''}')
            {else}
                reported_status = '{$foo}'
            {/if}
            {continue}
        {/if}
        {* 上报行为类别 *}
        {if $kk eq "reported_behavior"}
            {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
            {if is_array($foo)}
                reported_behavior in ('{$foo|join:'\' ,\''}')
            {else}
                reported_behavior = '{$foo}'
            {/if}
            {continue}
        {/if}
        {* 上报规则id *}
        {if $kk eq "reported_rule_id"}
            {if !$first_mark_1} and {else} where {$first_mark_1=0} {/if}
            {if is_array($foo)}
                reported_rule_id in ('{$foo|join:'\' ,\''}')
            {else}
                reported_rule_id = '{$foo}'
            {/if}
            {continue}
        {/if}
    {/foreach}
{/if}
),
increment_order_list as (
select * from ddc_platform.dwd_sdk_user_payment
{* 筛选条件 *}
{if !empty($params)}
    {assign var="first_mark_2" value=1}
    {foreach $params as $kk => $foo}
        {* 付费时间 *}
        {if $kk eq "order_date"}
            {if !$first_mark_2} and {else} where {$first_mark_2=0} {/if}
            DATE(`pay_time`) between '{$foo[0]}' and '{$foo[1]}'
            {continue}
        {/if}
        {* 充值订单 *}
        {if $kk eq "order_id"}
            {if !$first_mark_2} and {else} where {$first_mark_2=0} {/if}
            {if is_array($foo)}
                order_id in ('{$foo|join:'\' ,\''}')
            {else}
                order_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {* 包号(充值订单) *}
        {if $kk eq "order_package_id"}
            {if !$first_mark_2} and {else} where {$first_mark_2=0} {/if}
            {if is_array($foo)}
                package_id in ('{$foo|join:'\' ,\''}')
            {else}
                package_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {* 核心账号(充值订单) *}
        {if $kk eq "order_core_account"}
            {if !$first_mark_2} and {else} where {$first_mark_2=0} {/if}
            {if is_array($foo)}
                core_account in ('{$foo|join:'\' ,\''}')
            {else}
                core_account = '{$foo}'
            {/if}
            {continue}
        {/if}
    {/foreach}
{/if}
)
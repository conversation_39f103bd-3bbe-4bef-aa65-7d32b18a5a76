<?php
namespace app\ad_upload\tool;

use app\ad_upload\contract\AdBaseInterface;
use Plus\Util\StringUtil;

/**
 * 渠道工厂
 */
class ChannelFactory
{

    /**
     * @var array 渠道类 缓存
     */
    private static $adChannelArr = [];
    /**
     * @var array 渠道code 缓存
     */
    private static $adCodeArr = [];


    /**
     * 根据渠道id获取平台类
     * @param int $channelId
     * @return AdBaseInterface
     */
    public static function getChannel($channelId)
    {
        $code = self::getChannelCode($channelId);
        return self::getChannelByCode($code);
    }

    /**
     * 根据渠道id获取渠道code
     * @param int $channelId 渠道id
     * @return string
     */
    public static function getChannelCode($channelId)
    {
        $code = self::$adCodeArr[$channelId] ?? false;
        if (!$code) {
            $code                        = \Plus::$app->base_conf_platform->get('tb_base_channel_conf', 'channel_code', ['channel_id'=>$channelId]);
            self::$adCodeArr[$channelId] = $code;
        }
        return $code;
    }

    /**
     * 根据渠道code获取平台类
     * @param string $adCode
     * @return AdBaseInterface
     */
    public static function getChannelByCode($adCode)
    {
        if (!isset(self::$adChannelArr[$adCode])) {
            $className = '\app\ad_upload\channels\\' . StringUtil::convertToCamelHump($adCode, '_');
            if (!class_exists($className)) {
                throw new \Exception('没有找到对应的类'.$className);
            }
            self::$adChannelArr[$adCode] = new $className();
        }
        return self::$adChannelArr[$adCode];
    }
}

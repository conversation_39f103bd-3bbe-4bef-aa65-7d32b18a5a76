<?php

namespace app\service\AdvertiserData\Components\Matcher\Traits;

use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Aura\SqlQuery\QueryInterface;
use Spiral\Database\Query\SelectQuery;

trait AdAccountAble
{

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchAdAccountId(&$qb, array &$params)
    {
        if (empty($params['ad_account_id'])) return;

        $field = $this->getReflectField('ad_account_id');
        $data  = $params['ad_account_id'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchAdAccount(&$qb, array &$params)
    {
        if (empty($params['ad_account'])) return;

        $field = $this->getReflectField('ad_account');
        $data  = $params['ad_account'];

        $qb->where($field, 'like', "'%{$data}%'");
    }

    /**
     * @param       $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchAccountId(&$qb, array &$params)
    {
        if (!array_key_exists('account_id', $params)) return;

        $field = $this->getReflectField('account_id');
        $data  = $params['account_id'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param       $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchAccountName(&$qb, array &$params)
    {
        if (!array_key_exists('account_name', $params)) return;

        $field = $this->getReflectField('account_name');
        $data  = $params['account_name'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

}
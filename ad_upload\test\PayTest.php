<?php

namespace app\ad_upload\test;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\CustomProcess;

/**
 * 支付测试
 * <AUTHOR>
 */
class PayTest extends CustomProcess
{

    use CommonTrait;

    /**
     * 渠道id
     * @var int
     */
    public int $channel_id = 0;
    /**
     * 天数
     * @var int
     */
    public int $days = 0;

    /**
     * run
     * @return void
     */
    public function run()
    {
        $action = AdBaseInterface::ACTION_PAY;
        echo '0. 删除缓存', PHP_EOL;
        $this->delCache('ad_up_repeat_' . $action);
        echo '1. 同步配置', PHP_EOL;
        $this->syncConfig();
        echo '2. 设置上报点', PHP_EOL;
        //$timeBegin = date('Y-m-d', strtotime("-{$this->days} day")) . ' 00:00:00';
        $timeBegin = date('Y-m-d H:i:s', strtotime("-{$this->days} hour")) . '';
        $timeEnd   = date('Y-m-d H:i:s');
        $timeBegin = '2024-12-28 14:00:00';
        $timeEnd   = '2024-12-28 14:30:00';
        $sql       = "select pay_time from tb_sdk_user_payment where pay_time >='$timeBegin' and pay_time <='$timeEnd' and pay_result=1 limit 1";
        $pay_time  = \Plus::$app->origin_platform->query($sql)->fetchColumn();
        $id        = strtotime($pay_time);
        if ($id > 0) {
            \Plus::$app->dataspy2->update(
                'tb_ad_upload_log',
                ['last_action_id' => $id],
                ['channel_id' => $this->channel_id, 'action' => $action]
            );
        }

        $exe = "php cli.php -f ad_upload/AdUpload.php -s debug --p 'channel_id={$this->channel_id} actions={$action} begin_time=\"{$timeBegin}\" end_time=\"{$timeEnd}\"'";
        echo '3. 已处理上报点，运行命令：' . $exe . "\n";
        sleep(3);
        passthru($exe);
        $sql   = "select action_id from bigdata_dwd.dwd_reported_platform_log ";
        $where = "where time >= '$timeBegin' and time <= '$timeEnd' and channel_id = {$this->channel_id} and action = '$action'";
        $rs1   = \Plus::$app->doris_entrance->query($sql . $where)->fetchAll(\PDO::FETCH_ASSOC);
        $rs2   = \Plus::$app->doris_entrance2->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $rs1 = array_column($rs1, 'action_id');
        $rs2 = array_column($rs2, 'action_id');

        echo '正式数据库数量：' . count($rs1) . "\n";
        echo '测试数据库数量：' . count($rs2) . "\n";
        if (count($rs1) != count($rs2)) {
            echo '数据不一致，请检查！', PHP_EOL;
            $diff1 = array_diff($rs1, $rs2);
            $diff2 = array_diff($rs2, $rs1);
            echo '正式数据库多：' . implode(',', $diff1) . "\n";
            echo '测试数据库多：' . implode(',', $diff2) . "\n";
        } else {
            echo 'ok数据一致！', PHP_EOL;
        }
    }
}

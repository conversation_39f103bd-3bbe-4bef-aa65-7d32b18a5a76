<?php


namespace app\extension;

/**
 * Class ErrorHandler
 * 自定义错误处理，返回json 格式
 * @package app\lib
 * phpcs:disable
 */
class ErrorHandler extends \Plus\Internal\ErrorHandler
{
    /**
     * 失败返回
     * @param string $msg
     * @param array  $data
     * @param int    $code
     */
    public function error($msg, $data = [], $code = 500)
    {
        $data = [
            'code'    => $code,
            'message' => $msg,
            'data'    => $data,
        ];
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        die(0);
    }

    /**
     * 致命错误显示
     * @param array $error
     */
    public function displayFatal($error)
    {
        $this->error('致命错误：', $error);
    }

    /**
     * 显示错误
     * @param $code
     * @param $message
     * @param $file
     * @param $line
     */
    public function displayError($code, $message, $file, $line)
    {
        $this->error($message);
    }

    /**
     * 显示异常
     * @param \Exception $exception
     * @param string     $custom 自定义错误信息
     */
    public function displayException($exception, $custom = '')
    {
        $this->error($exception->getMessage(),[],$exception->getCode());
    }
}

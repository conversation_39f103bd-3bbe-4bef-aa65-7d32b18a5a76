<?php

namespace app\models\DdcPlatform;

use Plus\MVC\Model\ActiveRecord;

/**
 * @DwsPackageBaseDaily 运营首登基础指标表
 *
 * @property int    id
 * @property string tday
 * @property int    cp_game_id
 * @property int    game_id
 * @property int    package_id
 * @property int    activate_device
 * @property float  cost
 * @property float  cost_discount
 * @property int    firstlogin_user
 * @property int    firstlogin_device
 * @property int    firstlogin_active_user
 * @property int    firstlogin_role
 * @property int    firstlogin_role2
 * @property int    create_role
 * @property int    firstlogin_real_user
 * @property int    firstlogin_active_user_week
 * @property int    active_user_week
 * @property int    firstlogin_active_user_7_days_ago
 * @property int    firstlogin_active_user_pay
 * @property int    active_user_7_days_ago
 * @property int    new_user
 * @property int    new_device
 * @property int    new_real_user
 * @property int    new_real_user_der
 * @property int    reg_user
 * @property int    reg_device
 * @property int    active_user
 * @property int    new_user_imulator
 * @property int    active_user_imulator
 * @property int    active_user_imulator_bind
 * @property int    is_multiple
 */
class DwsPackageBaseDaily extends ActiveRecord
{
    protected $_primaryKey = 'id';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->ddc_platform;
        parent::__construct($data);
    }
}
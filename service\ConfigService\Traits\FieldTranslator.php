<?php

namespace app\service\ConfigService\Traits;

use app\extension\Support\Collections\Collection;

/**
 * 表头格式化
 */
trait FieldTranslator
{
    /**
     * 格式化为标准格式
     * @param Collection $collect
     * @return Collection
     */
    public function formatStandard(Collection $collect): Collection
    {
        $result = collect();

        $collect->each(function ($item, $key) use (&$result) {
            $result->push($this->newStandard($key, $item));
        });

        return $result;
    }

    /**
     * 初始化表头格式
     * @param mixed $field
     * @param array $options
     * @return array
     */
    protected function newStandard($field, array $options = []): array
    {
        $indexEntity = [
            'title'     => '',
            'align'     => 'center',
        ];

        $entity              = array_merge($indexEntity, $options);
        $entity['dataIndex'] = $field;

        if (!empty($options['sorter'])) {
            $entity['sorter'] = true;
        }

        return $entity;
    }

}
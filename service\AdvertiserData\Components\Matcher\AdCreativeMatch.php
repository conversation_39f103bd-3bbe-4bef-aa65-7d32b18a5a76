<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\models\AdpPlatform\TbAdpCreativeBase;
use app\models\AdpPlatform\TbAdpPlanBase;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class AdCreativeMatch extends AdPlanMatch
{
    public function matchFnList(): array
    {
        $new = [
            'creative_id'   => function (SelectQuery &$qb, $key, $value) {
                if ($value == null) {
                    $qb->where($key, 'IS', 'NULL');
                }
                else {
                    QueryBuilderHelper::baseBuild($qb, $key, $value);
                }
            },
            'creative_name' => $this->matchCreativeName()
        ];

        return array_merge(parent::matchFnList(), $new);
    }


    /**
     * @return \Closure
     */
    protected function matchCreativeName(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value, $params = []) {
            if (isset($params['tday'])) {
                $rangeStart = $params['tday'][0];
                $rangeEnd   = $params['tday'][1];
            }
            else {
                $rangeStart = date('Y-m-d', strtotime('-30 day'));
                $rangeEnd   = date('Y-m-d');
            }

            $ids    = (new TbAdpCreativeBase())->getCreativeIdByName($value, $rangeStart, $rangeEnd);
            $planId = $this->getReflectKey('plan_id');

            if ($ids) {
                $qb->where($planId, new Parameter($ids));
            }
            else {
                $qb->where($planId, 'error');
            }

        };
    }
}
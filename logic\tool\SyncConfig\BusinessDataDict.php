<?php

namespace app\logic\tool\SyncConfig;

use app\service\Tool\BusinessDataDictServ;

/**
 * @description 同步业务数据字典
 */
class BusinessDataDict
{
    /**
     * @param array $list
     * @return void
     * @throws \RedisException
     * @throws \Throwable
     */
    public function __invoke(array $list)
    {
        $this->load($list);
    }

    /**
     * @param array $list
     * @return void
     * @throws \RedisException
     * @throws \Throwable
     */
    public function load(array $list)
    {
        (new BusinessDataDictServ())->updateOrInsert($list);
    }
}
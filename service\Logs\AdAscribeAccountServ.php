<?php

namespace app\service\Logs;

use app\extension\FakeDB\FakeDB;
use app\models\baseConfPlatform\TbBaseGameConf;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\BusinessPlatform\OptionServ;
use app\service\ConfigService\BasicServ;
use app\util\Common;
use phpseclib3\Math\BigInteger\Engines\PHP;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

class AdAscribeAccountServ
{
    /**
     * 简单的获取部分基础指标数据
     *
     *
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function simpleBaseInfo(
        array $params = [],
        array $groups = [],
        array $sort = [],
        array $columns = []
    ): array
    {
        $data = [];
        return $data;
    }

    /**
     *
     *
     *
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $columns
     *
     * @return array
     */
    public function listInfo(
        array $params = []
    ): array
    {
        $where = $this->where($params);

        if ($where == ' 1=1 ') {
            return  ['list' => [], 'summary' => [], 'total' => 0];
        }

        // $gruop = ' group by d.SOURCE_ID ';
        if ($params['main_account'] ?? '') {
            $gruop = '';
            //广告新增为准
            $sql = "SELECT TMP.* FROM  (SELECT
                    a.id GB_ID,
                    d.id ID,
                    d.SOURCE_ID,
                    d.id ACTIVE_ID,
                    d.SV_KEY,
                    d.ACTIVATE_TIME,
                    a.`TIME` LOGIN_TIME,
                    DATEDIFF(a.TIME, c.TIME) diff_days,
                    d.PACKAGE_ID,
                    a.CORE_ACCOUNT,
                    a.LOGIN_ACCOUNT,
                    b.GAME_ID,
                    b.CP_GAME_ID,
                    d.PACKAGE_ID NEWLOGIN_PACKAGE_ID,
                    d.MAIN_ACCOUNT,
                    d.NEWLOGIN_TIME,
                    d.DEVICE_KEY,
                    e.AD_NAME,
                    md5(d.ip),
                    f.OAID,
                    f.ANDROID_ID,
                    f.MD5_DEVICE_ID,
                    f.ip,
                    h.AID,
                    IF(((g.OAID = f.OAID and f.OAID != '') or (md5(g.OAID) = f.OAID and f.OAID != '')), 'OAID', IF(g.ANDROID_ID = f.ANDROID_ID and f.ANDROID_ID != '', 'ANDROID_ID',IF(g.MD5_DEVICE_ID = f.MD5_DEVICE_ID and f.MD5_DEVICE_ID != '','MD5_DEVICE_ID', IF(md5(d.ip) = F.IP, 'IP', '')))) MATCH_TYPE,
                    IF(((g.OAID = i.OAID and i.OAID != '') or (md5(g.OAID) = i.OAID and i.OAID != '')), 'OAID', IF(g.ANDROID_ID = i.ANDROID_ID and i.ANDROID_ID != '', 'ANDROID_ID',IF(g.MD5_DEVICE_ID = i.MD5_DEVICE_ID and i.MD5_DEVICE_ID != '','MD5_DEVICE_ID',IF(md5(d.ip) = i.IP, 'IP', '')))) MATCH_TYPE_2
                FROM origin_platform.tb_sdk_user_newlogin_package a
                LEFT JOIN  base_conf_platform.tb_base_game_conf b USING(game_id)
                LEFT JOIN ddc_platform.dwd_sdk_user_newlogin_seven c on b.CP_GAME_ID=c.CP_GAME_ID and a.CORE_ACCOUNT=c.CORE_ACCOUNT
                LEFT JOIN ddc_platform.dwd_sdk_adsource_game d on c.SOURCE_ID=d.SOURCE_ID
                LEFT JOIN base_conf_platform.tb_ad_channel_conf e on d.CHANNEL_ID=e.ID
                LEFT JOIN ddc_platform.dwd_ad_click_match_log f on d.CLICK_ID = f.ID
                left join origin_platform.tb_ad_click_match_log_2022 i on d.CLICK_ID = i.ID
                LEFT JOIN origin_platform.tb_sdk_active_log g on d.ACTIVATE_TIME = g.TIME and d.DEVICE_KEY=g.DEVICE_KEY
                LEFT JOIN dataspy.tb_ad_svlink_conf h on d.SV_KEY = h.ID
                WHERE {$where} order by d.NEWLOGIN_TIME DESC LIMIT 10000) TMP ORDER BY NEWLOGIN_TIME DESC";
        }else{
            //新增表为主
            $sql = "SELECT TMP.* FROM  (SELECT
                    a.id GB_ID,
                    d.id ID,
                    d.id ACTIVE_ID,
                    d.SV_KEY,
                    d.ACTIVATE_TIME,
                    a.TIME LOGIN_TIME,
                    DATEDIFF(a.TIME, c.TIME) diff_days,
                    a.PACKAGE_ID,
                    a.CORE_ACCOUNT,
                    a.LOGIN_ACCOUNT,
                    b.GAME_ID,
                    b.CP_GAME_ID,
                    d.PACKAGE_ID NEWLOGIN_PACKAGE_ID,
                    d.MAIN_ACCOUNT,
                    d.NEWLOGIN_TIME,
                    d.DEVICE_KEY,
                    e.AD_NAME,
                    md5(d.ip),
                    f.OAID,
                    f.ANDROID_ID,
                    f.MD5_DEVICE_ID,
                    f.ip,
                    h.AID,
                    IF(((g.OAID = f.OAID and f.OAID != '') or (md5(g.OAID) = f.OAID and f.OAID != '')), 'OAID', IF(g.ANDROID_ID = f.ANDROID_ID and f.ANDROID_ID != '', 'ANDROID_ID',IF(g.MD5_DEVICE_ID = f.MD5_DEVICE_ID and f.MD5_DEVICE_ID != '','MD5_DEVICE_ID', IF(md5(d.ip) = F.IP, 'IP', '')))) MATCH_TYPE,
                    IF(((g.OAID = i.OAID and i.OAID != '') or (md5(g.OAID) = i.OAID and i.OAID != '')), 'OAID', IF(g.ANDROID_ID = i.ANDROID_ID and i.ANDROID_ID != '', 'ANDROID_ID',IF(g.MD5_DEVICE_ID = i.MD5_DEVICE_ID and i.MD5_DEVICE_ID != '','MD5_DEVICE_ID',IF(md5(d.ip) = i.IP, 'IP', '')))) MATCH_TYPE_2
                FROM origin_platform.tb_sdk_user_newlogin_package a
                LEFT JOIN  base_conf_platform.tb_base_game_conf b USING(game_id)
                LEFT JOIN ddc_platform.dwd_sdk_user_newlogin_seven c on b.CP_GAME_ID=c.CP_GAME_ID and a.CORE_ACCOUNT=c.CORE_ACCOUNT and a.TIME >= c.TIME
                LEFT JOIN ddc_platform.dwd_sdk_adsource_game d on c.SOURCE_ID=d.SOURCE_ID
                LEFT JOIN base_conf_platform.tb_ad_channel_conf e on d.CHANNEL_ID=e.ID
                LEFT JOIN ddc_platform.dwd_ad_click_match_log f on d.CLICK_ID = f.ID
                left join origin_platform.tb_ad_click_match_log_2022 i on d.CLICK_ID = i.ID
                LEFT JOIN origin_platform.tb_sdk_active_log g on d.ACTIVATE_TIME = g.TIME and d.DEVICE_KEY=g.DEVICE_KEY
                LEFT JOIN dataspy.tb_ad_svlink_conf h on d.SV_KEY = h.ID
                WHERE {$where} order by d.NEWLOGIN_TIME DESC LIMIT 10000) TMP ORDER BY NEWLOGIN_TIME DESC";
        }


// echo $sql;die;
        $list = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

        $list = $this->format($list);

        return  ['list' => $list, 'summary' => [], 'total' => count($list)];
    }

    /**
     * where条件
     *
     * @param array $params
     * @return void
     */
    public function where($params = [])
    {
        $where = " 1=1 ";

        if ($params['core_account'] ?? '') {
            $where .= " AND a.CORE_ACCOUNT = '{$params['core_account']}'";
        }

        // 从login_account查询核心账号
        if ($params['login_account'] ?? '') {
            // 判断是否手机号码追加 mob_
            // if (Common::isPhoneNumber($params['login_account'])) $params['login_account'] = 'mob_' . $params['login_account'];
            $loginAccount = Common::isPhoneNumber($params['login_account']) ?  'mob_' . $params['login_account'] : $params['login_account'];
            $coreAccount = (new OptionServ())
                ->getAccountInfo([
                    'login_account' => $loginAccount,
                ], [
                    'login_account', 'core_user',
                ]);
            $coreAccount = array_column($coreAccount, 'core_user');
            $coreAccount = $coreAccount ? : ['-1'];
            $coreAccount = implode("','", $coreAccount);

            $where .= " AND a.CORE_ACCOUNT IN ('{$coreAccount}')";
            $where .= " AND a.LOGIN_ACCOUNT = '{$params['login_account']}'";
        }

        // 从cp_game_id查询对应game_id，游戏原名必须配合其他查询才能用
        if (($params['cp_game_id'] ?? '') && (($params['core_account'] ?? '') || ($params['login_account'] ?? '') || ($params['main_account'] ?? ''))) {
            $getAllGameIds = (new TbBaseGameConf())->getAllGameIds(['cp_game_id' => $params['cp_game_id']]);
            $getAllGameIds = $getAllGameIds ? : ['-1'];

            $getAllGameIds = "('" . implode("','", $getAllGameIds) . "')";

            $where .= " AND d.game_id IN {$getAllGameIds}";

            // 主账号查询必须配合game_id使用才能使用索引
            if ($params['main_account'] ?? '') {
                $where .= " AND d.MAIN_ACCOUNT = '{$params['main_account']}'";
            }
        }

        return $where;
    }

    /**
     * 格式化
     *
     * @param array $list
     * @return void|array
     */
    public function format($list = [])
    {
        $constConfCollect = (new BasicServ())->getMultiOptions(['cp_game_id:all', 'game_id']);
        $columnsList = [];
        foreach ($constConfCollect as $key => $value) {
            $columnsList[$key] = array_column($value->toArray(), 'val', 'key');
        }

        foreach ($list as $key => $value) {
            $value = array_change_key_case($value, CASE_LOWER);
            $list[$key] = $value;
            $list[$key]['plan'] = $value['aid'] ?? '';
            $list[$key]['is_sub_account'] = $value['main_account'] != $value['core_account'] ? '是' : '否';
            $list[$key]['is_ad_new'] = $value['package_id'] == $value['newlogin_package_id'] && $value['main_account'] == $value['core_account'] ? '是' : '否';

            $list[$key]['cp_game_id'] = $columnsList['cp_game_id'][$value['cp_game_id']] ?? $value['cp_game_id'];
            $list[$key]['game_id'] = $columnsList['game_id'][$value['game_id']] ?? $value['game_id'];
        }

        return $list;
    }
}
<?php

namespace app\logic\advertise;

use app\apps\internal\Helpers\IndicatorsHelpers;
use app\extension\Support\Collections\Arr;
use app\service\Advertiser\AdDashProvider;
use app\service\Advertiser\AdDimensionProvider;
use app\service\Advertiser\AdPayPermeationProvider;
use Smarty\Exception;

/**
 * 广告看板指标查询
 *
 * @date 2024/09/23
 *
 */
class AdDashLogic
{
    /**
     * 基础指标
     *
     * @return array
     * @throws Exception
     */
    public function tableBase(
        array $params,
        array $groups = [],
        array $paginate = [],
        array $sort = [],
        array $columns = [],
        int   $mode = AdDashProvider::RESULT_ALL,
        bool  $isApiRule = false
    ): array
    {
        $today = date('Y-m-d');

        if (!isset($params['range_date'])) {
            $params['range_date'] = [
                $params['range_date_start'] ?? $today,
                $params['range_date_end'] ?? $today,
            ];
        }

        sort($params['range_date']);
        unset($params['range_date_start'], $params['range_date_end']);

        if (!empty($sort)) {
            foreach ($sort as $k => $foo) {
                if (in_array($k, ['click', 'show', 'download', 'activate', 'convert', 'install'])) {
                    $nk        = $k . '_cnt';
                    $sort[$nk] = $foo;
                    unset($sort[$k]);
                }
            }
            $sort = array_filter($sort);
        }

        if (!empty($groups)) {
            $fillGroups = [
                'tday', 'tmonth', 'cp_game_id', 'game_id', 'app_show_id',
                'channel_main_id', 'promotion_channel_id', 'promotion_id',
                'user_id', 'department_id', 'package_id', 'platform_id',
                'account_id', 'ad_account', 'campaign_id', 'plan_id', 'creative_id', 'dim_user_os',
            ];

            $groups = array_intersect($fillGroups, $groups);
        }

        $result = (new AdDashProvider())->getData($params, $groups, $paginate, $sort, $columns, $mode, $isApiRule);

        $appendPercent = function (&$data) {
            foreach ($data as $kk => &$foo) {
                if (str_contains($kk, 'percent')) {
                    $foo .= '%';
                }
            }
        };

        if (!empty($result['list'])) {
            $list = &$result['list'];
            foreach ($list as &$chill) {
                $appendPercent($chill);
            }
        }

        if (!empty($result['summary'])) {
            $appendPercent($result['summary']);
        }

        return $result;
    }

    /**
     * @param $list
     * @param $groups
     * @return void
     */
    public function fillAdDimensionName(&$list, $groups)
    {
        $works    = [];
        $provider = new AdDimensionProvider();
        $mergeFn  = function ($data, array $keys) {
            $keyIndex = array_fill_keys($keys, 0);

            return array_combine(
                array_map(
                    fn($item) => implode('|', array_merge($keyIndex, array_intersect_key($item, $keyIndex))), $data
                ),
                array_values($data)
            );
        };

        if (in_array('creative_id', $groups)) {
            $creatives   = array_filter(array_unique(array_column($list, 'creative_id')));
            $creativeMap = $provider->loadDimensionName($creatives, $provider::AD_CREATIVE);
            $creativeMap = $mergeFn($creativeMap, ['plan_id', 'creative_id']);

            $works[] = function (&$data) use ($creativeMap) {
                $creativeIndex = ['plan_id' => 0, 'creative_id' => 0];
                $kk            = implode('|', array_merge($creativeIndex, array_intersect_key($data, $creativeIndex)));

                if (isset($creativeMap[$kk])) {
                    $data['creative_name'] = $creativeMap[$kk]['creative_name'] ?? '';
                }
            };
        }

        if (in_array('plan_id', $groups)) {
            $plans   = array_filter(array_unique(array_column($list, 'plan_id')));
            $planMap = $provider->loadDimensionName($plans, $provider::AD_PLAN);
            $planMap = $mergeFn($planMap, ['plan_id']);

            $works[] = function (&$data) use ($planMap) {
                $kk = $data['plan_id'];

                if (isset($planMap[$kk])) {
                    $data['plan_name'] = $planMap[$kk]['plan_name'] ?? '';

                    if (!empty($data['campaign_name'])) {
                        $data['campaign_name'] = $planMap[$kk]['campaign_name'] ?? '';
                    }
                }
            };

        }
        elseif (in_array('campaign_id', $groups)) {
            // 目前不需要填充campaign_name
//            $campaigns   = array_filter(array_unique(array_column($list, 'campaign_id')));
//            $campaignMap = $provider->loadDimensionName($campaigns, $provider::AD_CAMPAIGN);
//            $campaignMap = $mergeFn($campaignMap, ['campaign_id']);
//
//            $work[] = function (&$data) use ($campaignMap) {
//                $kk = $data['campaign_id'];
//
//                if (isset($campaignMap[$kk])) {
//                    if (empty($data['campaign_name'])) {
//                        $data['campaign_name'] = $campaignMap[$kk]['campaign_name'] ?? '';
//                    }
//                }
//            };
        }

        if (!empty($works)) {
            foreach ($works as $fnFn) {
                foreach ($list as &$chill)
                    $fnFn($chill);
            }
        }
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array|null
     * @throws Exception
     */
    public function dataDash(
        array $params, array $groups = [], array $paginate = [], array $sort = [], array $columns = []
    ): ?array
    {
        $params['range_date'] = [
            $params['range_date_start'] ?? date('Y-m-d'),
            $params['range_date_end'] ?? date('Y-m-d'),
        ];
        sort($params['range_date']);

        if (isset($params['marketing_goal'])) {
            sort($params['marketing_goal']);
        }

        $provider = new AdDashProvider();
        $rr       = $provider->listFull(...func_get_args());
        $fnMap    = [];
        if (!empty($columns)) {
            if (in_array('pay_permeation_percent', $columns)) {
                $groupIndex = array_fill_keys($groups, 0);
                $ro         = $this->loadPayPermeation($params, $groups, [], [], ['new_user', 'pay_permeation_percent']);

                if (!empty($ro['list'])) {
                    $roList  = array_combine(array_map(fn($item) => implode('|', array_merge($groupIndex, array_intersect_key($item, $groupIndex))), $ro['list']), $ro['list']);
                    $fnMap[] = function (&$data) use ($roList, $groupIndex) {
                        $kk = implode('|', array_merge($groupIndex, array_intersect_key($data, $groupIndex)));
                        if (isset($roList[$kk])) {
                            $data['pay_permeation_percent'] = $roList[$kk]['pay_permeation_percent'] ?? '';
                        }
                    };
                }

                if (!empty($rr['summary'])) {
                    $rr['summary']['pay_permeation_percent'] = $ro['summary']['pay_permeation_percent'] ?? '0.00%';
                }

            }
        }

        if (!empty($rr['list'])) {
            $list = &$rr['list'];

            foreach ($list as &$chill) {
                if (!empty($fnMap)) {
                    foreach ($fnMap as $fnFn) {
                        $fnFn($chill);
                    }
                }

                $this->appendPercent($chill);
            }
        }

        if (!empty($rr['summary'])) {
            $summary = &$rr['summary'];
            $this->appendPercent($summary);
        }

        return $rr;
    }

    /**
     * @param $data
     * @return void
     */
    protected function appendPercent(&$data)
    {
        foreach ($data as $kk => &$foo) {
            if (
                str_contains($kk, '_percent')
                || str_contains($kk, 'roi_')
                || str_contains($kk, 'pass_level_')
                || str_contains($kk, 'retain_')
                || str_contains($kk, 'paid_retain')
                || str_contains($kk, '_roi')
            ) {
                if (empty($foo)) {
                    $foo = '-';
                }
                else {
                    $foo .= '%';
                }

            }
        }
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public function loadPayPermeation(array $params, array $groups = [], array $paginate = [], array $sort = [], array $columns = []): array
    {
        return (new AdPayPermeationProvider())->dashList(...func_get_args());
    }

}
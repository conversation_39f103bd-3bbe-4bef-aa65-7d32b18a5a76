<?php
declare(ticks=1);
$GLOBALS['_'] = '/usr/local/webserver/php74/bin/php';
$loader       = require __DIR__ . '/vendor/autoload.php';
$config       = require __DIR__ . '/config/main.php';
$service      = require __DIR__ . '/config/service.php';

unset($config['components']['error']);

$opt = getopt('f:');
$f   = $opt['f'] ?? '';
// 自定义错误处理
if (strpos($f, 'ad_upload/AdUpload') !== false) {
    $config['components']['error'] = [
        'class' => \app\ad_upload\tool\ErrorHandler::class,
    ];
}

require __DIR__ . '/vendor/framework/plus/src/Plus.php';

new \app\extension\ApplicationService($service);

try {
    (new \app\extension\Application($config))->run();
} catch (Exception $e) {
    echo $e->getMessage();
    die();
}

<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\service\AdvertiserData\Components\Matcher\Traits\CommonAble;
use app\service\AdvertiserData\Components\Matcher\Traits\PackageMatchAble;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

/**
 * 基础匹配累
 */
class BaseMatch extends ParameterMatch
{
    use CommonAble, PackageMatchAble;

    /**
     * @param array $reflectMap
     */
    public function __construct(array $reflectMap = [])
    {
        $this->fieldReflect = $reflectMap;
    }

    /**
     * @return array
     */
    protected function processLine(): array
    {
        return [
            [$this, 'matchCpGameId'],
            [$this, 'matchGameId'],
            [$this, 'matchPackages'],
            [$this, 'matchPlatform'],
            [$this, 'matchPromotion'],
        ];
    }

    /**
     * 执行匹配
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    public function exec(&$qb, array &$params)
    {
        $processLine = array_merge($this->processLine(), $this->newLine);

        // 过滤部分方法
        if (!empty($this->filterKeygen)) {
            foreach ($processLine as $i => $line) {
                if (!is_callable($line))
                    unset($processLine[$i]);

                if (is_array($line)) {
                    $fnName = $line[1] ?? null;
                    if (in_array($fnName, $this->filterKeygen)) {
                        unset($processLine[$i]);
                    }
                }
            }
        }

        foreach ($processLine as $process) {
            try {
                call_user_func_array($process, [&$qb, &$params]);
            }
            catch (\Exception $e) {
                continue;
            }
        }
    }
}
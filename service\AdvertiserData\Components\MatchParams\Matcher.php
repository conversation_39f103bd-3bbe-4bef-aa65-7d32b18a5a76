<?php

namespace app\service\AdvertiserData\Components\MatchParams;

abstract class Matcher
{
    /**
     * 绑定的条件
     *
     * @var array
     */
    protected array $wheres = [];


    /**
     * @param       $params
     * @param array $workLine
     * @return void
     */
    protected function runProcessLine($params, array $workLine = [])
    {
        foreach ($workLine as $callback) {
            if (is_callable($callback)) {
                $this->wheres[] = call_user_func_array($callback, [$params]);
            }
        }
    }

}
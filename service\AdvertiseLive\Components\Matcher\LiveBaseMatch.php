<?php

namespace app\service\AdvertiseLive\Components\Matcher;

use app\service\AdvertiserData\Components\Matcher\BaseMatch;
use app\service\AdvertiserData\Components\Matcher\Traits\AdChannelAble;
use app\service\AdvertiserData\Components\Matcher\Traits\DepartmentMatchAble;
use app\service\General\Matcher\Traits\TagsMatcher;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

class LiveBaseMatch extends BaseMatch
{
    use AdChannelAble, TagsMatcher, DepartmentMatchAble;

    public function processLine(): array
    {
        $newLine = [
            [$this, 'matchTDay'],
            [$this, 'matchChannelMainId'],
            [$this, 'matchChannelId'],
            [$this, 'matchChannelTagsQb'],
            [$this, 'matchChannelMainTagsQb'],
            [$this, 'matchPackageTagsQb'],
            [$this, 'matchUserId'],
        ];

        return array_merge(parent::processLine(), $newLine);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchTDay(&$qb, array $params)
    {
        if (
            empty($params['range_date_start'])
            || empty($params['range_date_end'])
        ) return;

        $rangeDate = array_filter([
            $params['range_date_start'],
            $params['range_date_end'],
        ]);

        sort($rangeDate);
        [$timeStart, $timeEnd] = $rangeDate;

        $timeStart .= ' 00:00:00';
        $timeEnd   .= ' 23:59:59';

        $field = $this->getReflectField('tday');

        $qb->where($field, 'BETWEEN', $timeStart, $timeEnd);
    }


}
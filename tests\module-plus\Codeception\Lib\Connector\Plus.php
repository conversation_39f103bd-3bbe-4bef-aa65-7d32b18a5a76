<?php

namespace Codeception\Lib\Connector;

use Plus\MVC\Application;
use \Plus\Service\Application as ApplicationService;
use Symfony\Component\BrowserKit\AbstractBrowser as Client;
use Symfony\Component\BrowserKit\Response;

class Plus extends Client
{
    public $configFile;
    public $serviceFile;
    public $applicationClass;
    public $applicationServiceClass;

    protected $kernel;
    protected $service;

    public function resetApplication()
    {
        \Plus::$app   = \Plus::$service = null;
        $this->kernel = $this->service = null;
        gc_collect_cycles();
    }

    public function restart()
    {
        parent::restart();
        $this->resetApplication();
    }

    public function getApplication()
    {
        if (is_null(\Plus::$app)) {
            $this->startApp();
        }
    }

    public function startApp()
    {
        $config  = include $this->configFile;
        $service = include $this->serviceFile;

        if (class_exists($this->applicationServiceClass)) {
            $this->service = new $this->applicationServiceClass($service);
        } else {
            $this->service = new ApplicationService($service);
        }
        if (class_exists($this->applicationClass)) {
            $this->kernel = new $this->applicationClass($config);
        } else {
            $this->kernel = new Application($config);
        }
    }

    /**
     * @param \Symfony\Component\BrowserKit\Request $request
     * @return \Symfony\Component\BrowserKit\Response
     */
    protected function doRequest($request)
    {
        $_COOKIE  = $request->getCookies();
        $_SERVER  = $request->getServer();
        $_FILES   = $request->getFiles();
        $_REQUEST = $request->getParameters();
        $_POST    = $_GET = [];
        if (strtoupper($request->getMethod()) === 'GET') {
            $_GET = $_REQUEST;
        } else {
            $_POST = $_REQUEST;
        }
        $uri                       = $request->getUri();
        $pathString                = parse_url($uri, PHP_URL_PATH);
        $queryString               = parse_url($uri, PHP_URL_QUERY);
        $_SERVER['REQUEST_URI']    = $queryString === null ? $pathString : $pathString . '?' . $queryString;
        $_SERVER['REQUEST_METHOD'] = strtoupper($request->getMethod());
        $_SERVER['QUERY_STRING']   = (string)$queryString;
        parse_str($queryString, $params);
        foreach ($params as $k => $v) {
            $_GET[$k] = $v;
        }
        ob_start();
        $this->kernel->processURI();
        $content = ob_get_clean();
        return new Response($content, 200, \Plus::$app->response->getHeader());
    }
}

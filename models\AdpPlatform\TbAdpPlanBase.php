<?php

namespace app\models\AdpPlatform;

use app\util\Common;
use Plus\MVC\Model\ActiveRecord;


/**
 *   计划
 * */
class TbAdpPlanBase extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->adp_platform;
        parent::__construct($data);
    }


    //根据计划id查询信息
    public function getDataById(array $planIds){
        $sql = "SELECT b.CAMPAIGN_NAME,a.CAMPAIGN_ID,a.PLAN_NAME,a.PLAN_ID FROM adp_platform.tb_adp_plan_base a 
                LEFT JOIN  adp_platform.tb_adp_campaign b using(`CHANNEL_ID`, `CAMPAIGN_ID`) 
                WHERE a.PLAN_ID IN('".implode("','",$planIds)."')";
        $data =  $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $searchPlanId = array_column($data,"PLAN_ID");
        $diffPlanId = array_diff($planIds,$searchPlanId);
        $svlinkData = [];
        if($diffPlanId){
            //查询短链
            $sql = "SELECT '' CAMPAIGN_NAME,0 CAMPAIGN_ID,AID PLAN_NAME,ID PLAN_ID  FROM dataspy.tb_ad_svlink_conf WHERE ID IN(".implode(",",$diffPlanId).")";
            $svlinkData =  $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        }
        //短链计划合并
        $data = array_merge($data,$svlinkData);
        $data = array_column($data,null,"PLAN_ID");
        return $data;
    }


    //根据名称获取计划/短链id
    public function getPlanIdByName(string $name,string $beginDate,string  $endDate){

        $planSql = "SELECT PLAN_ID FROM adp_platform.tb_adp_plan_base WHERE PLAN_NAME LIKE '%{$name}%'";
        $svlinkSql = "SELECT ID FROM dataspy.tb_ad_svlink_conf WHERE AID LIKE '{$name}'";

        if ($_db = Common::pingDoris()) {
             $this->_db =  $_db;
             $planSql .= " ORDER BY ID DESC LIMIT 1000";
             $svlinkSql .= " ORDER BY ID DESC LIMIT 1000";
        }else{
             $this->_db =  \Plus::$app->adp_platform;
             //使用mysql查询最近3个月的计划名称/短链名称
             $beginTime = date("Y-m-d H:i:s",strtotime($beginDate." -90 days"));
             $planSql .= " AND  AD_CREATE_TIME BETWEEN '{$beginTime}' AND '{$endDate} 23:59:59'";
             $svlinkSql .= " AND ADD_TIME BETWEEN '{$beginTime}' AND '{$endDate} 23:59:59'";
        }

        $planIdArr = $this->_db->query($planSql)->fetchAll(\PDO::FETCH_ASSOC);
        $planIdArr = array_column($planIdArr,"PLAN_ID");
        $svlinkIdArr = $this->_db->query($svlinkSql)->fetchAll(\PDO::FETCH_ASSOC);
        $svlinkIdArr = array_column($svlinkIdArr,"ID");
        $planIdArr = array_merge($planIdArr,$svlinkIdArr);

        return $planIdArr;
    }

    //根据计划id获取
    public function getPlanData($plan_id){
        $mainTable = $this->getTableName();
        $join = ["[>]tb_adp_oauth" => "ADVERTISER_ID",];
        $columns = [
            $mainTable.".advertiser_id",
            $mainTable.".plan_name",
            $mainTable.".user_id",
            "tb_adp_oauth.ADVERTISER_ACCOUNT(ad_account)"
        ];
        return $this->_db->get(
            $mainTable,
            $join,
            $columns,
            ["PLAN_ID"=>$plan_id]
        );
    }
}
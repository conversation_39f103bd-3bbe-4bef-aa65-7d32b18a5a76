<?php

namespace app\service\General;

use app\extension\FakeDB\FakeDB;
use app\service\General\Helpers\BaseConfPlatformTable;


class BizTagsServ
{

    static $tagsList = [];
    /**
     * @param array $params
     * @return array
     * @throws \RedisException
     */
    public function getTagsList(array $params = []): array
    {
        try {
            if(self::$tagsList){
                return self::$tagsList;
            }
        }
        catch (\Exception $e) {
            // nothing to do
        }

        if (!empty($cacheData)) {
            return $cacheData;
        }

        $db = $this->getConn();
        $qb = $db->select()->from(BaseConfPlatformTable::BizTags);

        if (!empty($params)) {
            $qb->where($params);
        }

        $data = $qb->fetchAll();
        if(!$data){
            $data = [
                6568,
                6822,
                5329,
                5327,
                6447,
            ];
        }
        self::$tagsList = $data;
        return $data;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('base_conf_platform');
    }
}
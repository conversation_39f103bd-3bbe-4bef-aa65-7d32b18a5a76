<?php

namespace app\logic\operator;

use app\extension\Support\Collections\Arr;
use app\service\ConfigService\BasicServ;
use app\service\OperationData\ConvertDataServ;

class ConvertDataLogic
{
    /**
     * @param array $param
     * @return array
     * @throws \Smarty\Exception
     * @throws \Exception
     */
    public function getList(array $param): array
    {
        $page     = Arr::pull($param, 'page', 1);
        $pageSize = Arr::pull($param, 'page_size', 100);
        $sort     = Arr::pull($param, 'sort', null);

        if (!empty($sort)) {
            $order = Arr::pull($param, 'order');
            if ($order == 'ascend') {
                $order = 'ASC';
            }
            else {
                $order = 'DESC';
            }
            $orderBy = [$sort => $order];
        }
        else {
            $orderBy = ['id' => 'DESC'];
        }

        if (!empty($param['range_date_start']) && !empty($param['range_date_end'])) {
            $param['range_time'] = [
                $param['range_date_start'], $param['range_date_end']
            ];
            sort($param['range_time']);
            unset($param['range_date_start'], $param['range_date_end']);
        }

        foreach ($param as $kk => &$item) {
            if (is_array($item) && in_array($kk, ['cp_game_id', 'game_id'])) {
                $item = implode(',', $item);
            }
        }

        $constMap        = (new BasicServ())->getMultiOptions(['cp_game_id', 'game_id']);
        $cpGameMap       = array_column($constMap->get('cp_game_id')->toArray(), 'val', 'key');
        $gameMap         = array_column($constMap->get('game_id')->toArray(), 'val', 'key');
        $result          = (new ConvertDataServ())->getList($param, [], ['page' => $page, 'page_size' => $pageSize], $orderBy);
        $list            = &$result['list'];
        $summaryRow      = &$result['summary'];
        $changeDefaultFn = function (&$data) {
            foreach ([
                         'android_convert_percentage',
                         'android_source_records_percentage',
                         'ios_convert_percentage',
                         'ios_source_records_percentage'
                     ] as $key) {

                if (isset($data[$key])) {
                    $data[$key] = $data[$key] . '%';
                }
                else {
                    $data[$key] = '0.00%';
                }
            }
        };

        $changeDefaultFn($summaryRow);

        foreach ($list as &$item) {
            $item               = array_change_key_case($item);
            $item['cp_game_id'] = $cpGameMap[($item['cp_game_id'] ?? '')] ?? '';
            $item['game_id']    = $gameMap[($item['game_id'] ?? '')] ?? '';


            $changeDefaultFn($item);
        }
        unset($item);

        return $result;
    }


}
<?php

namespace app\service\OperationData;

use app\apps\internal\Helpers\ConstHub;
use app\extension\FakeDB\FakeDB;
use app\extension\Support\Helpers\TimeUtil;
use app\extension\Support\Helpers\Zakia;
use app\service\General\Helpers\DdcPlatformTable;
use app\service\OperationData\Components\MatchParams\PackageMatch;
use app\util\Common;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

/**
 * 运维首登&新增维度基础指标查询
 * @date 2024/01/12
 * @package app\service\OperationData
 */
class PackageBaseServ
{
    /**
     * Returns the constant identifier of the pattern
     */
    const MODE_ALL        = 31;
    const MODE_DETAIL     = 1;
    const MODE_TOTAL      = 2;
    const MODE_SUMMARY    = 4;
    const MODE_DETAIL_QB  = 8;
    const MODE_SUMMARY_QB = 16;

    /**
     * mode for the query builder
     */
    const QB_MODE_ALL     = 7;
    const QB_MODE_BASE    = 1;
    const QB_MODE_PAYMENT = 2;
    const QB_MODE_POWER   = 4;


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * @param int $qbMode
     * @param array $params
     * @param array $groups
     * @return \Spiral\Database\Query\SelectQuery|null
     */
    protected function queryBaseBuilder(int $qbMode, array $params = [], array $groups = []): ?\Spiral\Database\Query\SelectQuery
    {
        $db            = $this->getConn();
        $qb            = null;
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;
        $mainTable     = '';

        if ($qbMode & self::QB_MODE_BASE) {
            if ($timeDimension == ConstHub::DIMENSION_MONTH) {
                $qb = $db->select()->from(DdcPlatformTable::DwsPackageBaseMonth . ' as t_base');
            }
            else {
                $qb = $db->select()->from(DdcPlatformTable::DwsPackageBaseDaily . ' as t_base');
            }

            $mainTable = 't_base';
        }

        if ($qbMode & self::QB_MODE_PAYMENT) {
            if ($timeDimension == ConstHub::DIMENSION_MONTH) {
                $paymentTable = DdcPlatformTable::DwsPackagePaymentMonth;
            }
            else {
                $paymentTable = DdcPlatformTable::DwsPackagePaymentDaily;
            }

            $onWhere = [
                't_pay.tday'       => 't_base.tday',
                't_pay.package_id' => 't_base.package_id'
            ];

            if ($qb != null) {
                $qb
                    ->leftJoin($paymentTable, 't_pay')
                    ->on($onWhere);
            }
            else {
                $qb        = $db->select()->from(DdcPlatformTable::DwsPackagePaymentDaily . ' as t_pay');
                $mainTable = 't_pay';
            }
        }
        else {
            if ($qb == null) throw new \InvalidArgumentException('必须选一个表作为主表');
        }

        if ($qbMode & self::QB_MODE_POWER) {
            $qb
                ->innerJoin(new Fragment(str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql())), 'power')
                ->on([$mainTable . '.package_id' => 'power.package_id']);
        }

        return $qb;
    }

    /**
     * 获取时间维度字段
     *
     * @param array $params
     * @param array $out
     * @param string $table
     * @return Fragment|string
     * @throws \Exception
     */
    protected function getDimensionTimeField(
        array &$params = [], array &$out = [], string $table = 't_base'
    )
    {
        $f             = '';
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        if ($timeDimension == ConstHub::DIMENSION_MONTH) {
            $f = new Fragment("DATE_FORMAT(STR_TO_DATE({$table}.tday, '%Y%m'), '%Y-%m') as group_day");
        }
        elseif ($timeDimension == ConstHub::DIMENSION_WEEK) {
            $rangeTime = $params['tday'];
            sort($rangeTime);
            // Get the weekly cycle dimension
            [
                'begin' => $rangeStart,
                'end'   => $rangeEnd,
                'cycle' => $rangeCycle
            ] = TimeUtil::divideWeekByRangeDate($rangeTime[0], $rangeTime[1]);

            $cols = [];

            // Changing the query time for the weekly dimension
            $params['range_date_start'] = $rangeStart;
            $params['range_date_end']   = $rangeEnd;
            $params['tday']             = [$rangeStart, $rangeEnd];
            $out                        = $rangeCycle;

            foreach ($rangeCycle as $r) {
                ['begin_date' => $begin, 'end_date' => $end] = $r;
                $cols [] = sprintf(
                    "when %s.tday between '%s' and '%s' then '%s'",
                    $table, $begin, $end, $begin . '/' . $end
                );
            }

            $caseString = implode(' ', $cols);
            $dayString  = sprintf(" CONCAT(case %s end) as group_day ", $caseString);
            $f          = new Fragment($dayString);
        }
        else {
            $f = "{$table}.tday as group_day";
        }

        return $f;
    }

    /**
     * 暂时不返回week的特定指标
     *
     * @param int $qbMode
     * @param bool $isSummary
     * @param array $params
     * @param array $groups
     * @param array $eqCols
     * @return array
     */
    protected function getBaseFields(
        int $qbMode = -1, bool $isSummary = false, array $params = [], array $groups = [], array $eqCols = []
    ): array
    {
        // todo: Provide full indicator splicing
        $infoMaps = [
            'cp_game_id'      => 't_base.cp_game_id as cp_game_id',
            'game_id'         => 't_base.game_id as game_id',
            'package_id'      => 't_base.package_id as package_id',
            'channel_id'      => 'power.channel_id as channel_id',
            'channel_main_id' => 'power.channel_main_id as channel_main_id',
            'user_id'         => 'power.ad_user_id as user_id',
            'department_id'   => 'power.ad_department_id as department_id',
            'platform_id'     => 'power.platform_id as platform_id',
        ];

        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;
        $calc          = [];

        if ($timeDimension == ConstHub::DIMENSION_DAY) {
            $calc = [
                'new_user'               => new Fragment('COALESCE(SUM(new_user),0) as new_user'),
                'active_user'            => new Fragment('COALESCE(SUM(active_user),0) as active_user'),
                'pay_user_newlogin_all'  => new Fragment('COALESCE(SUM(pay_user_newlogin_all),0) as pay_user_newlogin_all'),
                'pay_user_all'           => new Fragment('COALESCE(SUM(pay_user_all),0) as pay_user_all'),
                'pay_money_newlogin_all' => new Fragment('COALESCE(SUM(pay_money_newlogin_all),0) as pay_money_newlogin_all'),
                'pay_money_all'          => new Fragment('COALESCE(SUM(pay_money_all),0) as pay_money_all'),
            ];
        }
        elseif ($timeDimension == ConstHub::DIMENSION_MONTH) {
            $calc = [
                'new_user'               => new Fragment('COALESCE(SUM(new_user),0) as new_user'),
                'active_user_month'      => new Fragment('COALESCE(SUM(active_user_month),0) as active_user'),
                'pay_user_newlogin_all'  => new Fragment('COALESCE(SUM(pay_user_newlogin_all),0) as pay_user_newlogin_all'),
                'pay_user_all_month'     => new Fragment('COALESCE(SUM(pay_user_all_month),0) as pay_user_all'),
                'pay_money_newlogin_all' => new Fragment('COALESCE(SUM(pay_money_newlogin_all),0) as pay_money_newlogin_all'),
                'pay_money_all'          => new Fragment('COALESCE(SUM(pay_money_all),0) as pay_money_all'),
            ];
        }
        elseif ($timeDimension == ConstHub::DIMENSION_WEEK) {
            $calc = [
                'new_user'               => new Fragment('COALESCE(SUM(new_user),0) as new_user'),
                'pay_user_newlogin_all'  => new Fragment('COALESCE(SUM(pay_user_newlogin_all),0) as pay_user_newlogin_all'),
                'pay_money_newlogin_all' => new Fragment('COALESCE(SUM(pay_money_newlogin_all),0) as pay_money_newlogin_all'),
                'pay_money_all'          => new Fragment('COALESCE(SUM(pay_money_all),0) as pay_money_all'),
            ];
        }

        $cols = [];

        if (!$isSummary) {
            $cols = array_values(array_merge($infoMaps, $calc));
        }
        else {
            $cols = array_values(array_merge($infoMaps, $calc, [new Fragment('t_base.update_time as last_update_time')]));
        }

        return $cols;
    }

    /**
     *
     *
     * @param array $rangeCycle
     * @param array $fields
     * @param string $table
     * @return array
     */
    protected function getFieldForWeek(
        array $rangeCycle, array $fields, string $table = 't_base'
    ): array
    {
        $res = [];

        foreach ($fields as $f) {
            $caseWhen = [];
            $sf       = str_replace('_week', '', $f);
            foreach ($rangeCycle as $range) {
                [
                    'begin_date' => $start, 'end_date' => $end
                ] = $range;

                $caseWhen[] = " WHEN {$table}.tday between '{$start}' and '{$end}' THEN SUM(IF({$table}.tday = '{$start}', {$f} , 0)) ";
            }

            $caseString = implode(' ', $caseWhen);
            $res[]      = new Fragment("CASE {$caseString} END as {$sf}");
        }

        return $res;
    }


    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $columns
     * @param int $mode
     * @return array
     * @throws \Exception
     */
    public function getBaseInfo(
        array $params,
        array $groups = [],
        array $paginate = [],
        array $sort = [],
        array $columns = [],
        int   $mode = -1
    ): array
    {
        $result    = [];
        $groupsMap = [
            'tday' => 'group_day',
        ];

        $qb = $this->queryBaseBuilder(self::QB_MODE_ALL, $params, $groups);

        if ($qb == null) {
            throw new \RuntimeException('SQL构建失败');
        }

        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;
        $rc            = [];
        $dayCol        = $this->getDimensionTimeField($params, $rc);
        $cols          = $this->getBaseFields($this::QB_MODE_ALL, false, $params);
        $cols[]        = $dayCol;

        if ($timeDimension == ConstHub::DIMENSION_WEEK) {
            // 按周的部分字段特殊处理
            $weekCol = $this->getFieldForWeek($rc, ['active_user_week', 'pay_user_all_week']);
            $cols    = array_merge($cols, $weekCol);
        }
        $matcher = new PackageMatch([
                                        'tday'        => 't_base.tday',
                                        'cp_game_id'  => 't_base.cp_game_id',
                                        'game_id'     => 't_base.game_id',
                                        'package_id'  => 't_base.package_id',
                                        'platform_id' => 'power.platform_id'
                                    ]);

        if ($timeDimension == ConstHub::DIMENSION_MONTH) {
            $params['tday'] = array_map(fn($item) => date('Ym', strtotime($item)), $params['tday']);
        }

        $matcher($qb, $params);

        // 必要条件, 转端包渠道
        // 转端包不看65180001包号数据
        $qb
            ->where('channel_id', 'in', new Parameter(['6791', '6792']))
            ->where('t_base.package_id', 'not in', new Parameter(['65180001']));

        // 列表查询
        $infoQb = clone $qb;
        $infoQb->columns($cols);

        if (!empty($groups)) {
            foreach ($groups as $g) {
                if (isset($groupsMap[$g])) {
                    $infoQb->groupBy($groupsMap[$g]);
                }
                else {
                    $infoQb->groupBy($g);
                }
            }
        }

        $notPageInfoQb = clone $infoQb;

        if (!empty($sort)) {
            $sort = array_combine(array_map(fn($a) => $groupsMap[$a] ?? $a, array_keys($sort)), array_values($sort));
            $infoQb->orderBy($sort);
        }

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $pageLen] = $paginate;
            $infoQb->limit($pageLen)->offset(($page - 1) * $pageLen);
        }

        if ($mode & self::MODE_DETAIL_QB) {
            // 返回查询构造对象
            $result['detail_qb'] = clone $infoQb;
        }

        if ($mode & self::MODE_DETAIL) {
            @Common::dumpSql($infoQb->__toString());
            $result['list'] = $infoQb->fetchAll();
        }

        if ($mode & self::MODE_TOTAL) {
            $totalQb         = clone $notPageInfoQb;
            $result['total'] = $this->getConn()->select()->from(new Fragment('(' . $totalQb->__toString() . ') as total'))->count();
        }

        $wrapInfoQb = $this->getConn()->select()->from(new Fragment('(' . (clone $notPageInfoQb)->__toString() . ') as wrap_qb'));
        $wrapInfoQb
            ->columns([
                          new Fragment('COALESCE(SUM(new_user),0) as new_user'),
                          new Fragment('COALESCE(SUM(active_user),0) as active_user'),
                          new Fragment('COALESCE(SUM(pay_user_newlogin_all),0) as pay_user_newlogin_all'),
                          new Fragment('COALESCE(SUM(pay_user_all),0) as pay_user_all'),
                          new Fragment('COALESCE(SUM(pay_money_newlogin_all),0) as pay_money_newlogin_all'),
                          new Fragment('COALESCE(SUM(pay_money_all),0) as pay_money_all'),
                      ]);

        if ($mode & self::MODE_SUMMARY_QB) {
            $result['summary_qb'] = $wrapInfoQb;
        }

        if ($mode & self::MODE_SUMMARY) {
            @Common::dumpSql($wrapInfoQb->__toString());
            $result['summary'] = ($wrapInfoQb->fetchAll())[0] ?? [];
        }

        return $result;
    }

}


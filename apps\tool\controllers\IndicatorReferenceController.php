<?php

namespace app\apps\tool\controllers;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\Zakia;
use app\logic\tool\IndicatorReferenceLogic;
use app\service\ConfigService\BasicServ;
use app\service\Tool\IndicatorReferenceServ;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\IReader;
use Plus\MVC\Controller\JsonController;

/**
 * @desc "指标参考标准"管理
 * <AUTHOR>
 *
 */
class IndicatorReferenceController extends JsonController
{
    /**
     * @return array|array[]
     * @throws \Exception
     */
    public function listAction(): array
    {
        $request  = \Plus::$app->request;
        $paginate = [
            'page'      => $request->getValue('page', 1),
            'page_size' => $request->getValue('page_size', 100),
        ];
        $data     = (new IndicatorReferenceServ())->getAllIndicator($paginate);
        $fields   = [
            ['title' => '名称', 'dataIndex' => 'name', 'classify' => ['attrs', 'base']],
            ['title' => '创建人', 'dataIndex' => 'operator', 'classify' => ['attrs', 'base']],
            ['title' => '更新时间', 'dataIndex' => 'update_at', 'classify' => ['attrs', 'base']],
        ];

        $constMap = (new BasicServ())->getMultiOptions(['user:all']);
        $userMap  = array_column($constMap->get('user:all')->toArray(), 'val', 'key');
        $userMap  = Arr::except($userMap, 0);

        $result['list']   = $data['list'] ?? [];
        $result['total']  = $data['total'] ?? 0;
        $result['fields'] = $fields;

        if (!empty($result['list'])) {
            foreach ($result['list'] as &$foo) {
                $foo['operator'] = $userMap[$foo['operator']] ?? $foo['operator'];
            }
            unset($foo);
        }

        return $this->success($result);
    }

    /**
     * @return array
     */
    public function infoWithDashAction(): array
    {
        $request  = \Plus::$app->request;
        $parentId = $request->getValue('parent_id');

        if (empty($parentId)) {
            return $this->error('missing argument');
        }

        $data   = (new IndicatorReferenceServ())->getInfoWithParentId([
            'parent_ids' => $parentId,
        ]);
        $list   = $data['list'];
        $result = [];

        foreach ($list as $foo) {
            $title = $foo['indicator_title'] ?? '';

            $indexCollect = json_decode($foo['indicator_index']);

            if (str_contains($title, 'ROI')) {
                $standard = round(($foo['standard'] ?? 0.00) * 100, 2) . '%';
            } else {
                $standard = $foo['standard'] ?? 0.00;
            }

            foreach ($indexCollect as $item) {
                $result[$item] = $standard;
            }
        }

        return $this->success(['list' => $result]);
    }

    /**
     * @return array
     * @throws Exception
     * @throws \Throwable
     */
    public function saveAction(): array
    {
        ini_set('memory_limit', '2048M');
        $request   = \Plus::$app->request;
        $fileKey   = $request->getValue('target', 'file');
        $input     = $request->getFileItem($fileKey);
        $id        = $request->postValue('id');
        $name      = $request->postValue('name');
        $excelData = null;

        if (!empty($input)) {
            $fileInfo = $input->save(CACHE_DIR . $input->getFilename());
            $filePath = $fileInfo->getPathname();

            if (empty($filePath)) {
                return $this->error('获取路径失败');
            }

            $excelData = $this->explainExcel($filePath);
        }

        if (empty($name) && empty($input)) {
            return $this->error('参数缺失');
        }

        $data = [
            'id'       => $id,
            'name'     => $name,
            'children' => $excelData,
        ];

        try {
            (new IndicatorReferenceLogic())->save(array_filter($data));

            // 一级下拉更新缓存
            (new IndicatorReferenceLogic())->getFirstGradeList(true);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }

        return $this->success([]);
    }

    /**
     * @return array
     */
    public function delAction(): array
    {
        $request     = \Plus::$app->request;
        $requestJson = json_decode($request->getRawBody(), true);

        if (JSON_ERROR_NONE !== json_last_error()) {
            return $this->error('参数错误');
        }

        try {
            $ids = $requestJson['ids'] ?? [];
            if (!empty($ids)) {
                (new IndicatorReferenceServ())->deleteIndicatorWithId(Arr::wrap($ids));
            }
        } catch (\Throwable $e) {
            return $this->error('删除失败, 请稍后再试', ['trace' => $e->getMessage()]);
        }

        return $this->success([]);
    }


    /**
     * @param $filePath
     * @return array
     * @throws Exception
     */
    private function explainExcel($filePath): array
    {
        $againstFormats = [
            IOFactory::WRITER_CSV,
            IOFactory::WRITER_XLSX,
            IOFactory::WRITER_XLS,
        ];

        $reader      = IOFactory::load($filePath, IReader::READ_DATA_ONLY | IReader::IGNORE_EMPTY_CELLS, $againstFormats);
        $rowIterator = $reader->getActiveSheet()->getRowIterator();
        $excelData   = [];

        foreach ($rowIterator as $foo) {
            if ($foo->isEmpty()) {
                continue;
            }
            if ($rowIterator->key() == 1) {
                continue;
            }

            $cellIterator = $foo->getCellIterator();
            $qIndex       = null;

            foreach ($cellIterator as $cell) {
                $i = $cellIterator->getCurrentColumnIndex();
                $d = Zakia::convertChinesePunctuationToEnglish(trim($cell->getFormattedValue()));
                if ($i & 1) {
                    $qIndex = $d;
                } else {
                    $excelData[$qIndex] = $d;
                    $qIndex             = null;
                }
            }
        }// end foreach()

        return $excelData;
    }
}

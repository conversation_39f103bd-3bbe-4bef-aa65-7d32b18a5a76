<?php
/**
 * 腾讯广点通渠道数据上报
 * Created by PhpStorm.
 * User: Tim
 * Date: 2019/3/14
 * Time: 16:33
 * phpcs:disable
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class TxGdt extends AdBaseInterface
{

    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'ACTIVATE_APP');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, 'REGISTER');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'PURCHASE');
    }

    /**
     * @param $info
     * @param $uploadType
     * @param $remarkSuffix
     * @return void
     */
    protected function uploadData($info, $uploadType = 'ACTIVATE_APP', $remarkSuffix = 'TX_GDT_PARAM_')
    {
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = str_replace('_param_', '', strtolower($remarkSuffix));
        $logInfo['log_type']     = 'reported_platform_log';
        $url                     = $info['CALLBACK_URL'];
        if (!empty($url)) {
            //拼接上报参数
            $money = isset($info['MONEY']) ? $info['MONEY'] * 100 : 100;
            $imei  = $idfa = $androidId = '';
            if (substr($info['PACKAGE_ID'], -2) == 99) { //IOS包
                $idfa = $info['MD5_DEVICE_ID'];
            } else { //安卓包
                $imei      = $info['MD5_DEVICE_ID'];
                $androidId = md5($info['ANDROID_ID']);
            }
            $data = [
                'actions' => [
                    [
                        'outer_action_id' => $info['ID'] . $uploadType, //可根据此去重，防止重复上报
                        'action_time'     => time(),
                        'user_id'         => [
                            'hash_imei'       => $imei,
                            'hash_idfa'       => $idfa,
                            'hash_android_id' => $androidId,
                        ],
                        'action_type'     => $uploadType,
                        'action_param'    => [
                            'value' => $money,
                        ],

                    ],
                ],
            ]; //可以多条上报

            $logInfo['request'] = \json_encode(['url' => $url, 'params' => $data]);
            //上报
            $account_id = $info['EXT_CLICK']['advertiser_id'] ?? '';
            if (empty($account_id)) {
                $account_id = $info['EXT_CLICK']['origin_json']['advertiser_id'] ?? '';
            }
            $content = $this->gdtPost($url, $account_id, $data);

            $logInfo['response'] = $content;
            $resArr              = json_decode($content, true);

            if ($resArr['code'] == 0) {
                $logInfo['reported_status'] = 1;
            } else {
                $logInfo['reported_status'] = -1;
                if (!empty($info['paid_report_log'])) {
                    $info['paid_report_log']['no_reported_origin'] = '接口返回编码异常';
                }
            }
            $this->log($info, $logInfo, $content, $url);
        } else {
            if (!empty($info['paid_report_log'])) {
                $info['paid_report_log']['reported_status']    = -1;
                $info['paid_report_log']['reported_money']     = empty($info['paid_report_log']['reported_money']) ? $info['MONEY'] : $info['paid_report_log']['reported_money'];
                $info['paid_report_log']['no_reported_origin'] = '缺少必要上报参数callback_url';
                \Plus::$app->log->info(json_encode($info).'缺少必要上报参数callback_url', [], self::LOG_DIR);
                $this->logPaidToDoris($info, $logInfo, '');
            }
        }// end if()
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

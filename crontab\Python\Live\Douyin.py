import requests
import threading
import json,time,datetime,re
from collections import OrderedDict
import argparse
import  os
import  sys

#python目录下包
BASE_DIR=os.path.dirname(os.path.dirname( os.path.abspath(__file__) ))
sys.path.append( BASE_DIR  )
from Common import Confg
from Common import Db

# 创建 ArgumentParser 对象
parser = argparse.ArgumentParser()

# 添加命令行参数
parser.add_argument('--begin_time', help='开始时间')
parser.add_argument('--end_time', help='结束时间')
parser.add_argument('--account_id', help='账号ID')
# 解析命令行参数
args = parser.parse_args()

# 头部信息
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
    "Referer": "https://dy.huitun.com/",
    "Origin": "https://dy.huitun.com",
    'Content-Type': 'application/json',
}
session = requests.session()
session.headers.update(headers)

class Douyin:

    #数据开始结束时间
    begin_time = ""
    end_time = ""

    # 登录接口
    login_url1 = "https://login.huitun.com/weChat/userLogin"
    login_url2 = "https://dyapi.huitun.com/userLogin?htspm=&shareid="
    # 抖音号搜索
    search_url = "https://dyapi.huitun.com/search/user?from={page}&fusionShopFlag=false&keyword={keyword}&type=authorId"
    # 直播记录
    live_record_url = "https://dyapi.huitun.com/live/v2/record?_t={time}&from={page}&time=&has=&keyword=&mod=DESC&sort=&start={begin_date}&end={end_date}&filterMap=&uid={uid}&example="
    #直播信息
    live_info_url ="https://dyapi.huitun.com/live/roomInfo?_t={time}&roomId={roomId}&uid={uid}&example=false"


    def __init__(self,config):
        self.begin_time = config["begin_time"]
        self.end_time = config["end_time"]


    #登录
    def login(self,username, password):
        # 构造 JSON 数据
        data = {
            "mobile": username,
            "password": password
        }
        # 发送 post 请求
        response = session.post(self.login_url1, json=data)

        # 判断登录是否成功
        if response.status_code == 200:
            response = session.post(self.login_url2, json=data)
            if response.status_code == 200:
                print("登录成功")
            else:
                print("登录失败")
        else:
            print("登录失败")


    #抖音号搜索
    def searchUser(self,account_id):
        self.account_id = account_id
        # 构造请求URL，并发送请求
        url = self.search_url.format(page=1,keyword=self.account_id)
        response = session.get(url)
        # 解析响应数据
        uid = ""
        if response.status_code == 200:
            json_data = json.loads(response.text)
            for item in json_data["data"]:
                if item["authorId"] == self.account_id:
                    uid = item["uid"]
                    break
            if uid:
                self.liveRecord(uid)
            else:
                print(self.account_id+":抖音号没查询到！")
        else:
            print("请求失败")

    #直播记录
    def liveRecord(self,uid):
        page = 1
        while True:
            # 构造请求URL，并发送请求
            url = self.live_record_url.format(page=page,uid=uid, begin_date=self.begin_time,end_date=self.end_time,time=  int(time.time() * 1000))
            response = session.get(url)
            # 解析响应数据
            if response.status_code == 200:
                json_data = json.loads(response.text)
                if json_data["data"]:
                    for item in json_data["data"]:
                        roomId  = item["roomId"]
                        self.liveInfo(uid,roomId)
                else:
                    print(self.account_id+":没有数据了")
                    break
            else:
                print("请求失败")
            page += 1
            time.sleep(1)


    # 直播信息
    def liveInfo(self, uid,roomId):
        # 构造请求URL，并发送请求
        url = self.live_info_url.format(roomId = roomId, uid=uid,time=int(time.time() * 1000))
        response = session.get(url)
        # 解析响应数据
        if response.status_code == 200:
            json_data = json.loads(response.text)
            data = json_data["data"]
            if  data:
                live_data = list()
                #日期
                time_tuple = time.strptime(data["startLiveTime"], "%Y-%m-%d %H:%M:%S")
                timestamp = time.mktime(time_tuple)
                time_tuple = time.localtime(timestamp)
                date = time.strftime("%Y-%m-%d", time_tuple)
                print(self.account_id + ":日期:" + date)

                #结束时间
                end_time = "0000-01-01 00:00:00"
                if  data["endLiveTime"]:
                    end_time = data["endLiveTime"]
                ext = json.dumps(data,ensure_ascii=False)
                ext = ext.replace("\\n", ",")

                #格式化
                fields = ["avgUserDuration","watchTimes","maxUserNum","avgOnline","itemRate","fansClubInc","fansInc"]
                for v in fields:
                    data[v] = self.formatData(data[v])

                #数据入库
                live_item = OrderedDict({
                    "TDAY":date,
                    "TITLE":data["title"],
                    "ROOM_ID":roomId,
                    "ACCOUNT_ID":self.account_id,
                    "TYPE":1,
                    "START_TIME":data["startLiveTime"],
                    "END_TIME": end_time,
                    "STATUS":2,  #1=进行中 2=已结束
                    "WATCH":data["watchTimes"], #观看人次
                    "USER_DURATION":int(data["avgUserDuration"])*int(data["watchTimes"]), #总停留时长（秒）
                    "MAX_USER":data["maxUserNum"],  #人数峰值
                    "AVG_ONLINE":data["avgOnline"], #平均在线
                    "LIVE_DURATION":self.parseStrTime(data["liveDuration"]), #直播时长（秒）7h10m10s
                    "INTERACTION":float(data["itemRate"])*int(data["watchTimes"]), #互动数 = 互动率*观看次数
                    "FANS_CLUB":data["fansClubInc"],    #新增粉丝团
                    "FANS": data["fansInc"],            #粉丝数
                    "UPDATE_TIME":datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })

                if not data["endLiveTime"]:
                   live_item["STATUS"]=1
                live_data.append(live_item)
                # 抖音直播数据抓取
                mysql = Db.Mysql(Confg.getDb(database="ddc_platform", mode="master"))
                mysql.batchUpdate("dwd_live_log",live_data)
                del mysql
            else:
                print("直播信息无数据")
        else:
            print("请求失败")

    #解析时间字符串
    def parseStrTime(self,str):
        hour = 0
        minutes = 0
        seconds = 0
        pattern1 = r'(\d+)h'
        pattern2 = r'(\d+)m'
        pattern3 = r'(\d+)s'
        match = re.search(pattern1, str)
        if match:
            hour = match.group(1)
        match = re.search(pattern2, str)
        if match:
            minutes = match.group(1)
        match = re.search(pattern3, str)
        if match:
            seconds = match.group(1)
        hour = int(hour)
        minutes = int(minutes)
        seconds = int(seconds)
        return hour*3600+minutes*60+seconds

    #格式化
    def formatData(self,value):
        if value is None:
            value=0
        return value
if __name__ == "__main__":

    # 获取当前日期和时间
    current_time = datetime.datetime.now()
    # 获取当天日期
    today_date = current_time.strftime('%Y-%m-%d')
    # 获取昨天日期
    yesterday_date = (current_time - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
    begin_time = yesterday_date
    end_time = yesterday_date
    if args.begin_time:
        begin_time =  args.begin_time
    if args.end_time:
        end_time =  args.end_time

    #接收参数
    config = {
        "begin_time":begin_time,
        "end_time":end_time,
    }
    huituan = Douyin(config)
    # 传入需要登录的账号和密码
    huituan.login("***********", "xieyuting")

    # 要爬取的抖音账号列表
    mysql = Db.Mysql(Confg.getDb(database="base_conf_platform"))
    sql = "SELECT ACCOUNT_ID FROM tb_live_account_conf WHERE TYPE=1"
    if args.account_id:
        sql+=" AND ACCOUNT_ID IN("+args.account_id+")"
    mysql.cursor.execute(sql)
    accounts = mysql.cursor.fetchall()
    account_ids = []
    for v in accounts:
        account_ids.append(v[0])

    threads = []
    for account_id in account_ids:
        huituan = Douyin(config)
        # 创建一个线程，并启动
        t = threading.Thread(target=huituan.searchUser, args=(account_id,))
        threads.append(t)
        t.start()

    # 等待所有线程执行完毕
    for t in threads:
        t.join()

    print("所有线程已完成")

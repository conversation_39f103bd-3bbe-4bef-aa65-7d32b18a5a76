<?php

namespace app\service\OperationData;

use app\apps\internal\Helpers\ConstHub;
use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\extension\TableAssistant\Helpers\TableBaseHelp;
use app\service\AdvertiserData\Scheme\BaseScheme;
use app\service\AdvertiserData\Traits\BasicOperator;
use app\service\ConfigService\Traits\BaseHub;
use Aura\SqlQuery\Common\SelectInterface;
use Spiral\Database\Injection\Expression;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;

/**
 * @NewUserDistribution 用户分布来源(库:dataspy)
 */
class NewUserDistribution
{
    use BasicOperator;

    /**
     * 沿用spy1.0 NewUserDistribuLogic - getSearchList查询逻辑
     *
     * @param array $params
     * @param array $page
     * @param array $group
     *
     * @return array
     * @throws \Exception
     */
    public function getList(
        array $params,
        array $page = [],
        array $group = []
    ): array
    {

        $today    = date('Y-m-d');
        $table    = 'dataspy.tb_new_user_distribu';
        $scheme   = (new BaseScheme())->from($table, 'nud');
        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $options  = $params;
        $scheme
            ->joinPowerSql($powerSql, 'nud');

        $timeDimension = (int)Arr::pull($params, 'range_date_dimension', ConstHub::DIMENSION_DAY);

        if (ConstHub::DIMENSION_WEEK === $timeDimension) {
            $beginT    = new \DateTime(Arr::get($params, 'range_date_start'));
            $endT      = new \DateTime(Arr::get($params, 'range_date_end'));
            $beginDate = (clone $beginT)->add(new \DateInterval('P3D'))->format('W');
            $endDate   = (clone $endT)->add(new \DateInterval('P3D'))->format('W');
            $beginYear = (clone $beginT)->format('Y');
            $endYear   = (clone $endT)->format('Y');

            if ($beginDate == 1 && (clone $beginT)->format('m') == 12) {
                $beginYear += 1;
            }

            if ($beginDate == 1 && (clone $endT)->format('m') == 12) {
                $endYear += 1;
            }

            $options['range_date_start'] = $beginYear . $beginDate;
            $options['range_date_end']   = $endYear . $endDate;
        }
        elseif (ConstHub::DIMENSION_MONTH === $timeDimension) {
            $options['range_date_start'] = date('Ym', strtotime(Arr::get($params, 'range_date_start')));
            $options['range_date_end']   = date('Ym', strtotime(Arr::get($params, 'range_date_end')));
        }
        else {
            $options['range_date_start'] = date('Ymd', strtotime(Arr::get($params, 'range_date_start')));
            $options['range_date_end']   = date('Ymd', strtotime(Arr::get($params, 'range_date_end')));
        }

        $scheme->scope(function (SelectInterface $select) use ($options) {
            $reportType = Arr::get($options, 'report_type', 1);
            $groupType  = Arr::get($options, 'group_type', 1);
            $eventType  = Arr::get($options, 'event_type', 3);
            $startDate  = $options['range_date_start'];
            $endDate    = $options['range_date_end'];

            $select
                ->where('nud.report_type=' . $reportType)
                ->where('nud.group_type=' . $groupType)
                ->where('nud.event_type=' . $eventType)
                ->where("nud.tday between '{$startDate}' and '{$endDate}'");

            if (!empty($options['cp_game_id'])) {
                $cpGameId = implode(',', Arr::wrap($options['cp_game_id']));
                $select->where("POWER.cp_game_id IN ({$cpGameId})");
            }
        });

        $cols = [
            'nud.tday',
            'nud.week',
            'nud.game_id',
            'nud.package_id',
            'nud.ad_department_id',
            'POWER.cp_game_id',
            'POWER.channel_main_id',
            'POWER.channel_id',
            'POWER.popularize_v1_id',
            'POWER.platform_id',
            'SUM(tj_num) AS event_sum',
        ];

        $maxDayType = Arr::get($params, 'max_day_type', 4);

        for ($i = 0; $i <= $maxDayType; $i++) {
            $cols[] = sprintf('SUM(IF(day_type=%d, tj_num, 0)) as day_type%d', $i, $i);
        }

        $cols[] = 'SUM(IF(day_type=1000, tj_num, 0)) as day_type1000';

        $scheme
            ->scope(TableBaseHelp::setColumn($cols));

        if (!empty($group)) {
            $scheme->scope(
                TableBaseHelp::setGroup(
                    TableBaseHelp::changeGroups(
                        $group,
                        [
                            'tday'          => 'nud.tday',
                            'package_id'    => 'nud.package_id',
                            'department_id' => 'nud.ad_department_id',
                        ]
                    )
                )
            );
        }

        if (!empty($page)) {
            $scheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }

        return \Plus::$app->dataspy->query($scheme->toSql())->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @param array $page
     *
     * @return array
     * @throws \Exception
     */
    public function fetchSpecInfo(array $params, array $groups = [], array $sort = [], array $page = []): array
    {
        $table      = 'dataspy.tb_new_user_distribu';
        $powerTable = 'base_conf_platform.tb_package_detail_conf';
        $today      = date('Y-m-d');
        $db         = $this->getDBConn();
        $qb         = $db->select();

        $qb
            ->from($table . ' as nud')
            ->innerJoin($powerTable, 'power')
            ->on('nud.package_id', 'power.package_id');

        $reportType = $params['report_type'] ?? 1;
        $groupType  = $params['group_type'] ?? 2;
        $eventType  = $params['event_type'] ?? 3;
        $dateStart  = (new \DateTime($params['range_date_start'] ?? $today))->format('Ymd');
        $dateEnd    = (new \DateTime($params['range_date_end'] ?? $today))->format('Ymd');

        $qb
            ->where('report_type', $reportType)
            ->where('group_type', $groupType)
            ->where('event_type', $eventType)
            ->where('tday', 'between', $dateStart, $dateEnd);

        if (!empty($params['cp_game_id'])) {
            $cpGameIds = $params['cp_game_id'];

            if (str_contains($cpGameIds, ',')) {
                $cpGameIds = Arr::wrap(explode(',', $cpGameIds));
            }

            $qb->where([
                'power.cp_game_id' => ['in' => new Parameter(Arr::wrap($cpGameIds))],
            ]);
        }

        // info
        $cols = [
            new Fragment("DATE_FORMAT(STR_TO_DATE(`nud`.`tday`, '%Y%m%d'), '%Y-%m-%d') as tday"),
            'nud.week',
            'nud.game_id',
            'nud.package_id',
            'nud.ad_department_id',
            'nud.day_type',
            'power.cp_game_id',
            'power.channel_main_id',
            'power.channel_id',
            'power.popularize_v1_id',
        ];

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        $infoQb = (clone $qb)
            ->columns(array_merge($cols, [new Fragment('SUM(tj_num) as type_num')]));

        $eventQb = (clone $qb)
            ->columns(array_merge($cols, [new Fragment('SUM(tj_num) as event_sum')]));

        $infoQb->groupBy('day_type');

        $eventRe    = $eventQb->fetchAll();
        $infoRe     = $infoQb->getIterator();
        $resultRe   = [];
        $groupIndex = array_fill_keys($groups, 0);

        $uniqueKeyFn = function ($data, $groupIndex) {
            $kr = array_merge($groupIndex, array_intersect_key($data, $groupIndex));
            ksort($kr);
            return implode('|', $kr);
        };

        $eventRe = array_combine(array_map(function ($foo) use ($groupIndex, $uniqueKeyFn) {
            return $uniqueKeyFn($foo, $groupIndex);
        }, $eventRe), $eventRe);

        foreach ($infoRe as $foo) {
            $uniqueK = $uniqueKeyFn($foo, $groupIndex);
            $typeKey = 'day_type' . ($foo['day_type'] ?? 0);

            isset($resultRe[$uniqueK]) ?: $resultRe[$uniqueK] = ($eventRe[$uniqueK] ?? []);

            $chill = &$resultRe[$uniqueK];

            isset($chill[$typeKey])
                ? $chill[$typeKey] += $foo['type_num']
                : $chill[$typeKey] = $foo['type_num'];
        }

        return array_values($resultRe);
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getDBConn()
    {
        return FakeDB::connection('dataspy');
    }


}
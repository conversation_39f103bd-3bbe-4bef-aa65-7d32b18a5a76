<?php

namespace app\ad_upload\strategies;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\contract\AdUploadStrategyInterface;
use app\ad_upload\contract\DeductionStrategyInterface;
use app\ad_upload\contract\LogDbTrait;
use app\ad_upload\contract\PayUploadStrategyTrait;
use app\ad_upload\deduction\AmountDeductionStrategy;
use app\ad_upload\deduction\CustomDeductionStrategy;
use app\ad_upload\deduction\TimesDeductionStrategy;
use app\ad_upload\services\DeductionService;
use app\ad_upload\tool\ChannelFactory;
use app\ad_upload\tool\CommonFunc;

/**
 * 付费 上报 策略
 * <AUTHOR>
 */
class PayUploadStrategy extends AdUploadStrategyInterface
{
    use PayUploadStrategyTrait;
    use LogDbTrait;

    /**
     * 上报类型
     * @var string
     */
    public $action = AdBaseInterface::ACTION_PAY;

    /**
     * 最大id的数据
     * @var array
     */
    private $maxlastData = [];

    /**
     * 初始化上报点
     * @return void
     */
    public function initUploadLast()
    {
        parent::initUploadLast();
        $sql               = 'select max(id) as ID from tb_sdk_user_payment';
        $this->maxlastData = \Plus::$app->origin_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * 获取需要上报的数据
     * @param array  $uploadConfig 上报配置
     * @param string $packages     包号
     * @return array
     */
    public function getData($uploadConfig, $packages): array
    {
        if (empty($packages) || $this->lastId <= 0) {
            return [];
        }
        //先获取已支付订单
        $orderInfo = $this->getPaidOrder($packages);

        $allOrderIds = array_column($orderInfo, 'ORDER_ID');
        //手动指定订单id，不需要合并未匹配归因订单
        if (empty($this->ids)) {
            $allOrderIds = array_merge($allOrderIds, $this->unmatchedIds); //合并未匹配归因订单
        }

        if (empty($allOrderIds)) {
            return [];
        }

        //小游戏归：广告新增
        $xcxPackages = [];
        //包号新增
        $packageIds = [];
        foreach ($uploadConfig as $pkgId => $v) {
            if (in_array($pkgId, CommonFunc::getXcxPackageId())) {
                $xcxPackages[] = $pkgId;
            } else {
                $packageIds[] = $pkgId;
            }
        }
        $data1 = $xcxOrderIds = [];
        //小程序归因不一样
        if (!empty($xcxPackages)) {
            $data1       = $this->getPaidWithXcx($allOrderIds, $xcxPackages);
            $xcxOrderIds = array_column($data1, 'ORDER_ID');
        }
        //减去小程序归因
        $pkgOrderIds = array_diff($allOrderIds, $xcxOrderIds);
        $data2       = $this->getPaidWithPackageNew($pkgOrderIds, $packageIds);
        $rs          = array_merge($data1, $data2);
        //支付时间正序
        usort($rs, function ($a, $b) {
            return (strtotime($a['PAY_TIME']) - strtotime($b['PAY_TIME'])) > 0;
        });
        if (!empty($rs)) {
            //补充订单 投放信息
            foreach ($orderInfo as $v) {
                foreach ($rs as $k2 => $v2) {
                    if ($v['ORDER_ID'] == $v2['ORDER_ID']) {
                        $rs[$k2]['CAMPAIGN_ID'] = $v['CAMPAIGN_ID'];
                        $rs[$k2]['PLAN_ID']     = $v['PLAN_ID'];
                    }
                }
            }
        }
        return $rs;
    }

    /**
     * 设置如果上一步getData 获取数据为空，设置最大上报id
     * 避免长期没有数据上报的渠道，查询过多数据
     * @return void
     */
    public function setMaxLastId(): void
    {
        $data             = $this->maxlastData;
        $data['PAY_TIME'] = date('Y-m-d H:i:s');
        $this->setLastId($data);
    }

    /**
     * 获取已支付的订单id
     * @param string $packages 包号
     * @return array|false
     */
    private function getPaidOrder($packages)
    {
        //获取补漏上报ID
        $unmatchedIdString = CommonFunc::arr2Str($this->unmatchedIds);

        //lastId记录的是PAY_TIME时间戳
        $payTime = date('Y-m-d H:i:s', $this->lastId);
        $wheres  = [
            'a1.PAY_RESULT=1',
            "(a3.channel_id in ({$this->channelId}) or a4.channel_id in ({$this->channelId}))",
        ];
        if (!empty($this->timeBegin) && !empty($this->timeEnd)) {
            $wheres[] = "a1.PAY_TIME between '{$this->timeBegin}' and '{$this->timeEnd}'";
        } else if (!empty($this->ids)) {
            $idsArr   = explode(',', $this->ids);
            $ids      = CommonFunc::arr2Str($idsArr);
            $wheres[] = "a1.ORDER_ID in ({$ids})";
        } else {
            $wheres[] = "(a1.PAY_TIME > '{$payTime}' or a1.order_id in({$unmatchedIdString}))";
        }
        //指定订单id，不需要查包号
        if (empty($this->ids)) {
            $wheres[] = "(a3.PACKAGE_ID IN ({$packages}) or a4.PACKAGE_ID IN ({$packages}))";
        }

        $whereString = ' WHERE ' . implode(' AND ', $wheres);
        $sql         = "SELECT 
            a1.ID, a1.ORDER_ID, a3.CAMPAIGN_ID, a3.PLAN_ID
         FROM origin_platform.tb_sdk_user_payment a1 
         left join ddc_platform.dwd_sdk_user_payment a2 on a1.order_id = a2.order_id
         left join ddc_platform.dwd_sdk_adsource_game a3 on a2.SOURCE_ID = a3.SOURCE_ID
         left join origin_platform.tb_sdk_user_newlogin_package a4 on a1.package_id = a4.package_id and a1.core_account = a4.core_account 
         {$whereString}
         order by a1.ID";
        return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 走广告归因上报的数据
     *
     * @param array $orderIds   订单id
     * @param array $packageIds 包号
     * @return array
     */
    public function getPaidWithXcx($orderIds, $packageIds)
    {
        if (empty($orderIds)) {
            return [];
        }
        $orderIdString = CommonFunc::arr2Str($orderIds);
        $wheres        = [
            "t1.order_id in ({$orderIdString})",
        ];
        if (!empty($packageIds)) {
            $packageIdString = CommonFunc::arr2Str($packageIds);
            $wheres[]        = "t3.package_id IN ({$packageIdString})";
        }
        $whereString = ' WHERE ' . implode(' and ', $wheres);
        $sql         = "
        select t1.ID,
           t1.DEVICE_KEY,
           t3.PACKAGE_ID,
           t1.ORDER_ID,
           t1.DEVICE_CODE,
           t1.OAID,
           t1.DEVICE_ID,
           t1.MD5_DEVICE_ID,
           t1.CORE_ACCOUNT as CORE_ACCOUNT,
           t2.PAYWAY,
           t2.MONEY,
           t3.GAME_ID,
           ''              as OS,
           t1.IP,
           t1.ANDROID_ID,
           t3.CHANNEL_ID,
           t3.SV_KEY,
           t3.CLICK_ID,
           '{$this->action}'       as TYPE,
           t3.NEWLOGIN_TIME,
           t1.PAY_TIME,
           t3.CAMPAIGN_ID,
           t3.PLAN_ID,
           t1.ROLE_NAME,
           t1.ROLE_ID,
           t1.ROLE_RANK,
           t1.ROLE_VIP,
           t1.GAME_SERVER_ID,
           t3.CP_GAME_ID,
           t1.GAME_ID      as PAY_GAME_ID,
           t3.GAME_ID      as SOURCE_GAME_ID,
           t1.PACKAGE_ID   as PAY_PACKAGE_ID,
           t2.SOURCE_ID    as SOURCE_ID,
           t1.PAY_RESULT,
           t1.money as ACTUALLY_MONEY,
           t3.DEVICE_KEY,
           '2' as SOURCE_DIMENSION,
           t3.ACTIVATE_TIME
        from origin_platform.tb_sdk_user_payment t1
         left join ddc_platform.dwd_sdk_user_payment t2 on t1.order_id = t2.order_id
         left join ddc_platform.dwd_sdk_adsource_game t3 on t2.source_id = t3.source_id
       {$whereString}";
        return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 走包号新增归因上报的数据
     *
     * @param array $orderIds   订单id
     * @param array $packageIds 包号
     * @return array
     */
    private function getPaidWithPackageNew($orderIds, $packageIds)
    {
        if (empty($orderIds)) {
            return [];
        }

        $orderIdString = CommonFunc::arr2Str($orderIds);
        $wheres        = [
            "a.order_id in ({$orderIdString})",
        ];

        if (!empty($packageIds)) {
            $packageIdString = CommonFunc::arr2Str($packageIds);
            $wheres[]        = "b.package_id IN ({$packageIdString})";
        }

        $whereString = ' WHERE ' . implode(' and ', $wheres);

        $sql = "
            SELECT 
               a.ID,
               a.PACKAGE_ID,
               a.ORDER_ID,
               a.DEVICE_KEY,
               a.DEVICE_CODE,
               a.OAID,
               IF(b.DEVICE_ID = '', a.DEVICE_ID, b.DEVICE_ID)                                                            AS DEVICE_ID,
               IF(b.MD5_DEVICE_ID = 'aes_tzqDZ5J0JYlLaCosoB_v1qr2O5qLmJbCDutkYlUdaqw', a.MD5_DEVICE_ID,
                  b.MD5_DEVICE_ID)                                                                                       AS MD5_DEVICE_ID,
               a.CORE_ACCOUNT,
               a.PAYWAY,
               a.MONEY,
               a.GAME_ID,
               b.USERAGENT,
               b.OS,
               a.IP,
               a.ANDROID_ID,
               a.CHANNEL_ID,
               a.SV_KEY,
               a.CLICK_ID,
               '{$this->action}'                                                                                         AS TYPE,
               IF(DATE(a.PAY_TIME) = DATE(b.TIME), 1, 0)                                                                 AS IS_NEW_USER,
               b.TIME                                                                                                    as NEWLOGIN_TIME,
               a.PAY_TIME,
               a.ROLE_RANK,
               a.ROLE_ID,
               a.ROLE_NAME,
               a.GAME_SERVER_ID,
               a.ROLE_VIP,
               pc.CP_GAME_ID,
               a.GAME_ID                                                                                                 as PAY_GAME_ID,
               b.GAME_ID                                                                                                 as SOURCE_GAME_ID,
               b.DEVICE_KEY,
               a.PAY_RESULT,
               a.MONEY as ACTUALLY_MONEY,
               c.SOURCE_ID,
               '3' as SOURCE_DIMENSION
        FROM origin_platform.tb_sdk_user_payment a
        left join ddc_platform.dwd_sdk_user_payment c on a.order_id = c.order_id
        LEFT JOIN origin_platform.tb_sdk_user_newlogin_package b on a.PACKAGE_ID = b.PACKAGE_ID  AND a.CORE_ACCOUNT = b.CORE_ACCOUNT
        LEFT JOIN base_conf_platform.tb_package_detail_conf pc on a.PACKAGE_ID = pc.PACKAGE_ID
        {$whereString}
        ORDER BY a.ID";
        return \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 重写过滤数据
     * @param array $data         data
     * @param array $uploadConfig config
     * @return array
     */
    public function filterData($data, $uploadConfig): array
    {
        $unmatchedIds = $this->unmatchedIds;
        $dataPay      = [];
        //根据包号补充渠道
        foreach ($data as $key => $val) {
            //echo json_encode($val), PHP_EOL;
            $uploadConfigOne = $uploadConfig[$val['PACKAGE_ID']] ?? [];
            /**
             * 移除未匹配的订单:
             * 1.不存在渠道配置的包
             * 2.是渠道不匹配
             */
            if (empty($uploadConfigOne) || $val['CHANNEL_ID'] != $this->channelId) {
                if (in_array($val["ORDER_ID"], $unmatchedIds)) {
                    $unmatchedIds = array_values(array_diff($unmatchedIds, [$val['ORDER_ID']]));
                }
                \Plus::$app->log->error('not_match_config渠道配置不存在' . json_encode($val), [], AdBaseInterface::LOG_DIR);
            }

            foreach ($uploadConfigOne as $v) {
                //1为全量上报，2为匹配点击上报
                if ($v['UPLOAD_METHOD'] == 1 || ($v['UPLOAD_METHOD'] == 2 && $val['CHANNEL_ID'] == $v['CHANNEL_ID'])) {
                    $channelId = $v['UPLOAD_METHOD'] == 1 ? $v['CHANNEL_ID'] : $val['CHANNEL_ID'];
                    $clickId   = $v['UPLOAD_METHOD'] == 1 ? 0 : $val['CLICK_ID'];

                    $ext = $v['EXT'];

                    if (!empty($ext['upload_in_7_days'])) {
                        // @section 开启新增7天内控制上报
                        $n = (int)CommonFunc::dateDiff($val['NEWLOGIN_TIME'], $val['PAY_TIME'], '%R%a');

                        if ($n >= 7) {
                            \Plus::$app->log->info('过滤新增7天内数据' . $val['ID'] . '，相差天数' . $n, [], AdBaseInterface::LOG_DIR);
                            continue;
                        }
                    }
                    if ($channelId == 8365) {
                        //热云 只上报自然量
                        if ($val['CLICK_ID'] > 0) {
                            \Plus::$app->log->info('热云只上报自然量' . $val['ID'], [], AdBaseInterface::LOG_DIR);
                            continue;
                        }
                    }

                    if ($channelId == 6695) {
                        // 华为联运(即华为商店)
                        // 自然量不上报
                        if (empty($val['PLAN_ID'])) {
                            \Plus::$app->log->info('华为联运自然量不上报' . $val['ID'], [], AdBaseInterface::LOG_DIR);
                            continue;
                        }
                    }

                    //过滤已上报的订单
                    if (!empty($val['CLICK_ID']) && $this->force == 0 && CommonFunc::checkPayUploaded($val['ORDER_ID'])) {
                        \Plus::$app->log->error('过滤已上报的订单' . $val['ID'], [], AdBaseInterface::LOG_DIR_ERR);
                        continue;
                    }

                    // 广点通小程序 数据检查
                    if ($channelId == 1105) {
                        if (!$this->checkGdtXcx($val)) {
                            continue;
                        }
                    }
                    // 头条小程序 数据检查
                    if ($channelId == 1107) {
                        if (!$this->checkToutiaoXcx($val)) {
                            continue;
                        }
                    }
                    
                    //未归因，大于6小时的数据记录日志
                    if (empty($val['CLICK_ID']) && strtotime($val['PAY_TIME']) < strtotime($this->unmatchedTime)) {
                        $val['paid_report_log']                       = $this->initPaidReportLog($val);
                        $val['paid_report_log']['reported_money']     = 0;
                        $val['paid_report_log']['reported_behavior']  = -1;
                        $val['paid_report_log']['no_reported_origin'] = CommonFunc::getClientType($val['PACKAGE_ID']) . '归因自然量，不上报';
                        $this->logPaidToDoris($val, ['channel_code' => ChannelFactory::getChannelCode($channelId)], '');
                        //从未归因数据缓存中删除
                        if (in_array($val['ORDER_ID'], $unmatchedIds)) {
                            $unmatchedIds = array_values(array_diff($unmatchedIds, [$val['ORDER_ID']]));
                        }
                        \Plus::$app->log->info('未归因数据缓存:' . $val['ID'], [], AdBaseInterface::LOG_DIR);
                        continue;
                    }
                    //未归因数据缓存
                    $unmatchedIds = $this->processUnmatchedIds($unmatchedIds, $val);
                    if (in_array($val["ORDER_ID"], $unmatchedIds)) {
                        \Plus::$app->log->info('unmatched_' . $this->action . $val['ID'], [], AdBaseInterface::LOG_DIR);
                        continue; //剔除未匹配的数据
                    }

                    $dataPay[] = array_merge($val, [
                        'CHANNEL_ID'    => $channelId,
                        'UPLOAD_METHOD' => $v['UPLOAD_METHOD'],

                        'EXT_ID'   => $v['ID'],
                        'EXT'      => $v['EXT'],
                        'CLICK_ID' => $clickId,
                    ]);
                }// end if()
            }// end foreach()
        }// end foreach()
        $this->unmatchedIds = $unmatchedIds;
        return $dataPay;
    }

    /**
     * 头条小程序订单检查
     * @param array $info 订单数据
     * @return bool
     */
    private function checkToutiaoXcx($info)
    {
        //判断“付费时间-激活时间”如果超过30天，付费则不上报。订单在全量订单表中记录“付费距激活超30天”
        $activeTime = strtotime($info['ACTIVATE_TIME'] ?? '0000-00-00 00:00:00');
        $payTime    = strtotime($info['PAY_TIME']);
        if ($payTime > 0 && $activeTime > 0 && ($payTime - $activeTime) > 30 * 24 * 3600) {
            $info['paid_report_log']                      = $this->initPaidReportLog($info);
            $logInfo['reported_status']                   = 0;
            $info['paid_report_log']['reported_behavior'] = '-1';
            //$info['paid_report_log']['actually_money']         = 0;
            $info['paid_report_log']['reported_money']         = 0;
            $info['paid_report_log']['source_dimension']       = 2;
            $info['paid_report_log']['no_reported_origin']     = '小游戏，用户激活后，超过30天后的付费，不上报';
            $info['paid_report_log']['reported_rule_id']       = 0;
            $info['paid_report_log']['reported_behavior_rule'] = '';
            \Plus::$app->log->info(json_encode($info) . '付费距激活超30天', [], AdBaseInterface::LOG_DIR);
            $this->logPaidToDoris($info, $logInfo, '');
            return false;
        }
        return true;
    }

    /**
     * 广点通订单检查
     * @param array $info 订单数据
     * @return bool
     */
    private function checkGdtXcx($info)
    {
        /**
         *  2024/01/16
         *  新增规则 pwayway not in ('minigame', 'wechat_jsapi') (即小程序内付费) 或者 money为0的付费数据 不上报
         *       - wechat_jsapi 为ios小程序使用
         *       - minigame 为安卓小程序使用
         */
        $paywayA = $info['PAYWAY'] ?? '';
        if (in_array($paywayA, ['minigame', 'wechat_jsapi']) && !empty($info['MONEY'])) {

            /**
             *  2024/06/13
             *  (广点通微信小程序)当付费和归因属于是不同的游戏统计名的时候,不上报
             */
            $payGameId    = $info['PAY_GAME_ID'] ?? '';
            $sourceGameId = $info['SOURCE_GAME_ID'] ?? '';
            if ($payGameId != $sourceGameId) {
                $info['paid_report_log']                           = $this->initPaidReportLog($info);
                $info['paid_report_log']['reported_behavior']      = -1;
                $info['paid_report_log']['no_reported_origin']     = '归因广点通渠道，归因gameid和充值gameid不一致，不上报';
                $info['paid_report_log']['reported_rule_id']       = 0;
                $info['paid_report_log']['reported_behavior_rule'] = '';
                $info['paid_report_log']['reported_money']         = 0;
                $this->logPaidToDoris($info, ['channel_code' => ChannelFactory::getChannelCode($info['CHANNEL_ID'])], '');
                return false;
            }
            return true;
        } else {
            $info['paid_report_log']                           = $this->initPaidReportLog($info);
            $info['paid_report_log']['reported_behavior']      = -1;
            $info['paid_report_log']['no_reported_origin']     = '不上报原因：非小游戏正式支付，不上报';
            $info['paid_report_log']['reported_rule_id']       = 0;
            $info['paid_report_log']['reported_behavior_rule'] = '';
            $info['paid_report_log']['reported_money']         = 0;
            $this->logPaidToDoris($info, ['channel_code' => ChannelFactory::getChannelCode($info['CHANNEL_ID'])], '');
        }// end if()
        return false;
    }

    /**
     * 未归因数据缓存处理
     * @param array $unmatchedIds 订单id
     * @param array $val          data
     * @return array|mixed
     */
    private function processUnmatchedIds($unmatchedIds, $val)
    {
        $id     = $val['ORDER_ID'] ?? 0;
        $money1 = $val['MONEY'] ?? 0;
        $money2 = $val['ACTUALLY_MONEY'] ?? 0;
        // 代金券支付的，money 字段可能是0，会延后更新
        if (!empty($val['CLICK_ID']) && $money1 > 0 && $money2 > 0) {
            // 如果 ID 在 unmatchedIds 中，移除它
            if (in_array($id, $unmatchedIds)) {
                $unmatchedIds = array_values(array_diff($unmatchedIds, [$id]));
            }
        } else if (!in_array($id, $unmatchedIds)) {
            \Plus::$app->log->info('未归因数据缓存:' . json_encode($val), [], AdBaseInterface::LOG_DIR);
            // 如果 CLICK_ID 为空，将 ID 添加到 unmatchedIds
            $unmatchedIds[] = $id;
        }
        return $unmatchedIds;
    }

    /**
     * 上报数据 处理
     * @param array $data         data
     * @param array $uploadConfig 配置
     * @return array
     */
    public function processData(array $data, $uploadConfig): array
    {
        //上报数据 处理
        $rs = $this->processDataPay($data, $uploadConfig);
        //扣量上报
        $deductionService = new DeductionService();
        //扣量策略
        $strategies = [
            //new AmountDeductionStrategy(),
            //new TimesDeductionStrategy(),
            new CustomDeductionStrategy(),
        ];
        foreach ($rs as $k => $v) {
            /**
             * 策略
             * @var DeductionStrategyInterface $s
             */
            foreach ($strategies as $s) {
                $deductionService->setStrategy($s);
                $apply = $deductionService->applyDeduction($v);
                if ($apply) {
                    $rs[$k] = $s->getData(); //获取处理后的数据
                    break; //满足一个策略就退出循环
                }
                /* 没有匹配到规则，正常上报
                else {
                    if (get_class($s) == CustomDeductionStrategy::class) {
                        $s->log(['ratio' => ['type' => $s::RATIO_ALL_FILTER]], false, '不上报原因：没有配置扣量');
                        $rs[$k] = $s->getData();
                        break;
                    }
                }
                */
            }
        }// end foreach()
        return $rs;
    }
}

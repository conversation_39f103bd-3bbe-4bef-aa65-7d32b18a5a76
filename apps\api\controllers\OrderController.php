<?php

namespace app\apps\api\controllers;

use app\extension\Exception\ParameterException;
use app\models\baseConfPlatform\TbOathConf;
use app\service\Admin;
use Plus\MVC\Controller\JsonController;
use Plus\Util\Pagination;

/**
 * Class Order
 * <AUTHOR>
 */
class OrderController extends JsonController
{

    /**
     * get access token
     * @param array $data 请求参数
     * @return array
     */
    public function adNewloginAction()
    {
        //params
        $pageNo    = $this->getValue("page_no", 1);
        $pageSize  = $this->getValue("page_size", 1000);
        $orderId   = $this->getValue("order_id");
        $gameId   = $this->getValue("game_id");
        $packageId   = $this->getValue("package_id");
        $date = $this->getValue("date");


        if (!$orderId && !$date) {
            throw new ParameterException("订单号或者时间必须选择一个");
        }

        $where = " a.PAY_RESULT = 1 ";
        if ($orderId) {
            $orderId = "'" . implode("','", explode(",", $orderId)) . "'";
            $where   .= " AND a.ORDER_ID IN($orderId)";
        }
        if ($date) {
            $startTime = $date." 00:00:00";
            $endTime = $date." 23:59:59";
            $where .= " AND a.PAY_TIME BETWEEN '{$startTime}'  AND '{$endTime}' ";
        }

        if($packageId){
            $where .= " AND b.PACKAGE_ID IN($packageId) ";
        }

        if($gameId){
            $where .= " AND b.GAME_ID IN($gameId) ";
        }

        $sql   = "SELECT
                          COUNT(*) total
                        FROM
                            dwd_sdk_user_payment  a LEFT JOIN dwd_sdk_adsource_game b USING ( SOURCE_ID )
                        WHERE
                            {$where}";
        $data  = \Plus::$app->ddc_platform->query($sql)->fetch();
        $total = $data["total"];

        $pagination = new Pagination($total, $pageNo, $pageSize);

//        $limit = "LIMIT " . $pagination->getOffset() . "," . $pagination->getLimit();
        $limit = "LIMIT " .  (($pageNo -1) * $pageSize). "," . $pageSize;
        $sql   = "SELECT
                        IF
                            ( d.USER_ID = 0 OR d.USER_ID IS NULL, e.AD_USER_ID, d.USER_ID ) AS user_id,
                        IF
                            ( f.DEPARTMENT_ID = 0 OR f.DEPARTMENT_ID IS NULL, e.AD_DEPARTMENT_ID, f.DEPARTMENT_ID ) AS department_id,
                            b.PACKAGE_ID ad_newlogin_package,
                            b.NEWLOGIN_TIME ad_newlogin_time,
                            c.PACKAGE_ID first_login_package,
                            c.time  first_login_time,
                            order_id,
                            a.package_id,
                            order_time,
                            pay_time,
                            money,
                            payway, 
                            e.channel_id,
                            e.channel_main_id,
                            b.campaign_id,
                            b.plan_id,
                            b.creative_id,
                            b.sv_key,
                            b.main_account,
                            a.core_account,
                            g.cp_game_id,
                            b.game_id, 
                            b.source_id,
                            (select source_game.SV_KEY from dwd_sdk_user_newlogin_seven seven join dwd_sdk_adsource_game source_game on seven.SOURCE_ID=source_game.SOURCE_ID and seven.CORE_ACCOUNT=source_game.MAIN_ACCOUNT where seven.CP_GAME_ID=b.CP_GAME_ID and  seven.CORE_ACCOUNT=b.MAIN_ACCOUNT  order by source_game.NEWLOGIN_TIME asc limit 1) ad_firstlogin_svkey
                        FROM
                            dwd_sdk_user_payment a
                            LEFT JOIN dwd_sdk_adsource_game b USING ( SOURCE_ID )
                            LEFT JOIN dwd_sdk_user_firstlogin c ON a.CP_GAME_ID = c.CP_GAME_ID 
                            AND a.CORE_ACCOUNT = c.CORE_ACCOUNT
                            LEFT JOIN dataspy.tb_ad_svlink_conf d ON b.SV_KEY = d.ID
                            LEFT JOIN base_conf_platform.tb_package_detail_conf e ON b.PACKAGE_ID = e.PACKAGE_ID
                            LEFT JOIN dataspy.admin_user f ON d.USER_ID = f.ID 
                            LEFT JOIN base_conf_platform.tb_package_detail_conf g ON a.PACKAGE_ID = g.PACKAGE_ID
                        WHERE
                            {$where}  $limit";
        $data = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        return $this->success($data);
    }

}

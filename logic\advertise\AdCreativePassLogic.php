<?php

namespace app\logic\advertise;

use app\apps\internal\Helpers\IndicatorsHelpers;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\AdCreativePassServ;
use Smarty\Exception;

/**
 * 广告创意层先导指标逻辑查询
 */
class AdCreativePassLogic
{
    /**
     * @param array $params
     * @param array $groups
     * @return array
     * @throws Exception
     */
    public function getAdDashboardDimension(array $params = [], array $groups = []): array
    {
        $sourceGroup = $groups;
        if (in_array('promotion_channel_id', $groups)) {
            $groups[array_search('promotion_channel_id', $groups)] = 'channel_id';
        }

        if (isset($params['range_date_start'])) {
            $params['range_time'] = [
                $params['range_date_start'],
                $params['range_date_end'] ?? $params['range_date_start']
            ];
            sort($params['range_time']);
        }

        $serv    = new AdCreativePassServ();
        $columns = [
            "DATE_FORMAT(t_pass.tday, '%Y-%m-%d') as group_day",
            'SUM(IF(ROLE_TYPE = 1, NUMERATOR, 0)) as pass_level_1',
            'SUM(IF(ROLE_TYPE = 2, NUMERATOR, 0)) as pass_level_2',
            'SUM(IF(ROLE_TYPE = 3, NUMERATOR, 0)) as pass_level_3',
            'SUM(IF(ROLE_TYPE = 1,DENOMINATOR, 0)) as pass_denominator_1',
            'SUM(IF(ROLE_TYPE = 2,DENOMINATOR, 0)) as pass_denominator_2',
            'SUM(IF(ROLE_TYPE = 3,DENOMINATOR, 0)) as pass_denominator_3'
        ];

        if (isset($params['promotion_channel_id'])) {
            $params['channel_id'] = $params['promotion_channel_id'];
        }
        foreach ($params as $k => &$foo) {
            if (
                is_array($foo)
                && in_array($k, [
                    'cp_game_id', 'game_id', 'package_id',
                    'channel_main_id', 'channel_id', 'platform_id', 'app_show_id',
                    'promotion_id', 'department_id', 'user_id', 'campaign_id', 'plan_id'
                ])
            ) {
                $foo = implode(',', array_map(fn($chill) => "'{$chill}'", Arr::wrap($foo)));
            }
        }

        if (in_array('tday', $groups)) {
            $groups[array_search('tday', $groups)] = 'group_day';
        }

        $res  = $serv->getInfoList($params, $groups, $columns);
        $list = &$res['list'];
        foreach ($list as &$foo) {
            $foo['promotion_channel_id'] = $foo['channel_id'] ?? 0;
        }
        $groupIndex = array_fill_keys($sourceGroup, 0);
        $list       = array_combine(array_map(function ($item) use ($groupIndex) {
            return IndicatorsHelpers::flattenUnionKey($item, $groupIndex);
        }, $list), $list);

        return $res;
    }

    /**
     * @throws Exception
     */
    public function getRowInfo(array $params = [], array $groups = []): array
    {
        $rowOptions = $params['row_option'] ?? '';

        if (in_array('promotion_channel_id', $groups)) {
            $groups[array_search('promotion_channel_id', $groups)] = 'channel_id';
        }

        if (isset($params['promotion_channel_id'])) {
            $params['channel_id'] = $params['promotion_channel_id'];
        }

        if (!empty($rowOptions) && is_string($rowOptions)) {
            $rowOptions = \json_decode($rowOptions, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \InvalidArgumentException("行数据解析错误/缺失");
            }
        }

        if (isset($rowOptions['promotion_channel_id'])) {
            $rowOptions['channel_id'] = $rowOptions['promotion_channel_id'];
            unset($rowOptions['promotion_channel_id']);
        }

        if (!empty($params['target'])) {
            $params['role_type'] = str_replace('pass_level_', '', $params['target']);
        }

        foreach ($groups as $k) {
            $chill = $rowOptions[$k] ?? '-';
            if ($chill == '-') {
                $params[$k] = 0;
                continue;
            }

            if ($k == 'tday') {
                $params['range_date_start'] = $params['range_date_end'] = $chill;
            }
            else {
                $params[$k] = $chill;
            }
        }

        if (isset($params['range_date_start'])) {
            $params['range_time'] = [
                $params['range_date_start'],
                $params['range_date_end'] ?? $params['range_date_start']
            ];
            sort($params['range_time']);
        }

        $serv = new AdCreativePassServ();

        foreach ($params as $k => &$foo) {
            if (
                is_array($foo)
                && in_array($k, [
                    'cp_game_id', 'game_id', 'package_id',
                    'channel_main_id', 'channel_id', 'platform_id', 'app_show_id',
                    'promotion_id', 'department_id', 'user_id', 'campaign_id', 'plan_id'
                ])
            ) {

                $foo = implode(',', array_map(fn($chill) => "'{$chill}'", Arr::wrap($foo)));
            }
        }
        $params['is_hour'] = 1;
        $res               = $serv->getInfoList($params, ['tday']);
        $list              = $res['list'];
        $data              = [];
        $passRateFn        = function ($numerator, $denominator) {
            if (empty($numerator) || empty($denominator)) {
                return '0.00%';
            }
            else {
                return round($numerator / $denominator * 100, 2) . '%';
            }
        };

        foreach ($list as $item) {
            $numerator   = $item['numerator'];
            $denominator = $item['denominator'];
            $tday        = $item['tday'];
            $data[$tday] = [
                'tday'        => $tday,
                'denominator' => $denominator,
                'numerator'   => $numerator,
                'pass_rate'   => $passRateFn($numerator, $denominator),
            ];
        }

        ksort($data);

        return [
            'list' => array_values($data),
        ];
    }


}
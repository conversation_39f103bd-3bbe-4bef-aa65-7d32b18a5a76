<?php

namespace app\extension\Support\Collections;

/**
 * @HigherOrderCollectionProxy
 *
 */
class HigherOrderCollectionProxy
{
    /**
     * @var Enumerable
     */
    protected Enumerable $collection;

    /**
     * @var string
     */
    protected string $method;

    public function __construct(Enumerable $collection, $method)
    {
        $this->method     = $method;
        $this->collection = $collection;
    }

    public function __get($key)
    {
        return $this->collection->{$this->method}(function ($value) use ($key) {
            return is_array($value) ? $value[$key] : $value->{$key};
        });
    }

    /**
     * @param $method
     * @param $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        return $this->collection->{$this->method}(function ($value) use ($method, $parameters) {
            return $value->{$method}(...$parameters);
        });
    }

}
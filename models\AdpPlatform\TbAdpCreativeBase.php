<?php

namespace app\models\AdpPlatform;

use app\util\Common;
use Plus\MVC\Model\ActiveRecord;


/**
 * 创意
 * */
class TbAdpCreativeBase extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->adp_platform;
        parent::__construct($data);
    }

    //根据创意id查询信息
    public function getDataById(array $planIds, array $creativeIds)
    {

        $sql = "SELECT campaign.CAMPAIGN_NAME,creative.CAMPAIGN_ID,plan.PLAN_NAME,creative.PLAN_ID,creative.CREATIVE_ID,creative.CREATIVE_NAME FROM  adp_platform.tb_adp_creative_base creative  
                LEFT JOIN adp_platform.tb_adp_plan_base plan using(PLAN_ID) 
                LEFT JOIN  adp_platform.tb_adp_campaign campaign  ON plan.CHANNEL_ID=campaign.CHANNEL_ID AND plan.CAMPAIGN_ID=campaign.CAMPAIGN_ID 
                WHERE creative.PLAN_ID IN('" . implode("','", $planIds) . "') AND creative.CREATIVE_ID IN('" . implode("','", $creativeIds) . "')";

        $data = $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $newData = [];
        foreach ($data as $item) {
            $newData[$item["PLAN_ID"] . "_" . $item["CREATIVE_ID"]] = $item;
        }

        return $newData;
    }


    //根据名称获取创意id
    public function getCreativeIdByName(string $name, string $beginDate, string $endDate)
    {

        $sql = "SELECT CREATIVE_ID FROM   adp_platform.tb_adp_creative_base WHERE CREATIVE_NAME LIKE '%{$name}%' ";
        if ($_db = Common::pingDoris()) {
            $this->_db = $_db;
            $sql.=" ORDER BY ID DESC LIMIT 1000";
        } else {
            $this->_db = \Plus::$app->adp_platform;
            //使用mysql查询最近3个月的计划名称/短链名称
            $beginTime = date("Y-m-d H:i:s", strtotime($beginDate . " -90 days"));
            $sql .= " AND  CREATIVE_CREATE_TIME BETWEEN '{$beginTime}' AND '{$endDate} 23:59:59'";
        }

        $IdArr = $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $IdArr = array_column($IdArr, "CREATIVE_ID");
        return $IdArr;
    }
}
<?php

namespace app\service\Revenue;

use app\extension\FakeDB\FakeDB;
use Cycle\ORM\Select\QueryBuilder;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

/**
 * @CostServ 消耗管理
 */
class CostServ
{
    private const TB_AD_COST = 'origin_platform.tb_ad_cost';

    private const TB_ACCOUNT_CONF = 'base_conf_platform.tb_ad_account_conf';

    /**
     * 获取总体的消耗详情,
     * 暂时单查tb_ad_cost表
     *
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return array
     * @throws \Exception
     */
    public function getOverallCostInfo(array $params = [], array $groups = ['tday'], array $columns = []): array
    {
        $db       = $this->getConn();
        $qb       = $db->select()->from(static::TB_AD_COST . ' as t_cost');
        $powerSql = \Plus::$service->admin->getAdminPowerSql();

        if (empty($columns)) {
            $columns = [
                new Fragment('DATE(TIME) as tday'),
                'base_channel.channel_main_id as channel_main_id',
                new Fragment('SUM(cost) as cost'),
                new Fragment('SUM(cost_discount) as cost_discount'),
            ];
        }

        $qb
            ->leftJoin('base_conf_platform.tb_base_channel_conf', 'base_channel')
            ->on('t_cost.channel_id', 'base_channel.channel_id');

        $this->costMatcher($qb, $params);

        if (!empty($groups)) {
            foreach ($groups as $g) {
                $qb->groupBy($g);
            }
        }

        $qb->columns($columns);

        return $qb->fetchAll();
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('origin_platform');
    }

    /**
     * @param SelectQuery|QueryBuilder $qb
     * @param array                    $params
     *
     * @return void
     * @throws \Exception
     */
    private function costMatcher(&$qb, array $params = [])
    {
        $today    = date('Y-m-d');
        $fieldMap = [
            'status'     => 't_cost.status',
            'cp_game_id' => 't_cost.cp_game_id',
        ];


        if (
            !empty($params['range_date_start'])
            || !empty($params['range_date_end'])
        ) {
            $rangeDate = [
                $params['range_date_start'] ?? $today,
                $params['range_date_end'] ?? $today,
            ];

            sort($rangeDate);

            $qb->where('t_cost.time', 'between', ...$rangeDate);
        }

        if (!empty($params['mode'])) {
            if ($params['mode'] == 1) {
                $db = $this->getConn();

                // 排除逗娱账号
                $newQb = $db
                    ->select()
                    ->from(static::TB_ACCOUNT_CONF)
                    ->where('company', 2)
                    ->columns(['ad_account'])
                    ->distinct();

                $qb->where('ad_account', 'NOT IN', $newQb);
            }
        }

        foreach ($params as $k => $item) {
            if (!isset($fieldMap[$k])) continue;

            $field = $fieldMap[$k];
            if (is_array($item)) {
                $qb->where($field, new Parameter($item));
            }
            else {
                $qb->where($field, $item);
            }
        }


    }
}
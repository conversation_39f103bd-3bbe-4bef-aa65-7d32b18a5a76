<?php

namespace app\models\baseConfPlatform;

use app\util\Common;
use Plus\MVC\Model\ActiveRecord;

/**
 * games
 *
 * @property int    $CHANNEL_ID        渠道ID(CHANNEL_ID)
 * @property string $CHANNEL_CODE      渠道代码
 * @property string $CHANNEL_NAME      渠道名
 * @property string $CHANNEL_MAIN_ID   主渠道ID
 * @property string $CHANNEL_MAIN_NAME 主渠道名
 * @property string $CHANNEL_LABEL     渠道ID/标识(特殊渠道)
 * @property string $CHAN_ID           大渠道ID
 * @property string $BILL_ID           账单分类ID
 * @property string $PLATFORM_ID       客户端ID
 * @property string $POPULARIZE_ID     渠道默认推广分类ID
 * @property string $PROMOTION_WAY_ID  推广方式ID
 * <AUTHOR>
 */
class TbBaseChannelConf extends ActiveRecord
{
    public $_primaryKey = 'id';

    /**
     * 初始化，设置数据、数据库连接类
     *
     * @param array $data 批量配置数据
     *
     */
    public function __construct($data = [])
    {
        parent::__construct($data);
        $this->_db = \Plus::$app->base_conf_platform;
    }

    /**
     * 获取渠道ids
     *
     * @param $where
     *
     * @return TbOathConf
     */
    public function getAllChannel($where)
    {
        $list       = $this->asArray()->findAll($where);
        $channelIds = [];
        foreach ($list as $item) {
            $channelIds[] = intval($item["CHANNEL_ID"]);
        }
        return $channelIds;
    }

    /**
     * @param array $channelNames
     *
     * @return array
     */
    public function getChannelByNames(array $channelNames): array
    {
        $channels = "'" . implode("' , '", $channelNames) . "'";
        $sql      = "SELECT
                channel_id, channel_name, channel_main_id, channel_main_name 
            FROM base_conf_platform.tb_base_channel_conf where channel_name IN ({$channels})";

        if ($_db = Common::pingDoris()) {
            $this->_db = $_db;
        }

        $channelInfoRe = $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        return array_column($channelInfoRe, 'channel_id', 'channel_name');
    }

    /**
     * @param array $channelMainNames
     *
     * @return array
     */
    public function getChannelMainByNames(array $channelMainNames): array
    {
        $channelMains = "'" . implode("' , '", $channelMainNames) . "'";
        $sql          = "
        SELECT distinct channel_main_id, channel_main_name 
            FROM base_conf_platform.tb_base_channel_conf where channel_main_name IN ({$channelMains})";

        if ($_db = Common::pingDoris()) {
            $this->_db = $_db;
        }

        $channelMainRe = $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        return array_column($channelMainRe, 'channel_main_id', 'channel_main_name');
    }

}

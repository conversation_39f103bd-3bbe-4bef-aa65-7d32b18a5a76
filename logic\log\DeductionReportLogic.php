<?php

namespace app\logic\log;

use app\apps\internal\Traits\ColumnsInteract;
use app\service\ConfigService\BasicServ;
use app\service\General\AdpConfServ;
use app\service\Logs\DeductionReportServ;

class DeductionReportLogic
{
    use ColumnsInteract;

    /**
     * @param array $params
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \Smarty\Exception
     * @throws \Exception
     */
    public function tableList(array $params = [], array $paginate = [], array $sort = []): array
    {
        $this->formatParams($params);
        $result    = (new DeductionReportServ())->getList($params, $paginate, $sort);
        $list      = &$result['list'];
        $constMap  = (new BasicServ())->getMultiOptions(['cp_game_id:all', 'game_id', 'channel_id']);
        $replaceFn = $this->replaceColumnDefine($constMap);
        $planIds   = array_filter(array_column($list, 'plan_id'));
        $planInfo  = (new AdpConfServ())->getPlanInfoByPlanIds($planIds);
        if (!empty($planInfo)) {
            $planInfo = array_column($planInfo, 'plan_name', 'plan_id');
        }

        foreach ($list as &$item) {
            $replaceFn($item);
            $planId            = $item['plan_id'] ?? '';
            $item['plan_name'] = $planInfo[$planId] ?? '';

            if ($item['report_type'] == 1) {
                $item['rule_type'] = '次数扣减';
            }
            elseif ($item['report_type'] == 2) {
                $item['rule_type'] = '金额扣减';
            }
            else {
                $item['rule_type'] = '-';
            }
        }

        return $result;
    }

    /**
     * 格式化参数
     *
     * @param $params
     * @return void
     */
    private function formatParams(&$params)
    {
        if (isset($params['rule_type'])) {
            $params['report_type'] = $params['rule_type'];
            unset($params['rule_type']);
        }

        if (isset($params['rule_name'])) {
            $params['report_rule_name'] = $params['rule_name'];
            unset($params['rule_name']);
        }

        if (isset($params['range_date_start']) || isset($params['range_date_end'])) {
            $today                 = date('Y-m-d');
            $params['report_time'] = [
                $params['range_date_start'] ?? $today,
                $params['range_date_end'] ?? $today,
            ];
            sort($params['report_time']);
            unset($params['range_date_start'], $params['range_date_end']);

            $params['report_time'][0] .= ' 00:00:00';
            $params['report_time'][1] .= ' 23:59:59';
        }

        if (!empty($params)) {
            foreach ($params as $k => &$foo) {
                if (
                    in_array($k, ['cp_game_id', 'game_id', 'package_id', 'core_account', 'order_id'])
                    && is_array($foo)
                ) {
                    $foo = implode(',', array_map(fn($item) => "'{$item}'", $foo));
                }
            }
        }
    }
}
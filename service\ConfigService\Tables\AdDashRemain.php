<?php

namespace app\service\ConfigService\Tables;

use app\extension\Constant\AD\AdEnum;
use app\extension\Support\Helpers\Mather;
use app\service\ConfigService\Constants\FieldTag;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class AdDashRemain implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    public function getFields($options = null)
    {
        $adLevel       = $options['ad_level'] ?? AdEnum::AD_PLAN;
        $groups        = $options['groups'] ?? [];
        $today         = new \DateTime();
        $fixRetainNode = array_merge(
            range(1, 29), [44, 59], Mather::findNumInScope(59, 719, 30)
        );
        $result        = $this->getBaseFields(['baseCollect', 'AdAccountBaseCollect', 'campaignBaseCollect']);
        $retainNodes   = 719;

        if ($adLevel == AdEnum::AD_PLAN) {
            $result = $result->merge($this->getBaseFields(['campaignBaseCollect', 'planBaseCollect']));
        }
        elseif ($adLevel == AdEnum::AD_CAMPAIGN) {
            $result = $result->merge($this->getBaseFields(['campaignBaseCollect']));
        }
        elseif ($adLevel == AdEnum::AD_CREATIVE) {
            $result = $result->merge($this->getBaseFields(['campaignBaseCollect', 'planBaseCollect', 'creativeBaseCollect']));
        }

        if (in_array('package_id', $groups) || empty($groups)) {
            $result = $result->merge([
                'package_tags' => ['title' => '包号标签', 'classify' => ['attrs', 'tags']],
            ]);
        }

        if (
            in_array('package_id', $groups)
            || in_array('promotion_channel_id', $groups)
            || empty($groups)
        ) {
            $result = $result->merge([
                'channel_tags' => ['title' => '推广子渠道标签', 'classify' => ['attrs', 'tags']],
            ]);
        }

        if (
            in_array('game_id', $groups)
            || in_array('package_id', $groups)
            || empty($groups)
        ) {
            $result = $result->merge([
                'game_tags' => ['title' => '统计名标签', 'classify' => ['attrs', 'tags']],
            ]);
        }

        $result = $result->merge([
            'new_user'       => ['title' => '广告新增用户', 'sorter' => 'true'],
            'remain_days'    => ['title' => '留存统计天数'],
            'remain_current' => ['title' => '当前留存率'],
        ]);

        $retainNodeList = $this->hasNodesCols(Mather::findIn($retainNodes, $fixRetainNode), 'remain', '<n>留', fn($i) => $i + 1, '次留', true);
        $retainNodeList = FieldTag::tagClassifyToNField($retainNodeList, 'remain',
            [
                ['range' => [1, 29], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_1]],
                ['range' => [44, 179], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_2]],
                ['range' => [209, 359], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_3]],
                ['range' => [389, 719], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_4]],
            ]
        );
        $result         = $result->merge($retainNodeList);

        return $this->formatStandard($result);
    }
}
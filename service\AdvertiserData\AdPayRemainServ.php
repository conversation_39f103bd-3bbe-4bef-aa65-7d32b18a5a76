<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Matcher\AdCreativeMatch;
use app\service\AdvertiserData\Components\Matcher\AdPayRemainMatch;
use app\service\AdvertiserData\Components\Matcher\AdPlanMatch;
use app\service\General\BizTagsServ;
use app\service\General\Helpers\AdpPlatformTable;
use app\service\General\Helpers\BaseConfPlatformTable;
use app\service\General\Helpers\DataSpyTable;
use app\service\General\Helpers\DdcPlatformTable;
use app\util\Common;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class AdPayRemainServ
{
    protected string $dimension = 'plan';

    const QB_MODE_ALL = 63;
    const QB_MODE_POWER = 1;
    const QB_MODE_BASE = 2;
    const QB_MODE_AD_CHANNEL = 4;
    const QB_MODE_ADP_OAUTH = 8;
    const QB_MODE_AD_USER = 16;
    const QB_MODE_AD_ACCOUNT = 32;

    public function __construct($dimension = 'plan')
    {
        $this->dimension = $dimension;
    }

    /**
     * @param SelectQuery $infoQb
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $cols
     * @param int $mode
     * @return array
     * @throws \Exception
     */
    public function getListWithInfoQb(
        SelectQuery $infoQb,
        array       $params = [],
        array       $groups = [],
        array       $paginate = [],
        array       $sort = [],
        array       $cols = [],
        int         $mode = -1
    ): array
    {
        $result = [];
        $qb     = $this->queryBuilderJoinInfoQb($infoQb, $params, $groups);
        // 表示此页能关联到的付费留存数据
        $qb->where('link_mark', '=', 1);

        $matcher = new AdPayRemainMatch([
            'tday'               => 't_pay_remain.tday',
            'channel_id'         => 'slave_info.tztz_promotion_channel_id',
            'channel_main_id'    => 'slave_info.tztz_channel_main_id',
            'package_id'         => 'slave_info.package_id',
            'cp_game_id'         => 'slave_info.cp_game_id',
            'game_id'            => 'slave_info.game_id',
            'platform_id'        => 'slave_info.platform_id',
            'app_show_id'        => 'slave_info.app_show_id',
            'promotion_id'       => 'slave_info.promotion_id',
            'department_id'      => 'slave_info.tztz_department_id',
            'user_id'            => 'slave_info.tztz_user_id',
            'ad_account'         => 'slave_info.ad_account',
            'account_id'         => 'slave_info.account_id',
            'campaign_id'        => 'slave_info.campaign_id',
            'plan_id'            => 'slave_info.plan_id',
            'is_has_appointment' => 'slave_info.is_has_appointment',
            'marketing_goal'     => 'slave_info.marketing_goal',
            'tday'               => 'slave_info.tday',
        ]);
        // 辅助命中部分搜索条件即可
        $tmpParams = array_diff_key($params, ['channel_id', 'channel_main_id', 'department_id', 'user_id']);
        $matcher($qb, $tmpParams);
        $topRemainCols   = $this->remainCols($params, 2);
        $topRemainCols[] = 'remain_1000';
        $topCols         = array_merge([new Fragment('slave_info.*')], $topRemainCols);
        $qb->columns($topCols);
        // 详细列表
        @Common::dumpSql($qb->__toString());

        $result['list'] = $qb->fetchAll();

        // 汇总数据
        $summaryQb = $this->baseQueryBuilder($params, $groups);
        $matcher   = new AdCreativeMatch([
            'tday'                => 't_remain.tday',
            'package_id'          => 't_base.package_id',
            'cp_game_id'          => 't_base.cp_game_id',
            'game_id'             => 't_base.game_id',
            'platform_id'         => 'power.platform_id',
            'app_show_id'         => 'power.app_show_id',
            'channel_main_id'     => 'ad_channel.channel_main_id',
            'channel_id'          => 't_base.channel_id',
            'promotion_id'        => 'power.popularize_v2_id',
            'power.department_id' => 'power.ad_department_id',
            'department_id'       => 'ad_user.department_id',
            'user_id'             => 't_base.user_id',
            'power.user_id'       => 'power.ad_user_id',
            'ad_account'          => 'base_account.ad_account',
            'account_id'          => 'base_account.account_id',
            'campaign_id'         => 't_base.campaign_id',
            'plan_id'             => 't_base.plan_id',
            'creative_id'         => 't_base.creative_id',
            'is_has_appointment'  => 't_base.is_has_appointment',
        ]);
        $matcher($summaryQb, $params);
        $summaryCols   = $this->remainCols($params);
        $summaryCols[] = new Fragment("SUM(IF(day_type = DATEDIFF(NOW(), t_remain.tday) - 1, LOGIN_NUM, 0)) as remain_1000");
        $summaryCols[] = new Fragment("MAX(t_remain.update_time) as last_update_time");
        $summaryQb->columns($summaryCols);
        $summaryQb->where('login_date', '<', new Fragment('DATE(NOW())'));
        @Common::dumpSql($summaryQb->__toString());
        $result['summary'] = $summaryQb->fetchAll();

        return $result;


    }

    /**
     * 查询语句构造
     *
     * @param SelectQuery $infoQb
     * @param array $params
     * @param array $groups
     * @return SelectQuery
     * @throws \Exception
     */
    protected function queryBuilderJoinInfoQb(
        SelectQuery $infoQb, array $params = [], array $groups = []
    ): SelectQuery
    {
        $db           = $this->getConn();
        $remainGroups = [];

        // 默认关联状态
        if (empty($groups)) {
            if ($this->dimension == 'plan') {
                $remainGroups = [
                    'tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id'
                ];
            }
            else {
                $remainGroups = [
                    'tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id', 'creative_id'
                ];
            }
        }
        else {
            $remainGroups = $groups;
        }

        // 包含其他汇总维度
        $mainSubQuery = $this->baseQueryBuilder();
        $matcher      = new AdCreativeMatch([
            'tday'                => 't_remain.tday',
            'package_id'          => 't_base.package_id',
            'cp_game_id'          => 't_base.cp_game_id',
            'game_id'             => 't_base.game_id',
            'platform_id'         => 'power.platform_id',
            'app_show_id'         => 'power.app_show_id',
            'channel_main_id'     => 'ad_channel.channel_main_id',
            'channel_id'          => 't_base.channel_id',
            'promotion_id'        => 'power.popularize_v2_id',
            'power.department_id' => 'power.ad_department_id',
            'department_id'       => 'ad_user.department_id',
            'user_id'             => 't_base.user_id',
            'power.user_id'       => 'power.ad_user_id',
            'ad_account'          => 'base_account.ad_account',
            'account_id'          => 'base_account.account_id',
            'campaign_id'         => 't_base.campaign_id',
            'plan_id'             => 't_base.plan_id',
            'creative_id'         => 't_base.creative_id',
            'is_has_appointment'  => 't_base.is_has_appointment',
            'marketing_goal'      => 't_base.marketing_goal',
        ]);
        $mainSubQuery->where('login_date', '<', new Fragment('DATE(NOW())'));
        $matcher($mainSubQuery, $params);

        $groupMap = [
            'tday'                 => 't_remain.tday',
            'cp_game_id'           => 't_base.cp_game_id',
            'game_id'              => 't_base.game_id',
            'app_show_id'          => 'power.app_show_id',
            'channel_main_id'      => 'tztz_channel_main_id',
            'channel_id'           => 'tztz_promotion_channel_id',
            'promotion_channel_id' => 'tztz_promotion_channel_id',
            'package_id'           => 't_base.package_id',
            'platform_id'          => 'power.platform_id',
            'account_id'           => 't_base.account_id',
            'user_id'              => 'tztz_user_id',
            'department_id'        => 'tztz_department_id',
        ];

        foreach ($remainGroups as $g) {
            if (isset($groupMap[$g])) {
                $g = $groupMap[$g];
            }
            $mainSubQuery->groupBy($g);
        }

        $infoCols     = $this->remainInfoCols($params);
        $remainCols   = $this->remainCols($params);
        $remainCols[] = new Fragment("SUM(IF(day_type = DATEDIFF(NOW(), t_remain.tday) - 1, LOGIN_NUM, 0)) as remain_1000");
        $mainSubQuery->columns(array_merge($infoCols, $remainCols));

        $joinOn = [];

        $remainGroupRe = [
            'channel_main_id'      => 'tztz_channel_main_id',
            'channel_id'           => 'tztz_promotion_channel_id',
            'promotion_channel_id' => 'tztz_promotion_channel_id',
            'user_id'              => 'tztz_user_id',
            'department_id'        => 'tztz_department_id',
        ];

        foreach ($remainGroups as $gg) {
            if (isset($remainGroupRe[$gg])) {
                $gg = $remainGroupRe[$gg];
            }

            $m          = 't_pay_remain.' . $gg;
            $s          = 'slave_info.' . $gg;
            $joinOn[$m] = $s;
        }

        $newDb = $this->getConn();
        $newQb = $newDb->select()->from(new Fragment('(' . $mainSubQuery->__toString() . ') as t_pay_remain'));
        $newQb
            ->leftJoin(new Fragment('(' . $infoQb->__toString() . ')'), 'slave_info')
            ->on($joinOn);

        return $newQb;
    }

    /**
     * @param array $params
     * @param int $mode
     * @return array
     * @throws \Exception
     */
    protected function remainCols(array $params = [], int $mode = 1): array
    {
        $cols      = [];
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $minDay    = $yesterday;

        if (isset($params['tday'])) {
            $tDay   = Arr::wrap($params['tday']);
            $minDay = min($tDay);
        }

        $maxDayNode = days_apart($yesterday, $minDay);

        if ($mode == 1) {
            $colFn = fn($i) => new Fragment("SUM(IF(day_type = {$i}, login_num, 0)) as remain_{$i}");
        }
        else {
            $colFn = fn($i) => "remain_{$i}";
        }

        for ($i = 1; $i <= $maxDayNode; $i++) {
            $cols[] = $colFn($i);
        }

        return $cols;
    }


    /**
     * @param array $params
     * @param array $eqCols
     * @return array
     * @throws \RedisException
     */
    protected function remainInfoCols(array $params, array $eqCols = []): array
    {
        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString      = sprintf(
            "IF(power.channel_id NOT IN (%s), power.channel_id, IF(t_base.channel_id != 0, IF(t_base.channel_id=1013, 4, t_base.channel_id), power.channel_id)) AS tztz_promotion_channel_id",
            $planChannelsString
        );

        $channelMainString = sprintf(
            "COALESCE(IF(power.channel_id IN (%s), IF(ad_channel.channel_main_id != 0, ad_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS tztz_channel_main_id",
            $planChannelsString
        );

        $departmentString = sprintf(
            "COALESCE(IF(`power`.`channel_id` IN (%s), `ad_user`.`department_id`, `power`.`ad_department_id`), 0)   AS `tztz_department_id`",
            $planChannelsString
        );

        $adUserString = sprintf(
            "COALESCE(IF(`power`.`channel_id` IN (%s), `t_base`.`USER_ID`, `POWER`.`AD_USER_ID`),0) AS  `tztz_user_id`",
            $planChannelsString
        );

        $infoColsMap = [
            'tday'                      => 't_base.tday as tday',
            'cp_game_id'                => 't_base.cp_game_id',
            'game_id'                   => 't_base.game_id as game_id',
            'app_show_id'               => 'power.app_show_id as app_show_id',
            'platform_id'               => 'power.platform_id as platform_id',
            'tztz_promotion_channel_id' => new Fragment($channelString),
            'tztz_channel_main_id'      => new Fragment($channelMainString),
            'package_id'                => 't_base.package_id as package_id',
            'promotion_id'              => 'power.popularize_v2_id as promotion_id',
            'campaign_id'               => 't_base.campaign_id as campaign_id',
            'plan_id'                   => 't_base.plan_id as plan_id',
            'tztz_department_id'        => new Fragment($departmentString),
            'tztz_user_id'              => new Fragment($adUserString),
            'account_id'                => 't_base.account_id',
            'account_name'              => 'adp_oauth.advertiser_name as account_name',
            'marketing_goal'            => 'marketing_goal as marketing_goal',
            'dim_user_os'               => new Fragment("case when USER_OS = JSON_ARRAY('ANDROID') then 'ANDROID' when USER_OS = JSON_ARRAY('IOS') then 'IOS' when JSON_CONTAINS(USER_OS, '[\"ANDROID\",\"IOS\"]', '$') = 1 then '混投' else '混投' end as dim_user_os"),
        ];

        if ($this->dimension == 'creative') {
            $infoColsMap['creative_id'] = 't_base.creative_id as creative_id';
        }

        if (!empty($eqCols)) {
            $infoColsMap = array_intersect_key($infoColsMap, array_flip($eqCols));
        }

        return array_values($infoColsMap);
    }


    /**
     * @param array $params
     * @param array $groups
     * @param int $qbMode
     * @return SelectQuery
     */
    protected function baseQueryBuilder(
        array $params = [], array $groups = [], int $qbMode = -1
    ): SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from(DdcPlatformTable::DwsCreativeAdPayRemainDaily . ' as t_remain');
        $qb
            ->leftJoin('adp_platform.tb_adp_campaign', 'base_campaign')
            ->on([
                't_remain.campaign_id'     => 'base_campaign.campaign_id',
                't_remain.main_channel_id' => 'base_campaign.channel_id',
            ]);

        if ($qbMode & self::QB_MODE_POWER) {
            $qb
                ->innerJoin(new Fragment(str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql())), 'power')
                ->on([
                    't_remain.package_id' => 'power.package_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_BASE) {

            if ($this->dimension == 'plan') {
                $qb
                    ->leftJoin(DdcPlatformTable::DwsPlanAdBaseDaily, 't_base')
                    ->on([
                        't_remain.tday'       => 't_base.tday',
                        't_remain.package_id' => 't_base.package_id',
                        't_remain.channel_id' => 't_base.channel_id',
                        't_remain.plan_id'    => 't_base.plan_id',
                    ]);
            }
            else {
                $qb
                    ->leftJoin(DdcPlatformTable::DwsCreativeAdBaseDaily, 't_base')
                    ->on([
                        't_remain.tday'        => 't_base.tday',
                        't_remain.package_id'  => 't_base.package_id',
                        't_remain.channel_id'  => 't_base.channel_id',
                        't_remain.campaign_id' => 't_base.campaign_id',
                        't_remain.plan_id'     => 't_base.plan_id',
                        't_remain.creative_id' => 't_base.creative_id',
                    ]);
            }
        }

        if ($qbMode & self::QB_MODE_AD_CHANNEL) {
            $qb
                ->leftJoin(BaseConfPlatformTable::TbBaseChannelConf, 'ad_channel')
                ->on([
                    't_base.channel_id' => 'ad_channel.channel_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_ADP_OAUTH) {
            $qb
                ->leftJoin(AdpPlatformTable::TbAdpOauth, 'adp_oauth')
                ->on([
                    'adp_oauth.advertiser_id' => 't_base.account_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_AD_USER) {
            $qb
                ->leftJoin(DataSpyTable::AdminUser, 'ad_user')
                ->on([
                    'ad_user.id' => 't_base.user_id',
                ]);
        }

        if ($qbMode & self::QB_MODE_AD_ACCOUNT) {
            $qb
                ->leftJoin(BaseConfPlatformTable::TbAdAccountConf, 'base_account')
                ->on([
                    'base_account.account_id' => 't_base.account_id',
                    'base_account.status'     => new Fragment('1')
                ]);
        }


        return $qb;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
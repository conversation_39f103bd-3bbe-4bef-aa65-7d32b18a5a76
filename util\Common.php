<?php

namespace app\util;

use app\models\DataSpy\AdminUser;
use Plus\Util\StringUtil;

class Common
{
    /**
     * 批量更新
     * @param       $table
     * @param       $data
     * @param array $initDataTmp
     * @return string
     * @throws \Exception
     */
    public static function getBatchUpdateSql($table, $data, $initDataTmp = [])
    {
        if (empty($initDataTmp)) throw new \Exception("initDataTmp no none");
        $values = "";
        foreach ($data as $k => $v) {
            //拼接初始数据
            $update = [];
            foreach ($initDataTmp as $kInit => $vInit) {
                $update[$kInit] = isset($v[$kInit]) ? $v[$kInit] : $vInit;
            }
            $values .= "('" . implode("','", $update) . "'),";
        }
        //拼凑更新条件
        $updateWhere = "";
        $fileds      = [];
        foreach ($initDataTmp as $filed => $v) {
            $updateWhere .= $filed . "=values(" . $filed . "),";
            $fileds[]    = $filed;
        }

        return "INSERT INTO {$table}(" . implode(',', $fileds) . ")VALUES" . trim(
                $values,
                ","
            ) . " ON DUPLICATE KEY UPDATE " . trim($updateWhere, ",");
    }

    //检测doris连接状态
    public static function pingDoris()
    {
        $dorisDbArr = ["ddc_doris1", "ddc_doris2", "ddc_doris3"];
        $source     = false;
        foreach ($dorisDbArr as $db) {
            $c          = \Plus::$app->getComponent($db);
            $doris      = $c->slavePdo[0];
            $timeout    = 5; // 超时时间，单位为秒
            $connection = @fsockopen($doris["server"], $doris["port"], $errno, $errstr, $timeout);
            if (!$connection) continue;
            $source = \Plus::$app->$db;
            break;
        }
        return $source;
    }

    /**
     * 判断是否手机号码
     *
     * @param [type] $number
     * @return boolean
     */
    public static function isPhoneNumber($number)
    {
        $pattern = '/^1[3456789]\d{9}$/';
        return preg_match($pattern, $number);
    }

    //企业微信告警
    public static function qyWxBotForAll($key, $content)
    {
        // 机器人key
        $webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=" . $key;
        // 初始化
        $curl = curl_init();
        // 设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $webhook);
        // 设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 1);
        // 设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        // 设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // 设置post数据
        $post_data = '{"msgtype":"text","text":{"content":"' . $content . '","mentioned_list":"@all"}}'; // @群里所有人
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        // 执行命令
        $data = curl_exec($curl);
        // 关闭URL请求
        curl_close($curl);
        // 显示获得的数据
        // print_r($data);
        return $data;
    }

    public static function qyWxBot($key, $content)
    {
        // 机器人key
        $webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=" . $key;
        // 初始化
        $curl = curl_init();
        // 设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $webhook);
        // 设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 1);
        // 设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        // 设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // 设置post数据
        $post_data = '{"msgtype":"text","text":{"content":"' . $content . '"}}'; // @群里所有人
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        // 执行命令
        $data = curl_exec($curl);
        // 关闭URL请求
        curl_close($curl);
        // 显示获得的数据
        // print_r($data);
        return $data;
    }


    public static function unicode_decode($name)
    {

        $json = '{"str":"' . $name . '"}';
        $arr  = json_decode($json, true);
        if (empty($arr)) return '';
        return $arr['str'];
    }

    /**
     * 获取doris连接标识
     *
     * @return false|string
     * @throws \Exception
     */
    public static function pingDorisIndex()
    {
        $dorisDbArr = ["ddc_doris1", "ddc_doris2", "ddc_doris3"];
        $source     = false;
        foreach ($dorisDbArr as $db) {
            $c          = \Plus::$app->getComponent($db);
            $doris      = $c->slavePdo[0];
            $timeout    = 5; // 超时时间，单位为秒
            $connection = @fsockopen($doris["server"], $doris["port"], $errno, $errstr, $timeout);
            if (!$connection) continue;

            $source = $db;
            break;
        }
        return $source;
    }

    /**
     * @param string $sql 打印输出的sql语句
     * @param string $title 标题
     * @return bool|mixed
     * @throws \Exception
     */
    public static function dumpSql($sql, $title = "")
    {
        $redis    = \Plus::$app->redis;
        $username = \Plus::$service->admin->getUsername();
        $switch   = $redis->get($username . ":debug:switch");
        if ($switch != 1) return false;
        if (!$title) {
            $router     = \Plus::$app->router;
            $app        = StringUtil::unCamelize($router->getApp(), '-');
            $controller = StringUtil::unCamelize($router->getController(), '-');
            $action     = StringUtil::unCamelize($router->getAction(), '-');
            $uri        = "/" . implode('/', [$app, $controller, $action]);
            $urImap     = [
                "/internal/ad-plan/data"                     => "广告投放/广告数据看板",
                "/internal/ad-creative/data"                 => "广告投放/广告数据看板",
                "/internal/ad-plan-ltv/data"                 => "广告投放/广告LTV、ROI",
                "/internal/ad-creative-ltv/data"             => "广告投放/广告LTV、ROI",
                "/internal/ad-plan-remain/data"              => "广告投放/广告留存",
                "/internal/ad-creative-remain/data"          => "广告投放/广告留存",
                "/advertise/ad-month/data"                   => "广告投放/月度报表",
                "/advertise/ad-booking-dashboard/data"       => "广告投放/广告投放预约数据",
                "/operator/first-login/data"                 => "游戏运营/综合指标报表/首登",
                "/operator/first-login-ltv/data"             => "游戏运营/LTV、ROI统计/首登",
                "/operator/new-login-ltv/data"               => "游戏运营/LTV、ROI统计/新增",
                "/operator/first-login-remain/data"          => "游戏运营/留存统计/首登",
                "/operator/new-login-remain/data"            => "游戏运营/留存统计/新增",
                "/operator/event-distribute-firstlogin/data" => "游戏运营/用户来源时间分布/首登",
                "/operator/event-distribute-ad/data"         => "游戏运营/用户来源时间分布/广告新增",
            ];
            if (array_key_exists($uri, $urImap)) {
                $title = $urImap[$uri];
            }
            else {
                $title = "未知报表";
            }
        }
        if (!$sql || !$title) {
            throw new \Exception("参数错误:sql,title");
        }

        $date    = date("Y-m-d H:i:s", time());
        $message = "【{$title}】【{$date}】\n $sql";
        $data    = $redis->get($username . ":debug:content") ? json_decode($redis->get($username . ":debug:content"), true) : [];
        array_push($data, $message);
        return $redis->set($username . ":debug:content", json_encode($data));
    }

    /**
     * @param $params
     * @return array
     */
    public static function parseArgv($params): array
    {
        $result = [];

        foreach ($params as $f) {
            if (str_contains($f, '=')) {
                [$key, $value] = explode('=', $f);

                if (str_contains($f, ',')) {
                    $value = explode(',', $value);
                }

                $result[$key] = $value;
            }
        }

        return $result;
    }

}


<?php
/**
 * 微信数据上报
 * Created by PhpStorm.
 * User: Tim
 * Date: 2019/3/14
 * Time: 16:33
 */

namespace app\ad_upload\channels;

class Gdt extends TxGdt
{
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'ACTIVATE_APP');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, 'REGISTER');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'PURCHASE');
    }

    /**
     * @param $info
     * @param $uploadType
     * @param $remarkSuffix
     * @return void
     */
    protected function uploadData($info, $uploadType = 'ACTIVATE_APP', $remarkSuffix = 'TX_GDT_PARAM_')
    {
        parent::uploadData($info, $uploadType, 'GDT_PARAM_');
    }
}

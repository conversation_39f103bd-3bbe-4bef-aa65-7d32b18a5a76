<?php

namespace app\service\AdvertiseLive;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiseLive\Helpers\TableConst;
use Spiral\Database\Database;
use Spiral\Database\Injection\Parameter;


class LiveAnchorServ
{
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = []
    ): array
    {
        $db = $this->getConn();
        $qb = $db->select()->from(TableConst::CONF_LIVE_ANCHOR);

        $qb->columns([
            'id as id',
            'team_properties as team_properties',
            'anchor_name as anchor_name',
            'docking_partner as docking_partner',
            'operator_id as operator_id',
            'state as state',
            'sorted as sorted',
        ]);

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        if (!empty($params['state'])) {
            $qb->where('state', $params['state']);
        }

        return $qb->fetchAll();
    }

    /**
     * @param $list
     *
     * @return mixed
     * @throws \Throwable
     */
    public function updateMultiById($list)
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $nDb) use ($list) {
            foreach ($list as $item) {
                $id = Arr::pull($item, 'id');

                if (is_null($id)) continue;

                $nDb
                    ->table(TableConst::CONF_LIVE_ANCHOR)
                    ->update($item)
                    ->where('id', $id)
                    ->run();
            }

            return true;
        });
    }

    /**
     * @param array $singleData
     *
     * @return mixed
     * @throws \Throwable
     */
    public function insert(array $singleData = [])
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $nDb) use ($singleData) {
            $baseQb = $nDb->table(TableConst::CONF_LIVE_ANCHOR);

            if (empty($singleData['anchor_name'])) {
                throw  new \InvalidArgumentException('主播名称缺失');
            }

            $count = (clone $baseQb)->select()->where('anchor_name', $singleData['anchor_name'])->count();

            if ($count > 0) {
                throw new \InvalidArgumentException('该主播已存在');
            }

            if (!isset($singleData['team_properties'])) {
                throw new \InvalidArgumentException('主播团队参数缺失');
            }

            $fields = [
                'team_properties' => '',
                'anchor_name'     => '',
                'docking_partner' => '',
                'sorted'          => 0,
            ];

            $data = array_intersect_key($singleData, $fields);

            return (clone $baseQb)->insertOne($data);
        });

    }

    /**
     * 硬删除
     *
     * @param array $ids
     *
     * @return mixed
     * @throws \Throwable
     */
    public function removeByIds(array $ids)
    {
        $db = $this->getConn();

        return $db->transaction(function (Database $nDb) use ($ids) {
            $baseQb = $nDb->table(TableConst::CONF_LIVE_ANCHOR);

            $delQb = (clone $baseQb)->delete()->where('id', new Parameter($ids));

            return $delQb->run();
        });
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('base_conf_platform');
    }

}
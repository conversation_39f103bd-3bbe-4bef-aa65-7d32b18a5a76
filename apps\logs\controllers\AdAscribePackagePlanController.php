<?php

namespace app\apps\logs\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\service\Logs\AdAscribePackagePlanServ;
/**
 * 日志 -> 广告归因包号/计划
 *
 * @route /logs/ad-ascribe-package-plan/*
 *
 */
class AdAscribePackagePlanController extends BaseTableController
{
    /**
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $options = $params->toArray();

        try {
            return (new AdAscribePackagePlanServ())->listInfo($options);
        }
        catch (\UnexpectedValueException $ue) {
            return ['list' => [], 'summary' => [], 'total' => 0];
        }
    }

    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '登录时间', 'dataIndex'    => 'login_time', 'classify'          => ['attrs', 'base']],
            ['title' => '登录账号', 'dataIndex'    => 'login_account', 'classify'       => ['attrs', 'base']],
            ['title' => '核心账号', 'dataIndex'    => 'core_account', 'classify'        => ['attrs', 'base']],
            ['title' => '登录包号', 'dataIndex'    => 'package_id', 'classify'          => ['attrs', 'base']],
            ['title' => '是否广告新增', 'dataIndex'  => 'is_ad_new', 'classify'           => ['attrs', 'base']],
            ['title' => '是否子账号', 'dataIndex'   => 'is_sub_account', 'classify'      => ['attrs', 'base']],
            ['title' => '广告新增主账号', 'dataIndex' => 'main_account', 'classify'        => ['attrs', 'base']],
            ['title' => '归因包号', 'dataIndex'    => 'newlogin_package_id', 'classify' => ['attrs', 'base']],
            ['title' => '来源渠道', 'dataIndex'    => 'ad_name', 'classify' => ['attrs', 'base']],
            ['title' => '归因计划', 'dataIndex'    => 'plan', 'classify'                => ['attrs', 'base']],
            ['title' => '广告新增时间', 'dataIndex'  => 'newlogin_time', 'classify'       => ['attrs', 'base']],
            ['title' => '匹配方式', 'dataIndex'    => 'match_type', 'classify'          => ['attrs', 'base']],
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                ],
            ],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $start = date('Y-m-d');
        $end   = date('Y-m-d');
        /*

        */
        return collect([
            // 登录包号
            ['field' => 'package_id'],
            // 登录时间
            ['field' => 'login_time'], // 统计日期开始时间
            // 归因包号
            ['field' => 'newlogin_package_id'],
            // 广告新增时间
            ['field' => 'newlogin_time'],
            // 归因计划或短链名称
            ['field' => 'plan'],
            // 是否广告新增
            ['field' => 'is_ad_new'],
            // 是否子账号
            ['field' => 'is_sub_account'],

            // ['field' => 'page_size', 'default' => 100],
            // ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }
}
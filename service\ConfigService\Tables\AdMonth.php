<?php

namespace app\service\ConfigService\Tables;

use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class AdMonth implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    public function getFields($options = null)
    {
        $fields = collect([
            't_month'           => ['title' => '月份', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'cp_game_id'        => ['title' => '游戏原名', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'game_id'           => ['title' => '游戏统计名', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'channel_main_id'   => ['title' => '主渠道', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'channel_id'        => ['title' => '推广子渠道', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'package_id'        => ['title' => '包号', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'platform_id'       => ['title' => '客户端', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'promotion_id'      => ['title' => '推广分类', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'department_id'     => ['title' => '投放部门', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'user_id'           => ['title' => '投放人', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'new_user_num'      => ['title' => '广告新增用户', 'sorter' => true, 'classify' => ['attrs', 'new_user_base']],
            'new_user_cost'     => ['title' => '新用户成本', 'sorter' => true, 'classify' => ['attrs', 'new_user_base']],
            'cost_discount_sum' => ['title' => '返点后消耗金额', 'sorter' => true, 'classify' => ['attrs', 'new_user_base']],
            'pay_sum'           => ['title' => '付费金额', 'classify' => ['attrs', 'new_user_base']],
            'new_user_pay'      => ['title' => '新用户付费金额', 'classify' => ['attrs', 'new_user_base']],
            'total_ltv'         => ['title' => '累计LTV', 'classify' => ['attrs', 'new_user_base']],
            'total_roi'         => ['title' => '累计ROI', 'classify' => ['attrs', 'new_user_base']],
        ]);

        $rangeN = [1, 3, 7, 15, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330, 360];
        $ltvN   = [];
        $roiN   = [];
        foreach ($rangeN as $n) {
            $t = ['title' => 'LTV' . $n];
            $r = ['title' => 'ROI' . $n];

            if (1 <= $n && $n <= 30) {
                $t['classify'] = ['attrs', 'ltv_group_1'];
                $r['classify'] = ['attrs', 'roi_group_1'];
            }
            elseif (60 <= $n && $n <= 360) {
                $t['classify'] = ['attrs', 'ltv_group_2'];
                $r['classify'] = ['attrs', 'roi_group_2'];
            }
            $ltvN['ltv_' . $n] = $t;
            $roiN['roi_' . $n] = $r;
        }

        $fields = $fields->merge($ltvN)->merge($roiN);

        return $this->formatStandard($fields);
    }
}
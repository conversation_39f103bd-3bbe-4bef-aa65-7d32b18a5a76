<?php

/**
 * 头条微信小游戏数据上报
 * Created by PhpStorm.
 * User: weiqi
 * Date: 2022/04/18
 * Time: 09:15
 * phpcs:disable
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class ToutiaoXcx extends AdBaseInterface
{

    /**
     * 上报登录
     * @param array $info
     * @param array $ext
     */
    public function uploadLogin($info, $ext = [])
    {
        $this->uploadData($info, 'LOGIN');
    }

    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'ACTIVE');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'PAY');
    }


    public function uploadBefore($info, $uploadMethod)
    {
        if (!parent::uploadBefore($info, $uploadMethod)) {
            return false;
        }
        $clueToken = !empty($info['active_info']['android_id']) ? $info['active_info']['android_id'] : ($info['ANDROID_ID'] ?? '');

        $info['clueToken']       = $clueToken;
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'toutiao_xcx';

        if ($uploadMethod == 'uploadPay') {
            $logInfo['reported_status']                = -1;
            $info['paid_report_log']['reported_money'] = $info['paid_report_log']['reported_money'] ?? $info['MONEY'];
            //缺少参数的不上报
            if (empty($clueToken)) {
                $info['paid_report_log']['reported_money']     = 0;
                $info['paid_report_log']['no_reported_origin'] = '缺少必要上报参数clue token';
                \Plus::$app->log->info(json_encode($info) . '缺少必要上报参数clue token', [], self::LOG_DIR);
                $this->logPaidToDoris($info, $logInfo, '');
                return false;
            }
        }// end if()
        return $info;
    }

    /**
     *
     * 上报数据
     *
     * @param        $info
     * @param string $type
     */
    private function uploadData($info, $type = '')
    {
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'toutiao_xcx';
        $logInfo['log_type']     = 'reported_platform_log';

        $aValue = 0;
        switch ($type) {
            case 'LOGIN':
                $aValue = '6';
                break;
            case 'ACTIVE':
                $aValue = '0';
                break;
            case 'REG':
                $aValue = '1';
                break;
            case 'PAY':
                $aValue = '2';
                break;
        }
        // 上传参数
        $requestParams = [
            'clue_token' => $info['clueToken'],
            'union_id'   => '',
            'open_id'    => $info['DEVICE_CODE'],
            'event_type' => $aValue, // 0-激活, 1-注册, 2-付费, 6-次留
        ];

        if ($type == 'PAY') {
            $requestParams['props']['pay_amount'] = intval($info['MONEY'] * 100);
        }

        // {"token":"3E6E5B61253B1C8AEE5B2C344768EB70","url":"https:\/\/clue.oceanengine.com\/outer\/wechat\/applet\/token\/1709510552712196"}
        // 生成签名
        $token     = (!empty($info['EXT']['token'])) ? $info['EXT']['token'] : "00019617991CDCDF5A8569C01CAAFD13";
        $nonce     = (string)rand(100, 999);
        $timestamp = (string)time();

        $params = [
            'token'     => $token,
            'nonce'     => $nonce,
            'timestamp' => $timestamp,
        ];

        usort($params, 'strcmp');
        $signature = sha1(implode('', $params));

        $url = (isset($info['EXT']['url']) && !empty($info['EXT']['url'])) ? $info['EXT']['url'] : 'https://clue.oceanengine.com/outer/wechat/applet/token/1635292116641796';
        $url .= '?timestamp=' . $timestamp . '&nonce=' . $nonce . '&signature=' . $signature;

        // 上报
        //        $headers = array("Content-type: application/json;charset='utf-8'", "Accept: application/json", "Cache-Control: no-cache", "Pragma: no-cache");
        //        $res = $this->curlPost($url, $requestParams, $headers);
        $logInfo['request'] = json_encode(['url' => $url, 'params' => $requestParams]);

        $http = new Http($url);
        $res  = $http->postJson($requestParams);

        $logInfo['response'] = $res;
        // 记录上报结果
        $resArr = json_decode($res, true);

        if (isset($resArr['status']) && ($resArr['status'] == 0 || $resArr['status'] == 200)) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
            if (!empty($info['paid_report_log'])) {
                $info['paid_report_log']['no_reported_origin'] = '接口返回编码异常';
            }
        }

        $this->log($info, $logInfo, $res, $url);
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }
}

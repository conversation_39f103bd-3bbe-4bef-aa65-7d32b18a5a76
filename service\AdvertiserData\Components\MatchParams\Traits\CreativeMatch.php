<?php

namespace app\service\AdvertiserData\Components\MatchParams\Traits;

use app\models\AdpPlatform\TbAdpCreativeBase;
use app\service\AdvertiserData\Components\Helpers\Convert;

trait CreativeMatch
{
    /**
     * ### 创意ID搜索
     * **注意null也会作为筛选条件**
     *
     * @param $params
     *
     * @return string
     */
    protected function matchCreativeId($params): string
    {
        if (!array_key_exists('creative_id', $params)) return '';
        $field = $this->getField('creative_id');

        if (is_null($params['creative_id'])) {
            return "{$field} IS NULL";
        }

        $data = Convert::convertInString($params['creative_id']);

        return "{$field} IN ({$data})";
    }

    /**
     * @param $params
     *
     * @return string
     */
    protected function matchCreativeName($params): string
    {
        if (empty($params['creative_name'])) return '';

        $field = $this->getField('creative_id');
        $data  = $params['creative_name'];
        $ids = (new TbAdpCreativeBase())->getCreativeIdByName($data,$params["range_date_start"],$params["range_date_end"]);
        if($ids){
            return "({$field} IN ('".implode("','",$ids)."'))";
        }
        return "({$field} ='error' )";
    }
}
<?php

namespace app\apps\logs\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\log\UserLogQueryLogic;

class UserLogQueryInfoController extends BaseTableController
{
    /**
     * @param Collection $params
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        return (new UserLogQueryLogic())->getInfoList($params->toArray());
    }

    protected function fields(Collection $params): array
    {
        $rowOptions = $params['row_options'] ?? '';

        if (empty($rowOptions)) {
            throw new \Exception('行数据缺失');
        }

        $rowOptions = \json_decode($rowOptions, true);

        if (JSON_ERROR_NONE != json_last_error()) {
            throw new \Exception('行数据解析错误');
        }

        $fields    = [];
        $eventType = Arr::pull($rowOptions, 'event_type');

        if ($eventType == '设备激活') {
            $fields = [
                ['title' => '事件类型', 'dataIndex' => 'event_type'],
                ['title' => '发生时间', 'dataIndex' => 'event_time', 'sorter' => true],
                ['title' => '上报时间', 'dataIndex' => 'time_server', 'sorter' => true],
                ['title' => '游戏ID', 'dataIndex' => 'game_id', 'sorter' => true],
                ['title' => '游戏包号', 'dataIndex' => 'package_id', 'sorter' => true],
                ['title' => '设备机型', 'dataIndex' => 'device_type', 'sorter' => true],
                ['title' => '操作系统', 'dataIndex' => 'os', 'sorter' => true],
                ['title' => '系统版本', 'dataIndex' => 'os_version', 'sorter' => true],
                ['title' => 'SDK版本号', 'dataIndex' => 'sdk_version', 'sorter' => true],
                ['title' => '游戏版本号', 'dataIndex' => 'game_version', 'sorter' => true],
                ['title' => '网络环境', 'dataIndex' => 'network_type', 'sorter' => true],
                ['title' => 'IP地址', 'dataIndex' => 'ip', 'sorter' => true],
                ['title' => 'DEVICE_CODE', 'dataIndex' => 'device_code', 'sorter' => true],
                ['title' => 'OAID', 'dataIndex' => 'oaid', 'sorter' => true],
            ];
        }
        elseif ($eventType == '账号注册') {
            $fields = [
                ['title' => '事件类型', 'dataIndex' => 'event_type'],
                ['title' => '发生时间', 'dataIndex' => 'event_time', 'sorter' => true],
                ['title' => '上报时间', 'dataIndex' => 'time_server', 'sorter' => true],
                ['title' => '游戏ID', 'dataIndex' => 'game_id', 'sorter' => true],
                ['title' => '游戏包号', 'dataIndex' => 'package_id', 'sorter' => true],
                ['title' => '设备机型', 'dataIndex' => 'device_type', 'sorter' => true],
                ['title' => '操作系统', 'dataIndex' => 'os', 'sorter' => true],
                ['title' => '系统版本', 'dataIndex' => 'os_version', 'sorter' => true],
                ['title' => 'SDK版本号', 'dataIndex' => 'sdk_version', 'sorter' => true],
                ['title' => '游戏版本号', 'dataIndex' => 'game_version', 'sorter' => true],
                ['title' => '网络环境', 'dataIndex' => 'network_type', 'sorter' => true],
                ['title' => 'IP地址', 'dataIndex' => 'ip', 'sorter' => true],
                ['title' => 'DEVICE_CODE', 'dataIndex' => 'device_code', 'sorter' => true],
                ['title' => '登录账号', 'dataIndex' => 'login_account', 'sorter' => true],
                ['title' => '核心账号', 'dataIndex' => 'core_account', 'sorter' => true],
            ];
        }
        elseif ($eventType == '账号登录') {
            $fields = [
                ['title' => '事件类型', 'dataIndex' => 'event_type'],
                ['title' => '发生时间', 'dataIndex' => 'event_time', 'sorter' => true],
                ['title' => '上报时间', 'dataIndex' => 'time_server', 'sorter' => true],
                ['title' => '游戏ID', 'dataIndex' => 'game_id', 'sorter' => true],
                ['title' => '游戏包号', 'dataIndex' => 'package_id', 'sorter' => true],
                ['title' => '设备机型', 'dataIndex' => 'device_type', 'sorter' => true],
                ['title' => '操作系统', 'dataIndex' => 'os', 'sorter' => true],
                ['title' => '系统版本', 'dataIndex' => 'os_version', 'sorter' => true],
                ['title' => 'SDK版本号', 'dataIndex' => 'sdk_version', 'sorter' => true],
                ['title' => '游戏版本号', 'dataIndex' => 'game_version', 'sorter' => true],
                ['title' => '网络环境', 'dataIndex' => 'network_type', 'sorter' => true],
                ['title' => 'IP地址', 'dataIndex' => 'ip', 'sorter' => true],
                ['title' => 'DEVICE_CODE', 'dataIndex' => 'device_code', 'sorter' => true],
                ['title' => 'OAID', 'dataIndex' => 'oaid', 'sorter' => true],
                ['title' => '登录账号', 'dataIndex' => 'login_account', 'sorter' => true],
                ['title' => '核心账号', 'dataIndex' => 'core_account', 'sorter' => true],
            ];
        }
        elseif ($eventType == '角色登录') {
            $fields = [
                ['title' => '事件类型', 'dataIndex' => 'event_type'],
                ['title' => '发生时间', 'dataIndex' => 'event_time', 'sorter' => true],
                ['title' => '上报时间', 'dataIndex' => 'time_server', 'sorter' => true],
                ['title' => '游戏ID', 'dataIndex' => 'game_id', 'sorter' => true],
                ['title' => '游戏包号', 'dataIndex' => 'package_id', 'sorter' => true],
                ['title' => '设备机型', 'dataIndex' => 'device_type', 'sorter' => true],
                ['title' => '操作系统', 'dataIndex' => 'os', 'sorter' => true],
                ['title' => '系统版本', 'dataIndex' => 'os_version', 'sorter' => true],
                ['title' => 'SDK版本号', 'dataIndex' => 'sdk_version', 'sorter' => true],
                ['title' => '游戏版本号', 'dataIndex' => 'game_version', 'sorter' => true],
                ['title' => '网络环境', 'dataIndex' => 'network_type', 'sorter' => true],
                ['title' => 'IP地址', 'dataIndex' => 'ip', 'sorter' => true],
                ['title' => 'DEVICE_CODE', 'dataIndex' => 'device_code', 'sorter' => true],
                ['title' => '区服', 'dataIndex' => 'game_server', 'sorter' => true],
                ['title' => '登录账号', 'dataIndex' => 'login_account', 'sorter' => true],
                ['title' => '核心账号', 'dataIndex' => 'core_account', 'sorter' => true],
                ['title' => '角色ID', 'dataIndex' => 'role_id', 'sorter' => true],
                ['title' => '角色名', 'dataIndex' => 'role_name', 'sorter' => true],
                ['title' => '角色等级', 'dataIndex' => 'role_rank', 'sorter' => true],
                ['title' => '角色VIP等级', 'dataIndex' => 'role_vip', 'sorter' => true],
            ];
        }
        elseif ($eventType == '用户充值') {
            $fields = [
                ['title' => '事件类型', 'dataIndex' => 'event_type'],
                ['title' => '下单时间(发生)', 'dataIndex' => 'order_time', 'sorter' => true],
                ['title' => '支付时间', 'dataIndex' => 'pay_time', 'sorter' => true],
                ['title' => '游戏ID', 'dataIndex' => 'game_id', 'sorter' => true],
                ['title' => '游戏包号', 'dataIndex' => 'package_id', 'sorter' => true],
                ['title' => 'SDK版本号', 'dataIndex' => 'sdk_version', 'sorter' => true],
                ['title' => '游戏版本号', 'dataIndex' => 'game_version', 'sorter' => true],
                //                ['title' => '系统版本', 'dataIndex' => 'os_version'],
                //                ['title' => '网络环境', 'dataIndex' => 'network_type'],
                ['title' => 'IP地址', 'dataIndex' => 'ip', 'sorter' => true],
                ['title' => 'DEVICE_CODE', 'dataIndex' => 'device_code', 'sorter' => true],
                ['title' => '区服', 'dataIndex' => 'game_server', 'sorter' => true],
                ['title' => '登录账号', 'dataIndex' => 'login_account', 'sorter' => true],
                ['title' => '核心账号', 'dataIndex' => 'core_account', 'sorter' => true],
                ['title' => '角色ID', 'dataIndex' => 'role_id', 'sorter' => true],
                ['title' => '角色名', 'dataIndex' => 'role_name', 'sorter' => true],
                ['title' => '角色等级', 'dataIndex' => 'role_rank', 'sorter' => true],
                ['title' => '角色VIP等级', 'dataIndex' => 'role_vip', 'sorter' => true],
                ['title' => '我方订单号', 'dataIndex' => 'order_id', 'sorter' => true],
                ['title' => 'CP订单号', 'dataIndex' => 'cp_order_id', 'sorter' => true],
                ['title' => '第三方订单号', 'dataIndex' => 'channel_order_id', 'sorter' => true],
                ['title' => '支付结果', 'dataIndex' => 'pay_result', 'sorter' => true],
                ['title' => '订单金额', 'dataIndex' => 'money', 'sorter' => true],
                ['title' => '游戏币', 'dataIndex' => 'game_coin', 'sorter' => true],
                ['title' => '商品名', 'dataIndex' => 'goods_name', 'sorter' => true],
                ['title' => '游戏币到账结果', 'dataIndex' => 'game_result', 'sorter' => true],
                ['title' => '订单状态', 'dataIndex' => 'order_type', 'sorter' => true],
            ];
        }
        elseif ($eventType == '崩溃日志') {
            $fields = [
                ['title' => '事件类型', 'dataIndex' => 'event_type'],
                ['title' => '发生时间', 'dataIndex' => 'event_time', 'sorter' => true],
                ['title' => '上报时间', 'dataIndex' => 'time_server', 'sorter' => true],
                ['title' => '游戏ID', 'dataIndex' => 'game_id', 'sorter' => true],
                ['title' => '游戏包号', 'dataIndex' => 'package_id', 'sorter' => true],
                ['title' => '设备机型', 'dataIndex' => 'device_type', 'sorter' => true],
                ['title' => '操作系统', 'dataIndex' => 'os', 'sorter' => true],
                ['title' => '系统版本', 'dataIndex' => 'os_version', 'sorter' => true],
                ['title' => 'SDK版本号', 'dataIndex' => 'sdk_version', 'sorter' => true],
                ['title' => '游戏版本号', 'dataIndex' => 'game_version', 'sorter' => true],
                ['title' => '网络环境', 'dataIndex' => 'network_type', 'sorter' => true],
                ['title' => 'IP地址', 'dataIndex' => 'ip', 'sorter' => true],
                ['title' => 'DEVICE_CODE', 'dataIndex' => 'device_code', 'sorter' => true],
                ['title' => '报错类型', 'dataIndex' => 'error_type', 'sorter' => true],
                ['title' => '报错内容', 'dataIndex' => 'error_content', 'sorter' => true],
            ];
        }
        elseif ($eventType == '角色升级') {
            $fields = [
                ['title' => '事件类型', 'dataIndex' => 'event_type'],
                ['title' => '发生时间', 'dataIndex' => 'event_time', 'sorter' => true],
                ['title' => '上报时间', 'dataIndex' => 'time_server', 'sorter' => true],
                ['title' => '游戏ID', 'dataIndex' => 'game_id', 'sorter' => true],
                ['title' => '游戏包号', 'dataIndex' => 'package_id', 'sorter' => true],
                ['title' => '设备机型', 'dataIndex' => 'device_type', 'sorter' => true],
                ['title' => '操作系统', 'dataIndex' => 'os', 'sorter' => true],
                ['title' => '系统版本', 'dataIndex' => 'os_version', 'sorter' => true],
                ['title' => 'SDK版本号', 'dataIndex' => 'sdk_version', 'sorter' => true],
                ['title' => 'IP地址', 'dataIndex' => 'ip', 'sorter' => true],
                ['title' => 'DEVICE_CODE', 'dataIndex' => 'device_code', 'sorter' => true],
                ['title' => '登录账号', 'dataIndex' => 'login_account', 'sorter' => true],
                ['title' => '核心账号', 'dataIndex' => 'core_account', 'sorter' => true],
                ['title' => '角色ID', 'dataIndex' => 'role_id', 'sorter' => true],
                ['title' => '角色名', 'dataIndex' => 'role_name', 'sorter' => true],
                ['title' => '角色等级', 'dataIndex' => 'role_rank', 'sorter' => true],
                ['title' => '角色VIP等级', 'dataIndex' => 'role_vip', 'sorter' => true],
            ];
        }
        elseif ($eventType == '城堡升级') {
            $fields = [
                ['title' => '事件类型', 'dataIndex' => 'event_type'],
                ['title' => '发生时间', 'dataIndex' => 'event_time', 'sorter' => true],
                ['title' => '上报时间', 'dataIndex' => 'time_server', 'sorter' => true],
                ['title' => '游戏ID', 'dataIndex' => 'game_id', 'sorter' => true],
                ['title' => '游戏包号', 'dataIndex' => 'package_id', 'sorter' => true],
                ['title' => '设备机型', 'dataIndex' => 'device_type', 'sorter' => true],
                ['title' => '操作系统', 'dataIndex' => 'os', 'sorter' => true],
                ['title' => '系统版本', 'dataIndex' => 'os_version', 'sorter' => true],
                ['title' => 'SDK版本号', 'dataIndex' => 'sdk_version', 'sorter' => true],
                ['title' => 'IP地址', 'dataIndex' => 'ip', 'sorter' => true],
                ['title' => 'DEVICE_CODE', 'dataIndex' => 'device_code', 'sorter' => true],
                ['title' => '登录账号', 'dataIndex' => 'login_account', 'sorter' => true],
                ['title' => '核心账号', 'dataIndex' => 'core_account', 'sorter' => true],
                ['title' => '角色ID', 'dataIndex' => 'role_id', 'sorter' => true],
                ['title' => '角色名', 'dataIndex' => 'role_name', 'sorter' => true],
                ['title' => '角色等级', 'dataIndex' => 'role_rank', 'sorter' => true],
                ['title' => '角色VIP等级', 'dataIndex' => 'role_vip', 'sorter' => true],
            ];
        }
        elseif ($eventType == '新增创角') {
            $fields = [
                ['title' => '事件类型', 'dataIndex' => 'event_type'],
                ['title' => '发生时间', 'dataIndex' => 'event_time', 'sorter' => true],
                ['title' => '上报时间', 'dataIndex' => 'time_server', 'sorter' => true],
                ['title' => '游戏ID', 'dataIndex' => 'game_id', 'sorter' => true],
                ['title' => '游戏包号', 'dataIndex' => 'package_id', 'sorter' => true],
                ['title' => '设备标识(device_key)', 'dataIndex' => 'device_key', 'sorter' => true],
                ['title' => '区服ID', 'dataIndex' => 'game_server', 'sorter' => true],
                ['title' => '登录账号', 'dataIndex' => 'login_account', 'sorter' => true],
                ['title' => '核心账号', 'dataIndex' => 'core_account', 'sorter' => true],
                ['title' => '角色ID', 'dataIndex' => 'role_id', 'sorter' => true],
                ['title' => '角色名', 'dataIndex' => 'role_name', 'sorter' => true],
                ['title' => '角色等级', 'dataIndex' => 'role_rank', 'sorter' => true],
                ['title' => '角色VIP等级', 'dataIndex' => 'role_vip', 'sorter' => true],
                ['title' => 'IP', 'dataIndex' => 'ip', 'sorter' => true],
            ];
        }


        return ['fields' => $fields];
    }

}
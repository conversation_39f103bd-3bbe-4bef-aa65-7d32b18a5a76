<?php

namespace app\service\ConfigService\Tables;

use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\Mather;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

/**
 * @AdCreative TableFieldsHubServ服务提供者
 */
class AdCreative implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    /**
     * @inerhitDoc
     * @param mixed $options
     * @return Collection
     * @throws \Exception
     */
    public function getFields($options = null): Collection
    {
        $today       = new \DateTime();
        $remainNodes = array_merge(range(1, 6), [14, 29]);
        $NNodes      = array_merge(range(1, 60), []);
        $groups      = $options['groups'] ?? [];

        $collect = $this->getBaseFields([
            'baseCollect',
            'AdAccountBaseCollect',
            'creativeBaseCollect',
            'campaignBaseCollect',
            'planBaseCollect',
        ]);

        if (in_array('package_id', $groups) || empty($groups)) {
            $collect = $collect->merge([
                'package_tags' => ['title' => '包号标签', 'classify' => ['attrs', 'tags']],
            ]);
        }

        if (
            in_array('package_id', $groups)
            || in_array('promotion_channel_id', $groups)
            || empty($groups)
        ) {
            $collect = $collect->merge([
                'channel_tags' => ['title' => '推广子渠道标签', 'classify' => ['attrs', 'tags']],
            ]);
        }

        if (
            in_array('game_id', $groups)
            || in_array('package_id', $groups)
            || empty($groups)
        ) {
            $collect = $collect->merge([
                'game_tags' => ['title' => '统计名标签', 'classify' => ['attrs', 'tags']],
            ]);
        }

        $collect    = $collect->merge($this->getBaseFields(['adIndexBaseCollect']));
        $nDays      = 720;
        $remainDays = $nDays - 1;
        $collect    = $collect
            ->merge($this->hasNodesCols(Mather::findIn($nDays, $NNodes), 'ltv', 'LTV<n>', '', '', true, ['classify' => ['attrs', 'ltv_index']]))
            ->merge($this->hasNodesCols(Mather::findIn($nDays, $NNodes), 'roi', 'ROI<n>', '', '', true, ['classify' => ['attrs', 'roi_index']]))
            ->merge($this->hasNodesCols(Mather::findIn($remainDays, $remainNodes), 'retain', '<n>留', fn($i) => $i + 1, '次留', true, ['classify' => ['attrs', 'remain_index']]))
            ->merge($this->hasNodesCols(Mather::findIn($remainDays, $remainNodes), 'paid_retain_7', '付费<n>留', fn($i) => $i + 1, '付费次留', true, ['classify' => ['attrs', 'pay_remain_index']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'paid_user_with_new_node', '新增后<n>日付费人数', '', '新用户付费人数', true, ['classify' => ['attrs', 'our_side_ad']]))
            ->merge($this->hasNodesCols([3, 7], 'paid_cnt_with_new_node', '新增后<n>日付费次数', '', '', true, ['classify' => ['attrs', 'our_side_ad']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'paid_cnt_avg_with_user', '新增后<n>日人均付费次数', '', '新用户人均付费次数', true, ['classify' => ['attrs', 'our_side_ad']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'ltv_amount', 'LTV<n>付费金额', '', '新用户当日付费金额', true, ['classify' => ['attrs', 'our_side_ad']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'paid_cnt_cost', '新增后<n>日付费成本(次数)', '', '新用户付费成本(次数)', true, ['classify' => ['attrs', 'our_side_ad']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'back_paid_user_new_node', '回传_新增后<n>日付费人数', '', '回传_新用户付费人数', true, ['classify' => ['attrs', 'back_side_ad']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'back_paid_amount_new_node', '回传_新增后<n>日付费金额', '', '回传_新用户当日付费金额', true, ['classify' => ['attrs', 'back_side_ad']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'back_paid_new_roi', '回传_新增后<n>日付费ROI', '', '回传_新增当日付费ROI', true, ['classify' => ['attrs', 'back_side_ad']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'back_paid_cnt_new_node', '回传_新增后<n>日付费次数', '', '回传_新用户付费次数', true, ['classify' => ['attrs', 'back_side_ad']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'back_paid_cnt_cost_node', '回传_新增后<n>日付费成本(次数)', '', '回传_新用户付费成本(次数)', true, ['classify' => ['attrs', 'back_side_ad']]))
            ->merge($this->hasNodesCols([1, 3, 7], 'back_paid_cnt_avg_node', '回传_新增后<n>日人均付费次数', '', '回传_新用户人均付费次数', true, ['classify' => ['attrs', 'back_side_ad']]))
            ->merge([
                'back_paid_percent'                    => ['title' => '回传_新用户付费率', 'classify' => ['attrs', 'back_side_ad']],
                'back_paid_user_new_within_24_hours'   => ['title' => '回传_新增后24小时内付费人数', 'classify' => ['attrs', 'back_side_ad']],
                'back_paid_amount_new_within_24_hours' => ['title' => '回传_新增后24小时内付费金额', 'classify' => ['attrs', 'back_side_ad']],
                'back_paid_cnt_new_within_24_hours'    => ['title' => '回传_新增后24小时内付费次数', 'classify' => ['attrs', 'back_side_ad']],
                'back_paid_roi_within_24_hours'        => ['title' => '回传_新增后24小时内付费ROI', 'classify' => ['attrs', 'back_side_ad']],
                'back_paid_cnt_cost_node_user'         => ['title' => '回传_新用户付费成本(人数)', 'classify' => ['attrs', 'back_side_ad']],
            ])
            ->merge([
                'tt_active_pay_intra_day_count'      => ['title' => '头条_激活当日首次付费数', 'classify' => ['attrs', 'media_side_ad']],
                'tt_game_in_app_ltv_1day'            => ['title' => '头条_当日付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'tt_game_in_app_roi_1day'            => ['title' => '头条_当日付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'tt_game_in_app_ltv_4days'           => ['title' => '头条_激活后三日付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'tt_game_in_app_roi_4days'           => ['title' => '头条_激活后三日付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'tt_game_in_app_ltv_8days'           => ['title' => '头条_激活后七日付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'tt_game_in_app_roi_8days'           => ['title' => '头条_激活后七日付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'tt_game_pay_7d_count'               => ['title' => '头条_7日付费次数(激活时间)', 'classify' => ['attrs', 'media_side_ad']],
                'tt_active_pay_intra_one_day_count'  => ['title' => '头条_激活后24h首次付费数', 'classify' => ['attrs', 'media_side_ad']],
                'tt_active_pay_intra_one_day_amount' => ['title' => '头条_激活后24h付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'tt_active_pay_intra_one_day_roi'    => ['title' => '头条_激活后24h付费ROI', 'classify' => ['attrs', 'media_side_ad']],
            ])
            ->merge([
                'gdt_mini_game_register_users'       => ['title' => '腾讯_小游戏注册人数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_paying_users_d1'      => ['title' => '腾讯_注册首日付费人数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_minigame_1d_pay_count'          => ['title' => '腾讯_注册首日付费次数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_paying_amount_d1'     => ['title' => '腾讯_注册首日付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_first_day_paying_roi' => ['title' => '腾讯_注册首日付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_pay_d3_uv'            => ['title' => '腾讯_小游戏注册3日付费人数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_d3_pay_count'         => ['title' => '腾讯_小游戏注册3日付费次数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_paying_amount_d3'     => ['title' => '腾讯_小游戏注册3日付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_pay_d3_roi'           => ['title' => '腾讯_小游戏注册3日付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_pay_d7_uv'            => ['title' => '腾讯_小游戏注册7日付费人数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_d7_pay_count'         => ['title' => '腾讯_小游戏注册7日付费次数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_paying_amount_d7'     => ['title' => '腾讯_小游戏注册7日付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_mini_game_pay_d7_roi'           => ['title' => '腾讯_小游戏注册7日付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_minigame_24h_pay_uv'            => ['title' => '腾讯_小游戏注册首24小时付费人数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_minigame_24h_pay_amount'        => ['title' => '腾讯_小游戏注册首24小时付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_minigame_24h_pay_roi'           => ['title' => '腾讯_小游戏注册首24小时付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_first_day_first_pay_count'      => ['title' => '腾讯_激活首日付费人数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_first_day_pay_count'            => ['title' => '腾讯_激活首日付费次数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_first_day_pay_amount'           => ['title' => '腾讯_激活首日付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_roi_activated_d1'               => ['title' => '腾讯_激活首日付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_active_d3_pay_count'            => ['title' => '腾讯_激活3日付费次数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_payment_amount_activated_d3'    => ['title' => '腾讯_激活3日付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_roi_activated_d3'               => ['title' => '腾讯_激活3日付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_active_d7_pay_count'            => ['title' => '腾讯_激活7日付费次数', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_payment_amount_activated_d7'    => ['title' => '腾讯_激活7日付费金额', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_roi_activated_d7'               => ['title' => '腾讯_激活7日付费ROI', 'classify' => ['attrs', 'media_side_ad']],
                'gdt_reg_dedup_pv'                   => ['title' => '腾讯_注册人数', 'classify' => ['attrs', 'media_side_ad']],
            ]);

        return $this->formatStandard($collect);
    }
}
<?php

namespace app\service\ComprehensiveIndex;

use app\extension\Support\Contracts\RequireAccessToken;
use app\models\DdcPlatform\DwsSvkeyAdBaseDaily;
use app\models\DdcPlatform\DwsSvkeyAdLtvDaily;
use app\models\DdcPlatform\DwsSvkeyAdPaymentDaily;
use Plus\SQL\Medoo;

class AdIndexServ
{
    /**
     * @param array $wheres
     * @return array
     */
    public function listBaseIndex(array $wheres = []): array
    {
        $bindWheres = $this->buildWhere($wheres);

        $cols = [
            'tday',
            'new_user'        => Medoo::raw('SUM(`new_user`)'), // 广告新增用户
            'cost_discount'   => Medoo::raw('SUM(`cost_discount`)'), // 消耗金额
            'create_role_new' => Medoo::raw('SUM(`create_role_new`)'),
        ];

        $bindWheres['GROUP'] = ['tday'];
        $stm                 = DwsSvkeyAdBaseDaily::getInstance();
        $stm->asArray()->findAll($bindWheres, $cols);

        return array_column($stm->asArray()->findAll($bindWheres, $cols), null, 'tday');
    }

    /**
     * @param array $wheres
     * @return array
     */
    public function listPaymentIndex(array $wheres = []): array
    {
        $bindWheres = $this->buildWhere($wheres);

        $cols = [
            'tday',
            'pay_user_new'  => Medoo::raw('SUM(`pay_user_new`)'),
            'pay_money_new' => Medoo::raw('SUM(`pay_money_new`)'),
        ];

        $bindWheres['GROUP'] = ['tday'];
        $stm                 = DwsSvkeyAdPaymentDaily::getInstance();

        return array_column($stm->asArray()->findAll($bindWheres, $cols), null, 'tday');
    }

    /**
     * @param array $wheres
     * @return array
     */
    public function fetchLtv1(array $wheres = []): array
    {
        $bindWheres = $this->buildWhere($wheres, 0);

        $cols = [
            'tday',
            'money_all' => Medoo::raw('SUM(`money_all`)'),
            'money'     => Medoo::raw('SUM(`money`)'),
        ];

        $bindWheres['day_type'] = 1;
        $bindWheres['GROUP']    = ['tday'];
        $stm                    = DwsSvkeyAdLtvDaily::getInstance();

        return array_column($stm->asArray()->findAll($bindWheres, $cols), null, 'tday');
    }

    /**
     *
     * @param array $wheres
     * @param int   $isRemove
     * @return array
     */
    protected function buildWhere(array $wheres, int $isRemove = 0): array
    {
        $today = date('Y-m-d');

        [
            $rangeStart, $rangeEnd,
        ] = [
            $wheres['range_time_start'] ?? $today,
            $wheres['range_time_end'] ?? $today,
        ];

        $bindWheres             = [];
        $bindWheres['tday[<>]'] = [$rangeStart, $rangeEnd];

        if (!empty($wheres['cp_game_id'])) {
            $bindWheres['cp_game_id'] = $wheres['cp_game_id'];
        }

        if ($isRemove) {
            $bindWheres['is_remove'] = 0;
        }

        return $bindWheres;
    }
}
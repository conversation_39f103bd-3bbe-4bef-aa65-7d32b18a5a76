<?php

namespace app\service\OperationData;

use app\apps\operator\Helpers\ConstFirstLogin;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Scheme\BaseScheme;
use app\service\AdvertiserData\Traits\BasicOperator;
use app\service\AdvertiserData\Traits\Converter;
use app\service\OperationData\Components\MatchParams\FirstLoginMatcher;
use app\util\Common;

class BasicRemainIndex
{
    use Converter, BasicOperator;

    protected string $remainTable = '';

    protected string $remainTableAs = '';


    public function __construct($remainTable, $alias = '')
    {
        $this->remainTable = $remainTable;

        if (!empty($alias)) {
            $this->remainTableAs = $alias;
        }
        else {
            if (str_contains($remainTable, '.')) {
                $t           = \explode('.', $remainTable);
                $remainTable = Arr::pull($t, $remainTable);
            }

            $this->remainTableAs = $remainTable;
        }
    }


    /**
     * @throws \Exception
     */
    public function remainInfo(
        array $params = [], array $groups = [], bool $hasSummaryRow = true
    ): array
    {
        $powerSql           = \Plus::$service->admin->getAdminPowerSql();
        $rangeTimeDimension = (int)Arr::pull(
            $params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY
        );

        if (ConstFirstLogin::DIMENSION_WEEK === $rangeTimeDimension) {
            $scheme = $this->getWeekSubSqlBody($params);
        }
        elseif (ConstFirstLogin::DIMENSION_MONTH === $rangeTimeDimension) {
            $scheme = $this->getMonthSubSqlBody($params);
        }
        else {
            $scheme = $this->getDailySqlBody($params);
        }

        if (!in_array('day_type', $groups)) {
            $groups[] = 'day_type';
        }

        $dayCol = $this->remainTableAs . '.tday';
        $cols   = array_merge([$dayCol], $this->getCols($params));
        $scheme = $scheme
            ->scope($this->buildColumn($cols))
            ->joinPowerSql($powerSql, $this->remainTableAs);

        (new FirstLoginMatcher($this->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        $scheme->scope($this->buildGroup($groups));

        $backupScheme = clone $scheme;
        //打印sql调试
        Common::dumpSql((clone $scheme)->toSql());
        $result = [
            'list' => \Plus::$app->ddc_platform->query($scheme->toSql())->fetchAll(\PDO::FETCH_ASSOC),
        ];

        // 返回留存汇总行信息
        if ($hasSummaryRow) {
            $summaryScheme = (new BaseScheme())
                ->fromSub((clone $backupScheme)->toSql(), 'summary_body')
                ->scope($this->buildColumn(['day_type', 'SUM(login_num) as login_num']))
                ->scope($this->buildGroup(['day_type']));
            //打印sql调试
            Common::dumpSql((clone $summaryScheme)->toSql());
            $result['summary_row'] =
                \Plus::$app->ddc_platform->query($summaryScheme->toSql())->fetchAll(\PDO::FETCH_ASSOC);
        }

        return $result;

    }

    /**
     * @throws \Exception
     */
    protected function getWeekSubSqlBody(array &$params = []): SchemeContract
    {
        [
            'begin' => $rangeDateStart,
            'end'   => $rangeDateEnd,
            'cycle' => $rangeDateCycle,
        ] = TimeUtil::divideWeekByRangeDate(
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end')
        );

        $options = [
            'range_date_start' => $rangeDateStart,
            'range_date_end'   => $rangeDateEnd,
        ];


        $baseScheme = new BaseScheme();
        $baseScheme
            ->from($this->remainTable)
            ->scope($this->buildColumn([
                $this->getTimeColForWeek($rangeDateCycle, $this->remainTable),
                'cp_game_id', 'game_id', 'package_id',
                'day_type', 'login_num', 'login_date',
            ]));

        (new FirstLoginMatcher())
            ->setParams($options)
            ->execute($baseScheme);

        return (new BaseScheme())->fromSub($baseScheme, $this->remainTableAs);
    }

    /**
     * @param array $params
     *
     * @return BaseScheme
     */
    protected function getMonthSubSqlBody(array &$params = []): BaseScheme
    {
        $options = [
            'range_date_start' => Arr::pull($params, 'range_date_start'),
            'range_date_end'   => Arr::pull($params, 'range_date_end'),
        ];

        $baseScheme = new BaseScheme();
        $baseScheme
            ->from($this->remainTable)
            ->scope($this->buildColumn([
                "DATE_FORMAT(tday, '%Y-%m') as tday",
                'cp_game_id', 'game_id', 'package_id',
                'day_type', 'login_num', 'login_date',
            ]));

        (new FirstLoginMatcher())
            ->setParams($options)
            ->execute($baseScheme);

        return (new BaseScheme())->fromSub($baseScheme, $this->remainTableAs);
    }

    /**
     * @param array $params
     *
     * @return BaseScheme
     */
    protected function getDailySqlBody(array $params = []): BaseScheme
    {
        return (new BaseScheme())->from($this->remainTable, $this->remainTableAs);
    }


    /**
     * @param array $dateCycle
     * @param string $mainTable
     *
     * @return string
     */
    protected function getTimeColForWeek(array $dateCycle = [], string $mainTable = ''): string
    {
        $tDayCols = [];

        foreach ($dateCycle as $item) {
            ['begin_date' => $begin, 'end_date' => $end] = $item;

            $tDayCols[] = sprintf(
                "when {$mainTable}.tday between '%s' and '%s' then '%s'",
                $begin, $end, $begin . '/' . $end
            );
        }
        $caseString = implode(' ', $tDayCols);

        return sprintf(" CONCAT(case %s end) as tday ", $caseString);
    }


    /**
     * @param array $params
     * @param bool $isSummaryRow
     * @param bool $isTop
     *
     * @return array
     */
    protected function getCols(array $params = [], bool $isSummaryRow = false, bool $isTop = false): array
    {
        $mainTable = $this->remainTableAs;
        $yesterday     = date('Y-m-d', strtotime('-1day'));

        $fixedCols = [
            'cp_game_id'      => ['source' => $mainTable],
            'game_id'         => ['source' => $mainTable],
            'package_id'      => ['source' => $mainTable],
            'login_date'      => ['source' => $mainTable],
            'app_show_id'     => ['source' => 'POWER'],
            'channel_id'      => ['source' => 'POWER'],
            'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
            'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
            'platform_id'     => ['source' => 'POWER'],
            'channel_main_id' => ['source' => 'POWER'],
            'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v1_id'],
        ];

        $calcCols = [
            'day_type'  => ['source' => $mainTable],
            //            'login_num' => ['source' => $mainTable, 'aggregate' => 'sum'],
            'login_num' => ['raw' => "IFNULL(IF(day_type = 1000, IF(login_date = '{$yesterday}', sum(`login_num`), 0), sum(`login_num`)), 0) AS login_num"],
        ];

        return $this->generateColsArray($fixedCols, $calcCols, $isSummaryRow, $isTop);
    }


    /**
     * 拼接查询字段
     *
     * @param array $fixedCols
     * @param array $calcCols
     * @param bool $isSummaryRow
     * @param bool $isTop
     *
     * @return array
     */
    protected function generateColsArray(
        array $fixedCols = [], array $calcCols = [], bool $isSummaryRow = false, bool $isTop = false
    ): array
    {
        $result  = [];
        $collect = collect();

        if (!$isSummaryRow) {
            $collect = $collect->merge($fixedCols);
        }
        $collect = $collect->merge($calcCols);

        $collect->each(function (&$item, $key) use (&$result, $isSummaryRow, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];

                if ($this->isCalcValid($aggregate)) {
                    $format = "IFNULL({$aggregate}(%s), 0)";
                }
                else {
                    $format = "{$aggregate}(%s)";
                }
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }
            $field    .= $item['source_field'] ?? $key;
            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }

    /**
     * @param $aggregate
     *
     * @return bool
     */
    protected function isCalcValid($aggregate): bool
    {
        return in_array($aggregate, ['sum', 'avg']);
    }

    private function fieldReflect(): array
    {
        return [
            'tday'            => $this->remainTableAs,
            'cp_game_id'      => $this->remainTableAs,
            'game_id'         => $this->remainTableAs,
            'package_id'      => $this->remainTableAs,
            'app_show_id'     => 'POWER',
            'channel_main_id' => 'POWER',
            'channel_id'      => 'POWER',
            'platform_id'     => 'POWER',
            'promotion_id'    => 'POWER.popularize_v2_id',
        ];
    }

    /**
     * @param array $params
     * @param array $groups
     * @param bool $hasSummaryRow
     *
     * @return array
     */
    public function simpleListByDay(array $params = [], array $groups = [], bool $hasSummaryRow = true): array
    {
        $wheres      = [];
        $whereString = '';

        [$dateStart, $dateEnd] = [
            $params['range_date_start'], $params['range_date_end'],
        ];

        $wheres[] = "tday between '{$dateStart}' and '{$dateEnd}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = $params['cp_game_id'];
            if (str_contains($cpGameId, ',')) {
                $wheres[] = "cp_game_id IN ({$cpGameId})";
            }
            else {
                $wheres[] = "cp_game_id = {$cpGameId}";
            }
        }

        if (($params['max_day_type'] ?? 0) > 0) {
            $maxDayType = $params['max_day_type'];
            $wheres[]   = "(day_type <= {$maxDayType} or day_type = 1000)";
        }

        if (!empty($wheres)) {
            $whereString = ' WHERE ' . implode(' and ', $wheres);
        }

        $sql = "
        SELECT 
         tday, day_type, cp_game_id, sum(login_num) as login_num
        FROM {$this->remainTable} {$whereString} group by tday, day_type
        ";

        $summarySql = "
        SELECT 
             day_type, cp_game_id, sum(login_num) as login_num
        FROM {$this->remainTable} {$whereString} group by day_type
        ";

        $list       = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $summaryRow = \Plus::$app->ddc_platform->query($summarySql)->fetchAll(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => $summaryRow,
        ];
    }

}
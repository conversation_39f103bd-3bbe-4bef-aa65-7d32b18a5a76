<?php

namespace app\service\OperationData;

use app\apps\internal\Helpers\ConstHub;
use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\extension\Support\Helpers\Zakia;
use app\service\General\Helpers\DdcPlatformTable;
use app\service\OperationData\Components\MatchParams\PackageMatch;
use app\util\Common;
use PhpParser\Node\Expr\AssignOp\Plus;
use phpseclib3\File\ASN1\Maps\Time;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Query\SelectQuery;

class FirstLoginPayRemainServ
{
    const QB_MODE_ALL = 1;
    const QB_MODE_POWER = 1;

    /**
     * @param SelectQuery $infoQb
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param array $cols
     * @param int $mode
     * @return array
     * @throws \Exception
     */
    public function getListWithInfoQb(
        SelectQuery $infoQb,
        array       $params = [],
        array       $groups = [],
        array       $paginate = [],
        array       $sort = [],
        array       $cols = [],
        int         $mode = -1
    ): array
    {
        $result = [];
        /**
         * @notice 这里会更改查询条件中的选择范围(周维度)
         */
        $qb = $this->queryBuilderJoinInfoQb($infoQb, $params, $groups);
        $qb->where('link_mark', '=', 1);
        $matcher      = new PackageMatch([
            'tday'            => 't_pay_remain.tday',
            'package_id'      => 'slave_info.package_id',
            'cp_game_id'      => 'slave_info.cp_game_id',
            'game_id'         => 'slave_info.game_id',
            'platform_id'     => 'slave_info.platform_id',
            'app_show_id'     => 'slave_info.app_show_id',
            'channel_main_id' => 'slave_info.channel_main_id',
            'channel_id'      => 'slave_info.channel_id',
            'promotion_id'    => 'slave_info.promotion_id',
            'department_id'   => 'slave_info.department_id',
            'user_id'         => 'slave_info.user_id',
        ]);
        $filterParams = array_intersect_key(array_diff_key($params, ['tday' => '']), array_flip($groups));
        $matcher($qb, $filterParams);
        $topRemainCols   = $this->remainCols($params, 2);
        $topRemainCols[] = 'remain_1000';
        $topCols         = array_merge([new Fragment('slave_info.*')], $topRemainCols);
        $qb->columns($topCols);
        @Common::dumpSql($qb->__toString());
        $result['list'] = $qb->fetchAll();

        $summaryQb = $this->baseQueryBuilder($params, $groups);
        $matcher   = new PackageMatch([
            'tday'            => 't_remain.tday',
            'package_id'      => 'power.package_id',
            'cp_game_id'      => 'power.cp_game_id',
            'game_id'         => 'power.game_id',
            'platform_id'     => 'power.platform_id',
            'app_show_id'     => 'power.app_show_id',
            'channel_main_id' => 'power.channel_main_id',
            'channel_id'      => 'power.channel_id',
            'promotion_id'    => 'power.popularize_v2_id',
            'department_id'   => 'power.ad_department_id',
            'user_id'         => 'power.ad_user_id',
        ]);

        $matcher($summaryQb, $params);
        $summaryCols   = $this->remainCols($params);
        $summaryCols[] = new Fragment("SUM(IF(day_type = DATEDIFF(NOW(), t_remain.tday) - 1, LOGIN_NUM, 0)) as remain_1000");
        $summaryCols[] = new Fragment("MAX(t_remain.update_time) as last_update_time");
        $summaryQb->columns($summaryCols);
        @Common::dumpSql($summaryQb->__toString());
        $result['summary'] = $summaryQb->fetchAll();

        return $result;
    }

    /**
     * @param SelectQuery $infoQb
     * @param array $params
     * @param array $groups
     * @return SelectQuery
     * @throws \Exception
     */
    protected function queryBuilderJoinInfoQb(
        SelectQuery $infoQb, array &$params = [], array $groups = []
    ): SelectQuery
    {
        $db           = $this->getConn();
        $remainGroups = $groups;
        $mainSubQuery = $this->baseQueryBuilder();
        $matcher      = new PackageMatch([
            'tday'            => 't_remain.tday',
            'package_id'      => 'power.package_id',
            'cp_game_id'      => 'power.cp_game_id',
            'game_id'         => 'power.game_id',
            'platform_id'     => 'power.platform_id',
            'app_show_id'     => 'power.app_show_id',
            'channel_main_id' => 'power.channel_main_id',
            'channel_id'      => 'power.channel_id',
            'promotion_id'    => 'power.popularize_v2_id',
            'department_id'   => 'power.ad_department_id',
            'user_id'         => 'power.ad_user_id',
        ]);

        $daysCols     = $this->getDayWithDimension($params, 't_remain');
        $infoCols     = $this->remainInfoCols($params);
        $remainCols   = $this->remainCols($params);
        $remainCols[] = new Fragment("SUM(IF(day_type = DATEDIFF(NOW(), t_remain.tday) - 1, login_num, 0)) as remain_1000");
        $cols         = array_merge($infoCols, $remainCols);

        if (in_array('tday', $groups)) {
            $cols = array_merge($cols, $daysCols);
        }

        $mainSubQuery->columns($cols);
        $matcher($mainSubQuery, $params);

        $groupMap = [
            'package_id'      => 't_remain.package_id',
            'channel_id'      => 'power.channel_id',
            'channel_main_id' => 'power.channel_main_id',
            'user_id'         => 'power.ad_user_id',
            'department_id'   => 'power.ad_department_id',
            'tday'            => 'group_day',
        ];

        $joinOn = [];
        foreach ($remainGroups as $g) {
            if (isset($groupMap[$g])) {
                $mainSubQuery->groupBy($groupMap[$g]);
            }
            else {
                $mainSubQuery->groupBy($g);
            }

            $m          = 't_pay_remain.' . $g;
            $s          = 'slave_info.' . $g;
            $joinOn[$m] = $s;
        }

        $newDb = $this->getConn();
        $newQb = $newDb->select()->from(new Fragment('(' . $mainSubQuery->__toString() . ') as t_pay_remain'));
        $newQb
            ->leftJoin(new Fragment('(' . $infoQb->__toString() . ')'), 'slave_info')
            ->on($joinOn);

        return $newQb;
    }

    /**
     * @param array $params
     * @param array $eqCols
     * @return array
     */
    protected function remainInfoCols(array $params, array $eqCols = []): array
    {
        $infoColsMap = [
            'cp_game_id'      => 'power.cp_game_id as cp_game_id',
            'game_id'         => 'power.game_id as game_id',
            'app_show_id'     => 'power.app_show_id as app_show_id',
            'channel_id'      => 'power.channel_id as channel_id',
            'channel_main_id' => 'power.channel_main_id as channel_main_id',
            'platform_id'     => 'power.platform_id as platform_id',
            'package_id'      => 'power.package_id as package_id',
            'promotion_id'    => 'power.popularize_v2_id as promotion_id',
            'department_id'   => 'power.ad_department_id as department_id',
            'user_id'         => 'power.ad_user_id as user_id',
        ];

        if (!empty($eqCols)) {
            $infoColsMap = array_intersect_key($infoColsMap, array_flip($eqCols));
        }

        return array_values($infoColsMap);
    }

    /**
     * @param array $params
     * @param int $mode
     * @return array
     * @throws \Exception
     */
    protected function remainCols(array $params = [], int $mode = 1): array
    {
        $cols      = [];
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $minDay    = $yesterday;

        if (isset($params['tday'])) {
            $tDay   = Arr::wrap($params['tday']);
            $minDay = min($tDay);
        }

        $maxDayNode = days_apart($yesterday, $minDay);
        if ($mode == 1) {
            $colFn = fn($i) => new Fragment("SUM(IF(day_type = {$i}, login_num, 0)) as remain_{$i}");
        }
        else {
            $colFn = fn($i) => "remain_{$i}";
        }

        for ($i = 1; $i <= $maxDayNode; $i++) {
            $cols[] = $colFn($i);
        }

        return $cols;
    }


    /**
     * @param array $params
     * @param array $groups
     * @param int $qbMode
     * @return SelectQuery
     */
    protected function baseQueryBuilder(
        array $params = [], array $groups = [], int $qbMode = -1
    ): SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from(DdcPlatformTable::DwsFirstloginPayRemainDaily . ' as t_remain');

        if ($qbMode & self::QB_MODE_POWER) {
            $qb
                ->innerJoin(new Fragment(str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql())), 'power')
                ->on([
                    't_remain.package_id' => 'power.package_id',
                ]);
        }

        return $qb;
    }

    /**
     * @param array $params
     * @param string $table
     * @return array|Fragment[]|string[]
     * @throws \Exception
     */
    protected function getDayWithDimension(array &$params = [], string $table = 't_base'): array
    {
        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        if ($timeDimension == ConstHub::DIMENSION_WEEK) {
            $tDays = $params['tday'];
            sort($tDays);

            [
                'begin' => $rangeDateStart,
                'end'   => $rangeDateEnd,
                'cycle' => $rangeDateCycle,
            ] = TimeUtil::divideWeekByRangeDate(
                ...$tDays
            );

            $params['tday'] = [
                $rangeDateStart, $rangeDateEnd
            ];

            $dayCols = [];
            foreach ($rangeDateCycle as $item) {
                ['begin_date' => $begin, 'end_date' => $end] = $item;
                $dayCols[] = sprintf(
                    "when %s.tday between '%s' and '%s' then '%s'",
                    $table, $begin, $end, $begin . '/' . $end
                );
            }
            $caseString    = implode(' ', $dayCols);
            $tDayString    = sprintf(" CONCAT(case %s end) as tday ", $caseString);
            $dayGroupAlias = sprintf(" CONCAT(case %s end) as group_day ", $caseString);

            return [new Fragment($tDayString), new Fragment($dayGroupAlias)];
        }
        elseif ($timeDimension == ConstHub::DIMENSION_MONTH) {
            return [
                new Fragment(" DATE_FORMAT({$table}.tday, '%Y-%m') as tday"),
                new Fragment(" DATE_FORMAT({$table}.tday, '%Y-%m') as group_day"),
            ];
        }
        else {
            return [
                "{$table}.tday as tday",
                "{$table}.tday as group_day",
            ];
        }
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * 获取doris连接
     *
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     * @throws \Exception
     */
    private function getDorisConn()
    {
        static $connIndex;

        if ($connIndex === null) {
            $connIndex = Common::pingDorisIndex();
        }

        return FakeDB::connection($connIndex);
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $mode
     * @return array
     * @throws \Exception
     */
    public function payRetainInfo(
        array $params = [],
        array $groups = [],
        array $paginate = [],
        array $sort = [],
        int   $mode = -1
    ): array
    {
        $sortMap = [
            'tday' => 'group_day'
        ];

        $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;

        sort($params['tday']);
        [$timeBegin, $timeEnd] = $params['tday'];

        // 补全时间
        if ($timeDimension == ConstHub::DIMENSION_WEEK) {
            ['begin' => $begin, 'end' => $end] = TimeUtil::divideWeekByRangeDate($timeBegin, $timeEnd);
            $params['tday'] = [$begin, $end];
        }
        elseif ($timeDimension == ConstHub::DIMENSION_MONTH) {
            $params['tday'] = [
                date('Y-m-01', strtotime($timeBegin)),
                date('Y-m-t', strtotime($timeEnd))
            ];
        }

        // 根据搜索条件查询最细维度数据
        $retainSql = $this->retainDws($params, $groups);
        $mainSql   = $this->retainMainLogicQuery($params, $groups, $paginate, $sort);
        $wrapSql   = "with retain_detail as ({$retainSql}) {$mainSql}";

        if (!empty($sort)) {
            $orderBy = [];
            foreach ($sort as $key => $value) {
                $sortField = $key;
                if (isset($sortMap[$key])) {
                    $sortField = $sortMap[$key];
                }

                $orderBy[] = "{$sortField} {$value}";
            }

            $orderString = ' order by ' . implode(', ', $orderBy);
            $wrapSql     .= $orderString;

        }

        $doris = $this->getDorisConn();
        $list  = $doris->query($wrapSql)->fetchAll();

        return [
            'list' => $list,
        ];
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return string
     * @throws \Exception
     */
    private function retainMainLogicQuery(
        array $params = [], array $groups = [], array $paginate = [], array $sort = []
    ): string
    {
        $sourceGroups  = $groups;
        $groupRelation = [
            'tday'            => 'add_date',
            'cp_game_id'      => 't_result.cp_game_id',
            'game_id'         => 't_result.game_id',
            'app_show_id'     => 't_result.app_show_id',
            'channel_main_id' => 't_result.channel_main_id',
            'channel_id'      => 't_result.channel_id',
            'promotion_id'    => 't_result.popularize_v2_id',
            'package_id'      => 't_result.package_id',
            'department_id'   => 't_result.ad_department_id',
            'user_id'         => 't_result.ad_user_id',
            'platform_id'     => 't_result.platform_id',
        ];

        $groups        = array_map(fn($item) => $groupRelation[$item], $groups);
        $wrapGroup     = $groups;
        $connDimension = array_unique(array_merge($groups, ['money_type', 'add_date', 'active_date']));
        $joinConn      = implode(',', array_map(fn($item) => str_replace('t_result.', '', $item), $connDimension));
        $dimensionSql  = $this->buildDimensionSql($params, $sourceGroups, $sort);
        $cols          = $groups;
        $cols[]        = 't_result.money_type';

        if (in_array('add_date', $groups)) {
            // 如果汇总行包含了时间维度就分析时间维度的
            $timeDimension = $params['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;
            $wrapGroup[]   = 'group_day';
            unset($wrapGroup[array_search('add_date', $cols)]);

            if ($timeDimension == ConstHub::DIMENSION_WEEK) {
                [$rangeStart, $rangeEnd] = $params['tday'];
                ['cycle' => $cycle] = TimeUtil::divideWeekByRangeDate($rangeStart, $rangeEnd);

                $dayCols = [];
                foreach ($cycle as $c) {
                    ['begin_date' => $b, 'end_date' => $e] = $c;

                    $dayCols[] = sprintf(
                        "when t_result.add_date between '%s' and '%s' then '%s' ", $b, $e, $b . '/' . $e
                    );
                }
                $caseString = implode(' ', $dayCols);
                $dayString  = sprintf(" CONCAT(case %s end) as group_day ", $caseString);

            }
            elseif ($timeDimension == ConstHub::DIMENSION_MONTH) {
                $dayString = "date_format(t_result.add_date , '%Y-%m') as group_day ";
            }
            else {
                $dayString = "t_result.add_date as group_day ";
            }

            $cols[] = $dayString;

            unset($cols[array_search('add_date', $cols)]);
        }

        $cols[]          = "coalesce(sum(retain_count), 0) as retain_count";
        $cols[]          = "coalesce(sum(t_result.add_count), 0) as add_count";
        $cols[]          = "datediff(t_result.active_date, t_result.add_date) as day_type";
        $cols            = array_unique($cols);
        $wrapGroup[]     = 'day_type';
        $wrapGroup[]     = 'money_type';
        $wrapGroupString = implode(',', array_map(fn($item) => str_replace('t_result.', '', $item), $wrapGroup));
        $colString       = implode(', ', $cols);

        return "select {$colString}  from ({$dimensionSql}) t_result left join retain_detail using ({$joinConn}) where  datediff(t_result.active_date, t_result.add_date) >= 0 group by {$wrapGroupString}";
    }


    /**
     * 根据临时表建立维度
     *
     * @param array $params
     * @param array $groups
     * @param array $sort
     * @return string
     */
    protected function buildDimensionSql(
        array $params = [], array $groups = [], array $sort = []
    ): string
    {
        $groupRelation = [
            'tday'            => 'retain_detail.add_date',
            'cp_game_id'      => 'retain_detail.cp_game_id',
            'game_id'         => 'retain_detail.game_id',
            'app_show_id'     => 'retain_detail.app_show_id',
            'channel_main_id' => 'retain_detail.channel_main_id',
            'channel_id'      => 'retain_detail.channel_id',
            'promotion_id'    => 'retain_detail.popularize_v2_id',
            'package_id'      => 'retain_detail.package_id',
            'department_id'   => 'retain_detail.ad_department_id',
            'user_id'         => 'retain_detail.ad_user_id',
            'platform_id'     => 'retain_detail.platform_id',
        ];

        $groups        = array_map(fn($item) => $groupRelation[$item] ?? $item, $groups);
        $cols          = array_unique(array_merge($groups, ['money_type', 'retain_detail.add_date', 't_date.active_date', 'retain_detail.add_count']));
        $minDay        = $params['tday'][0] ?? date('Y-m-d');
        $retainLastDay = $params['retain_days'][1] ?? $minDay;

        // 补全时间节点
        $pathTimeNode = "select to_date(DATE_ADD('{$minDay}', date_rank)) as active_date
             from (select array_range(0, datediff('{$retainLastDay}', '{$minDay}') + 1) as date_list) as t lateral view explode(date_list) tmp1 as date_rank";

        $colString = implode(',', $cols);

        return "select distinct {$colString} from retain_detail left join ({$pathTimeNode}) t_date on true";
    }


    /**
     * 付费留存明细: 首登后开始计算有付费的用户
     *
     * @notice 这里不作时间维度处理
     * @param array $params
     * @param array $groups
     * @return string
     */
    protected function retainDws(array $params = [], array $groups = []): string
    {
        $groupRelation = [
            'tday'            => 'retain_detail.add_date',
            'cp_game_id'      => 'retain_detail.cp_game_id',
            'game_id'         => 'retain_detail.game_id',
            'app_show_id'     => 'power.app_show_id',
            'channel_main_id' => 'power.channel_main_id',
            'channel_id'      => 'power.channel_id',
            'promotion_id'    => 'power.popularize_v2_id',
            'package_id'      => 'retain_detail.package_id',
            'department_id'   => 'power.ad_department_id',
            'user_id'         => 'power.ad_user_id',
            'platform_id'     => 'power.platform_id',
        ];

        $groups = array_map(fn($item) => $groupRelation[$item], $groups);

        // 必要维度
        $partition = array_unique(array_merge($groups, [
            'retain_detail.add_date',
            't_pay.money_type'
        ]));

        $groups = array_unique(array_merge($groups, [
            'retain_detail.add_date',
            'retain_detail.active_date',
            't_pay.money_type'
        ]));

        $joinPaymentSql = $this->hitPaymentInfo($params);
        $retainTable    = 'bigdata_dws.dws_firstlogin_retain_detail';
//        $powerSql       = str_replace('POWER', 'power', \Plus::$service->admin->getAdminPowerSql());
        $powerSql  = 'base_conf_platform.tb_package_detail_conf as power';
        $cols      = array_merge($groups, ['count(*) as retain_count']);
        $cols[]    = 'first_value(count(*)) over (partition by ' . implode(',', $partition) . ' order by retain_detail.active_date) as add_count';
        $cols[]    = 'sum(pay_money) as pay_money';
        $retainCol = implode(',', $cols);

        $retainSql = "
        select {$retainCol} from {$retainTable} retain_detail
            left join {$powerSql} on retain_detail.package_id = power.package_id
            join ({$joinPaymentSql}) t_pay on retain_detail.cp_game_id = t_pay.cp_game_id and retain_detail.core_account = t_pay.core_account";

        $wheres = ["t_pay.money_type <> ''",];

        // 拼接搜索条件
        $searchFnMap = $this->searchFnMap();
        $params      = array_intersect_key($params, $searchFnMap);
        foreach ($params as $k => $foo) {
            $fn = $searchFnMap[$k] ?? null;
            if (is_null($fn)) continue;
            $wheres[] = $fn($foo);
        }
        $whereString = ' where ' . implode(' and ', $wheres);
        $retainSql   .= $whereString;
        $retainSql   .= " group by " . implode(',', $groups);

        return $retainSql;
    }

    /**
     * 根据支付时间等支付筛选条件命中目标用户再根据cp去重关联
     *
     * @param array $params
     * @return string
     */
    protected function hitPaymentInfo(array $params = []): string
    {
        $paymentTable    = 'ddc_platform.dwd_sdk_user_payment';
        $payFirstTime    = $params['pay_first_time'] ?? date('Y-m-d 00:00:00');
        $payLastTime     = $params['pay_last_time'] ?? date('Y-m-d 23:59:59');
        $paidAmountRange = "case when sum(money) > 0 then '0-∞' else '' end as money_type";

        if (!empty($params['paid_amount_range'])) {
            // 拼接查询金额区间
            $paidRange = [];
            $lastRange = 0;
            foreach ($params['paid_amount_range'] as $n) {
                $paidRange[] = "when sum(money) > {$lastRange} and sum(money) <= {$n} then '{$lastRange}-{$n}'";
                $lastRange   = $n;
            }
            $paidRange[]     = "when sum(money) > {$lastRange} then '>{$lastRange}'";
            $paidRange[]     = "else ''";
            $paidAmountRange = "case " . implode(" ", $paidRange) . " end as money_type";
        }

        $wheres = [
            "pay_time between '{$payFirstTime}' and '{$payLastTime}'",
            'pay_result = 1',
        ];

        if (isset($params['paid_amount_days']) && $params['paid_amount_days'] > 0) {
            $wheres[] = 'datediff(t_payment.pay_time, t_first.time) <= ' . ($params['paid_amount_days'] - 1);
        }

        $whereString = implode(' and ' , $wheres);

        return <<<EOT
    select 
        t_payment.cp_game_id, 
        t_payment.core_account, 
        sum(money) as pay_money, 
        {$paidAmountRange}
    from 
        {$paymentTable} t_payment left join ddc_platform.dwd_sdk_user_firstlogin t_first  
            on t_first.cp_game_id = t_payment.cp_game_id and t_first.core_account = t_payment.core_account 
    where {$whereString}
    group by cp_game_id, core_account
EOT;
    }

    /**
     * @return mixed
     */
    protected function searchFnMap(): array
    {
        return [
            'tday'            => function ($value) {
                $value = Arr::wrap($value);
                sort($value);
                [$begin, $stop] = $value;

                return "retain_detail.add_date between '{$begin}' and '{$stop}'";
            },
            'retain_days'     => function ($value) {
                $value = Arr::wrap($value);
                sort($value);
                [$begin, $stop] = $value;

                return "retain_detail.active_date between '{$begin}' and '{$stop}'";
            },
            'cp_game_id'      => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "retain_detail.cp_game_id in ({$value})";
            },
            'game_id'         => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "retain_detail.game_id in ({$value})";
            },
            'app_show_id'     => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "power.app_show_id in ({$value})";
            },
            'channel_main_id' => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "power.channel_main_id in ({$value})";
            },
            'channel_id'      => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "power.channel_id in ({$value})";
            },
            'platform_id'     => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "power.platform_id in ({$value})";
            },
            'package_id'      => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "retain_detail.package_id in ({$value})";
            },
            'promotion_id'    => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "power.popularize_v2_id in ({$value})";
            },
            'department_id'   => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "power.ad_department in ({$value})";
            },
            'user_id'         => function ($value) {
                $value = implode(',', Arr::wrap($value));
                return "power.ad_user_id in ({$value})";
            }
        ];
    }

}
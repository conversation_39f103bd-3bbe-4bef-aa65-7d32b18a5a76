<?php

namespace app\models\DdcPlatform;

use Aura\SqlQuery\QueryFactory;
use Plus\MVC\Model\ActiveRecord;
use Plus\SQL\Medoo;


/**
 * @DwsSvkeyAdBaseDaily
 *                     短链维度-广告新增数据指标
 * @property int    ID
 * @property int    CP_GAME_ID
 * @property int    GAME_ID
 * @property int    PACKAGE_ID
 * @property int    CHANNEL_ID
 * @property int    SV_KEY
 * @property string ACCOUNT_ID
 * @property int    SHOW
 * @property int    CLICK
 * @property int    DOWNLOAD
 * @property int    ACTIVATE
 * @property int    CONVERT
 * @property int    INSTALL
 * @property int    LP_VIEW
 * @property int    LP_DOWNLOAD
 * @property int    DOWNLOAD_START
 * @property int    REGISTER
 * @property int    COST
 * @property int    COST_DISCOUNT
 * @property int    NEW_REAL_USER
 * @property int    NEW_USER
 * @property int    NEW_USER_EMULATOR
 * @property int    ACTIVATE_DEVICE
 * @property int    ACTIVE_USER
 * @property int    ACTIVE_USER_PAY
 * @property int    ACTIVE_USER_NEW_7_DAYS_AGO
 * @property int    CREATE_ROLE_NEW
 * @property int    PAY_NEW_USER_7DAYS
 * @property int    PAY_FREQUENCY_7DAYS
 * @property int    ONLINE_TIME
 * @property int    FIRST_ONLINE_TIME
 * @property string UPDATE_TIME
 * @property int    IS_REMOVE
 */
class DwsSvkeyAdBaseDaily extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->ddc_platform;
        parent::__construct($data);
    }

    /**
     * 获取广告指标数据
     * @param $params
     * @return array|bool|mixed|\Plus\SQL\Db
     */
    public function list($params)
    {
        $fields = [];
        $mainTable    = $this->getTableName();
        $queryFactory = new QueryFactory('mysql');
        $select       = $queryFactory->newSelect();
        $select->from($this->getTableName());     // alias the table as desired

        //汇总
        $group    = explode(",", $params["groups"]);
        $groupArr = [];
        foreach ($group as $key) {
            $groupArr[] = $this->getKeyMap($key);
            //查询字段 - 需要显示字段
            if (!in_array($key, $fields)) {
                $fields[] = $this->getSearchField($key);
            }
        }

        //条件筛选
        $fiterParamArr = ["cp_game_id", "game_id", "package_id", "promotion_channel_id","promotion_id","channel_id", "sv_key","user_id","department_id"];
        foreach ($fiterParamArr as $key) {
            if (isset($params[$key])) {
                $select->where($this->getKeyMap($key) . " = {$params[$key]}");
                //查询字段 - 需要显示字段
                if (!in_array($key, $fields)) {
                    $fields[] = $this->getSearchField($key);
                }
            }
        }
        $select->where(
            $this->getTableName() . ".tday BETWEEN '{$params["range_date_start"]}' AND '{$params["range_date_end"]}'"
        );

        //查询字段 - 需要统计的指标 例如新增用户  付费金额等
        $fieldsArr = explode(",", $params["fields"]);
        foreach ($fieldsArr as $key => $item) {
            $fields[] = "SUM($item) AS $item";
        }

        if ($groupArr) {
            $select->groupBy($groupArr);
        }

        $limit  = $params["page"] * $params["page_size"];
        $offset = ($params["page"] - 1) * $params["page_size"];
        $select->limit($limit)->offset($offset);
        if (isset($params["order"]) && $params["order"]) {
            $select->orderBy([$params["order"]]);
        }

        $select->join(
            'LEFT',
            'dws_svkey_ad_payment_daily AS svkey_ad_payment',
            "$mainTable.TDAY=svkey_ad_payment.TDAY AND  $mainTable.PACKAGE_ID =svkey_ad_payment.PACKAGE_ID AND $mainTable.SV_KEY =svkey_ad_payment.SV_KEY "
        )->join(
            'LEFT',
            'base_conf_platform.tb_package_detail_conf AS package_detail',
            "$mainTable.PACKAGE_ID =package_detail.PACKAGE_ID"
        )->join(
            'LEFT',
            'dataspy.tb_ad_svlink_conf AS svlink',
            "$mainTable.SV_KEY =svlink.ID"
        )->join(
            'LEFT',
            'dataspy.admin_user AS admin',
            "svlink.USER_ID=admin.ID"
        );
        $select->cols($fields);
        $sql = $select->__toString();
        return $this->_db->query($sql)->fetchAll(2);
    }

    /**
     * 获取映射关系 - 用于查询显示转换
     * @param $key
     * @return string
     */
    public function getSearchField($key)
    {
        return $this->getKeyMap($key) . " AS $key";
    }

    /**
     * 获取查询字段
     * @param $key
     * @return string
     */
    public function getKeyMap($key)
    {
        $spceMap = [
            "tday"                 => $this->getTableName() . ".tday",
            "cp_game_id"           => $this->getTableName() . ".cp_game_id",
            "game_id"              => $this->getTableName() . ".game_id",
            "package_id"           => $this->getTableName() . ".package_id",
            "channel_id"           => "package_detail.channel_id",
            "promotion_id"         => "package_detail.popularize_v2_id",
            "sv_key"               => $this->getTableName() . ".sv_key",
            "promotion_channel_id" => $this->getTableName() . ".channel_id",
            'user_id'              => 'IF ( svlink.USER_ID = 0 OR svlink.USER_ID IS NULL, package_detail.AD_USER_ID, svlink.USER_ID )',
            'department_id'        => 'IF( admin.DEPARTMENT_ID = 0 OR admin.DEPARTMENT_ID IS NULL, package_detail.AD_DEPARTMENT_ID, admin.DEPARTMENT_ID )'
        ];
        return isset($spceMap[$key]) ? $spceMap[$key] : $key;
    }
}
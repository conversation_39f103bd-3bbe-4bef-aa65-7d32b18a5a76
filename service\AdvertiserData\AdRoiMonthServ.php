<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\AdvertiserData\Components\Matcher\AdMonthMatch;
use app\service\General\BizTagsServ;
use app\util\Common;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

/**
 * @AdRoiMonthServ 广告月度看板查询
 */
class AdRoiMonthServ
{
    const MODE_ALL         = 3;
    const MODE_SUMMARY     = 2;
    const MODE_LIST        = 1;
    const QB_MODE_BOTH     = 10;
    const QB_MODE_SIMPLE   = 1;
    const ROW_MODE_SUMMARY = 3;
    const ROW_MODE_INFO    = 1;

    /**
     * 基础查询
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int   $mode MODE_ALL(默认), MODE_ALL ^ MODE_SUMMARY , MODE_LIST ...
     *
     * @return array
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $result  = [];
        $allMode = static::MODE_ALL | static::MODE_SUMMARY | static::MODE_LIST;
        $qb      = $this->getSimpleQueryBuilder(static::QB_MODE_BOTH);

        $matcher = new AdMonthMatch($this->reflectFieldMap($mode));
        $matcher->exec($qb, $params);

        $qb
            ->where('t_base.game_id', '!=', 0)
            ->columns($this->getColumns(static::ROW_MODE_INFO));

        if (($mode & $allMode) & static::MODE_LIST) {
            $infoQb = clone $qb;

            if (!empty($groups)) {
                $this->changeGroups($infoQb, $groups);
            }

            if (!empty($sort)) {
                foreach ($sort as $k => $foo) {
                    if ($k == 'channel_id') {
                        $sort['t_channel'] = $foo;
                        unset($sort[$k]);
                    }
                    elseif ($k == 'channel_main_id') {
                        $sort['channel_main'] = $foo;
                        unset($sort[$k]);
                    }
                }

                $infoQb->orderBy($sort);
            }

            $notPageInfoQb = clone $infoQb;

            // 加入分页
            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;
                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }
            Common::dumpSql($infoQb->__toString());
            $result['list'] = $infoQb->fetchAll();

            // 获取总页数
            $chillDb         = $this->getConn();
            $result['total'] = $chillDb
                ->select()
                ->from(new Fragment('(' . $notPageInfoQb->__toString() . ') as totalBody'))
                ->count();
        }

        if (($mode & $allMode) & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            $summaryQb->columns($this->getColumns(static::ROW_MODE_SUMMARY));
            Common::dumpSql($infoQb->__toString());
            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * @param int $queryBuilderMode
     *
     * @return \Spiral\Database\Query\SelectQuery
     */
    private function getSimpleQueryBuilder(int $queryBuilderMode = -1): \Spiral\Database\Query\SelectQuery
    {
        $allMode = static::QB_MODE_BOTH | static::QB_MODE_SIMPLE;

        $powerSql = str_replace('POWER', '', \Plus::$service->admin->getAdminPowerSql());
        $db       = $this->getConn();
        $qb       = $db->select()->from(TableCollect::DDC_AD_ROI_MONTH . ' as t_base');

        $qb
            ->innerJoin(new Fragment($powerSql), 'power')
            ->on('t_base.package_id', 'power.package_id');

        if (($allMode & $queryBuilderMode) & static::QB_MODE_BOTH) {
            // 关联渠道信息
            $qb
                ->leftJoin(TableCollect::BASE_CHANNEL_CONF, 'base_channel')
                ->on('t_base.channel_id', 'base_channel.channel_id');
        }

        return $qb;
    }


    /**
     * @param SelectQuery |QueryInterface $qb
     * @param array                       $groups
     *
     * @return void
     */
    private function changeGroups(&$qb, array $groups)
    {
        $groupsMap = [
            'channel_id'      => 't_channel',
            'channel_main_id' => 'channel_main',
            'package_id'      => 't_base.package_id',
            'cp_game_id'      => 't_base.cp_game_id',
            'game_id'         => 't_base.game_id',
        ];

        foreach ($groups as $g) {
            if (isset($groupsMap[$g])) {
                $g = $groupsMap[$g];
            }

            $qb->groupBy($g);
        }
    }

    /**
     * @param int $mode
     *
     * @return Fragment[]
     */
    private function getColumns(int $mode = 1): array
    {
        $planChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $infoFields = [
            't_month'       => new Fragment("DATE_FORMAT(STR_TO_DATE(t_base.month, '%Y%m'), '%Y-%m') as t_month"),
            'package_id'    => 't_base.package_id',
            'cp_game_id'    => 't_base.cp_game_id',
            'game_id'       => 't_base.game_id',
            'channel_main'  => new Fragment("COALESCE(IF(power.channel_id IN (".$planChannelsString."), IF(COALESCE(base_channel.channel_main_id,0) != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) as channel_main"),
            't_channel'     => new Fragment("IF(IF(t_base.channel_id != 0,t_base.channel_id, power.channel_id)=1013,4,IF(t_base.channel_id != 0,t_base.channel_id, power.channel_id)) as t_channel"),
            'department_id' => 't_base.ad_department_id as department_id',
            'user_id'       => 't_base.ad_user_id as user_id',
            'platform_id'   => 'power.platform_id as platform_id',
            'promotion_id'  => 'power.popularize_v2_id as promotion_id',
        ];

        $calcFields = [
            'new_user_num'        => new Fragment("COALESCE(SUM(new_user_num)) as new_user_num"),
            'cost_discount_sum'   => new Fragment("COALESCE(SUM(cost_discount_sum)) as cost_discount_sum"),
            'total_ltv_money_sum' => new Fragment("COALESCE(SUM(total_ltv_money_sum)) as total_ltv_money_sum"),
            'pay_sum'             => new Fragment("COALESCE(SUM(pay_sum)) as pay_sum"),
            'new_uer_pay_sum1'    => new Fragment("COALESCE(SUM(new_uer_pay_sum1)) as new_uer_pay_sum1"),
            'new_uer_pay_sum2'    => new Fragment("COALESCE(SUM(new_uer_pay_sum2)) as new_uer_pay_sum2"),
            'new_uer_pay_sum3'    => new Fragment("COALESCE(SUM(new_uer_pay_sum3)) as new_uer_pay_sum3"),
            'new_uer_pay_sum4'    => new Fragment("COALESCE(SUM(new_uer_pay_sum4)) as new_uer_pay_sum4"),
            'new_uer_pay_sum5'    => new Fragment("COALESCE(SUM(new_uer_pay_sum5)) as new_uer_pay_sum5"),
            'new_uer_pay_sum6'    => new Fragment("COALESCE(SUM(new_uer_pay_sum6)) as new_uer_pay_sum6"),
            'new_uer_pay_sum7'    => new Fragment("COALESCE(SUM(new_uer_pay_sum7)) as new_uer_pay_sum7"),
            'new_uer_pay_sum8'    => new Fragment("COALESCE(SUM(new_uer_pay_sum8)) as new_uer_pay_sum8"),
            'new_uer_pay_sum9'    => new Fragment("COALESCE(SUM(new_uer_pay_sum9)) as new_uer_pay_sum9"),
            'new_uer_pay_sum10'   => new Fragment("COALESCE(SUM(new_uer_pay_sum10)) as new_uer_pay_sum10"),
            'new_uer_pay_sum11'   => new Fragment("COALESCE(SUM(new_uer_pay_sum11)) as new_uer_pay_sum11"),
            'new_uer_pay_sum12'   => new Fragment("COALESCE(SUM(new_uer_pay_sum12)) as new_uer_pay_sum12"),
            'ltv_money_sum1'      => new Fragment("COALESCE(SUM(ltv_money_sum1)) as ltv_money_sum1"),
            'ltv_money_sum3'      => new Fragment("COALESCE(SUM(ltv_money_sum3)) as ltv_money_sum3"),
            'ltv_money_sum7'      => new Fragment("COALESCE(SUM(ltv_money_sum7)) as ltv_money_sum7"),
            'ltv_money_sum15'     => new Fragment("COALESCE(SUM(ltv_money_sum15)) as ltv_money_sum15"),
            'ltv_money_sum30'     => new Fragment("COALESCE(SUM(ltv_money_sum30)) as ltv_money_sum30"),
            'ltv_money_sum60'     => new Fragment("COALESCE(SUM(ltv_money_sum60)) as ltv_money_sum60"),
            'ltv_money_sum90'     => new Fragment("COALESCE(SUM(ltv_money_sum90)) as ltv_money_sum90"),
            'ltv_money_sum120'    => new Fragment("COALESCE(SUM(ltv_money_sum120)) as ltv_money_sum120"),
            'ltv_money_sum150'    => new Fragment("COALESCE(SUM(ltv_money_sum150)) as ltv_money_sum150"),
            'ltv_money_sum180'    => new Fragment("COALESCE(SUM(ltv_money_sum180)) as ltv_money_sum180"),
            'ltv_money_sum210'    => new Fragment("COALESCE(SUM(ltv_money_sum210)) as ltv_money_sum210"),
            'ltv_money_sum240'    => new Fragment("COALESCE(SUM(ltv_money_sum240)) as ltv_money_sum240"),
            'ltv_money_sum270'    => new Fragment("COALESCE(SUM(ltv_money_sum270)) as ltv_money_sum270"),
            'ltv_money_sum300'    => new Fragment("COALESCE(SUM(ltv_money_sum300)) as ltv_money_sum300"),
            'ltv_money_sum330'    => new Fragment("COALESCE(SUM(ltv_money_sum330)) as ltv_money_sum330"),
            'ltv_money_sum360'    => new Fragment("COALESCE(SUM(ltv_money_sum360)) as ltv_money_sum360"),
            'max_update_time'     => new Fragment("max(t_base.update_time) as max_update_time"),
        ];

        if ($mode === static::ROW_MODE_INFO) {
            $fields = array_merge($infoFields, $calcFields);
        }
        else {
            $fields = $calcFields;
        }

        return $fields;
    }


    /**
     * where 条件字段映射关系配置
     *
     * @param int $mode
     *
     * @return array
     */
    private function reflectFieldMap(int $mode = -1): array
    {
        return [
            'month'           => 't_base.month',
            'package_id'      => 't_base.package_id',
            'cp_game_id'      => 't_base.cp_game_id',
            'game_id'         => 't_base.game_id',
            'channel_id'      => [
                't_base.channel_id',
                'power.channel_id',
            ],
            'channel_main_id' => [
                'base_channel.channel_main_id',
                'power.channel_main_id',
            ],
            'platform_id'     => 'power.platform_id',
            'promotion_id'    => 'power.popularize_v2_id',
            'department_id'   => 't_base.ad_department_id',
            'user_id'         => 't_base.ad_user_id',
        ];
    }


    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
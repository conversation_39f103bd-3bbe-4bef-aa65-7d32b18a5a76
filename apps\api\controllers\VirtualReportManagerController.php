<?php

namespace app\apps\api\controllers;

use app\service\AdvertiserData\VirtualReportLogServ;
use Plus\MVC\Controller\JsonController;

/**
 *
 */
class VirtualReportManagerController extends JsonController
{
    /**
     * @return array
     */
    public function LogInfoAction(): array
    {
        $id       = $this->getValue('id');
        $page     = $this->getValue('page', 1);
        $pageSize = $this->getValue('page_size', 50);

        if (empty($id)) {
            return $this->error('missing id');
        }

        $result = (new VirtualReportLogServ())->getListByReportId($id, $page, $pageSize);
        $fields = [
            ['title' => '上报时间', 'dataIndex' => 'report_time'],
            ['title' => '包号', 'dataIndex' => 'package_id'],
            ['title' => '上报事件', 'dataIndex' => 'event_type'],
            ['title' => '上报参数', 'dataIndex' => 'request_params'],
            ['title' => '返回结果', 'dataIndex' => 'report_result'],
        ];

        $data = array_merge($result, ['fields' => $fields]);

        return $this->success($data);
    }


}
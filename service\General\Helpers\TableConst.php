<?php

namespace app\service\General\Helpers;


class TableConst
{
    const ORIGIN_SDK_USER_PAYMENT      = 'origin_platform.tb_sdk_user_payment';
    const DDC_SDK_USER_PAYMENT_VIRTUAL = 'ddc_platform.dwd_sdk_user_payment_virtual_ext';
    const CONF_PACKAGE_DETAIL          = 'base_conf_platform.tb_package_detail_conf';
    const CONF_BASE_CHANNEL            = 'base_conf_platform.tb_base_channel_conf';
    const CONF_GAME_BASE               = 'base_conf_platform.tb_base_game_conf';
    const CONF_PAY_WAY                 = 'base_conf_platform.tb_base_payway_conf';
    const PAYMENT_TAGGING              = 'ddc_platform.dwd_sdk_payment_tagging';
    const BIZ_TEST_ACCOUNT             = 'base_conf_platform.biz_test_account';
    const BIZ_PY_PAYMENT               = 'py_platform.payment';
    const BASE_LIVE_ACCOUNT            = 'base_conf_platform.tb_ad_live_account_conf';
    const DWD_SDK_USER_PAYMENT         = 'ddc_platform.dwd_sdk_user_payment';
    const BASE_PAYWAY_CONF            = 'base_conf_platform.tb_base_payway_conf';
    const AD_DATA_UPLOAD_CONF          = 'dataspy.tb_ad_data_upload_conf';
    const DWD_SDK_USER_PAYMENT_VOIDED  = 'ddc_platform.dwd_sdk_user_payment_voided';
}
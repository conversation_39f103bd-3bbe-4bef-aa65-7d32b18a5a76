<?php
declare(ticks = 1);

namespace app\daemon;

use app\models\DataSpy\AdminUser;
use app\service\Queue;
use PhpAmqpLib\Message\AMQPMessage;
use Plus\CLI\DaemonProcess;

/**
 * 同步OA外部用户
 * <AUTHOR>
 */
class SynOaExtUser extends DaemonProcess
{

    /**
     * run
     * @return void
     */
    public function run()
    {
        $queue = \Plus::$service->queue->rabbitQueue;
        $queue->subscribe([$this, 'job'], 'basic_user', Queue::BASIC_USER_SPY);
    }

    /**
     * job 执行方法
     * @param AMQPMessage $message msg
     * @return void
     */
    public function job($message)
    {
        $message->ack();
        $data = json_decode($message->getBody(), true);
        \Plus::$app->log->info($data, [], 'oa_user_ext');
        if (empty($data)) {
            return;
        }
        $user = $data['body'];
        if ($user['userType'] != 2) { // 非外部用户
            return;
        }
        $findApp = false;
        foreach ($user['applicationIds'] as $v) {
            if (in_array($v, [24, 73])) { //智推、手盟数据
                $findApp = true;
                break;
            }
        }
        if (!$findApp) {
            return;
        }
        $id = '10' . $user['userId'];  // id 加 10开头

        $userBiz = new AdminUser();
        if (!empty($userBiz->findById($id)->getRawData())) {
            return;
        }
        $userBiz->ID               = $id;
        $userBiz->USER_NAME        = $user['username'];
        $userBiz->REAL_NAME        = $user['realname'];
        $userBiz->PASSWORD         = 'c71672754d70cc0423c01a4bd47fa855';
        $userBiz->DEPARTMENT_ID    = 387;
        $userBiz->GROUP_IDS        = ',';
        $userBiz->LEVEL            = 1;
        $userBiz->STATUS           = 1;
        $userBiz->SEX              = 1;
        $userBiz->EMAIL            = '';
        $userBiz->ARRANGE_ALLOW    = 1;
        $userBiz->ADMIN_DEPARTMENT = 0;
        $userBiz->IS_SHOUMENG      = 0;
        $userBiz->ROI_POWER        = 2;
        $userBiz->insert();
    }
}

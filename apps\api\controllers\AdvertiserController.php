<?php

namespace app\apps\api\controllers;

use app\extension\Exception\ParameterException;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\StringAKA;
use app\logic\advertise\AdDashLiveLogic;
use app\logic\advertise\AdDashLogic;
use app\logic\advertise\AdPaymentInfoLogic;
use app\models\DdcPlatform\DwsPlanAdBaseDaily;
use app\models\DdcPlatform\DwsSvkeyAdBaseDaily;
use app\models\DdcPlatform\DwsSvkeyAdPaymentDaily;
use app\service\AdConf\SvLinkProvider;
use app\service\Advertiser\AdDashProvider;
use app\service\Advertiser\AdPaymentProvider;
use app\service\Advertiser\AdIndicatorsProvider;
use app\service\AdvertiserData\SplitNature;
use app\service\General\GeneralOptionServ;
use app\service\OriginData\SdkLoginProvider;
use app\util\Common;
use Plus\MVC\Controller\JsonController;
use Smarty\Exception;

class AdvertiserController extends JsonController
{
    const TIPS_NULL_DATE_PARAM = '缺失时间段参数';


    /**
     * 查询拆分自然量接口
     *
     * request:
     *  start_time(string): 查询开始时间
     *  end_time(string): 查询结束时间
     *  package_id(string|array): 包号
     *  department_id(string|array): 部门
     *
     * @return array
     * @throws ParameterException
     */
    public function listBySplitNatureAction(): array
    {
        [
            $rangeDateBegin,
            $rangeDateEnd,
            $packageId,
            $departmentId,
        ] = [
            $this->getValue('start_time'),
            $this->getValue('end_time'),
            $this->getValue('package_id'),
            $this->getValue('department_id'),
        ];


        if (empty($rangeDateBegin) || empty($rangeDateEnd)) {
            throw new ParameterException(static::TIPS_NULL_DATE_PARAM);
        }

        $serv = new SplitNature();

//        todo: test
//        $data = $serv->list([
//            't1.TDAY'       => [
//                $rangeDateBegin,
//                $rangeDateEnd,
//            ],
//            't1.PACKAGE_ID' => '99999999',
//            [
//                ['POWER.AD_DEPARTMENT_ID', 'in', 666],
//                ['t4.DEPARTMENT_ID', 'in', 666],
//            ],
//        ], [
//
//        ]);

        $wheres         = [];
        $wheres['TDAY'] = [$rangeDateBegin, $rangeDateEnd];

        if (!empty($packageId)) {
            $wheres['PACKAGE_ID'] = is_string($packageId) ? \explode(',', $packageId) : $packageId;
        }

        if (!is_null($departmentId)) {
            $wheres['DEPARTMENT_ID'] = is_string($departmentId) ? \explode(',', $departmentId) : $departmentId;
        }

        // todo: 暂时默认后续优化
        $data = $serv->list($wheres);

        //查询条件不拆分自然量时的订单金额
        //$realPayMoney = (new DwsSvkeyAdPaymentDaily())->getPayMoney($wheres);
        //计算拆分的具体金额
        //$data[0]["total_amount"] = $data[0]["total_amount"] - $realPayMoney;

        return $this->success($data);
    }

    //获取广告指标
    public function dataAction()
    {
        //params
        $params = [];
        [
            $params["range_date_start"],
            $params["range_date_end"],
            $params["page"],
            $params["page_size"],
            $params["cp_game_id"],
            $params["game_id"],
            $params["package_id"],
            $params["promotion_channel_id"],
            $params["sv_key"],
            $params["groups"],
            $params["fields"],
            $params["order"],
            $params["user_id"],
            $params["department_id"],
            $params["channel_id"],
            $params["promotion_id"],
        ] = [
            $this->getValue("range_date_start", date("Y-m-d")),
            $this->getValue("range_date_end", date("Y-m-d")),
            $this->getValue("page", 1),
            $this->getValue("page_size", 20),
            $this->getValue("cp_game_id"),
            $this->getValue("game_id"),
            $this->getValue("package_id"),
            $this->getValue("promotion_channel_id"),
            $this->getValue("sv_key"),
            $this->getValue("groups", "tday"),
            $this->getValue("fields", "new_user"),
            $this->getValue("order", ""),
            $this->getValue("user_id"),
            $this->getValue("department_id"),
            $this->getValue("channel_id"),
            $this->getValue("promotion_id"),
        ];
        $data = (new DwsSvkeyAdBaseDaily())->list($params);
        return $this->success($data);
    }

    public function liveUserAction()
    {
        $startDate = $this->getValue("range_date_start", date("Y-m-d"));
        $endDate   = $this->getValue("range_date_end", date("Y-m-d"));
        $page      = $this->getValue("page", 1);
        $pagesize  = $this->getValue("page_size", 2);
        $limit     = $page * $pagesize;
        $offset    = ($page - 1) * $pagesize;

        if (!$startDate || !$endDate) {
            throw new ParameterException("请输入时间");
        }
        $startTime = $startDate . " 00:00:00";
        $endTime   = $endDate . " 23:59:59";
        //直播包号
        $sql  = "select PACKAGE_ID from ddc_platform.dws_plan_ad_base_daily   where TDAY BETWEEN  '{$startDate}' and '{$endDate}' and MARKETING_GOAL=2 and  PLAN_ID=0";
        $data = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        if (!$data) {
            $data = [["PACKAGE_ID" => *********]];
        }
        $packageIds = implode(",", array_column($data, "PACKAGE_ID"));

        //直播计划
        $sql  = "select PLAN_ID from ddc_platform.dws_plan_ad_base_daily   where TDAY BETWEEN '{$startDate}' and '{$endDate}' and MARKETING_GOAL=2 and  PLAN_ID!=0";
        $data = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        if (!$data) {
            $data = [["PLAN_ID" => *********]];
        }
        $planIds = "'" . implode("','", array_column($data, "PLAN_ID")) . "'";

        $sql = "
        select cp_game_id,MAIN_ACCOUNT core_account from ddc_platform.dwd_sdk_adsource_game where PACKAGE_ID in({$packageIds}) and NEWLOGIN_TIME BETWEEN '{$startTime}' and '{$endTime}' and PLAN_ID=0 UNION all 
select CP_GAME_ID,MAIN_ACCOUNT from ddc_platform.dwd_sdk_adsource_game where (PLAN_ID in({$planIds}) or SV_KEY IN({$planIds})) and NEWLOGIN_TIME BETWEEN '{$startTime}' and '{$endTime}'  limit $offset,$limit
        ";

        $data = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        return $this->success($data);
    }

    /**
     * 对外广告数据接口
     *
     * @link http://192.168.100.16:3030/project/6425/interface/api/42870
     * @return array
     *
     */
    public function dashDataAction(): array
    {
        $request = $this->request;

        $paginate = [
            'page'      => $request->getValue('page', 1),
            'page_size' => $request->getValue('page_size', 100),
        ];

        $rangeDate = [
            $request->getValue('start_time', date("Y-m-d")),
            $request->getValue('end_time', date("Y-m-d")),
        ];

        sort($rangeDate);

        $package       = $this->getValue('package_id');
        $planId        = $this->getValue('plan_id');
        $promotionId   = $this->getValue('promotion_id');
        $marketingGoal = $this->getValue('marketing_goal');

        $options = [
            'range_date' => $rangeDate,
        ];

        if (!empty($package)) {
            $options['package_id'] = Arr::wrap($package);
        }
        if (!empty($planId)) {
            $options['plan_id'] = Arr::wrap($planId);
        }
        if (!empty($promotionId)) {
            $options['promotion_id'] = Arr::wrap($promotionId);
        }
        if (!empty($marketingGoal)) {
            $options['marketing_goal'] = Arr::wrap($marketingGoal);
        }

        $groups  = ['tday', 'package_id', 'plan_id'];
        $columns = ['pay_user', 'pay_money', 'new_user', 'pay_user_new', 'pay_money_new', 'pay_user_new_percent', 'create_role_new', 'active_user'];
        if (!empty($promotionId) && !empty($marketingGoal)) {
            $groups  = ['tmonth', 'cp_game_id', 'game_id', 'promotion_id', 'promotion_channel_id'];
            $columns = ['tmonth', 'pay_money'];
        }

        try {
            $logic  = new AdDashLogic();
            $result = $logic->tableBase(
                $options,
                $groups,
                $paginate,
                [],
                $columns,
                AdDashProvider::RESULT_ALL,
                true,
            );

            if (!empty($result['list'])) {
                $list = &$result['list'];
                $logic->fillAdDimensionName($list, $groups);
            }

            return $this->success($result);
        }
        catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 通过短链地址查找短链id
     * @link http://192.168.100.16:3030/project/6425/interface/api/42878
     * @return array
     */
    public function findSvlinkAction()
    {
        $requestJson   = $this->request->getRawBody();
        $requestParams = json_decode($requestJson, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return $this->error('JSON解析错误', ['trace' => json_last_error_msg()]);
        }

        try {
            $svLinks  = $requestParams["sv_links"];
            $provider = new SvLinkProvider();
            $result   = $provider->findSvLinkId($svLinks);

            return $this->success($result);
        }
        catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * @return array
     */
    public function adPaymentInfoAction(): array
    {
        $request   = $this->request;
        $paginate  = [
            'page'      => $request->getValue('page', 1),
            'page_size' => $request->getValue('page_size', 100),
        ];
        $timeStart = $request->getValue('start_time', date("Y-m-d"));
        $timeEnd   = $request->getValue('end_time', date('Y-m-d'));
        $params    = [
            'range_date' => [
                $timeStart, $timeEnd,
            ],
        ];
        $package   = $this->getValue('package_id');
        $planId    = $this->getValue('plan_id');
        $mode      = $this->getValue('type', 'pay_money');

        if (!empty($package)) {
            $params['package_id'] = $package;
        }

        if (!empty($planId)) {
            $params['plan_id'] = $planId;
        }

        if (!empty($mode)) {
            $params['target'] = $mode;
        }
        try {
            $provider = new AdPaymentProvider();
            $result   = $provider->getCreativeInfo($params, [], $paginate, [], true);

            if (!empty($result['list'])) {
                $list = &$result['list'];

                $mainAccount      = array_column($list, 'main_account');
                $sdkLoginProvider = new SdkLoginProvider();
                $accountMap       = $sdkLoginProvider->getLastLoginTimeWithAccounts($mainAccount);
                $accountMap       = array_column($accountMap, 'last_login_time', 'core_account');

                foreach ($list as &$foo) {
                    $account = $foo['main_account'];
                    if (isset($accountMap[$account])) {
                        $foo['last_login_time'] = $accountMap[$account];
                    }
                    else {
                        $foo['last_login_time'] = '';
                    }
                }
            }

            return $this->success($result);
        }
        catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * @return array
     */
    public function indicatorsDetailAction(): array
    {
        $today      = date('Y-m-d');
        $req        = (fn() => $this->params ?? [])->call(\Plus::$app->request);
        $indicators = Arr::pull($req, 'indicators');
        $provider   = new AdIndicatorsProvider();
        $fn         = StringAKA::underscoreToCamelCase($indicators);

        $params = [
            'range_date' => [
                Arr::pull($req, 'start_time', $today), Arr::pull($req, 'end_time', $today),
            ],
        ];

        sort($params['range_date']);

        $paginate = [
            'page'      => Arr::pull($req, 'page', 1),
            'page_size' => Arr::pull($req, 'page_size', 100),
        ];

        $params = array_merge($params, $req);

        if (method_exists($provider, $fn)) {
            try {
                ['list' => $list, 'total' => $total] = call_user_func_array([$provider, $fn], [$params, $paginate]);

                if ($indicators == 'ad_payment_live_exclude') {
                    $channelCollect = (new GeneralOptionServ())->listChannelOptions()->toArray();
                    $channelMap     = array_column($channelCollect, 'val', 'key');

                    foreach ($list as &$foo) {
                        if (!empty($foo['first_login_package_channel'])) {
                            $foo['first_login_package_channel_name'] = $channelMap[$foo['first_login_package_channel']] ?? '';
                        }
                        else {
                            $foo['first_login_package_channel_name'] = '';
                        }

                        if (!empty($foo['first_paid_package_channel'])) {
                            $foo['first_paid_package_channel_name'] = $channelMap[$foo['first_paid_package_channel']] ?? '';
                        }
                        else {
                            $foo['first_paid_package_channel_name'] = '';
                        }

                        if (!empty($foo['after_login_package_channel'])) {
                            $foo['after_login_package_channel_name'] = $channelMap[$foo['after_login_package_channel']] ?? '';
                        }
                        else {
                            $foo['after_login_package_channel_name'] = '';
                        }
                    }

                }

            }
            catch (\Exception $e) {
                return $this->error('Return format not recognized!', $e->getMessage());
            }
        }
        else {
            return $this->error('unknown this indicators!');
        }

        return $this->success(['list' => $list, 'total' => $total]);
    }

    /**
     * 广告数据(直播向)
     * @return array
     */
    public function dashDataLiveAction(): array
    {
        $request   = $this->request;
        $params    = (fn() => $this->params ?? [])->call($request);
        $params    = array_filter($params);
        $paginate  = [
            'page'      => Arr::pull($params, 'page', 1),
            'page_size' => Arr::pull($params, 'page_size', 100),
        ];
        $params['range_date'] = [
            Arr::pull($params, 'start_time', date('Y-m-d')),
            Arr::pull($params, 'end_time', date('Y-m-d')),
        ];

        sort($params['range_date']);
        $groups = ['tday', 'package_id', 'plan_id'];

        foreach (['package_id', 'plan_id', 'promotion_id', 'marketing_goal'] as $kk) {
            if (isset($params[$kk])) {
                $params[$kk] = Arr::wrap($params[$kk]);
            }
        }
        $columns = ['pay_user', 'pay_money', 'new_user', 'pay_user_new', 'pay_money_new', 'pay_user_new_percent', 'create_role_new', 'active_user'];
        if (!empty($params['promotion_id']) && !empty($params['marketing_goal'])) {
            $groups  = ['tmonth', 'cp_game_id', 'game_id', 'promotion_id', 'promotion_channel_id'];
            $columns = ['tmonth', 'pay_money'];
        }

        try {
            $logic  = new AdDashLiveLogic();
            $result = $logic->tableBase(
                $params, $groups, [], $paginate, $columns, true,
            );

            if (!empty($result['list'])) {
                $list = &$result['list'];
                (new AdDashLogic())->fillAdDimensionName($list, $groups);
            }

            return $this->success($result);
        }
        catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}









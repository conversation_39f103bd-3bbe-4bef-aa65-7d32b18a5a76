<?php

namespace app\service\AdvertiserData\Components\Matcher\Traits;

use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

trait DepartmentMatchAble
{
    /**
     * 部门匹配
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchDepartment(&$qb, array &$params)
    {
        if (!isset($params['department_id'])) return;

        $field = $this->getReflectField('department_id');
        $data  = $params['department_id'];

        QueryBuilderHelper::priorityOrderBuild($qb, $field, $data);
    }

    /**
     * 投放人匹配
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchUserId(&$qb, array &$params)
    {
        if (!isset($params['user_id'])) return;

        $field = $this->getReflectField('user_id');
        $data  = $params['user_id'];

        QueryBuilderHelper::priorityOrderBuild($qb, $field, $data);
    }
}
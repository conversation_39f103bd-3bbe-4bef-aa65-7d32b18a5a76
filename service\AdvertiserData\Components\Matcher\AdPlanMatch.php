<?php

namespace app\service\AdvertiserData\Components\Matcher;

use app\extension\Support\Collections\Arr;
use app\models\AdpPlatform\TbAdpPlanBase;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class AdPlanMatch extends AdCampaignMatch
{
    /**
     * @inerhitDoc
     * @return array
     * @throws \RedisException
     */
    protected function matchFnList(): array
    {
        $new = [
            'plan_id'            => function (SelectQuery &$qb, $key, $value) {
                if ($value == null) {
                    $qb->where($key, 'IS', 'NULL');
                }
                else {
                    QueryBuilderHelper::baseBuild($qb, $key, $value);
                }
            },
            'plan_name'          => $this->matchPlanName(),
            'is_has_natural'     => $this->matchIsHasNatural(),
            'is_has_appointment' => $this->matchIsHasAppointment(),
            'data_scope'         => $this->matchDataScope(),
            'marketing_goal'     => $this->matchMarketingGoal()
        ];

        return array_merge(parent::matchFnList(), $new);
    }

    /**
     * @return \Closure
     */
    protected function matchPlanName(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value, $params = []) {
            if (isset($params['tday'])) {
                $rangeStart = $params['tday'][0];
                $rangeEnd   = $params['tday'][1];
            }
            else {
                $rangeStart = date('Y-m-d', strtotime('-30 day'));
                $rangeEnd   = date('Y-m-d');
            }

            $planId = $this->getReflectKey('plan_id');

            if (\trim($value) == '自然量') {
                $qb->where($planId, 0);
            }
            else {
                $ids = (new TbAdpPlanBase())->getPlanIdByName($value, $rangeStart, $rangeEnd);

                if ($ids) {
                    $qb->where($planId, new Parameter($ids));
                }
                else {
                    $qb->where($planId, 'error');
                }
            }
        };
    }

    /**
     * @return \Closure
     */
    protected function matchIsHasNatural(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value, $params = []) {
            if (empty($value)) {
                $planId = $this->getReflectKey('plan_id');
                $qb->where($planId, '>', '0');
            }
        };
    }

    /**
     * @return \Closure
     */
    protected function matchIsHasAppointment(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value, $params = []) {
            if (empty($value)) {
                $planId = $this->getReflectKey('is_appointment');
                $qb->where($planId, '!=', '1');
            }
        };
    }

    /**
     * @return \Closure
     */
    protected function matchDataScope(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value) {
            $field = $this->getReflectKey('is_ad_data');

            $n = (int)$value;
            if ($n === 1) {
                $qb->where($field, '=', '1');
            }
            elseif ($n === 2) {
                $qb->where($field, '=', '0');
            }
        };
    }

    /**
     * @return \Closure
     */
    protected function matchMarketingGoal(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value) {
            $options = Arr::wrap($value);
            $f       = $this->getReflectKey('marketing_goal');

            if (count($options) == 2) {
                return;
            }

            $a = $options[0];

            if ($a == 1) {
                $qb->where($f, '!=', '2');
            }
            elseif ($a == 2) {
                $qb->where($f, '=', '2');
            }
        };
    }

}
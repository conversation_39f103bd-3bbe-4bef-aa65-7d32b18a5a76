<?php

namespace app\service\Media\Components\Matcher;

use app\extension\Support\Helpers\DBHelper\MatcherAbstract;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\SelectQuery;


class LiveAccountMatcher extends MatcherAbstract
{
    protected function matchFnList(): array
    {
        return [
            'operations_manager' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'use_kind'           => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'business_ownership' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'media_platform_id'  => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            }
        ];
    }
}
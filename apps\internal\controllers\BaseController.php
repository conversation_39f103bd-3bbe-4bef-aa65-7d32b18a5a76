<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Traits\AdIndexCalculators;
use app\apps\internal\Traits\ColumnsInteract;
use app\apps\internal\Traits\InternalRoutes;
use app\extension\Exception\ParameterException;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\service\ConfigService\TableFieldsHubServ;
use app\service\Tool\BusinessDataDictServ;
use Plus\MVC\Controller\JsonController;

/**
 * @BaseController
 *
 */
abstract class BaseController extends JsonController
{
    use InternalRoutes, AdIndexCalculators, ColumnsInteract;


    /**
     * @param ...$options
     *
     * @return array
     */
    protected function tableFields(...$options): array
    {
        $classmap  = \explode('\\', static::class);
        $className = str_replace('Controller', '', array_pop($classmap));
        $serv      = new TableFieldsHubServ($className);
        $fields    = $serv->getFields(...$options);

        return $fields->toArray();
    }

    /**
     * @param array|Collection $data
     *
     * @return array
     */
    protected function filterField($data): array
    {
        return $data instanceof Collection ? $data->values()->toArray() : (array)$data;
    }

    /**
     * 数据接口
     *
     * @route {host}/internal/{block}/data
     * @template 参数示例:
     *             range_date(选择日期)
     *             cp_game_id(游戏原名ID)
     *             game_id(游戏统计名ID)
     *             app_show_id(游戏前端名ID)
     *             channel_main_id(主渠道ID)
     *             platform_id(客户端ID)
     *             package_id(包号ID)
     *             sv_key(短链ID)
     *
     * @return array
     * @throws \RedisException
     */
    public function dataAction(): array
    {
        $result        = [];
        $params        = $this->wrapParams(\Plus::$app->request);
        $getMethodFunc = Arr::wrap($params->pull('method'));

        foreach ($getMethodFunc as $func) {
            if (!is_callable([$this, $func])) continue;

            $r = $this->{$func}($params);

            if ($func == 'fields') {
                if (!empty($r['fields'])) {
                    $this->appendFieldTips($r['fields']);
                }
            }

            $result = array_merge($result, $r);
        }

        return $this->success($result);
    }

    /**
     * 可用请求参数
     *
     * @param string[]|string $key
     *
     * @return Collection
     */
    protected function registerParams($key = null): Collection
    {
        $today = date('Y-m-d');

        $collect = collect([
            ['field' => 'range_date_start', 'default' => $today], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
            ['field' => 'cp_game_id'], // 游戏原名
            ['field' => 'game_id'], // 游戏统计名
            ['field' => 'app_show_id'], // 游戏前端名
            ['field' => 'channel_main_id'], // 主渠道
            ['field' => 'channel_id'], //渠道
            ['field' => 'promotion_channel_id'], // 推广渠道
            ['field' => 'platform_id'], // 客户端ID
            ['field' => 'package_id'], // 包号
            ['field' => 'promotion_id'], // 推广分类
            ['field' => 'department_id'], // 部门
            ['field' => 'user_id'], // 投放人
            ['field' => 'ad_account'], // 投放账号
            ['field' => 'advertiser_id'], // 广告账号ID
            ['field' => 'campaign_id'], // 广告组ID
            ['field' => 'campaign_name'], // 广告组名称
            ['field' => 'plan_name'], // 计划名
            ['field' => 'plan_id'], // 计划ID
            ['field' => 'creative_name'], // 创意名
            ['field' => 'creative_id'], // 创意ID
            ['field' => 'data_range'], // 数据范围
            ['field' => 'tab'],
            ['field' => 'is_has_natural', 'default' => 1], // 是否含自然量
            ['field' => 'is_has_appointment', 'default' => 1], // 是否包含预约数据
            ['field' => 'account_id'], // 账号ID搜索
            ['field' => 'data_scope'], // 数据范围搜索
            ['field' => 'channel_id_tags'], // 渠道标签
            ['field' => 'channel_main_id_tags'], // 主渠道标签
            ['field' => 'marketing_goal'], // 营销场景
            ['field' => 'package_id_tags'], // 包号标签
            ['field' => 'game_id_tags'], // 包号标签
            ['field' => 'page_size', 'default' => 50],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups'],
            ['field' => 'user_os'],
            ['field' => 'method', 'default' => ['data', 'fields']],
            ['field' => 'cols', 'default' => []],
        ]);

        if (!empty($key)) {
            $collect = $collect->where('field', $key);
        }

        return $collect;
    }

    /**
     * @param array $fields
     * @return void
     * @throws \RedisException
     */
    protected function appendFieldTips(array &$fields)
    {
        $tips = (new BusinessDataDictServ())->getAll();

        foreach ($fields as &$foo) {
            $title = $foo['title'];
            if (isset($tips[$title])) {
                $foo['tip'] = nl2br($tips[$title]);
            }
        }
    }

}
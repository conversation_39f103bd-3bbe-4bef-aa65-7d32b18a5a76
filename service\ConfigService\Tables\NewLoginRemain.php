<?php

namespace app\service\ConfigService\Tables;

use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\Mather;
use app\service\ConfigService\Constants\FieldTag;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class NewLoginRemain implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    /**
     * @param $options
     *
     * @return Collection
     * @throws \Exception
     */
    public function getFields($options = null): Collection
    {
        $rangeDateDimension = (int)($options['range_date_dimension'] ?? 2);

        $today  = new \DateTime();
        $NNodes = array_merge(
            range(1, 29), [44, 59], Mather::findNumInScope(59, 719, 30)
        );

        $collect = $this->getBaseFields(['baseOperationCollect']);

        $collect = $collect->merge([
            'new_user'       => ['title' => '新增用户', 'sorter' => 'true'],
            'remain_days'    => ['title' => '留存统计天数'],
            'remain_current' => ['title' => '当前留存率'],
        ]);

        $groups = $options['groups'] ?? [];

        if (in_array('package_id', $groups)) {
            $collect = $collect->merge([
                'package_tags' => ['title' => '包号标签', 'classify' => ['attrs', 'tags']]
            ]);
        }

        if (
            in_array('package_id', $groups)
            || in_array('channel_id', $groups)
        ) {
            $collect = $collect->merge([
                'channel_tags' => ['title' => '推广子渠道标签', 'classify' => ['attrs', 'tags']]
            ]);
        }

        // $nDays        = days_apart($today, $options['range_date_start'] ?? $today);
        $nDays        = 720;
        $nDays        -= 1;
        $remainFields = $this->remainNCollect(Mather::findIn($nDays, $NNodes));
        $remainFields = FieldTag::tagClassifyToNField($remainFields, 'remain',
            [
                ['range' => [1, 29], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_1]],
                ['range' => [44, 179], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_2]],
                ['range' => [209, 359], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_3]],
                ['range' => [389, 719], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_4]],
            ]
        );

        $collect = $collect->merge($remainFields);

        return $this->formatStandard($collect);
    }
}
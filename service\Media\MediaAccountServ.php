<?php

namespace app\service\Media;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiserData\Components\Helpers\Convert;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\General\Helpers\TableConst;
use app\service\Media\Helper\MediaTableConst;
use PhpOffice\PhpSpreadsheet\Calculation\Statistical\Distributions\F;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;


class MediaAccountServ
{
    const QB_MODE_ALL = 2;
    const QB_MODE_ACCOUNT = 1;
    const MODE_ALL = 4;
    const MODE_SUMMARY = 2;
    const MODE_LIST = 1;


    public function getInfoList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $qb = $this->getQueryBuilder();
        $this->whereMatch(
            $qb,
            $params,
            [
                'media_platform_id'  => 'live_account.type',
                'account_id'         => 'dws_account.account_id',
                'business_ownership' => 'live_account.business_ownership',
            ]
        );
        $result = [];

        if ($mode & static::MODE_LIST) {
            $qb->columns([
                'tday as tday',
                'live_account.account_id as account_id',
                'live_account.business_ownership as business_ownership',
                'live_account.account as account_name',
                'live_account.type as media_platform_id',
                'live_account.operations_manager as operations_manager',
                'live_account.use_kind as use_kind',

                new Fragment('SUM(current_fans) as current_fans'),
                new Fragment('SUM(all_video_play_today) as all_video_play_today'),
                new Fragment('SUM(normal_task_post) as normal_task_post'),
                new Fragment('SUM(normal_task_post_success) as normal_task_post_success'),
                new Fragment('SUM(normal_new_video_play_today) as normal_new_video_play_today'),
                new Fragment('SUM(normal_new_video_play) as normal_new_video_play'),
                new Fragment('SUM(normal_new_video_like) as normal_new_video_like'),
                new Fragment('SUM(normal_new_video_comment) as normal_new_video_comment'),
                new Fragment('SUM(normal_new_video_forward) as normal_new_video_forward'),
                new Fragment('SUM(normal_video_play_today) as normal_video_play_today'),
                new Fragment('SUM(contri_task_post) as contri_task_post'),
                new Fragment('SUM(contri_task_post_success) as contri_task_post_success'),
                new Fragment('SUM(contri_new_video_play_today) as contri_new_video_play_today'),
                new Fragment('SUM(contri_new_video_play) as contri_new_video_play'),
                new Fragment('SUM(contri_new_video_like) as contri_new_video_like'),
                new Fragment('SUM(contri_new_video_comment) as contri_new_video_comment'),
                new Fragment('SUM(contri_new_video_forward) as contri_new_video_forward'),
                new Fragment('SUM(contri_video_play_today) as contri_video_play_today'),
                new Fragment('SUM(today_comment) as today_comment'),
                new Fragment('SUM(today_watch) as today_watch'),
                new Fragment('SUM(today_like) as today_like'),
                new Fragment('SUM(normal_task_account) as normal_task_account'),
                new Fragment('SUM(normal_task_account_success) as normal_task_account_success'),
                new Fragment('SUM(contri_task_account) as contri_task_account'),
                new Fragment('SUM(contri_task_account_success) as contri_task_account_success'),
                new Fragment('SUM(active_task_account) as active_task_account'),
                new Fragment('SUM(current_fans) - SUM(fans_1_days_ago)  as new_fans_1_days_ago'),
                new Fragment('SUM(current_fans) - SUM(fans_7_days_ago)  as new_fans_7_days_ago'),
                new Fragment('SUM(current_fans) - SUM(fans_30_days_ago) as new_fans_30_days_ago'),
            ]);

            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    if ($g == 'media_platform') {
                        $g = 'media_platform_id';
                    }
                    if ($g == 'business_ownership_name') {
                        $g = 'business_ownership';
                    }
                    if ($g == 'use_kind_name') {
                        $g = 'use_kind';
                    }

                    $infoQb->groupBy($g);
                }
            }
            $noPageQb = clone $infoQb;

            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;

                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }

            if (!empty($sort)) {
                foreach ($sort as $s => $o) {
                    if ($s == 'account_id') {
                        $ns = 'live_account.account_id';
                    }
                    if ($s == 'job_kind') {
                        $ns = 'job_kind_id';
                    }
                    if ($s == 'media_platform') {
                        $ns = 'media_platform_id';
                    }
                    if ($s == 'use_kind_name') {
                        $ns = 'use_kind';
                    }
                    if ($s == 'business_ownership_name') {
                        $ns = 'business_ownership';
                    }
                    if (isset($ns)) {
                        $sort[$ns] = $o;
                        unset($sort[$s]);
                    }
                }
                $infoQb->orderBy($sort);
            }

            $result['list']  = $infoQb->fetchAll();
            $result['total'] = $this
                ->getConn()
                ->select()
                ->from(new Fragment('(' . (clone $noPageQb)->__toString() . ') as total_body'))
                ->count();
        }

        if ($mode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            $summaryQb->columns([
                new Fragment('SUM(current_fans) as current_fans'),
                new Fragment('SUM(all_video_play_today) as all_video_play_today'),
                new Fragment('SUM(normal_task_post) as normal_task_post'),
                new Fragment('SUM(normal_task_post_success) as normal_task_post_success'),
                new Fragment('SUM(normal_new_video_play_today) as normal_new_video_play_today'),
                new Fragment('SUM(normal_new_video_play) as normal_new_video_play'),
                new Fragment('SUM(normal_new_video_like) as normal_new_video_like'),
                new Fragment('SUM(normal_new_video_comment) as normal_new_video_comment'),
                new Fragment('SUM(normal_new_video_forward) as normal_new_video_forward'),
                new Fragment('SUM(normal_video_play_today) as normal_video_play_today'),
                new Fragment('SUM(contri_task_post) as contri_task_post'),
                new Fragment('SUM(contri_task_post_success) as contri_task_post_success'),
                new Fragment('SUM(contri_new_video_play_today) as contri_new_video_play_today'),
                new Fragment('SUM(contri_new_video_play) as contri_new_video_play'),
                new Fragment('SUM(contri_new_video_like) as contri_new_video_like'),
                new Fragment('SUM(contri_new_video_comment) as contri_new_video_comment'),
                new Fragment('SUM(contri_new_video_forward) as contri_new_video_forward'),
                new Fragment('SUM(contri_video_play_today) as contri_video_play_today'),
                new Fragment('SUM(today_comment) as today_comment'),
                new Fragment('SUM(today_watch) as today_watch'),
                new Fragment('SUM(today_like) as today_like'),
                new Fragment('SUM(normal_task_account) as normal_task_account'),
                new Fragment('SUM(normal_task_account_success) as normal_task_account_success'),
                new Fragment('SUM(contri_task_account) as contri_task_account'),
                new Fragment('SUM(contri_task_account_success) as contri_task_account_success'),
                new Fragment('SUM(active_task_account) as active_task_account'),
                new Fragment('SUM(current_fans) - SUM(fans_1_days_ago)  as new_fans_1_days_ago'),
                new Fragment('SUM(current_fans) - SUM(fans_7_days_ago)  as new_fans_7_days_ago'),
                new Fragment('SUM(current_fans) - SUM(fans_30_days_ago) as new_fans_30_days_ago'),
            ]);
            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    /**
     * 查询月维度报表
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $mode
     * @return array
     */
    public function getInfoListWithMonth(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $qb     = $this->getQueryBuilder(-1, 'month');
        $result = [];

        if (
            isset($params['range_date_start'])
            && isset($params['range_date_end'])
        ) {
            $params['range_date_start'] = date('Y-m-01', strtotime($params['range_date_start']));
            $params['range_date_end']   = date('Y-m-01', strtotime($params['range_date_end']));
        }

        $this->whereMatch(
            $qb, $params, [
            'media_platform_id'     => 'live_account.type',
            'account_id'            => 'dws_account.account_id',
            'business_ownership' => 'live_account.business_ownership',
            'tday'                  => 'tmonth',
        ]);

        if ($mode & static::MODE_LIST) {
            $qb->columns([
                new Fragment("DATE_FORMAT(tmonth,'%Y-%m') as tday"),
                'dws_account.account_id as account_id',
                'live_account.business_ownership as business_ownership',
                'live_account.account as account_name',
                'live_account.type as media_platform_id',
                'live_account.operations_manager as operations_manager',
                'live_account.use_kind as use_kind',

                new Fragment('SUM(fans_start_month) as fans_start_month'),
                new Fragment('SUM(fans_mid_month) as fans_mid_month'),
                new Fragment('SUM(fans_late_month) as fans_late_month'),
                new Fragment('SUM(month_add_fans) as month_add_fans'),
                new Fragment('SUM(homepage_access) as homepage_access'),
                new Fragment('SUM(normal_task_post) as normal_task_post'),
                new Fragment('SUM(normal_task_post_success) as normal_task_post_success'),
                new Fragment('SUM(normal_new_video_play_month) as normal_new_video_play_month'),
                new Fragment('SUM(normal_new_video_play) as normal_new_video_play'),
                new Fragment('SUM(normal_new_video_like) as normal_new_video_like'),
                new Fragment('SUM(normal_new_video_comment) as normal_new_video_comment'),
                new Fragment('SUM(normal_new_video_forward) as normal_new_video_forward'),
                new Fragment('SUM(normal_video_play_month) as normal_video_play_month'),
                new Fragment('SUM(contri_task_post) as contri_task_post'),
                new Fragment('SUM(contri_task_post_success) as contri_task_post_success'),
                new Fragment('SUM(contri_new_video_play_month) as contri_new_video_play_month'),
                new Fragment('SUM(contri_new_video_play) as contri_new_video_play'),
                new Fragment('SUM(contri_new_video_like) as contri_new_video_like'),
                new Fragment('SUM(contri_new_video_comment) as contri_new_video_comment'),
                new Fragment('SUM(contri_new_video_forward) as contri_new_video_forward'),
                new Fragment('SUM(contri_video_play_month) as contri_video_play_month'),
                new Fragment('SUM(month_comment) as month_comment'),
                new Fragment('SUM(month_watch) as month_watch'),
                new Fragment('SUM(month_like) as month_like'),
            ]);

            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    if ($g == 'media_platform') {
                        $g = 'media_platform_id';
                    }
                    if ($g == 'business_ownership_name') {
                        $g = 'business_ownership';
                    }
                    if ($g == 'use_kind_name') {
                        $g = 'use_kind';
                    }

                    $infoQb->groupBy($g);
                }
            }
            $noPageQb = clone $infoQb;

            if (!empty($paginate)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;

                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }

            if (!empty($sort)) {
                $infoQb->orderBy($sort);
            }

            $result['list']  = $infoQb->fetchAll();
            $result['total'] = $this
                ->getConn()
                ->select()
                ->from(new Fragment('(' . (clone $noPageQb)->__toString() . ') as total_body'))
                ->count();
        }

        if ($mode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            $summaryQb->columns([
                new Fragment('SUM(fans_start_month) as fans_start_month'),
                new Fragment('SUM(fans_mid_month) as fans_mid_month'),
                new Fragment('SUM(fans_late_month) as fans_late_month'),
                new Fragment('SUM(month_add_fans) as month_add_fans'),
                new Fragment('SUM(homepage_access) as homepage_access'),
                new Fragment('SUM(normal_task_post) as normal_task_post'),
                new Fragment('SUM(normal_task_post_success) as normal_task_post_success'),
                new Fragment('SUM(normal_new_video_play_month) as normal_new_video_play_month'),
                new Fragment('SUM(normal_new_video_play) as normal_new_video_play'),
                new Fragment('SUM(normal_new_video_like) as normal_new_video_like'),
                new Fragment('SUM(normal_new_video_comment) as normal_new_video_comment'),
                new Fragment('SUM(normal_new_video_forward) as normal_new_video_forward'),
                new Fragment('SUM(normal_video_play_month) as normal_video_play_month'),
                new Fragment('SUM(contri_task_post) as contri_task_post'),
                new Fragment('SUM(contri_task_post_success) as contri_task_post_success'),
                new Fragment('SUM(contri_new_video_play_month) as contri_new_video_play_month'),
                new Fragment('SUM(contri_new_video_play) as contri_new_video_play'),
                new Fragment('SUM(contri_new_video_like) as contri_new_video_like'),
                new Fragment('SUM(contri_new_video_comment) as contri_new_video_comment'),
                new Fragment('SUM(contri_new_video_forward) as contri_new_video_forward'),
                new Fragment('SUM(contri_video_play_month) as contri_video_play_month'),
                new Fragment('SUM(month_comment) as month_comment'),
                new Fragment('SUM(month_watch) as month_watch'),
                new Fragment('SUM(month_like) as month_like'),
            ]);
            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }


    /**
     * @param SelectQuery $qb
     * @param array $params
     * @param array $reflectMap
     *
     * @return void
     */
    protected function whereMatch(SelectQuery &$qb, array $params = [], array $reflectMap = [])
    {
        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            $rangeDate = [
                $params['range_date_start'],
                $params['range_date_end'],
            ];

            sort($rangeDate);
            $rangeDate = array_unique($rangeDate);
            $field     = $reflectMap['tday'] ?? 'tday';

            if (count($rangeDate) === 1) {
                $qb->where($field, $rangeDate[0]);
            }
            else {
                $qb->where($field, 'between', $rangeDate[0], $rangeDate[1]);
            }
        }

        if (isset($params['media_platform_id'])) {
            $d     = $params['media_platform_id'];
            $field = $reflectMap['media_platform_id'] ?? 'media_platform_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['business_ownership_id'])) {
            $d     = $params['business_ownership_id'];
            $field = $reflectMap['business_ownership'] ?? 'business_ownership';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['business_ownership'])) {
            $d     = $params['business_ownership'];
            $field = $reflectMap['business_ownership'] ?? 'business_ownership';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['account_id'])) {
            $d     = $params['account_id'];
            $field = $reflectMap['account_id'] ?? 'account_id';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['operations_manager'])) {
            $d     = $params['operations_manager'];
            $field = $reflectMap['operations_manager'] ?? 'operations_manager';

            $qb->where($field, new Parameter($d));
        }

        if (isset($params['use_kind'])) {
            $d     = $params['use_kind'];
            $field = $reflectMap['use_kind'] ?? 'use_kind';

            $qb->where($field, new Parameter($d));
        }

    }


    /**
     * @param int $qbMode
     * @param string $dimension
     * @return SelectQuery
     */
    private function getQueryBuilder(int $qbMode = -1, string $dimension = 'day'): \Spiral\Database\Query\SelectQuery
    {
        $db = $this->getConn();

        if ($dimension == 'month') {
            $qb = $db->select()->from(MediaTableConst::DWS_MEDIA_ACCOUNT_MONTH . ' as dws_account');
        }
        else {
            $qb = $db->select()->from(MediaTableConst::DWS_MEDIA_ACCOUNT_DAILY . ' as dws_account');
        }

        if ($qbMode & static::QB_MODE_ACCOUNT) {
            $qb
                ->leftJoin(TableConst::BASE_LIVE_ACCOUNT, 'live_account')
                ->on([
                    'dws_account.account_id' => 'live_account.account_id',
                ]);
        }

        return $qb;
    }


    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $mode
     *
     * @return array
     */
    public function getDimensionWithAccount(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $result = [];
        $db     = $this->getConn();
        $qb     = $db
            ->select()
            ->from('ddc_platform.dwd_media_account_snapshot_audience_analyze as account_audience');

        if ($mode & static::MODE_LIST) {
            $infoQb = clone $qb;

            $infoQb->columns([
                'tday',
                'data_type as data_type',
                'dimension as dimension',
                'num as num',
            ]);

            $result['list'] = $infoQb->fetchAll();
        }

        return $result;
    }

    /**
     * @param array $groupTA
     *
     * @return array|array[]
     */
    public function getDimensionGroupTA(array $groupTA): array
    {
        if (empty($groupTA)) return [];

        $result = ['list' => []];
        $db     = $this->getConn();
        $qb     = null;

        foreach ($groupTA as $foo) {
            [
                'tday'       => $tDay,
                'account_id' => $accountId,
            ] = $foo;

            if (empty($tDay) || empty($accountId)) {
                continue;
            }

            $newQb = $db
                ->select()
                ->from('ddc_platform.dwd_media_account_snapshot_audience_analyze')
                ->where('tday', $tDay)
                ->where('account_id', $accountId)
                ->columns([
                    'tday as tday',
                    'account_id as account_id',
                    'data_type as data_type',
                    'dimension as dimension',
                    'num as num',
                ]);

            if (is_null($qb)) {
                $qb = $newQb;
            }
            else {
                $qb->unionAll($newQb);
            }
        }

        if (!is_null($qb)) {
            $result['list'] = $qb->fetchAll();
        }

        return $result;
    }


}
<?php

namespace app\extension\Support\Helpers;

use app\extension\Support\Collections\Arr;

class RemainCalculators
{
    /**
     * @param array $remainList
     * @param \Closure|null $getDenominatorFn
     * @param int $remainType
     * @param string $targetCol
     * @param string $prefix
     * @return \Closure
     */
    public static function calcNode(
        array    $remainList,
        \Closure $getDenominatorFn = null,
        int      $remainType = 0,
        string   $targetCol = 'remain_user',
        string   $prefix = 'remain_'
    ): \Closure
    {
        $calcFn = static::getRemainShowFn($remainType);

        return function (&$target, &$context, $key) use (
            $remainList, $getDenominatorFn, $calcFn, $prefix, $targetCol
        ) {
            $remainMap = $remainList[$key] ?? [];

            preg_match_all('/' . $prefix . '\d+/', implode(',', array_keys($remainMap)), $keyMatches);
            $keys      = Arr::pull($keyMatches, 0, []);
            $remainMap = array_intersect_key($remainMap, array_flip($keys));

            $context['remain_key_map'] = $remainMap;

            foreach ($remainMap as $k => $remain) {
                $ii = (int)str_replace($prefix, '', $k);

                if ($ii == 1000) {
                    // 当前留存率
                    $denominator = $target[$targetCol] ?? 0;
                }
                else {
                    $denominator = $getDenominatorFn($target, $ii);
                }

                $target[$k] = $calcFn($remain, $denominator);
            }
        };
    }

    /**
     * @param int $remainType
     * @return \Closure
     */
    public static function getRemainShowFn(int $remainType = 0): \Closure
    {
        if ($remainType == 1) {
            return function ($numerator, $denominator) {
                return $numerator;
            };
        }
        elseif ($remainType == 2) {
            return function ($numerator, $denominator) {
                if ($numerator == '-') return '-';

                if (empty($numerator) || empty($denominator)) {
                    $r = '0.00%';
                }
                else {
                    $r = round($numerator / $denominator * 100, 2) . '%';
                }

                return "{$numerator}({$r})";
            };
        }
        else {
            return function ($numerator, $denominator) {
                if ($numerator == '-') return '-';

                if (empty($numerator) || empty($denominator)) return '0.00%';

                return round($numerator / $denominator * 100, 2) . '%';
            };
        }

    }

    public static function remainCombo(
        array $data, \Closure $mergeFn, string $hitDay = 'tday'
    ): array
    {
        $result = [];

        foreach ($data as $foo) {
            $nodeKey  = $mergeFn($foo);
            $dayNode  = $foo['day_type'];
            $loginNum = $foo['login_num'] ?? 0;

            if (empty($result[$nodeKey])) {
                $result[$nodeKey] = ['nodes' => []];
            }

            $chill = &$result[$nodeKey]['nodes'];

            if (!empty($chill[$dayNode])) {
                $chill[$dayNode]['login_num'] += $loginNum;
            }
            else {
                $chill[$dayNode] = [
                    'day_type'  => $dayNode,
                    'login_num' => $loginNum,
                ];
            }
        }

        return $result;
    }


}
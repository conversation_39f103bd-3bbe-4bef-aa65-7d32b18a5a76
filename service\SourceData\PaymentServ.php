<?php

namespace app\service\SourceData;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\TableCollect;
use app\service\BusinessPlatform\OptionServ;
use app\service\General\Helpers\TableConst;
use Php<PERSON>arser\Node\Expr\AssignOp\Plus;
use Spiral\Database\Database;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class PaymentServ
{
    /**
     * 连表模式
     *
     * + QB_MODE_ALL - 正常全部关联
     * + QB_MODE_VIRTUAL_EXT - 订单扩展表关联
     * + QB_MODE_PACKAGE_DETAIL - 包号关联
     * + QB_MODE_BASE_CHANNEL - 渠道关联
     * + QB_MODE_BASE_GAME - 游戏配置关联
     * + QB_MODE_PAY_WAY - 支付渠道关联
     */
    const QB_MODE_ALL            = 127;
    const QB_MODE_VIRTUAL_EXT    = 1;
    const QB_MODE_PACKAGE_DETAIL = 2;
    const QB_MODE_BASE_CHANNEL   = 4;
    const QB_MODE_BASE_GAME      = 8;
    const QB_MODE_PAY_WAY        = 16;
    const QB_MODE_TEST_ACCOUNT   = 32;
    const QB_MODE_TAGGING        = 64;

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int   $mode
     *
     * @return array
     * @throws \Exception
     */
    public function getList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $qb = $this->getPaymentQb();

        $this->whereMatch($qb, $params);

        $totalQb = clone $qb;

        $qb->columns([
            new Fragment('DATE(t_payment.pay_time) as tday'),
            'base_game.cp_game_name as cp_game_id',
            'base_game.sm_game_name as game_id',
            't_payment.package_id as package_id',
            't_payment.game_server_id as game_server_id',
            't_payment.login_account as login_account',
            't_payment.core_account as core_account',
            't_payment.role_id as role_id',
            't_payment.role_name as role_name',
            't_payment.role_rank as role_rank',
            't_payment.role_vip as role_vip',
            't_payment.order_id as order_id',
            't_payment.cp_order_id as cp_order_id',
            't_payment.channel_order_id as channel_order_id',
            't_payment.company as company',
            //            't_payment.payway as payway',
            't_payment.money_type as money_type',
            't_payment.money as money',
            't_payment.game_coin as game_coin',
            't_payment.goods_name as goods_name',
            't_payment.order_time as order_time',
            't_payment.pay_time as pay_time',
            't_payment.pay_result as order_status',
            'base_channel.channel_name as channel_id',
            'base_channel.channel_main_name as channel_main_id',
            't_payment.game_result as game_back_status',
            'base_pay_way.name as payway',
            't_payment.order_type.order_type',
//            new Fragment("IF(PAY_RESULT = 1, IFNULL(payment_ext.real_money, t_payment.money), 0) as pay_amount"),
            new Fragment("IFNULL(payment_ext.real_money, t_payment.money) as pay_amount"),
//            new Fragment("IFNULL(payment_ext.money, t_payment.money) as amount"),
            new Fragment("IF(t_payment.package_id in ('69560001', '69580099','69620001','69640099','69680001','69700099','69740001','69760099','69800001','69820099','69840001','70900001','70920099','71060001','71080099','71100001','71120099'), IFNULL(payment_ext.money, t_payment.money) * 100, IFNULL(payment_ext.money, t_payment.money)) as amount"),
            new Fragment('COALESCE(payment_ext.decuct_coin, 0) as decuct_coin'),
            new Fragment('COALESCE(payment_ext.coupon_money, 0) as coupon_amount'),
            new Fragment("IF(pay_tag.operator_id IS NOT NULL, pay_tag.operator_id, IF(t_payment.order_type != 0 and biz_test.id is not null, 0, '')) as tag_operator_id"), //
            new Fragment("IF(biz_test.dep_id is null,IF(pay_tag_department.level >= 3, pay_tag_department.parent_id, pay_tag_department.id), biz_test.dep_id) as test_department"), // test_department 测试账号归属
            new Fragment("IF(t_payment.order_type != 0 and biz_test.id is not null, 0, pay_tag.tag_mode) as tag_mode"), // 代添加字段
        ]);

        if (!empty($sort)) {
            $qb->orderBy($sort);
        }

        if (!empty($paginate)) {
            ['page' => $page, 'page_size' => $pageSize] = $paginate;
            $qb->limit($pageSize)->offset(($page - 1) * $pageSize);
        }

        return [
            'list'  => $qb->fetchAll(),
            'total' => $totalQb->count(),
        ];
    }


    /**
     * @param int $mode
     *
     * @return SelectQuery
     */
    protected function getPaymentQb(int $mode = -1): SelectQuery
    {
        $db        = $this->getConn();
        $qb        = $db->select()->from(TableConst::ORIGIN_SDK_USER_PAYMENT . ' as t_payment');
        $paramMode = $mode & $this->allMode();

        if ($paramMode & static::QB_MODE_VIRTUAL_EXT) {
            $qb
                ->leftJoin(TableConst::DDC_SDK_USER_PAYMENT_VIRTUAL, 'payment_ext')
                ->on(['t_payment.order_id' => 'payment_ext.order_id']);
        }

        if ($paramMode & static::QB_MODE_PACKAGE_DETAIL) {
            $qb
                ->leftJoin(TableConst::CONF_PACKAGE_DETAIL, 'base_package')
                ->on(['t_payment.package_id' => 'base_package.package_id']);
        }

        if ($paramMode & (static::QB_MODE_PACKAGE_DETAIL | static::QB_MODE_BASE_CHANNEL)) {
            $qb
                ->leftJoin(TableConst::CONF_BASE_CHANNEL, 'base_channel')
                ->on(['base_package.channel_id' => 'base_channel.channel_id']);
        }

        if ($paramMode & (static::QB_MODE_PACKAGE_DETAIL | static::QB_MODE_BASE_GAME)) {
            $qb
                ->leftJoin(TableConst::CONF_GAME_BASE, 'base_game')
                ->on(['base_package.game_id' => 'base_game.game_id']);
        }

        if ($paramMode & static::QB_MODE_PAY_WAY) {
            $qb
                ->leftJoin(TableConst::CONF_PAY_WAY, 'base_pay_way')
                ->on(['t_payment.payway' => 'base_pay_way.code']);
        }

        if ($paramMode & static::QB_MODE_TEST_ACCOUNT) {
            $qb
                ->leftJoin(TableConst::BIZ_TEST_ACCOUNT, 'biz_test')
                ->on(['t_payment.core_account' => 'biz_test.core_account']);
        }

        if ($paramMode & static::QB_MODE_TAGGING) {
//            $qb
//                ->leftJoin(TableConst::PAYMENT_TAGGING, 'pay_tag')
//                ->on(['t_payment.order_id' => 'pay_tag.order_id'])
//                ->leftJoin(TableCollect::OA_PEOPLE_CONF, 'pay_tag_user')
//                ->on(['pay_tag.operator_id' => 'pay_tag_user.id'])
//                ->leftJoin(TableCollect::OA_DEPARTMENT_CONF, 'pay_tag_department')
//                ->on(['pay_tag_user.department_id' => 'pay_tag_department.id']);

            $subPayTag = $this
                ->getConn()
                ->select()
                ->from(TableConst::PAYMENT_TAGGING)
                ->where('state', 1)
                ->groupBy('order_id')
                ->columns([
                    'order_id as order_id',
                    'operator_id as operator_id',
                    'tag_mode as tag_mode',
                    'state as state',
                ]);

            $qb
                ->leftJoin(new Fragment('(' . $subPayTag->__toString() . ')'), 'pay_tag')
                ->on(['t_payment.order_id' => 'pay_tag.order_id'])
                ->leftJoin(TableCollect::OA_PEOPLE_CONF, 'pay_tag_user')
                ->on(['pay_tag.operator_id' => 'pay_tag_user.id'])
                ->leftJoin(TableCollect::OA_DEPARTMENT_CONF, 'pay_tag_department')
                ->on(['pay_tag_user.department_id' => 'pay_tag_department.id']);
        }

        return $qb;
    }

    /**
     * @param SelectQuery $qb
     * @param array       $params
     *
     * @return void
     * @throws \Exception
     */
    protected function whereMatch(SelectQuery &$qb, array $params)
    {
        // 时间查询
        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            $rangeTime = [
                $params['range_date_start'], $params['range_date_end'],
            ];

            sort($rangeTime);

            $startTime = (new \DateTime($rangeTime[0]))->format('Y-m-d') . ' 00:00:00';
            $endTime   = (new \DateTime($rangeTime[1]))->format('Y-m-d') . ' 23:59:59';

            $qb->where('t_payment.pay_time', 'between', $startTime, $endTime);
        }

        $paymentMapField = [
            'order_id'       => 't_payment.order_id',  // 手盟订单
            'package_id'     => 't_payment.package_id', // 包号
            'core_account'   => 't_payment.core_account', // 核心账号
            'game_server_id' => 't_payment.game_server_id', // 区服
            'role_id'        => 't_payment.role_id', // 角色id
            'cp_game_id'     => 'base_game.cp_game_id', // 游戏原名
            'game_id'        => 'base_game.game_id', // 游戏统计名
            'payway'         => 't_payment.payway', // 支付渠道
        ];

        foreach ($paymentMapField as $f => $foo) {
            if (empty($params[$f])) continue;

            $data = $this->convertArray($params[$f]);
            $qb->where($foo, new Parameter($data));
        }

        // 订单类型搜索
        if (!empty($params['order_type'])) {
            $orderType = $this->convertArray($params['order_type']);

            $qb->where(static function (SelectQuery $select) use ($orderType) {
                $businessServ = new OptionServ();

                foreach ($orderType as $type) {
                    if (1 == $type) {
                        $select->orWhere('t_payment.order_type', new Parameter([
                            '1', '339', '596', '342', '343', '340', '344', '345', '346', '347',
                        ]));
                    }
                    else {
                        $select->orWhere('t_payment.order_type', $type);
                    }
                }
            });
        }

        if (isset($params['order_status'])) {
            $payResult = $this->convertArray($params['order_status']);
            $qb->where('t_payment.pay_result', new Parameter($payResult));
        }

        if (isset($params['game_back_status'])) {
            // 游戏回调失败 game_result = 0
            $gameResult = $this->convertArray($params['game_back_status']);
            $qb->where('t_payment.game_result', new Parameter($gameResult));
        }

        // 支付组合
        if (!empty($params['pay_combo'])) {
            $payCombo = $this->convertArray($params['pay_combo']);

            $qb->where(static function (SelectQuery $select) use ($payCombo) {
                foreach ($payCombo as $pay) {
                    if (1 == $pay) {
                        $select
                            ->orWhere(new Fragment('IFNULL(payment_ext.real_money, t_payment.money) > 0'));
                    }
                    elseif (2 == $pay) {
                        $select->orWhere('payment_ext.decuct_coin', '>', 0);
                    }
                    elseif (3 == $pay) {
                        $select->orWhere('payment_ext.coupon_money', '>', 0);
                    }
                }
            });
        }

        if (!empty($params['login_account'])) {
            $loginAccount = $this->convertArray($params['login_account']);
            $coreList     = (new OptionServ())->getAccountInfo(['login_account' => new Parameter($loginAccount)], ['login_account', 'core_user']);

            if (empty($coreList)) {
                throw new \UnexpectedValueException('没有该登录账号信息');
            }

            $coreAccount = array_column($coreList, 'core_user');

            $qb->where('t_payment.core_account', new Parameter($coreAccount));
        }

        if (!empty($params['test_department'])) {
            $testDepartment = $this->convertArray($params['test_department']);
            $qb->where(static function (SelectQuery $select) use ($testDepartment) {
                $select
                    ->orWhere('biz_test.dep_id', new Parameter($testDepartment))
                    ->orWhere('pay_tag_department.id', new Parameter($testDepartment))
                    ->orWhere('pay_tag_department.parent_id', new Parameter($testDepartment));
            });
        }

        if (isset($params['tagging_operator'])) {
            $operator = $this->convertArray($params['tagging_operator']);

            $qb->where(static function (SelectQuery $select) use ($operator) {
                $select->where('pay_tag.operator_id', new Parameter($operator));
                if (in_array(0, $operator)) {
                    $select->orWhere(static function (SelectQuery $subSelect) {
                        $subSelect
                            ->where('t_payment.order_type', '!=', 0)
                            ->where(new Fragment('biz_test.id is not null'));
                    });
                }
            });
        }

        if (isset($params['tag_mode'])) {
            $tagMode = $this->convertArray($params['tag_mode']);

            $qb->where(static function (SelectQuery $select) use ($tagMode) {
                $select->where('pay_tag.tag_mode', new Parameter($tagMode));

                if (in_array(0, $tagMode)) {
                    $select->orWhere(static function (SelectQuery $subSelect) {
                        $subSelect
                            ->where('t_payment.order_type', '!=', 0)
                            ->where(new Fragment('biz_test.id is not null'));
                    });
                }
            });

        }

    }

    /**
     * @param $data
     *
     * @return array
     */
    protected function convertArray($data): array
    {
        if (is_string($data) && str_contains($data, ',')) {
            $data = \explode(',', $data);
        }
        return Arr::wrap($data);
    }


    /**
     * @return int
     */
    private function allMode(): int
    {
        return static::QB_MODE_ALL
            | static::QB_MODE_VIRTUAL_EXT
            | static::QB_MODE_PACKAGE_DETAIL
            | static::QB_MODE_BASE_CHANNEL
            | static::QB_MODE_BASE_GAME
            | static::QB_MODE_PAY_WAY
            | static::QB_MODE_TEST_ACCOUNT
            | static::QB_MODE_TAGGING;
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('origin_platform');
    }


    /**
     * @throws \Throwable
     */
    public function taggingWithOrderIds($orderIds, $orderType, $operatorId)
    {
        $db = FakeDB::connection('ddc_platform');

        return $db->transaction(static function (Database $ndb) use ($orderIds, $orderType, $operatorId) {
            // 根据订单号
            $updateOrders = $ndb
                ->table(TableConst::ORIGIN_SDK_USER_PAYMENT)
                ->select()
                ->columns(['order_id'])
                ->where('order_id', new Parameter($orderIds))
                ->fetchAll();
            // 更新sdk_user_payment
            $ndb
                ->table(TableConst::ORIGIN_SDK_USER_PAYMENT)
                ->update([
                    'order_type' => $orderType,
                ])
                ->where('order_id', new Parameter($orderIds))
                ->run();
            // 更新业务库订单状态
            FakeDB::connection('py_platform')
                ->table(TableConst::BIZ_PY_PAYMENT)
                ->update([
                    'v2_id' => $orderType,
                ])
                ->where('order_id', new Parameter($orderIds))
                ->run();
            // 锁定已存在的记录
            $needUpdateTagLogs = $ndb
                ->table(TableConst::PAYMENT_TAGGING)
                ->select()
                ->where('order_id', new Parameter($orderIds))
                ->where('state', 1)
                ->columns([
                    'id as id',
                    'order_id as order_id',
                    'state as state',
                ])
//                ->forUpdate()
                ->fetchAll();

            $notValidIds = array_column($needUpdateTagLogs, 'id');

            if (!empty($notValidIds)) {
                // 对应订单ID已经存在state=1的情况时更新为无效
                $ndb
                    ->table(TableConst::PAYMENT_TAGGING)
                    ->update(['state' => 0])
                    ->where('id', new Parameter($notValidIds))
                    ->run();
            }

            // 对应 order_id 插入一条记录
            foreach ($updateOrders as $item) {
                $order = $item['order_id'] ?? '';

                if (empty($order)) continue;

                $ndb->table(TableConst::PAYMENT_TAGGING)->insertOne([
                    'order_id'    => $order,
                    'order_type'  => $orderType,
                    'operator_id' => $operatorId,
                    'tag_mode'    => 1,
                    'state'       => 1,
                ]);
            }

            return true;
        });
    }

}
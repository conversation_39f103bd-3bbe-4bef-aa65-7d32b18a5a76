<?php

namespace app\service\AdvertiserData;

use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Components\MatchParams\AdCreativeMatcher;
use app\service\AdvertiserData\Components\MatchParams\AdPlanMatcher;
use app\service\AdvertiserData\Scheme\AdCreativeLtvScheme;
use app\service\AdvertiserData\Traits\AdServiceable;
use app\service\AdvertiserData\Traits\Converter;
use app\service\General\BizTagsServ;
use app\util\Common;
use Aura\SqlQuery\Common\SelectInterface;

/**
 * @CreativeLtvIndex LTV查询服务
 */
class CreativeLtvIndex
{
    use Converter, AdServiceable;

    /**
     * 获取实时LTV值
     *
     * @return array|false
     */
    public function fetchRealTimeByCreative($params)
    {
        return $this->fetchAllByCreative($params, 60);
    }

    /**
     * @param $params
     *
     * @return array|false
     */
    public function fetchRealTimeByPlan($params)
    {
        return $this->fetchAllByPlan($params, 60);
    }

    /**
     * @param          $params
     * @param int|null $maxDayType
     *
     * @return array|false
     * @deprecated
     */
    public function fetchAllByCreative($params, int $maxDayType = null)
    {
        // 需要补全暂时以最细维度查询
        $groups   = ['tday', 'package_id', 'channel_id', 'creative_id', 'day_type'];
        $powerSql = \Plus::$service->admin->getAdminPowerSql();

        $scheme = AdCreativeLtvScheme::NewOne()->select();
        $scheme
            ->joinPowerSql($powerSql)
            ->joinCreativeBase()
            ->join($this->joinAccount('t_base'))
            ->joinAdpOauth('left', 't_base')
            ->join((new JoinClause('left', 'dataspy.admin_user', 't_admin'))->on('t_base.USER_ID', '=', 't_admin.ID'), 'left')
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params)));

        $matcher = new AdCreativeMatcher($scheme->fieldReflect());
        $matcher->setParams($params);
        $matcher->execute($scheme);

        if (!empty($maxDayType)) {
            $scheme->scope($this->matchLtvParams(['max_day_type' => $maxDayType]));
        }
        Common::dumpSql((clone $scheme)->toSql());
        return $this->fetchAll($scheme->toSql());
    }

    /**
     * 不需要补全的查询方式(直接汇总)
     *
     * @param $params
     * @param $groups
     *
     * @return array|false
     */
    public function ltvInfoByCreative($params, $groups)
    {
        $groups[] = 'day_type';
        $groups   = array_unique($groups);
        $powerSql = \Plus::$service->admin->getAdminPowerSql();

        $scheme = AdCreativeLtvScheme::NewOne()->select();
        $scheme
            ->joinPowerSql($powerSql)
            ->joinCreativeBase()
            ->join($this->getAccountJoins('t_base'))
            ->joinAdpOauth('left', 't_base')
            ->join((new JoinClause('left', 'dataspy.admin_user', 't_admin'))->on('t_base.USER_ID', '=', 't_admin.ID'), 'left')
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params)));

        $matcher = new AdCreativeMatcher($scheme->fieldReflect());
        $matcher->setParams($params);
        $matcher->execute($scheme);

        if (isset($params['max_day_type'])) {
            $scheme->scope($this->matchLtvParams(['max_day_type' => $params['max_day_type']]));
        }
        Common::dumpSql((clone $scheme)->toSql());
        return $this->fetchAll($scheme->toSql());
    }

    /**
     * @param          $params
     * @param int|null $maxDayType
     *
     * @return array|false
     */
    public function fetchAllByPlan($params, ?int $maxDayType)
    {
        $groups = ['tday', 'package_id', 'channel_id', 'campaign_id', 'plan_id', 'creative_id', 'day_type'];

        $scheme = $this->getLtvStrandedSchemeByPlan();
        $scheme
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params, true)));

        $matcher = new AdPlanMatcher($scheme->fieldReflect('plan'));
        $matcher->setParams($params);
        $matcher->execute($scheme);

        if (!empty($maxDayType)) {
            $scheme->scope($this->matchLtvParams(['max_day_type' => $maxDayType]));
        }
        Common::dumpSql((clone $scheme)->toSql());
        return $this->fetchAll($scheme->toSql());
    }

    /**
     * @param array $params
     * @param array $groups
     *
     * @return array|false
     * @throws \Exception
     */
    public function ltvInfoByPlan(array $params, array $groups = [])
    {
        $scheme = $this->getLtvStrandedSchemeByPlan();

        if (!in_array('day_type', $groups)) {
            $groups[] = 'day_type';
        }

        $scheme
            ->scope($this->buildGroup($groups))
            ->scope($this->buildColumn($this->getColumns($params, true)));

        $matcher = new AdPlanMatcher($scheme->fieldReflect('plan'));
        $matcher->setParams($params);
        $matcher->execute($scheme);

        Common::dumpSql((clone $scheme)->toSql());

        return $this->fetchAll($scheme->toSql());
    }

    /**
     * @return AdCreativeLtvScheme
     */
    public function getLtvStrandedSchemeByPlan(): AdCreativeLtvScheme
    {
        $powerSql = \Plus::$service->admin->getAdminPowerSql();

        $scheme = AdCreativeLtvScheme::NewOne()->select();

        $scheme
            ->joinPowerSql($powerSql)
            ->joinPlanBase('left')
            ->join($this->joinAccount('t_base'))
            ->join((new JoinClause('left', 'dataspy.admin_user', 't_admin'))->on('t_base.USER_ID', '=', 't_admin.ID'), 'left')
            ->joinAdpOauth('left', 't_base')
            ->join((new JoinClause('left', 'base_conf_platform.tb_base_channel_conf', 'base_channel'))
                ->on('t_base.channel_id', '=', 'base_channel.channel_id'))
            ->join((new JoinClause('left', 'adp_platform.tb_adp_campaign', 'base_campaign'))
                ->on('base_campaign.channel_id', '=', 't_base.main_channel_id')
                ->on('base_campaign.campaign_id', '=', 't_base.campaign_id'));

        return $scheme;
    }


    /**
     * @param array $params
     * @param bool $isPlan
     * @param bool $isTop
     * @param bool $isTotal
     *
     * @return array
     */
    protected function getColumns(array $params = [], bool $isPlan = false, bool $isTop = false, bool $isTotal = false): array
    {
        $mainAlias = AdCreativeLtvScheme::MAIN_TABLE['alias'];

        $planChannels       = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $planChannelsString = implode(',', $planChannels);
        $channelString      = sprintf(
            "IF(POWER.channel_id NOT IN (%s), POWER.channel_id, IF(t_base.channel_id != 0,IF(t_base.channel_id=1013,4,t_base.channel_id), POWER.channel_id)) AS promotion_channel_id",
            $planChannelsString
        );
        $channelMainString  = sprintf(
            "COALESCE(IF(POWER.channel_id IN (%s), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id",
            $planChannelsString
        );


        /**
         * 固定详情数据
         */
        $fixInfoIndex = [
            'tday'                 => ['source' => $mainAlias],
            'cp_game_id'           => ['source' => $mainAlias],
            'game_id'              => ['source' => $mainAlias],
            'app_show_id'          => ['source' => 'POWER'],
            //            'channel_main_id'      => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (6568, 6822, 5329), IF(base_channel.channel_main_id != 0, base_channel.channel_main_id, power.channel_main_id), power.channel_main_id),0) AS channel_main_id'],
            'channel_main_id'      => ['info', 'raw' => $channelMainString],
            //            'promotion_channel_id' => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id NOT IN (6568, 6822, 5329), POWER.channel_id,IF(t_base.channel_id != 0, t_base.channel_id,POWER.channel_id)), 0) AS promotion_channel_id'],
            'promotion_channel_id' => ['info', 'raw' => $channelString],
            'platform_id'          => ['source' => 'POWER'],
            'package_id'           => ['source' => $mainAlias],
            'campaign_id'          => ['source' => $mainAlias],
            'plan_id'              => ['source' => $mainAlias],
            'creative_id'          => ['source' => $mainAlias],
            'promotion_id'         => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            'department_id'        => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),t_admin.department_id, POWER.ad_department_id),0) as department_id'],
            'user_id'              => ['info', 'raw' => 'COALESCE(IF(POWER.channel_id IN (' . $planChannelsString . '),t_base.USER_ID, POWER.AD_USER_ID), 0) AS user_id'],
            'ad_account'           => ['source' => 'base_account'],
            'account_id'           => ['source' => 'base_account'],
            'account_name'         => ['source' => 'adp_oauth', 'source_field' => 'ADVERTISER_NAME'],
            'day_type'             => ['source' => $mainAlias],
            'last_update_time'     => ['source' => $mainAlias, 'source_field' => 'update_time', 'aggregate' => 'max'],
            'dim_user_os'          => ['info', 'raw' => "case when USER_OS = JSON_ARRAY('ANDROID') then 'ANDROID' when USER_OS = JSON_ARRAY('IOS') then 'IOS' when JSON_CONTAINS(USER_OS, '[\"ANDROID\",\"IOS\"]', '$') = 1 then '混投' else '混投' end as dim_user_os"]
        ];

        $calculatedIndex = [
            'money'     => ['source' => $mainAlias, 'aggregate' => 'sum'], // 充值金额（不包含虚拟币)
            'money_all' => ['source' => $mainAlias, 'aggregate' => 'sum'], // 充值金额（包含虚拟币）
        ];


        $result  = [];
        $collect = collect();

        if (!$isTotal) {
            $collect = $collect->merge($fixInfoIndex);
        }
        $collect = $collect->merge($calculatedIndex);

        $collect->each(function (&$item, $key) use (&$result, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];
                $format    = "{$aggregate}(%s)";
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }

            $field .= $item['source_field'] ?? $key;

            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }

    /**
     * 建立查询字段
     *
     * @param array $columns
     *
     * @return \Closure
     */
    protected function buildColumn(array $columns = ['*']): \Closure
    {
        return function (&$query) use ($columns) {
            if (!$query instanceof SelectInterface) return;
            $query->cols($columns);
        };
    }

    /**
     *
     * @param $params
     *
     * @return \Closure
     */
    protected function matchLtvParams($params): \Closure
    {
        return function (&$query) use ($params) {
            $mainAlias = AdCreativeLtvScheme::MAIN_TABLE['alias'];

            if (!$query instanceof SelectInterface) return;

            if (!empty($maxDayTYpe = $params['max_day_type'])) {
                $query->where("{$mainAlias}.day_type <= {$maxDayTYpe}");
            }
        };
    }

    /**
     * @param $groups
     *
     * @return \Closure
     */
    protected function buildGroup($groups): \Closure
    {
        $groups = $this->changeGroups($groups);

        return function (&$query) use ($groups) {
            if (!$query instanceof SelectInterface) return;

            $query->groupBy($groups);
        };
    }

    /**
     * 组合映射
     *
     * @return string[]
     */
    private function groupsReflect(): array
    {
        $mainAlias = AdCreativeLtvScheme::MAIN_TABLE['alias'];

        return [
            'tday'        => $mainAlias . '.tday',
            'cp_game_id'  => $mainAlias . '.cp_game_id',
            'game_id'     => $mainAlias . '.game_id',
            'package_id'  => $mainAlias . '.package_id',
            'channel_id'  => $mainAlias . '.channel_id',
            //            'channel_main_id' => 'POWER.channel_main_id',
            'creative_id' => $mainAlias . '.creative_id',
            'day_type'    => $mainAlias . '.day_type',
        ];
    }

    /**
     * @param string $sql
     *
     * @return array|false
     */
    private function fetchAll(string $sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * @param $params
     *
     * @return array
     * @todo 整合
     */
    public function simpleList($params): array
    {
        $today       = date('Y-m-d');
        $table       = AdCreativeLtvScheme::MAIN_TABLE['table'];
        $wheres      = [];
        $whereString = '';

        [$dateStart, $dateEnd] = [
            $params['range_date_start'], $params['range_date_end'],
        ];

        $wheres[] = "tday between '{$dateStart}' and '{$dateEnd}'";
        $wheres[] = "pay_date < '{$today}'";

        if (!empty($params['cp_game_id'])) {
            $cpGameId = $params['cp_game_id'];
            if (str_contains($cpGameId, ',')) {
                $wheres[] = "cp_game_id IN ({$cpGameId})";
            }
            else {
                $wheres[] = "cp_game_id = {$cpGameId}";
            }
        }

        if (($params['max_day_type'] ?? 0) > 0) {
            $maxDayType = $params['max_day_type'];
            $wheres[]   = "(day_type <= {$maxDayType} or day_type = 1000)";
        }

        if (!empty($wheres)) {
            $whereString = ' WHERE ' . implode(' and ', $wheres);
        }

        $sql = "
        SELECT 
            tday, day_type, cp_game_id, sum(money_all) as money_all, sum(money) as money 
        FROM ddc_platform.dws_creative_ad_ltv_daily {$whereString} group by tday, day_type
        ";

        $summarySql = "
        SELECT 
             day_type, cp_game_id, sum(money_all) as money_all, sum(money) as money 
        FROM ddc_platform.dws_creative_ad_ltv_daily {$whereString} group by day_type
        ";

        $list       = \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $summaryRow = \Plus::$app->ddc_platform->query($summarySql)->fetchAll(\PDO::FETCH_ASSOC);

        return [
            'list'    => $list,
            'summary' => $summaryRow,
        ];
    }

}
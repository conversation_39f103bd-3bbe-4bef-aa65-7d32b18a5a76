<?php

/**
 * 星图-微信小程序
 * phpcs:disable
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class XingtuXcx extends AdBaseInterface
{


    /**
     * 上报激活
     * @param array $info
     * @param $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'ACTIVE');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'PAY');
    }


    /**
     * @param $info
     * @param string $type
     */
    protected function uploadData($info, $type = '', $mark = 'xingtu_xcx')
    {

        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = $mark;

        $aValue = 0;
        switch ($type) {
            case 'ACTIVE':
                $aValue = 0;
                break;
            case 'REG':
                $aValue = 1;
                break;
            case 'PAY':
                $aValue = 2;
                break;
        }

        $callbackUrl = $info['CALLBACK_URL'] ?? '';
        if (empty($callbackUrl)) {
            if (!empty($info['paid_report_log'])) {
                $info['paid_report_log']['reported_status']    = -1;
                $info['paid_report_log']['reported_money']     = empty($info['paid_report_log']['reported_money']) ? $info['MONEY'] : $info['paid_report_log']['reported_money'];
                $info['paid_report_log']['no_reported_origin'] = '缺少必要上报参数{callback_url}';
                \Plus::$app->log->info(json_encode($info).'缺少必要上报参数callback_url', [], self::LOG_DIR);
                $this->logPaidToDoris($info, $logInfo, '');
                return;
            }
        }

        $callbackUrl .= "&event_type=" . $aValue;
        if ($type == 'PAY') {
            $props        = '{"pay_amount":"' . intval($info['MONEY'] * 100) . '"}';
            $callbackUrl .= "&props=" . urlencode($props);
        }
        $logInfo['request'] = json_encode(['url' => $callbackUrl]);

        $http                = new Http($callbackUrl);
        $res                 = $http->get();
        $logInfo['response'] = $res;

        // 记录上报结果
        $resArr = json_decode($res, true);

        if (isset($resArr['code']) && $resArr['code'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
            if (!empty($info['paid_report_log'])) {
                $info['paid_report_log']['no_reported_origin'] = '接口返回编码异常';
            }
        }

        $this->log($info, $logInfo, $res, $callbackUrl);
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

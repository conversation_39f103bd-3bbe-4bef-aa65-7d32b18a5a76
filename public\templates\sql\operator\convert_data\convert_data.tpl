select
    tday,
    cp_game_id as cp_game_id,
    game_id as game_id ,
    android_firstlogin_user as android_firstlogin_user,
    ios_firstlogin_user as ios_firstlogin_user,
    android_ad_new_user as android_ad_new_user,
    ios_ad_new_user as ios_ad_new_user,
    android_source_transaction_records as android_source_transaction_records,
    android_covert_transaction_records as android_covert_transaction_records,
    ROUND(android_covert_transaction_records / (android_source_transaction_records + android_covert_transaction_records) * 100, 2) as android_convert_percentage,
    android_new_user_transaction_records_month as android_new_user_transaction_records_month,
    ROUND(android_source_transaction_records / (android_source_transaction_records + ios_source_transaction_records) * 100, 2) as android_source_records_percentage,
    ios_source_transaction_records as ios_source_transaction_records,
    ios_convert_transaction_records as ios_convert_transaction_records,
    ROUND(ios_convert_transaction_records / (ios_source_transaction_records + ios_convert_transaction_records) * 100, 2) as ios_convert_percentage,
    ios_new_user_transaction_records_month as ios_new_user_transaction_records_month,
    ROUND(ios_source_transaction_records / (android_source_transaction_records + ios_source_transaction_records) * 100, 2) as ios_source_records_percentage,
    android_convert_new_user as android_convert_new_user,
    android_convert_paid_user as android_convert_paid_user,
    android_convert_active_user as android_convert_active_user,
    CONCAT(ROUND((ios_convert_transaction_records + ios_source_transaction_records)/(ios_convert_transaction_records+ ios_source_transaction_records + android_covert_transaction_records + android_source_transaction_records) * 100 , 2),'%') as ios_records_percentage,
    ios_convert_new_user as ios_convert_new_user,
    ios_convert_paid_user as ios_convert_paid_user,
    ios_convert_active_user as ios_convert_active_user,
    android_active_paid_l1 as android_active_paid_l1,
    android_active_paid_l2 as android_active_paid_l2,
    android_active_paid_l3 as android_active_paid_l3,
    android_active_paid_l4 as android_active_paid_l4,
    ios_active_paid_l1 as ios_active_paid_l1,
    ios_active_paid_l2 as ios_active_paid_l2,
    ios_active_paid_l3 as ios_active_paid_l3,
    ios_active_paid_l4 as ios_active_paid_l4,
    related_id as related_id,
    create_at as create_at,
    update_at as update_at
from dws_convert_platform_dashboard_daily

{include file="sql/operator/convert_data/convert_data_match.tpl"}

{* 分组汇总 *}
{if $group_by|isset}
    group by {$group_by}
{/if}

{* 排序 *}
{if $sort|isset}
    order by
    {$is_first=1}
    {foreach $sort as $k => $foo}
        {if $is_first eq 1}
            {$k} {$foo}
            {$is_first=0}
        {else}
            , {$k} {$foo}
        {/if}
    {/foreach}
{/if}

{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}
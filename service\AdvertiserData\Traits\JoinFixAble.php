<?php /** @noinspection PhpMissingReturnTypeInspection */

namespace app\service\AdvertiserData\Traits;

use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Scheme\RealtimeCreativeScheme;

trait JoinFixAble
{
    /**
     * 添加权限查询关联
     *
     * @param string $sql
     *
     * @return static
     */
    public function joinPowerSql(string $sql, string $mainTable = '')
    {
        $mainTable = empty($mainTable) ? self::MAIN_TABLE['alias'] : $mainTable;

        if (str_contains($sql, 'as')) {
            $powerSql = \trim(str_replace('POWER', '', $sql));
            $powerSql = \trim($powerSql, '()');
        }
        else {
            $powerSql = $sql;
        }

        $alias = 'POWER';

        $power =
            (new JoinClause('inner', $powerSql, $alias))
                ->on('POWER.package_id', '=', $mainTable . '.package_id');

        array_unshift($this->joinTables, $power);

        return $this;
    }

    /**
     * @param             $table
     * @param string|null $join
     * @param string|null $cond
     *
     * @return $this
     */
    public function join($table, string $join = 'inner', string $cond = '')
    {
        if ($table instanceof JoinClause) {
            $this->joinTables[] = $table;
        }
        else {
            $this->joinTables[] = compact('join', 'table', 'cond');
        }

        return $this;
    }

    /**
     * 拼接关联语句
     *
     * @param $joinTables
     *
     * @return void
     */
    public function completeJoins($joinTables)
    {
        foreach ($joinTables as $table) {
            if ($table instanceof JoinClause) {
                $this->completeJoinClause($table);
            }
            else {
                ['join' => $method, 'table' => $t, 'cond' => $cond] = $table;

                $this->query->join($method, $table, $cond);
            }
        }
    }

    /**
     * @param JoinClause $table
     *
     * @return $this
     */
    public function completeJoinClause(JoinClause $table)
    {
        $table->isSubQuery()
            ? $this->query->joinSubSelect(
            $table->getMethod(), $table->getTable(false), $table->getAlias(), $table->getCond()
        ) : $this->query->join($table->getMethod(), $table->getTable(), $table->getCond());

        return $this;
    }

    /**
     * 月度数据过滤没到日期的实时数据
     *
     * @param array $infoCol sql对应select的字段数据
     * @param string $mainTable 主表别名 用于left join对应日报表 t_base 主表， 日报表则t_base1一天前的数据  t_base2两天前的数据
     * @param string $fixedTable 副表别名 同上
     * @return void|static
     */
    public function joinMonthDataFilterDailyData($infoCol = [], $mainTable = '', $fixedTable = '')
    {
        // 通过 ColumChanger::ltvRoiLoginMonth 判断是否拼接日报表相减多余日期数据

        $mainTable = $mainTable ? $mainTable : self::MAIN_TABLE['alias'];
        $fixedTable = $fixedTable ? $fixedTable : self::FIXED_TABLE['alias'];

        // sql字段字符串
        $column = implode(',', $infoCol);


        for ($i=1; $i <= 2; $i++) {
            $day = $i -1;
            $tday = date('Y-m-d', strtotime("-{$day} day"));

            // 判断是否有查询1天、2天前的数据（主表、一般为消耗数据）
            if (strstr($column, "{$mainTable}{$i}")) {
                // 主表月报表对应日报表
                $joinTable = self::MAIN_TABLE['daily_table'];
                $leftJoinTable =
                    (new JoinClause('left', "{$joinTable}", "{$mainTable}{$i}"))
                        ->on("{$mainTable}{$i}.package_id = {$mainTable}.package_id AND {$mainTable}{$i}.tday = '{$tday}'");
                array_unshift($this->joinTables, $leftJoinTable);
            }

            // 判断是否有查询1天、2天前的数据（副表、一般为付费数据）
            if (strstr($column, "{$fixedTable}{$i}")) {
                // 副表月报表对应日报表
                $joinTable = self::FIXED_TABLE['daily_table'];
                $leftJoinTable =
                    (new JoinClause('left', "{$joinTable}", "{$fixedTable}{$i}"))
                        ->on("{$fixedTable}{$i}.package_id = {$mainTable}.package_id AND {$fixedTable}{$i}.tday = '{$tday}'");
                array_unshift($this->joinTables, $leftJoinTable);
            }
        }

        return $this;
    }
}
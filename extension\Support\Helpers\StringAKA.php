<?php

namespace app\extension\Support\Helpers;

class StringAKA
{
    /**
     * @param string $string
     * @param bool   $capitalizeFirstCharacter
     * @return string
     */
    public static function underscoreToCamelCase(string $string, bool $capitalizeFirstCharacter = false): string
    {
        $str = str_replace(' ', '', ucwords(str_replace('_', ' ', $string)));

        if (!$capitalizeFirstCharacter) {
            $str[0] = strtolower($str[0]);
        }

        return (string)$str;
    }

    /**
     * 判断字符串是否含中文
     *
     * @param $str
     * @return bool
     */
    public static function hasChinese($str): bool
    {
        return preg_match('/[\x{4e00}-\x{9fa5}]/u', $str) > 0;
    }

    /**
     * @param string $str
     * @param string $split
     * @return string
     */
    public static function toUnderscore(string $str, string $split = '_'): string
    {
        return preg_replace('/(?<=[a-zA-Z])(?=\d)|(?<=\d)(?=[a-zA-Z])/u', $split, strtolower($str));
    }
}

<?php

namespace app\service\AdvertiserData\Components\Helpers;


use app\extension\Constant\AD\AdEnum;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\Mather;

class ColumnManager
{
    /**
     * @var array 广告创意层汇总关联映射
     */
    const RELATE_AD_CREATIVE = [
        'creative_id'          => ['plan_id', 'creative_name'],
        'plan_id'              => ['campaign_id', 'plan_name'],
        'campaign_id'          => [
            'promotion_channel_id', 'account_id', 'ad_account', 'account_name', 'dim_user_os', 'user_id', 'campaign_name',
        ],
        'package_id'           => [
            'game_id', 'cp_game_id', 'platform_id', 'app_show_id', 'promotion_id',
        ],
        'channel_id'           => ['channel_main_id'],
        'channel_main_id'      => [],
        'game_id'              => ['cp_game_id'],
        'user_id'              => ['department_id'],
        'platform_id'          => [],
        'account_id'           => ['ad_account', 'account_name'],
        'ad_account'           => ['account_id'],
        'account_name'         => [],
        'app_show_id'          => [],
        'promotion_channel_id' => ['channel_main_id'],
        'promotion_id'         => [],
        'cp_game_id'           => [],
        'tday'                 => [],
        'dim_user_os'          => [],
    ];

    /**
     * @var array 广告计划层汇总关联映射
     */
    const RELATE_AD_PLAN = [
        'plan_id'              => ['campaign_id','plan_name'],
        'campaign_id'          => [
            'promotion_channel_id', 'account_id', 'ad_account', 'account_name', 'dim_user_os', 'user_id','campaign_name'
        ],
        'package_id'           => [
            'game_id', 'cp_game_id', 'platform_id', 'app_show_id', 'promotion_id',
        ],
        'channel_id'           => ['channel_main_id'],
        'channel_main_id'      => [],
        'game_id'              => ['cp_game_id'],
        'user_id'              => ['department_id'],
        'platform_id'          => [],
        'account_id'           => ['ad_account', 'account_name'],
        'ad_account'           => ['account_id'],
        'account_name'         => [],
        'app_show_id'          => [],
        'promotion_channel_id' => ['channel_main_id'],
        'promotion_id'         => [],
        'cp_game_id'           => [],
        'tday'                 => [],
        'dim_user_os'          => [],
    ];

    /**
     * @var array 广告组层汇总关联映射
     */
    const RELATE_AD_CAMPAIGN = [
        'campaign_id'          => [
            'promotion_channel_id', 'account_id', 'ad_account', 'account_name', 'dim_user_os', 'user_id','campaign_name'
        ],
        'promotion_channel_id' => ['channel_main_id'],
        'channel_id'           => ['channel_main_id'],
        'channel_main_id'      => [],
        'package_id'           => [
            'game_id', 'cp_game_id', 'platform_id', 'app_show_id', 'promotion_id',
        ],
        'game_id'              => ['cp_game_id'],
        'cp_game_id'           => [],
        'user_id'              => ['department_id'],
        'platform_id'          => [],
        'account_id'           => ['ad_account', 'account_name'],
        'ad_account'           => ['account_id'],
        'account_name'         => [],
        'app_show_id'          => [],
        'promotion_id'         => [],
        'tday'                 => [],
        'dim_user_os'          => [],
    ];


    /**
     * @param array $groups
     *
     * @return array
     * @deprecated 下一版弃用
     */
    public static function groupFilterColumn(array $groups): array
    {
        $resetCols = [];

        if (
            !in_array('channel_id', $groups)
            && !in_array('package_id', $groups)
            && !in_array('creative_id', $groups)
            && !in_array('plan_id', $groups)
            && !in_array('campaign_id', $groups)
        ) {
            $resetCols['channel_id'] = '-';
        }

        if (
            !in_array('cp_game_id', $groups)
            && !in_array('game_id', $groups)
            && !in_array('package_id', $groups)
            && !in_array('creative_id', $groups)
            && !in_array('plan_id', $groups)
            && !in_array('campaign_id', $groups)
        ) {
            $resetCols['cp_game_id'] = '-';
        }

        if (
            !in_array('game_id', $groups)
            && !in_array('package_id', $groups)
            && !in_array('creative_id', $groups)
            && !in_array('plan_id', $groups)
        ) {
            $resetCols['game_id'] = '-';
        }

        if (
            !in_array('package_id', $groups)
            && !in_array('creative_id', $groups)
            && !in_array('plan_id', $groups)
        ) {
            $resetCols['package_id'] = '-';
        }

        if (
            !in_array('user_id', $groups)
            && !in_array('package_id', $groups)
            && !in_array('creative_id', $groups)
            && !in_array('plan_id', $groups)
        ) {
            $resetCols['user_id'] = '-';
        }

        if (
            !in_array('department_id', $groups)
            && !in_array('package_id', $groups)
            && !in_array('creative_id', $groups)
            && !in_array('plan_id', $groups)
        ) {
            $resetCols['department_id'] = '-';
        }

        if (
            !in_array('creative_id', $groups)
        ) {
            $resetCols['creative_id']   = '-';
            $resetCols['creative_name'] = '-';
        }

        if (
            !in_array('plan_id', $groups)
            && !in_array('creative_id', $groups)
        ) {
            $resetCols['plan_id']   = '-';
            $resetCols['plan_name'] = '-';
        }

        if (
            !in_array('campaign_id', $groups)
            && !in_array('creative_id', $groups)
            && !in_array('plan_id', $groups)
        ) {
            $resetCols['campaign_id']   = '-';
            $resetCols['campaign_name'] = '-';
        }

        if (!in_array('tday', $groups)) {
            $resetCols['tday'] = '-';
        }

        if (
            !in_array('channel_main_id', $groups)
            && !in_array('channel_id', $groups)
            && !in_array('package_id', $groups)
            && !in_array('plan_id', $groups)
            && !in_array('creative_id', $groups)
            && !in_array('campaign_id', $groups)
            && !in_array('channel_main_id', $groups)
        ) {
            $resetCols['channel_main_id'] = '-';
        }

        if (
            !in_array('platform_id', $groups)
            && !in_array('package_id', $groups)
            && !in_array('plan_id', $groups)
            && !in_array('creative_id', $groups)
            && !in_array('campaign_id', $groups)
        ) {
            $resetCols['platform_id'] = '-';
        }

        if (
            !in_array('app_show_id', $groups)
            && !in_array('package_id', $groups)
            && !in_array('plan_id', $groups)
            && !in_array('creative_id', $groups)
        ) {
            $resetCols['app_show_id'] = '-';
        }

        if (
            !in_array('promotion_channel_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['promotion_channel_id'] = '-';
        }

        if (
            !in_array('promotion_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['promotion_id'] = '-';
        }


        return $resetCols;
    }

    /**
     * @param array $groups
     *
     * @return array
     * @deprecated 下一版弃用
     */
    public static function groupFilterOperatorColumn(array $groups): array
    {
        $resetCols = [];

        if (
            !in_array('channel_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['channel_id'] = '-';
        }

        if (
            !in_array('cp_game_id', $groups)
            && !in_array('game_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['cp_game_id'] = '-';
        }

        if (
            !in_array('game_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['game_id'] = '-';
        }

        if (
            !in_array('package_id', $groups)
        ) {
            $resetCols['package_id'] = '-';
        }

        if (
            !in_array('user_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['user_id'] = '-';
        }

        if (
            !in_array('department_id', $groups)
            && !in_array('user_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['department_id'] = '-';
        }

        if (!in_array('tday', $groups)) {
            $resetCols['tday'] = '-';
        }

        if (
            !in_array('channel_main_id', $groups)
            && !in_array('channel_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['channel_main_id'] = '-';
        }

        if (
            !in_array('platform_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['platform_id'] = '-';
        }

        if (
            !in_array('app_show_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['app_show_id'] = '-';
        }

        if (
            !in_array('promotion_id', $groups)
            && !in_array('package_id', $groups)
        ) {
            $resetCols['promotion_id'] = '-';
        }

        return $resetCols;


    }

    /**
     * 广告看板汇总关系处理
     * @param array $groups
     *
     * @return array
     * @deprecated
     * @example static::groupAdFilter(['tday', 'package_id'])
     */
    public static function groupAdRelation(array $groups): array
    {
        $relationTree = [
            'creative_id'          => [
                'creative_name', 'plan_id',
            ],
            'plan_id'              => [
                'plan_name', 'campaign_id',
            ],
            'campaign_id'          => [
                'campaign_name', 'promotion_channel_id', 'promotion_id',
                'account_id', 'ad_account', 'account_name', 'dim_user_os', 'user_id',
            ],
            'package_id'           => [
                'game_id', 'cp_game_id', 'user_id',
                'platform_id', 'app_show_id',
                'channel_main_id', 'channel_id',
            ],
            'channel_id'           => ['channel_main_id'],
            'channel_main_id'      => [],
            'game_id'              => ['cp_game_id'],
            'user_id'              => ['department_id'],
            'platform_id'          => [],
            'account_id'           => ['ad_account', 'account_name'],
            'ad_account'           => ['account_id'],
            'account_name'         => [],
            'app_show_id'          => [],
            'promotion_channel_id' => ['channel_main_id'],
            'promotion_id'         => [],
            'cp_game_id'           => [],
            'tday'                 => [],
            'dim_user_os'          => [],
        ];

        return static::matchRelationByGroups($relationTree, $groups);
    }

    public static function groupAdFill(array $groups): array
    {
        $relationTree = [
            'creative_id'          => ['plan_id',],
            'plan_id'              => ['campaign_id',],
            'campaign_id'          => [
                'promotion_channel_id', 'promotion_id', 'account_id',
                'ad_account', 'account_name', 'dim_user_os', 'user_id',
            ],
            'package_id'           => [
                'game_id', 'cp_game_id', 'user_id',
                'platform_id', 'app_show_id',
                'channel_main_id',
            ],
            'channel_id'           => ['channel_main_id'],
            'channel_main_id'      => [],
            'game_id'              => ['cp_game_id'],
            'user_id'              => ['department_id'],
            'platform_id'          => [],
            'account_id'           => ['ad_account', 'account_name'],
            'ad_account'           => ['account_id'],
            'account_name'         => [],
            'app_show_id'          => [],
            'promotion_channel_id' => ['channel_main_id'],
            'promotion_id'         => [],
            'cp_game_id'           => [],
            'tday'                 => [],
            'dim_user_os'          => [],
        ];

        return static::matchRelationByGroups($relationTree, $groups);
    }


    /**
     * 运营看板汇总关系处理
     *
     * @param array $groups
     *
     * @return array
     */
    public static function groupOperatorRelation(array $groups): array
    {
        $relationTree = [
            'package_id'    => [
                'game_id', 'platform_id', 'user_id',
                'app_show_id', 'channel_id',
            ],
            'channel_id'    => ['channel_main_id'],
            'game_id'       => ['cp_game_id'],
            'user_id'       => [],
            'platform_id'   => [],
            'ad_account_id' => [],
            'ad_account'    => [],
            'app_show_id'   => [],
            'thour'         => ['tday'],
            'tday'          => [],
        ];

        return static::matchRelationByGroups($relationTree, $groups);
    }

    /**
     * 匹配多个汇总字段关系
     *
     * @param $relationTree
     * @param $groups
     *
     * @return array
     */
    public static function matchRelationByGroups($relationTree, $groups): array
    {
        $relateGroup = [];
        foreach ($groups as $groupNode) {
            $r           = Mather::bfs($relationTree, $groupNode);
            $relateGroup = array_merge($relateGroup, $r);
        }

        return array_unique($relateGroup);
    }

    /**
     * 充值汇总关系处理
     *
     * @param array $groups
     *
     * @return array
     * @example static::groupAdFilter(['tday', 'package_id'])
     */
    public static function groupRechargeSummaryRelation(array $groups): array
    {
        $relationTree = [
            'package_id'  => [
                'game_id', 'cp_game_id',
                'platform_id', 'payway',
            ],
            'game_id'     => ['cp_game_id', 'payway'],
            'cp_game_id'  => ['payway'],
            'platform_id' => ['payway'],
            'tday'        => ['payway'],
            'payway'      => ['payway'],
        ];

        return static::matchRelationByGroups($relationTree, $groups);
    }


    /**
     * 充值汇总关系处理
     *
     * @param array $groups
     *
     * @return array
     * @example static::groupAdFilter(['tday', 'package_id'])
     */
    public static function groupPaymentVoidedRelation(array $groups): array
    {
        $relationTree = [
            'game_id'      => ['cp_game_id'],
            'cp_game_id'   => [],
            'tday'         => [],
            'core_account' => [],
            'role_id'      => [],
        ];

        return static::matchRelationByGroups($relationTree, $groups);
    }

    /**
     * @param string $dimension
     * @return array
     */
    public static function returnAdRelation(string $dimension = AdEnum::AD_PLAN): array
    {
        if ($dimension == AdEnum::AD_CAMPAIGN) {
            return AdEnum::RELATE_AD_CAMPAIGN;
        }
        elseif ($dimension == AdEnum::AD_CREATIVE) {
            return AdEnum::RELATE_AD_CREATIVE;
        }
        else {
            return AdEnum::RELATE_AD_PLAN;
        }
    }
}
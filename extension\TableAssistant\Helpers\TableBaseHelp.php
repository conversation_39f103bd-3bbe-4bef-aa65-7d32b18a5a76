<?php

namespace app\extension\TableAssistant\Helpers;

use Aura\SqlQuery\Common\SelectInterface;

/**
 * @TableBaseHelp 查询通用的嵌套拼接sql操作
 */
class TableBaseHelp
{
    /**
     * @param $groups
     *
     * @return \Closure
     */
    public static function setGroup($groups): \Closure
    {
        return function (SelectInterface &$query) use ($groups) {
            $query->groupBy($groups);
        };
    }

    /**
     * @param array $columns
     *
     * @return \Closure
     */
    public static function setColumn(array $columns = ['*']): \Closure
    {
        return function (SelectInterface &$query) use ($columns) {
            $query->cols($columns);
        };
    }

    /**
     * @param $order
     *
     * @return \Closure
     */
    public static function setOrder($order): \Closure
    {
        return function (SelectInterface &$query) use ($order) {
            $query->orderBy($order);
        };
    }

    /**
     * @param     $pageSize
     * @param int $page
     *
     * @return \Closure
     */
    public static function setPage($pageSize, int $page = 1): \Closure
    {
        return function (&$query) use ($pageSize, $page) {
            $offset = ($page - 1) * $pageSize;

            $query
                ->limit($pageSize)
                ->offset($offset);
        };
    }

    /**
     * @param       $group
     * @param array $groupMap
     *
     * @return array
     */
    public static function changeGroups($group, array $groupMap): array
    {
        return collect($group)->map(function ($item) use ($groupMap) {
            if (!isset($groupMap[$item])) return $item;
            return $groupMap[$item];
        })->all();
    }
}
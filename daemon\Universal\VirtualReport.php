<?php
declare(ticks=1);


namespace app\daemon\Universal;

use app\extension\Exception\ParameterException;
use app\extension\Support\CommonCenter\ConfCenter\Request;
use app\extension\Support\CommonCenter\ConfCenter\Uri;
use app\extension\Support\Helpers\DBHelper\FieldCollect;
use app\service\AdvertiserData\VirtualReportLogServ;
use app\service\OriginData\ClickLogServ;
use app\service\ReportMedia\ReportFactory;
use Plus\CLI\DaemonProcess;


/**
 * 虚拟上报监控进程
 *
 */
class VirtualReport extends DaemonProcess
{

    /**
     * @return void
     */
    public function run()
    {
        echo 'staring...' . PHP_EOL;

        $interval      = 5;
        $nextExecution = time() + $interval;

        while (true) {
            $currentTime = time();

            if ($currentTime >= $nextExecution) {
                echo '轮询中...' . PHP_EOL;

                try {
                    // 执行循环体内的操作
                    $condition = $this->getAllCondition();

                    if (!empty($condition)) {
                        $jobX = null;
                        foreach ($condition as $item) {
                            if ($item['state'] != 2) continue;

                            $tId = $item['id'];

                            if ($this->getLock($tId)) {
                                // 获取第一条有效的信息
                                $jobX = $item;
                                break;
                            }
                        }

                        if (is_null($jobX)) goto next_loop;

                        echo 'get report job::' . PHP_EOL;
                        print_r($jobX);

                        $result = $this->reportFn($jobX);

                        if ($result) {
                            // 同步成功状态
                            $requestC = new Request(Uri::getHost() . Uri::URI_FINISH_REPORT);
                            $response = $requestC->get(['id' => $jobX['id']]);
                            print_r($response);
                        }

                    }
                }
                catch (\Exception $e) {
                    echo $e->getMessage() . PHP_EOL;
                }

                next_loop:
                // 更新下一次执行的时间
                $nextExecution = $currentTime + $interval;
            }

            usleep(10000); // 10 毫秒
        }
    }


    /**
     * 获取全部已经开启的的虚拟上报任务
     *
     * @return array
     * @throws \Exception
     */
    protected function getAllCondition(): array
    {
        $jobList   = [];
        $isRunning = true;
        $page      = 1;
        $requestC  = new Request(Uri::getHost() . Uri::URI_CONDITION_RULE);

        do {
            try {
                $result    = $requestC->get(['state' => 2, 'pageNo' => $page, 'pageSize' => 10]);
                $response  = \json_decode($result, true);
                $items     = $response['data']['items'] ?? [];
                $pageCount = $response['pageCount'] ?? 1;

                if (empty($items)) {
                    $isRunning = false;
                    goto last_line;
                }
                else {
                    $jobList = array_merge($jobList, $items);
                }

                if ($pageCount == $page) $isRunning = false;
            }
            catch (\Exception $e) {
                $isRunning = false;
            }

            last_line:
            $page++;
        }
        while ($isRunning);

        return $jobList;
    }

    /**
     *
     * 上报操作
     *
     * @param $conf
     *
     * @return bool
     */
    protected function reportFn($conf): bool
    {
        $channel   = $conf['report_media'] ?? null;
        $packageId = $conf['package_id'] ?? null;

        if (is_null($channel) || empty($packageId)) {
            throw new \RuntimeException('missing argument');
        };

        $startTime    = new \DateTime();
        $jobNum       = $conf['config_data']['number'] ?? 1;
        $isHasPlan    = ($conf['config_data']['plan_type'] ?? 1) == 1 ? 0 : 1;
        $planId       = $conf['config_data']['plan_id'] ?? '';
        $options      = ['channel_id' => $channel, 'package_id' => $packageId];
        $id           = $conf['id'] ?? 0;
        $reportEvents = $conf['config_data']['options'] ?? [];
        $amount       = $conf['config_data']['amount'] ?? 0.01;

        if ($isHasPlan) {
            $options['plan_id'] = $planId;
        }

        $serv          = new ClickLogServ();
        $reportFactory = ReportFactory::getReportFactory($channel);
        $cacheKey      = $this->getCacheKey($id);

        if (empty($reportEvents)) {
            throw new \RuntimeException('missing argument');
        };

        while ($this->checkRunningPass($jobNum, $startTime)) {
            try {
                $jobOption  = $options;
                $handledIds = \Plus::$app->redis->lRange($cacheKey, 0, -1);

                if (is_array($handledIds) && !empty($handledIds)) {
                    $jobOption['id[!]'] = array_unique($handledIds);
                }

                $info = $serv->getClickInfo($jobOption, ['page_size' => $jobNum]);

                echo '上报随机抽取信息>>' . PHP_EOL;

                foreach ($info as $item) {
                    print_r($item);
                    $itemId      = $item['ID'] ?? null;
                    $callbackUrl = $item['CALLBACK_URL'] ?? null;

                    if (!empty($itemId)) \Plus::$app->redis->lPush($cacheKey, $itemId);

                    if (empty($itemId) || empty($callbackUrl)) continue;

                    foreach ($reportEvents as $event) {
                        if ($event == 'pay') {
                            $result = $reportFactory->report($callbackUrl, $item, $event, $amount);
                        }
                        else {
                            $result = $reportFactory->report($callbackUrl, $item, $event);
                        }

                        $response = $result['response'] ?? null;

                        if (empty($response)) {
                            $status = -1;
                        }
                        else {
                            $status   = 1;
                        }

//                        $requestUri = $result['request'] ?? null;

                        $log = [
                            'REPORT_ID'      => $id,
                            'PACKAGE_ID'     => $packageId,
                            'REQUEST_PARAMS' => \json_encode($result),
                            'REPORT_RESULT'  => $result['response'] ?? '',
                            'REPORT_TIME'    => date('Y-m-d H:i:s'),
                            'EVENT_TYPE'     => $event,
                            'STATE'          => $status,
                        ];

                        (new VirtualReportLogServ())->insertLog($log);
                    }

                    $jobNum--;
                }
            }
            catch (\Exception $e) {
                echo 'Something error::' . $e->getMessage() . PHP_EOL;
                break;
            }

            sleep(5);
        }

        return $jobNum <= 0;
    }


    /**
     * @param $id
     *
     * @return string
     */
    private function getCacheKey($id): string
    {
        return 'virtual_report:id:' . $id;
    }

    /**
     * 执行超过一天判定为失败
     *
     * @param int       $jobNum
     * @param \DateTime $startTime
     *
     * @return bool
     */
    private function checkRunningPass(int $jobNum, \DateTime $startTime): bool
    {
        $now  = new \DateTime();
        $diff = (int)$now->diff($startTime)->format('%a');

        return $jobNum > 0 || $diff >= 1;
    }

    /**
     * @param $id
     *
     * @return array|bool|\Redis
     * @throws \RedisException
     */
    private function getLock($id)
    {
        $lockKey = 'virtual_report_lock:id:' . $id;
        $re      = \Plus::$app->redis->setnx($lockKey, 1);

        if ($re) {
            \Plus::$app->redis->expire($lockKey, 60 * 60 * 24 * 7);
        }

        return $re;
    }


}
<?php
/**
 * 微信数据上报
 * Created by PhpStorm.
 * User: Tim
 * Date: 2019/3/14
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class Taptap extends AdBaseInterface
{


    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, "active");
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, "reg");
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, "pay");
    }

    /**
     * 上报数据
     * @param $packageId
     * @param $info
     * @param $uploadConfId
     */
    private function uploadData($info, $event)
    {
        $extClick   = $info['EXT_CLICK']?$info['EXT_CLICK']:[];
        $tapTrackId = isset($extClick["tap_track_id"])?$extClick["tap_track_id"]:"";
        $gameId     = $info['EXT']["app_id"];
        if ($info["CLICK_ID"]) {
            //归因
            $callbackUrl = $info["CALLBACK_URL"]."&";
        } else {
            //自然量
            $callbackUrl = "https://dcc.iem.taptap.cn/v1/deep/callback?";
        }
        $time = time();

        $callbackUrl .="tap_track_id={$tapTrackId}&tap_project_id={$gameId}&event_timestamp={$time}&ipv4=".$info["IP"]."&ipv6=".$info["IP"]."&ua=".urlencode($info["USERAGENT"]);
        //拼接要上报的数据
        if (substr($info['PACKAGE_ID'], -2) == 99) { //IOS包
            $idfa         = $extClick["idfa"]?$extClick["idfa"]:$info['DEVICE_KEY'];
            $callbackUrl .="&md5_idfa=".md5($idfa)."&md5_caid=".md5($idfa);
        } else { //安卓包
            $callbackUrl .="&md5_android_id=".md5($info["ANDROID_ID"])."&md5_oaid=".md5($info["OAID"])."&md5_caid=".md5($info["OAID"]);
        }

        switch ($event) {
            case "active":
                $title        = "激活";
                $callbackUrl .="&event_type=1";
                break;
            case "reg":
                $title        = "注册";
                $callbackUrl .="&event_type=2";
                break;
            case "pay":
                $title        = "付费";
                $callbackUrl .="&event_type=3&amount=".($info["MONEY"]*100);
                break;
        }

        $http = new Http($callbackUrl);
        $res  = $http->get();

        //记录上报结果
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'tabtab';
        $logInfo['request']      = json_encode(['url' => $callbackUrl]);
        $logInfo['response']     = $res;
        $resContent              = json_decode($res, true);

        if ($resContent['Code'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        //写日志
        $this->log($info, $logInfo, $res, $callbackUrl);
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

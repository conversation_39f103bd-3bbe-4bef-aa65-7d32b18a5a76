<?php
/* Smarty version 5.0.0-rc3, created on 2025-06-23 10:33:52
  from 'file:sql/advertise/ad_ltv/creative_ltv_total.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.0.0-rc3',
  'unifunc' => 'content_6858bd10386360_75615749',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'c2b277e28848327aa339492fe6c094da0bb97aba' => 
    array (
      0 => 'sql/advertise/ad_ltv/creative_ltv_total.tpl',
      1 => 1749631986,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:sql/advertise/ad_ltv/creative_ltv.tpl' => 1,
  ),
))) {
function content_6858bd10386360_75615749 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = '/mnt/e/project/spy2.0-api/public/templates/sql/advertise/ad_ltv';
?>select count(1) as total_count from (
    <?php $_smarty_tpl->renderSubTemplate("file:sql/advertise/ad_ltv/creative_ltv.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), (int) 0, $_smarty_current_dir);
?>
) total_body
<?php }
}

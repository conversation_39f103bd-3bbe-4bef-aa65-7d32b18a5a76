<?php

namespace app\logic\adconf;


use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\Zakia;
use app\models\baseConfPlatform\TbBasePackageConf;
use app\models\OriginPlatform\TbSdkActiveLog;
use app\models\OriginPlatform\TbSdkUserNewloginPackage;
use app\models\OriginPlatform\TbSdkUserPayment;
use app\service\AdConf\AdUploadConfServ;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use app\service\General\Helpers\TableConst;

class ReportingConfLogic
{
    /**
     * @throws \Exception
     */
    public function getInfo(
        array $params = [], array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $reportContentMap = [
            'active_upload'      => '',
            'reg_upload'         => '',
            'login_upload'       => '',
            'pay_upload'         => '',
            'remain_upload'      => '',
            'create_role_upload' => '',
            'pay_virtual_upload' => '',
        ];

        $constMap     = (new BasicServ())->getMultiOptions(['game_id']);
        $gameIdMap    = array_column($constMap->get('game_id')->toArray(), 'val', 'key');
        $channelIdMap = array_column((new GeneralOptionServ())->listChannelOptions()->toArray(), 'val', 'key');
        $userMap      = array_column((new GeneralOptionServ())->getAllUserList(), 'real_name', 'id');
        $info         = (new AdUploadConfServ())->getList($params, $groups, $paginate, $sort);
        $list         = &$info['list'];

        foreach ($list as &$foo) {
            $gameId    = $foo['game_id'] ?? '';
            $channelId = $foo['channel_id'] ?? '';
            $status    = $foo['status'];
            $userId    = $foo['user_id'];

            if (!empty($foo['ext'])) {
                $ext = \json_decode($foo['ext'], true);

                if (json_last_error() == JSON_ERROR_NONE) {
                    $ext = array_change_key_case($ext);
                    $foo = array_merge($foo, $ext);
                }
            }

            $foo['status_id']    = $status;
            $foo['status_name']  = $status == 1 ? '上报中' : '暂停上报';
            $foo['user_name']    = $userMap[$userId] ?? '';
            $foo['game_name']    = $gameIdMap[$gameId] ?? '';
            $foo['channel_name'] = $channelIdMap[$channelId] ?? '';
            $reportContent       = array_intersect_key($foo, $reportContentMap);
            $foo                 = array_diff_key($foo, $reportContentMap);

            $foo['report_content'] = $reportContent;
            unset($foo['ext']);
        }

        return $info;
    }

    /**
     * @param $list
     *
     * @return mixed
     * @throws \Exception
     * @throws \Throwable
     */
    public function updateOrInsert($list)
    {
        $now = date('Y-m-d H:i:s');

        foreach ($list as &$item) {
            $keys          = array_keys($item);
            $reportContent = $this->formatOnlyUpdateReportContent($item['report_content'] ?? []);
            $item          = array_merge($item, $reportContent);
            unset($item['report_content']);

            if (empty(array_diff($keys, ['id', 'report_content']))) {
                $item['add_time'] = $now;
            }
            else {
                if (empty($item['id'])) {
                    $item['add_time'] = $now;
                }
                $item = $this->formatForInsertOrUpdate($item);
            }
            $item['update_time'] = $now;
            $item['admin_id']    = \Plus::$service->admin->getUserId();
        }

        return (new AdUploadConfServ())->updateOrInsert($list);
    }

    /**
     * @param $item
     *
     * @return array
     */
    private function formatOnlyUpdateReportContent($item): array
    {
        return array_intersect_key(
            $item,
            [
                'active_upload'      => '',
                'reg_upload'         => '',
                'login_upload'       => '',
                'pay_upload'         => '',
                'remain_upload'      => '',
                'create_role_upload' => '',
                'pay_virtual_upload' => '',
            ]
        );
    }

    /**
     * @param $item
     *
     * @return array
     * @throws \Exception
     */
    private function formatForInsertOrUpdate($item): array
    {
        $channel = $item['channel_id'] ?? null;
        $package = $item['package_id'] ?? null;

        if (empty($channel) || empty($package)) {
            throw new \Exception('缺少包号或渠道参数');
        }

        if ($this->requireAKeyChannel($channel) && !isset($item['akey'])) {
            throw new \Exception('akey必填');
        }

        if ($this->requireAppKeyChannel($channel) && !isset($item['app_key'])) {
            throw  new \Exception('APP_KEY必填');
        }

        if ($this->requireUrlAndTokenChannel($channel) && empty($item['url']) && empty($item['token'])) {
            throw new \Exception('url和token必填');
        }

        if ($this->requireAppIdChannel($channel) && empty(trim($item['app_id']))) {
            throw new \Exception('appid必填');
        }


        //优酷
        if ($channel == 8411 && empty($item['token'])) {
            throw new \Exception('token必填');
        }

        if (in_array($channel, [2, 8329])) {
            // 头条feed
            if (
                !empty($item['paymoney_range_left'])
                || !empty($item['paymoney_range_right'])
            ) {
                $payMoneyLeft  = $item['paymoney_range_left'] ?? 0;
                $payMoneyRight = $item['paymoney_range_right'] ?? 0;
                $payNumLeft    = $item['paynum_range_left'] ?? 0;
                $payNumRight   = $item['paynum_range_right'] ?? 0;

                if (!is_numeric($payMoneyLeft) || !is_numeric($payMoneyRight)) {
                    throw new \Exception('单次付费金额必须是数字');
                }

                if ($payMoneyLeft > $payMoneyRight) {
                    throw new \Exception('单次付费金额左边值必须比右边值小');
                }

                if (!is_numeric($payNumLeft) || !is_numeric($payNumRight)) {
                    throw new \Exception('单次付费金额必须是数字');
                }

                if ($payNumLeft > $payNumRight) {
                    throw new \Exception('付费次数左边值必须比右边值小');
                }
            }

            if (isset($item['upload_rate']) && $item['upload_rate'] != '') {
                if (!is_numeric($item['upload_rate'])) {
                    throw new \Exception('上报比例必须为数字');
                }

                if ($item['upload_rate'] > 100) {
                    throw new \Exception('上报比例不能超过100%');
                }
            }
        }

        $infoMap = [
            'id'                 => '',
            'channel_id'         => '',
            'package_id'         => '',
            'game_id'            => '',
            'status'             => '',
            'os'                 => '',
            'active_upload'      => '',
            'reg_upload'         => '',
            'login_upload'       => '',
            'pay_upload'         => '',
            'remain_upload'      => '',
            'notice'             => '',
            'create_role_upload' => '',
            'pay_virtual_upload' => '',
            'add_time'           => '',
            'update_time'        => '',
            'admin_id'           => '',
        ];

        $ext         = array_diff_key($item, $infoMap);
        $info        = array_intersect_key($item, $infoMap);
        $info['ext'] = \json_encode($ext);

        return $info;
    }

    /**
     * akey 必填的渠道
     *
     * @param $channel
     *
     * @return bool
     */
    private function requireAKeyChannel($channel): bool
    {
        return ($channel == 1023)
            or ($channel == 12)
            or ($channel == 3)
            or ($channel == 32);
    }

    /**
     * @param $channel
     *
     * @return bool
     */
    private function requireAppKeyChannel($channel): bool
    {
        return $channel == 8365;
    }

    /**
     *
     * @param $channel
     * @return bool
     */
    private function requireUrlAndTokenChannel($channel): bool
    {
        return $channel == 1107;
    }

    /**
     * appid必填的渠道
     *
     * @param $channel
     * @return bool
     */
    private function requireAppIdChannel($channel): bool
    {
        return $channel == 131
            || $channel == 1105;
    }

    /**
     * 激活上报记录插入
     *
     * @param $packageId
     * @param $originDeviceId
     * @param $androidId
     * @param $oaid
     *
     * @return bool|int
     * @throws \RedisException
     */
    public function reportActive($packageId, $originDeviceId, $androidId, $oaid)
    {
        [$deviceId, $md5DeviceId] = Zakia::getDeviceId($originDeviceId);

        $gameId = TbBasePackageConf::getInstance()->asArray()->find(['package_id' => $packageId], 'game_id');

        $wheres = [
            'DEVICE_CODE' => md5($md5DeviceId),
            'GAME_ID'     => $gameId,
            'PACKAGE_ID'  => $packageId,
            'DEVICE_ID'   => $deviceId,
            'DEVICE_KEY'  => '',
        ];
        if (!empty($oaid)) {
            $wheres['OAID'] = $oaid;
        }

        $info = TbSdkActiveLog::getInstance()->asArray()->find($wheres);

        if (!empty($info)) {
            TbSdkActiveLog::getInstance()->delete(['id' => $info['ID']]);
            unset($info['ID']);

            $insertModel = TbSdkActiveLog::getInstance();

            foreach ($info as $k => $f) {
                $insertModel->$k = $f;
            }

            return $insertModel->insert();
        }
        else {
            $newActive                = TbSdkActiveLog::getInstance();
            $newActive->GAME_ID       = $gameId;
            $newActive->PACKAGE_ID    = $packageId;
            $newActive->TIME          = date('Y-m-d H:i:s');
            $newActive->TIME_SERVER   = date('Y-m-d H:i:s');
            $newActive->DEVICE_ID     = $deviceId;
            $newActive->MD5_DEVICE_ID = $md5DeviceId;
            $newActive->DEVICE_CODE   = \md5($md5DeviceId);
            $newActive->DEVICE_KEY    = \md5(time());
            $newActive->ANDROID_ID    = $androidId;
            $newActive->OAID          = $oaid;
            $newActive->REFER         = 'debug_upload';

            return $newActive->insert();
        }

    }

    /**
     * 注册上报记录插入
     *
     * @param $packageId
     * @param $originDeviceId
     * @param $androidId
     * @param $coreAccount
     * @param $oaid
     *
     * @return bool|int
     * @throws \RedisException
     */
    public function reportReg($packageId, $originDeviceId, $androidId, $coreAccount, $oaid)
    {
        $coreAccount = !empty($coreAccount) ? $coreAccount : '数据上报联调_' . date('ymdHis');
        [$deviceId, $md5DeviceId] = Zakia::getDeviceId($originDeviceId);

        $gameId = TbBasePackageConf::getInstance()->asArray()->find(['package_id' => $packageId], 'game_id');

        // 获取记录
        $info = TbSdkUserNewloginPackage::getInstance()->asArray()->find([
            'GAME_ID'      => $gameId,
            'PACKAGE_ID'   => $packageId,
            'CORE_ACCOUNT' => $coreAccount,
        ]);

        if (!empty($info)) {
            TbSdkUserNewloginPackage::getInstance()->delete(['id' => $info['ID']]);
            unset($info['ID']);
            $newModel = TbSdkUserNewloginPackage::getInstance();
            foreach ($info as $k => $d) {
                $newModel->$k = $d;
            }
        }
        else {
            $newModel = TbSdkUserNewloginPackage::getInstance();

            $newModel->GAME_ID       = $gameId;
            $newModel->PACKAGE_ID    = $packageId;
            $newModel->CORE_ACCOUNT  = $coreAccount;
            $newModel->TIME          = date('Y-m-d H:i:s');
            $newModel->TIME_SERVER   = date('Y-m-d H:i:s');
            $newModel->DEVICE_ID     = $deviceId;
            $newModel->MD5_DEVICE_ID = $md5DeviceId;
            $newModel->DEVICE_CODE   = md5($md5DeviceId);
            $newModel->ANDROID_ID    = $androidId;
            $newModel->OAID          = $oaid;
            $newModel->REFER         = 'debug_upload';

        }

        return $newModel->insert();
    }

    /**
     * 付费上报记录插入
     *
     * @param $packageId
     * @param $originDeviceId
     * @param $androidId
     * @param $coreAccount
     * @param $money
     * @param $oaid
     *
     * @return true
     * @throws \RedisException
     * @throws \Exception
     */
    public function reportPay($packageId, $originDeviceId, $androidId, $coreAccount, $money, $oaid): bool
    {
        $coreAccount = !empty($coreAccount) ? $coreAccount : '数据上报联调_' . date('ymdHis');
        [$deviceId, $md5DeviceId] = Zakia::getDeviceId($originDeviceId);

        $gameId = TbBasePackageConf::getInstance()->asArray()->find(['package_id' => $packageId], 'game_id');

        $insertModel                = TbSdkUserNewloginPackage::getInstance();
        $insertModel->GAME_ID       = $gameId;
        $insertModel->PACKAGE_ID    = $packageId;
        $insertModel->CORE_ACCOUNT  = $coreAccount;
        $insertModel->DEVICE_ID     = $deviceId;
        $insertModel->MD5_DEVICE_ID = $md5DeviceId;
        $insertModel->DEVICE_CODE   = \md5($md5DeviceId);
        $insertModel->OAID          = $oaid;
        $insertModel->TIME          = date('Y-m-d H:i:s');
        $insertModel->TIME_SERVER   = date('Y-m-d H:i:s');
        $insertModel->REFER         = 'debug_upload';

        $orderId = date('ymdHis') . '00000';
        // 插入订单数据
        $paymentModel                = TbSdkUserPayment::getInstance();
        $paymentModel->GAME_ID       = $gameId;
        $paymentModel->PACKAGE_ID    = $packageId;
        $paymentModel->CORE_ACCOUNT  = $coreAccount;
        $paymentModel->DEVICE_ID     = $deviceId;
        $paymentModel->MD5_DEVICE_ID = $md5DeviceId;
        $paymentModel->ANDROID_ID    = $androidId;
        $paymentModel->OAID          = $oaid;
        $paymentModel->ORDER_ID      = $orderId;
        $paymentModel->ORDER_TIME    = date('Y-m-d H:i:s');
        $paymentModel->PAY_TIME      = date('Y-m-d H:i:s');
        $paymentModel->PAY_RESULT    = 1;
        $paymentModel->MONEY         = $money;
        $paymentModel->REFER         = 'debug_upload';

        $result = $paymentModel->insert();

        if (!$result) {
            throw new \Exception('订单数据插入失败');
        }

        return true;
    }

    /**
     * @param $packageId
     * @param $originDeviceId
     * @param $androidId
     * @param $coreAccount
     * @param $oaid
     *
     * @return bool|int
     * @throws \RedisException
     */
    public function reportCreateRole($packageId, $originDeviceId, $androidId, $coreAccount, $oaid)
    {
        $coreAccount = !empty($coreAccount) ? $coreAccount : '数据上报联调_' . date('ymdHis');
        [$deviceId, $md5DeviceId] = Zakia::getDeviceId($originDeviceId);

        $packageInfo = TbBasePackageConf::getInstance()->asArray()->find(['package_id', $packageId], ['game_id']);
        $gameId      = $packageInfo['game_id'] ?? null;

        $info = TbSdkUserNewloginPackage::getInstance()
            ->asArray()
            ->find([
                'GAME_ID'      => $gameId,
                'PACKAGE_ID'   => $packageId,
                'CORE_ACCOUNT' => $coreAccount,
            ]);

        if (!empty($info)) {
            TbSdkUserNewloginPackage::getInstance()->delete(['id' => $info['ID']]);
            unset($info['ID']);

            $newModel = TbSdkUserNewloginPackage::getInstance();
            foreach ($info as $k => $d) {
                $newModel->$k = $d;
            }

        }
        else {
            $newModel                = TbSdkUserNewloginPackage::getInstance();
            $newModel->GAME_ID       = $gameId;
            $newModel->PACKAGE_ID    = $packageId;
            $newModel->CORE_ACCOUNT  = $coreAccount;
            $newModel->TIME          = date('Y-m-d H:i:s');
            $newModel->TIME_SERVER   = date('Y-m-d H:i:s');
            $newModel->DEVICE_ID     = $deviceId;
            $newModel->MD5_DEVICE_ID = $md5DeviceId;
            $newModel->DEVICE_CODE   = \md5($md5DeviceId);
            $newModel->ANDROID_ID    = $androidId;
            $newModel->OAID          = $oaid;
            $newModel->REFER         = 'debug_upload';
        }

        return $newModel->insert();
    }


}
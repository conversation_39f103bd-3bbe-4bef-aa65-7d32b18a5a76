<?php

namespace app\apps\goals\controllers;

use app\apps\internal\Helpers\IndexCalculators;
use app\apps\internal\Traits\InternalRoutes;
use app\extension\Support\Collections\Arr;
use app\service\General\GoalsManagerServ;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Plus\MVC\Controller\JsonController;

/**
 * 目标管理接口
 */
class ManagerController extends JsonController
{
    use InternalRoutes;


    /**
     * 通过文件导入数据
     *
     * @return array
     */
    public function importByFileAction(): array
    {
        $input    = \Plus::$app->request->getFileItem('goals_file');
        $goalType = \Plus::$app->request->requestValue('goal_category', GoalsManagerServ::GOAL_CATEGORY_ROLLING);

        if (empty($input)) {
            return $this->error('missing file');
        }

        $fileInfo = $input->save(CACHE_DIR . $input->getFilename());
        $filePath = $fileInfo->getPathname();

        if (empty($filePath)) {
            return $this->error('发生未知错误, 请稍后再试');
        }

        $againstFormats = [
            IOFactory::WRITER_CSV,
            IOFactory::WRITER_XLSX,
            IOFactory::WRITER_XLS,
        ];

        $reader    = IOFactory::load($filePath, 0, $againstFormats);
        $tableData = $reader->getActiveSheet()->toArray();

        if (empty($tableData)) {
            return $this->error("未成功读取文件数据, 请稍后再试");
        }

        try {
            $title = Arr::pull($tableData, 0);

            // check title
            if ($goalType == GoalsManagerServ::GOAL_CATEGORY_ROLLING) {
                if ($title != ['月份', '目标游戏', '新用户', '消耗金额', '新用户付费金额', '老用户付费金额', '总付费目标']) {
                    throw new \InvalidArgumentException('表头需要规范');
                }
            }
            elseif ($goalType == GoalsManagerServ::GOAL_CATEGORY_INITIALLY) {
                if ($title != ['月份', '新用户', '消耗金额', '新用户付费金额', '老用户付费金额', '总付费目标']) {
                    throw new \InvalidArgumentException('表头需要规范');
                }
            }
            else {
                throw new \InvalidArgumentException('unknown category');
            }

            foreach ($tableData as &$item) {
                $item = array_combine($title, $item);
            }

            (new GoalsManagerServ())->coverGoalsWithFile($tableData, $goalType);
        }
        catch (\Throwable $th) {
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            return $this->error($th->getMessage());
        }

        if (file_exists($filePath)) {
            unlink($filePath);
        }

        return $this->success([]);
    }

    /**
     * @return array
     */
    public function listGoalsAction(): array
    {
        $request      = \Plus::$app->request;
        $year         = $request->getValue('year', date('Y'));
        $goalCategory = $request->getValue('goal_category', GoalsManagerServ::GOAL_CATEGORY_ROLLING);
        $methods      = $request->getValue('method', ['data', 'fields']);
        $result       = [];

        if (in_array('data', $methods)) {
            $listRe = (new GoalsManagerServ())->listGoalsGroupYear(['year' => $year], $goalCategory);
            $listRe = array_column($listRe, null, 'mo');
            $list   = [];

            for ($i = 1; $i <= 12; $i++) {
                $mo = $year . '-' . str_pad($i, 2, 0, STR_PAD_LEFT);

                if (!isset($listRe[$mo])) {
                    $list[$mo] = [
                        'mo'     => $mo,
                        'goal_1' => 0,
                        'goal_2' => 0,
                        'goal_3' => 0,
                        'goal_4' => 0,
                        'goal_5' => 0,
                    ];
                }
                else {
                    $list[$mo] = $listRe[$mo];
                }
            }

            $result['list'] = array_values($list);
        }

        if (in_array('fields', $methods)) {
            $fields = [
                ['title' => '月份', 'dataIndex' => 'mo'],
            ];

            foreach (GoalsManagerServ::GOALS_TYPE as $i => $field) {
                $t        = ['title' => $field, 'dataIndex' => 'goal_' . $i];
                $fields[] = $t;
            }
            $result['fields'] = $fields;
        }

        return $this->success($result);
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function listGoalsCpgameAction(): array
    {
        $request = \Plus::$app->request;
        $month   = $request->getValue('month');
        $methods = $request->getValue('method', ['data', 'fields']);
        $result  = [];

        if ($month == null) {
            return $this->error('missing argument');
        }

        if (in_array('data', $methods)) {
            $list       = (new GoalsManagerServ())->listGoalsGroupCpGame(['month' => $month]);
            $otherIndex = -1;

            foreach ($list as $i => &$item) {
                if ($item['cp_game_id'] == 0) {
                    $otherIndex           = $i;
                    $item['cp_game_name'] = '其他游戏合计';
                }
            }

            if ($otherIndex >= 0) {
                $otherItem = array_splice($list, $otherIndex, 1);
                $list[]    = $otherItem[0];
            }

            unset($item);

            $result['list'] = $list;
        }

        if (in_array('fields', $methods)) {
            $fields = [
                ['title' => '月份', 'dataIndex' => 'mo'],
                ['title' => '目标游戏', 'dataIndex' => 'cp_game_name'],
            ];

            foreach (GoalsManagerServ::GOALS_TYPE as $i => $field) {
                $t        = ['title' => $field, 'dataIndex' => 'goal_' . $i];
                $fields[] = $t;
            }
            $result['fields'] = $fields;
        }

        return $this->success($result);
    }

}
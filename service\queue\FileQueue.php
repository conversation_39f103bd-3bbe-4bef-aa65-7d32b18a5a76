<?php

namespace app\service\queue;

use RuntimeException;

/**
 * 基于文件内存队列
 * @package app\service\queue
 * <AUTHOR>
 */
class FileQueue
{
    /**
     * 文件路径
     * @var string
     */
    protected string $baseDir;

    /**
     * 注册的队列名称
     * @var array
     */
    protected array $registered;

    /**
     * 构造函数
     * @param string $baseDir    文件路径
     * @param array  $registered 注册的队列名称
     */
    public function __construct(string $baseDir = '/dev/shm/queue', array $registered = [])
    {
        $dir = rtrim($baseDir, '/');
        $dir = rtrim($dir, '\\');

        $this->baseDir    = $dir;
        $this->registered = $registered;
    }

    /**
     * 检查文件路径是否有效
     * @param string $queueName 队列名
     * @return bool|string
     * @throws RuntimeException
     */
    private function isValid(string $queueName)
    {
        if (!in_array($queueName, $this->registered)) {
            throw new RuntimeException(sprintf("队列 %s 未注册", $queueName));
        }

        $dir = $this->baseDir . DIRECTORY_SEPARATOR . $queueName;
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
        if (false === (file_exists($dir) && is_writable($dir))) {
            return false;
        } else {
            return $dir;
        }
    }

    /**
     * 入队
     * @param string $queueName 队列名
     * @param mixed  $obj       数据
     * @return bool
     */
    public function put(string $queueName, $obj)
    {
        if (false === ($src = $this->isValid($queueName))) {
            return false;
        }
        $objContent = serialize($obj);
        $objSize    = strlen($objContent);

        $queueFile   = $src . DIRECTORY_SEPARATOR . time() . '_' . rand(1000, 9999) . '.queue';
        $byteWritten = file_put_contents($queueFile, $objContent, LOCK_EX);
        unset($objContent);
        return ($byteWritten == $objSize);
    }

    /**
     * 出队
     * @param string $queueName 队列名
     * @return mixed
     */
    public function get(string $queueName)
    {
        if (false === ($src = $this->isValid($queueName))) {
            return false;
        }
        $obj = null;
        $dir = dir($src);
        if ($dir) {
            while (false !== ($file = $dir->read())) {
                if ($file != '.' && $file != '..') {
                    $queueFile  = $src . DIRECTORY_SEPARATOR . $file;
                    $objContent = file_get_contents($queueFile, LOCK_EX);
                    if ($objContent) {
                        $obj = unserialize($objContent);
                        unset($objContent);
                        unlink($queueFile);
                    }
                    break;
                }
            }
            $dir->close();
            unset($dir);
        }
        return $obj;
    }
}

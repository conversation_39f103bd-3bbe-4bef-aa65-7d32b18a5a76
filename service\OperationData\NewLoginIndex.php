<?php

namespace app\service\OperationData;

use app\apps\internal\Helpers\ConstHub;
use app\apps\operator\Helpers\ConstFirstLogin;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\TimeUtil;
use app\extension\TableAssistant\Helpers\ColumChanger;
use app\extension\TableAssistant\Helpers\TableBaseHelp;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Components\Helpers\WrapQuery;
use app\service\AdvertiserData\Scheme\BaseScheme;
use app\service\AdvertiserData\Traits\BasicOperator;
use app\service\AdvertiserData\Traits\Converter;
use app\service\ConfigService\Traits\BaseHub;
use app\service\OperationData\Components\MatchParams\FirstLoginMatcher;
use app\service\OperationData\Scheme\PackageBaseDailyScheme;
use app\service\OperationData\Scheme\PackageBaseMonthScheme;
use app\util\Common;
use Aura\SqlQuery\Common\SelectInterface;

/**
 * @NewLoginIndex 运营新增维度指标查询
 * @todo          目前仅保证新增用户数,消耗数据字段的准确性(待新增维度实时看板版本完善)
 *
 */
class NewLoginIndex
{
    use Converter, BasicOperator;

    /**
     * @param array $params
     * @param array $page
     * @param array $group
     * @param       $sort
     * @param array $column
     * @param bool  $returnSummary
     *
     * @return array
     * @throws \Exception
     */
    public function listBase(
        array $params = [],
        array $page = [],
        array $group = [],
              $sort = null,
        array $column = [],
        bool  $returnSummary = true
    ): array
    {
        $rangeDateDimension = (int)Arr::pull($params, 'range_date_dimension', ConstHub::DIMENSION_DAY);

        if (ConstHub::DIMENSION_WEEK === $rangeDateDimension) {
            return $this->listByWeek(...func_get_args());
        }
        elseif (ConstHub::DIMENSION_MONTH === $rangeDateDimension) {
            return $this->listByMonth(...func_get_args());
        }
        else {
            return $this->listByDaily(...func_get_args());
        }
    }

    /**
     * 获取基础数据集合(按天)
     *
     * @param array $params
     * @param array $page
     * @param array $groups
     * @param null  $sort
     * @param array $columns
     * @param bool  $returnSummary
     *
     * @return array
     * @throws \Exception
     */
    public function listByDaily(
        array $params = [], array $page = [], array $groups = [],
              $sort = null, array $columns = [], bool $returnSummary = true
    ): array
    {
        $result   = collect();
        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = PackageBaseDailyScheme::NewOne()->select();

        $infoCol = array_merge(
            $this->getColForTDay($params),
            $this->getCols($params)
        );

        $scheme
            ->joinPowerSql($powerSql)
            ->scope(TableBaseHelp::setGroup($groups))
            ->scope(TableBaseHelp::setColumn($infoCol));

        (new FirstLoginMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        // 备用
        $backupScheme = clone $scheme;

        if (!empty($sort)) {
            $scheme->scope(TableBaseHelp::setOrder(Arr::wrap($sort)));
        }

        if (!empty($page)) {
            $scheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }
        //输出调试sql
        Common::dumpSql((clone $scheme)->toSql());
        $result->put('list', $this->fetchAll($scheme->toSql()));

        if ($returnSummary) {
            // 汇总行处理
            $summaryBaseSql  = (clone $backupScheme)->toSql();
            $summaryCols     = array_merge(
                $this->getColForTDay($params, true, true),
                $this->getCols($params, true, true)
            );
            $summarySql      = WrapQuery::totalRowQuery($summaryBaseSql, $summaryCols);
            $countSql        = WrapQuery::countRowQuery($summaryBaseSql);
            $totalRow        = $this->fetch($countSql);
            //输出调试sql
            Common::dumpSql($summarySql);
            $summaryRow      = $this->fetch($summarySql);
            $lastUpdateTimes = Arr::pull($summaryRow, 'update_time');

            $result
                ->put('summary', $summaryRow)
                ->put('time', $lastUpdateTimes)
                ->put('total', $totalRow['total'] ?? 0);
        }

        return $result->toArray();

    }

    /**
     * 获取基础数据集合(按周)
     *
     * @param array             $params
     * @param array             $page
     * @param array             $groups
     * @param null|array|string $sort
     * @param array             $columns
     * @param                   $options
     *
     * @return array
     * @throws \Exception
     */
    public function listByWeek(
        array $params = [], array $page = [], array $groups = [],
              $sort = null, array $columns = [], $returnSummary = true
    ): array
    {
        $result = collect();

        // 若选择周期不满足为7天的周期则往后面继续延长到7天为一周
        [
            'begin' => $rangeDateStart,
            'end'   => $rangeDateEnd,
            'cycle' => $rangeDateCycle,
        ] = TimeUtil::divideWeekByRangeDate(
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end')
        );

        $params['range_date_start'] = $rangeDateStart;
        $params['range_date_end']   = $rangeDateEnd;

        $infoCols = array_merge(
            $this->getColForTDay($params, false, false, ConstFirstLogin::DIMENSION_WEEK),
            $this->getCols($params, false, false)
        );

        $powerSql  = \Plus::$service->admin->getAdminPowerSql();
        $subScheme = PackageBaseDailyScheme::NewOne()->select();
        $subScheme
            ->joinPowerSql($powerSql)
            ->scope(TableBaseHelp::setColumn($this->getSubColsForWeek($params, $rangeDateCycle)));

        (new FirstLoginMatcher($subScheme->fieldReflect()))
            ->setParams($params)
            ->execute($subScheme);

        $infoTopCols = array_merge(
            $this->getColForTDay($params, false, true, ConstFirstLogin::DIMENSION_WEEK),
            $this->getCols($params, false, true)
        );

        $mainScheme = new BaseScheme();
        $mainScheme
            ->from($subScheme, 'main_body')
            ->scope(TableBaseHelp::setColumn($infoTopCols))
            ->scope(TableBaseHelp::setGroup($groups));

        $backupScheme = clone $mainScheme;

        if (!empty($page)) {
            $mainScheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }
        if (!empty($sort)) {
            $mainScheme->scope(TableBaseHelp::setOrder(Arr::wrap($sort)));
        }
        //输出调试sql
        Common::dumpSql((clone $mainScheme)->toSql());
        $result->put('list', $this->fetchAll($mainScheme->toSql()));

        if ($returnSummary) {
            $summaryCols = array_merge(
                $this->getColForTDay($params, true, true, ConstFirstLogin::DIMENSION_WEEK),
                $this->getCols($params, true, true)
            );

            $summaryBaseSql = (clone $backupScheme)->toSql();
            $summarySql     = WrapQuery::totalRowQuery($summaryBaseSql, $summaryCols);

            // 汇总
            $countSql = WrapQuery::countRowQuery($summaryBaseSql);
            $totalRow = $this->fetch($countSql);
            //输出调试sql
            Common::dumpSql($summarySql);
            $summaryRow      = $this->fetch($summarySql);
            $lastUpdateTimes = Arr::pull($summaryRow, 'update_time');

            $result
                ->put('summary', $summaryRow)
                ->put('time', $lastUpdateTimes)
                ->put('total', $totalRow['total'] ?? 0);
        }

        return $result->toArray();
    }

    /**
     * 获取基础数据集合(按月)
     *
     * @param array $params
     * @param array $page
     * @param array $groups
     * @param null  $sort
     * @param array $columns
     * @param bool  $returnSummary
     *
     * @return array
     * @throws \Exception
     */
    public function listByMonth(
        array $params = [], array $page = [], array $groups = [],
              $sort = null, array $columns = ['*'], bool $returnSummary = true
    ): array
    {
        $result   = collect();
        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = PackageBaseMonthScheme::NewOne()->select();

        // 日期格式化处理
        [$rangeDateStart, $rangeDateEnd] = [
            Arr::pull($params, 'range_date_start'),
            Arr::pull($params, 'range_date_end'),
        ];

        foreach ([&$rangeDateStart, &$rangeDateEnd] as &$item) {
            $item = date('Ym', strtotime($item));
        }

        Arr::set($params, 'range_date_start', $rangeDateStart);
        Arr::set($params, 'range_date_end', $rangeDateEnd);

        $infoCol = array_merge(
            $this->getColForTDay(
                $params, false, false,
                ConstFirstLogin::DIMENSION_MONTH, PackageBaseMonthScheme::MAIN_TABLE['alias']
            ),
            $this->getCols(
                $params, false, false,
                PackageBaseMonthScheme::MAIN_TABLE['alias'],
            )
        );

        $scheme
            ->joinPowerSql($powerSql)
            ->joinMonthDataFilterDailyData($infoCol)
            ->scope(TableBaseHelp::setGroup($groups))
            ->scope(TableBaseHelp::setColumn($infoCol));

        (new FirstLoginMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        $backupScheme = clone $scheme;

        if (!empty($sort)) {
            $scheme->scope(TableBaseHelp::setOrder(Arr::wrap($sort)));
        }

        if (!empty($page)) {
            $scheme->scope(TableBaseHelp::setPage($page['page_size'], $page['page']));
        }
        //输出调试sql
        Common::dumpSql((clone $scheme)->toSql());
        $result->put('list', $this->fetchAll($scheme->toSql()));

        if ($returnSummary) {
            // 汇总行处理
            $summaryBaseSql  = (clone $backupScheme)->toSql();
            $summaryCols     = array_merge(
                $this->getColForTDay(
                    $params, true, true,
                    ConstFirstLogin::DIMENSION_MONTH, PackageBaseMonthScheme::MAIN_TABLE['alias']
                ),
                $this->getCols(
                    $params, true, true,
                    ConstFirstLogin::DIMENSION_MONTH, PackageBaseMonthScheme::MAIN_TABLE['alias']
                )
            );
            $summarySql      = WrapQuery::totalRowQuery($summaryBaseSql, $summaryCols);
            //输出调试sql
            Common::dumpSql($summarySql);
            $countSql        = WrapQuery::countRowQuery($summaryBaseSql);
            $totalRow        = $this->fetch($countSql);
            $summaryRow      = $this->fetch($summarySql);
            $lastUpdateTimes = Arr::pull($summaryRow, 'update_time');

            $result
                ->put('summary', $summaryRow)
                ->put('time', $lastUpdateTimes)
                ->put('total', $totalRow['total'] ?? 0);
        }

        return $result->toArray();
    }


    /**
     * 获取有关时间维度而变化的字段
     *
     * @param array  $params
     * @param bool   $isSummary
     * @param bool   $notNeedAggregate
     * @param int    $timeDimension
     * @param string $mainTable
     * @param string $paymentTable
     *
     * @return array
     * @throws \Exception
     */
    protected function getColForTDay(
        array  $params,
        bool   $isSummary = false,
        bool   $notNeedAggregate = false,
        int    $timeDimension = ConstFirstLogin::DIMENSION_DAY,
        string $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias'],
        string $paymentTable = 't_payment'
    ): array
    {
        if (ConstFirstLogin::DIMENSION_WEEK === $timeDimension) {
            return $this->getColForTDayByWeek($params, $isSummary, $notNeedAggregate, $mainTable, $paymentTable);
        }
        elseif (ConstFirstLogin::DIMENSION_MONTH === $timeDimension) {
            return $this->getColForTDayByMonth($params, $isSummary, $notNeedAggregate, $mainTable, $paymentTable);
        }
        else {
            return $this->getColForTDayByDay($params, $isSummary, $notNeedAggregate, $mainTable, $paymentTable);
        }
    }

    /**
     * 获取有关时间维度而变化的字段(周 维度)
     *
     * @param array  $params
     * @param bool   $isSummary
     * @param bool   $notNeedAggregate
     * @param string $mainTable
     * @param string $paymentTable
     * @param array  $rangeDateCycle
     *
     * @return array
     * @throws \Exception
     */
    protected function getColForTDayByWeek(
        array  $params,
        bool   $isSummary = false,
        bool   $notNeedAggregate = false,
        string $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias'],
        string $paymentTable = 't_payment',
        array  $rangeDateCycle = []
    ): array
    {
        $today          = date('Y-m-d');
        $isHasVisualPay = (int)Arr::get($params, 'has_visual_pay', 0);

        if (empty($rangeDateCycle)) {
            ['cycle' => $rangeDateCycle] = TimeUtil::divideWeekByRangeDate(
                Arr::get($params, 'range_date_start', $today),
                Arr::get($params, 'range_date_end', $today)
            );
        }

        $tDayCols = [];

        foreach ($rangeDateCycle as $item) {
            [
                'begin_date' => $begin,
                'end_date'   => $end,
            ] = $item;

            $tDayCols[] = sprintf(
                "when {$mainTable}.tday between '%s' and '%s' then '%s'",
                $begin, $end, $begin . '/' . $end
            );
        }

        $caseString = implode(' ', $tDayCols);
        $resultTDay = [
            'tday' => ['raw' => sprintf(" CONCAT(case %s end) as tday ", $caseString)],
        ];

        $payUserCol = $isHasVisualPay ? 'pay_user_all_week' : 'pay_user_week';

        if (!$isSummary && !$notNeedAggregate) {
            $weekSpecialCol = [
                'active_user' => ['raw' => "CONCAT_WS('|', `{$mainTable}`.`tday`, active_user_week) as active_user"],
                'pay_user'    => ['raw' => "CONCAT_WS('|', `{$mainTable}`.`tday`, {$payUserCol}) as pay_user"],
            ];

            return ColumChanger::changeArrayForCols($resultTDay, $weekSpecialCol, $isSummary, $notNeedAggregate);
        }

        if ($isSummary && !$notNeedAggregate) {
            $result = ColumChanger::changeArrayForCols($resultTDay, [], $isSummary, $notNeedAggregate);

            $specialCol = [
                "SUM(IF(SUBSTRING_INDEX({$mainTable}.tday, '/', 1) = SUBSTRING_INDEX(active_user_week, '|', 1) as active_user",
                "SUM(IF(SUBSTRING_INDEX({$mainTable}.tday, '/', 1) = SUBSTRING_INDEX({$paymentTable}.{$payUserCol}, '|', 1),
                SUBSTRING_INDEX(SUBSTRING_INDEX({$paymentTable}.{$payUserCol}, '|', -1), '|', 1), 0)) AS pay_user",
            ];

            return array_merge($result, $specialCol);
        }

        $weekSpecialCol = [
            'active_user' => ['source' => $mainTable, 'aggregate' => 'sum'],
            'pay_user'    => ['aggregate' => 'sum'],
        ];

        return ColumChanger::changeArrayForCols($resultTDay, $weekSpecialCol, $isSummary, $notNeedAggregate);
    }

    /**
     * 获取有关时间维度而变化的字段(月 维度)
     *
     * @param array  $params
     * @param bool   $isSummary
     * @param bool   $notNeedAggregate
     * @param string $mainTable
     * @param string $paymentTable
     *
     * @return array
     */
    protected function getColForTDayByMonth(
        array  $params,
        bool   $isSummary = false,
        bool   $notNeedAggregate = false,
        string $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias'],
        string $paymentTable = 't_payment'
    ): array
    {
        $isHasVisualPay = (int)Arr::get($params, 'has_visual_pay', 0);

        $resultTDay = [
            'tday' => ['raw' => "DATE_FORMAT(STR_TO_DATE({$mainTable}.tday,'%Y%m'), '%Y-%m') as tday"],
        ];

        $monthSpecialCol = [
            'active_user' => ['source' => $mainTable, 'source_field' => 'active_user_month', 'aggregate' => 'sum'],
        ];

        $payUserCol                  = $isHasVisualPay ? 'pay_user_all_month' : 'pay_user_month';
        $monthSpecialCol['pay_user'] = ['source' => $paymentTable, 'source_field' => $payUserCol, 'aggregate' => 'sum'];

        return ColumChanger::changeArrayForCols($resultTDay, $monthSpecialCol, $isSummary, $notNeedAggregate);
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return array|false
     * @todo 和首登维度操作一样建议抽出
     *
     */
    public function getInfoByFixedDate(
        array $params = [], array $groups = [], array $columns = []
    )
    {
        $dateDimension = (int)Arr::get($params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        if ($dateDimension === 3) {
            [
                'begin' => $rangeDateStart,
                'end'   => $rangeDateEnd,
            ] = TimeUtil::divideWeekByRangeDate(
                Arr::pull($params, 'range_date_start'),
                Arr::pull($params, 'range_date_end')
            );

            $params['range_date_start'] = $rangeDateStart;
            $params['range_date_end']   = $rangeDateEnd;
        }

        return $this->fetchAll(($this->sqlInfoByFixedDate(...func_get_args()))->toSql());
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return array|false
     * @throws \Exception
     */
    public function getSummaryInfoByFixedDate(
        array $params = [], array $groups = [], array $columns = []
    )
    {
        $dateDimension = (int)Arr::get($params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        if ($dateDimension === 3) {
            [
                'begin' => $rangeDateStart,
                'end'   => $rangeDateEnd,
            ] = TimeUtil::divideWeekByRangeDate(
                Arr::pull($params, 'range_date_start'),
                Arr::pull($params, 'range_date_end')
            );

            $params['range_date_start'] = $rangeDateStart;
            $params['range_date_end']   = $rangeDateEnd;
        }

        $baseInfoScheme = $this->sqlInfoByFixedDate(...func_get_args());

        $summaryScheme = (new BaseScheme())
            ->from($baseInfoScheme)
            ->scope($this->buildColumn(
                ColumChanger::changeArrayForCols([], $columns, true, true)
            ))
            ->scope(function (&$query) {
                if (!$query instanceof SelectInterface) return;
                $query->groupBy(['tday']);
            });

        return $this->fetchAll($summaryScheme->toSql());
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $columns
     *
     * @return PackageBaseDailyScheme
     */
    protected function sqlInfoByFixedDate(
        array $params = [], array $groups = [], array $columns = []
    ): PackageBaseDailyScheme
    {
        $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias'];

        $groupMap = [
            'tday'            => $mainTable . '.tday',
            'cp_game_id'      => $mainTable . '.cp_game_id',
            'game_id'         => $mainTable . '.game_id',
            'package_id'      => $mainTable . '.package_id',
            'channel_id'      => 'POWER.channel_id',
            'channel_main_id' => 'POWER.channel_main_id',
            'day_type'        => $mainTable . '.day_type',
            'app_show_id'     => 'POWER.app_show_id',
        ];

        if (empty($columns)) {
            $columns = [
                'tday'            => ['source' => 't_base'],
                'cost_discount'   => ['source' => 't_base', 'aggregate' => 'sum'],
                'cost'            => ['source' => 't_base', 'aggregate' => 'sum'],
                'firstlogin_user' => ['source' => 't_base', 'aggregate' => 'sum'],
                'new_user'        => ['source' => 't_base', 'aggregate' => 'sum'],
                'app_show_id'     => ['source' => 'POWER'],
                'channel_id'      => ['source' => 'POWER'],
                'channel_main_id' => ['source' => 'POWER'],
                'department_id'   => ['source' => 'POWER', 'source_field' => 'ad_department_id'],
                'user_id'         => ['source' => 'POWER', 'source_field' => 'ad_user_id'],
                'platform_id'     => ['source' => 'POWER'],
                'promotion_id'    => ['source' => 'POWER', 'source_field' => 'popularize_v2_id'],
            ];
        }

        if (!in_array('tday', $groups)) $groups[] = 'tday';

        $powerSql = \Plus::$service->admin->getAdminPowerSql();
        $scheme   = PackageBaseDailyScheme::NewOne()->select();
        $scheme
            ->joinPowerSql($powerSql)
            ->scope(TableBaseHelp::setGroup(TableBaseHelp::changeGroups($groups, $groupMap)))
            ->scope(TableBaseHelp::setColumn(ColumChanger::changeArrayForCols([], $columns)));

        (new FirstLoginMatcher($scheme->fieldReflect()))
            ->setParams($params)
            ->execute($scheme);

        return $scheme;
    }


    /**
     * 获取有关时间维度而变化的字段(天 维度)
     *
     * @param array  $params
     * @param bool   $isSummary
     * @param bool   $notNeedAggregate
     * @param string $mainTable
     * @param string $paymentTable
     *
     * @return array
     */
    protected function getColForTDayByDay(
        array  $params,
        bool   $isSummary = false,
        bool   $notNeedAggregate = false,
        string $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias'],
        string $paymentTable = 't_payment'
    ): array
    {
        $resultTDay     = [
            'tday' => ['source' => $mainTable,],
        ];
        $isHasVisualPay = (int)Arr::get($params, 'has_visual_pay', 0);
        $payUserCol     = $isHasVisualPay ? 'pay_user_all' : 'pay_user';
        $specialCol     = [
            'active_user' => ['source' => $mainTable, 'aggregate' => 'sum'],
            'pay_user'    => ['source' => $paymentTable, 'source_field' => $payUserCol, 'aggregate' => 'sum'],
        ];

        return ColumChanger::changeArrayForCols($resultTDay, $specialCol, $isSummary, $notNeedAggregate);
    }

    /**
     * 组装除了tday的需要查询的字段
     *
     * @param array  $params
     * @param bool   $isSummary
     * @param bool   $notNeedAggregate
     * @param string $mainTable
     *
     * @return void
     */
    protected function getCols(
        array  &$params = [],
        bool   $isSummary = false,
        bool   $notNeedAggregate = false,
        string $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias']
    ): array
    {
        $powerTable     = 'POWER';
        $paymentTable   = 't_payment';
        $isHasVisualPay = (int)Arr::pull($params, 'has_visual_pay', 0);
        $dateDimension  = Arr::get($params, 'range_date_dimension', ConstFirstLogin::DIMENSION_DAY);

        $fixedIndex = [
            'cp_game_id'      => ['source' => $mainTable],
            'game_id'         => ['source' => $mainTable],
            'package_id'      => ['source' => $mainTable],
            'app_show_id'     => ['source' => $powerTable],
            'channel_main_id' => ['source' => $powerTable],
            'channel_id'      => ['source' => $powerTable],
            'platform_id'     => ['source' => $powerTable],
            'promotion_id'    => ['source' => $powerTable, 'source_field' => 'popularize_v2_id'],
            'department_id'   => ['source' => $powerTable, 'source_field' => 'ad_department_id'],
            'user_id'         => ['source' => $powerTable, 'source_field' => 'ad_user_id'],
            'is_multiple'     => ['source' => $mainTable],
        ];

        $calcIndex = [
            // base表指标字段
            'activate_device' => ['source' => $mainTable, 'aggregate' => 'sum'],
            'cost'            => ['source' => $mainTable, 'aggregate' => 'sum'],
            'cost_discount'   => ['source' => $mainTable, 'aggregate' => 'sum'],
            'create_role'     => ['source' => $mainTable, 'aggregate' => 'sum'],
            'new_user'        => ['source' => $mainTable, 'aggregate' => 'sum'],
            'update_time'     => ['source' => $mainTable, 'aggregate' => 'max'],
            //            'new_device'                => ['source' => $mainTable, 'aggregate' => 'sum'],
            //            'reg_device'                => ['source' => $mainTable, 'aggregate' => 'sum'],
            //            'new_real_user'             => ['source' => $mainTable, 'aggregate' => 'sum'],
            //            'new_real_user_der'         => ['source' => $mainTable, 'aggregate' => 'sum'],
            //            'reg_user'                  => ['source' => $mainTable, 'aggregate' => 'sum'],
            //            'new_user_imulator'         => ['source' => $mainTable, 'aggregate' => 'sum'],
            //            'active_user_imulator'      => ['source' => $mainTable, 'aggregate' => 'sum'],
            //            'active_user_imulator_bind' => ['source' => $mainTable, 'aggregate' => 'sum'],
        ];

        $isHasVisualPay
            ? $calcIndex = array_merge($calcIndex, $this->getColsForPaymentOnHasVisual($paymentTable))
            : $calcIndex = array_merge($calcIndex, $this->getColsForPaymentOnNotHasVisual($paymentTable));

        // 非汇总行时，查询月度报表时减去最新max_data_day_ago天实时报表
        if (!$isSummary && $params['range_date_dimension'] == 4) {
            $calcIndex = ColumChanger::ltvRoiLoginMonth($calcIndex, $params['max_data_day_ago'], $params['range_date_end']);
        }

        return ColumChanger::changeArrayForCols($fixedIndex, $calcIndex, $isSummary, $notNeedAggregate);
    }

    /**
     * 含虚拟币支付的付费字段
     *
     * @param $paymentTable
     *
     * @return array[]
     */
    private function getColsForPaymentOnHasVisual($paymentTable): array
    {
        return [
            'pay_money'     => ['source' => $paymentTable, 'aggregate' => 'sum', 'source_field' => 'pay_money_all'],
            'pay_new_user'  => ['source' => $paymentTable, 'aggregate' => 'sum', 'source_field' => 'pay_user_newlogin_all'],
            'pay_money_new' => ['source' => $paymentTable, 'aggregate' => 'sum', 'source_field' => 'pay_money_newlogin_all'],
            'order_count'   => ['source' => $paymentTable, 'aggregate' => 'sum', 'source_field' => 'order_count_all'],
            'order_success' => ['source' => $paymentTable, 'aggregate' => 'sum', 'source_field' => 'order_success_all'],
        ];
    }

    /**
     * 不含虚拟币支付的付费字段
     *
     * @param $paymentTable
     *
     * @return array
     */
    private function getColsForPaymentOnNotHasVisual($paymentTable): array
    {
        return [
            'pay_money'     => ['source' => $paymentTable, 'aggregate' => 'sum',],
            'pay_new_user'  => ['source' => $paymentTable, 'aggregate' => 'sum', 'source_field' => 'pay_user_newlogin'],
            'pay_money_new' => ['source' => $paymentTable, 'aggregate' => 'sum', 'source_field' => 'pay_money_newlogin'],
            'order_count'   => ['source' => $paymentTable, 'aggregate' => 'sum',],
            'order_success' => ['source' => $paymentTable, 'aggregate' => 'sum',],
        ];
    }

    /**
     * @param array  $params
     * @param array  $dateCycle
     * @param string $mainTable
     *
     * @return array
     */
    protected function getSubColsForWeek(
        array  $params,
        array  $dateCycle = [],
        string $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias']
    ): array
    {
        $tDayCols = [];

        foreach ($dateCycle as $item) {
            ['begin_date' => $begin, 'end_date' => $end] = $item;
            $tDayCols[] = sprintf(
                "when {$mainTable}.tday between '%s' and '%s' then '%s'",
                $begin, $end, $begin . '/' . $end
            );
        }
        $caseString = implode(' ', $tDayCols);
        $tDayString = sprintf(" CONCAT(case %s end) as tday ", $caseString);

        $isHasVisualPay = (int)Arr::get($params, 'has_visual_pay', 0);

        $result = [
            $tDayString,
            $mainTable . '.cp_game_id',
            $mainTable . '.game_id',
            $mainTable . '.package_id',
            'order_success_all as order_success_all',
            "{$mainTable}.is_multiple",
            'activate_device',
            'cost',
            'cost_discount',
            'active_user_7_days_ago',
            'create_role',
            'new_user',
            'new_device',
            'new_real_user',
            'new_real_user_der',
            'reg_user',
            'reg_device',
            'active_user_imulator',
            'active_user_imulator_bind',
            'POWER.channel_id',
            'POWER.channel_main_id',
            'POWER.ad_department_id as department_id',
            'POWER.ad_user_id as user_id',
            'POWER.popularize_v2_id as promotion_id',
            'POWER.app_show_id',
            'POWER.platform_id',
            "{$mainTable}.update_time",
            'new_user_imulator',
            "CONCAT_WS('|', {$mainTable}.tday, active_user_week) as active_user",
        ];

        if ($isHasVisualPay) {
            $specialCol = [
                "pay_user_newlogin_all as pay_new_user",
                "pay_money_newlogin_all as pay_money_new",
                "pay_money_all as pay_money",
                "CONCAT_WS('|', {$mainTable}.tday, pay_user_all_week) as pay_user",
                'order_success_all as order_success',
                'order_count_all as order_count',
                'pay_money_reg_all as pay_money_reg',
            ];
        }
        else {
            $specialCol = [
                "pay_user_newlogin as pay_new_user",
                "pay_money_newlogin as pay_money_new",
                "pay_money",
                "CONCAT_WS('|', {$mainTable}.tday, pay_user_week) as pay_user",
                'order_success',
                'order_count',
                'pay_money_reg',
            ];
        }

        return array_merge($result, $specialCol);
    }

    /**
     * @param string $sql
     *
     * @return array|false
     */
    private function fetchAll(string $sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * @param $sql
     *
     * @return mixed
     */
    private function fetch($sql)
    {
        return \Plus::$app->ddc_platform->query($sql)->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * @param $aggregate
     *
     * @return bool
     */
    private function isCalcValid($aggregate): bool
    {
        return in_array($aggregate, ['sum', 'avg']);
    }

    /**
     * @param string $mainTable
     *
     * @return string[]
     */
    protected function groupsReflect(string $mainTable = PackageBaseDailyScheme::MAIN_TABLE['alias']): array
    {
        return [
            'tday'            => $mainTable . '.tday',
            'cp_game_id'      => $mainTable . '.cp_game_id',
            'game_id'         => $mainTable . '.game_id',
            'package_id'      => $mainTable . '.package_id',
            'day_type'        => $mainTable . '.day_type',
            'channel_id'      => 'POWER.channel_id',
            'channel_main_id' => 'POWER.channel_main_id',
            'app_show_id'     => 'POWER.app_show_id',
        ];
    }
}
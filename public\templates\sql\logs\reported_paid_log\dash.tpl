{include file="sql/logs/reported_paid_log/dash_body.tpl"}
select
    t1.*,
    t2.main_account as source_core_account,
    t2.plan_id      as source_plan_id,
    t2.channel_id   as source_channel_id,
    t2.cp_game_id,
    t2.package_id   as source_package_id
from ddc_platform.dwd_sdk_adsource_game t2 right join (
    select
    a1.*,
    a2.source_id,
    a2.core_account as paid_core_account,
    a2.package_id   as paid_package_id,
    a2.money as order_money
from log_order_list a1 join increment_order_list a2 on a1.order_id = a2.order_id
) t1 on t1.source_id = t2.source_id
{if !empty($params)}
    {assign var="first_mark_3" value=1}
    {foreach $params as $kk => $foo}
        {* 核心账号(归因) *}
        {if $kk eq "source_core_account"}
            {if !$first_mark_3} and {else} where {$first_mark_3=0} {/if}
            {if is_array($foo)}
                t2.main_account in ('{$foo|join:'\' ,\''}')
            {else}
                t2.main_account = '{$foo}'
            {/if}
            {continue}
        {/if}
        {* 归因计划 *}
        {if $kk eq "plan_name"}
            {if !$first_mark_3} and {else} where {$first_mark_3=0} {/if}
            t2.plan_id in (select distinct plan_id from adp_platform.tb_adp_plan_base where plan_name like '%{$foo}%')
        {/if}
        {* 归因包号 *}
        {if $kk eq "source_package_id"}
            {if !$first_mark_3} and {else} where {$first_mark_3=0} {/if}
            {if is_array($foo)}
                t2.package_id in ('{$foo|join:'\' ,\''}')
            {else}
                t2.package_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {* 归因渠道 *}
        {if $kk eq "source_channel_id"}
            {if !$first_mark_3} and {else} where {$first_mark_3=0} {/if}
            {if is_array($foo)}
                t2.channel_id in ('{$foo|join:'\' ,\''}')
            {else}
                t2.package_id = '{$foo}'
            {/if}
        {/if}
    {/foreach}
{/if}
{* 排序 *}
{if !empty($sorts)}
    order by
    {foreach $sorts as $ss => $oo}
        {$ss} {$oo}
        {if !$oo@last}, {/if}
    {/foreach}
{/if}
{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}
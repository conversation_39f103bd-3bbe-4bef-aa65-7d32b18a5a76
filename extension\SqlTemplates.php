<?php

namespace app\extension;

use app\extension\Faker\FakeRabbit\Configuration;
use Smarty\Exception;
use Smarty\Smarty;


class SqlTemplates extends Smarty
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     * @throws Exception
     */
    public function init()
    {
        $config     = get_object_vars($this);
        $config     = $config['locate_conf'];
        $templates  = APP_ROOT . ($config['templates'] ?? 'public/templates');
        $templatesC = APP_ROOT . ($config['templates_c'] ?? 'public/templates_c');

        $this->setTemplateDir($templates);
        $this->setCompileDir($templatesC);
        if (APP_EVN == 'PRO') {
            // 线上不检查模板更新
            $this->setCompileCheck(\Smarty\Smarty::COMPILECHECK_OFF);
        }
        $this->registerPlugin('modifier', 'array_intersect', function ($array1, $array2) {
            return array_intersect($array1, $array2);
        });
    }
}
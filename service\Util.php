<?php

namespace app\service;

use DateTime;
use Plus\Net\Http;

/**
 * 工具函数
 * @package app\service
 * <AUTHOR>
 */
class Util
{
    const OA_API_KEY = 'TgqNwoywmScP2lQ4YiZt';

    /**
     * 发送企业微信通知
     * @param string $oaid    用户oa系统id
     * @param string $title   标题
     * @param string $content 内容
     * @param string $type    格式 text|markdown
     * @return mixed
     * @throws \Exception
     */
    public static function sendWXAppMsg($oaid, $title, $content, $type = 'text')
    {
        $uri   = 'https://newoa.910app.com/newoaApi/openapi/auth/sendWXAppMsg';
        $t     = time();
        $key   = self::OA_API_KEY;
        $query = [
            'name' => 'CRM', // todo
            'sign' => md5('CRM' . $t . $key),
            'time' => $t,
        ];
        $uri   = $uri . '?' . http_build_query($query);
        $curl  = new Http($uri);
        $res   = $curl->postJson(json_encode([
            'contentType' => $type,
            'peopleIds'   => [$oaid],
            'title'       => $title,
            'content'     => $content,
        ], JSON_UNESCAPED_UNICODE));
        return json_decode($res, true);
    }

    /**
     * oa员工列表
     * @return mixed
     * @throws \Exception
     */
    public static function getOaUserList()
    {
        $uri   = 'https://newoa.910app.com/newoaApi/openapi/auth/people/list';
        $t     = time();
        $key   = self::OA_API_KEY;
        $query = [
            'name' => 'CRM', // todo
            'sign' => md5('CRM' . $t . $key),
            'time' => $t,
        ];
        $uri   = $uri . '?' . http_build_query($query);
        $curl  = new Http($uri);
        $res   = $curl->get();
        return json_decode($res, true);
    }
}

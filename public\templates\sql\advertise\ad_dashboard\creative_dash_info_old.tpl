with dashboard_info as (
select tday,
a1.cp_game_id,
a1.game_id,
a1.package_id,
{if isset($ad_channels) }
    COALESCE( IF(power.channel_id not in ({$ad_channels|join:','}), power.channel_id, IF(a1.channel_id != 0, IF(a1.channel_id =1013, 4, a1.channel_id), power.channel_id) ) ,0) as promotion_channel_id,
    COALESCE( IF(power.channel_id not in ({$ad_channels|join:','}), power.ad_user_id, IF(a1.user_id != 0, a1.user_id, power.ad_user_id)),0 ) as dim_user_id,
{else}
    a1.channel_id as promotion_channel_id,
    a1.user_id as dim_user_id,
{/if}
a1.campaign_id,
a1.plan_id,
a1.creative_id,
a1.main_channel_id,
a1.account_id,
a2.ad_account,
any(power.platform_id) as platform_id,
any(a4.advertiser_name) as account_name,
any(a1.is_ad_data) as is_ad_data,
any(a1.is_appointment) as is_appointment,
any(a1.marketing_goal) as marketing_goal,
any(case
    when user_os = '["ANDROID"]' then 'ANDROID'
    when user_os = '["IOS"]' then 'IOS'
    else '混投' end)                                                           as dim_user_os,
any(coalesce(a3.campaign_name,''))                                            as campaign_name,
any(power.popularize_v2_id)                                                   as promotion_id,
any(power.app_show_id)                                                        as app_show_id,
sum(`show`)                                                                   as show_cnt,
sum(click)                                                                    as click_cnt,
sum(download)                                                                 as download_cnt,
sum(activate)                                                                 as activate_cnt,
sum(`convert`)                                                                as convert_cnt,
sum(`install`)                                                                as install_cnt,
sum(lp_view)                                                                  as lp_view,
sum(lp_download)                                                              as lp_download,
sum(download_start)                                                           as download_start,
sum(register)                                                                 as register,
sum(cost)                                                                     as cost,
sum(cost_discount)                                                            as cost_discount,
sum(new_real_user)                                                            as new_real_user,
sum(new_user)                                                                 as new_user,
sum(new_user_emulator)                                                        as new_user_emulator,
sum(activate_device)                                                          as activate_device,
sum(create_role_new)                                                          as create_role_new,
sum(pay_new_user_7days)                                                       as pay_new_user_7days,
sum(pay_frequency_7days)                                                      as pay_frequency_7days,
sum(online_time)                                                              as online_time,
sum(first_online_time)                                                        as first_online_time,
sum(active_user)                                                              as active_user,
sum(active_user_week)                                                         as active_user_week,
sum(total_play)                                                               as total_play,
sum(play_time_per_play)                                                       as play_time_per_play,
sum(play_duration_3s)                                                         as play_duration_3s,
sum(pay_user)                                                                 as pay_user,
sum(pay_money)                                                                as pay_money,
sum(pay_count)                                                                as pay_count,
sum(pay_user_new)                                                             as pay_user_new,
sum(pay_money_new)                                                            as pay_money_new,
sum(pay_count_new)                                                            as pay_count_new,
sum(pay_money_no_visual)                                                      as pay_money_no_visual,
sum(pay_money_new_no_visual)                                                  as pay_money_new_no_visual,
max(a1.update_time)                                                           as update_time
from bigdata_dws.dws_ad_creative_daily a1
{if !empty($power_join_sql) && $power_join_sql == 'base_conf_platform.tb_package_detail_conf'}
    join ({$power_join_sql}) power on a1.package_id = power.package_id
{else}
    join base_conf_platform.tb_package_detail_conf power on a1.package_id = power.package_id
{/if}
left join base_conf_platform.tb_ad_account_conf a2 on a1.account_id = a2.account_id
left join (
select
campaign_id,
channel_id,
campaign_name,
cast(user_os as string) as user_os from adp_platform.tb_adp_campaign where campaign_id in (
select campaign_id from bigdata_dws.dws_ad_creative_daily
where
{if !empty($params['range_date'])}tday between '{$params['range_date'][0]}' and '{$params['range_date'][1]}'{/if}
group by campaign_id)
) a3 on a1.main_channel_id = a3.channel_id and a1.campaign_id = a3.campaign_id
left join adp_platform.tb_adp_oauth a4 on a1.account_id = a4.advertiser_id and a1.main_channel_id = a4.channel_id
{if !empty($params)}
    {assign var="tag_first_where" value=1}
    where
    {foreach $params as $kk => $foo}
        {if $kk eq 'range_date'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            a1.tday between '{$foo[0]}' and '{$foo[1]}'
            {continue}
        {/if}
        {if $kk eq 'cp_game_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if is_array($foo)}
                a1.cp_game_id in ({$foo|join:','})
            {else}
                a1.cp_game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'game_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if is_array($foo)}
                a1.game_id in ({$foo|join:','})
            {else}
                a1.game_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'app_show_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.app_show_id in ({$foo|join:','})
            {else}
                power.app_show_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'platform_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.platform_id in ({$foo|join:','})
            {else}
                power.platform_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'package_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if is_array($foo)}
                a1.package_id in ({$foo|join:','})
            {else}
                a1.package_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'promotion_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if is_array($foo)}
                power.popularize_v2_id in ({$foo|join:','})
            {else}
                power.popularize_v2_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'ad_account'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            a2.ad_account like '{'%'|cat:$foo|cat:'%'}'
            {continue}
        {/if}
        {if $kk eq 'account_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            a1.account_id like  '{'%'|cat:$foo|cat:'%'}'
            {continue}
        {/if}
        {if $kk eq 'campaign_name'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            a1.campaign_id in (select distinct campaign_id from adp_platform.tb_adp_campaign where campaign_name like '{'%'|cat:$foo|cat:'%'}')
            {continue}
        {/if}
        {if $kk eq 'campaign_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if is_array($foo)}
                a1.campaign_id in ({$foo|join:','})
            {else}
                a1.campaign_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'plan_name'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            (
            a1.plan_id in (select distinct plan_id from adp_platform.tb_adp_plan_base where plan_name like '{'%'|cat:$foo|cat:'%'}')
            or a1.plan_id in (select distinct id  from dataspy.tb_ad_svlink_conf where aid like '{'%'|cat:$foo|cat:'%'}')
            )
            {continue}
        {/if}
        {if $kk eq 'plan_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if is_array($foo)}
                a1.plan_id in ({$foo|join:','})
            {else}
                a1.plan_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'creative_id'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if is_array($foo)}
                a1.creative_id in ({$foo|join:','})
            {else}
                a1.creative_id = '{$foo}'
            {/if}
            {continue}
        {/if}
        {if $kk eq 'data_scope'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if $foo eq 1}
                a1.is_ad_data = 1
            {elseif $foo eq 2}
                a1.is_ad_data = 0
            {/if}
            {continue}
        {/if}
        {if $kk eq 'marketing_goal'}
            {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
            {if $foo neq [1,2] || $foo neq [2,1]}
                {if in_array(1, $foo)}
                    a1.marketing_goal != 2
                {/if}
                {if in_array(2, $foo)}
                    a1.marketing_goal = 2
                {/if}
            {/if}
            {continue}
        {/if}
        {if $kk eq 'is_has_appointment'}
            {if empty($foo)} {if !$tag_first_where} and {else} {$tag_first_where=0} {/if} a1.is_appointment != 1 {/if}
            {continue}
        {/if}
        {if $kk eq 'is_has_natural'}
            {if empty($foo)} {if !$tag_first_where} and {else} {$tag_first_where=0} {/if} a1.channel_id > 0 {/if}
            {continue}
        {/if}
        {if $kk eq 'user_os'}
            {if is_array($foo)}
                {if !$tag_first_where} and {else} {$tag_first_where=0} {/if}
                (
                {foreach $foo as $ii => $chill}
                    {if !$chill@first} or {/if}
                    {if $chill eq 1} a3.user_os =  '["IOS"]'{/if}
                    {if $chill eq 2} a3.user_os =  '["ANDROID"]'{/if}
                    {if $chill eq 3} (a3.user_os != '["IOS"]' and a3.user_os != '["ANDROID"]'){/if}
                {/foreach}
                )
            {/if}
        {/if}
    {/foreach}
{/if}
group by
tday, a1.cp_game_id, a1.game_id, a1.package_id, promotion_channel_id, a1.campaign_id, a1.plan_id,
a1.creative_id, a1.main_channel_id, a1.account_id, dim_user_id, a2.ad_account
)
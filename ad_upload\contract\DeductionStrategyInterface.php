<?php

namespace app\ad_upload\contract;

use app\ad_upload\tool\Proxy2BD;
use app\util\BizConfig;

/**
 * 扣量上报策略接口
 * <AUTHOR>
 */
abstract class DeductionStrategyInterface
{
    /**
     * 配置
     * @var array
     */
    protected array $config;
    /**
     * 数据
     * @var array
     */
    protected array $data;

    /**
     * 获取数据
     * @return array
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * 初始化配置、数据
     * @param array $config 配置
     * @param array $data   数据
     * @return void
     */
    public function init($config, $data)
    {
        $this->config = $config;

        $data['REPORT_RULE_ID']   = $this->config['id'] ?? '';
        $data['REPORT_RULE_NAME'] = $this->config['name'] ?? '';
        $data['REPORT_TYPE']      = $this->config['kind'] ?? '';

        $this->data = $data;
    }

    /**
     * 通用的时间检查
     * @return bool
     */
    public function timeCheck():bool
    {
        $conf = $this->config['config_data'];
        $data = $this->data;

        $payTime        = new \DateTime($data['PAY_TIME']);
        $rangeOrderTime = $conf['order_time_range'];
        //订单时间区间不一定配置有
        if (empty($rangeOrderTime)) {
            return true;
        }
        sort($rangeOrderTime);
        foreach ($rangeOrderTime as &$foo) {
            $foo = new \DateTime($foo);
        }
        if (!isset($rangeOrderTime[1])) {
            \Plus::$app->log->alert('扣量规则配置错误'.json_encode($conf), [], AdBaseInterface::LOG_DIR);
            return true;
        }

        if ($payTime >= $rangeOrderTime[0] && $payTime <= $rangeOrderTime[1]) {
            return true;
        }
        return false;
    }

    /**
     * 返回是否通过扣量检查
     * @return bool
     */
    abstract public function isPass(): bool;

    /**
     * 上报大数据
     * @return void
     */
    public function reportBigData()
    {
        $data       = $this->data;
        $params     = [
            'data_sign' => 'sdk_v2',
            'key'       => 'payment_intercept',
            'data'      => [
                'core_account'     => $data['CORE_ACCOUNT'] ?? '',
                'game_id'          => $data['GAME_ID'] ?? '',
                'package_id'       => $data['PACKAGE_ID'] ?? '',
                'os'               => $data['OS'] ?? '',
                'oaid'             => $data['OAID'] ?? '',
                'device_key'       => $data['DEVICE_KEY'] ?? '',
                'device_code'      => $data['DEVICE_CODE'] ?? '',
                'money'            => $data['MONEY_ACTUALY'] ?? ($data['MONEY'] ?? 0.00),
                'money_report'     => $data['MONEY_REPORT'] ?? ($data['MONEY'] ?? 0.00),
                'order_id'         => $data['ORDER_ID'] ?? '',
                'role_vip'         => $data['ROLE_VIP'] ?? '',
                'role_id'          => $data['ROLE_ID'] ?? '',
                'role_name'        => $data['ROLE_NAME'] ?: '',
                'role_rank'        => $data['ROLE_RANK'] ?: '',
                'game_server'      => $data['GAME_SERVER_ID'] ?: '',
                'pay_time'         => $data['PAY_TIME'] ?: '',
                'pay_result'       => 1,
                'report_rule_id'   => $data['REPORT_RULE_ID'] ?: 0,
                'report_rule_name' => $data['REPORT_RULE_NAME'] ?: '',
                'report_type'      => $data['REPORT_TYPE'] ?: -1,
                'report_time'      => time(),
                'ext'              => [
                    'source' => 'cltj_upload_ad',
                ],
            ],
        ];
        $request2BD = new Proxy2BD();
        ;
        $request2BD->sdkSendV2($params);
    }
}

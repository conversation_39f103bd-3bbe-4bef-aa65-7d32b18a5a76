select
{if $is_summary|isset}
    '汇总'
{else} dt {/if} as tday, a1.game_id, a2.cp_game_id,
sum(pull_cnt)                                              as pull_cnt,
sum(exposure_cnt)                                          as exposure_cnt,
round(SUM(exposure_cnt) / SUM(pull_cnt) * 100, 2)          as exposure_percent,
sum(click_cnt)                                             as click_cnt,
round(SUM(click_cnt) / SUM(exposure_cnt) * 100, 2)         as click_percent,
round(SUM(income_rmb) * 1000 / SUM(exposure_cnt), 2)       as ecpm,
sum(income_rmb)                                            as income_rmb
from origin_platform.tb_ad_income_crawler a1 left join  base_conf_platform.tb_base_game_conf a2 on a1.game_id = a2.game_id
{include file="sql/advertise/ad_income_dash/dash_match.tpl"}

{if $group_by|isset}
    GROUP BY {$group_by}
{/if}

{* 排序 *}
{if $sort|isset}
    order by
    {$is_first=1}
    {foreach $sort as $k => $foo}
        {if $is_first eq 1}
            {$k} {$foo}
            {$is_first=0}
        {else}
            , {$k} {$foo}
        {/if}
    {/foreach}
{/if}

{* 分页 *}
{if !empty($paginate)}
    {assign var=page_size value=$paginate.page_size}
    {assign var=page value=$paginate.page}
    limit {$page_size} offset {($page-1) * $page_size}
{/if}


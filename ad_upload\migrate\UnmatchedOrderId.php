<?php

namespace app\ad_upload\migrate;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\strategies\PayUploadStrategy;
use app\ad_upload\tool\CacheUploadAd;
use app\ad_upload\tool\CommonFunc;
use app\ad_upload\tool\CustomProcess;

/**
 * 未匹配订单号字段数据中, 移除其他渠道的订单号
 * 仅针对小游戏渠道
 *
 * php cli.php -f ad_upload/migrate/UnmatchedOrderId.php -s debug --p 'channel_id=70'
 * <AUTHOR>
 */
class UnmatchedOrderId extends CustomProcess
{
    /**
     * 渠道id
     * @var int
     */
    public $channel_id = 0;

    /**
     * run
     * @return void
     */
    public function run()
    {
        $action        = AdBaseInterface::ACTION_PAY;
        $cache         = new CacheUploadAd();
        $orderIds      = $cache->getUnmatchedIds($action, $this->channel_id);
        $orderIdString = CommonFunc::arr2Str($orderIds);
        $sql           = "
        select 
           t1.ORDER_ID,
           t3.CHANNEL_ID,
           t3.CLICK_ID
        from origin_platform.tb_sdk_user_payment t1
         left join ddc_platform.dwd_sdk_user_payment t2 on t1.order_id = t2.order_id
         left join ddc_platform.dwd_sdk_adsource_game t3 on t2.source_id = t3.source_id
         where t1.order_id in ({$orderIdString})";
        $data          = \Plus::$app->origin_platform->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $rs            = [];
        foreach ($data as $v) {
            if ($v['CHANNEL_ID'] == $this->channel_id) {
                $rs[] = $v['ORDER_ID'];
            }
        }
        $cache->setUnmatchedIds($this->channel_id, $rs, $action);
    }
}

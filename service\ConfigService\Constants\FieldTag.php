<?php

namespace app\service\ConfigService\Constants;

use app\extension\Support\Collections\Collection;

class FieldTag
{
    /**
     * ### 基础信息
     */
    const BASE_INFO = 'base_info';

    /**
     * ### 广告信息
     */
    const AD_INFO = 'ad_info';

    /**
     * ### 新用户基础指标
     */
    const NEW_USER_BASE_INFO = 'new_user_base';

    /**
     * 新用户转化指标(标识)
     */
    const NEW_USER_CONVERT = 'new_user_convert';

    /**
     * ### ltv 分组1 [ltv1-30]
     */
    const LTV_GROUP_1 = 'ltv_group_1';

    /**
     * ### ltv 分组2 [ltv45-180]
     */
    const LTV_GROUP_2 = 'ltv_group_2';

    /**
     * ### LTV分组3 [ltv210-360]
     */
    const LTV_GROUP_3 = 'ltv_group_3';

    /**
     * LTV分组4[ltv390-720]
     */
    const LTV_GROUP_4 = 'ltv_group_4';

    /**
     * ### roi 分组1 [roi1-30]
     */
    const ROI_GROUP_1 = 'roi_group_1';

    /**
     * ### roi 分组2 [roi45-180]
     */
    const ROI_GROUP_2 = 'roi_group_2';

    /**
     * ### roi [roi210-360]
     */
    const ROI_GROUP_3 = 'roi_group_3';

    /**
     * ### roi分组4[roi390-720]
     */
    const ROI_GROUP_4 = 'roi_group_4';

    /**
     * ### 留存 分组1 [次留 - 30留]
     */
    const REMAIN_GROUP_1 = 'remain_group_1';

    /**
     * ### 留存 分组2 [45留-180留]
     */
    const REMAIN_GROUP_2 = 'remain_group_2';

    /**
     * ### 留存分组3 [210留-360留]
     */
    const REMAIN_GROUP_3 = 'remain_group_3';

    /**
     * ### 留存分组4 [390留-720留]
     *
     */
    const REMAIN_GROUP_4 = 'remain_group_4';


    /**
     * @param Collection $collect
     * @param string     $fieldPrefix
     * @param array      $attrList
     *
     * @return Collection
     */
    public static function tagClassifyToNField(Collection $collect, string $fieldPrefix = '', array $attrList = []): Collection
    {
        return $collect->map(function ($item, $k) use ($fieldPrefix, $attrList) {
            $i = (int)str_replace($fieldPrefix . '_', '', $k);

            foreach ($attrList as $foo) {
                $range = $foo['range'];
                sort($range);

                if ($range[0] <= $i && $i <= $range[1]) {
                    return array_merge($item, array_diff_key($foo, ['range' => '']));
                }
            }

            return $item;
        });
    }

}
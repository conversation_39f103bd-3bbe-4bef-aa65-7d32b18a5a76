<?php

namespace app\apps\media\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\media\MediaAccountLogic;
use app\logic\media\MediaVideoLogic;

class AccountController extends BaseTableController
{
    use ColumnsInteract;

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $paginate = [
            'page'      => $params->pull('page'),
            'page_size' => $params->pull('page_size'),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = [];
        }

        $groups  = Arr::wrap($params->pull('groups'));
        $options = $params->toArray();

        $logic = new MediaAccountLogic();
        return $logic->getInfo($options, $groups, $paginate, $sort);
    }

    /**
     * @param Collection $params
     *
     * @return array[]
     */
    protected function fields(Collection $params): array
    {
        $groups             = $params->get('groups');
        $rangeDateDimension = $params->get('range_date_dimension');

        if ($rangeDateDimension == 4) {
            $classify = [
                [
                    'value'    => 'attrs',
                    'label'    => '属性设置',
                    'children' => [
                        ['value' => 'base', 'label' => '基础信息'],
                        ['value' => 'fans_index', 'label' => '粉丝指标'],
                        ['value' => 'flux_index', 'label' => '流量指标'],
                        ['value' => 'base_task', 'label' => '普通任务指标'],
                        ['value' => 'publish_task', 'label' => '投稿任务指标'],
                        ['value' => 'interact', 'label' => '养号指标'],
                    ],
                ],
            ];

            $info = [
                'tday'                    => ['title' => '统计月份', 'dataIndex' => 'tday', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'media_platform'          => ['title' => '媒体平台', 'dataIndex' => 'media_platform', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'business_ownership_name' => ['title' => '业务归属', 'dataIndex' => 'business_ownership_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'use_kind_name'           => ['title' => '账号归属', 'dataIndex' => 'use_kind_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'account_name'            => ['title' => '账号名称', 'dataIndex' => 'account_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'account_id'              => ['title' => '账号ID', 'dataIndex' => 'account_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'operations_manager'      => ['title' => '运营负责人', 'dataIndex' => 'operations_manager', 'sorter' => true, 'classify' => ['attrs', 'base']],
            ];

            $fields = [
                ['title' => '粉丝量(月初)', 'dataIndex' => 'fans_start_month', 'sorter' => true, 'classify' => ['attrs', 'fans_index']],
                ['title' => '粉丝量(月中)', 'dataIndex' => 'fans_mid_month', 'sorter' => true, 'classify' => ['attrs', 'fans_index']],
                ['title' => '粉丝量(月末)', 'dataIndex' => 'fans_late_month', 'sorter' => true, 'classify' => ['attrs', 'fans_index']],
                ['title' => '月增粉量', 'dataIndex' => 'month_add_fans', 'sorter' => true, 'classify' => ['attrs', 'fans_index']],
                ['title' => '转粉率', 'dataIndex' => 'convert_fans', 'classify' => ['attrs', 'fans_index']],
                ['title' => '涨粉评级', 'dataIndex' => 'up_fans_grading', 'sorter' => true, 'classify' => ['attrs', 'fans_index']],
                ['title' => '当月新增视频平均播放量', 'dataIndex' => 'avg_new_video_play_month', 'sorter' => true, 'classify' => ['attrs', 'flux_index']],
                ['title' => '流量评级', 'dataIndex' => 'flow_rate_grading', 'sorter' => true, 'classify' => ['attrs', 'flux_index']],
                ['title' => '主页访问量', 'dataIndex' => 'homepage_access', 'sorter' => true, 'classify' => ['attrs', 'flux_index']],
                ['title' => '普通任务安排发布量', 'dataIndex' => 'normal_task_post', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '普通任务成功发布量', 'dataIndex' => 'normal_task_post_success', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '普通任务发布成功率', 'dataIndex' => 'normal_task_post_success_rating', 'classify' => ['attrs', 'base_task']],
                ['title' => '当月新增普通视频播放量', 'dataIndex' => 'normal_new_video_play_month', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '当月新增普通视频累计播放量', 'dataIndex' => 'normal_new_video_play', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '当月新增普通视频累计点赞数', 'dataIndex' => 'normal_new_video_like', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '当月新增普通视频累计评论数', 'dataIndex' => 'normal_new_video_comment', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '当月新增普通视频累计转发数', 'dataIndex' => 'normal_new_video_forward', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '所有普通视频当日播放量', 'dataIndex' => 'normal_video_play_month', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '投稿任务安排发布量', 'dataIndex' => 'contri_task_post', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '投稿任务发布成功量', 'dataIndex' => 'contri_task_post_success', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '投稿任务发布成功率', 'dataIndex' => 'contri_task_post_success_rating', 'classify' => ['attrs', 'publish_task']],
                ['title' => '当月新增投稿视频播放量', 'dataIndex' => 'contri_new_video_play_month', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当月新增投稿视频累计播放量', 'dataIndex' => 'contri_new_video_play', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当月新增投稿视频累计点赞数', 'dataIndex' => 'contri_new_video_like', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当月新增投稿视频累计评论数', 'dataIndex' => 'contri_new_video_comment', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当月新增投稿视频累计转发数', 'dataIndex' => 'contri_new_video_forward', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '所有投稿视频当日播放量', 'dataIndex' => 'contri_video_play_month', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当月回复评论数', 'dataIndex' => 'month_comment', 'sorter' => true, 'classify' => ['attrs', 'interact']],
                ['title' => '当月养号刷视频数', 'dataIndex' => 'month_watch', 'sorter' => true, 'classify' => ['attrs', 'interact']],
                ['title' => '当月点赞数', 'dataIndex' => 'month_like', 'sorter' => true, 'classify' => ['attrs', 'interact']],
            ];
        }
        else {
            $classify = [
                [
                    'value'    => 'attrs',
                    'label'    => '属性设置',
                    'children' => [
                        ['value' => 'base', 'label' => '基础信息'],
                        ['value' => 'fans_index', 'label' => '粉丝指标'],
                        ['value' => 'video_center', 'label' => '视频总指标'],
                        ['value' => 'base_task', 'label' => '普通任务指标'],
                        ['value' => 'publish_task', 'label' => '投稿任务指标'],
                        ['value' => 'interact', 'label' => '养号指标'],
                        ['value' => 'fans_portrait', 'label' => '粉丝画像'],
                    ],
                ],
            ];

            $info = [
                'tday'                    => ['title' => '统计日期', 'dataIndex' => 'tday', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'media_platform'          => ['title' => '媒体平台', 'dataIndex' => 'media_platform', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'business_ownership_name' => ['title' => '业务归属', 'dataIndex' => 'business_ownership_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'use_kind_name'           => ['title' => '账号归属', 'dataIndex' => 'use_kind_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'account_name'            => ['title' => '账号名称', 'dataIndex' => 'account_name', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'account_id'              => ['title' => '账号ID', 'dataIndex' => 'account_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
                'operations_manager'      => ['title' => '运营负责人', 'dataIndex' => 'operations_manager', 'sorter' => true, 'classify' => ['attrs', 'base']],
            ];

            $fields = [
                ['title' => '当前粉丝量', 'dataIndex' => 'current_fans', 'sorter' => true, 'classify' => ['attrs', 'fans_index']],
                ['title' => '近1日涨粉量', 'dataIndex' => 'new_fans_1_days_ago', 'sorter' => true, 'classify' => ['attrs', 'fans_index']],
                ['title' => '近7日涨粉量', 'dataIndex' => 'new_fans_7_days_ago', 'sorter' => true, 'classify' => ['attrs', 'fans_index']],
                ['title' => '近30日涨粉量', 'dataIndex' => 'new_fans_30_days_ago', 'sorter' => true, 'classify' => ['attrs', 'fans_index']],
                ['title' => '所有视频当日播放量', 'dataIndex' => 'all_video_play_today', 'sorter' => true, 'classify' => ['attrs', 'video_center']],
                ['title' => '普通任务安排发布量', 'dataIndex' => 'normal_task_post', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '普通任务成功发布量', 'dataIndex' => 'normal_task_post_success', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '普通任务发布成功率', 'dataIndex' => 'normal_task_post_success_rating', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '当日新增普通视频播放量', 'dataIndex' => 'normal_new_video_play_today', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '当日新增普通视频累计播放量', 'dataIndex' => 'normal_new_video_play', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '当日新增普通视频累计点赞数', 'dataIndex' => 'normal_new_video_like', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '当日新增普通视频累计评论数', 'dataIndex' => 'normal_new_video_comment', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '当日新增普通视频累计转发数', 'dataIndex' => 'normal_new_video_forward', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '所有普通视频当日播放量', 'dataIndex' => 'normal_video_play_today', 'sorter' => true, 'classify' => ['attrs', 'base_task']],
                ['title' => '投稿任务安排发布量', 'dataIndex' => 'contri_task_post', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '投稿任务发布成功量', 'dataIndex' => 'contri_task_post_success', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '投稿任务发布成功率', 'dataIndex' => 'contri_task_post_success_rating', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当日新增投稿视频播放量', 'dataIndex' => 'contri_new_video_play_today', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当日新增投稿视频累计播放量', 'dataIndex' => 'contri_new_video_play', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当日新增投稿视频累计点赞数', 'dataIndex' => 'contri_new_video_like', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当日新增投稿视频累计评论数', 'dataIndex' => 'contri_new_video_comment', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当日新增投稿视频累计转发数', 'dataIndex' => 'contri_new_video_forward', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '所有投稿视频当日播放量', 'dataIndex' => 'contri_video_play_today', 'sorter' => true, 'classify' => ['attrs', 'publish_task']],
                ['title' => '当日回复评论数', 'dataIndex' => 'today_comment', 'sorter' => true, 'classify' => ['attrs', 'interact']],
                ['title' => '当日养号刷视频数', 'dataIndex' => 'today_watch', 'sorter' => true, 'classify' => ['attrs', 'interact']],
                ['title' => '当日点赞数', 'dataIndex' => 'today_like', 'sorter' => true, 'classify' => ['attrs', 'interact']],
            ];

            if (
                (
                    empty(array_diff($groups, ['tday', 'account_id', 'media_platform_id', 'business_ownership_id']))
                    && empty(array_diff(['tday', 'account_id', 'media_platform_id', 'business_ownership_id'], $groups))
                )
                || (
                    empty(array_diff($groups, ['tday', 'account_id']))
                    && empty(array_diff(['tday', 'account_id'], $groups))
                )
            ) {
                $specialField = [
                    ['title' => '男粉比例', 'dataIndex' => 'fans_man_percent', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '女粉比例', 'dataIndex' => 'fans_women_percent', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝年龄段top1', 'dataIndex' => 'fans_age_top1', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝年龄段top2', 'dataIndex' => 'fans_age_top2', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝兴趣top1', 'dataIndex' => 'fans_interest_top1', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝兴趣top2', 'dataIndex' => 'fans_interest_top2', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝兴趣top3', 'dataIndex' => 'fans_interest_top3', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝设备top1', 'dataIndex' => 'fans_device_top1', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝设备top2', 'dataIndex' => 'fans_device_top2', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝设备top3', 'dataIndex' => 'fans_device_top3', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝地区top1', 'dataIndex' => 'fans_area_top1', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝地区top2', 'dataIndex' => 'fans_area_top2', 'classify' => ['attrs', 'fans_portrait']],
                    ['title' => '粉丝地区top3', 'dataIndex' => 'fans_area_top3', 'classify' => ['attrs', 'fans_portrait']],
                ];

                $fields = array_merge($fields, $specialField);
            }
        }

        $logic  = new MediaAccountLogic();
        $groups = Arr::wrap($params->pull('groups'));

        if (!empty($groups)) {
            $resetGroupFn = $this->resetGroupsCols($logic->groupRelationMap($groups), $groups, array_keys($info));
        }
        else {
            $resetGroupFn = fn() => true;
        }

        $resetGroupFn($info);
        $fields = array_merge($info, $fields);
        foreach ($fields as $k => &$item) {
            if ($item == '-') {
                $item = null;
            }
        }

        return ['fields' => array_values(array_filter($fields)), 'classify' => $classify];
    }


    protected function registerParams(): Collection
    {
        $nowToday = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start', 'default' => $nowToday],
            ['field' => 'range_date_end', 'default' => $nowToday],
            ['field' => 'media_platform_id'],
            ['field' => 'account_id'],
            ['field' => 'business_ownership'],
            ['field' => 'row_option'],
            ['field' => 'operations_manager'],
            ['field' => 'use_kind'],

            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'range_date_dimension'],
            ['field' => 'groups', 'default' => ['tday', 'account_id', 'media_platform', 'business_ownership_name', 'use_kind_name', 'operations_manager']],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

    /**
     * @return void
     */
    public function audienceAnalyzeAction(): array
    {
        $params     = $this->wrapParams(\Plus::$app->request);
        $rowOptions = \json_decode($params->pull('row_option'), true);

        if (json_last_error() != JSON_ERROR_NONE) {
            return $this->error('JSON解析错误');
        }

        [
            'tday'       => $tDay,
            'account_id' => $accountId,
        ] = $rowOptions;

        $options = [['tday' => $tDay, 'account_id' => $accountId]];

        $logic = new MediaAccountLogic();
        $info  = $logic->audienceAnalyze($options, []);

        return $this->success($info);
    }
}
<?php

/**
 * 广点通微信小游戏数据上报
 * Created by PhpStorm.
 * User: weiqi
 * Date: 2022/04/24
 * Time: 10:35
 * phpcs:disable
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\ChannelFactory;
use app\ad_upload\tool\Http;

class GdtXcx extends AdBaseInterface
{

    /**
     * 上报创角
     * @param array $info
     * @param array $ext
     */
    public function uploadCreateRole($info, $ext = [])
    {
        $this->uploadData($info, 'CREATE');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $params
     * @throws FreeSQLException
     */
    public function uploadPay($info, $params = [])
    {
        $this->uploadData($info, 'PAY');
    }

    /**
     * 广点通只需要创角和付费, 其他行为不用上传, 若上传会导致重复
     * @param        $info
     * @param string $type
     */
    private function uploadData($info, $type = '')
    {
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'gdt_xcx';
        $logInfo['log_type']     = 'reported_platform_log';
        // CLICK_ID 对应 id 字段 origin_platform.tb_ad_click_match_log 拿到 CALLBACK_URL
        if (empty($info['OAID'])) {
            if ($type == 'PAY' && isset($info['paid_report_log'])) {
                $info['paid_report_log']['reported_status']    = -1;
                $info['paid_report_log']['reported_money']     = empty($info['paid_report_log']['reported_money']) ? $info['MONEY'] : $info['paid_report_log']['reported_money'];
                $info['paid_report_log']['no_reported_origin'] = '缺少必要上报参数OAID';
                \Plus::$app->log->info(json_encode($info) . '缺少必要上报参数OAID', [], self::LOG_DIR);
                $this->logPaidToDoris($info, $logInfo, '');
                return;
            }
        }

        $typeName = '';
        switch ($type) {
            case 'PAY':
                $typeName = 'PURCHASE';
                break;
            case 'CREATE':
                $typeName = 'CREATE_ROLE';
                break;
        }

        $payAmount = 100;
        if ($type == 'PAY') {
            $payAmount = intval($info['MONEY'] * 100);
        }

        $version = $info['EXT_CLICK']['ad_version'] ?? 0;

        if ($version >= 3.0) {
            $openId  = $info['OAID'];
            $clickId = $info['EXT_CLICK']['report_col_click_id'];
        } else {
            $openId  = $info['DEVICE_CODE'];
            $clickId = $info['CLICK_ID'];
        }

        // 上传参数
        $params = [
            'actions' => [
                [
                    'action_time'  => time(), // 转化发生时间
                    'user_id'      => [
                        'wechat_app_id'  => $info['EXT']['app_id'], // 小游戏appid, 微信小游戏上报必填 且必须通过授权
                        'wechat_openid'  => $openId, // 转化发生用户的openid, 微信小游戏上报必填
                        'wechat_unionid' => '', // 转化发生用户的unionid, 微信小游戏上报必填
                    ],
                    'trace'        => [
                        'click_id' => $clickId,
                    ],
                    'action_type'  => $typeName, // PURCHASE-付费行为（需要上传付费金额）, START_APP-次留人数, CREATE_ROLE-创角行为
                    'action_param' => [
                        'value' => $payAmount, // 付费金额（单位：分）
                    ],
                ],
            ],
        ];

        // 用注册数据的 click_id 字段当 id 去 origin_platform.tb_ad_click_match_log 这个表获取对应id的 CALLBACK_URL

        $url = $info['CALLBACK_URL'];

        // http://tracking.e.qq.com/conv?cb=************************************************************************************&conv_id=10001
        // 这里请求的url&path 直接从点击转发出去的__CALLBACK__字段中URLDecode获得，左边是示例请求，请勿直接上报
        $logInfo['request'] = \json_encode(['url' => $url, 'params' => $params]);
        //上报
        $account_id = $info['EXT_CLICK']['advertiser_id'] ?? '';
        if (empty($account_id)) {
            $account_id = $info['EXT_CLICK']['origin_json']['advertiser_id'] ?? '';
        }
        $res                 = $this->gdtPost($url, $account_id, $params);
        $logInfo['response'] = $res;

        //记录上报结果
        $resArr = json_decode($res, true);
        if (isset($resArr['code']) && $resArr['code'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
            if (!empty($info['paid_report_log'])) {
                $info['paid_report_log']['no_reported_origin'] = '接口返回编码异常';
            }
        }
        $this->log($info, $logInfo, $res, $url);
    }

    public function uploadActive($info, $ext = [])
    {
        // TODO: Implement uploadActive() method.
    }

    public function uploadRegister($info, $ext = [])
    {
        // TODO: Implement uploadRegister() method.
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

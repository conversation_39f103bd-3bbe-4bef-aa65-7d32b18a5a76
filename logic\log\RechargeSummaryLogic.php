<?php

namespace app\logic\log;

use app\apps\internal\Helpers\ConstHub;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\Logs\RechargeSummary;

class RechargeSummaryLogic
{

    /**
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    public function listInfo(Collection $params): array
    {
        $page     = $params->pull('page');
        $pageSize = $params->pull('page_size');
        $sort     = $params->pull('sort', null);
        $groups  = Arr::wrap($params->pull('groups'));
        if (!empty($sort)) {
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sort => $order];
        }
        else {
            $sort = ['es.TDAY' => 'DESC'];
        }

        try {
            $serv   = new RechargeSummary();
            $result = $serv->getList($params->toArray(), $groups, ['page' => $page, 'page_size' => $pageSize], $sort);
        }
        catch (\UnexpectedValueException $ue) {
            return ['list' => [], 'summary' => [], 'total' => 0];
        }
        $configBasic      = new BasicServ();
        $constMap = $configBasic->getMultiOptions([
            'cp_game_id:all','game_id','platform_id'
        ]);
        $cpGameId   = array_column($constMap->offsetGet('cp_game_id')->toArray() ?? [], 'val', 'key');
        $gameId   = array_column($constMap->offsetGet('game_id')->toArray() ?? [], 'val', 'key');
        $platformId   = array_column($constMap->offsetGet('platform_id')->toArray() ?? [], 'val', 'key');
        $relationship = ColumnManager::groupRechargeSummaryRelation($groups);
        $resetCols = [];
        if($relationship){
            $diffCols  = array_diff(["tday","payway","package_id","cp_game_id","game_id","platform_id"], $relationship);
            $resetCols = array_fill_keys($diffCols, '-');
        }
        foreach ($result["list"] as &$item){
            $item["cp_game_id"] = $cpGameId[$item["cp_game_id"]]??"";
            $item["game_id"] = $gameId[$item["game_id"]]??"";
            $item["platform_id"] = $platformId[$item["platform_id"]]??"";
            $item["money_proportion"]    = number_format($item["money_proportion"]*100,2,".",".")."%";
            $item["pay_user_proportion"] = number_format($item["pay_user_proportion"]*100,2,".",".")."%";
            $item = array_merge($item, $resetCols);
        }



        return $result;
    }
}
<?php

namespace app\service\AdvertiserData\Traits;

use app\extension\Support\Collections\Arr;
use Aura\SqlQuery\Common\SelectInterface;

/**
 *
 */
trait Converter
{
    /**
     * 转换分组字段定义
     *
     * @param $groups
     *
     * @return false|mixed|string[]
     * @deprecated
     */
    protected function changeGroups($groups)
    {
        if (!is_callable([$this, 'groupsReflect'])) {
            return $groups;
        }

        $groupMaps = $this->groupsReflect();

        if (is_string($groups)) {
            $groups = str_replace(array_keys($groupMaps), array_values($groupMaps), $groups);
            $groups = \explode(',', $groups);
        }
        elseif (is_array($groups)) {
            array_walk($groups, fn(&$item) => $item = ($groupMaps[$item] ?? $item));
        }
        return $groups;
    }

    /**
     * 简单的拼装查询字段
     * source -- 为关联的表名或别名
     * source_field -- 对应的表字段不填则为key
     * aggregate -- 基本的聚合方法
     * raw -- 原型返回该语句
     *
     * @param array $fixedCols    固定的详情字段不需要计算的字段
     * @param array $calcCols     需要统计的字段
     * @param bool  $isSummaryRow 输出汇总行形式
     * @param bool  $isTop        不加SUM的操作
     *
     * @return array
     */
    protected function generateColsArray(
        array $fixedCols = [], array $calcCols = [], bool $isSummaryRow = false, bool $isTop = false
    ): array
    {
        $result  = [];
        $collect = collect();

        if (!$isSummaryRow) {
            $collect = $collect->merge($fixedCols);
        }

        $collect = $collect->merge($calcCols);

        $collect->each(function (&$item, $key) use (&$result, $isSummaryRow, $isTop) {
            if (isset($item['aggregate'])) {
                $aggregate = $item['aggregate'];

                $this->isCalcValid($aggregate)
                    ? $format = "IFNULL({$aggregate}(%s), 0)"
                    : $format = "{$aggregate}(%s)";
            }
            else {
                $format = "%s";
            }

            if ($isTop) {
                $result[] = sprintf($format, "`{$key}`") . ' as ' . $key;
                return;
            }

            if (isset($item['raw'])) {
                $result[] = $item['raw'];
                return;
            }

            $field = '';

            if (isset($item['source'])) {
                $field .= $item['source'] . '.';
            }
            $field    .= $item['source_field'] ?? $key;
            $result[] = sprintf($format, $field) . ' as ' . $key;
        });

        return $result;
    }

    protected function isCalcValid($aggregate): bool
    {
        return in_array($aggregate, ['sum', 'avg']);
    }

}
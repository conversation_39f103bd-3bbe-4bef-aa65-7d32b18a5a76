<?php

namespace app\service\Logs\Components\Matcher;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\DBHelper\MatcherAbstract;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Query\SelectQuery;

class BaseLogMatch extends MatcherAbstract
{
    /**
     * @return array
     */
    protected function matchFnList(): array
    {
        return [
            'login_account' => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'core_account'  => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'package_id'    => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            },
            'dt'            => $this->matchDt(),
            'device_code'   => function (SelectQuery &$qb, $key, $value) {
                QueryBuilderHelper::baseBuild($qb, $key, $value);
            }
        ];
    }

    /**
     * 事件发生时间筛选项
     * @return \Closure
     */
    protected function matchDt(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value) {
            if (is_array($value) && count($value) > 1) {
                [$start, $end] = $value;
                $qb->where($key, 'between', $start, $end);
            }
            else {
                if (is_array($value)) {
                    $value = $value[0];
                }

                $qb->where($key, $value);
            }
        };
    }

    protected function matchTime(): \Closure
    {
        return function (SelectQuery &$qb, $key, $value) {
            if (is_array($value) && count($value) > 1) {
                [$start, $end] = $value;
                $start .= ' 00:00:00';
                $end   .= ' 23:59:59';
            }
            else {
                if (is_array($value)) {
                    $value = $value[0];
                }

                $start = $value . ' 00:00:00';
                $end   = $value . ' 23:59:59';
            }

            $qb->where($key, 'between', $start, $end);
        };
    }
}
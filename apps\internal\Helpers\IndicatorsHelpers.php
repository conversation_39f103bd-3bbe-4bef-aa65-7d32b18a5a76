<?php

namespace app\apps\internal\Helpers;

use app\extension\Support\Collections\Arr;
use MathParser\StdMathParser;

/**
 * @IndicatorsHelpers 指标计算工具类
 */
class IndicatorsHelpers
{

    /**
     * 补全LTV
     *
     * @param      $ltvN
     * @param bool $isRealTime
     *
     * @return void
     * @throws \Exception
     */
    public static function repairLtvN(&$ltvN, bool $isRealTime = false)
    {
        $today = new \DateTime();

        if ($isRealTime) {
            $diffFn = fn($date) => min((int)$today->diff(new \DateTime($date))->format('%a') + 1, 30);
        }
        else {
            $diffFn = fn($date) => (int)$today->diff(new \DateTime($date))->format('%a');
        }


        foreach ($ltvN as &$foo) {
            $tDay    = $foo['tday'];
            $dayDiff = $diffFn($tDay);

            $child = &$foo['day_type'];

            for ($i = 1; $i <= $dayDiff; $i++) {

                if (Arr::exists($child, $i)) continue;

                $last = $child[$i - 1] ?? ['money_all' => 0.00, 'money' => 0.00];

                $child[$i] = $last;
            }
        }
    }

    /**
     * 根据组合键和特定字段合成维度
     *
     * @param iterable $data
     * @param string $dimKey
     * @param array $combinedField
     * @param array $mergeField
     * @param string $separator
     *
     * @return array
     */
    public static function dimReduction(
        iterable $data,
        string   $dimKey,
        array    $combinedField = [],
        array    $mergeField = [],
        string   $separator = '|'
    ): array
    {
        $result            = [];
        $combinedIndexKeys = array_fill_keys($combinedField, 0);

        foreach ($data as $foo) {
            $k = static::flattenUnionKey($foo, $combinedIndexKeys, $separator);

            if (!Arr::exists($foo, $dimKey)) continue;

            if (!Arr::exists($result, $k)) {
                $result[$k] = array_filter(
                    $foo, fn($tKey) => !in_array($tKey, $mergeField),
                    ARRAY_FILTER_USE_KEY
                );
            }

            $rr       = &$result[$k];
            $childKey = $foo[$dimKey];

            foreach ($mergeField as $mergeChild) {
                $kk = implode('.', [$dimKey, $childKey, $mergeChild]);
                $dk = implode('.', [$dimKey, $childKey, $dimKey]);

                if (!isset($rr[$dimKey][$childKey][$mergeChild])) {
                    Arr::set($rr, $kk, $foo[$mergeChild] ?? 0);
                    Arr::set($rr, $dk, $foo[$dimKey] ?? 0);
                }
                else {
                    $rr[$dimKey][$childKey][$mergeChild] += ($foo[$mergeChild] ?? 0);
                }
            }

        }

        return $result;
    }


    /**
     * 建立维度
     *
     * @param                $data
     * @param array $combinedField
     * @param array|\Closure $needCalculateField
     * @param \Closure|null $filterCallback
     * @param string $separator
     *
     * @return array
     */
    public static function buildDim(
        $data,
        array $combinedField,
        $needCalculateField,
        \Closure $filterCallback = null,
        string $separator = '|'
    ): array
    {

        if (!is_null($filterCallback)) {
            $initFilter = $filterCallback;
        }
        else {
            $initFilter = fn($item, $key) => array_filter(
                $item, fn($kk) => in_array($kk, $combinedField),
                ARRAY_FILTER_USE_KEY
            );
        }

        if ($needCalculateField instanceof \Closure) {
            $calculateFunc = $needCalculateField;
        }
        elseif (is_array($needCalculateField)) {
            $calculateFunc = function (&$target, $source) use ($needCalculateField) {
                foreach ($needCalculateField as $filed) {
                    $target[$filed] += $source[$filed];
                }
            };
        }
        else {
            $calculateFunc = function (&$target, $source) use ($combinedField) {
                foreach ($source as $key => $foo) {
                    if (!in_array($key, $combinedField)) {
                        $target[$key] += $foo;
                    }
                }
            };
        }

        return static::mergeDimension(
            $data, $combinedField, $initFilter, $calculateFunc, $separator
        );
    }

    /**
     * @param          $data
     * @param          $unionKeys
     * @param \Closure $filterCallback
     * @param \Closure $cacCallback
     * @param string $separator
     *
     * @return array
     */
    public static function mergeDimension(
        $data, $unionKeys, \Closure $filterCallback, \Closure $cacCallback, string $separator = '|'
    ): array
    {
        $collect       = [];
        $unionIndexCom = array_fill_keys(array_values($unionKeys), 0);

        foreach ($data as $key => $foo) {

            $unionKey = static::flattenUnionKey($foo, $unionIndexCom, $separator);

            if (!Arr::exists($collect, $unionKey)) {
                $collect[$unionKey] = $filterCallback($foo, $key, $unionKeys);
            }

            $child = &$collect[$unionKey];

            $cacCallback($child, $foo, $unionKeys);
        }


        return $collect;
    }


    /**
     * 返回组合唯一键
     *
     * @param        $data
     * @param        $unionIndex
     * @param string $separator
     *
     * @return string
     */
    public static function flattenUnionKey($data, $unionIndex, string $separator = '|'): string
    {
        $keys = array_merge($unionIndex, array_intersect_key($data, $unionIndex));
        ksort($keys);

        return \implode($separator, $keys);
    }

    /**
     * 除法计算
     *
     * @param      $molecular
     * @param      $denominator
     * @param int $precision
     * @param bool $isPercent
     *
     * @return bool|string
     */
    public static function division($molecular, $denominator, int $precision = 2, bool $isPercent = false)
    {
        $d = (empty((float)$molecular) || empty((float)$denominator))
            ? 0.00
            : ($molecular / $denominator);

        if ($isPercent) {
            $d = ($d === 0.00)
                ? '0.00%'
                : round($d * 100, 2) . '%';
        }
        else {
            $d = ($d === 0.00) ? 0.00 : round($d, 2);
        }

        return $d;
    }
}
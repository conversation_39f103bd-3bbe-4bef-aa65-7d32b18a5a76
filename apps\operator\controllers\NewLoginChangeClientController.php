<?php

namespace app\apps\operator\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\logic\operator\NewLoginBaseLogic;

class NewLoginChangeClientController extends BaseTableController
{
    /**
     *
     *
     * @param Collection $params
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $sort     = [];
        $paginate = [
            'page'      => $params->pull('page'),
            'page_size' => $params->pull('page_size'),
        ];
        $groups   = [];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }

        if ($params->has('groups')) {
            $groups = Arr::wrap($params->pull('groups'));

            if (empty($sort)) {
                $sort = ['new_user' => 'DESC'];
            }
        }

        if (empty($sort)) {
            $sort = ['tday' => 'DESC', 'new_user' => 'DESC'];
        }

        $options = $params->toArray();
        $logic   = new NewLoginBaseLogic();

        return $logic->getList($options, $groups, $paginate, $sort);
    }

    /**
     * 表头字段
     * @param Collection $params
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $fields = [
            'tday'                   => ['title' => '日期', 'dataIndex' => 'tday', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'cp_game_id'             => ['title' => '游戏原名', 'dataIndex' => 'cp_game_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'game_id'                => ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'channel_id'             => ['title' => '推广子渠道', 'dataIndex' => 'channel_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'package_id'             => ['title' => '包号', 'dataIndex' => 'package_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'platform_id'            => ['title' => '客户端', 'dataIndex' => 'platform_id', 'sorter' => true, 'classify' => ['attrs', 'base']],
            'new_user'               => ['title' => '新增用户', 'dataIndex' => 'new_user', 'sorter' => true, 'classify' => ['attrs', 'user_index']],
            'pay_user_new_percent'   => ['title' => '新用户付费率', 'dataIndex' => 'pay_user_new_percent', 'classify' => ['attrs', 'user_index']],
            'active_user'            => ['title' => '活跃用户', 'dataIndex' => 'active_user', 'sorter' => true, 'classify' => ['attrs', 'user_index']],
            'active_arpu'            => ['title' => '活跃arpu', 'dataIndex' => 'active_arpu', 'classify' => ['attrs', 'user_index']],
            'active_pay_percent'     => ['title' => '活跃付费率', 'dataIndex' => 'active_pay_percent', 'classify' => ['attrs', 'user_index']],
            'pay_user_newlogin_all'  => ['title' => '付费新用户', 'dataIndex' => 'pay_user_newlogin_all', 'sorter' => true, 'classify' => ['attrs', 'pay_index']],
            'pay_money_newlogin_all' => ['title' => '新用户付费金额', 'dataIndex' => 'pay_money_newlogin_all', 'sorter' => true, 'classify' => ['attrs', 'pay_index']],
            'pay_user_all'           => ['title' => '付费用户', 'dataIndex' => 'pay_user_all', 'sorter' => true, 'classify' => ['attrs', 'pay_index']],
            'pay_money_all'          => ['title' => '付费金额', 'dataIndex' => 'pay_money_all', 'sorter' => true, 'classify' => ['attrs', 'pay_index']],
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'user_index', 'label' => '新用户与活跃'],
                    ['value' => 'pay_index', 'label' => '用户付费指标'],
                ],
            ],
        ];

        return ['fields' => array_values($fields), 'classify' => $classify];
    }

    protected function registerParams($key = null): Collection
    {
        $today = date('Y-m-d');

        return collect([
                           ['field' => 'range_date_start', 'default' => $today], // 统计日期开始时间
                           ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
                           ['field' => 'range_date_dimension', 'default' => 2], // 时间维度, 1|null-时, 2-日, 3-周, 4-月
                           ['field' => 'thour'], // 小时
                           ['field' => 'cp_game_id',], // 游戏原名
                           ['field' => 'game_id',], // 游戏统计名
                           ['field' => 'app_show_id',], // 游戏前端名
                           ['field' => 'channel_main_id',], // 主渠道
                           ['field' => 'channel_id',], // 渠道
                           ['field' => 'promotion_channel_id',], // 推广渠道
                           ['field' => 'platform_id',], // 客户端ID
                           ['field' => 'package_id',], // 包号
                           ['field' => 'promotion_id',], // 推广分类
                           ['field' => 'department_id',], // 部门
                           ['field' => 'user_id',], // 投放人
                           ['field' => 'has_no_cost',], // 是否含无消费
                           ['field' => 'has_visual_pay',], // 是否含虚拟币
                           ['field' => 'ltv_type'], // ltv值类型
                           ['field' => 'remain_type'], // remain值类型 ['field' => 'column_scope', 'default' => ['ltv', 'roi']], // 报表展示类型，可单独展示LTV/ROI
                           ['field' => 'search_mode', 'default' => ['normal']], // 查询关联模式,控制关联的方式
                           ['field' => 'max_day_type', 'default' => -1], // 最大节点限制
                           ['field' => 'column_scope', 'default' => ['ltv', 'roi']], // 报表展示类型，可单独展示LTV/ROI
                           ['field' => 'max_data_day_ago', 'default' => 0], // 最大数据日期 0当天 1昨天 2前天 如此类推，一般用于ltv/roi、留存获取最大日期用
                           ['field' => 'channel_id_tags'], // 渠道标签
                           ['field' => 'channel_main_id_tags'], // 主渠道标签
                           ['field' => 'package_id_tags'], // 包号标签
                           ['field' => 'game_id_tags'], // 包号标签

                           ['field' => 'page_size', 'default' => 50],
                           ['field' => 'page', 'default' => 1],
                           ['field' => 'order'],
                           ['field' => 'sort'],
                           ['field' => 'groups'],
                           ['field' => 'method', 'default' => ['data', 'fields']],
                       ]);
    }
}
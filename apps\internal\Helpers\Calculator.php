<?php

namespace app\apps\internal\Helpers;

class Calculator
{
    /**
     * @param iterable         $data
     * @param array            $fields
     * @param string           $operator
     * @param string           $dayField
     * @param \DateTime|string $referDate
     *
     * @return array
     * @throws \Exception
     */
    public static function cumulativeOnDays(
        iterable $data,
        array    $fields,
        string   $operator = '<=',
        string   $dayField = 'tday',
                 $referDate = null
    ): array
    {
        if (!static::isValidOperator($operator)) return [];

        if (is_null($referDate)) {
            $referDate = new \DateTime();
        }

        $resultKeys = array_map(fn(&$item) => $item . '_n', $fields);
        $result     = array_fill_keys($resultKeys, []);
        $compare    = fn($i, $target) => eval("return $i $operator $target;");

        foreach ($data as $foo) {
            if (empty($foo[$dayField])) continue;

            $dayDiff = days_apart($referDate, $foo[$dayField]);

            foreach ($fields as $field) {
                $tKey  = $field . '_n';
                $child = &$result[$tKey];

                for ($i = 1; $compare($i, $dayDiff); $i++) {
                    !isset($child[$i])
                        ? $child[$i] = $foo[$field]
                        : $child[$i] += $foo[$field];
                }
            }
        }

        return $result;
    }

    /**
     * 判断比较符是否合法
     *
     * @param string $operator
     *
     * @return bool
     */
    public static function isValidOperator(string $operator): bool
    {
        return $operator === '<='
            || $operator === '<';
    }
}
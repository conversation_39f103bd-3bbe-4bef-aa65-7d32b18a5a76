<?php
declare(ticks = 1);
namespace app\crontab;

use app\models\DataSpy\AdminGroup;
use app\service\Auth;
use Plus\CLI\DaemonProcess;
use Plus\Net\Http;

/**
 * spy1.0 和 spy2.0 用户数据权限同步
 *
 * @package app\crontab
 */
class UserDataPermissionsSync extends DaemonProcess
{

    public function run()
    {
        $args     = func_get_args();
        $username = $args[0]["p"]?? "";
        $sql = "select a.* from dataspy.admin_user a join base_conf_platform.oa_base_people_conf b on a.USER_NAME=b.login_account where b.type=1";
        if($username){
            $sql.=" and a.USER_NAME='{$username}'";
        }
        $userArr = \Plus::$app->dataspy->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $adminGroup = (new AdminGroup());
        echo "数量:".count($userArr),PHP_EOL;
        foreach ($userArr as $user){
            //$this->syncsSpy($user);
            $userName = $user["USER_NAME"];
            if(!$userName){
                continue;
            }
            $cpGames = [0];
            $games = [0];
            $mainChannel = [0];
            $submitData = [
                "username"=>$userName,
                "cpGames"=>$cpGames,
                "games" => $games,
                "mainChannel" => $mainChannel
            ];

            if ($user["LEVEL"] !=9){
                //非超级管理员
                $adminGroup = $adminGroup->find(["NAME"=>$user["REAL_NAME"].$user["ID"]]);
                if(!$adminGroup->offsetExists("ID")){
                    continue;
                }
                if(!$adminGroup->CP_GAME_IDS){
                   unset($submitData["cpGames"]);
                }
                if(!$adminGroup->GAME_IDS){
                    unset($submitData["games"]);
                }
                if(!$adminGroup->CHANNEL_MAIN_IDS){
                    unset($submitData["mainChannel"]);
                }

                if ($adminGroup->CP_GAME_IDS && $adminGroup->CP_GAME_IDS !="all"){
                    $submitData["cpGames"] = explode(",",$adminGroup->CP_GAME_IDS);
                }
                if ($adminGroup->GAME_IDS && $adminGroup->GAME_IDS !="all"){
                    $submitData["games"] = explode(",",$adminGroup->GAME_IDS);
                }
                if ($adminGroup->CHANNEL_MAIN_IDS && $adminGroup->CHANNEL_MAIN_IDS !="all"){
                    $submitData["mainChannel"] = explode(",",$adminGroup->CHANNEL_MAIN_IDS);
                }
                if ($adminGroup->PACKAGE_IDS){
                    $submitData["pkg"] = explode(",",$adminGroup->PACKAGE_IDS);
                }
            }


            try {
                //get oa user token
                $result = (new Auth())->getUserToken(["username" => $userName]);
                $token = $result["data"];
                $domain = "https://basic.910admin.com";
                //$domain = "https://test-basic.910admin.com";
                $result = (new Http("{$domain}/api/v1/user/detail"))->get(["username"=>$userName],[
                    "token:$token"
                ]);
                $reponse = json_decode($result,true);
                if($reponse["code"]>0){
                    throw new \Exception($result);
                }

                $menuIds = [];
                foreach ($reponse["data"]["menuList"] as $item){
                   $menuIds = array_merge($menuIds,$item);
                }
                if(!$menuIds){
                    throw new \Exception("菜单为空");
                }
                $submitData["menuList"] = $menuIds;
                $result = (new Http("{$domain}/api/v1/user/authority/distribute"))->post($submitData,false,[
                    "token:$token",
                    "Content-Type:application/json"
                ]);
                var_dump($result);
            }catch (\Exception $e){
                echo $e->getMessage(),PHP_EOL;
            }

        }

    }

    public function syncsSpy($user){
        $userName = $user["USER_NAME"];
        if ($user["LEVEL"] !=9) {
            //非超级管理员
            $adminGroup = (new AdminGroup())->find(["NAME" => $user["REAL_NAME"] . $user["ID"]]);
            if (!$adminGroup->offsetExists("ID")) {
                return false;
            }
        }
        try {
            //get oa user token
            $result = (new Auth())->getUserToken(["username" => $userName]);
            $token = $result["data"];
            $domain = "https://basic.910admin.com";
            $domain = "https://test-basic.910admin.com";
            $result = (new Http("{$domain}/api/v1/user/detail"))->get(["userId"=>$user['ID']],[
                    "token:$token"
                ]);
            var_dump($result);
        }catch (\Exception $e){
            echo $e->getMessage(),PHP_EOL;
        }

        die;
    }
}
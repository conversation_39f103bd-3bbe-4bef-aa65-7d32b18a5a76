<?php

namespace app\service\AdvertiserData\Components\MatchParams\Traits;


use app\extension\Support\Collections\Arr;
use app\logic\advertise\Traits\RemainUtilTrait;
use app\models\AdpPlatform\TbAdpCampaign;
use app\service\AdvertiserData\Components\Helpers\Convert;

trait CampaignMatch
{
    /**
     * ### 筛选广告组ID
     * **注意null也会作为筛选条件**
     *
     * @param $params
     *
     * @return string
     */
    protected function matchCampaignId($params): string
    {
        if (!array_key_exists('campaign_id', $params)) return '';

        $field = $this->getField('campaign_id');

        if (is_null($params['campaign_id'])) {
            return "{$field} IS NULL";
        }

        $data = Convert::convertInString($params['campaign_id']);

        return "{$field} IN ({$data})";
    }

    /**
     * @param $params
     *
     * @return string
     */
    protected function matchCampaignName($params): string
    {
        if (empty($params['campaign_name'])) return '';

        $field = $this->getField('campaign_id');
        $data  = $params['campaign_name'];
        $ids   = (new TbAdpCampaign())->getCampaignIdByName($data, $params["range_date_start"], $params["range_date_end"]);
        if ($ids) {
            return "({$field} IN ('" . implode("','", $ids) . "'))";
        }
        return "({$field} ='error' )";
    }

    /**
     * 筛选用户定向
     * 0:无定向
     * 1:ios
     * 2:android
     * 3:混投
     *
     * @param $params
     * @return string
     */
    protected function matchUserOS($params): string
    {
        if (isset($params['dim_user_os'])) {
            if ($params['dim_user_os'] == 'IOS') {
                $params['user_os'] = 1;
            }
            elseif ($params['dim_user_os'] == 'ANDROID') {
                $params['user_os'] = 2;
            }
            elseif ($params['dim_user_os'] == '混投') {
                $params['user_os'] = 3;
            }
        }

        if (empty($params['user_os'])) return '';

        $field   = $this->getField('user_os');
        $data    = $params['user_os'];
        $orWhere = [];

        foreach (Arr::wrap($data) as $item) {
            if ($item == 1) {
                $orWhere[] = "{$field} = JSON_ARRAY('IOS')";
            }
            elseif ($item == 2) {
                $orWhere[] = "{$field} = JSON_ARRAY('ANDROID')";
            }
            elseif ($item == 3) {
                $orWhere[] = "(JSON_CONTAINS({$field}, '[\"ANDROID\",\"IOS\"]' , '$') =1 or {$field} is null)";
            }
        }

        if (!empty($orWhere)) {
            return '(' . implode(' or ', $orWhere) . ')';
        }
        else {
            return '';
        }
    }

}
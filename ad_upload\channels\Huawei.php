<?php
/**
 * 华为数据上报
 * Created by PhpStorm.
 * User: Tim
 * Date: 2021/01/13
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

class <PERSON>aw<PERSON> extends AdBaseInterface
{
    private $key = '8A0FCAZS0pjgDOcbb0tkoDCKksvmBGjQFgJy/wyZHnc=';


    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->uploadData($info, 'activate');
    }

    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->uploadData($info, 'register');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->uploadData($info, 'paid');
    }

    /**
     * 上报数据
     * @param $packageId
     * @param $info
     * @param $uploadConfId
     */
    private function uploadData($info, $type)
    {
        $key = $info['EXT']['akey'];
        if (!empty($key)) {
            $this->key = $key;
        }
        $url     = "https://ppscrowd-drcn.op.hicloud.com/action-lib-track/hiad/v2/actionupload";
        $ext     = $info['EXT_CLICK'];
        $typeArr = ['activate' => 'ACTIVE', 'register' => 'REG', 'paid' => 'PAY'];

        //拼接参数(华为的参数为字符串的需要用双引号括起来)
        $ip              = empty($ext['ip']) ? '' : $ext['ip'];
        $trackingEnabled = empty($ext['tracking_enabled']) ? 1 : $ext['tracking_enabled'];
        $cid             = empty($info['CID']) ? -1 : $info['CID'];
        $campaignId      = empty($ext['campaign_id']) ? -1 : $ext['campaign_id'];
        $time            = time();
        $param           = [
            "callback" => $info['CALLBACK_URL']."",
            "content_id" => $cid."",
            "campaign_id" => $campaignId."",
            "oaid" => $info['OAID']."",
            "tracking_enabled" => intval($trackingEnabled),
            "ip" => $ip."",
            "conversion_type" => $type."",
            "conversion_time" => $time."",
            "timestamp" => $time."000",
        ];
        if ($type == 'paid') {
            $param['conversion_count'] = 1;
            $param['conversion_price'] = number_format(round($info['MONEY'], 2), 2, '.', '');
        }

        //拼接请求头部
        $authorization = 'Authorization:Digest validTime="'.$time.'000",response="'.hash_hmac('sha256', json_encode($param), $this->key).'"';
        $headers       = ['Content-type: application/json;charset="utf-8"', "Accept: application/json", "Cache-Control: no-cache", "Pragma: no-cache", $authorization];


        $http = new Http($url);
        $res  = $http->post($param, null, $headers);

        //记录上报结果
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'huawei';
        $logInfo['request']      = json_encode(['url' => $url, 'data' => $param]);
        $logInfo['response']     = $res;
        $resContent              = json_decode($res, true);

        if ($resContent['resultCode'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        //写日志
        $this->log($info, $logInfo, $res, $url);
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

<?php
/**
 * Uc数据上报
 * Created by PhpStorm.
 * User: AvalonP
 * Date: 2017/7/25
 * Time: 16:33
 */

namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;
use <PERSON>urlHelper;
use LogAPI;
use Plus\SQL\FreeSQLException;

class <PERSON><PERSON>yi extends AdBaseInterface
{
    const CONVERT_ACTIVE   = 0;
    const CONVERT_REGISTER = 1;
    const CONVERT_PURCHASE = 17;


    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, 'ACTIVE');
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, 'REG');
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, 'PAY');
    }

    /**
     * 公共上报
     * @param $info
     * @param $type
     * @return bool
     */
    private function upload($info, $type)
    {
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'aiqiyi';

        $callbackUrl = '';

        switch ($type) {
            case 'ACTIVE':
                $callbackUrl = $info['CALLBACK_URL'] . '&event_type=' . self::CONVERT_ACTIVE;
                $typeName    = '激活';
                break;
            case 'REG':
                $callbackUrl = $info['CALLBACK_URL'] . '&event_type=' . self::CONVERT_REGISTER;
                $typeName    = '注册';
                break;
            case 'PAY':
                $callbackUrl = $info['CALLBACK_URL'] . '&event_type=' . self::CONVERT_PURCHASE;
                $callbackUrl = str_replace("__PAYMENT_AMOUNT__", $info['MONEY'] * 100, $callbackUrl);
                $typeName    = '付费';
                break;
        }
        $logInfo['request'] = json_encode(['url' => $callbackUrl]);
        $http               = new Http($callbackUrl);
        $res                = $http->get();

        //写日志
        $logInfo['response'] = $res;
        $resContent          = json_decode($res, true);
        //记录上报结果
        if ($resContent['status'] == 200) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        $this->log($info, $logInfo, $res, $callbackUrl);
        return $logInfo['reported_status'] == 1;
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }

    public function uploadRemain($info, $ext = [])
    {
        // TODO: Implement uploadRemain() method.
    }
}

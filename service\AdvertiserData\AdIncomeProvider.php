<?php

namespace app\service\AdvertiserData;

use app\extension\FakeDB\FakeDB;
use app\util\Common;

class AdIncomeProvider
{
    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @return array
     * @throws \Smarty\Exception
     * @throws \Exception
     */
    public function getList(
        array $params, string $groups = '', array $paginate = [], array $sort = []
    ): array
    {
        $db       = $this->getConn();
        $baseTpl  = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_income_dash/dash.tpl');
        $countTpl = \Plus::$app->sqlTemplates->createTemplate('sql/advertise/ad_income_dash/dash_count.tpl');

        $baseTpl->assign('params', $params);

        $summaryTpl = clone $baseTpl;
        $summaryTpl->assign('is_summary', 1);

        $baseTpl
            ->assign('sort', $sort)
            ->assign('paginate', $paginate);

        if (!empty($groups)) {
            $baseTpl->assign('group_by', $groups);
        }

        $countTpl
            ->assign('params', $params);

        if (!empty($groups)) {
            $countTpl->assign('group_by', $groups);
        }

        $infoSQL    = $baseTpl->fetch();
        $summarySQL = $summaryTpl->fetch();
        $countSQL   = $countTpl->fetch();

        @Common::dumpSql($infoSQL);
        $list       = $db->query($infoSQL)->fetchAll();
        $summaryRow = $db->query($summarySQL)->fetch();
        $countRow   = $db->query($countSQL)->fetch();

        return [
            'list'    => $list,
            'summary' => $summaryRow,
            'total'   => $countRow['total'] ?? 0,
        ];
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('origin_platform');
    }
}
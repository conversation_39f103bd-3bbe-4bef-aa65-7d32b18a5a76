<?php

namespace app\models\AdpPlatform;

use Plus\MVC\Model\ActiveRecord;


/**
 * @TbAdpOauth
 *            [MODEL:TbAdpOauthVO]用户授权
 *
 * @property int    ID
 * @property string ADVERTISER_ID
 * @property string ADVERTISER_NAME
 * @property string SHORT_AD_NAME
 * @property int    ADVERTISER_TYPE
 * @property int    ADVERTISER_ACCOUNT
 * @property int    ADVERTISER_ACCOUNT_EXT
 * @property int    PARENT_ADVERTISER
 * @property int    APP_ID
 * @property int    ACCESS_TOKEN
 * @property int    REFRESH_TOKEN
 * @property int    SUBJECT_ID
 * @property int    CHANNEL_ID
 * @property int    COMPANY
 * @property int    IS_GRAB
 * @property int    STATUS
 * @property int    FETCH_SPEED_STATUS
 * @property int    BALANCE_ALARM
 * @property int    DAILY_BUDGET
 * @property int    AUTO_BUDGET
 * @property int    PUBLIC_AUTH
 * @property int    PUBLIC_AUTH_VALID_DATE
 * @property string CP_GAME_IDS
 * @property string GAME_IDS
 * @property string VIDEO_GAME_TYPE
 * @property int    IMAGE_GAME_TYPE
 * @property int    VIDEO_MATERIAL_TYPE
 * @property int    IMAGE_MATERIAL_TYPE
 * @property int    VIDEO_WH
 * @property int    IMAGE_WH
 * @property int    DEMANDER
 * @property int    PRE_UPLOAD_TIME
 * @property int    EXT_DATA
 * @property int    UPDATE_TIME
 * @property int    LAST_UPLOAD_TIME
 * @property int    LAST_FETCH_SPEED_TIME
 * @property int    VIDEO_MEDIA_TYPE
 * @property int    IMAGE_MEDIA_TYPE
 * @property int    VIDEO_MAKING_TYPE
 * @property int    IMAGE_MAKING_TYPE
 * @property int    ADVERTISER_STATUS
 * @property int    ACCOUNT_STATUS
 * @property string REMARK
 * @property string PACKAGE_IDS
 * @property string ADD_TIME
 * @property string MEDIA_SUBJECT_ID
 */
class TbAdpOauth extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->dataspy;
        parent::__construct($data);
    }
}
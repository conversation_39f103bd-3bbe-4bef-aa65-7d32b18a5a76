<?php

namespace app\apps\advertise\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\service\AdvertiserData\BookingBaseIndexServ;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;

class AdBookingDashboardController extends BaseTableController
{
    use ColumnsInteract;

    /**
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        [$page, $pageSize] = [
            $params->pull('page'),
            $params->pull('page_size'),
        ];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }
        else {
            $sort = ['tday' => 'ASC', 'cost_discount' => 'DESC'];
        }

        $options = $params->toArray();
        $groups  = $this->groupAdRelation(Arr::wrap($params->pull('groups')));

        $serv       = new BookingBaseIndexServ();
        $baseRe     = $serv->getList($options, $groups, ['page' => $page, 'page_size' => $pageSize], $sort);
        $list       = &$baseRe['list'];
        $summaryRow = &$baseRe['summary'];

        $constConfCollect = (new BasicServ())
            ->getMultiOptions(['platform_id', 'promotion_id', 'cp_game_id:all', 'department_id', 'user_id', 'game_id', 'channel_main_id'])
            ->put('channel_id', (new GeneralOptionServ())->listChannelOptions());

        $resetGroupFn = $this->resetGroupsCols($groups, $groups,
            array_diff(ConstHub::AD_FIXED_INFO_COLS, ['creative_id', 'creative_name'])
        );

        $replaceFn = $this->replaceColumnDefine($constConfCollect);
        $process   = new ProcessLine();

        $process
            ->addProcess($replaceFn)
            ->addProcess($resetGroupFn);

        $process->run($list);

        $baseRe['time'] = $summaryRow['max_update_time'] ?? '';

        return $baseRe;
    }

    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'media_index', 'label' => '媒体转化目标'],
                    ['value' => 'ad_cost', 'label' => '广告成本指标'],
                ],
            ],
        ];

        $fields = $this->tableFields($params->toArray());

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $today = date('Y-m-d');

        return collect([
            ['field' => 'range_date_start', 'default' => $today], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $today], // 统计日期结束时间
            ['field' => 'cp_game_id'], // 游戏原名
            ['field' => 'game_id'], // 游戏统计名
            ['field' => 'app_show_id'], // 游戏前端名
            ['field' => 'channel_main_id'], // 主渠道
            ['field' => 'channel_id'], //渠道
            ['field' => 'promotion_channel_id'], // 推广渠道
            ['field' => 'platform_id'], // 客户端ID
            ['field' => 'package_id'], // 包号
            ['field' => 'promotion_id'], // 推广分类
            ['field' => 'department_id'], // 部门
            ['field' => 'user_id'], // 投放人
            ['field' => 'ad_account'], // 投放账号
            ['field' => 'advertiser_id'], // 广告账号ID
            ['field' => 'campaign_id'], // 广告组ID
            ['field' => 'campaign_name'], // 广告组名称
            ['field' => 'plan_name'], // 计划名
            ['field' => 'plan_id'], // 计划ID
            ['field' => 'data_range'], // 数据范围
            ['field' => 'is_has_natural', 'default' => 1], // 是否含自然量
            ['field' => 'account_id'], // 账号ID搜索
            ['field' => 'data_scope'], // 数据范围搜索
            ['field' => 'channel_id_tags'], // 渠道标签
            ['field' => 'channel_main_id_tags'], // 主渠道标签
            ['field' => 'package_id_tags'], // 包号标签
            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups', 'default' => ['tday', 'package_id', 'channel_id', 'plan_id']],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

    /**
     * @param array $groups
     *
     * @return array
     */
    private function groupAdRelation(array $groups): array
    {
        $relationTree = [
            'plan_id'              => [
                'plan_name', 'campaign_id', 'package_id',
            ],
            'campaign_id'          => [
                'campaign_name', 'channel_id', 'promotion_id', 'package_id',
                'account_id', 'ad_account', 'account_name',
            ],
            'package_id'           => [
                'game_id', 'cp_game_id', 'user_id',
                'platform_id', 'app_show_id',
                'channel_main_id', 'channel_id',
            ],
            'promotion_channel_id' => ['channel_id'],
            'channel_id'           => ['channel_main_id'],
            'channel_main_id'      => [],
            'game_id'              => ['cp_game_id'],
            'user_id'              => ['department_id'],
            'platform_id'          => [],
            'account_id'           => ['ad_account'],
            'ad_account'           => ['account_id'],
            'account_name'         => [],
            'app_show_id'          => [],
            'promotion_id'         => [],
            'cp_game_id'           => [],
            'tday'                 => [],
        ];

        return ColumnManager::matchRelationByGroups($relationTree, $groups);
    }

}
<?php

namespace app\service;

use app\extension\FakeDB\FakeDB;
use Smarty\Template;

class BaseProvider
{
    /**
     * @param Template $tpl
     * @param array    $assignArray
     * @return void
     */
    protected function tplAssign(Template &$tpl, array $assignArray)
    {
        if (empty($assignArray)) return;

        foreach ($assignArray as $key => $value) {
            if (is_numeric($key)) continue;

            $tpl->assign($key, $value);
        }
    }
    /**
     * doris连接
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    protected function dorisConn()
    {
        return FakeDB::connection('doris_entrance');
    }
}
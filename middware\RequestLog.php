<?php


namespace app\middware;

use app\models\AdminLogs;
use app\models\DataSpy\SystemAccessLog;
use app\service\Auth;
use Plus;
use Plus\MVC\Request\Request;
use Plus\MVC\Response\Response;
use Plus\Util\StringUtil;
use Plus\Util\TimeStamp;

/**
 * 访问日志
 * Class RequestLog
 * @package app\middware
 */
class RequestLog
{

    /**
     * 访问日志
     * @param Request $request 请求对象
     * @param Response $response 响应对象
     * @return bool|\Generator
     * @throws \Exception
     */
    public static function record(Request $request, Response $response)
    {
        yield;

        //请求参数
        $req = '';
        if ($request->getRequestMethod() == 'POST') {
            if (!empty($_POST)) {
                $req = json_encode($_POST);
            } else {
                $req = file_get_contents('php://input');
            }
        } else {
            $req = $request->getURI();
        }

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            return true;
        }
        $username = \Plus::$service->admin->getUsername();
        if (!$username) {
            return true;
        }


        $router = Plus::$app->router;
        $app = StringUtil::unCamelize($router->getApp(), '-');
        $controller = StringUtil::unCamelize($router->getController(), '-');
        $action = StringUtil::unCamelize($router->getAction(), '-');
        $uri = "/" . implode('/', [$app, $controller, $action]);
        //不记录的url
        $notRecordUrl = ['/auth/menu/list'];
        if (in_array($uri, $notRecordUrl)) {
            return true;
        }

        $type = "API";
        $remark = $request->getRequestMethod() . '访问参数：' . $req;
        //前端页面访问日志记录
        if ($uri == "/auth/menu/collect" && $req) {
            $type = "PAGE";
            $uri = json_decode($req, true)["uri"];
            if ($uri == "/error/403") {
                //错误页面不记录
                return true;
            }
            $remark = "";
        }

        //内存使用
        $memorySize = memory_get_usage(true);
        $memorySizeInMB = round($memorySize / 1024 / 1024, 2);  // 将字节转换成 MB，保留两位小数

        //访问日志插入数据库
        $adminLogs = SystemAccessLog::getInstance();
        $adminLogs->USER_NAME = $username;
        $adminLogs->URI = $uri;
        $adminLogs->IP = $request->getRemoteAddr();
        $adminLogs->SPENT_TIME = microtime(true) - APP_BEGIN_TIME;
        $adminLogs->SPENT_MEM = $memorySizeInMB;
        $adminLogs->ADD_TIME = TimeStamp::now();
        $adminLogs->REMARK = $remark;
        $adminLogs->TYPE = $type;
        try {
            $adminLogs->insert();
        } catch (\Throwable $e) {
            // nothing to do.
        }
    }

}

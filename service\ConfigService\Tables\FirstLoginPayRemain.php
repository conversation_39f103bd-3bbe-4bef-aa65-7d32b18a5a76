<?php

namespace app\service\ConfigService\Tables;

use app\extension\Support\Helpers\Mather;
use app\service\ConfigService\Constants\FieldTag;
use app\service\ConfigService\Contracts\TableFieldAble;
use app\service\ConfigService\Traits\BaseHub;
use app\service\ConfigService\Traits\FieldTranslator;

class FirstLoginPayRemain implements TableFieldAble
{
    use BaseHub, FieldTranslator;

    public function getFields($options = null)
    {
        $rangeDateDimension = (int)($options['range_date_dimension'] ?? 2);

        $today  = new \DateTime();
        $NNodes = array_merge(
            range(1, 29), [44, 59], Mather::findNumInScope(59, 719, 30)
        );

        $collect = $this->getBaseFields(['baseOperationCollect']);
        $groups  = $options['groups'];

        // if (in_array('package_id', $groups)) {
        //     $collect = $collect->merge([
        //         'package_tags' => ['title' => '包号标签', 'classify' => ['attrs', 'tags']]
        //     ]);
        // }
        //
        // if (
        //     in_array('package_id', $groups)
        //     || in_array('channel_id', $groups)
        // ) {
        //     $collect = $collect->merge([
        //         'channel_tags' => ['title' => '推广子渠道标签', 'classify' => ['attrs', 'tags']]
        //     ]);
        // }

        $collect = $collect->merge([
            'pay_interval'     => ['title' => '累计付费金额'],
            'summary_pay_user' => ['title' => '统计付费用户'],
        ]);

//        $nDays = days_apart($today, $options['range_date_start'] ?? $today);
        $nDays = 720;
        $nDays -= 1;

        $remainFields = $this->remainNCollect(Mather::findIn($nDays, $NNodes), '付费');
        $remainFields = FieldTag::tagClassifyToNField($remainFields, 'remain',
            [
                ['range' => [1, 29], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_1]],
                ['range' => [44, 179], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_2]],
                ['range' => [209, 359], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_3]],
                ['range' => [389, 719], 'classify' => ['attrs', FieldTag::REMAIN_GROUP_4]],
            ]
        );

        $collect = $collect->merge($remainFields);

        return $this->formatStandard($collect);
    }
}
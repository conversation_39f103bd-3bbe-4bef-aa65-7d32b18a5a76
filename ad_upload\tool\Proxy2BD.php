<?php

namespace app\ad_upload\tool;

/**
 * 转发大数据
 */
class Proxy2BD
{
    /**
     * @param $data
     * @return true|void
     */
    public function sdkSendV2($data)
    {
        if (APP_EVN == 'DEV') {
            return; //测试环境不转发
        }
        $url   = "https://data.910app.com/sdk_v2";
        $param = json_encode($data);
        $crypt = new SmSdkCrypt();
        $param = $crypt->encrypt($param);
        for ($i = 0; $i < 3; $i++) {
            $options['http'] = [
                'timeout' => 10,
                'method'  => 'POST',
                'header'  => 'Content-type:application/x-www-form-urlencoded',
                'content' => $param,
            ];
            $context         = stream_context_create($options);
            file_get_contents($url, false, $context);
            $code = $http_response_header[0];
            if ($code == "HTTP/1.1 200 OK") {
                break;
            }
        }
        \Plus::$app->log->info($data, [], 'sdk_send');
    }

}
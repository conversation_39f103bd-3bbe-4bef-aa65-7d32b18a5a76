<?php

namespace app\extension\Support\CommonCenter\ConfCenter;

/**
 * Uri存放类
 */
class Uri
{
    /**
     * Uri：token请求地址
     */
    const URI_TOKEN = '/api/auth/get-access-token';
    /**
     * Uri: 游戏原名列表获取地址
     */
    const URI_CP_GAME = '/game/cp-games/list';
    /**
     * Uri: 游戏统计名列表获取地址
     */
    const URI_GAME = '/game/games/list';
    /**
     * Uri: 游戏前端名列表获取地址
     */
    const URI_APP_SHOW = '/game/game-app-show-name/list';
    /**
     *
     */
    const URI_APP_CHANNEL = '/channel/app-channel/list';
    /**
     *
     */
    const URI_PACKAGE_MAIN_CHANNEL = '/channel/package-channel-main/list';
    /**
     *
     */
    const URI_PACKAGE = '/game/packages/list';
    /**
     * Uri:推广分类
     */
    const URI_POPULARIZE_MEDIA = '/channel/popularize-media/list';

    /**
     * Uri:通用下拉选择选项获取
     */
    const URI_MULTI_OPTIONS = '/api/common/options';

    /**
     * 下来配置选项
     */
    const URI_COMMON_KV = '/game/common-kv-config/list';

    /**
     * 增量上报配置列表
     */
    const URI_CONDITION_RULE = '/operate/report-condition-rule/list';

    const URI_FINISH_REPORT = '/operate/report-condition-rule/finish';

    /**
     * 生成微信小游戏推广短链
     * @link http://**************:3030/project/6239/interface/api/40965
     */
    const URI_WX_LINK = '/mini/marketing/wx-url-link';

    /**
     * 虚拟付费上报规则获取
     *
     * @link http://**************:3030/project/6239/interface/api/41515
     */
    const URI_VIRTUAL_PAYMENT_RULES = '/operate/virtual-increment-rules/list';

    /**
     * 广告变现抓取配置获取
     * @link http://**************:3030/project/6239/interface/api/45798
     */
    const URI_CRAWLER_AD_GAMES = '/mini/spy/games';

    /**
     * 在中台获取微信access_token
     * @link http://**************:3030/project/6239/interface/api/45805
     */
    const URI_WX_TOKEN = '/mini/token/wx';

    /**
     * 正式环境状态索引
     */
    const HOST_MAIN = 1;

    protected const HOSTS = [
        1 => 'http://biz-api.sm910.com',
    ];

    /**
     * 获取host地址
     *
     * @param int $type 环境参数,例如区分正式环境和测试环境
     *
     * @return string
     */
    public static function getHost(int $type = self::HOST_MAIN): string
    {
        return static::HOSTS[$type] ?: '';
    }
}
<?php

namespace app\service\AdvertiserData\Components\Helpers;

use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Contracts\SchemeContract;
use Aura\SqlQuery\Common\DeleteInterface;
use Aura\SqlQuery\Common\InsertInterface;
use Aura\SqlQuery\Common\SelectInterface;
use Aura\SqlQuery\Common\UpdateInterface;
use Aura\SqlQuery\QueryFactory;

/**
 *
 */
class WrapQuery
{
    /**
     * 汇总条数sql
     * @param string $sql
     * @return string
     */
    public static function countRowQuery(string $sql): string
    {
        $stm = static::warpQuery($sql, 'total_body');

        $stm->cols(['count(1) as total']);

        return $stm->__toString();
    }

    /**
     * 汇总行sql
     * @param string $sql
     * @param array  $cols
     * @return string
     */
    public static function totalRowQuery(string $sql, array $cols = []): string
    {
        $stm = static::warpQuery($sql, 'total_body');
        $stm->cols($cols);

        return $stm->__toString();
    }

    /**
     * 按天汇总
     * @param string $sql
     * @param array  $cols
     * @return string
     */
    public static function summaryByDateQuery(string $sql, array $cols = []): string
    {
        Arr::prepend($cols, 'tday');

        $stm = static::warpQuery($sql, 'total_body');
        $stm->cols($cols);
        $stm->groupBy(['tday']);

        return $stm->__toString();
    }

    /**
     * 包裹一层sql
     *
     * @param string         $sql
     * @param                $alias
     * @return DeleteInterface|InsertInterface|SelectInterface|UpdateInterface
     */
    public static function warpQuery(string $sql, $alias)
    {
        $builder = new QueryFactory('mysql');
        $stm     = $builder->newSelect();

        return $stm->fromSubSelect($sql, $alias);
    }

}
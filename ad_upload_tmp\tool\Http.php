<?php

namespace app\ad_upload_tmp\tool;

/**
 * curl 请求
 * Class Http
 * @package Plus\Net
 * phpcs:disable
 */
class Http
{

    private $url;
    private $proxyIp;
    private $proxyPort;
    private $timeOut;
    private $transferTimeOut;

    /**
     * Http constructor.
     * @param string $url             网址
     * @param string $proxyIp         代理ip
     * @param int    $proxyPort       代理端口
     * @param int    $timeOut         超时
     * @param int    $transferTimeOut 传送数据超时
     * @throws \Exception
     */
    public function __construct($url, $proxyIp = '', $proxyPort = 0, $timeOut = 10, $transferTimeOut = 300)
    {

        //URL地址
        if (!$url) {
            throw new \Exception("必须指定URL地址！");
        }
        $this->url             = $url;
        $this->timeOut         = $timeOut;
        $this->transferTimeOut = $transferTimeOut;

        //代理服务器I的设置
        if ($proxyIp) {
            $this->proxyIp   = $proxyIp;
            $this->proxyPort = $proxyPort ? $proxyPort : 80;
        }
    }

    /**
     * 测试
     * @return false|string
     */
    public function test()
    {
        if (APP_EVN != 'DEV') {
            return false;
        }
        return '{"data":"上报成功","message":"test","status":200}';
    }

    /**
     * 发送 get 请求
     * @param null|array $params
     * @return bool|string
     */
    public function get($params = null, $headers = [])
    {
        $testRs = $this->test();
        if($testRs){
            return $testRs;
        }
        //组合带参数的URL
        $url = $this->url;

        if ($params && is_array($params)) {
            $url = $url . '?' . http_build_query($params);
        }

        //初始化curl
        $curl = curl_init();
        if ($this->proxyIp && $this->proxyPort) {
            $proxy = "http://{$this->proxyIp}:{$this->proxyPort}";
            curl_setopt($curl, CURLOPT_PROXY, $proxy);
        }
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $this->timeOut);
        curl_setopt($curl, CURLOPT_TIMEOUT, $this->transferTimeOut);

        if (!empty($headers)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            ;
        }

        $content = curl_exec($curl);
        curl_close($curl);

        return $content;
    }

    /**
     * 发送post 请求
     * @param null|array $params     body 参数
     * @param bool       $fileUpload 是否上传文件
     * @param array      $headers    header 头 array("Content-Type: application/json");// 注意header头，格式k:v
     * @return bool|string
     */
    public function post($params = null, $fileUpload = false, $headers = [])
    {
        $testRs = $this->test();
        if($testRs){
            return $testRs;
        }

        //初始化curl
        $curl = curl_init();
        if ($this->proxyIp && $this->proxyPort) {
            $proxy = "http://{$this->proxyIp}:{$this->proxyPort}";
            curl_setopt($curl, CURLOPT_PROXY, $proxy);
        }
        curl_setopt($curl, CURLOPT_URL, $this->url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, $this->timeOut);
        curl_setopt($curl, CURLOPT_TIMEOUT, $this->transferTimeOut);

        if (!empty($headers)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            ;
        }
        //设置POST参数
        curl_setopt($curl, CURLOPT_POST, 1);

        if ($params) {
            if (is_array($params)) {
                $json = false;
                foreach ($headers as $v) {
                    if (stripos($v, 'application/json') !== false) {
                        $json = true;
                    }
                }
                if ($fileUpload) {
                    curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
                } elseif ($json) {
                    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params, JSON_UNESCAPED_UNICODE));
                } else {
                    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($params));
                }
            } else {
                if (is_string($params)) {
                    curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
                }
            }
        }// end if()

        $content = curl_exec($curl);
        curl_close($curl);

        return $content;
    }

    /**
     * post 发送 json 数据
     * @param array|string $params
     * @return bool|string
     */
    public function postJson($params)
    {
        return $this->post($params, false, ['Content-Type: application/json']);
    }
}

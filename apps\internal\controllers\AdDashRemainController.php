<?php

namespace app\apps\internal\controllers;

use app\apps\internal\Traits\AdIndexCalculators;
use app\apps\internal\Traits\ColumnsInteract;
use app\extension\Constant\AD\AdEnum;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\Zakia;
use app\logic\advertise\AdDashLogic;
use app\logic\advertise\AdDashRemainLogic;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\ConfigService\BasicServ;
use app\service\General\GeneralOptionServ;
use Smarty\Exception;

/**
 * @AdDashRemainController 广告留存查询
 */
class AdDashRemainController extends BaseTableController
{
    use AdIndexCalculators, ColumnsInteract;

    /**
     * 数据获取
     * @param Collection $params
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        $today    = date('Y-m-d');
        $adLevel  = $params->pull('ad_level', AdEnum::AD_PLAN);
        $groups   = Arr::wrap($params->pull('groups', []));
        $paginate = [
            'page'      => $params->pull('page', 1),
            'page_size' => $params->pull('page_size', 100),
            'offset'    => ($params->pull('page', 1) - 1) * $params->pull('page_size', 100),
        ];

        if (empty($groups)) $groups = array_keys(ColumnManager::returnAdRelation($adLevel));
        $fullGroups = ColumnManager::matchRelationByGroups(ColumnManager::returnAdRelation($adLevel), $groups);

        if ($params->has('sort')) {
            $sf        = $params->pull('sort');
            $sort[$sf] = $params->pull('order', 'desc') == 'ascend' ? 'asc' : 'desc';
        }
        else {
            $sort = ['new_user' => 'desc'];
            if (in_array('tday', $groups)) $sort['tday'] = 'asc';
        }

        $paramArray               = $params->toArray();
        $paramArray['range_date'] = [
            Arr::pull($paramArray, 'range_date_start', $today),
            Arr::pull($paramArray, 'range_date_end', $today),
        ];
        sort($paramArray['range_date']);

        $paramArray['show_type'] = Arr::pull($paramArray, 'remain_type', 0);
        $result                  = (new AdDashRemainLogic())->dashData($paramArray, $fullGroups, $paginate, $sort);

        if (!empty($result['list'])) {
            $list             = &$result['list'];
            $groupIndex       = array_fill_keys($groups, '');
            $configBasic      = new BasicServ();
            $optionsServ      = new GeneralOptionServ();
            $constConfCollect = $configBasic->getMultiOptions([
                'platform_id', 'promotion_id', 'department_id', 'user_id',
                'cp_game_id:all', 'game_id', 'app_show_id', 'channel_main_id',
            ])->put('promotion_channel_id', $optionsServ->listChannelOptions());

            $processFn   = [];
            $processFn[] = static function (&$target) use ($groupIndex) {
                $rowOption            = array_intersect_key($target, $groupIndex);
                $target['row_option'] = $rowOption;
            };
            $processFn[] = $this->replaceColumnDefine($constConfCollect);

            // 包号标签追加
            if (in_array('package_id', $groups)) {
                $listPackages  = array_filter(array_column($list, 'package_id'));
                $packageTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'package_id', 'id' => $listPackages]) ?? []);

                if (!empty($packageTagMap)) {
                    $packageTagAppendFn = function (&$target) use ($packageTagMap) {
                        if (
                            !empty($target['package_id'])
                            && $target['package_id'] != '-'
                        ) {
                            $packageId              = $target['package_id'];
                            $target['package_tags'] = array_values($packageTagMap[$packageId] ?? []);
                        }
                    };

                    array_unshift($processFn, $packageTagAppendFn);
                }
            }
            // 推广子渠道标签
            if (
                in_array('promotion_channel_id', $groups)
                || in_array('package_id', $groups)
            ) {
                $listChannel   = array_column($list, 'promotion_channel_id');
                $channelTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'channel_id', 'id' => $listChannel]) ?? []);

                if (!empty($channelTagMap)) {
                    $channelTagAppendFn = function (&$target) use ($channelTagMap) {
                        if (!empty($target['promotion_channel_id']) && $target['promotion_channel_id'] != '-') {
                            $channelId              = $target['promotion_channel_id'];
                            $target['channel_tags'] = array_values($channelTagMap[$channelId] ?? []);
                        }
                    };

                    array_unshift($processFn, $channelTagAppendFn);
                }
            }
            // 游戏统计名标签
            if (
                in_array('game_id', $groups)
                || in_array('package_id', $groups)
            ) {
                $listGames  = array_column($list, 'game_id');
                $gameTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'game_id', 'id' => $listGames]) ?? []);

                if (!empty($gameTagMap)) {
                    $gameTagAppendFn = function (&$target) use ($gameTagMap) {
                        if (!empty($target['game_id']) && $target['game_id'] != '-') {
                            $gameId              = $target['game_id'];
                            $target['game_tags'] = array_values($gameTagMap[$gameId] ?? []);
                        }
                    };
                    array_unshift($processFn, $gameTagAppendFn);
                }
            }
            // section: 补全广告信息
            (new AdDashLogic())->fillAdDimensionName($list, $fullGroups);
            $this->processingLine($list, $groups, $processFn);
        }

        return $result;
    }

    /**
     * 表头获取
     *
     * @param Collection $params
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $baseCollect   = [
            'tday', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id',
            'promotion_channel_id', 'promotion_id', 'platform_id',
            'department_id', 'user_id',
        ];
        $adBaseCollect = [
            'ad_account', 'account_id', 'campaign_name', 'campaign_id',
            'plan_name', 'plan_id', 'creative_name', 'creative_id', 'account_name',
        ];

        $newBaseCollect = [
            'new_user', 'remain_days', 'remain_current',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'ad_base', 'label' => '广告信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],

                    ['value' => 'remain_group_1', 'label' => '次留-30留'],
                    ['value' => 'remain_group_2', 'label' => '45留-180留'],
                    ['value' => 'remain_group_3', 'label' => '210留-360留'],
                    ['value' => 'remain_group_4', 'label' => '360留-720留'],
                ],
            ],
        ];

        $fields = $this->tableFields($params->toArray());

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'] ?? '';
            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $adBaseCollect)) {
                $field['classify'] = ['attrs', 'ad_base'];
            }
            elseif (in_array($dIndex, $newBaseCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }
}
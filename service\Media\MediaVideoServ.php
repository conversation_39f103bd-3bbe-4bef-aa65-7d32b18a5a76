<?php

namespace app\service\Media;

use app\extension\FakeDB\FakeDB;
use app\extension\Support\Collections\Arr;
use app\service\AdvertiserData\Components\Helpers\Convert;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\General\Helpers\TableConst;
use app\service\Media\Helper\MediaTableConst;
use Spiral\Database\Injection\Fragment;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\SelectQuery;

class MediaVideoServ
{
    const QB_MODE_ALL = 31;
    const QB_MODE_TASK_SCRIPT = 16;
    const QB_MODE_DEMO_VIDEO = 8;
    const QB_MODE_ACCOUNT = 4;
    const QB_MODE_PUBLISH_TASK = 2;
    const QB_MODE_VIDEO_EXT = 1;

    const MODE_ALL = 4;
    const MODE_SUMMARY = 2;
    const MODE_LIST = 1;

    /**
     * @return void
     */
    public function getInfoList(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $mode = -1
    ): array
    {
        $qb     = $this->commonTableQuery(...func_get_args());
        $result = [];

        if ($mode & static::MODE_LIST) {
            $qb->columns($this->sqlFields(!empty($groups), false));

            $infoQb = clone $qb;

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    if ($g == 'account_id') {
                        $g = 'live_account.account_id';
                    }
                    if ($g == 'job_kind') {
                        $g = 'job_kind_id';
                    }
                    if ($g == 'media_platform') {
                        $g = 'media_platform_id';
                    }
                    if ($g == 'use_kind_name') {
                        $g = 'use_kind';
                    }
                    if ($g == 'business_ownership_name') {
                        $g = 'business_ownership';
                    }

                    $infoQb->groupBy($g);
                }
            }

            $noPageQb = clone $infoQb;

            if (!empty($paginate) && (!empty($params) || !empty($groups))) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;

                $infoQb->limit($pageSize)->offset(($page - 1) * $pageSize);
            }

            if (!empty($sort)) {
                $infoQb->orderBy($sort);
            }

            $result['list'] = $infoQb->fetchAll();

            if (empty($params)) {
                $result['total'] = $this->getConn()->select()->from(MediaTableConst::DWD_MEDIA_VIDEO)->count();
            }
            else {
                $result['total'] = $this
                    ->getConn()
                    ->select()
                    ->from(new Fragment('(' . (clone $noPageQb)->__toString() . ') as total_body'))
                    ->count();
            }
        }

        if ($mode & static::MODE_SUMMARY) {
            $summaryQb = clone $qb;
            $summaryQb->columns($this->sqlFields(true, true));
            $result['summary'] = $summaryQb->fetchAll()[0] ?? [];
        }

        return $result;
    }

    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $qbMode
     * @return SelectQuery
     */
    public function commonTableQuery(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $qbMode = -1
    ): SelectQuery
    {
        $qb = $this->getQueryBuilder($params, $groups, $paginate, $sort);
        $this->whereMatch($qb, $params);

        return $qb;
    }


    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $qbMode
     * @return SelectQuery
     */
    private function getQueryBuilder(array $params = [], array $groups = [], array $paginate = [], array $sort = [], int $qbMode = -1): SelectQuery
    {
        $db      = $this->getConn();
        $mainSub = $db->select()->from(MediaTableConst::DWD_MEDIA_VIDEO);

        if (empty($params)) {
            if (isset($sort['tday'])) {
                $mainSub->orderBy('publish_time', $sort['tday']);
            }

            if (!empty($paginate) && empty($groups)) {
                ['page' => $page, 'page_size' => $pageSize] = $paginate;
                $mainSub->limit($pageSize)->offset(($page - 1) * $pageSize);
            }
        }
        else {
            $this->matchWheres($mainSub, $params);
        }

        $qb = $db->select()->from(new Fragment('(' . $mainSub->__toString() . ') as dwd_video'));

        if ($qbMode & static::QB_MODE_PUBLISH_TASK) {
            $qb
                ->leftJoin(MediaTableConst::MEDIA_PUBLISH_TASK, 'publish_task')
                ->on(['publish_task.id' => 'dwd_video.task_id']);
        }

        if ($qbMode & static::QB_MODE_VIDEO_EXT) {
            $qb
                ->leftJoin(MediaTableConst::DWS_MEDIA_VIDEO_EXT, 'video_ext')
                ->on([
                    'dwd_video.video_fitid' => 'video_ext.video_fitid',
                ]);
        }

        if (
            $qbMode & static::QB_MODE_PUBLISH_TASK
            && $qbMode & static::QB_MODE_DEMO_VIDEO
        ) {
            $qb
                ->leftJoin(MediaTableConst::MEDIA_DEMO_VIDEO, 'demo_video')
                ->on([
                    'demo_video.video_num' => 'publish_task.video_num',
                ]);
        }

        if ($qbMode & static::QB_MODE_ACCOUNT) {
            $qb
                ->leftJoin(TableConst::BASE_LIVE_ACCOUNT, 'live_account')
                ->on([
                    'live_account.account_id' => 'dwd_video.account_id',
                ]);
        }

        if (
            ($qbMode & static::QB_MODE_PUBLISH_TASK) &&
            ($qbMode & static::QB_MODE_TASK_SCRIPT)
        ) {
            $qb
                ->leftJoin('ddc_platform.media_publish_task_scripts', 'task_script')
                ->on([
                    'task_script.id' => 'publish_task.script_id',
                ]);
        }

        return $qb;
    }

    /**
     * @param SelectQuery $qb
     * @param array $params
     * @return void
     */
    protected function matchWheres(SelectQuery &$qb, array $params = [])
    {
        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            $rangeDate = [
                $params['range_date_start'],
                $params['range_date_end'],
            ];
            sort($rangeDate);

            $qb->where('publish_time', 'between', $rangeDate[0] . ' 00:00:00', $rangeDate[1] . ' 23:59:59');
        }

        if (!empty($params['media_video_title'])) {
            $qb->where('video_title', 'like', "%{$params['media_video_title']}%");
        }

        if (!empty($params['media_video_topic'])) {
            $qb->where('video_topic', 'like', "%{$params['media_video_topic']}%");
        }

        if (!empty($params['video_fitid'])) {
            $qb->where('video_fitid', 'like', "%{$params['video_fitid']}%");
        }

        if (isset($params['task_id'])) {
            QueryBuilderHelper::baseBuild($qb, 'task_id', $params['task_id']);
        }

        // 转换到task_id查询
        if ($this->checkIsWhereWithTask($params)) {
            $subTaskQuery = $this->whereSubChangeTask($params);
            $qb->where('task_id', 'in', $subTaskQuery);
        }
        // 根据搜索条件转为account_id
        if ($this->checkIsWhereWithAccount($params)) {
            $subTaskQuery = $this->whereSubChangeAccount($params);
            $qb->where('account_id', 'in', $subTaskQuery);
        }
    }

    /**
     * @param array $params
     * @return bool
     */
    private function checkIsWhereWithTask(array $params): bool
    {
        return isset($params['video_title'])
            || isset($params['video_topic'])
            || isset($params['demo_video_id'])
            || isset($params['mc_id'])
            || isset($params['mc_video_id'])
            || isset($params['task_script_name'])
            || isset($params['data_label']);
    }

    /**
     * @param array $params
     * @return bool
     */
    private function checkIsWhereWithAccount(array $params): bool
    {
        return isset($params['media_platform_id'])
            || isset($params['operations_manager'])
            || isset($params['use_kind'])
            || isset($params['business_ownership']);
    }

    /**
     * @param array $params
     * @return SelectQuery
     */
    private function whereSubChangeTask(array $params): SelectQuery
    {
        $taskServ = new MediaTaskServ();
        $qb       = $taskServ->commonTableQuery($params);

        $qb->columns(['publish_task.id'])->distinct();
        return $qb;
    }

    /**
     * @param array $params
     * @return SelectQuery
     */
    private function whereSubChangeAccount(array $params): SelectQuery
    {
        $serv = new LiveAccountInfoServ();
        $qb   = $serv->commonTableQuery($params);

        $qb->columns(['account_id'])->distinct();
        return $qb;
    }

    /**
     * @param bool $isHasGroup
     * @param bool $isSummary
     * @return array|Fragment[]|string[]
     */
    private function sqlFields(bool $isHasGroup = true, bool $isSummary = false): array
    {
        $info = [
            new Fragment('DATE(publish_time) as tday'),
            'publish_time as publish_time',
            'dwd_video.account_id as account_id',
            'live_account.account as account_name',
            'dwd_video.task_id as task_id',
            'live_account.business_ownership as business_ownership',
            'live_account.operations_manager as operations_manager',
            'live_account.use_kind as use_kind',
            'live_account.type as media_platform_id',
            'dwd_video.video_title as media_video_title',
            'publish_task.video_title as video_title',
            new Fragment("REPLACE(dwd_video.video_topic, ' ', '&nbsp;') as media_video_topic"),
            'demo_video.mc_id as mc_id',
            'demo_video.mc_video_id as mc_video_id',
            'demo_video.demo_video_id as demo_video_id',
            'demo_video.demo_video_title as demo_video_title',
            'task_script.name as task_script_name',
            'task_script.kind as kind',
            'publish_task.data_label as data_label',
            'dwd_video.video_fitid as video_fitid',
            'task_script.kind as job_kind_id',
            'demo_video.video_duration as video_duration',

            new Fragment("REPLACE(publish_task.`video_topic`, ' ', '&nbsp;') as video_topic"),
        ];

        if ($isHasGroup) {
            $num = [
                new Fragment('COALESCE(SUM(play_one), 0) as play_one'),
                new Fragment('COALESCE(SUM(play_two), 0) as play_two'),
                new Fragment('COALESCE(SUM(play_three), 0) as play_three'),
                new Fragment('SUM(play_count) as play_count'),
                new Fragment('SUM((completion_rate_5s * play_count) / 100) as completion_5s'),
                new Fragment('SUM(like_count) as like_count'),
                new Fragment('SUM(comment_count) as comment_count'),
                new Fragment('SUM(share_count) as share_count'),
                new Fragment('SUM(fans_increase_count) as fans_increase_count'),
                new Fragment('SUM(homepage_access_count) as homepage_access_count'),
                new Fragment('SUM(completion_rate * play_count / 100) as completion_play'),
                new Fragment('SUM(avg_play_duration) as avg_play_duration'), // 播放总时长
            ];
        }
        else {
            $num = [
                new Fragment('COALESCE(play_one, 0) as play_one'),
                new Fragment('COALESCE(play_two, 0) as play_two'),
                new Fragment('COALESCE(play_three, 0) as play_three'),
                new Fragment('play_count as play_count'),
                new Fragment('(completion_rate_5s * play_count) / 100 as completion_5s'),
                new Fragment('like_count as like_count'),
                new Fragment('comment_count as comment_count'),
                new Fragment('share_count as share_count'),
                new Fragment('fans_increase_count as fans_increase_count'),
                new Fragment('homepage_access_count as homepage_access_count'),
                new Fragment('completion_rate * play_count / 100 as completion_play'),
                new Fragment('avg_play_duration as avg_play_duration'),
            ];
        }

        if ($isSummary) {
            return $num;
        }
        else {
            return array_merge($info, $num);
        }
    }


    /**
     * @param SelectQuery $qb
     * @param array $params
     *
     * @return void
     */
    protected function whereMatch(SelectQuery &$qb, array $params = [])
    {
        if (!empty($params['range_date_start']) && !empty($params['range_date_end'])) {
            $rangeDate = [
                $params['range_date_start'],
                $params['range_date_end'],
            ];

            sort($rangeDate);

            $qb->where('publish_time', 'between', $rangeDate[0] . ' 00:00:00', $rangeDate[1] . ' 23:59:59');
        }

        if (isset($params['media_platform_id'])) {
            $d = $params['media_platform_id'];

            $qb->where('live_account.type', new Parameter($d));
        }

        if (isset($params['business_ownership'])) {
            $d = $params['business_ownership'];

            $qb->where('business_ownership', new Parameter($d));
        }

        if (isset($params['account_id'])) {
            $d = $params['account_id'];

            $qb->where('dwd_video.account_id', new Parameter($d));
        }

        if (isset($params['video_title'])) {
            $d = $params['video_title'];

            $qb->where('publish_task.video_title', 'like', "%{$d}%");
        }

        if (isset($params['video_topic'])) {
            $d = $params['video_topic'];
            $qb->where('publish_task.video_topic', 'like', "%{$d}%");
        }

        if (isset($params['video_topic:exact'])) {
            $d = $params['video_topic:exact'];
            $qb->where(new Fragment("REPLACE(publish_task.video_topic,' ','' ) = '{$d}'"));
        }

        if (isset($params['video_fitid'])) {
            $d = $params['video_fitid'];
            $qb->where('dwd_video.video_fitid', $d);
        }

        if (!empty($params['media_video_title'])) {
            $qb->where('dwd_video.video_title', 'like', "%{$params['media_video_title']}%");
        }

        if (!empty($params['media_video_topic'])) {
            $qb->where('dwd_video.video_topic', 'like', "%{$params['media_video_topic']}%");
        }

        // 账号归属
        if (isset($params['use_kind'])) {
            $d = $params['use_kind'];
            $qb->where('live_account.use_kind', new Parameter($d));
        }

        // 运营负责人
        if (isset($params['operations_manager'])) {
            $d = $params['operations_manager'];
            $qb->where('operations_manager', new Parameter($d));
        }

        // 视频样片标题或ID
        if (isset($params['demo_video_id'])) {
            $d = $params['demo_video_id'];
            $qb->where('demo_video_id', new Parameter($d));
        }

        // 批创ID
        if (isset($params['mc_id'])) {
            $d = $params['mc_id'];
            $qb->where('mc_id', 'like', "%{$d}%");
        }
        // 批创视频ID
        if (isset($params['mc_video_id'])) {
            $d = $params['mc_video_id'];
            $qb->where('mc_video_id', 'like', "%{$d}%");
        }
        // 视频时长（秒）
        if (isset($params['video_duration'])) {
            $d = $params['video_duration'];
            $qb->where('demo_video.video_duration', 'like', "%{$d}%");
        }

        if (isset($params['task_script_id'])) {
            $d = $params['task_script_id'];
            $qb->where('task_script.id', new Parameter($d));
        }

        if (isset($params['data_label'])) {
            $d = $params['data_label'];
            $qb->where('data_label', new Parameter($d));
        }

        if (isset($params['job_kind'])) {
            $d = $params['job_kind'];
            $qb->where('task_script.kind', new Parameter($d));
        }
    }


}
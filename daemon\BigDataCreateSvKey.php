<?php declare(ticks=1);

namespace app\daemon;

use app\models\AdpPlatform\TbAdpPlanBase;
use app\models\baseConfPlatform\TbAdAccountConf;
use app\models\baseConfPlatform\TbAdAccountExtConf;
use app\models\DataSpy\TbAdSvlinkConf;
use app\models\DdcPlatform\DwdSdkAdsourceGame;
use app\models\DdcPlatform\DwdSdkAdsourcePackage;
use app\models\OriginPlatform\TbAdCost;
use app\models\OriginPlatform\TbAdCreativeCost;
use app\models\OriginPlatform\TbSdkActiveLog;
use app\util\Common;
use Plus\CLI\DaemonProcess;

/**
 * 根据大数据匹配记录创建短链
 * phpcs:disable
 * @package app/daemon
 */
class BigDataCreateSvKey extends DaemonProcess
{
    private const QueueTopic = 'ad_match_log';
    private const WxBotKey   = '144fcd2c-6462-4838-bcad-621c4301496c';

    public function __construct()
    {
    }

    /**
     * @throws \Exception
     */
    public function run()
    {

        //消费
        \Plus::$app->queue->consume(function ($message) {
            try {

                /**
                 * @var \PhpAmqpLib\Message\AMQPMessage $message
                 */
                $queueData = json_decode($message->getBody(), true);
                $message->ack();  //当配置 auto_ack== false，需要手动确认
                if (!$queueData) {
                    return;
                }
                \Plus::$app->log->info(["log" => $queueData], [], 'ad_match_log');
                $waringContent = Common::unicode_decode(str_replace('"', "'", json_encode($queueData)));
                //防止创建短链读取从库为空值
                sleep(0.1);
                //创建短链
                try {
                    $svkey = $this->createSvkey($queueData);
                } catch (\Exception $e) {
                    \Plus::$app->log->warning("短链创建错误,内容：" . $e->getMessage(), [], 'ad_match_log');
                    //Common::qyWxBot(self::WxBotKey, "短链创建错误,内容：" . $e->getMessage());
//                    \Plus::$app->queue->put(self::QueueTopic, $queueData);
                    return;
                }

                if ($svkey === false) {
                    Common::qyWxBot(self::WxBotKey, "短链创建失败,内容：" . $waringContent);
//                    \Plus::$app->queue->put(self::QueueTopic, $queueData);
                    return;
                }


                //更新来源表
                $data = ["SV_KEY" => $svkey];
                switch ($queueData["dimension"]) {
                    case "package":
                        $table = "tb_sdk_active_log";
                        $model = new TbSdkActiveLog();
                        //DEVICE_CODE 32   DEVICE_KEY 32  DEVICE_ID 255
                        if (strlen($queueData["device_code"]) > 32) {
                            $queueData["device_code"] = substr($queueData["device_code"], 0, 32);
                        }
                        if (strlen($queueData["device_key"]) > 32) {
                            $queueData["device_key"] = substr($queueData["device_key"], 0, 32);
                        }
                        $where = ["DEVICE_CODE" => $queueData["device_code"], "PACKAGE_ID" => $queueData["package_id"], "DEVICE_ID" => $queueData["device_id"], "DEVICE_KEY" => $queueData["device_key"]];
                        break;
                    case "package_ad_newlogin":
                        $table = "dwd_sdk_adsource_package";
                        $model = new DwdSdkAdsourcePackage();
                        $where = ["PACKAGE_ID" => $queueData["package_id"], "DEVICE_KEY" => $queueData["device_key"], "CLICK_ID" => $queueData["click_id"]];
                        break;
                    case "game_ad_newlogin":
                        $table = "dwd_sdk_adsource_game";
                        $model = new DwdSdkAdsourceGame();
                        $where = ["SOURCE_ID" => $queueData["source_id"]];
                        break;
                }// end switch()

                if (!empty($queueData["dimension"]) && $queueData["dimension"] == 'package_ad_newlogin') {
                    $log    = $model->asArray()->find($where, ["SV_KEY", "ID"]);
                    $result = true;

                    if ($log) {
                        //判断svkey是否已经更新再进行操作，防止误告警
                        if ($log["SV_KEY"] != $svkey) {
                            $where['ID'] = $log['ID'] ?? 0;
                            $result      = $model->updateByWhere($data, $where);
                        }
                    } else {
                        //防止大数据写入延迟，查不到数据 推回去队列 延迟处理
                        \Plus::$app->queue->put(self::QueueTopic, $queueData);
                        //Common::qyWxBotForAll(self::WxBotKey,"{$table}找不到该数据，更新失败，已重推队列延迟处理,内容：".$waringContent);
                    }
                } else {
                    $log    = $model->asArray()->find($where, ["SV_KEY"]);
                    $result = true;
                    if ($log) {
                        //判断svkey是否已经更新再进行操作，防止误告警
                        if ($log["SV_KEY"] != $svkey) {
                            $result = $model->updateByWhere($data, $where);
                        }
                    } else {
                        //防止大数据写入延迟，查不到数据 推回去队列 延迟处理
                        \Plus::$app->queue->put(self::QueueTopic, $queueData);
                        //Common::qyWxBotForAll(self::WxBotKey,"{$table}找不到该数据，更新失败，已重推队列延迟处理,内容：".$waringContent);
                    }
                }// end if()

                if ($result === false) {
//                    Common::qyWxBotForAll(self::WxBotKey, "更新{$table}表短链失败,内容：" . $waringContent);
                    Common::qyWxBot(self::WxBotKey, "更新{$table}表短链失败,内容：" . $waringContent);
                    return;
                }
            } catch (\Exception $e) {
                Common::qyWxBot(self::WxBotKey, "更新短链失败,内容：" . $e->getMessage());
                return;
            }// end try()
        }, self::QueueTopic);
    }

    //创建短链
    public function createSvkey($data)
    {
        $tbAdSvlinkConfModel = new TbAdSvlinkConf();
        $planId              = $data['plan_id'];
        $user_id             = $data["user_id"] ?? 0;
        $row                 = $tbAdSvlinkConfModel->asArray()->find(["PACKAGE_ID" => $data["package_id"], "PLAN_ID" => $planId]);
        if ($row) {
            $updateData = [];
            //投放人
            if ($user_id && $row['USER_ID'] != $user_id) {
                $updateData["USER_ID"] = $user_id;
            }
            //短链渠道
            if ($row['CHANNEL_ID'] != $data["channel_id"]) {
                $updateData["CHANNEL_ID"] = $data["channel_id"];
            }
            if ($updateData) {
                //更新短链投放人
                $result = $tbAdSvlinkConfModel->updateByWhere($updateData, ["ID" => $row['ID']]);
                if (!$result) {
                    Common::qyWxBotForAll(self::WxBotKey, "更新短链投放人或者渠道失败：{$row['ID']}-{$user_id}-{$data['channel_id']}");
                }
            }
            return $row['ID'];
        }

        //获取计划信息
        $planData = (new TbAdpPlanBase())->getPlanData($planId);
        if (!$planData) {
            //根据消费查询
            $planData = (new TbAdCreativeCost())->getPlanData($planId);
            if (!$planData) {
                $planData = (new TbAdCost())->getPlanData($data["channel_id"] . "_" . $planId);
            }
        }

        //查找投放人
        if (!isset($planData["user_id"]) && $data["advertiser_id"]) {
            $accountExtData         = (new TbAdAccountExtConf())->asArray()->find(["ADVERTISER_ID" => $data["advertiser_id"], "ORDER" => ["UPDATE_TIME" => "DESC"]]);
            $accountData            = (new TbAdAccountConf())->findById($accountExtData["AD_ACCOUNT_ID"]);
            $planData["user_id"]    = $accountExtData["ADV_USER_ID"] ?? 0;
            $planData["ad_account"] = $accountData["AD_ACCOUNT"] ?? '';
        }

        if (!$planData) {
            \Plus::$app->log->error(["log" => json_encode($data)], [], 'createSvkey');
        }

        $adSvlinkConf             = new TbAdSvlinkConf();
        $adSvlinkConf->CHANNEL_ID = $data['channel_id'];
        $adSvlinkConf->CP_GAME_ID = $data['cp_game_id'];
        $adSvlinkConf->GAME_ID    = $data['game_id'];
        $adSvlinkConf->PACKAGE_ID = $data['package_id'];
        $adSvlinkConf->PLAN_ID    = $data['plan_id'];
        $adSvlinkConf->AID        = $data['plan_name'] ? $data['plan_name'] : ($planData["plan_name"] ? $planData["plan_name"] : $data['plan_id']);
        $adSvlinkConf->AD_ACCOUNT = $planData["ad_account"] ? $planData["ad_account"] : "";
        $adSvlinkConf->EXT        = "php创建";
        $adSvlinkConf->USER_ID    = $user_id ? $user_id : ($planData["user_id"] ? $planData["user_id"] : 0);
        $adSvlinkConf->ADD_TIME   = date("Y-m-d H:i:s");
        $adSvlinkConf->STATUS     = 1;
        $adSvlinkConf->OS         = substr($data["package_id"], -4) === '0099' ? 2 : 1;

        return $adSvlinkConf->insert();
    }
}

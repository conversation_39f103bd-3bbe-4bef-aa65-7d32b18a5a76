<?php

namespace app\service\Media;

use app\extension\FakeDB\FakeDB;
use app\service\Media\Components\Matcher\TaskMatcher;
use app\service\Media\Helper\MediaTableConst;
use Spiral\Database\Query\SelectQuery;

class MediaTaskServ
{
    const QB_MODE_ALL = 3;
    const QB_MODE_TASK_SCRIPT = 1;
    const QB_MODE_DEMO_VIDEO = 2;

    /**
     * 拼装关联任务的查询构造
     *
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $sort
     * @param int $qbMode
     * @return SelectQuery
     */
    public function commonTableQuery(
        array $params, array $groups = [], array $paginate = [], array $sort = [], int $qbMode = -1
    ): SelectQuery
    {
        $qb = $this->baseQueryBuilder($qbMode);

        // where 条件拼接
        $matchFn = new TaskMatcher([
            'job_kind'   => 'task_script.kind',
            'data_label' => 'publish_task.data_label'
        ]);
        $matchFn($qb, $params);

        return $qb;
    }

    /**
     *
     *
     * @param int $mode
     * @return SelectQuery
     */
    protected function baseQueryBuilder(int $mode = -1): SelectQuery
    {
        $db = $this->getConn();
        $qb = $db->select()->from(MediaTableConst::MEDIA_PUBLISH_TASK . ' as publish_task');

        if ($mode & self::QB_MODE_TASK_SCRIPT) {
            $qb
                ->leftJoin(MediaTableConst::MEDIA_PUBLISH_TASK_SCRIPT, 'task_script')
                ->on('publish_task.script_id', 'task_script.id');
        }

        if ($mode & self::QB_MODE_DEMO_VIDEO) {
            $qb
                ->leftJoin(MediaTableConst::MEDIA_DEMO_VIDEO, 'demo_video')
                ->on('demo_video.video_num', 'publish_task.video_num');
        }

        return $qb;
    }

    /**
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('ddc_platform');
    }
}
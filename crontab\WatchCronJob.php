<?php
declare(ticks=1);

namespace app\crontab;

use app\daemon\AdIncomeCrawler;
use Crontab\CronManager;

/**
 * 定时任务管理
 */
class WatchCronJob extends CronManager
{
    //多个参数  --p  A参数  --p B参数
    public function __construct()
    {
        $yesterday = date('Y-m-d', strtotime('-1day'));

        $this->config = [
            //spy1.0权限同步基础中台 每10分钟执行
            //            ['rule' => '*/10 * * * * ', 'file' => UserDataPermissionsSync::class],
            //自动创短链，自动更新短链信息
            ['rule' => '* * * * * ', 'file' => AutoCreateSvKey::class],
            // 代金卷报表邮件通知
            ['rule' => '0 10 3 * * ', 'file' => MailCouponDashBoard::class, 'param' => ""],
            //退款同步脚本
            ['rule' => '*/9 * * * * ', 'file' => PaymentVoided::class],
            // 抓取广告收入
//            ['rule' => '1 9,10,11,12,14,15,18 * * *', 'file' => AdIncomeCrawler::class, 'param' => " begin_time={$yesterday} end_time={$yesterday} "],
            // 定时拉取业务payway数据
            ['rule' => '0 0/12 * * *', 'file' => PullBizPayWay::class, 'param' => ""],
        ];
    }

}
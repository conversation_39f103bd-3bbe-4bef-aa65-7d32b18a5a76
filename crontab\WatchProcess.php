<?php

namespace app\crontab;

use app\daemon\AdIncomeCrawler;
use app\daemon\BigDataCreateSvKey;
use app\daemon\SyncLivePlatform;
use app\daemon\SynOaExtUser;
use app\daemon\Universal\Exporter;
use Plus\CLI\ProcessManage;

class WatchProcess extends ProcessManage
{
    public function __construct()
    {
        $this->config = [
            [
                'file' => BigDataCreateSvKey::class,
                'num'  => 3,
            ],
            [
                'file' => SynOaExtUser::class,
                'num'  => 1,
            ],
            [
                'file' => SyncLivePlatform::class,
                'num'  => 1,
            ],
            [
                'file' => AdIncomeCrawler::class,
                'num'  => 1,
            ],
        ];
    }
}
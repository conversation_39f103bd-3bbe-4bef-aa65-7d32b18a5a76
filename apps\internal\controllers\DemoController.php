<?php

namespace app\apps\internal\controllers;

use app\extension\Exception\ParameterException;
use app\extension\Support\Collections\Collection;
use app\service\AdvertiserData\RealtimeIndex;
use app\service\ConfigService\BasicServ;

/**
 * @DemoController
 *                demo控制器， 路径: /internal/*
 */
class DemoController extends BaseController
{

    /**
     * @route {host}/internal/demo/data
     *
     * @return array
     */
    public function dataAction(): array
    {
        $request = \Plus::$app->request;

        $funcMethod = $request->getValue('method') ?? ['data', 'fields'];

        $options = [
            'cp_game_id'      => $request->getValue('cp_game_id'),
            'game_id'         => $request->getValue('game_id'),
            'range_date'      => [
                $request->getValue('range_date_start'),
                $request->getValue('range_date_end'),
            ],
            'package_id'      => $request->getValue('package_id'),
            'page_size'       => $request->getValue('page_size'),
            'page'            => $request->getValue('page'),
            'channel_main_id' => $request->getValue('channel_main_id'),
            'channel_id'      => $request->getValue('channel_id'),
            'platform_id'     => $request->getValue('platform_id'),
            'promotion_id'    => $request->getValue('promotion_id'),
            'department_id'   => $request->getValue('department_id'),
            'user_id'         => $request->getValue('user_id'),
            'order'           => $request->getValue('order') ?? 'ascend', // 升序 or 降序
            'sort'            => $request->getValue('sort') ?? '', // 排序字段
        ];

        $result = [];

        foreach ($funcMethod as $method) {
            if (!is_callable([$this, $method])) {
                continue;
            }

            $d = (array)$this->{$method}($options);

            $result = array_merge($result, $d);
        }

        return $this->success($result);
    }


    /**
     * @param array $options
     * @return array
     */
    protected function data(array $options = []): array
    {
        $serv   = new RealtimeIndex();
        $result = $serv->demo($options);

        $list = &$result['list'];

        $configBasic       = new BasicServ();
        $departmentCollect = $configBasic->queryDepartment();
        $userCollect       = $configBasic->queryAdUser();

        foreach ($list as &$foo) {
            $foo['ad_user']         = '';
            $foo['department_name'] = '';

            if (!empty($foo['department_id'])) {
                $foo['department_name'] = ($departmentCollect->where('key', $foo['department_id'])->all())[0]['val'] ?? '';
            }

            if (!empty($foo['user_id'])) {
                $foo['ad_user'] = ($userCollect->where('key', $foo['user_id'])->all())[0]['val'] ?? '';
            }
        }

        return $result;
    }

    /**
     * @param array $options
     * @return array
     * @throws ParameterException
     */
    protected function fields(array $options = []): array
    {
        return ['fields' => $this->tableFields($options)];
    }


    /**
     * @param Collection $registerFields
     * @param array      $options
     * @return Collection
     */
    protected function buildFields(Collection $registerFields, array $options = []): Collection
    {
        // todo:根据条件组合或时间范围生成N类指标

        return $registerFields;
    }

    protected function prependFields(Collection &$collect, ...$options): void
    {
        // TODO: Implement prependFields() method.
    }
}
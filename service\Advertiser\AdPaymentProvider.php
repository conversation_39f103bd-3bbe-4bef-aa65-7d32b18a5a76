<?php

namespace app\service\Advertiser;

use app\extension\FakeDB\FakeDB;
use app\service\General\BizTagsServ;
use Smarty\Exception;

/**
 * 广告付费明细查询
 *
 */
class AdPaymentProvider
{

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $columns
     * @param bool  $isApiRule
     * @return void
     * @throws Exception
     */
    public function getCreativeInfo(
        array $params, array $groups = [], array $paginate = [], array $columns = [], bool $isApiRule = false
    ): array
    {
        $adChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $powerSQL   = '';
        $db         = $this->getConn();
        $infoTpl    = \Plus::$app->sqlTemplates->createTemplate('sql/ad/ad_payment_creative_info/creative_payment_info.tpl');

        if (!$isApiRule) {
            $powerSQL = \Plus::$service->admin->powerSubSQL();
        }

        $infoTpl
            ->assign('params', $params)
            ->assign('ad_channels', $adChannels)
            ->assign('paginate', $paginate)
            ->assign('paid_mode', $params['target']);

        if (!$isApiRule) {
            $infoTpl->assign('power_join_sql', $powerSQL);
        }

        $infoSQL  = $infoTpl->fetch();
        $totalTpl = \Plus::$app->sqlTemplates->createTemplate('sql/ad/ad_payment_creative_info/creative_payment_info_total.tpl');
        $totalTpl
            ->assign('params', $params)
            ->assign('ad_channels', $adChannels)
            ->assign('paid_mode', $params['target']);

        if (!$isApiRule) {
            $totalTpl->assign('power_join_sql', $powerSQL);
        }

        $totalSQL = $totalTpl->fetch();
        $list     = $db->query($infoSQL)->fetchAll();
        $total    = $db->query($totalSQL)->fetch()['total_row'] ?? 0;

        return ['total' => $total, 'list' => $list];
    }

    /**
     * @param array $params
     * @param array $groups
     * @param array $paginate
     * @param array $columns
     * @param bool  $isApiRule
     * @return array
     * @throws Exception
     */
    public function groupByCore(
        array $params, array $groups = [], array $paginate = [], array $columns = [], bool $isApiRule = false
    ): array
    {
        $adChannels = array_column((new BizTagsServ())->getTagsList(['tag_id' => 885, 'table_name' => 'app_channel']) ?? [], 'DATA_ID');
        $db         = $this->getConn();
        $tpl        = \Plus::$app->sqlTemplates->createTemplate('sql/ad/ad_payment_creative_info/creative_payment_group_core.tpl');
        $countTpl   = \Plus::$app->sqlTemplates->createTemplate('sql/ad/ad_payment_creative_info/creative_payment_group_core_cnt.tpl');

        $tpl
            ->assign('power_sql', str_replace('POWER', 'power', \Plus::$service->admin->getAdminPowerSql()))
            ->assign('ad_channels', $adChannels)
            ->assign('params', $params)
            ->assign('group_by', implode(',', $groups))
            ->assign('paginate', $paginate)
            ->assign('paid_mode', $params['target']);

        $countTpl
            ->assign('power_sql', str_replace('POWER', 'power', \Plus::$service->admin->getAdminPowerSql()))
            ->assign('ad_channels', $adChannels)
            ->assign('params', $params)
            ->assign('group_by', implode(',', $groups))
            ->assign('paid_mode', $params['target']);

        $list     = $db->query($tpl->fetch())->fetchAll();
        $countRow = $db->query($countTpl->fetch())->fetch();

        return [
            'list'  => $list,
            'total' => $countRow['count_rows'] ?? 0,
        ];
    }


    /**
     * 数据连接
     * @return \Spiral\Database\Database|\Spiral\Database\DatabaseInterface
     */
    private function getConn()
    {
        return FakeDB::connection('doris_entrance');
    }

}
<?php

namespace app\models\AdpPlatform;

use app\util\Common;
use Plus\MVC\Model\ActiveRecord;


/**
 *   广告组
 * */
class TbAdpCampaign extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->adp_platform;
        parent::__construct($data);
    }

    //根据广告组id查询信息
    public function getDataById(array $campaignIds){
        $sql = "SELECT CAMPAIGN_NAME,CAMPAIGN_ID FROM adp_platform.tb_adp_campaign 
                WHERE CAMPAIGN_ID IN('".implode("','",$campaignIds)."')";
        $data =  $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $data = array_column($data,null,"CAMPAIGN_ID");
        return $data;
    }


    //根据名称获取广告组id
    public function getCampaignIdByName(string $name,string $beginDate,string  $endDate){

        $sql = "SELECT CAMPAIGN_ID FROM   adp_platform.tb_adp_campaign b  WHERE CAMPAIGN_NAME LIKE '%{$name}%' ";
        if ($_db = Common::pingDoris()) {
            $this->_db =  $_db;
            $sql.=" ORDER BY ID DESC LIMIT 1000";
        }else{
            $this->_db =  \Plus::$app->adp_platform;
            //使用mysql查询最近3个月的计划名称/短链名称
            $beginTime = date("Y-m-d H:i:s",strtotime($beginDate." -90 days"));
            $sql .= " AND  CAMPAIGN_CREATE_TIME BETWEEN '{$beginTime}' AND '{$endDate} 23:59:59'";
        }

        $IdArr = $this->_db->query($sql)->fetchAll(\PDO::FETCH_ASSOC);
        $IdArr = array_column($IdArr,"CAMPAIGN_ID");

        return $IdArr;
    }
}
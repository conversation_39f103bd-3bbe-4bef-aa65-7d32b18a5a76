<?php

namespace app\logic\log;

use app\extension\Support\Collections\Arr;
use app\extension\Support\Helpers\UtilClosure;
use app\service\ConfigService\BasicServ;
use app\service\Logs\SdkActivateLogServ;
use app\service\Logs\SdkCastleRank;
use app\service\Logs\SdkCrashServ;
use app\service\Logs\SdkRoleFirstLoginServ;
use app\service\Logs\SdkRoleLoginServ;
use app\service\Logs\SdkRoleRankServ;
use app\service\Logs\SdkUserLoginLogServ;
use app\service\Logs\SdkUserPaymentServ;
use app\service\Logs\SdkUserRegisterLogServ;
use app\service\Logs\UserLogQueryServ;
use Spiral\Database\Injection\Fragment;

/**
 *
 */
class UserLogQueryLogic
{
    /**
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function overview(array $params): array
    {
        $sort     = [];
        $paginate = [
            'page'      => Arr::pull($params, 'page', 1),
            'page_size' => Arr::pull($params, 'page_size', 100)
        ];

        if (isset($params['game_server_id'])) {
            $params['game_server'] = $params['game_server_id'];
            unset($params['game_server_id']);
        }

        if (!empty($params['sort'])) {
            $sortField = $params['sort'];
            $order     = ($params['order'] ?? 'descend') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }

        $serv   = new UserLogQueryServ();
        $result = $serv->getOverviewInfo($params, $paginate, $sort);
        $list   = &$result['list'];

        $constMap  = (new BasicServ())->getMultiOptions(['game_id', 'cp_game_id:all']);
        $replaceFn = UtilClosure::changeDataWithCollectFn($constMap);

        foreach ($list as &$item) {
            $replaceFn($item);

            if ($item['key'] == 'role_rank') {
                $item['event_type'] = '角色升级';
            }
            elseif ($item['key'] == 'sdk_activate') {
                $item['event_type'] = '设备激活';
            }
            elseif ($item['key'] == 'role_login') {
                $item['event_type'] = '角色登录';
            }
            elseif ($item['key'] == 'sdk_crash') {
                $item['event_type'] = '崩溃日志';
            }
            elseif ($item['key'] == 'user_register') {
                $item['event_type'] = '账号注册';
            }
            elseif ($item['key'] == 'user_payment') {
                $item['event_type'] = '用户充值';
            }
            elseif ($item['key'] == 'castle_rank') {
                $item['event_type'] = '城堡升级';
            }
            elseif ($item['key'] == 'user_login') {
                $item['event_type'] = '账号登录';
            }
            elseif ($item['key'] == 'create_role_first') {
                $item['event_type'] = '新增创角';
            }
        }

        return $result;
    }

    /**
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function getInfoList(array $params): array
    {
        if (isset($params['game_server_id'])) {
            $params['game_server'] = $params['game_server_id'];
            unset($params['game_server_id']);
        }

        $eventMaps = [
            '设备激活' => SdkActivateLogServ::class,
            '账号注册' => SdkUserRegisterLogServ::class,
            '账号登录' => SdkUserLoginLogServ::class,
            '角色登录' => SdkRoleLoginServ::class,
            '用户充值' => SdkUserPaymentServ::class,
            '崩溃日志' => SdkCrashServ::class,
            '角色升级' => SdkRoleRankServ::class,
            '城堡升级' => SdkCastleRank::class,
            '新增创角' => SdkRoleFirstLoginServ::class
        ];

        $rowOptions = $params['row_options'] ?? '';

        if (empty($rowOptions)) {
            throw new \Exception('行数据缺失');
        }

        $rowOptions = \json_decode($rowOptions, true);

        if (JSON_ERROR_NONE != json_last_error()) {
            throw new \Exception('行数据解析错误');
        }

        Arr::pull($params, 'event_type');
        $eventType = Arr::pull($rowOptions, 'event_type');
        $ev        = $eventMaps[$eventType] ?? null;

        if (!$ev) {
            throw new \Exception('不支持该事件的查询');
        }

        $page     = Arr::pull($params, 'page', 1);
        $pageSize = Arr::pull($params, 'page_size', 100);
        $sort     = ['event_time' => 'DESC'];

        if (!empty($params['sort'])) {
            $sortField = $params['sort'];
            $order     = ($params['order'] ?? 'descend') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];

            unset($params['sort'], $params['order']);
        }

        $options = $params;
        if (!empty($rowOptions['package_id'])) {
            $options['package_id'] = $rowOptions['package_id'];
        }

        $chillCols = $this->getColes($eventType);
        $serv      = new $ev;

        $result = call_user_func_array([$serv, 'getList'], [$options, ['page' => $page, 'page_size' => $pageSize], [], $sort, $chillCols]);

        if (!empty($result['list'])) {
            $list = &$result['list'];

            foreach ($list as &$foo) {
                if (isset($foo['network_type'])) {
                    $network             = $foo['network_type'];
                    $foo['network_type'] = $this->changeWifi($network);
                }

                if (isset($foo['os'])) {
                    $os        = $foo['os'];
                    $foo['os'] = $this->changeOS($os);
                }
            }
        }

        return $result;
    }

    /**
     * @param $type
     * @return array
     */
    private function getColes($type): array
    {
        if ($type == '设备激活') {
            return [
                new Fragment("'设备激活' as event_type"),
                'time as event_time', 'time_server',
                'a1.game_id', 'a1.package_id', 'device_type',
                'os', 'os_version', 'sdk_version',
                'game_version', 'network_type', 'ip',
                'device_code', 'oaid'
            ];
        }
        elseif ($type == '账号注册') {
            return [
                new Fragment("'账号注册' as event_type"),
                'time as event_time', 'time_server',
                'a1.game_id', 'a1.package_id', 'device_type',
                'os', 'os_version', 'sdk_version',
                'game_version', 'network_type', 'ip',
                'device_code', 'login_account', 'core_account'
            ];
        }
        elseif ($type == '账号登录') {
            return [
                new Fragment("'账号登录' as event_type"),
                'time as event_time', 'time_server',

                'a1.game_id', 'a1.package_id', 'device_type',
                'os', 'os_version', 'sdk_version',
                'game_version', 'network_type', 'ip',
                'device_code', 'oaid', 'login_account', 'core_account'
            ];
        }
        elseif ($type == '角色登录') {
            return [
                new Fragment("'角色登录' as event_type"),
                'time as event_time', 'time_server',
                'a1.game_id', 'a1.package_id', 'device_type',
                'os', 'os_version', 'sdk_version',
                'game_version', 'network_type', 'ip',
                'device_code', 'game_server', 'login_account', 'core_account',
                'role_id', 'role_name', 'role_rank', 'role_vip'
            ];
        }
        elseif ($type == '用户充值') {
            return [
                new Fragment("'用户充值' as event_type"),
                'order_time as order_time', 'pay_time as pay_time', 'pay_time as dt',
                'pay_time as event_time', 'a1.game_id', 'a1.package_id', 'ip as ip', 'oaid as oaid',
                'login_account as login_account', 'core_account as core_account',
                'game_server_id as game_server', 'role_id as role_id', 'device_code',
                'role_name as role_name', 'role_rank as role_rank',
                'role_vip as role_vip', 'order_id as order_id',
                'cp_order_id as cp_order_id', 'channel_order_id as channel_order_id',
                'pay_result as pay_result', 'money as money', 'game_coin as game_coin',
                'goods_name as goods_name', 'game_result as game_result',
                'order_type as order_type', 'sdk_version as sdk_version', 'game_version as game_version',
            ];
        }
        elseif ($type == '崩溃日志') {
            return [
                new Fragment("'崩溃日志' as event_type"),
                'time as event_time', 'time_server',
                'a1.game_id', 'a1.package_id', 'device_type',
                'os', 'os_version', 'sdk_version',
                'game_version', 'network_type', 'ip',
                'device_code', 'error_type', 'error_content'
            ];
        }
        elseif ($type == '角色升级') {
            return [
                new Fragment("'角色升级' as event_type"),
                'time as event_time', 'time_server',
                'a1.game_id', 'a1.package_id', 'device_type',
                'os', 'os_version', 'sdk_version', 'ip',
                'device_code', 'login_account', 'core_account',
                'role_id', 'role_name', 'role_rank', 'role_vip'
            ];
        }
        elseif ($type == '城堡升级') {
            return [
                new Fragment("'城堡升级' as event_type"),
                'time as event_time', 'time_server',
                'a1.game_id', 'a1.package_id', 'device_type',
                'os', 'os_version', 'sdk_version', 'ip',
                'device_code', 'login_account', 'core_account',
                'role_id', 'role_name', 'role_rank', 'role_vip'
            ];
        }
        elseif ($type == '新增创角') {
            return [
                new Fragment("'新增创角' as event_type"),
                'time as event_time', 'a1.game_id as game_id', 'a1.package_id as package_id',
                'device_key as device_key', 'game_server as game_server',
                'login_account as login_account', 'core_account as core_account',
                'role_name as role_name', 'role_rank as role_rank', 'role_vip as role_vip',
                'ip as ip', 'time_server as time_server', 'role_id as role_id',
            ];

        }

        return [];
    }

    /**
     * @param $network
     * @return string
     */
    private function changeWifi($network): string
    {
        if ($network == 0) {
            return '没有网络';
        }
        elseif ($network == 1) {
            return 'WIFI';
        }
        elseif ($network == 2) {
            return '2G';
        }
        elseif ($network == 3) {
            return '3G';
        }
        elseif ($network == 4) {
            return 'WAP';
        }
        elseif ($network == 5) {
            return '4G';
        }
        elseif ($network == 6) {
            return '5G';
        }
        else {
            return 'Other';
        }
    }

    /**
     * @param $os
     * @return string
     */
    private function changeOS($os): string
    {
        if ($os == 1) {
            return 'Android';
        }
        elseif ($os == 2) {
            return 'IOS';
        }
        elseif ($os == 3) {
            return 'WINPHONE';
        }
        elseif ($os == 4) {
            return 'windows';
        }

        return 'other';
    }
}
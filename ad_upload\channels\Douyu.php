<?php


namespace app\ad_upload\channels;

use app\ad_upload\contract\AdBaseInterface;
use app\ad_upload\tool\Http;

/**
 * 斗鱼平台回调
 *
 * Class Douyu
 * @package Data\Ad
 */
class Douyu extends AdBaseInterface
{
    /**
     * eventType 激活
     */
    const TYPE_ACTIVE = 'app_activation';
    /**
     * eventType 注册
     */
    const TYPE_REGISTER = 'app_register';
    /**
     * eventType 付费
     */
    const TYPE_PAY = 'app_pay';
    /**
     * eventType 次日留存
     */
    const TYPE_RELAY = 'app_relay';
    /**
     * eventType 7日留存
     */
    const TYPE_7RELAY = 'app_7relay';

    const DEVICE_IOS = 'ios';

    const DEVICE_ANDROID = 'android';

    const SIGNATURE_KEY = '2bf452361718455aba227af050b4e36a';


    /**
     * 上报激活
     * @param array $info
     * @param array $ext
     */
    public function uploadActive($info, $ext = [])
    {
        $this->upload($info, self::TYPE_ACTIVE);
    }


    /**
     * 上报注册
     * @param array $info
     * @param array $ext
     */
    public function uploadRegister($info, $ext = [])
    {
        $this->upload($info, self::TYPE_REGISTER);
    }

    /**
     * 上报充值
     * @param array $info
     * @param array $ext
     */
    public function uploadPay($info, $ext = [])
    {
        $this->upload($info, self::TYPE_PAY);
    }


    /**
     * 上报留存
     * @param array $info
     * @param array $ext
     */
    public function uploadRemain($info, $ext = [])
    {
        // code this...
    }

    /**
     * @param $info
     * @param $type
     * @return false|void
     * @throws FreeSQLException
     */
    public function upload($info, $type)
    {
        $logInfo                 = $info['log_info'] ?? [];
        $logInfo['channel_code'] = 'douyu';
        $logInfo['log_type']     = 'reported_platform_log';
        $ext                     = $info['EXT_CLICK'];
        $extSource               = $info['EXT'];
        $callbackUrl             = $info['CALLBACK_URL'] ?? '';
        $dyid                    = $info['CID'] ?? '';

        if (empty($extSource['akey'])) {
            return false;
        }

        if (empty($callbackUrl)) {
            return false;
        }

        $callbackUrl = urldecode($callbackUrl);

        if (empty($ext['source']) || empty($dyid) || empty($ext['dtype'])) {
            return false;
        }
        $uTime = sprintf('%.0f', microtime(true) * 1000);

        $spliceArray = [
            'source'    => 'source=' . $ext['source'],
            'dyid'      => 'dyid=' . $dyid,
            'convTime'  => 'convTime=' . $uTime,
            'eventType' => 'eventType=' . $type,
        ];

        switch (strtolower($ext['dtype'])) {
            case self::DEVICE_IOS:
                if (empty($ext['idfa'])) {
                    return false;
                }
                $spliceArray['idfa'] = 'idfa=' . $ext['idfa'];
                $spliceArray['os']   = 'os=2';
                break;
            case self::DEVICE_ANDROID:
                if (empty($ext['imei']) && empty($info['OAID'])) {
                    return false;
                }

                if (!empty($ext['imei'])) {
                    $spliceArray['imei'] = 'imei=' . $ext['imei'];
                }
                if (!empty($info['OAID'])) {
                    $spliceArray['oaid'] = 'oaid=' . $info['OAID'];
                }

                $spliceArray['os'] = 'os=1';
                break;
            default:
                $spliceArray['os'] = 'os=0';
        }// end switch()

        ksort($spliceArray);
        $implodeString      = implode('&', $spliceArray);
        $callbackUrl       .= '&' . $implodeString;
        $paramString        = parse_url($callbackUrl, PHP_URL_QUERY);
        $key                = $extSource['akey'];
        $paramString       .= '&key=' . $key;
        $signature          = md5($paramString);
        $callbackUrl       .= '&signature=' . $signature;
        $logInfo['request'] = json_encode(['url' => $callbackUrl]);

        $http = new Http($callbackUrl);
        $res  = $http->get();

        $logInfo['response'] = $res;
        $result              = json_decode($res, true);

        if ($result['error'] == 0) {
            $logInfo['reported_status'] = 1;
        } else {
            $logInfo['reported_status'] = -1;
        }
        $this->log($info, $logInfo, $res, $callbackUrl);
        return $result['error'] == 0;
    }

    function uploadLogin($info, $ext = [])
    {
        // TODO: Implement uploadLogin() method.
    }

    function uploadCreateRole($info, $ext = [])
    {
        // TODO: Implement uploadCreateRole() method.
    }
}

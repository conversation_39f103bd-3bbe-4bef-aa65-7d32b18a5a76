<?php

namespace app\service\AdvertiseLive\Components\Matcher;

use app\extension\FakeDB\FakeDB;
use app\service\AdvertiseLive\Helpers\TableConst;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use app\service\AdvertiserData\Components\Matcher\BaseMatch;
use app\service\AdvertiserData\Components\Matcher\Traits\AdChannelAble;
use app\service\AdvertiserData\Components\Matcher\Traits\DepartmentMatchAble;
use app\service\General\Matcher\Traits\TagsMatcher;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;


/**
 * 直播类型条件筛选
 */
class AdLiveMatch extends BaseMatch
{
    use AdChannelAble, TagsMatcher, DepartmentMatchAble;

    /**
     * @return array
     */
    public function processLine(): array
    {
        $newLine = [
            [$this, 'matchTDay'],
            [$this, 'matchAnchor'],
            [$this, 'matchLivePlatform'],
            [$this, 'matchAnchorTeam'],
            [$this, 'matchDockingPartner'],
            [$this, 'matchAssistant'],
            [$this, 'matchOperator'],
            [$this, 'matchLiveAccountId'],
            [$this, 'matchOperationAccountId'],
            [$this, 'matchChannelMainId'],
            [$this, 'matchChannelId'],
            [$this, 'matchChannelTagsQb'],
            [$this, 'matchChannelMainTagsQb'],
            [$this, 'matchPackageTagsQb'],
            [$this, 'matchUserId'],
        ];

        return array_merge(parent::processLine(), $newLine);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchTDay(&$qb, array $params)
    {
        if (
            empty($params['range_date_start']) || empty($params['range_date_end'])
        ) return;

        $rangeDate = array_filter([
            $params['range_date_start'],
            $params['range_date_end'],
        ]);

        sort($rangeDate);

        [
            $timeStart, $timeEnd,
        ] = $rangeDate;

        $startTimeField = $this->getReflectField('start_time');

        $timeStart .= ' 00:00:00';
        $timeEnd   .= ' 23:59:59';

        $qb
            ->where($startTimeField, 'between', $timeStart, $timeEnd);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchAnchor(&$qb, array $params)
    {
        if (empty($params['anchor_id'])) return;

        $data  = $params['anchor_id'];
        $field = $this->getReflectField('anchor_id');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchLivePlatform(&$qb, array $params)
    {
        if (empty($params['live_platform'])) return;

        $data  = $params['live_platform'];
        $field = $this->getReflectField('live_platform');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchAnchorTeam(&$qb, array $params)
    {
        if (empty($params['live_team'])) return;

        $data  = $params['live_team'];
        $field = $this->getReflectField('live_team');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchDockingPartner(&$qb, array $params)
    {
        if (empty($params['docking_partner'])) return;

        $data  = $params['docking_partner'];
        $field = $this->getReflectField('docking_partner');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * 运营助手搜索
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchAssistant(&$qb, array $params)
    {
        if (empty($params['assistant_id'])) return;

        $data  = $params['assistant_id'];
        $field = $this->getReflectField('assistant_id');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * 配置人搜索
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchOperator(&$qb, array &$params)
    {
        if (empty($params['operator_id'])) return;

        $data  = $params['operator_id'];
        $field = $this->getReflectField('operator_id');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param SelectQuery|QueryInterface $qb
     * @param array                      $params
     *
     * @return void
     */
    protected function matchLiveAccountId(&$qb, array &$params)
    {
        if (empty($params['live_account_id'])) return;

        $data  = $params['live_account_id'];
        $field = $this->getReflectField('live_account_id');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * @param       $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchOperationAccountId(&$qb, array &$params)
    {
        if (empty($params['operation_account_id'])) return;

        $data  = $params['operation_account_id'];
        $field = $this->getReflectField('operation_account_id');

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

}
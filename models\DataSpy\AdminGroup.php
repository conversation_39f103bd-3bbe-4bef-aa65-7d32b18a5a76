<?php

namespace app\models\DataSpy;

use Plus\MVC\Model\ActiveRecord;


/**
 * @AdminGroup
 *  SPY系统用户表数据权限
 *
 * @property int       ID
 * @property string    NAME
 * @property string    GROUP_TYPE
 * @property string    CP_GAME_IDS
 * @property string    GAME_IDS
 * @property string    CHANNEL_IDS
 * @property string    CHANNEL_MAIN_IDS
 * @property string    PACKAGE_IDS
 * @property int       USER_ID

 */
class AdminGroup extends ActiveRecord
{
    public $_primaryKey = 'ID';

    public function __construct($data = [])
    {
        $this->_db = \Plus::$app->dataspy;
        parent::__construct($data);
    }

    public function getId(): int
    {
        return $this->ID;
    }

}
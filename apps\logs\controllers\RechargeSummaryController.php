<?php

namespace app\apps\logs\controllers;

use app\apps\internal\controllers\BaseTableController;
use app\extension\Support\Collections\Collection;
use app\logic\log\RechargeSummaryLogic;
use app\logic\log\UserPaymentLogic;
use app\service\BusinessPlatform\OptionServ;
use app\service\ConfigService\BasicServ;
use app\service\SourceData\PaymentServ;
use Spiral\Database\Injection\Parameter;

/**
 * 日志 -> 充值汇总
 *
 * @route /logs/payment-info/*
 *
 */
class RechargeSummaryController extends BaseTableController
{
    /**
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        try {
            return  (new RechargeSummaryLogic())->listInfo($params);

        }
        catch (\UnexpectedValueException $ue) {
            return ['list' => [], 'summary' => [], 'total' => 0];
        }
    }

    protected function fields(Collection $params): array
    {
        $fields = [
            ['title' => '日期', 'dataIndex' => 'tday','sorter'=>true, 'classify' => ['attrs', 'base']],
            ['title' => '游戏原名', 'dataIndex' => 'cp_game_id', 'classify' => ['attrs', 'base']],
            ['title' => '游戏统计名', 'dataIndex' => 'game_id', 'classify' => ['attrs', 'base']],
            ['title' => '包号', 'dataIndex' => 'package_id', 'classify' => ['attrs', 'base']],
            ['title' => '客户端', 'dataIndex' => 'platform_id', 'classify' => ['attrs', 'base']],
            ['title' => '支付渠道', 'dataIndex' => 'payway',  'classify' => ['attrs', 'base']],
            ['title' => '九玩币抵扣', 'dataIndex' => 'decuct_coin','sorter'=>true,  'classify' => ['attrs', 'pay_index']],
            ['title' => '代金券抵扣', 'dataIndex' => 'coupon_money','sorter'=>true,  'classify' => ['attrs', 'pay_index']],
            ['title' => '充值金额', 'dataIndex' => 'money','sorter'=>true,  'classify' => ['attrs', 'pay_index']],
            ['title' => '金额占比', 'dataIndex' => 'money_proportion', 'sorter'=>true, 'classify' => ['attrs', 'pay_index']],
            ['title' => '人数', 'dataIndex' => 'pay_user','sorter'=>true,  'classify' => ['attrs', 'pay_index']],
            ['title' => '人数占比', 'dataIndex' => 'pay_user_proportion','sorter'=>true,  'classify' => ['attrs', 'pay_index']],
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'pay_index', 'label' => '充值信息'],
                ],
            ],
        ];

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * @return Collection
     */
    protected function registerParams(): Collection
    {
        $start = date('Y-m-d 00:00:00');
        $end   = date('Y-m-d 23:59:59');

        return collect([
            ['field' => 'range_date_start', 'default' => $start], // 统计日期开始时间
            ['field' => 'range_date_end', 'default' => $end], // 统计日期结束时间
            ['field' => 'package_id'],
            ['field' => 'game_id'],
            ['field' => 'cp_game_id'],
            ['field' => 'payway'],
            ['field' => 'platform_id'],
            ['field' => 'page_size', 'default' => 100],
            ['field' => 'page', 'default' => 1],
            ['field' => 'order'],
            ['field' => 'sort'],
            ['field' => 'groups'],
            ['field' => 'method', 'default' => ['data', 'fields']],
        ]);
    }

}
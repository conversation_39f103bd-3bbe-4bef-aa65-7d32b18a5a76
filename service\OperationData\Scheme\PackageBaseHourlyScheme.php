<?php

namespace app\service\OperationData\Scheme;

use app\extension\Support\Macroable\Traits\Macroable;
use app\service\AdvertiserData\Components\Helpers\JoinClause;
use app\service\AdvertiserData\Contracts\SchemeContract;
use app\service\AdvertiserData\Traits\JoinFixAble;
use app\service\AdvertiserData\Traits\OperableQuery;
use app\service\AdvertiserData\Traits\Schemer;
use Aura\SqlQuery\QueryFactory;

class PackageBaseHourlyScheme implements SchemeContract
{
    use Macroable, Schemer, JoinFixAble, OperableQuery;

    public const MAIN_TABLE = [
        'table' => 'ddc_platform.dws_package_base_hourly',
        'alias' => 't_base',
    ];

    protected array $joinTables = [];


    protected QueryFactory $queryFactory;

    protected $query;


    public static function NewOne(): self
    {
        return new PackageBaseHourlyScheme();
    }

    public function __construct()
    {
        $this->queryFactory = new QueryFactory('mysql');
    }

    public function __clone()
    {
        $this->queryFactory = clone $this->queryFactory;
        $this->query        = clone $this->query;
    }

    public function fieldReflect(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];
        return [
            'tday'            => $mainTable,
            'cp_game_id'      => $mainTable,
            'thour'           => $mainTable,
            'game_id'         => $mainTable,
            'package_id'      => $mainTable,
            'app_show_id'     => 'POWER',
            'channel_main_id' => 'POWER',
            'channel_id'      => 'POWER',
            'platform_id'     => 'POWER',
            'promotion_id'    => 'POWER.popularize_v2_id',
        ];
    }

    protected function fixedTables(): array
    {
        $mainTable = self::MAIN_TABLE['alias'];
        $fixTables = [];

        $fixTables[] =
            (new JoinClause('left', 'ddc_platform.dws_package_payment_hourly', 't_payment'))
                ->on('t_payment.tday', '=', $mainTable . '.tday')
                ->on('t_payment.thour', '=', $mainTable . '.thour')
                ->on('t_payment.package_id', '=', $mainTable . '.package_id');

        return $fixTables;
    }
}
<?php

namespace app\apps\operator\controllers;

use app\apps\internal\controllers\BaseController;
use app\apps\internal\Helpers\Calculator;
use app\apps\internal\Helpers\ConstHub;
use app\apps\internal\Helpers\IndexCalculators;
use app\apps\internal\Helpers\IndicatorsHelpers;
use app\apps\internal\Helpers\LtvCalculator;
use app\apps\internal\Helpers\RoiCalculator;
use app\apps\internal\Traits\OfflineDash;
use app\apps\internal\Traits\ServHostingAble;
use app\apps\operator\Helpers\ConstFirstLogin;
use app\apps\operator\Traits\FirstLoginTrait;
use app\apps\operator\Traits\OperationCalculators;
use app\apps\operator\Traits\OperatorRequest;
use app\extension\Context;
use app\extension\Support\Collections\Arr;
use app\extension\Support\Collections\Collection;
use app\extension\Support\Helpers\ProcessLine;
use app\extension\Support\Helpers\Zakia;
use app\logic\operator\PackageLtvLogic;
use app\service\AdvertiserData\Components\Helpers\ColumnManager;
use app\service\AdvertiserData\Scheme\BaseScheme;
use app\service\ConfigService\BasicServ;
use app\service\ConfigService\Tables\FirstLoginLtv;
use app\service\General\GeneralOptionServ;
use app\service\OperationData\FirstLoginIndex;
use app\service\OperationData\BasicLtvIndex;
use Plus\Util\Pagination;

/**
 * @FirstLoginLtvController 首登LTV查询
 * @Date                    02/02/2023
 * @Route                   /first-login-ltv/*
 *
 */
class FirstLoginLtvController extends BaseController
{
    use OperatorRequest, OperationCalculators,
        FirstLoginTrait, ServHostingAble, OfflineDash;


    /**
     * @param Collection $params
     *
     * @return array
     */
    protected function fields(Collection $params): array
    {
        $baseCollect = [
            'tday', 'thour', 'cp_game_id', 'game_id', 'app_show_id',
            'package_id', 'channel_main_id', 'channel_id', 'promotion_id',
            'platform_id', 'department_id', 'user_id',
        ];

        $newUserCollect = [
            'firstlogin_user', 'cost_discount',
            'new_user_cost', 'new_user_total_pay',
            'total_ltv', 'total_roi',
        ];

        $classify = [
            [
                'value'    => 'attrs',
                'label'    => '属性设置',
                'children' => [
                    ['value' => 'base', 'label' => '基础信息'],
                    ['value' => 'new_user_base', 'label' => '新用户基础指标'],
                    ['value' => 'tags', 'label' => '标签'],

                    ['value' => 'ltv_group_1', 'label' => 'LTV1-30'],
                    ['value' => 'ltv_group_2', 'label' => 'LTV45-180'],
                    ['value' => 'ltv_group_3', 'label' => 'LTV210-360'],
                    ['value' => 'ltv_group_4', 'label' => 'LTV390-720'],

                    ['value' => 'roi_group_1', 'label' => 'ROI1-30'],
                    ['value' => 'roi_group_2', 'label' => 'ROI45-180'],
                    ['value' => 'roi_group_3', 'label' => 'ROI210-360'],
                    ['value' => 'roi_group_4', 'label' => 'ROI390-720'],
                ],
            ],
        ];

        $options = $params->toArray();
        if (empty($options['groups'])) {
            $options['groups'] = ['tday', 'package_id'];
        }

        $fields = $this->tableFields($options);

        foreach ($fields as &$field) {
            $dIndex = $field['dataIndex'];

            if (in_array($dIndex, $baseCollect)) {
                $field['classify'] = ['attrs', 'base'];
            }
            elseif (in_array($dIndex, $newUserCollect)) {
                $field['classify'] = ['attrs', 'new_user_base'];
            }
        }

        return ['fields' => $fields, 'classify' => $classify];
    }

    /**
     * ltv 数据查询
     *
     * @route /first-login-ltv/data
     *
     * @param Collection $params
     *
     * @return array
     * @throws \Exception
     */
    protected function data(Collection $params): array
    {
        ini_set('memory_limit', '4096M');
        $sort     = [];
        $paginate = [
            'page'      => $params->pull('page'),
            'page_size' => $params->pull('page_size'),
        ];
        $groups   = [];

        if ($params->has('sort')) {
            $sortField = $params->pull('sort');
            $order     = $params->pull('order') == 'ascend' ? 'ASC' : 'DESC';
            $sort      = [$sortField => $order];
        }

        if ($params->has('groups')) {
            $groups = Arr::wrap($params->pull('groups'));

            if (empty($sort)) {
                $sort = [$groups[0] => 'ASC', 'firstlogin_user' => 'DESC'];
            }
        }

        if (empty($sort)) {
            $sort = ['tday' => 'DESC', 'firstlogin_user' => 'DESC'];
        }
        $options = $params->toArray();
        $logic   = new PackageLtvLogic();

        return $logic->getList($options, $groups, $paginate, $sort);


        // ------ 分割线 -------------------------
        // $dateDimension = (int)$params->get('range_date_dimension', ConstFirstLogin::DIMENSION_DAY);
        // // 汇总项获取
        // if ($params->has('groups')) {
        //     $groups = Arr::wrap($params->pull('groups'));
        // }
        // else {
        //     $groups = ['tday', 'package_id'];
        // }
        //
        // [
        //     'pagination' => $pagination,
        //     'sort'       => $sort,
        // ] = $this->changeDefaultParams($params);
        //
        // $options                     = $params->toArray();
        // $options['max_data_day_ago'] = 1;
        //
        // // 格式化月份日期
        // if ($dateDimension === ConstFirstLogin::DIMENSION_MONTH) {
        //     $options['range_date_start'] = date("Y-m-d", strtotime($options['range_date_start']));
        //     $options['range_date_end']   = date("Y-m-d", strtotime("{$options['range_date_end']} +1 month -1day"));
        // }
        // // 看情况是否限制查询的最新日期是哪一天
        // $options['range_date_start'] = $options['range_date_start'] > date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) ? date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) : $options['range_date_start'];
        // $options['range_date_end']   = $options['range_date_end'] > date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) ? date('Y-m-d', strtotime("-{$options['max_data_day_ago']} day")) : $options['range_date_end'];
        //
        // $serv = new FirstLoginIndex();
        //
        // // 基础信息查询拿取新增/首登用户
        // if ($dateDimension === ConstFirstLogin::DIMENSION_WEEK) {
        //     $result = $serv->listByWeek($options, $pagination, $groups, $sort);
        // }
        // elseif ($dateDimension === ConstFirstLogin::DIMENSION_MONTH) {
        //     $result = $serv->listByMonth($options, $pagination, $groups, $sort);
        // }
        // else {
        //     $result = $serv->listByDaily($options, $pagination, $groups, $sort);
        // }
        //
        // $constConfCollect =
        //     (new BasicServ())->getMultiOptions([
        //                                            'platform_id', 'promotion_id', 'department_id',
        //                                            'user_id', 'cp_game_id', 'game_id', 'app_show_id', 'channel_main_id',
        //                                        ])->put('channel_id', (new GeneralOptionServ())->listChannelOptions());;
        //
        // $constConfCollect = $constConfCollect->map(function (Collection $item, $k) {
        //     if ('cp_game_id' === $k || 'game_id' === $k) {
        //         // 过滤'全部游戏'
        //         $item = $item->except([0]);
        //     }
        //     return $item;
        // });
        //
        // $ltvType     = (int)Arr::get($options, 'ltv_type', 0);
        // $list        = &$result['list'];
        // $ltvServ     = new BasicLtvIndex('ddc_platform.dws_firstlogin_ltv_daily', 't_ltv');
        // $processLine = new ProcessLine();
        //
        // $clearUnusedField = function (&$target) {
        //     unset(
        //         $target['ltv_info'],
        //         $target['firstlogin_user_n'],
        //         $target['cost_discount_n']
        //     );
        // };
        //
        // if (
        //     in_array('tday', $groups)
        //     && $dateDimension === ConstFirstLogin::DIMENSION_DAY
        // ) {
        //     $ltvGetUser = IndexCalculators::getSingleValue('firstlogin_user');
        //     $roiGetCost = IndexCalculators::getSingleValue('cost_discount');
        // }
        // else {
        //     $ltvGetUser = IndexCalculators::getValueInCollectByN('firstlogin_user_n');
        //     $roiGetCost = IndexCalculators::getValueInCollectByN('cost_discount_n');
        // }
        //
        // $this->cacheServForStorage($ltvServ, 'ltv');
        // $this->cacheServForStorage($serv, 'base');
        //
        // /**
        //  * section 1: 基于详情数据处理(如添加LTVn数据...)
        //  * ---
        //  */
        //
        // $optionsServ = new GeneralOptionServ();
        //
        // $channelTagAppendFn = fn() => true;
        //
        // if (
        //     in_array('package_id', $groups)
        //     || in_array('channel_id', $groups)
        // ) {
        //     $listChannel   = array_column($list, 'channel_id');
        //     $channelTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'channel_id', 'id' => $listChannel]) ?? []);
        //
        //     if (!empty($channelTagMap)) {
        //         $channelTagAppendFn = function (&$target) use ($channelTagMap) {
        //             if (
        //                 !empty($target['channel_id'])
        //                 && $target['channel_id'] != '-'
        //             ) {
        //                 $channelId              = $target['channel_id'];
        //                 $target['channel_tags'] = array_values($channelTagMap[$channelId] ?? []);
        //             }
        //         };
        //     }
        // }
        //
        // if (isset($options['pay_days_num'])) {
        //     $rangeStart = $options['range_date_start'];
        //
        //     if ($dateDimension == ConstHub::DIMENSION_DAY) {
        //         $options['less_pay_date'] = (new \DateTime($rangeStart))->add(new \DateInterval('P' . ($options['pay_days_num'] - 1) . 'D'))->format('Y-m-d');
        //     }
        // }
        //
        //
        // $calcFn = static function (&$target) use ($options) {
        //     $timeDimension       = $options['range_date_dimension'] ?? ConstHub::DIMENSION_DAY;
        //     $maxDaysNum          = $options['pay_days_num'] ?? 0;
        //     $target['total_ltv'] = 0.00;
        //     $target['total_roi'] = '0.00%';
        //
        //     if ($timeDimension == ConstHub::DIMENSION_DAY && $maxDaysNum > 0) {
        //         $ltvInfo  = $target['ltv_info'] ?? [];
        //         $ltvMoney = array_column($ltvInfo, 'money_all');
        //         if (!empty($ltvMoney)) {
        //             $lastMoney = max($ltvMoney);
        //
        //             if (!empty(floatval($lastMoney)) && !empty(floatval($target['firstlogin_user'] ?? 0))) {
        //                 $target['total_ltv'] = number_format(
        //                     math_eval('x/y', ['x' => $lastMoney, 'y' => $target['firstlogin_user']]),
        //                     2
        //                 );
        //             }
        //             if (!empty(floatval($lastMoney)) && !empty(floatval($target['cost_discount'] ?? 0))) {
        //                 $target['total_roi'] = round($lastMoney / $target['cost_discount'] * 100, 2) . '%';
        //             }
        //         }
        //     }
        //     else {
        //         if (!empty(floatval($target['new_user_total_pay'] ?? 0)) && !empty(floatval($target['firstlogin_user']) ?? 0)) {
        //             $target['total_ltv'] = number_format(
        //                 math_eval('x/y', ['x' => $target['new_user_total_pay'], 'y' => $target['firstlogin_user']]),
        //                 2
        //             );
        //         }
        //
        //         if (!empty(floatval($target['new_user_total_pay'] ?? 0)) && !empty(floatval($target['cost_discount']) ?? 0)) {
        //             $target['total_roi'] = round((float)$target['new_user_total_pay'] / (float)$target['cost_discount'] * 100, 2) . '%';
        //         }
        //     }
        // };
        //
        // $processLine
        //     // 追加每行组合的唯一键
        //     ->prependEachRow($this->addUnionKeyForRow($groups))
        //     // 添加每行LTV的信息
        //     ->addProcess($this->addInfoEachRow($options, $groups, ['firstlogin_user', 'cost_discount']))
        //     // 追加渠道标签
        //     ->addProcess($channelTagAppendFn)
        //     // 替换ID信息字段
        //     ->addProcess($this->replaceColumnDefine($constConfCollect))
        //     // 格式化汇总分组对应字段
        //     ->addProcess($this->resetUnusedColumn($groups))
        //     // 计算LTV_N
        //     ->addProcess(LtvCalculator::calcEachRow($ltvGetUser, ['groups' => $groups, 'ltv_type' => $ltvType]))
        //     // ROI_N计算
        //     ->addProcess(RoiCalculator::calcEachRow($roiGetCost, ['groups' => $groups, 'ltv_type' => $ltvType]))
        //     // 累计LTV计算
        //     // ->addProcess(LtvCalculator::cumulativeLtvEachRow())
        //     // 累计ROI
        //     // ->addProcess(RoiCalculator::cumulativeRoiEachRow())
        //     ->addProcess($calcFn)
        //     // 新用户成本
        //     ->addProcess(static function (&$target) {
        //         if (
        //             empty($target['cost_discount'])
        //             || empty($target['firstlogin_user'])
        //         ) {
        //             $target['new_user_cost'] = 0.00;
        //         }
        //         else {
        //             $target['new_user_cost'] = number_format(
        //                 round(math_eval('x/y', ['x' => $target['cost_discount'], 'y' => $target['firstlogin_user']]), 2),
        //                 2
        //             );
        //         }
        //     });
        //
        //
        // if (in_array('package_id', $groups)) {
        //     $listPackages  = array_column($list, 'package_id');
        //     $packageTagMap = Zakia::changeTagsStructMap($optionsServ->listTagsName(['option_type' => 'package_id', 'id' => $listPackages]) ?? []);
        //
        //     if (!empty($packageTagMap)) {
        //         $packageTagAppendFn = function (&$target) use ($packageTagMap) {
        //             if (
        //                 !empty($target['package_id'])
        //                 && $target['package_id'] != '-'
        //             ) {
        //                 $packageId              = $target['package_id'];
        //                 $target['package_tags'] = array_values($packageTagMap[$packageId] ?? []);
        //             }
        //         };
        //
        //         $processLine->addProcess($packageTagAppendFn);
        //     }
        // }
        //
        // // 处理LTV输出值 (LTVn选项)
        // if (2 === $ltvType) {
        //     // LTVn 增量展示
        //     $processLine->addProcess(LtvCalculator::ltvIncrementalEachRow());
        // }
        // elseif (3 === $ltvType) {
        //     // LTVn 赔率展示
        //     $processLine->addProcess(LtvCalculator::ltvMultipleEachRow());
        // }
        //
        // // 清理内存
        // $processLine->addProcess($clearUnusedField);
        //
        // $processLine->run($list);
        //
        // /**
        //  * section: 汇总行处理
        //  * ---
        //  */
        //
        // $summaryRow = &$result['summary'];
        //
        // $this->addInfoForSingleRow(
        //     $summaryRow, $options, [], $params['range_date_dimension'] ?? ConstFirstLogin::DIMENSION_DAY,
        //     ['firstlogin_user', 'cost_discount']
        // );
        //
        // $summaryLtvGetUser = IndexCalculators::getValueInCollectByN('firstlogin_user_n');
        // $summaryRoiGetCost = IndexCalculators::getValueInCollectByN('cost_discount_n');
        //
        // LtvCalculator::calcLtv($summaryRow, $summaryRow['ltv_info'] ?? [], $summaryLtvGetUser, $ltvType);
        // RoiCalculator::calcRoi($summaryRow, $summaryRow['ltv_info'] ?? [], $summaryRoiGetCost);
        //
        // // 计算累计LTV&ROI
        // $calcFn($summaryRow);
        //
        // // LtvCalculator::cumulativeLtv($summaryRow);
        // // RoiCalculator::cumulativeRoi($summaryRow);
        //
        // if (
        //     empty($summaryRow['cost_discount'])
        //     || empty($summaryRow['firstlogin_user'])
        // ) {
        //     $summaryRow['new_user_cost'] = 0.00;
        // }
        // else {
        //     $summaryRow['new_user_cost'] = number_format(
        //         round(math_eval('x/y', ['x' => $summaryRow['cost_discount'], 'y' => $summaryRow['firstlogin_user']]), 2),
        //         2
        //     );
        // }
        //
        // if (2 === $ltvType) {
        //     IndexCalculators::ltvIncrementalCalc($summaryRow);
        // }
        // elseif (3 === $ltvType) {
        //     IndexCalculators::ltvMultipleCalc($summaryRow);
        // }
        // $clearUnusedField($summaryRow);
        //
        // return $result;
    }

    /**
     * 拆解参数
     *
     * @param Collection $params
     *
     * @return array
     */
    private function changeDefaultParams(Collection &$params): array
    {
        // 分页获取
        [$page, $pageSize] = [
            $params->pull('page', 1), $params->pull('page_size', 20),
        ];

        // 排序获取
        if ($params->has('sort')) {
            $sort = $params->pull('sort');
            if ($params->has('order')) {
                $sort .= ' ' . ($params->pull('order') == 'ascend' ? 'asc' : 'desc');
            }
        }
        else {
            $sort = ['tday', 'new_user desc'];
        }

        return [
            'pagination' => ['page' => $page, 'page_size' => $pageSize],
            'sort'       => $sort,
        ];
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function simpleSearchAction(): array
    {
        $params        = $this->wrapParams($this->request);
        $groups        = ['tday'];
        $columnScope   = $params->get('column_scope');
        $timeDimension = (int)$params->get('range_date_dimension');

        $baseServ = new FirstLoginIndex();
        $ltvServ  = new BasicLtvIndex('ddc_platform.dws_firstlogin_ltv_daily', 't_ltv');;
        $options = $params->toArray();

        if (ConstHub::DIMENSION_WEEK === $timeDimension) {
            $baseResult = $baseServ->simpleListByWeek($options, [], $groups);
        }
        elseif (ConstHub::DIMENSION_MONTH === $timeDimension) {
            $baseResult = $baseServ->simpleListByMonth($options, [], $groups);
        }
        else {
            $baseResult = $baseServ->simpleListByDay($options, [], $groups);
        }

        $ltvResult = $ltvServ->simpleListByDay($options);
        $ltvInfo   = IndicatorsHelpers::dimReduction($ltvResult['list'], 'day_type', ['tday'], ['money', 'money_all']);


        $info = &$baseResult['list'];

        $processLine = new ProcessLine();
        $processLine->addProcess(function (&$target) use ($ltvInfo) {
            $tDay = $target['tday'];
            if (!empty($ltvInfo[$tDay])) {
                $target['ltv_info']           = $ltvInfo[$tDay]['day_type'] ?? [];
                $target['new_user_total_pay'] = $target['ltv_info'][1000]['money_all'] ?? 0.00;
            }
        });

        $ltvGetUser = IndexCalculators::getSingleValue('new_user');
        $roiGetCost = IndexCalculators::getSingleValue('cost_discount');

        if (in_array('ltv', $columnScope)) {
            $processLine->addProcess(
                LtvCalculator::calcEachRow($ltvGetUser, ['ltv_type' => 0, 'max_days' => 7])
            );
        }

        if (in_array('roi', $columnScope)) {
            $processLine->addProcess(
                RoiCalculator::calcEachRow($roiGetCost, ['ltv_type' => 0, 'max_days' => 7])
            );
        }

        $processLine
            ->addProcess(LtvCalculator::cumulativeLtvEachRow('new_user'))
            ->addProcess(RoiCalculator::cumulativeRoiEachRow())
            ->addProcess(function (&$target) {
                unset($target['ltv_info']);
            });

        $processLine->run($info);

        /**
         * 汇总行处理
         */
        $summaryRow = &$baseResult['summary'];
        $summaryLtv = array_column($ltvResult['summary'], null, 'day_type');

        // 累计已到天数数据
        $summaryNInfo = Calculator::cumulativeOnDays($info, ['new_user', 'cost_discount']);
        $summaryRow   = array_merge($summaryRow, $summaryNInfo);

        $summaryLtvGetUser = IndexCalculators::getValueInCollectByN('new_user_n');
        $summaryRoiGetCost = IndexCalculators::getValueInCollectByN('cost_discount_n');

        if (in_array('ltv', $columnScope)) {
            LtvCalculator::calcLtv($summaryRow, $summaryLtv, $summaryLtvGetUser);
            if (empty($summaryLtv[1000]) || empty($summaryRow['new_user'])) {
                $summaryRow['total_ltv'] = 0.00;
            }
            else {
                $summaryRow['total_ltv'] = number_format(
                    math_eval('x/y', ['x' => $summaryLtv[1000]['money_all'], 'y' => $summaryRow['new_user']]), 2
                );
            }
        }

        if (in_array('roi', $columnScope)) {
            RoiCalculator::calcRoi($summaryRow, $summaryLtv, $summaryRoiGetCost);
            if (empty($summaryLtv[1000]) || empty($summaryRow['cost_discount'])) {
                $summaryRow['total_ltv'] = 0.00;
            }
            else {
                $summaryRow['total_roi'] = number_format(
                        math_eval('x/y*100', ['x' => $summaryLtv[1000]['money_all'], 'y' => $summaryRow['cost_discount']]), 2
                    ) . '%';
            }
        }
        unset($summaryRow['new_user_n'], $summaryRow['cost_discount_n']);
        $fields = (new FirstLoginLtv())->getSimpleFields($options)->toArray();

        return $this->success(array_merge($baseResult, ['fields' => $fields]));
    }

    protected function resetUnusedColumn($groups): \Closure
    {
        $resetCols = ColumnManager::groupFilterOperatorColumn($groups);

        return function (&$target) use ($resetCols) {
            $target = array_merge($target, $resetCols);
        };
    }

}
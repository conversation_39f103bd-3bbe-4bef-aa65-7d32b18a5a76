<?php

namespace app\ad_upload\contract;

use app\ad_upload\tool\ChannelFactory;
use app\ad_upload\tool\CommonFunc;

/**
 * 支付、虚拟增量付费 上报公共方法
 * <AUTHOR>
 */
trait PayUploadStrategyTrait
{
    /**
     * 填充点击
     * @param array $info info
     * @return array|mixed
     */
    public function fillUpClick($info)
    {
        if (!in_array($info['PACKAGE_ID'], CommonFunc::getXcxPackageId())) {
            return parent::fillUpClick($info); // 非小程序查询的表不一样
        }
        // 小程序
        if ($info['UPLOAD_METHOD'] == 2) {
            $sql          = "
            SELECT 
                CLICK_ID AS CLICK_ID_AD,
                TIME AS CLICK_TIME,
                OAID,
                CALLBACK_URL,
                EXT AS EXT_CLICK,
                '' as CID 
            FROM ddc_platform.dwd_ad_click_match_log WHERE ID = '{$info['CLICK_ID']}'
            ";
            $dataClickTmp = \Plus::$app->doris_entrance->query($sql)->fetch(\PDO::FETCH_ASSOC);

            if (!empty($dataClickTmp)) {
                $dataClickTmp['EXT_CLICK'] = \json_decode($dataClickTmp['EXT_CLICK'], true);
                $info                      = array_merge($info, $dataClickTmp);
            } else {
                \Plus::$app->log->error('fillUpClick:' . $info['ID'], [], AdBaseInterface::LOG_DIR);
                $info = [];
            }
        }// end if()
        // 头条小程序，查询激活信息，国内激活表(不去重,game匹配来源)
        if (ChannelFactory::getChannelCode($this->channelId) != 'toutiao_xcx') {
            return $info;
        }

        $activateTime = $info['ACTIVATE_TIME'] ?? '';
        $deviceKey    = $info['DEVICE_KEY'] ?? '';

        if (empty($activateTime) || empty($deviceKey)) {
            return $info;
        }
        $activateDate = date('Y-m-d', strtotime($activateTime));

        $sql = "select android_id,time 
                       from bigdata_dwd.dwd_sdk_activate_log_sv_game 
                where dt='{$activateDate}' and device_key='{$deviceKey}' and time='{$activateTime}'";
        $res = \Plus::$app->doris_entrance->query($sql)->fetch(\PDO::FETCH_ASSOC);

        if (!empty($res)) {
            $info['active_info'] = $res;
        }
        return $info;
    }

    /**
     * 上报数据 处理
     * @param array $data         data
     * @param  array $uploadConfig 配置
     * @return array
     */
    public function processDataPay(array $data, $uploadConfig): array
    {
        $rs = parent::processData($data, $uploadConfig);
        foreach ($rs as $k => $v) {
            $reportLog = $this->initPaidReportLog($v);
            //补充支付相关上报日志
            if (isset($v['paid_report_log'])) {
                //合并数据
                $rs[$k]['paid_report_log'] = array_merge($reportLog, $v['paid_report_log']);
            } else {
                $rs[$k]['paid_report_log'] = $reportLog;
            }
        }
        return $rs;
    }

    /**
     * 补充支付相关上报日志
     * @param array $v data
     * @return array
     */
    public function initPaidReportLog($v)
    {
        return  [
            'order_id'         => $v['ORDER_ID'],
            'pay_result'       => $v['PAY_RESULT'] ?? 0,
            'actually_money'   => $v['ACTUALLY_MONEY'] ?? 0,
            'cp_game_id'       => $v['CP_GAME_ID'] ?? 0,
            'game_id'          => $v['GAME_ID'] ?? 0,
            'package_id'       => $v['PACKAGE_ID'] ?? 0,
            'channel_id'       => $v['CHANNEL_ID'] ?? 0,
            'oaid'             => $v['OAID'] ?? '',
            'device_key'       => $v['DEVICE_KEY'] ?? '',
            'pay_time'         => $v['PAY_TIME'] ?? '',
            'core_account'     => $v['CORE_ACCOUNT'] ?? '',
            'source_id'        => $v['SOURCE_ID'] ?? '',
            'source_dimension' => $v['SOURCE_DIMENSION'] ?? '',
        ];
    }
}

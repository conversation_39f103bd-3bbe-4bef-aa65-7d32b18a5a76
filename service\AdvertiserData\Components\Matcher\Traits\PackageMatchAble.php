<?php

namespace app\service\AdvertiserData\Components\Matcher\Traits;

use app\service\AdvertiserData\Components\Helpers\Convert;
use app\service\AdvertiserData\Components\Helpers\QueryBuilderHelper;
use Spiral\Database\Injection\Parameter;
use Spiral\Database\Query\QueryInterface;
use Spiral\Database\Query\SelectQuery;

trait PackageMatchAble
{
    /**
     * 包号搜索匹配
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchPackages(&$qb, array &$params)
    {
        if (empty($params['package_id'])) return;

        $field = $this->getReflectField('package_id');
        $data  = $params['package_id'];

        if (is_string($data) && str_contains($data, ',')) {
            $data = \explode(',', $data);
        }

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * 客户端搜索项匹配
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchPlatform(&$qb, array &$params)
    {
        if (empty($params['platform_id'])) return;

        $field = $this->getReflectField('platform_id');
        $data  = $params['platform_id'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

    /**
     * 推广分类搜索项匹配
     *
     * @param SelectQuery|QueryInterface $qb
     * @param array $params
     *
     * @return void
     */
    protected function matchPromotion(&$qb, array &$params)
    {
        if (empty($params['promotion_id'])) return;

        $field = $this->getReflectField('promotion_id');
        $data  = $params['promotion_id'];

        QueryBuilderHelper::baseBuild($qb, $field, $data);
    }

}